# Guide Upgrade to Umi 4 and antd 5
This document is mainly written to provide how to upgrade from umijs v3 + antd 4 to umijs v4 + antd 5. In order to reduce errors caused by directly installing the latest version, it is recommended to re-create a clean umi 4 + antd 5 project and then perform manual migration.
## Version
We are going to use **antd@5.16.4** and **Umi4.1.10**

Don't forget to add `"@umijs/plugins": "^4.1.8"` under devDependencies for dva, antd and tailwindcss.
## Useful Links
- [Umi Official Documentation](https://umijs.org/en-US/docs/introduce/introduce)
- [Umi Official Documentation - Upgrade to Umi 4](https://umijs.org/en-US/docs/introduce/upgrade-to-umi-4)

## Step-by-step guide
1. Follow the instructions in the [Umi official documentation](https://umijs.org/en-US/docs/guides/getting-started). Recommended to use [bun](https://bun.sh/) to create project.
2. Open `.umirc.ts`, there are some changes on the umi config settings.
### Removed from Umi 4
- nodeModulesTransform
- fastRefresh
- devtool: 'cheap-module-source-map'
- favicon
- webpack5
- workerLoader
### New from Umi 4
` npmClient `
` plugins ` To import the plugin from **@umijs/plugins**
example: 
```
  plugins: [
    '@umijs/plugins/dist/antd',
    '@umijs/plugins/dist/dva',
    '@umijs/plugins/dist/tailwindcss',
  ],
```
` antd: {} `: To Activate Antd
` dva: {} `: To Activate Dva


## Fixed Issue:

### Deprecated from antd 4

**Select** Component: 
` showArrow={false} `  <- *deprecated*
` suffixIcon={null} ` <- current

`allowClear` and `clearIcon` <- *deprecated*
`allowClear={{ clearIcon: <CloseOutlined /> }}` <- current

`dropdownMatchSelectWidth` <- *deprecated*
`popupMatchSelectWidth` <- current

`bordered={false}` <- *deprecated*
`variant={"borderless"}` <- current

Last updated: *04/12/2024*
