// import AntdMomentWebpackPlugin from '@ant-design/moment-webpack-plugin';

import { defineConfig } from 'umi';
export default defineConfig({
  proxy: {
    // CMA backend for experiments
    '/api/cma/exp': {
      target: 'http://ec2-3-235-170-15.compute-1.amazonaws.com:8080',
      // target: 'http://localhost:8080',
      changeOrigin: true,
      pathRewrite: { '^/api/cma/exp': '' },
    },
    // CMA backend for production
    '/api/cma/prod': {
      // target: 'http://ec2-44-222-3-252.compute-1.amazonaws.com:8080',
      target: 'http://ec2-54-146-231-140.compute-1.amazonaws.com:8080',
      changeOrigin: true,
      pathRewrite: { '^/api/cma/prod': '' },
    },
    // ACQ backend for experiments
    '/api/acq/exp': {
      target: 'http://ec2-3-235-170-15.compute-1.amazonaws.com:8090',
      changeOrigin: true,
      pathRewrite: { '^/api/acq/exp': '' },
    },
    // ACQ backend for production
    '/api/acq/prod': {
      target: 'http://ec2-54-146-231-140.compute-1.amazonaws.com:8090',
      changeOrigin: true,
      pathRewrite: { '^/api/acq/prod': '' },
    },
    // New house avm machine learning api - NOT USED
    '/api/newhouseavm/prod': {
      target: 'http://ec2-35-171-3-127.compute-1.amazonaws.com:8060',
      changeOrigin: true,
      pathRewrite: { '^/api/newhouseavm/prod': '' },
    },
    // New house avm machine learning api
    '/api/newhouseavm/ml/prod': {
      target: 'http://ec2-3-235-170-15.compute-1.amazonaws.com:8060',
      changeOrigin: true,
      pathRewrite: { '^/api/newhouseavm/ml/prod': '' },
    },
    '/api/mlsTest': {
      target: 'http://ec2-3-235-170-15.compute-1.amazonaws.com:8050',
      changeOrigin: true,
      // pathRewrite: { '^/api/mls/test': '' },
      pathRewrite: { '^/api/mlsTest': '' },
    },
    '/api/mls/prod': {
      target: 'http://ec2-54-146-231-140.compute-1.amazonaws.com:8050',
      changeOrigin: true,
      pathRewrite: { '^/api/mls/prod': '' },
    },
    // reworked off-market APIs prod
    '/api/offMarket/prod': {
      target: 'http://ec2-54-146-231-140.compute-1.amazonaws.com:8055',
      changeOrigin: true,
      pathRewrite: { '^/api/offMarket/prod': '' },
    },
    // reworked off-market test
    '/api/offMarket/exp': {
      target: 'http://ec2-3-235-170-15.compute-1.amazonaws.com:8055',
      changeOrigin: true,
      pathRewrite: { '^/api/offMarket/exp': '' },
    },
    // smelly-bun-server
    '/api/sbs/exp': {
      target: 'http://ec2-3-235-170-15.compute-1.amazonaws.com:9003',
      // target: 'http://localhost:9003',
      changeOrigin: true,
      pathRewrite: { '^/api/sbs/exp': '' },
    },
    '/api/sbs/prod': {
      target: 'http://ec2-54-146-231-140.compute-1.amazonaws.com:9003',
      changeOrigin: true,
      pathRewrite: { '^/api/sbs/prod': '' },
    },
    '/api/management/exp': {
      target: 'http://ec2-3-235-170-15.compute-1.amazonaws.com:8070',
      changeOrigin: true,
      pathRewrite: { '^/api/management/exp': '' },
    },
    '/api/management/prod': {
      target: 'http://ec2-3-235-170-15.compute-1.amazonaws.com:8070',
      changeOrigin: true,
      pathRewrite: { '^/api/management/prod': '' },
    },
    '/api/elixir/exp': {
      target: 'http://ec2-3-235-170-15.compute-1.amazonaws.com:4000',
      changeOrigin: true,
      pathRewrite: { '^/api/elixir/exp': '' },
    },
    '/api/elixir/prod': {
      target: 'http://ec2-54-146-231-140.compute-1.amazonaws.com:4000',
      changeOrigin: true,
      pathRewrite: { '^/api/elixir/prod': '' },
    },
    '/api/admin-server/exp': {
      target: 'http://ec2-3-235-170-15.compute-1.amazonaws.com:9027',
      changeOrigin: true,
      pathRewrite: { '^/api/admin-server/exp': '' },
    },
    '/api/admin-server/prod': {
      target: 'http://ec2-3-235-170-15.compute-1.amazonaws.com:9027',
      changeOrigin: true,
      pathRewrite: { '^/api/admin-server/prod': '' },
    },
  },
  routes: [{ path: '/', component: '@/pages/index' }],
  // npmClient: 'pnpm',
  title: 'Comparative Market Analysis | LocateAlpha by Spatial Laser',
  plugins: [
    '@umijs/plugins/dist/antd',
    '@umijs/plugins/dist/dva',
    '@umijs/plugins/dist/tailwindcss',
  ],
  jsMinifier: 'swc',
  // jsMinifier: 'terser', // use default esbuild
  // esbuildMinifyIIFE: true,
  antd: {},
  dva: {},

  tailwindcss: {},
  codeSplitting: {
    jsStrategy: 'granularChunks',
  },
  chainWebpack(config) {
    config.module
      .rule('worker')
      .test(/\.worker\.ts$/)
      .use('worker-loader')
      .loader('worker-loader')
      .end();
  },
  devtool: process.env.NODE_ENV === 'development' ? 'source-map' : false,
  // define: {
  //   'process.env.SENTRY_AUTH_TOKEN': process.env.SENTRY_AUTH_TOKEN,
  // },
  // headScripts: [
  //   `
  //     (function(h,o,t,j,a,r){
  //         h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
  //         h._hjSettings={hjid:6492526,hjsv:6};
  //         a=o.getElementsByTagName('head')[0];
  //         r=o.createElement('script');r.async=1;
  //         r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
  //         a.appendChild(r);
  //     })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
  //   `
  // ],
});
