# Commit Documentation


## Commit 4c3ac74
- **Date**: 2025-09-22 17:04:03 UTC
- **Message**: feat(proxy): add admin-server path proxy
- **Explanation**:
Failed to generate documentation: Error code: 400 - {'error': {'message': "This model's maximum context length is 16385 tokens. However, your messages resulted in 58832 tokens. Please reduce the length of the messages.", 'type': 'invalid_request_error', 'param': 'messages', 'code': 'context_length_exceeded'}}

---

## Commit a529525
- **Date**: 2025-09-17 15:20:30 UTC
- **Message**: refactor(land-search): max result export selection
- **Explanation**:
### Summary of Changes
- Added a constant `MAX_SELECTION_ROWS` with a value of 100.
- Removed the `ColumnSelectorLabelSourceTag` component.
- Updated the `useEffect` hook in the `ResultDisplay` component to clear `customSelectedResults` array before setting new values.
- Updated the logic in the `ResultDisplay` component to limit the number of selected results to `MAX_SELECTION_ROWS`.
- Adjusted the text displayed based on the number of selected results in the `ResultDisplay` component.
- Updated the logic in the `ResultDisplay` component for selecting and unselecting results based on the `MAX_SELECTION_ROWS` constant.
- Added a `disabled` prop to the `RowUI` component to handle the disabled state based on certain conditions.
- Updated the styling in the `RowUI` component to change text color when disabled.
- Updated the `ResultDisplayRow` component to handle the disabled state based on selected results count and export data mode.

### Reason for Changes
- The constant `MAX_SELECTION_ROWS` was added to limit the number of selected results to 100.
- The `ColumnSelectorLabelSourceTag` component was removed, possibly because it was no longer needed or used.
- The logic in the `ResultDisplay` component was updated to handle the selection and display of results within the specified limit.
- Text and functionality in the `ResultDisplay` component were adjusted to better indicate the number of selected results and handle selection actions within

---

## Commit c4d0347
- **Date**: 2025-09-15 15:21:45 UTC
- **Message**: fix(land-search): error message
- **Explanation**:
## Changes in LandParcelSearch Context

- Replaced `response?.data?.stage` with `response?.data?.message` in the `alert` method
- The change was likely made to display a more informative message when an error occurs during the search process

---

## Commit 7dd134e
- **Date**: 2025-09-12 15:50:02 UTC
- **Message**: refactor(land-search): remove all user access restrictions
- **Explanation**:
Failed to generate documentation: Error code: 400 - {'error': {'message': "This model's maximum context length is 16385 tokens. However, your messages resulted in 159140 tokens. Please reduce the length of the messages.", 'type': 'invalid_request_error', 'param': 'messages', 'code': 'context_length_exceeded'}}

---

## Commit d413a7c
- **Date**: 2025-09-12 14:09:38 UTC
- **Message**: fix(ParcelOwnerSummary): update institution naming for 'Cerberus' to 'Firstkey' and add console log for institutions
- **Explanation**:
## Summary of Changes:

- The order of imported dependencies in the `ParcelOwnerSummary.js` file under `GeneratePDF/InsightsPDF/components/ParcelOwnerSummary` was changed from `{ useState, useRef, useEffect }` to `{ Col, Progress, Row }` for `antd` components, and from `{ useEffect, useRef, useState }` to `{ useState, useRef, useEffect }` for `react` dependencies. This change might have been made for better code organization and readability.

- A console log statement was added to log the `institutions` array in the `ParcelOwnerSummary` function, providing additional debugging information.

- In the same function, a conditional statement within a mapping function was updated to change the display value based on the institution name. For the institution named 'Cerberus', the display value was updated to show 'Firstkey' instead.

- In another `ParcelOwnerSummary.js` file under `ResultTable/ParcelOwnerSummary`, a similar conditional statement was added to change the display value when the institution includes 'AH4R' or is 'Cerberus'. This change aligns with the previous update made in the other file.

## Possible Reasons for Changes:

- The changes in the imported dependencies order were likely made to maintain consistency or improve code readability within the file.

- The addition of console log statement and conditional updates in displaying institution names may have been made to enhance debugging capabilities or to provide a more user-friendly interface for end-users.

Overall

---

## Commit 1254ace
- **Date**: 2025-09-11 22:28:39 UTC
- **Message**: Update AreaDemographic.tsx
- **Explanation**:
## Changes in AreaDemographic Component

- Removed rendering of "Median Family Income" from the component.
- Adjusted the formatting of "Median Household Income" to remove decimal places and display as a whole number.
- The change may have been made to ensure consistency in displaying income data and to improve the overall presentation of the component.

---

## Commit 52ff928
- **Date**: 2025-09-02 13:31:51 UTC
- **Message**: chore: bump map version
- **Explanation**:
Failed to generate documentation: Error code: 400 - {'error': {'message': "This model's maximum context length is 16385 tokens. However, your messages resulted in 113985 tokens. Please reduce the length of the messages.", 'type': 'invalid_request_error', 'param': 'messages', 'code': 'context_length_exceeded'}}

---

## Commit 5bff491
- **Date**: 2025-08-30 17:15:34 UTC
- **Message**: fix: dep
- **Explanation**:
### Summary of Changes:
- Added `"tailwind-merge": "^3.3.1"` to the dependencies.
- Updated `"@types/express-serve-static-core"` to version `4.19.6`.
- Updated `"source-map-support"` to version `0.3.3`.
- Updated `"tailwindcss"` to version `3.4.17`.
- Updated `"jest-runner/source-map-support"` to version `0.5.21`.
- Updated `"source-map-support/source-map"` to version `0.1.32`.
- Updated `"source-map-support/source-map"` to version `0.6.1`.

### Reason for Changes:
- Addition of `"tailwind-merge": "^3.3.1"` may have been required for new Tailwind functionality.
- Upgrading dependencies like `"@types/express-serve-static-core"` and `"source-map-support"` may have been necessary for compatibility or security reasons.
- Updating versions of packages like `"tailwindcss"`, `"jest-runner/source-map-support"`, and `"source-map-support/source-map"` may have been done to leverage new features or bug fixes in the updated versions.

---

## Commit 298ca0d
- **Date**: 2025-08-30 16:51:55 UTC
- **Message**: feat: save land search filters
- **Explanation**:
Failed to generate documentation: Error code: 400 - {'error': {'message': "This model's maximum context length is 16385 tokens. However, your messages resulted in 46528 tokens. Please reduce the length of the messages.", 'type': 'invalid_request_error', 'param': 'messages', 'code': 'context_length_exceeded'}}

---

## Commit 14f776b
- **Date**: 2025-08-29 16:55:09 UTC
- **Message**: fix: add "Multifamily" to user group access list in userGroupAccess.json
- **Explanation**:
## Change in userGroupAccess.json

- Added "Muilty Family" to the list of access permissions for the "Workforcemobile" user group.
- The "Affordable Housing" permission was also added in addition to "Muilty Family".
- These changes were likely made to provide the "Workforcemobile" user group with access to additional features or functionalities related to "Muilty Family" and "Affordable Housing".

---

## Commit 1c24218
- **Date**: 2025-08-25 16:04:39 UTC
- **Message**: chore: bump map version
- **Explanation**:
### Changes in package.json

- Updated the version of "@spatiallaser/map" from "^1.0.268" to "^1.0.273"

### Reason for the change

- The change may have been made to include bug fixes, performance improvements, or new features in the updated version of "@spatiallaser/map". By updating to a newer version, the project can benefit from the latest enhancements and fixes provided by the package.

---

## Commit cb32081
- **Date**: 2025-08-21 21:04:25 UTC
- **Message**: chore: update hotjar site id
- **Explanation**:
## Summary of Changes

### /.umirc.ts
- Added a new headScripts configuration to include a Hotjar tracking script in the head of the application.
  
### src/pages/index.tsx
- Updated the Hotjar siteId from 6452680 to 6492526.

## Reason for Changes
- The Hotjar siteId was updated in order to track data for a different site or project.
- The headScripts configuration was added to include the Hotjar tracking script on all pages of the application for analytics purposes.

---

## Commit 3acd48f
- **Date**: 2025-08-18 16:47:15 UTC
- **Message**: Update bun.lock and userGroupAccess.json to resolve merge conflicts and add "Multifamily" to user group access list
- **Explanation**:
Failed to generate documentation: Error code: 400 - {'error': {'message': "This model's maximum context length is 16385 tokens. However, your messages resulted in 25068 tokens. Please reduce the length of the messages.", 'type': 'invalid_request_error', 'param': 'messages', 'code': 'context_length_exceeded'}}

---

## Commit d3f89e4
- **Date**: 2025-08-14 19:59:20 UTC
- **Message**: feat: access control on multifamily
- **Explanation**:
Failed to generate documentation: Error code: 400 - {'error': {'message': "This model's maximum context length is 16385 tokens. However, your messages resulted in 28683 tokens. Please reduce the length of the messages.", 'type': 'invalid_request_error', 'param': 'messages', 'code': 'context_length_exceeded'}}

---

## Commit ec1b2e6
- **Date**: 2025-08-14 00:22:42 UTC
- **Message**: chore: bump market condition
- **Explanation**:
Failed to generate documentation: Error code: 400 - {'error': {'message': "This model's maximum context length is 16385 tokens. However, your messages resulted in 50128 tokens. Please reduce the length of the messages.", 'type': 'invalid_request_error', 'param': 'messages', 'code': 'context_length_exceeded'}}

---

## Commit 5cb656a
- **Date**: 2025-08-12 15:17:00 UTC
- **Message**: Decrease weight of Chain Stores menu drop down options to match the others
- **Explanation**:
### Summary of Changes

- The text inside `<span>` tags for the "Chain Stores" button was changed from:
```html
<span className="text-sm font-medium leading-normal">
  Chain Stores
</span>
```
to:
```html
<span>Chain Stores</span>
```

- The text inside `<span>` tags for the "Gentrifying Neighborhoods" button was changed from:
```html
<span className="text-sm font-medium leading-normal">
  Gentrifying Neighborhoods
</span>
```
to:
```html
<span>Gentrifying Neighborhoods</span>
```

### Reason for Changes

The changes appear to be cosmetic, updating the styling of the text inside the `<span>` tags for consistency or to match a new design guideline. The removal of the `className="text-sm font-medium leading-normal"` suggests a simplification of the styling for these buttons.

---

## Commit 3aaeee3
- **Date**: 2025-08-12 02:21:20 UTC
- **Message**: fix(GeneratePDF): total selected mismatch
- **Explanation**:
### Summary of Changes:
1. In `GeneratePDF.js`:
   - Added selected MLS row keys for lease and sale to reflect user selections in the modal.
   - Updated conditions to check for selected MLS row keys when determining if there is lease or sale information available.

2. In `report.js`:
   - Added new properties for UI-cached datasets to ensure PDF parity.
   - Implemented logic to use UI-filtered datasets for NSFR and HotPads when users have selections.
   - Added checks to use UI-cached filtered datasets for MLS lease and sale information based on user selections.

3. In `calculations.ts`:
   - Modified `calculateAverage` function to remove non-finite values (NaN/Infinity) in addition to null and undefined values.

4. In `pdf.js`:
   - Revised the calculation of average rent per square foot for NSFR and HotPads to include filtering out non-positive square feet values.
   - Updated the calculation of average rent per square foot for HotPads to include additional filtering.

### Reason for Changes:
- The changes in `GeneratePDF.js` were likely made to enhance the functionality of reflecting user selections in the PDF generation modal.
- Changes in `report.js` were aimed at ensuring the consistency of PDF data by incorporating UI-cached datasets and utilizing user selections for specific information retrieval.
- Modifications in `calculations.ts` were made to improve the accuracy of average calculations by filtering out non-finite values

---

## Commit 61f812a
- **Date**: 2025-08-11 15:09:19 UTC
- **Message**: chore: add Heyday
- **Explanation**:
### Summary of Changes:
1. Added a new logo file `Heyday-logo.png` in the `public/images/logo` directory.
2. Updated the `userGroup.js` files in both the `src/components/GeneratePDF/InsightsPDF/utils` and `src/utils` directories to include a conditional block for `Heyday` client, setting specific information like client name, logo path, and flex properties.
3. Modified the `Header.tsx` file to display the `Heyday` logo when the client name is `Heyday`.
4. Updated the `header.css` file to add styling for the `Heyday` logo.
5. Added `Heyday` client information in the `userGroupAccess.json` file including abbreviations, applicable apps, and premium features.

### Reason for Change:
The changes were made to incorporate specific client information and logo for the `Heyday` client. This allows for a customized display of client logos in the header based on their respective client names, providing a more personalized and tailored user experience for different clients. Additionally, the changes in the userGroupAccess.json file ensure that the `Heyday` client has access to the relevant premium features and applications.

---

## Commit b125647
- **Date**: 2025-08-11 14:29:54 UTC
- **Message**: feat: multifamil community
- **Explanation**:
Failed to generate documentation: Error code: 400 - {'error': {'message': "This model's maximum context length is 16385 tokens. However, your messages resulted in 23761 tokens. Please reduce the length of the messages.", 'type': 'invalid_request_error', 'param': 'messages', 'code': 'context_length_exceeded'}}

---

## Commit 503e766
- **Date**: 2025-08-05 13:55:50 UTC
- **Message**: FUCK Firefox
- **Explanation**:
Failed to generate documentation: Error code: 400 - {'error': {'message': "This model's maximum context length is 16385 tokens. However, your messages resulted in 70160 tokens. Please reduce the length of the messages.", 'type': 'invalid_request_error', 'param': 'messages', 'code': 'context_length_exceeded'}}

---

## Commit 3d9bae3
- **Date**: 2025-08-01 18:42:52 UTC
- **Message**: fix: update BuiltForRentTable to manage column visibility and ensure proper rendering of filtered columns; adjust Sentry initialization for production environment
- **Explanation**:
### Changes Made:
1. In the `BuiltForRentTable.tsx` file:
   - Renamed the `columns` variable to `allColumns` for clarity.
   - Added a new useEffect hook to update visible columns when default columns change.
   - Created a new `filteredColumns` variable to filter and reorder table columns based on visibility settings.
   - Updated the `columns` prop in the `<TableHeader>` component to use the `filteredColumns` variable instead of the `columns` variable.

2. In the `index.tsx` file:
   - Added a condition to initialize Sentry only in the production environment instead of unconditionally.

### Reason for Changes:
1. The changes in `BuiltForRentTable.tsx` improve the handling of visible table columns based on user preferences and ensure consistency.
2. The change in `index.tsx` ensures that the Sentry error monitoring service is initialized only in the production environment to avoid unnecessary setup in other environments.

---

## Commit 366019b
- **Date**: 2025-08-01 15:17:55 UTC
- **Message**: fix: update ColumnManagerModal to handle undefined columns and improve type safety; integrate column management in BuiltForRentTable and TableHeader
- **Explanation**:
### Changes Made:
1. Added `import { ColumnConfig } from '../ColumnManagerModal';` in `BuiltForRentTable.tsx` file.
2. Added state and function for managing visible columns in `BuiltForRentTable.tsx`.
3. Updated `TableHeader` component in `TableHeader.tsx` to include new props related to columns and column changes.
4. Added type definitions and imports for Drag and Drop Context in `ColumnManagerModal.tsx`.
5. Updated `handleDragEnd` function in `ColumnManagerModal.tsx` to use type `DropResult`.

### Reason for Changes:
- The changes seem to be related to managing visible columns in the table and allowing users to customize columns using a modal window. The addition of `ColumnConfig` in multiple files suggests a structured way of defining columns and their visibility.
- The updates in `TableHeader` and `ColumnManagerModal` components indicate a focus on providing more flexibility to users in customizing the view of the data table by selecting which columns to display.
- The use of Drag and Drop functionality in the `ColumnManagerModal` suggests a user-friendly way to reorder columns based on user preferences.

---

## Commit 0c307b3
- **Date**: 2025-07-29 00:38:19 UTC
- **Message**: fix: enhance affordable housing data filtering to exclude future dates
- **Explanation**:
Failed to generate documentation: Error code: 400 - {'error': {'message': "This model's maximum context length is 16385 tokens. However, your messages resulted in 49525 tokens. Please reduce the length of the messages.", 'type': 'invalid_request_error', 'param': 'messages', 'code': 'context_length_exceeded'}}

---

## Commit 25ad42d
- **Date**: 2025-07-20 21:39:31 UTC
- **Message**: chore: bump map and set default radius
- **Explanation**:
## Changes in package.json
- **@spatiallaser/map**: Updated version from "^1.0.251" to "^1.0.262"
- **Reason**: Likely a bug fix, feature enhancement, or improvement in the @spatiallaser/map package.

## Changes in MapCMA.js
- **MapCMA component**: Added a new property `defaultRadius: 0.5` under `init` in the `streetview` object.
- **Reason**: This change could be related to setting a default radius value for a specific functionality within the MapCMA component.

---

## Commit 1699b1c
- **Date**: 2025-07-18 16:24:10 UTC
- **Message**: chore: add @hotjar/browser dependency to package.json
- **Explanation**:
### Changes in package.json

#### Added
- Added dependency on `"@hotjar/browser": "^1.0.9"`

#### Summary
- A new dependency on `"@hotjar/browser"` was added to the `package.json` file. This could have been added to implement Hotjar analytics features or functionality in the project.

---

## Commit 4e9899d
- **Date**: 2025-07-18 15:09:58 UTC
- **Message**: chore: update Sentry to version 9.40.0 and modify DSN in index.tsx; add @hotjar/browser dependency
- **Explanation**:
### Changes Made:
1. Added a new dependency "@hotjar/browser" version "^1.0.9" in the bun.lock file.
2. Updated the dependency "@sentry/react" from version "^8.55.0" to "^9.40.0" in the bun.lock file.
3. Updated the DSN for Sentry in the src/pages/index.tsx file.

### Reason for Changes:
1. The addition of "@hotjar/browser" dependency may be for integrating Hotjar analytics or functionality.
2. The update to "@sentry/react" suggests the need for the latest features or bug fixes provided in version "^9.40.0".
3. The change in the Sentry DSN could be due to updates or changes in the Sentry configuration.

---

## Commit 6deb935
- **Date**: 2025-07-18 14:51:20 UTC
- **Message**: fix: update Sentry DSN and add production environment configuration in index.tsx
- **Explanation**:
## Changes in index.tsx

- Changed the Sentry initialization `dsn` from 'https://<EMAIL>/12138' to 'http://2d90781197ca417dbebfbfa8aa00e096@3.235.170.15:9041/1' with the addition of 'environment: 'production'.
  
### Reason for Change
- Updating the Sentry `dsn` is a common practice to point to a different Sentry server or project for error monitoring. 
- Adding the `environment: 'production'` parameter indicates that this configuration is meant for the production environment, which helps in categorizing and distinguishing errors based on the environment they occurred in.

---

## Commit 4f66627
- **Date**: 2025-07-17 21:34:32 UTC
- **Message**: refactor: remove test error button from BatchProcessor component
- **Explanation**:
## Summary of Changes:
- Removed a button with an `onClick` event that threw a specific error when clicked.

## Reason for Change:
- The button was likely used for testing purposes and the specific error being thrown may not be necessary or relevant in the production code. Removing it helps clean up the code and removes unnecessary functionality that may not be needed in a production environment.

---

## Commit 246af04
- **Date**: 2025-07-15 18:49:41 UTC
- **Message**: feat: enhance date range filtering in affordable housing data
- **Explanation**:
## Summary of Changes:

### `bun.lock`:
- Updated the version of `@types/history` from `4.7.11` to `5.0.0` with added dependencies.
- Updated the version of `@types/node` from `24.0.13` to `24.0.14`.

### `panel.tsx`:
- Added date range filtering functionality to filter affordable housing data based on `last_seen_mls` property.

### `utils.ts`:
- Added date range filter functionality to filter affordable housing data based on `last_seen_mls` property.
- Added zip code filter functionality.

## Reason for Changes:
- Upgraded the versions of `@types/history` and `@types/node` to their latest versions for compatibility and added functionality.
- Updated `panel.tsx` and `utils.ts` to include filtering based on date ranges and zip codes for better data filtering and user experience.

---

## Commit 7e83421
- **Date**: 2025-07-14 19:51:48 UTC
- **Message**: feat: add community field to CSV export and result table for new builds
- **Explanation**:
Failed to generate documentation: Error code: 400 - {'error': {'message': "This model's maximum context length is 16385 tokens. However, your messages resulted in 19783 tokens. Please reduce the length of the messages.", 'type': 'invalid_request_error', 'param': 'messages', 'code': 'context_length_exceeded'}}

---

## Commit 6f3eb06
- **Date**: 2025-07-14 04:51:16 UTC
- **Message**: refactor: simplify score legend label and update score range in ScorecardHistoryLayer component
- **Explanation**:
Failed to generate documentation: Error code: 400 - {'error': {'message': "This model's maximum context length is 16385 tokens. However, your messages resulted in 66171 tokens. Please reduce the length of the messages.", 'type': 'invalid_request_error', 'param': 'messages', 'code': 'context_length_exceeded'}}

---

## Commit 425acbe
- **Date**: 2025-07-14 04:49:03 UTC
- **Message**: refactor: update scorecard color interpolation logic and adjust score labels in ScorecardHistoryLayer component
- **Explanation**:
### Summary of Changes:
- The `ScorecardHistoryLayer.js` file in the `MapCMA` component was updated.
- Changes were made to the color mapping for different score values in the `pointStyle`.
- The colors for scores 1, 3, and 4 were adjusted.
- The legend in the `ScorecardLegend` component was updated to show the values 3 and 8 instead of 1 and 10.

### Reason for Changes:
- The color mapping was modified to include a wider range of colors for different score values, potentially improving visual clarity and differentiation.
- Updating the legend to display values 3 and 8 instead of 1 and 10 may reflect changes in the scoring system or preferences for the displayed scores.

---

## Commit 80027db
- **Date**: 2025-07-10 18:34:48 UTC
- **Message**: refactor: comment out CSV download button in BatchProcessor component
- **Explanation**:
## Summary of Changes

In this commit, the changes made to `BatchProcessor.js` involve commenting out a section of code related to the conditional rendering of a download button for a CSV file. The original code used `{csvContent && (...)}` to conditionally render the button, but it has been changed to `{/*csvContent && (...)*/}` which effectively comments out the code. 

## Reason for Changes

The reason for this change is unclear based on the provided diff. It's possible that the download button for the CSV file was temporarily disabled or being reworked, so the code was commented out to prevent it from being rendered in the UI. This change could be part of a larger refactor or feature adjustment in the BatchProcessor component.

---

## Commit bd8c4bd
- **Date**: 2025-07-10 15:06:36 UTC
- **Message**: refactor: enhance row selection and median calculation logic in LastSalePublicRecordTable
- **Explanation**:
## Summary of Changes:
- Updated the calculation of median and median price per sqft in LastSalePublicRecordTable component to consider `props.lastSalePublicRecordDataForRender` if available.
- Added a condition to reset median values when no rows are selected.
- Added logic to select all rows by default when data changes.
- Added a comment to clarify the purpose of the `preserveSelectedRowKeys: false` property in the Table component.

## Reason for Changes:
- The changes were likely made to ensure accurate calculations of median values and to handle default selections and resets appropriately based on user interactions and data updates.

---

## Commit ddfa095
- **Date**: 2025-07-08 20:05:46 UTC
- **Message**: refactor: AreaPsychographicTable component and introduce SegmentModal

- Simplified the AreaPsychographicTable by removing unused imports and state variables.
- Integrated a new SegmentModal component for displaying segment details and performance metrics.
- Implemented a custom hook, useSegmentModal, to manage segment data fetching and modal state.
- Enhanced map interaction features, including hover effects and visibility handling for map layers.
- Updated table columns and titles for clarity and consistency.
- **Explanation**:
### Summary of Changes

#### **AreaBased/AreaPsychographicTable.tsx**
- Removed unused imports.
- Removed unused variables and functions.
- Refactored data fetching logic for better readability.
- Improved map layer management functions.
- Refactored state management and data calculations.
- Enhanced visibility handling and event handlers.
- Improved code structure and organization.

#### **SchoolScore/AreaBased/SegmentModal.tsx**
- Extracted modal component for segment overview.
- Improved code structure and readability.
- Added props for dynamic data rendering.
- Enhanced modal styling and event handling.

#### **SchoolScore/AreaBased/hooks/useSegmentModal.ts**
- Added custom hook for managing segment modal state.
- Improved data fetching and state management logic.
- Refactored functions for better code organization.
- Enhanced error handling and data processing.
- Improved interaction with Redux state and map component.

### Reason for Changes
- **Code Cleanup:** Removed unused imports, variables, and functions for better code cleanliness.
- **Refactoring:** Improved data fetching, state management, and event handling for enhanced performance and readability.
- **Enhancements:** Added new features like segment overview modal and custom hook for improved functionality.
- **Bug Fixes:** Addressed potential bugs, improved error handling, and optimized data processing.
- **Code Structure:** Restructured code for better organization and readability.

---

## Commit 0569f29
- **Date**: 2025-06-30 20:11:08 UTC
- **Message**: chore: add WebCity
- **Explanation**:
### Summary of Changes:
1. Added a new logo file `webcity_logo.png` in the `public/images/logo` directory.
2. Updated the `userGroup.js` file to include client information for the 'WebCity' user group, setting specific properties for this client.
3. Modified the `Header.tsx` file to render the WebCity logo if the client name is 'WebCity'.
4. Added styles for the WebCity logo in the `header.css` file.
5. Updated the `userGroupAccess.json` file to include access information for the 'WebCity' user group.
6. Updated the `utils/userGroup.js` file to include client information for the 'WebCity' user group.

### Reason for Changes:
These changes were likely made to add support for the 'WebCity' client, including their logo, user group access information, and client-specific styling. The modifications ensure that the application can differentiate and provide customized content for the 'WebCity' client.

---

## Commit ca629d8
- **Date**: 2025-06-30 14:01:34 UTC
- **Message**: Passes user email to menu schema

Updates the menu schema to receive the user's email address.

This allows the menu to be dynamically adjusted based on user-specific information.
- **Explanation**:
### Changes in Layout.js

- Added a new constant `userEmail` using `useSelector` to get the user's email from the CMA state.
  
- Modified the `schema` prop in the `<Menu>` component to pass the `userEmail` along with `selectedUserGroup` to `getMenuSchema` function.

### Reason for Changes

- The change was made to pass the user's email to the `getMenuSchema` function along with the user group. This might be necessary to customize the menu schema based on the user's email in addition to their user group.

---

## Commit eb5ed44
- **Date**: 2025-06-29 23:46:20 UTC
- **Message**: Updates dependencies and map component

Updates the lock file with the latest versions of dependencies, including core components and map-related libraries.

Additionally, enhances the MLS component to display last rent information and improve type filtering, ensuring data accuracy and presentation.
- **Explanation**:
Failed to generate documentation: Error code: 400 - {'error': {'message': "This model's maximum context length is 16385 tokens. However, your messages resulted in 102993 tokens. Please reduce the length of the messages.", 'type': 'invalid_request_error', 'param': 'messages', 'code': 'context_length_exceeded'}}

---

## Commit 92b4875
- **Date**: 2025-06-26 20:10:56 UTC
- **Message**: chore: enable BT's access to features
- **Explanation**:
### Changes Made
- Added "New House AVM" to the list of access permissions for user groups
- Added "workforcemobile", "Gentrifying Neighbourhoods", and "Affordable Housing" to the list of access permissions for user groups

### Reason for Change
- The addition of "New House AVM" may indicate a new feature or functionality that has been introduced and is now accessible to certain user groups.
- The addition of "workforcemobile", "Gentrifying Neighbourhoods", and "Affordable Housing" suggests that new modules or areas of access have been implemented, possibly to provide users with more tools or information related to these topics.

---

## Commit 57e2857
- **Date**: 2025-06-23 17:39:08 UTC
- **Message**: refactor: mls handle null closeprice
- **Explanation**:
## Summary of Changes:
- The commit changes the logic in the `processMLSProperties` function in `processAPIResponses.js` file.
- The change includes updating the assignment of `propertyClone.latestPrice` based on the condition of `property?.status === 'Closed'`.
- The update now checks for the existence of `property?.currentprice` if `property?.closeprice` is not available.
- This change may have been made to ensure that the latest price of a property is accurately determined even if some fields are missing or null.

---

## Commit 7881b5f
- **Date**: 2025-06-17 04:46:39 UTC
- **Message**: Updates @spatiallaser/map to v1.0.236
- **Explanation**:
## Summary of Changes

### bun.lock
- The version of "@spatiallaser/map" was updated from "1.0.225" to "1.0.236".
- This change includes updates to dependencies related to mapping and testing libraries.
- The change may have been made to incorporate bug fixes, security updates, or new features into the project.

### package.json
- The version of "@spatiallaser/map" was updated from "1.0.231" to "1.0.236".
- This change aligns the version of "@spatiallaser/map" across different configuration files.
- It ensures consistency in the version of the library used in the project.

---

## Commit 6f5bd7e
- **Date**: 2025-06-11 15:27:24 UTC
- **Message**: fix: new construction only filter
- **Explanation**:
## Summary of Changes

- Added a new condition to filter new construction properties when searching for properties in 'Sale' mode and using 'MLS' as the data source, while also considering the state variable `mlsSaleNewConstructionOnly`.
- The added code snippet filters the `filteredDataSource` based on the `new_construction` property of each property.
- This change may have been made to enhance user experience by providing more specific filtering options for new construction properties in the search results.

---

## Commit cf04cb7
- **Date**: 2025-06-06 05:51:40 UTC
- **Message**: chore: add AMH
- **Explanation**:
### Summary of Changes:

1. Added a new logo file `logo-amh.png` for the AMH client.
2. Updated the `userGroup` data to include information about the AMH client.
3. Updated the `userGroupAccess.json` file to include specific details about the AMH client.
4. Updated the `userGroup.js` file to include SVG content for the AMH client.
5. Updated the `Header` component to display the SVG logo for the AMH client.
6. Updated the `header.css` file to style the SVG logo for the AMH client.

### Reason for Changes:

These changes were likely made to introduce support for the AMH client within the application. This includes adding the necessary assets, updating configuration data, and modifying components to incorporate the AMH branding and features. The SVG logo provides a more dynamic and scalable representation of the AMH brand compared to a static PNG image.

---

## Commit eedbbf4
- **Date**: 2025-06-06 00:43:08 UTC
- **Message**: feat: hide intelligent comping mode for all
- **Explanation**:
## Summary of Changes:

In this commit, the code related to intelligent comping in the `Summary.js` file of the `ResultTable` component was commented out. The block of code that added the "Intelligent" option for `intelligentComping` with aggressive automatic filters was removed based on the condition `!['ILE'].includes(props.selectedUserGroup)`. This change implies that the option for intelligent comping will not be available if the selected user group is 'ILE'.

## Reason for Change:

The intelligent comping feature seems to have been disabled specifically for the user group 'ILE'. This change may have been made to restrict access to the aggressive automatic filters for this user group, possibly due to differing requirements or compatibility issues. By commenting out this code block, the behavior of the component has been adjusted to exclude the intelligent comping option for the specified user group.

---

## Commit c72bbd9
- **Date**: 2025-06-04 16:21:53 UTC
- **Message**: chore: bump map version
- **Explanation**:
## Package.json

### Changed
- Updated the version of `@spatiallaser/map` from `1.0.230` to `^1.0.231`

### Reason for Change
- The version of `@spatiallaser/map` was updated to a newer version, `^1.0.231`, possibly to include bug fixes, improvements, or new features in the package. This change ensures that the project is using the latest version of `@spatiallaser/map` for better functionality and performance.

---

## Commit be9bc7c
- **Date**: 2025-06-04 14:21:37 UTC
- **Message**: chore: bump map version
- **Explanation**:
The version of "@spatiallaser/map" in package.json was updated from "1.0.227" to "1.0.230". This change indicates that there may have been updates, bug fixes, or new features added to the "@spatiallaser/map" module, prompting the version incrementation.

---

## Commit 3e9c7d9
- **Date**: 2025-06-04 12:53:58 UTC
- **Message**: Refactor: move last updated to last one
- **Explanation**:
## Summary of Changes:
- The "Last Updated" column was removed from the list of columns to display in the Affordable Housing table.
- The "Last Updated" column was added back to the list of columns to display in the Affordable Housing table.

## Reason for Changes:
- The "Last Updated" information was initially removed but then added back, indicating that it is relevant and important to display to users in the Affordable Housing table.

---

## Commit fc63c5c
- **Date**: 2025-06-03 22:46:55 UTC
- **Message**: fix: Comments out date range filter in housing data

Temporarily disables the date range filter in the affordable housing
data filtering function by commenting it out. This change allows
for debugging and testing of other filters without the influence
of date constraints. Adds console logging to help track filter
match failures and the rendering of affordable housing data.

This adjustment is likely aimed at isolating and identifying
issues in the matching logic or performance. The expected
outcome is improved clarity in debugging efforts.
- **Explanation**:
### Summary of Changes:
- Removed the `matchesDateRange` function from the `filterAffordableData` function in `panel.tsx`.
- Added a conditional check for `matchesBeds`, `matchesBaths`, `matchesSqft`, and `matchesZipCode` in the `filterAffordableData` function.
- Commented out the check for `matchesDateRange` in the return statement of the `filterAffordableData` function.
- Added a console log statement in the `AffordableHousingPage` component to log `props.affordableHousingDataForRender`.

### Reason for Changes:
- The `matchesDateRange` function was removed, possibly because its functionality was not needed or was causing issues.
- The conditional check for `matchesBeds`, `matchesBaths`, `matchesSqft`, and `matchesZipCode` was added to ensure that all necessary filters are met before returning a match.
- The check for `matchesDateRange` in the return statement was commented out, suggesting that it may not be required or is currently not functioning correctly.
- The addition of the console log statement in the `AffordableHousingPage` component was likely added for debugging or monitoring purposes.

---

## Commit a276ac9
- **Date**: 2025-06-03 02:21:10 UTC
- **Message**: chore:

Updates @spatiallaser/map to version 1.0.227
- **Explanation**:
## Summary of Changes

- The version of "@spatiallaser/map" was updated from "1.0.225" to "1.0.227".

## Reason for Change

- The change may have been made to update the package to the latest version with potential bug fixes, performance improvements, or new features. This ensures that the project remains up-to-date and functional with the latest dependencies.

---

## Commit 933a068
- **Date**: 2025-06-02 17:35:42 UTC
- **Message**: refactor: land search filter min building coverage
- **Explanation**:
## Changes in `constants.ts`

- Changed the `max` value in the `LAND_DEVELOPMENT_FILTERS` object from 10 to 1.
  
### Reason for Change
- The change might have been made to update the maximum value limit for a specific filter in the application, potentially due to a requirement or bug fix that required this adjustment.

---

## Commit 2d0bfc3
- **Date**: 2025-06-02 17:04:48 UTC
- **Message**: refactor: land search parcel schema and init filter
- **Explanation**:
## Changes Summary
- Updated `LandParcelPropertiesSchema` in `zod-schemas.ts` to use `z.coerce.number()` for `mean_slope` and `mean_aspect` instead of `z.number()`.
- Updated `buildingCoverage` in `LAND_DEVELOPMENT_FILTERS` constant in `constants.ts` to set `isChecked` to true, change `type` to `'<='`, and update the `max` value to 10.

## Reason for Changes
- The change in `zod-schemas.ts` suggests that the `mean_slope` and `mean_aspect` values may require coercion before validation.
- The update in `constants.ts` for `buildingCoverage` may reflect a change in the filtering criteria for land development, potentially to be more restrictive with a maximum value of 10 instead of 20.

---

## Commit 51cb8c5
- **Date**: 2025-05-31 16:07:10 UTC
- **Message**: chore: bump map
- **Explanation**:
Failed to generate documentation: Error code: 400 - {'error': {'message': "This model's maximum context length is 16385 tokens. However, your messages resulted in 42707 tokens. Please reduce the length of the messages.", 'type': 'invalid_request_error', 'param': 'messages', 'code': 'context_length_exceeded'}}

---

## Commit abf45cc
- **Date**: 2025-05-29 23:37:39 UTC
- **Message**: chore: bump map version
- **Explanation**:
## Change in package.json

- Updated the version of "@spatiallaser/map" from "1.0.222" to "1.0.224"
  
### Reason for the change
- This change might have been made to incorporate bug fixes, new features, or improvements in the "@spatiallaser/map" package.

---

## Commit 46d6704
- **Date**: 2025-05-29 22:06:02 UTC
- **Message**: fix: the median rent should be '-' if none selected.
- **Explanation**:
## Summary of Changes:
- In the `TableHeader.tsx` file of the `RealtorSingleFamily` component, there was a change made in the rendering logic of the median value display.
- Previously, only the median value was displayed, or a hyphen (`-`) if the value was null.
- The change introduces a condition based on the length of `selectedRowKeys` array:
  - If `selectedRowKeys` length is 0, then a hyphen (`-`) is displayed regardless of the median value.
  - If `selectedRowKeys` length is not 0, then the median value is displayed if it exists, or a hyphen (`-`) if the median value is null.

## Reason for Change:
- This change was likely made to provide better visual feedback to users based on their interaction with the table. 
- By showing a hyphen when no rows are selected, it helps to clearly indicate the absence of a selected median value. 
- When rows are selected, displaying the actual median value or a hyphen as appropriate gives users a clearer understanding of the data being presented.

---

## Commit aaf828d
- **Date**: 2025-05-26 17:13:05 UTC
- **Message**: fix: use userEmail instead of tricon
- **Explanation**:
### Summary of Changes:

- The email address used in the getUserScorecardBatchResult function call was changed from a hardcoded email ('<EMAIL>') to a variable called userEmail.
- This change allows the function to use the value stored in the userEmail variable instead of a fixed email address, making the function more dynamic and flexible.

### Possible Reason for Change:

- The change was likely made to enable the function to work with different user email addresses based on the value stored in the userEmail variable, providing more customization and adaptability in handling user scorecard batch results.

---

## Commit 69e9c0c
- **Date**: 2025-05-26 15:37:41 UTC
- **Message**: chore: bump map
- **Explanation**:
## Changes made in the commit:
- Updated the version of "@spatiallaser/map" from "1.0.221" to "1.0.222" in the `bun.lock` file.
- Updated the version of "@spatiallaser/map" from "1.0.221" to "1.0.222" in the `package.json` file.

## Reason for the changes:
- The updates could include bug fixes, performance improvements, or new features in the "@spatiallaser/map" package.
- Ensuring that the latest version of the package is being used to maintain compatibility and take advantage of any enhancements or fixes.

---

## Commit 58630e7
- **Date**: 2025-05-26 04:39:04 UTC
- **Message**: feat: column manager added to affordable housing table
- **Explanation**:
Failed to generate documentation: Error code: 400 - {'error': {'message': "This model's maximum context length is 16385 tokens. However, your messages resulted in 22164 tokens. Please reduce the length of the messages.", 'type': 'invalid_request_error', 'param': 'messages', 'code': 'context_length_exceeded'}}

---

## Commit 1b09b1f
- **Date**: 2025-05-23 14:48:48 UTC
- **Message**: feat: major employer csv
- **Explanation**:
Failed to generate documentation: Error code: 400 - {'error': {'message': "This model's maximum context length is 16385 tokens. However, your messages resulted in 55185 tokens. Please reduce the length of the messages.", 'type': 'invalid_request_error', 'param': 'messages', 'code': 'context_length_exceeded'}}

---

## Commit 74220f8
- **Date**: 2025-05-19 07:50:10 UTC
- **Message**: feat: hot batch upload
- **Explanation**:
### Summary of Changes:
- Added a `BadgeWrapper` component around the text "Batch Comp" within a `Button` component in the `HeaderBar.tsx` file.
- Commented out the previous `span` element containing the text "Batch Comp" within the `Button` component.

### Reason for Changes:
- The `BadgeWrapper` component was likely added to improve the styling or functionality of the "Batch Comp" element within the `Button`.
- The previous `span` element was commented out, possibly to replace it with the `BadgeWrapper` component or to temporarily disable it.

---

## Commit 5e23f2b
- **Date**: 2025-05-16 15:43:05 UTC
- **Message**: chore: downgrade map version
- **Explanation**:
### Changes in package.json

- Updated the version of `@spatiallaser/map` from `"^1.0.207"` to `"1.0.207"`. 
- This change could have been made to pin the version of the package to a specific release for stability or compatibility reasons.

---

## Commit 2370049
- **Date**: 2025-05-16 13:40:08 UTC
- **Message**: feat: batch process wording
- **Explanation**:
## Summary of Changes
The commit removed some code related to uploading CSV files and added a new instruction to click a blue button to start batch processing. This change likely simplifies the user interface and streamlines the batch processing initiation process by removing the option to upload CSV files directly.

---

## Commit b2e834c
- **Date**: 2025-05-16 13:33:26 UTC
- **Message**: fix: remove csv upload
- **Explanation**:
## BatchProcessor.js Changes

- Removed the form tag wrapping the file input section and replaced it with a comment block.
- The reason for the change may be to temporarily disable or hide the file input section without completely deleting the code. This can be useful for testing or development purposes without affecting the functionality of the component.

---

## Commit dacd1d3
- **Date**: 2025-05-16 03:48:36 UTC
- **Message**: Merge pull request #434 from USDA-Prime-Ribeye-Tiramisu/429-batch-upload-modal

feat: batch upload
- **Explanation**:
Failed to generate documentation: Error code: 400 - {'error': {'message': "This model's maximum context length is 16385 tokens. However, your messages resulted in 56680 tokens. Please reduce the length of the messages.", 'type': 'invalid_request_error', 'param': 'messages', 'code': 'context_length_exceeded'}}

---

## Commit 50c56f2
- **Date**: 2025-05-14 20:36:50 UTC
- **Message**: Merge pull request #427 from USDA-Prime-Ribeye-Tiramisu/426-csv-export-issue

fix: csv issue
- **Explanation**:
## Summary of Changes:
- In the `getFormattedCSVRow` function, the line `statusData = statusData ? 'Available' : 'Closed';` was commented out by adding `//` at the beginning of the line. This change prevents the statusData from being set to 'Available' or 'Closed' based on its truthiness, and allows the `csvRowData` to take the original status data as is.
- In the `getCsvColumnDataIndex` function, the line `type === 'National SFR Operators Listings' ? 'status' : 'exists',` was changed to `type === 'National SFR Operators Listings' ? 'status' : 'status',`. This change ensures that the correct `dataIndex` value is used based on the 'type' parameter.

## Reason for Changes:
- It appears that the first change was made to preserve the original statusData value without modifying it. This change could have been made to ensure that the CSV row data accurately represents the status of the rowData being processed.
- The second change was likely made to maintain consistency and accuracy in determining the `dataIndex` value based on the 'type' parameter. This change could have been implemented to avoid confusion and ensure that the correct data is extracted for the CSV column.

---

## Commit 3d73e1e
- **Date**: 2025-05-14 19:14:35 UTC
- **Message**: Merge pull request #425 from USDA-Prime-Ribeye-Tiramisu/ruilu-cma-5-realtorcom-data-not-being-filter

fix: realtor.com comps not filtered by polygon
- **Explanation**:
### Summary of Changes:
- Added imports for `countyPolygon`, `schoolDistrictPolygon`, and `ZIPCodePolygon` from '../types'.
- Added state variables `prevSchoolDistrictProperties`, `prevCountyData`, and `prevZipCodeData`.
- Updated selectors to match the new state variables (`currentCountyData` and `currentZipCodeData`).
- Modified the filtering logic to utilize the new state variables (`currentCountyData` and `currentZipCodeData`).
- Added additional conditions in the useEffect hook to handle changes in the new state variables.
- Updated the dependencies for the useEffect hook to include the new state variables.

### Reason for Change:
The changes were made to enhance the functionality of filtering and processing real estate data based on school districts, counties, and ZIP codes. By introducing new state variables and updating the logic to incorporate them, the application can now apply more targeted filters and provide more accurate results to the users. These changes likely aim to improve the overall user experience and data accuracy within the real estate application.

---

## Commit a15c2e7
- **Date**: 2025-05-13 20:12:15 UTC
- **Message**: fix: logos
- **Explanation**:
### Summary of Changes:
- In the `Header.tsx` component, changes were made to the rendering of logos based on the `clientInfo.clientName` value.
- Previously, the logos were displayed based on the client name with specific styling for each client.
- The change introduced a condition where if the `clientInfo.clientName` is 'ILE', a placeholder div is rendered instead of displaying a logo.
- For all other client names, the logos are displayed as before with their respective styling.
- Additional client names were added with corresponding logo rendering logic.
- The rendering of the "Comparative Market Analysis" title was also updated slightly.
- An invisible divider and `AppShortcuts` component were also added for non-'ILE' clients.

### Reason for Change:
- The change was likely made to update the `Header` component to handle more client types and provide better styling flexibility.
- Introducing conditional rendering based on the client name allows for more dynamic customization of the header based on the client.
- The addition of more client names and corresponding logo rendering logic expands the flexibility of the component to cater to a wider range of clients.
- The small adjustment to the title rendering and the addition of the invisible divider and `AppShortcuts` component could be for better presentation and user experience.

---

## Commit ce7a068
- **Date**: 2025-05-12 22:14:02 UTC
- **Message**: feat: REMAXFine on contact info
- **Explanation**:
## Changes in MLSModal.js

In this commit, a change was made in the `MLSModal.js` file within the `ResultTable/MLS` directory. The change involves adding a new condition in the `MLSModal` component that checks if the group is `'REMAXFine'`. This condition was not present in the previous version of the code.

### Reason for the Change
The reason for this change could be to include the `'REMAXFine'` group in the list of groups that are allowed in the conditional check. This could be necessary for some new functionality or to accommodate specific requirements related to the `'REMAXFine'` group.

---

## Commit b4f9082
- **Date**: 2025-05-12 22:03:12 UTC
- **Message**: feat: REMAXFine on contact info
- **Explanation**:
## Change Summary:
- Added a condition to check if the `props.userGroup` includes 'REMAXFine' in addition to 'AvenueOne' in the MLSModal component.

## Reason for Change:
- This change was made to include 'REMAXFine' as a valid user group in the condition, allowing users in the 'REMAXFine' group to access certain features or functionalities in the MLSModal component.

---

## Commit ea74328
- **Date**: 2025-05-12 02:22:55 UTC
- **Message**: chore: allow DRHorton access to land parcel search
- **Explanation**:
## Change Summary
- Added "Land Parcel Search" to the list of applications accessible by the user group named "Marketing Team".

## Reason for Change
It appears that the Marketing Team needed access to the "Land Parcel Search" application for their tasks or projects, so it was included in their user group access permissions.

---

## Commit 003c05d
- **Date**: 2025-05-12 02:02:44 UTC
- **Message**: feat: fundrise community
- **Explanation**:
### Summary of Changes:
- Added the moment library for date handling.
- Added a new state property `selectedFundriseCommunity` to the `CMAState` interface.
- Added new props `subjectPropertyParcelData` and `searchingMode` to the `HeaderProps` interface.
- Implemented event listeners for handling Fundrise community selection events.
- Updated the component lifecycle methods `componentDidMount` and `componentWillUnmount` to add and remove event listeners.
- Created functions to handle postMessage events and custom DOM events related to Fundrise community selection.
- Updated the `generateUserMenuItems` function to include additional client name logos.
- Updated the connected component to include the new state property `selectedFundriseCommunity` in the props.

### Reason for Changes:
- The changes were made to enhance the functionality of handling Fundrise community selection events and updating the component state accordingly.
- The addition of new state properties and props allows for better management of data related to Fundrise community selection and search modes.
- The implementation of event listeners and event handling functions ensures that the component can respond appropriately to Fundrise community events.
- The updates to the `generateUserMenuItems` function improve the visual representation of client name logos.
- The changes in the connected component ensure that the new state property `selectedFundriseCommunity` is accessible in the component props.

---

## Commit b860ab9
- **Date**: 2025-05-07 16:10:50 UTC
- **Message**: chore: add AveOne
- **Explanation**:
### Changes Made:
1. Added a new file `Avenue_One_logo.png` to the `public/images/logo` directory.
2. Updated the `userGroup.js` file in the `GeneratePDF/InsightsPDF/utils` directory to include a new condition for userGroup `REMAXFine` to set client information for AveOne with the corresponding logo.
3. Modified the `Header.tsx` file to display the AveOne logo if `clientInfo.clientName` is 'AveOne'.
4. Added a new CSS class `.logo_AveOne` in `header.css` to style the AveOne logo.
5. Updated the `userGroup.js` file in the `utils` directory to include the same condition for userGroup `REMAXFine` to set client information for AveOne with the corresponding logo.

### Reason for Changes:
The changes were made to introduce support for a new client named AveOne, including adding the AveOne logo, updating client information for AveOne in multiple files, and styling the AveOne logo in the header. This likely indicates a new client partnership or the need to differentiate AveOne from other clients in the application.

---

## Commit fbb2dac
- **Date**: 2025-05-06 15:09:41 UTC
- **Message**: Chore: add back UpAndUp logo; add acqPortal shortcut for UpAndUp
- **Explanation**:
### Changes Made:
1. Added a new condition in the `Header` component to show a different logo when the `clientInfo.clientName` is 'UpAndUp'.
2. Updated the `userGroupAccess.json` file for the "UpAndUp" client to include the "acqPortal" app in the list of apps they have access to.

### Reason for Changes:
1. The change in the `Header` component was made to display a specific logo for the 'UpAndUp' client, as different clients may have different branding requirements.
2. The update in the `userGroupAccess.json` file was made to grant the "UpAndUp" client access to the "acqPortal" app, potentially to provide them with new functionalities or features related to that app.

---

## Commit 6408a95
- **Date**: 2025-05-05 13:58:34 UTC
- **Message**: feat: bfr median rent
- **Explanation**:
## Changes summary:

1. Added a new state `builtForRentMedianPrice` in the CMA state to store the median rent price of selected rows in the BuiltForRentTable.

2. Implemented logic in the `BuiltForRentTable` component to calculate the median rent price from selected rows and dispatch the result to the state.

3. Updated the `TableHeader` component to display the total selected rows and the median rent price, along with a button for column customization.

4. Added logic in the `TableHeader` component to handle column customization through a modal window.

5. Updated the `DEFAULT_STATE` in the CMA model to include the new state `builtForRentMedianPrice`.

## Reasons for changes:

- The changes were made to enhance the user experience by providing more relevant information, such as the median rent price, and allowing users to customize columns in the Built For Rent table.
- The addition of `builtForRentMedianPrice` state and calculation logic provides valuable insights into the distribution of rent prices in the selected rows.
- The column customization feature allows users to personalize their view of the table based on their preferences.

---

## Commit 947e9bd
- **Date**: 2025-05-02 05:09:14 UTC
- **Message**: chore: add BeaconRidge, HunterQuinn, and P2Construction
- **Explanation**:
### Summary of Changes

1. Added new logo images for `BeaconRidge` and `HunterQuinn` in the `public/images/logo` directory.
2. Updated the `userGroup.js` file in the `src/components/GeneratePDF/InsightsPDF/utils/` directory to include logic for setting client information for `BeaconRidge` and `HunterQuinn`.
3. Modified the `Header.tsx` file in the `src/components/` directory to display the new logos for `BeaconRidge` and `HunterQuinn`.
4. Updated the `header.css` file in the `src/components/` directory to include styles for the new logo images.
5. Updated the `userGroupAccess.json` file in the `src/` directory to add access information for `BeaconRidge` and `HunterQuinn`.
6. Updated the `userGroup.js` file in the `src/utils/` directory with the same changes as in the `GeneratePDF/InsightsPDF/utils/userGroup.js` file.

### Reason for Changes

The changes were made to add support for the new clients, `BeaconRidge` and `HunterQuinn`, by adding their logos, setting client information, and updating access details in the application. This allows for a more customized and personalized experience for users belonging to these client groups.

---

## Commit 6a22fb8
- **Date**: 2025-04-30 21:51:42 UTC
- **Message**: fix: ImageDetailTooltip container import issue
- **Explanation**:
## Changes Summary

- Added `imageDetailTooltipContainer` prop to `getColumnsConfig` function in `MLS.jsx` file.
- Adjusted positioning calculation for `mlsTooltip` in `MLS.jsx` file.
- Updated `customContainerRef` prop value in `getColumnsConfig` function in `columnConfig.js` file.

## Reason for Changes

- The `imageDetailTooltipContainer` prop was added to improve the functionality related to image detail tooltips.
- The positioning calculation adjustment for `mlsTooltip` was made to ensure correct positioning.
- The update to `customContainerRef` prop value was likely to ensure consistency and proper referencing of the container element.

---

## Commit d130bf1
- **Date**: 2025-04-28 19:09:03 UTC
- **Message**: feat: move toolbox behind divider
- **Explanation**:
### Changes Made
- The `<div>` element with the class `h-3.5 w-0.5 bg-BT-blue opacity-70` was removed from the code.
- An additional `<div>` element with the same class was added below the `Dropdown` component.

### Reason for Change
It appears that the `<div>` element with the class `h-3.5 w-0.5 bg-BT-blue opacity-70` was originally placed between the `Tooltip` and `Dropdown` components, but it was removed and added below the `Dropdown` component. This change might have been made to improve the layout or styling of the header bar component, ensuring consistency and better visual organization.

---

## Commit a7596c1
- **Date**: 2025-04-28 16:27:02 UTC
- **Message**: fix: disable fundrise communities for now
- **Explanation**:
### Changes in `package.json`

- Updated version of `@spatiallaser/map` from "^1.0.199" to "^1.0.200"
  
### Reason for the Change
- The change in version number may indicate a patch or minor update to `@spatiallaser/map`, possibly to fix a bug, add a feature, or improve performance. It is essential to stay up to date with dependencies to ensure the project's stability and security.

---

## Commit bc26f75
- **Date**: 2025-04-28 02:19:42 UTC
- **Message**: feat: market condition format
- **Explanation**:
### Changes in `bun.lock`

- Updated the version of `"@spatiallaser/market-condition"` to `"^0.0.26"` with the commit message "feat: market condition format".
- The previous version was `"^0.0.25"`, which was updated to `"^0.0.26"`.
- This change might have been made to introduce new features or improvements related to the market condition format.

---

## Commit c2389e5
- **Date**: 2025-04-27 20:28:34 UTC
- **Message**: chore: bump market-condition version
- **Explanation**:
### Changes in package.json

- Updated the version of `@spatiallaser/map` from "^1.0.191" to "^1.0.199"
- Updated the version of `@spatiallaser/market-condition` from "^0.0.24" to "^0.0.27"

### Reason for Change

These changes likely indicate updates or bug fixes to the `@spatiallaser/map` and `@spatiallaser/market-condition` packages. By incrementing the version numbers, the developer is signaling that there have been changes made to these packages that may impact functionality or introduce new features. It is important to stay up to date with package versions to ensure that the application continues to run smoothly and take advantage of any improvements.

---

## Commit e0a0993
- **Date**: 2025-04-26 00:06:09 UTC
- **Message**: Merge branch 'main' of https://github.com/USDA-Prime-Ribeye-Tiramisu/CMA-Standalone-v1
- **Explanation**:
## Summary of Changes:
- A condition was added to the `if` statement checking for `selectedOption.postalCode`.

## Reason for Change:
The conditional check was modified to include an additional condition `selectedOption.postalCode`, possibly to ensure that the search for a postcode only occurs when a valid postal code is present. This change could improve the functionality and accuracy of the address search feature.

---

## Commit 587b9f2
- **Date**: 2025-04-25 23:56:45 UTC
- **Message**: fix: only fetch ZIP Code boundary when ZIP Code is available when searching for a ZIP Code
- **Explanation**:
## Summary of Changes:
- A condition was added to the `if` statement checking for `selectedOption.postalCode`.

## Reason for Change:
- The conditional check was modified to include an additional condition `selectedOption.postalCode`, possibly to ensure that the search for a postcode only occurs when a valid postal code is present. This change could improve the functionality and accuracy of the address search feature.

---

## Commit c9c6efa
- **Date**: 2025-04-25 05:20:10 UTC
- **Message**: Merge branch 'main' of https://github.com/USDA-Prime-Ribeye-Tiramisu/CMA-Standalone-v1
- **Explanation**:
## Commit 1b6f053
- **Date**: 2025-04-25 05:17:13 UTC
- **Message**: feat: add timestamp and source when sending to off-market
- **Explanation**:
### Summary of Changes:
- Added a `meta` object to the data being passed to the `Summary` component.
- The `meta` object now includes `source` set to 'CMA' and `timestamp` set to the current date and time in ISO format.

### Reason for Change:
- The `meta` object was likely added to provide additional context or metadata to the component's data. This could be useful for tracking data sources or timestamps for when the data was retrieved or last updated.

---

## Commit 1b6f053
- **Date**: 2025-04-25 05:17:13 UTC
- **Message**: feat: add timestamp and source when sending to off-market
- **Explanation**:
### Summary of Changes:
- Added a `meta` object to the data being passed to the `Summary` component.
- The `meta` object now includes `source` set to 'CMA' and `timestamp` set to the current date and time in ISO format.

### Reason for Change:
- The `meta` object was likely added to provide additional context or metadata to the component's data. This could be useful for tracking data sources or timestamps for when the data was retrieved or last updated.

---

## Commit a501de9
- **Date**: 2025-04-22 16:21:24 UTC
- **Message**: fix: change radius on map when loading a saved filter
- **Explanation**:
### Summary of Changes:

- Added a new `map` variable using the `useSelector` hook to get the `map` state from the Redux store.
- Updated the destructuring assignment in the `selectedFilterValues` to include `currentRadiusMile`.
- Added logic to set the radius on the map if `currentRadiusMile` is present and valid.
- Introduced a console log statement to display the current saved filter values.
- Added a search parameter to the URL when a filter is selected.

### Reason for Changes:

- The `map` variable was added to utilize the map state in the component.
- Including `currentRadiusMile` in the destructuring assignment ensures that the component can access this value from the selected filter's filters.
- Setting the radius on the map based on the saved filter's `currentRadiusMile` provides a visual representation of the filter's radius.
- The console log statement aids in debugging and understanding the selected filter values.
- Adding a search parameter to the URL upon selecting a filter may be for updating the URL to reflect the filter selection for bookmarking or sharing purposes.

---

## Commit b082d05
- **Date**: 2025-04-22 14:13:44 UTC
- **Message**: feat: typo
- **Explanation**:
## Change Summary

- **Title Update:** Changed the titles in the psychographic table from comparing "current vs 2021" to "2025 vs 2021", "2025 vs 2024", and "2025 vs 2021".

- **Segments Display:** Updated the condition in the table to display "Top 20 Segments" instead of "Top 10 Segments" when `showMoreDetails` is true.

## Reason for Change

It appears that the changes were made to update the comparison years in the table from the current year to the year 2025, and to adjust the segment display for better presentation or accuracy. The updates likely aim to provide more relevant and up-to-date information to users using the psychographic table in the application.

---

## Commit d9244cf
- **Date**: 2025-04-22 04:03:03 UTC
- **Message**: fix: point it back to prod
- **Explanation**:
## Changes in amplify.yml

- Changed the value of `UMI_APP_SERVER_TYPE` when the `AWS_BRANCH` is "main" from `SERVER_TYPE_EXP` to `SERVER_TYPE_PROD`.
- Updated the value of `UMI_APP_SERVER_TYPE` to `SERVER_TYPE_PROD` when the `AWS_BRANCH` is "staging".
- Kept the value of `UMI_APP_SERVER_TYPE` as `SERVER_TYPE_EXP` when the `AWS_BRANCH` is "RELEASE-CANDIDATE-2".

### Reason for change
- The change was made to update the server type based on the branch for better configuration and deployment management.

---

## Commit f5d2c8d
- **Date**: 2025-04-21 20:01:26 UTC
- **Message**: fix: point to test
- **Explanation**:
## Changes in amplify.yml

- Changed the value of `UMI_APP_SERVER_TYPE` from `${SERVER_TYPE_PROD}` to `${SERVER_TYPE_EXP}` when the `AWS_BRANCH` is set to "main".
- This change was likely made to update the server type to `EXP` for the main branch, potentially for testing or development purposes.

---

## Commit 89910ba
- **Date**: 2025-04-18 03:17:06 UTC
- **Message**: refactor: put the tax rate before HOA fee
- **Explanation**:
### Summary of Changes
- Added a new section for Property Tax Rate in the Summary component of the ResultTable.
- Updated the styling of the Property Tax Rate text to have black color, bold font weight, cursor pointer, and no underline.
- Added a Tooltip with the title 'Click to show details' for the Property Tax Rate section.
- Changed the color of the Property Tax Rate text to blue and added an underline style in the previous version. 
- Updated the onClick behavior of the Property Tax Rate text to dispatch a 'saveCMAStates' action with the payload to show the Tax Details Modal.

### Reason for Changes
- The changes were made to improve the visual presentation and user interaction for the Property Tax Rate section in the Summary component.
- The styling changes aim to provide a more visually appealing and consistent design for the Property Tax Rate text.
- The inclusion of a Tooltip provides additional context and hints to users about the functionality of clicking on the Property Tax Rate text.
- The onClick behavior was updated to correctly trigger the display of the Tax Details Modal when the Property Tax Rate text is clicked.

---

## Commit e80bd45
- **Date**: 2025-04-18 03:15:56 UTC
- **Message**: feat: psychographic table - 20 segments
- **Explanation**:
### Summary of Changes:

- Added the `DownloadOutlined` icon import from `@ant-design/icons`.
- Replaced the `Select` component with the `Switch` component in the `antd` imports.
- Added a new state `showMoreDetails` as a boolean in the component's state.
- Updated the logic for slicing the `result` and `driveTimeResult` arrays based on the `showMoreDetails` state.
- Removed an unused function and integrated its functionality directly.
- Updated the column titles and rendering logic, including new columns for different years and comparisons.
- Added new functions for calculating totals and running totals.
- Updated the `handleChange` function to set the drive time value.
- Updated the `getTableDataSource` function to include a total row for both view modes.
- Added a toggle for showing more details which updates the display of segments.
- Added conditional rendering for the total row styling in the table.

### Reason for Changes:

- The changes appear to focus on improving user experience and information visibility by adding the ability to show more details in the table.
- New columns and functions have been added to enhance data presentation and comparison between different years.
- The inclusion of the `Switch` component and toggling functionality provides users with more control over the displayed information.
- The refactoring and cleanup of code suggest optimization and better organization for maintaining and extending the component in the future.

---

## Commit 0899a3b
- **Date**: 2025-04-17 14:11:47 UTC
- **Message**: feat: type error
- **Explanation**:
## Summary of Changes:

- The `PopulationDensity` component was modified to include conditional rendering based on the presence of `densityFiveRadius` and `densityTenRadius` values in `populationDensityData` before formatting and displaying them.
- Prior to the change, the values were directly formatted and displayed without any check for existence, which could have caused issues if the values were undefined.
- With the update, the code now checks if `densityFiveRadius` and `densityTenRadius` exist in `populationDensityData` before formatting and displaying them, ensuring that the component does not break if the values are missing.

---

## Commit 9a358d1
- **Date**: 2025-04-16 14:44:12 UTC
- **Message**: feat: Make the scatterplot filters persist when you change radius
- **Explanation**:
### Changes Made
- The component now stores the initial filter range when mounted, with default values if needed for 'bed', 'bath', 'sqft', and 'yearBuilt'.
- The component initializes year inputs and ensures the yearBuilt filter always starts with 0 as a minimum value.
- Functionality for updating year inputs and handling blur events has been added.
- The UI now displays the filter values and allows editing for 'yearBuilt' with input fields.
- The Slider component has been replaced with input fields for 'yearBuilt' and still uses the Slider for other filters.

### Reason for Changes
- Storing the initial filter range ensures consistent starting values for filters.
- Initializing and updating year inputs provide a better user experience for setting 'yearBuilt'.
- Allowing direct input for 'yearBuilt' provides more precise control for users.
- Using input fields for 'yearBuilt' while still using sliders for other filters may improve usability and accuracy in setting filters.

---

## Commit a815093
- **Date**: 2025-04-15 15:00:32 UTC
- **Message**: fix: badge wrapper
- **Explanation**:
## Summary of Changes:
- The `currentMapLayerOptions` prop being passed to the `BadgeWrapper` component was changed from `currentMapLayerOptions` to an empty array `[]`.
  
## Reason for Change:
- It is possible that the `currentMapLayerOptions` prop was not being used or needed in this context, so it was replaced with an empty array to remove any unnecessary data being passed to the component.

---

## Commit 90c604b
- **Date**: 2025-04-15 13:28:53 UTC
- **Message**: chore: bump map version
- **Explanation**:
### Changes in package.json

- Updated package version of `@spatiallaser/map` from `1.0.190` to `1.0.191`.
  
  This change could have been made to include bug fixes, performance improvements, or new features in the updated version of the package. It is important to stay up-to-date with package versions to ensure the stability and security of the application.

---

## Commit 8637e50
- **Date**: 2025-04-14 07:22:48 UTC
- **Message**: Feat: hot badge on help
- **Explanation**:
Failed to generate documentation: Error code: 400 - {'error': {'message': "This model's maximum context length is 16385 tokens. However, your messages resulted in 45210 tokens. Please reduce the length of the messages.", 'type': 'invalid_request_error', 'param': 'messages', 'code': 'context_length_exceeded'}}

---

## Commit 95fc1af
- **Date**: 2025-04-11 23:45:12 UTC
- **Message**: Merge branch 'main' of https://github.com/USDA-Prime-Ribeye-Tiramisu/CMA-Standalone-v1
- **Explanation**:
## Summary of Changes:
1. Added a new file `GACapital_logo.png` in the `public/images/logo` directory.
2. Updated the `userGroup.js` file in the `GeneratePDF/InsightsPDF/utils` directory to include client information for 'GACapital' with specific image and flex properties.
3. Modified the `Header.tsx` file to include the 'GACapital' logo based on client information.
4. Updated the `header.css` file to include styling for the 'GACapital' logo.
5. Added client information for 'GACapital' in the `userGroupAccess.json` file.
6. Updated the `userGroup.js` file in the `utils` directory to include client information for 'GACapital'.

## Reason for Changes:
These changes were likely made to incorporate support for the 'GACapital' client, including their specific logo and styling across different components and files within the project. The modifications ensure that the application can display the correct logo and information for the 'GACapital' client, enhancing the branding and user experience.

---

## Commit d6fb3f0
- **Date**: 2025-04-11 23:31:49 UTC
- **Message**: Merge branch 'main' of https://github.com/USDA-Prime-Ribeye-Tiramisu/CMA-Standalone-v1
- **Explanation**:
## Commit d3b9d34
- **Date**: 2025-04-11 23:16:04 UTC
- **Message**: chore: add GACapital
- **Explanation**: This commit includes several changes to incorporate support for the 'GACapital' client, such as adding their logo, updating files with client information, and styling for the logo.

## Summary of Changes:
1. Added a new file `GACapital_logo.png` in the `public/images/logo` directory.
2. Updated the `userGroup.js` file in the `GeneratePDF/InsightsPDF/utils` directory to include client information for 'GACapital' with specific image and flex properties.
3. Modified the `Header.tsx` file to include the 'GACapital' logo based on client information.
4. Updated the `header.css` file to include styling for the 'GACapital' logo.
5. Added client information for 'GACapital' in the `userGroupAccess.json` file.
6. Updated the `userGroup.js` file in the `utils` directory to include client information for 'GACapital'.

## Reason for Changes:
These changes were likely made to incorporate support for the 'GACapital' client, including their specific logo and styling across different components and files within the project. The modifications ensure that the application can display the correct logo and information for the 'GAC

---

## Commit d3b9d34
- **Date**: 2025-04-11 23:16:04 UTC
- **Message**: chore: add GACapital
- **Explanation**:
## Summary of Changes:
1. Added a new file `GACapital_logo.png` in the `public/images/logo` directory.
2. Updated the `userGroup.js` file in the `GeneratePDF/InsightsPDF/utils` directory to include client information for 'GACapital' with specific image and flex properties.
3. Modified the `Header.tsx` file to include the 'GACapital' logo based on client information.
4. Updated the `header.css` file to include styling for the 'GACapital' logo.
5. Added client information for 'GACapital' in the `userGroupAccess.json` file.
6. Updated the `userGroup.js` file in the `utils` directory to include client information for 'GACapital'.

## Reason for Changes:
These changes were likely made to incorporate support for the 'GACapital' client, including their specific logo and styling across different components and files within the project. The modifications ensure that the application can display the correct logo and information for the 'GACapital' client when accessed or used.

---

## Commit c4a08df
- **Date**: 2025-04-09 06:15:11 UTC
- **Message**: feat: bun
- **Explanation**:
Failed to generate documentation: Error code: 400 - {'error': {'message': "This model's maximum context length is 16385 tokens. However, your messages resulted in 18611 tokens. Please reduce the length of the messages.", 'type': 'invalid_request_error', 'param': 'messages', 'code': 'context_length_exceeded'}}

---

## Commit 58a5634
- **Date**: 2025-04-08 19:24:59 UTC
- **Message**: fix: tricon bfr-cluster
- **Explanation**:
### Summary of Changes:
- Updated the URL in the `getBFRClusterData` function from `/api/cma/exp/bfr-cluster/all` to `/api/cma/${serverType}/bfr-cluster/all`.

### Reason for Changes:
- It seems that the URL was updated to include the `${serverType}` variable in the path. This change might have been made to dynamically generate the URL based on the server type specified.

---

## Commit 5fb557f
- **Date**: 2025-04-07 14:00:23 UTC
- **Message**: feat: phone format
- **Explanation**:
## Summary of Changes:

- Added a new function `formatPhoneNumber` to handle formatting of phone numbers in a specific format.
- Modified the `renderPersonInfo` function to use the `formatPhoneNumber` function for displaying phone numbers.
- Removed the handling of last updated email information in the UI.

## Reason for Changes:

- The addition of the `formatPhoneNumber` function allows for consistent formatting of phone numbers displayed in the UI.
- The modification in `renderPersonInfo` function ensures that phone numbers are formatted correctly using the new `formatPhoneNumber` function.
- The removal of last updated email information in the UI might have been deemed unnecessary or out of scope for the current requirements.

---

## Commit 3c2f480
- **Date**: 2025-04-07 13:50:41 UTC
- **Message**: feat: lastReportedDatephone format
- **Explanation**:
### Changes in OwnerInformations.tsx
- Changed the `formatDate` function call from `phone.lastUpdated` to `phone.lastReportedDate`.
- This change was likely made to update the displayed information to use the `lastReportedDate` instead of `lastUpdated` for accuracy or data consistency.

---

## Commit 77a384d
- **Date**: 2025-04-07 07:10:27 UTC
- **Message**: feat: avenue one house details
- **Explanation**:
Failed to generate documentation: Error code: 429 - {'error': {'message': 'Request too large for gpt-3.5-turbo in organization org-ILcGeDDwSOw3TNvjMrDDA12f on tokens per min (TPM): Limit 200000, Requested 255758. The input or output tokens must be reduced in order to run successfully. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}

---

## Commit 858c6b9
- **Date**: 2025-04-04 20:41:24 UTC
- **Message**: fix: btr community info price 0 = N/A
- **Explanation**:
## Summary of Changes:

- The conditional rendering logic for displaying prices in the table has been updated in the `createColumns` function in the `Table.tsx` file.
- Previously, if `record.minPrice` was equal to `record.maxPrice`, it would show only the `record.minPrice`. Now, there are additional checks implemented to handle cases where either `record.minPrice` or `record.maxPrice` is `null` or `0`.
- If both `record.minPrice` and `record.maxPrice` are `null`, it will display 'N/A'.
- If `record.maxPrice` is `null` or `0`, it will only display the formatted `record.minPrice`.
- If `record.minPrice` is equal to `record.maxPrice`, it will display the formatted `record.minPrice`.
- If there is a range of prices (`record.minPrice` and `record.maxPrice` are different), it will display both values in the format of `minPrice - maxPrice`.
- This change seems to provide more accurate and user-friendly price display in the table based on different scenarios.

---

## Commit 76d9a76
- **Date**: 2025-04-04 15:54:23 UTC
- **Message**: fix: psychographic compare to 2022 not 2024
- **Explanation**:
### Changes in AreaPsychographicTable.tsx

- Added a new property `percentage2022` to the `PsychographicDataDetails` interface.
- Updated the calculation of `difference` in the `render` function to use the newly added `percentage2022` property instead of `percentage2024`.
- Updated the Tooltip title to display the `percentage2022` value instead of `percentage2024`.

### Reason for the Changes
It seems that the data for the year 2022 is now included in the calculation and display of psychographic information. This change allows for more recent data to be used and shown to users in the table.

---

## Commit 588f72c
- **Date**: 2025-04-04 15:41:43 UTC
- **Message**: fix: 2023->2024
- **Explanation**:
## Summary of Changes:
- The variable `percentage2023` was renamed to `percentage2024` in the `PsychographicDataDetails` interface.
- The tooltip in the `render` method was updated to display the percentage value for `percentage2024` instead of `percentage2023`.

## Reason for Changes:
The changes were made to reflect the correct year for the percentage values being displayed. The variables and tooltip were updated to ensure consistency and accuracy in the data being presented.

---

## Commit 5e45f16
- **Date**: 2025-04-04 15:33:28 UTC
- **Message**: fix: new spatialai table name
- **Explanation**:
### Changes in AreaPsychographicTable.tsx

- The variable `old_percentage` was renamed to `percentage2023` in the `PsychographicDataDetails` interface.
- The Tooltip title now displays the `percentage2023` instead of the old percentage.
- Calculations and comparisons in the component were adjusted accordingly to use the `percentage2023` instead of `old_percentage`.

### Reason for the Changes
These changes were likely made to provide more clarity and consistency in the codebase. Renaming `old_percentage` to `percentage2023` may better reflect the data being represented and used in the component. Updating the Tooltip title to display `percentage2023` ensures that the information shown is accurate and reflects the current data being used. This can help improve understanding and maintainability of the code.

---

## Commit c593aa0
- **Date**: 2025-04-04 05:26:53 UTC
- **Message**: Merge branch 'main' of https://github.com/USDA-Prime-Ribeye-Tiramisu/CMA-Standalone-v1
- **Explanation**:
## Commit cf00635
- **Date**: 2025-04-03 22:40:11 UTC
- **Message**: fix: SFR and HotPads comps table total selected number bug
- **Explanation**:
### Summary of Changes:

- Commented out the `useEffect` blocks responsible for updating the header table and total/median values in the NationalSFROperators component.
- The code within these blocks was checking for certain elements and updating their values based on `dataSource` and `rentMedian`.
- Changes may have been made to temporarily disable functionality for debugging or testing purposes.
- The purpose of these `useEffect` blocks may be revisited or modified in future updates.

---

## Commit cf00635
- **Date**: 2025-04-03 22:40:11 UTC
- **Message**: fix: SFR and HotPads comps table total selected number bug
- **Explanation**:
### Summary of Changes:

- Commented out the `useEffect` blocks that were responsible for updating the header table and total/median values in the NationalSFROperators component.
- The code within these `useEffect` blocks was checking for the existence of certain elements and updating their values based on the `dataSource` and `rentMedian` values.
- It is possible that these changes were made to temporarily disable the functionality controlled by the `useEffect` blocks, possibly for debugging or testing purposes.
- The purpose of these `useEffect` blocks may be revisited or modified in future updates.

---

## Commit 262bc68
- **Date**: 2025-04-03 05:03:16 UTC
- **Message**: chore: add MMG
- **Explanation**:
## Changes in userGroup.js

- Added a new condition for when the `userGroup` includes 'MMG'. 
- If 'MMG' is included in the `userGroup`, the `userInfo` object is updated with specific information such as clientName, shortName, png, flex, and svg.
- The SVG content for 'MMG' is updated with a new design.
- This change allows for customizing client information based on the user's group.

## Changes in Header.tsx

- Added a new condition to display the logo for 'MMG' client.
- The logo is displayed in a div with the class 'logo_MMG'.
- This change ensures that the correct logo is displayed for the 'MMG' client.

## Changes in header.css

- Added a new styling for the 'logo_MMG' class to define the width and height of the SVG logo for 'MMG'.
- The width is calculated based on the logo's aspect ratio to maintain the design.

## Changes in userGroupAccess.json

- Added a new entry for 'MMG' client with abbrev, app, metro, and premium information.
- This change ensures that 'MMG' client-specific details are included in the user group access data.

Overall, these changes seem to be focused on adding support and customization for the 'MMG' client, including updated client information and logo display.

---

## Commit fc30602
- **Date**: 2025-04-02 04:40:02 UTC
- **Message**: fix: use current price when closed price isn't available in some metros
- **Explanation**:
### Summary of Changes:

In this commit, a change was made to the `processMLSProperties` function in the `processAPIResponse.js` file. The change involved updating how the `latestPrice` property is determined for a property based on its status. Previously, the `latestPrice` was set to `closeprice` if the property status was 'Closed', otherwise it was set to `currentprice`.

The change made in this commit updates the logic to set the `latestPrice` to `closeprice` only if the property status is 'Closed' and the property has a `closeprice` value. This change ensures that the `latestPrice` is accurately determined for both closed and active listings based on the respective price fields available.

This change may have been made to enhance the accuracy of the `latestPrice` value assignment and better handle different scenarios for closed and active listings.

---

## Commit 7451730
- **Date**: 2025-04-01 14:30:01 UTC
- **Message**: feat: clueso.io help link
- **Explanation**:
### Summary of Changes

- Added a new file `CluesoLink.tsx` to the `src/components` directory, which contains a component `CluesoLink` that fetches a token and generates a link for help.
- Imported `CluesoLink` component in both `Header.tsx` and `HeaderBar.tsx`.
- In `Header.tsx`, added `CluesoLink` component to the header menu as a `Help` option.
- In `HeaderBar.tsx`, added `CluesoLink` component to the header bar for accessing help.
- The changes were likely made to provide a way for users to access help information related to Clueso within the application.

---

## Commit 3e5a63a
- **Date**: 2025-03-29 21:30:42 UTC
- **Message**: chore: add access to affordable housing and gentrifying neighborhoods to REMAXFine
- **Explanation**:
### Changes to userGroupAccess.json

- Added "Gentrifying Neighbourhoods" and "Affordable Housing" to the list of applications accessible by the group "Assessor".
- Previously, only "Land Parcel Search" was accessible, but now additional applications have been included.
- This change could have been made to provide the group with access to new tools and resources related to assessing properties, such as information on gentrifying neighborhoods and affordable housing options.

---

## Commit 1c1d3ba
- **Date**: 2025-03-28 20:51:45 UTC
- **Message**: chore: add REMAXFine; use REMAXFine for AvenueOne
- **Explanation**:
### Summary of Changes:

1. Added a condition to set the `appURL` based on the `selectedUserGroup` value in the `AppShortcuts.js` file. If the `selectedUserGroup` is 'REMAXFine', the `appURL` will be set to a specific URL.
   
2. Updated the `userGroupAccess.json` file to include a new user group "REMAXFine" with its corresponding abbreviation, list of apps, metro locations, and premium features.

### Reason for Changes:

1. The changes in the `AppShortcuts.js` file were made to dynamically set the `appURL` based on the `selectedUserGroup`, allowing for more flexibility and customization based on the user's group.

2. The addition of the "REMAXFine" user group in the `userGroupAccess.json` file likely reflects the need to grant specific access and privileges to users belonging to this group, including defining their abbreviation, available apps, metro locations, and premium features. This update enhances the access control and customization within the application for users in the "REMAXFine" group.

---

## Commit 22f91b2
- **Date**: 2025-03-28 13:29:08 UTC
- **Message**: feat: for demo user group, replace off-market in shortcut with portfolio underwriting portal
- **Explanation**:
### Summary of Changes:
- The `AppShortcuts` component now includes a condition for the `acqPortal` appKey, where if the selectedUserGroup is `demo-users`, the appURL will include `-portfolio-` before the appKey in the URL.
- This change was likely made to handle different URL structures based on the selectedUserGroup and appKey combination.

---

## Commit 6664316
- **Date**: 2025-03-28 13:15:15 UTC
- **Message**: feat: remove showSizeCHanger
- **Explanation**:
# Pagination Configuration Changes

In this commit, the `showSizeChanger` option was removed from the pagination configuration in multiple components within the `ResultTable` directory. This option allows users to change the number of items displayed per page in the table.

## Changes Made
- `showSizeChanger: false` was removed from the pagination configuration in the following components:
  - BTRCommunityInfoTable
  - BuiltForRentTable
  - LandCrexiTable
  - LastSalePublicRecordTable
  - RealtorMFTable
  - RealtorSFTable

## Reason for Change
The removal of the `showSizeChanger` option suggests that the ability for users to change the number of items displayed per page in the table has been disabled or no longer deemed necessary in these components. This change may have been made to simplify the user interface or to enforce a specific page layout.

---

## Commit c8177b9
- **Date**: 2025-03-23 20:22:52 UTC
- **Message**: Merge branch 'main' of https://github.com/USDA-Prime-Ribeye-Tiramisu/CMA-Standalone-v1
- **Explanation**:
## Summary of Changes

- Added a `size` property with a value of 2.
- Changed the color returned in the `color` function from `#000` to `'red'`.

## Reason for Changes

- The `size` property was likely added to adjust the size of certain elements in the chart.
- The change in color from `#000` to `'red'` suggests a visual adjustment to improve the aesthetics or readability of the chart.

---

## Commit c7f6ac6
- **Date**: 2025-03-23 19:45:10 UTC
- **Message**: refactor(comp scatter): make regression line red color
- **Explanation**:
### Summary of Changes:

- The `size` property was added with a value of 2.
- The color returned in the `color` function was changed from `#000` to `'red'`.

### Reason for Changes:

- It is likely that the `size` property was added to adjust the size of certain elements in the chart.
- The change in color from `#000` to `'red'` suggests a visual adjustment to improve the aesthetics or readability of the chart.

---

## Commit 32e5e90
- **Date**: 2025-03-21 22:01:37 UTC
- **Message**: fix: affordable housing filter bug
- **Explanation**:
### Summary of Changes:
1. Added new state variables `subjectPropertyParcelData`, `selectedRowKeys`, `CSVButtonLoading`, `activePropertyTypeFilters`, and `activeStatusFilters`.
2. Added a new function `applyAllFilters` to apply all filters.
3. Updated useEffect to reset active filters when new data is loaded and respond to changes in `compingMode` and `currentStatusMLS`.
4. Added a new useEffect to apply filters if there is affordable housing data.
5. Updated the `rowSelection` object and added functionality to handle selected row keys and trigger CSV download.
6. Updated table onChange to track and apply filters based on property type and status filters.
7. Removed commented-out code related to table columns.

### Reason for Change:
- The changes primarily focus on improving filter functionality in the Affordable Housing Panel component.
- The addition of new state variables and functions enhances the handling of filters and selected data rows.
- The useEffect updates ensure that filters are applied appropriately based on changes in comping mode, status MLS, and available data.
- The removal of unnecessary code improves the readability and efficiency of the component.

---

## Commit 10c8a21
- **Date**: 2025-03-21 17:10:45 UTC
- **Message**: refactor: use css var for template link color
- **Explanation**:
## BatchProcessor.js Changes

- Changed the color value from `#1677ff` to `var(--antd-active-blue)` in the `style` attribute for a link in BatchProcessor component.
- The color change may have been made to improve consistency with a theme or style guide that uses custom CSS variables for color values.

---

## Commit fa7149c
- **Date**: 2025-03-21 16:05:32 UTC
- **Message**: fix: batch processing template download link
- **Explanation**:
## Change Summary

In this commit, the `BatchProcessor.js` file was modified to include inline styling for the anchor element `<a>` linking to a batch template file. The changes added styles such as color, underline decoration, and font weight to the anchor element.

## Reason for Change

The changes were likely made to improve the visual presentation of the anchor link for the batch template file, making it more noticeable and aesthetically pleasing to users. The added styling helps to emphasize the link and make it stand out on the page.

---

## Commit e03c19f
- **Date**: 2025-03-21 04:16:43 UTC
- **Message**: feat: enable PDF and other premium features for ILE
- **Explanation**:
The commit updated the "premium" access permissions in the userGroupAccess.json file. Previously, the "premium" group had no access permissions, but now it includes access to various features such as "Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Gentrifying Neighbourhoods", and "Affordable Housing". This change was likely made to grant the "premium" group access to these additional features.

---

## Commit 7b1d88b
- **Date**: 2025-03-20 21:46:40 UTC
- **Message**: fix: wrong USLegacy logo url causing PDF generation to fail
- **Explanation**:
### Summary of Changes:
1. The file `USLegacy_logo.jpg` was deleted from the `public/images/logo/` directory.
2. A new file `USLegacy_logo.png` was added to the `public/images/logo/` directory.
3. In the `data.ts` file under `src/services/`, an empty line was removed (`+`) before the `postBTRPipelineCSV` function.

### Reason for Changes:
1. The deletion of `USLegacy_logo.jpg` suggests that the project might have switched from using a JPEG format for the logo to a PNG format, which is often preferred for logos due to its lossless compression.
2. The addition of `USLegacy_logo.png` indicates that the project has updated its logo file to a PNG format.
3. The removal of the empty line in the `data.ts` file could be for code cleanliness and consistency.

---

## Commit 7ac1d2e
- **Date**: 2025-03-20 00:20:29 UTC
- **Message**: Merge branch 'main' of https://github.com/USDA-Prime-Ribeye-Tiramisu/CMA-Standalone-v1
- **Explanation**:
## Commit Documentation

### Commit 3ce83fb
- **Date**: 2025-03-17 21:30:51 UTC
- **Message**: Merge branch 'main' of https://github.com/USDA-Prime-Ribeye-Tiramisu/CMA-Standalone-v1
- **Explanation**:

#### Summary of Changes:
- Reordered imports of `useSelector` and `useDispatch` from 'umi' in the file `MapDraw.js`.
- Added new state variables `realtorSingleFamilyDataForRender`, `currentRealtorDotComGeoJSON`, and `selectedRowKeysRealtorDotCom` to the state object.

#### Reason for Changes:
- Imports were reordered for consistency and code readability.
- New state variables were added to manage data related to Realtor.com listings for rendering on the map.

---

---

## Commit 3ce83fb
- **Date**: 2025-03-17 21:30:51 UTC
- **Message**: Merge branch 'main' of https://github.com/USDA-Prime-Ribeye-Tiramisu/CMA-Standalone-v1
- **Explanation**:
## Summary of Changes:

- Reordered the imports of `useSelector` and `useDispatch` from 'umi' in the file `MapDraw.js`.
- Added new state variables `realtorSingleFamilyDataForRender`, `currentRealtorDotComGeoJSON`, and `selectedRowKeysRealtorDotCom` to the state object.

### Reason for Changes:

- The imports of `useSelector` and `useDispatch` were reordered to maintain consistency and improve code readability.
- The new state variables were added to handle data related to Realtor.com listings and rendering on the map.

---

## Commit 44c6db8
- **Date**: 2025-03-17 20:46:32 UTC
- **Message**: Merge branch 'main' of https://github.com/USDA-Prime-Ribeye-Tiramisu/CMA-Standalone-v1
- **Explanation**:
## Commit b915a11
- **Date**: 2025-03-17 19:31:56 UTC
- **Message**: fix: click trashcan icon wouldn't remove realtor.com comps within hand drawn polygon
- **Explanation**:

### Summary of Changes:

- Reordered the imports of `useSelector` and `useDispatch` from 'umi' in the file `MapDraw.js`.
- Added new state variables `realtorSingleFamilyDataForRender`, `currentRealtorDotComGeoJSON`, and `selectedRowKeysRealtorDotCom` to the state object.

### Reason for Changes:

- The imports of `useSelector` and `useDispatch` were reordered to maintain consistency and improve code readability.
- The new state variables were added to handle data related to Realtor.com listings and rendering on the map.

---

## Commit b915a11
- **Date**: 2025-03-17 19:31:56 UTC
- **Message**: fix: click trashcan icon wouldn't remove realtor.com comps within hand drawn ploygon
- **Explanation**:
## Summary of Changes:

- Reordered the imports of `useSelector` and `useDispatch` from 'umi' in the file `MapDraw.js`.
- Added new state variables `realtorSingleFamilyDataForRender`, `currentRealtorDotComGeoJSON`, and `selectedRowKeysRealtorDotCom` to the state object.

## Reason for Changes:

- The imports of `useSelector` and `useDispatch` were reordered to maintain consistency and improve code readability.
- The new state variables were added to handle data related to Realtor.com listings and rendering on the map.

---

## Commit 00913e0
- **Date**: 2025-03-17 16:00:59 UTC
- **Message**: feat: add fetching realtor.com comps within polygon
- **Explanation**:
## Summary of Changes:

- Added a new function `getRealtorSingleFamilyWithinPolygon` to `src/services/data.ts` to fetch single-family properties within a polygon.
- Modified the `useRealtorDotComComps` hook in `src/libs/filter-comps/hooks/useRealtorDotComComps.ts` to handle fetching single-family properties within a polygon.
- Added a new state `prevDrawnCustomPolygons` and selector `drawnCustomPolygons` to track custom polygons drawn on the map.
- Updated the `fetchComps` function in the `useRealtorDotComComps` hook to check for changes in drawn custom polygons and fetch comps accordingly.
- Conditional logic added in `fetchComps` to call the appropriate API function based on whether custom polygons are drawn or not.

## Reasons for Changes:
The changes were likely made to enhance the functionality of fetching real estate comps by adding the capability to retrieve single-family properties within a custom polygon. This allows for more specific and targeted comparisons when analyzing real estate data for comparative market analysis. The addition of this feature provides users with more flexibility and precision in their search for comparable properties.

---

## Commit 5d69654
- **Date**: 2025-03-17 14:28:31 UTC
- **Message**: fix: click trashcan icon in radius selector wouldn't remove realtor.com comps
- **Explanation**:
### Changes in SelectRadius.js

- Added "realtorSingleFamilyDataForRender" and "currentRealtorDotComGeoJSON" to the state initialization.
- Added "selectedRowKeysRealtorDotCom" to the state initialization.

#### Reason for the change:
- It seems that the code was updated to include additional states related to realtor data rendering, possibly to enhance functionality or accommodate new features related to realtor data in the map controls.

---

## Commit 66e4018
- **Date**: 2025-03-17 07:17:50 UTC
- **Message**: style: table column widths
- **Explanation**:
### Changes Summary:

#### src/components/ResultTable/MLS/MLS.js
- Increased the width of the 'fulladdress' column from 200 to 300.
- Changed the dimensions of SVG icons in the 'Address' column.
- Adjusted the width of columns like 'distance', 'status', 'sfr_owner', 'latestPrice', 'deed_last_sale_price', 'deed_last_sale_date', and others to 100 for consistency.
- Altered the alignment of the 'sfr_owner' column to center.
- Modified the width of the 'propertysubtype' column to 100.
- Updated the width of the 'yearbuilt', 'bed', 'bath', 'size', 'area_acres', 'cdom', 'closedate', and 'psf' columns to 100.

#### src/components/ResultTable/NationalSFROperators/NationalSFROperators.js
- Expanded the width of the 'address' column from 200 to 300.
- Adjusted the width of columns like 'distance', 'status', 'sfr_owner', 'rent', 'deed_last_sale_price', 'deed_last_sale_date', and others to 100 for consistency.
- Changed the alignment of the 'brand' column to center.
- Modified the width of the 'propertysubtype' column to 100.

#### src/components/ResultTable/RealtorSingleFamily/Table.tsx
- Increased

---

## Commit afa71dc
- **Date**: 2025-03-14 14:25:22 UTC
- **Message**: Merge branch 'main' of https://github.com/USDA-Prime-Ribeye-Tiramisu/CMA-Standalone-v1
- **Explanation**:
### Commit Documentation

---

## Commit b71428e
- **Date**: 2025-03-14 12:50:18 UTC
- **Message**: feat: land crexi and showcase api
- **Explanation**:

## Changes Summary

1. **useLandData.ts**
   - Imported the `isEmpty` function from 'lodash'.
   - Updated the conditional check from `!currentPropertyAddress` to `isEmpty(currentPropertyAddress)`.

2. **Table.tsx**
   - Imported the `isEmpty` function from 'lodash'.
   - Updated the conditional check from `currentPropertyAddress` to `!isEmpty(currentPropertyAddress)`.

3. **ResultRow.js**
   - Updated the code by uncommenting the sections related to Land Showcase and Land Crexi Median calculations.

## Reason for Changes

It seems that the changes were made to implement a more robust check for empty values in the `currentPropertyAddress` variable. By importing the `isEmpty` function from 'lodash' and updating the conditional checks in the mentioned files, the code now ensures that the `currentPropertyAddress` is not empty before proceeding with further operations. Additionally, the uncommented sections in `ResultRow.js` suggest that the calculations for Land Showcase and Land Crexi Median were re-enabled, possibly after a troubleshooting or debugging phase.

---

## Commit 1bbb30e
- **Date**: 2025-03-14 12:24:16 UTC
- **Message

---

## Commit b71428e
- **Date**: 2025-03-14 12:50:18 UTC
- **Message**: feat: land crexi and showcase api
- **Explanation**:
## Changes Summary

1. **useLandData.ts**
   - Imported the `isEmpty` function from 'lodash'.
   - Updated the conditional check from `!currentPropertyAddress` to `isEmpty(currentPropertyAddress)`.

2. **Table.tsx**
   - Imported the `isEmpty` function from 'lodash'.
   - Updated the conditional check from `currentPropertyAddress` to `!isEmpty(currentPropertyAddress)`.

3. **ResultRow.js**
   - Updated the code by uncommenting the sections related to Land Showcase and Land Crexi Median calculations.

## Reason for Changes

It seems that the changes were made to implement a more robust check for empty values in the `currentPropertyAddress` variable. By importing the `isEmpty` function from 'lodash' and updating the conditional checks in the mentioned files, the code now ensures that the `currentPropertyAddress` is not empty before proceeding with further operations. Additionally, the uncommented sections in `ResultRow.js` suggest that the calculations for Land Showcase and Land Crexi Median were re-enabled, possibly after a troubleshooting or debugging phase.

---

## Commit 1bbb30e
- **Date**: 2025-03-14 12:24:16 UTC
- **Message**: feat: enable land crexi and showcase in prod
- **Explanation**:
### Changes in ResultTable.js

- Added LandShowcaseTable and LandCrexiTable components back in the ResultTable component under certain conditions.
- These components were previously commented out but have been now reinstated.
- This change suggests that the LandShowcaseTable and LandCrexiTable components are required for certain conditions in the ResultTable component.

---

## Commit 5a71ca1
- **Date**: 2025-03-14 06:43:39 UTC
- **Message**: feat: add ILE default match comping filter criteria
- **Explanation**:
### Summary of Changes:

- **Added `moment` import**: The commit adds an import statement for the `moment` library to the `useUserAuthentication.ts` file. This is likely to be used for date and time manipulation in the code.

- **Added `dateFormat` import**: The commit also adds an import statement for the `dateFormat` constant from the '../constants' file. This constant is likely used for formatting dates in a specific way throughout the code.

- **Added logic for `ILE` user group**: Within the `useUserAuthentication` function, a conditional check has been added to check if the user belongs to the `ILE` user group. If the user belongs to this group, specific data related to CMA states are dispatched using the `dispatch` function.

### Reason for Changes:

- The addition of the `moment` import suggests that the code may require date and time manipulation functionalities, which `moment` provides.
  
- The `dateFormat` constant import indicates the need for a specific date format application-wide.

- The logic for the `ILE` user group may have been added to handle specific behavior or data requirements for users belonging to this group within the application. This could involve setting particular CMA states based on the user's group for customization or access control.

---

## Commit 0d0df49
- **Date**: 2025-03-13 22:00:31 UTC
- **Message**: chore: add SmithDouglas
- **Explanation**:
### Summary of Changes:

- Added a new logo file `SmithDouglas-logo.png` under the `public/images/logo/` directory.
- Updated the `userGroup.js` file to include new client information for `SmithDouglas`.
- Modified the `Header.tsx` file to display the new `SmithDouglas` logo when the clientName is `SmithDouglas`.
- Updated the `header.css` file to include styling for the `SmithDouglas` logo.
- Added new userGroup data for `SmithDouglas` in the `userGroupAccess.json` file.
- Updated the `userGroup.js` file in the `utils` directory to include new client information for `SmithDouglas`.

### Reason for Changes:
The changes seem to have been made to incorporate support for a new client, `SmithDouglas`, in the application. This includes adding their logo, updating user group information, and ensuring their logo is displayed correctly in the header section of the application. The changes in multiple files suggest a comprehensive update to accommodate the new client's requirements and branding.

---

## Commit 88e25b3
- **Date**: 2025-03-12 14:37:51 UTC
- **Message**: feat: area psychographic table - percentage (changes) column
- **Explanation**:
The changes made in this commit involve updating the title of a table column from 'Percentage' to 'Percentage (changes)'. Additionally, the render function now takes a string parameter instead of a wildcard underscore. This adjustment may have been made to provide more context or clarity to the user viewing the table, indicating that the displayed percentage values are depicting changes.

---

## Commit b76bd45
- **Date**: 2025-03-12 13:24:25 UTC
- **Message**: feat: make area based in default
- **Explanation**:
## Changes Summary

- Updated conditional statements in various components to include a check for the existence of the `map` object before removing layers and sources.
- Reordered the options in the Segmented component in `SchoolAndDemographic` component from 'Parcel Based', 'Area Based' to 'Area Based', 'Parcel Based'.
- Updated the default value for `schoolAndDemographicTab` in `cma.js` model from 'Parcel Based' to 'Area Based'.

## Reason for Changes

- The `map` object was being accessed without validation in the conditional statements, leading to potential errors if `map` was undefined. By adding the check for `map` existence, it ensures a safer operation when removing layers and sources.
- The reordering of Segmented options in `SchoolAndDemographic` might have been done for a better user experience or to prioritize 'Area Based' information over 'Parcel Based'.
- The update in the `schoolAndDemographicTab` default value in `cma.js` may align better with the user's workflow or data availability.

---

## Commit 448f5b2
- **Date**: 2025-03-11 19:48:42 UTC
- **Message**: fix: pool allowed back to true after switching to match mode
- **Explanation**:
## Summary of Changes

In this commit, the code in the `generateSmartFilterValues` function within `filterFunctions.js` was modified. Specifically, the conditional check that sets `smartFilterValues.selectedPoolAllowed` to true based on the presence of a specific item in the `filterExpression` array was commented out. This suggests that the developer might have temporarily disabled this functionality for debugging or testing purposes.

---

## Commit 16bad9e
- **Date**: 2025-03-11 19:28:40 UTC
- **Message**: fix: display realtor.com comps status as closed
- **Explanation**:
## Summary of Changes:
- The code inside the `render` function of the `RealtorSFTable` component was modified to always display "Closed" instead of dynamically displaying "Closed" or "Active" based on the `listeddate` property of the `record`.
- The original ternary operator was commented out and replaced with a hardcoded value of "Closed".

## Reason for Change:
- It seems that the condition based on the `record.listeddate` property may no longer be necessary or causing issues, so the decision was made to always display "Closed" in this particular scenario.

---

## Commit 1bad4dc
- **Date**: 2025-03-11 19:01:12 UTC
- **Message**: fix: USLegacy missing logo
- **Explanation**:
## Changes Made

- **Header.tsx**:
  - Changed the rendering of the USLegacy logo from an `<img>` element to a `<div>` element with the class `styles.logo_USLegacy` containing `clientInfo.svg`. This change was made to improve consistency in rendering logos across different client names.

- **header.css**:
  - Updated the CSS styling for the `.logo_USLegacy svg` selector, changing the width calculation from `500px` to `212px` with a different ratio calculation. This adjustment was likely made to better align the size of the USLegacy logo with the overall design layout.

---

## Commit 413517d
- **Date**: 2025-03-11 14:55:47 UTC
- **Message**: fix: missing lot size filter value in shareable url causing CMA to filter out all MLS comps

feat: add support for marketRentPreference in shareable url
- **Explanation**:
### Changes Made:
1. In `general.js`:
   - Added a `marketRentPreference` property to the query object in the `loadSubjectPropertyFromURL` function.
   - Added a `preference` property to the query object in the `loadSubjectPropertyFromURL` function.
   - Added a `preference` property to the query object in the `loadSubjectPropertyFromURL` function.

2. In `ShareSubjectPropertyButton.js`:
   - Added 'LotSize' to the list of filter values.
   - Updated the `filterValuesInShareableURL` string to include the `marketRentPreference` property.

### Reason for Changes:
The changes in `general.js` adding `marketRentPreference` and `preference` properties to the query object suggest that there is now a need to handle or utilize this information in the functions. 

In `ShareSubjectPropertyButton.js`, the addition of 'LotSize' and the inclusion of the `marketRentPreference` property in the `filterValuesInShareableURL` string indicate that this information needs to be shared or included in the URL being generated for sharing subject property information.

---

## Commit 8e28029
- **Date**: 2025-03-11 14:31:16 UTC
- **Message**: Merge branch 'main' of https://github.com/USDA-Prime-Ribeye-Tiramisu/CMA-Standalone-v1
- **Explanation**:
## Summary of Changes:

1. Commit 24ecb77:
   - Removed the code related to displaying the "Project Type" property in the BTRPipeLineTooltip component by deleting the corresponding <div> element.
   - Likely removed the "Project Type" property because it was no longer relevant or required to be displayed in the tooltip. This change could be due to updated requirements or design decisions.

2. Commit 62c0b8f:
   - Removed the 'Intelligent' option from the Summary component for certain user groups by using a conditional statement based on the selectedUserGroup prop.
   - The 'Intelligent' option is now only displayed if the selectedUserGroup is not 'ILE'.
   - This change was likely made to customize the options available in the Summary component based on the user group, ensuring that the appropriate filters are displayed for each user group.

---

## Commit 24ecb77
- **Date**: 2025-03-11 02:25:50 UTC
- **Message**: fix: remove project type from newly subdivided lot
- **Explanation**:
## Summary of Changes:
- The code related to displaying the "Project Type" property in the BTRPipeLineTooltip component was removed by deleting the corresponding <div> element.

## Reason for Changes:
- The "Project Type" property might have been removed because it was no longer relevant or required to be displayed in the tooltip. This change could be a result of updated requirements or design decisions.

---

## Commit 62c0b8f
- **Date**: 2025-03-10 21:20:05 UTC
- **Message**: feat: remove intelligent comping for ILE
- **Explanation**:
### Summary of Changes

- Removed the 'Intelligent' option from the Summary component for certain user groups by using a conditional statement based on the selectedUserGroup prop.
- The 'Intelligent' option with aggressive automatic filters is now only displayed if the selectedUserGroup is not 'ILE'.
- This change was likely made to customize the options available in the Summary component based on the user group, ensuring that the appropriate filters are displayed for each user group.

---

## Commit 8856bd5
- **Date**: 2025-03-10 20:46:30 UTC
- **Message**: Merge branch 'main' of https://github.com/USDA-Prime-Ribeye-Tiramisu/CMA-Standalone-v1
- **Explanation**:
## Summary of Changes

### Commit 34b366c
- **Date**: 2025-03-10 16:56:09 UTC
- **Message**: fix: Greystar missing logo
- **Explanation**:
  - Changes in Header Component: Added a condition to display the Greystar logo if `clientInfo.clientName` is 'Greystar'. This allows for dynamic rendering of the logo based on the client's name. The new image element is styled with the class `logo_Greystar`.

### Commit 8b807f4
- **Date**: 2025-03-10 16:44:54 UTC
- **Message**: chore: add Rithm logo file for pdf report

### Reason for Changes:
- The changes in commit 34b366c were made to address the issue of the Greystar logo missing, ensuring the correct logo is displayed based on the client's name. The addition of the condition and styling allows for improved customization and branding within the application.

---

## Commit 34b366c
- **Date**: 2025-03-10 16:56:09 UTC
- **Message**: fix: Greystar missing logo
- **Explanation**:
### Changes in Header Component

- Added a condition to display a logo image if `clientInfo.clientName` is 'Greystar'.
- This change allows for dynamic rendering of the Greystar logo based on the client's name.
- The new image element with the Greystar logo is styled with the class `logo_Greystar`.

---

## Commit 8b807f4
- **Date**: 2025-03-10 16:44:54 UTC
- **Message**: chore: add Rithm logo file for pdf report
- **Explanation**:
## Change Summary:
- A new file `Rithm_logo.png` was added to the `/public/images/logo` directory.
- The new file has a file mode of `100644` and its contents differ from `/dev/null`.

## Reason for Change:
- The new logo file `Rithm_logo.png` was likely added to the repository to update or replace the existing logo with a new version or design.

---

## Commit 5ac14db
- **Date**: 2025-03-10 16:18:53 UTC
- **Message**: Merge branch 'main' of https://github.com/USDA-Prime-Ribeye-Tiramisu/CMA-Standalone-v1
- **Explanation**:
## Summary of Changes

### Commit 2469a69
- **Date**: 2025-03-10 16:05:49 UTC
- **Message**: fix: table column head word wrapping issue caused by antd table breaking change

#### Changes Made:
- Added style rule to set the width of `.ant-input-number` to 80px within `#filtersWrapper`.
- Added style rule to set the minimum width of `.ant-table-column-title` to auto within `#root .ant-table-wrapper`.

#### Reasons for Changes:
- To address the word wrapping issue with table column header caused by antd table breaking change.
- Ensuring consistency in styling of input number fields and allowing flexibility for column title width based on content.

### Commit 57a228c
- **Date**: 2025-03-10 15:22:59 UTC
- **Message**: fix: latest price should be decided by status, not whether currentprice exists

#### Reason for Change:
- Change made to ensure that the latest price is determined by the status, rather than solely by the existence of current price.

---

## Commit 2469a69
- **Date**: 2025-03-10 16:05:49 UTC
- **Message**: fix: table column head word wrapping issue caused by antd table breaking change
- **Explanation**:
### Global CSS Changes

- Added a new style rule to set the width of `.ant-input-number` to 80px within `#filtersWrapper`.
- Added a new style rule to set the minimum width of `.ant-table-column-title` to auto within `#root .ant-table-wrapper`.

#### Reason for the Changes
- The width of the `.ant-input-number` elements within `#filtersWrapper` was set to 80px, likely to ensure consistency in the styling of input number fields.
- The minimum width of the `.ant-table-column-title` within `#root .ant-table-wrapper` was set to auto, possibly to allow the column title to expand or shrink based on its content.

---

## Commit 57a228c
- **Date**: 2025-03-10 15:22:59 UTC
- **Message**: fix: latest price should be decided by status, not whether currentprice exists
- **Explanation**:
## Summary of Changes:
- The `processMLSProperties` function in `processAPIResponses.js` was modified to update how the `latestPrice` property is determined for a property.
- Previously, the `latestPrice` was set based on `property.closeprice` if it existed, otherwise, it defaulted to `property.currentprice`.
- The change now checks if the `status` of the property is `'Closed'` and sets `latestPrice` accordingly to either `closeprice` or `currentprice`.
- This change likely allows for more accurate handling of different property statuses and their corresponding prices.

---

## Commit 08fd062
- **Date**: 2025-03-10 14:21:46 UTC
- **Message**: Merge branch 'main' of https://github.com/USDA-Prime-Ribeye-Tiramisu/CMA-Standalone-v1
- **Explanation**:
## Summary of Changes:
1. Modified the `BFRClusterLayer` component to include dynamic tooltip content and improved tooltip handling based on feature properties.
2. Added a new `BTRCommunityInfoLayer` component with tooltip functionality to display community information.
3. Updated the `BadgeWrapper` component to calculate badge text and color based on layer options for improved user experience.
4. Updated the version of `@spatiallaser/map` in `package.json` from `1.0.186` to `^1.0.187` for potential bug fixes or new features.

## Reason for Changes:
1. Enhance user experience by providing dynamic tooltips and improved handling for the `BFRClusterLayer` component.
2. Introduce a new component, `BTRCommunityInfoLayer`, to display community information with tooltip functionality increasing user interaction.
3. Improve the display of badge text and color in the `BadgeWrapper` component based on selected layer options for easier understanding.
4. Update the version of `@spatiallaser/map` in `package.json` to ensure compatibility with new features or bug fixes.

---

## Commit 0695a6d
- **Date**: 2025-03-10 12:52:48 UTC
- **Message**: feat: site plan generator and monthly image thing are disabled
- **Explanation**:
### Summary

In this commit, the `disabled` attribute for two buttons (`Monthly Images` and `Site Plan Generator`) in the `HeaderBar` component was changed to `true`. Additionally, comments were added to the existing code that was disabling these buttons based on certain conditions.

### Reason for Change

It appears that the buttons were previously being disabled based on the user group or access permissions, but in this commit, the decision was made to always disable these buttons by setting `disabled` to `true`. This could be for various reasons such as temporarily removing access to these features, simplifying the logic, or resolving issues related to user group checks.

---

## Commit 6ad5f1d
- **Date**: 2025-03-08 22:54:48 UTC
- **Message**: feat: new header bar menu
- **Explanation**:
### Summary of Changes:
1. Updated the version of `@spatiallaser/map` from `1.0.186` to `^1.0.187` in `package.json`.
2. Refactored the `BadgeWrapper` component in `BadgeWrapper.tsx` to calculate badge text and color based on layer options.
3. Added logic in the `BFRClusterLayer` component to generate tooltip content dynamically and improved tooltip handling.
4. Added a new component `BTRCommunityInfoLayer` with tooltip functionality in `BTRCommunityInfoLayer.js`.

### Reason for Changes:
1. The package version of `@spatiallaser/map` was updated, possibly to include bug fixes or new features in the newer version.
2. Refactoring the `BadgeWrapper` component ensures dynamic display of badge text and color based on selected layer options for better user experience.
3. The changes in `BFRClusterLayer` enhance the tooltip functionality and improve tooltip handling for better interactivity.
4. The `BTRCommunityInfoLayer` component was added to provide tooltip functionality for displaying community information, enhancing user interaction with the map interface.

---

## Commit ed3cea0
- **Date**: 2025-03-08 18:54:22 UTC
- **Message**: Merge branch 'main' of https://github.com/USDA-Prime-Ribeye-Tiramisu/CMA-Standalone-v1
- **Explanation**:
## Commit 182146e
- **Date**: 2025-03-08 18:49:31 UTC
- **Message**: fix: pool allowed not changing to false when comping mode is switched from ALL to Match
- **Explanation**: Updated the initial state of `selectedPoolAllowed` in Filters2.js and Summary.js to ensure correct functionality.

### Summary of Changes

#### Filters2.js
- Removed duplicate `selectedPoolAllowed: true` in the initial state object.

#### Summary.js
- Changed the `selectedPoolAllowed` property to `false` in the `Summary` component.
- This change was likely made to update the initial state of `selectedPoolAllowed` and improve application functionality.

---

## Commit 182146e
- **Date**: 2025-03-08 18:49:31 UTC
- **Message**: fix: pool allowed not changing to false when comping mode is switched from ALL to Match
- **Explanation**:
## Summary of Changes

### Filters2.js
- Removed duplicate `selectedPoolAllowed: true` in the initial state object.

### Summary.js
- Changed the `selectedPoolAllowed` property to `false` in the `Summary` component.
- This change might have been made to update the initial state of `selectedPoolAllowed` in order to reflect the correct value or functionality in the application.

---

## Commit 6412f50
- **Date**: 2025-03-07 15:46:18 UTC
- **Message**: refactor: scorecard batch template download link
- **Explanation**:
## Changes in Scorecard.js

- Updated the URL for downloading `sbti_address.xlsx` from `https://spat-scorecard.s3.amazonaws.com/scorecard_templates/sbti_address.xlsx` to `https://spat-scorecard.s3.us-east-1.amazonaws.com/scorecard_templates/sbti_address.xlsx`.
- Updated the URL for downloading `sbti_coordinates.xlsx` from `https://spat-scorecard.s3.amazonaws.com/scorecard_templates/sbti_coordinates.xlsx` to `https://spat-scorecard.s3.us-east-1.amazonaws.com/scorecard_templates/sbti_coordinates.xlsx`.

### Reason for Change
The URLs for downloading the excel sheets were updated, possibly due to changes in the storage location or to improve performance by using a different server location.

---

## Commit fd9f944
- **Date**: 2025-03-07 15:42:36 UTC
- **Message**: feat: match land search parcel detail with map parcel detail
- **Explanation**:
### Changes Made:
- Added the `alternativeApn` field to the parcel data object, sourced from `parcel.estated.owners.apn`.
- Modified the `mailingAddress`, `mailingCity`, `mailingState`, and `mailingZip` fields to be sourced from `parcel.estated.owners` if available, otherwise falling back to the original `parcel` object.
- Added `last_sale_date`, `last_sale_amount`, `improvementValue`, `landValue`, `parcelValue`, and `propertyTax` fields to the `countyProvidedValues` section of the parcel data object, sourced from `parcel.estated`.
- Added a new `propertySalesAndValue` section to the parcel data object.
- Added a new `TAXPROPER` view to `currentView` in the LandBreakdownProvider component.
- Added functionality to fetch AVM data and tax proper details data for the `TAXPROPER` view.
- Added a new TaxProper component to display tax-related information.
- Added new functions `getParcelAVMData` and `getTaxProperDetailsData` to the `services/data.ts` file.

### Reason for Changes:
- Enhancing parcel data by including additional information such as alternative APN and improved mailing address details.
- Providing more detailed and accurate property sales and value information.
- Adding a new view for tax information to provide users with in-depth property tax details.
- Introducing functionality

---

## Commit 583bdd4
- **Date**: 2025-03-06 20:38:19 UTC
- **Message**: chore: bump map version
- **Explanation**:
### Changes Made:
1. Updated the version of `"@spatiallaser/map"` dependency from `^1.0.184` to `^1.0.185` in `package.json`.

### Reason for the Change:
The change in the dependency version of `"@spatiallaser/map"` could be due to bug fixes, performance improvements, or new features introduced in the updated version `^1.0.185`. This change ensures that the project uses the latest version of the dependency to leverage any improvements or fixes provided in the newer release.

---

## Commit 3e03087
- **Date**: 2025-03-06 02:38:04 UTC
- **Message**: ci: remove /amplify and aws-exports.js for multiple region deployment
- **Explanation**:
### Changes Summary:
Several files related to AWS Amplify configuration and settings were deleted in this commit. These files included configuration for authentication, backend settings, project information, and more.

### Reason for Changes:
The changes may have been made to clean up unnecessary or unused configuration files, streamline the project structure, or update settings that were no longer needed or relevant to the project's current state. Deleting these files helps in reducing clutter and maintaining a more organized project structure.

---

## Commit c78145f
- **Date**: 2025-03-05 15:00:18 UTC
- **Message**: chore: bump map version
- **Explanation**:
### Changes in package.json

- Updated version of `@spatiallaser/map` from `1.0.183` to `1.0.184`.

### Reason for Change
- This change likely includes bug fixes, new features, or improvements in the `@spatiallaser/map` package. Version updates often indicate enhancements or fixes to existing functionality, hence updating to the newer version may provide better performance or resolve issues that were present in the previous version.

---

## Commit 8b2b76e
- **Date**: 2025-03-05 11:45:20 UTC
- **Message**: chore: bump map version
- **Explanation**:
### Changes to package.json

- Updated the version of `@spatiallaser/map` from `1.0.181` to `1.0.183`.
  
### Reason for the change

- The change might have been made to include bug fixes, performance improvements, or new features introduced in version `1.0.183` of `@spatiallaser/map`. It is important to keep dependencies updated to maintain the overall stability and functionality of the project.

---

## Commit 18a53ad
- **Date**: 2025-03-03 18:13:28 UTC
- **Message**: chore: bump map version
- **Explanation**:
### Changes in Commit

#### bun.lockb
- Binary files `a/bun.lockb` and `b/bun.lockb` differ.
  
#### package.json
- Updated version of `@spatiallaser/map` from "^1.0.179" to "^1.0.181".
- No other changes made.
  
### Reason for Changes
- Most likely, the version of `@spatiallaser/map` was updated to incorporate bug fixes, new features, or improvements in the module.

---

## Commit 2f369b8
- **Date**: 2025-03-02 04:24:37 UTC
- **Message**: fix: add rent/sold price range to saved filters
- **Explanation**:
### Summary of Changes:
- Added new constants `minRentPrice`, `maxRentPrice`, `minSoldPrice`, and `maxSoldPrice` using `useSelector` to retrieve values from the `state.CMA`.
- Updated the `case 'smartFilter'` and `case 'noFilter'` in the switch statement to include the newly added constants.
  
### Reason for Change:
The changes were made to improve the functionality of the `SavedFilters` component by adding additional filter parameters related to rent and sold prices. This would allow for more precise filtering options when using the component with different filter modes.

---

## Commit 061e525
- **Date**: 2025-02-28 22:02:57 UTC
- **Message**: feat: clear header space for ILE
- **Explanation**:
### Summary of Changes:
- The commit updated the render logic for displaying client logos in the Header component.
- The changes introduced conditional rendering based on the client's name.
- If the client name is 'ILE', a placeholder div with height 36 is rendered instead of the logo.
- For other client names, the corresponding logo is displayed based on the clientInfo data.
- The commit also included changes related to the display of 'Comparative' and 'Market Analysis' text.
- A divider and AppShortcuts component were added below the logos.

### Reason for Changes:
- The changes were likely made to improve the customization and flexibility of displaying client logos in the Header component.
- By introducing conditional rendering, the code can now cater to different client names and display the appropriate logos.
- The addition of text display for 'Comparative' and 'Market Analysis' enhances the visual presentation of the Header.
- The inclusion of a divider and AppShortcuts component improves the overall user experience and functionality of the Header component.

---

## Commit 5dad365
- **Date**: 2025-02-28 20:22:56 UTC
- **Message**: feat: add retry for placekey api requests to deal with too many requests error
- **Explanation**:
## Summary of Changes
- Added `axiosRetry` library to handle retries with specific conditions for axios requests in BatchProcessor.js.
- Configured axios retries to attempt a maximum of 999 times with a retry interval of 20 seconds between attempts.
- Added a condition to only retry requests when a 429 (Too Many Requests) error is received from the server. 

## Reason for Change
The change was likely made to improve the robustness of the application when making API requests, specifically when interacting with the Placekey bulk API. By implementing retries with defined conditions, the application can better handle situations where the server responds with a 429 error, allowing for smoother processing of requests and potentially reducing the impact of rate limiting.

---

## Commit 6632463
- **Date**: 2025-02-28 17:36:38 UTC
- **Message**: feat: add secondavenue to public housing
- **Explanation**:
## HeaderBar Component Changes

- Added `'SecondAvenue'` to the list of conditions where certain elements should be disabled or hidden for specific user groups.
- The condition was updated in three places within the `HeaderBar` component to reflect this change.
- This change was likely made to accommodate a new user group or feature called `'SecondAvenue'` that should have different behavior compared to the existing user groups.

---

## Commit 6e96e1c
- **Date**: 2025-02-28 17:05:39 UTC
- **Message**: feat: roll back to old HeaderBar
- **Explanation**:
### Change Summary:
- Removed `BadgeWrapper` import from `HeaderBar.tsx` file
- Removed commented out code related to `BTR` and `Batch Comp`
- Changed the styling of the `Row` component by removing `gap-2`, `w-screen`, `py-1`, `border-t`, `border-b`, `border-gray-200`, `border-opacity-5`, and `bg-white` classes
- Updated the `Dropdown` component with key `workforce dropdown`, replacing menu items with `menuItemsWorkforce` and adding an `onClick` event handler
- Updated the `Dropdown` component with key `chain stores dropdown`, replacing menu items with `menuItemsChainStores` and adding an `onClick` event handler
- Updated the `Dropdown` component with key `land development dropdown`, replacing menu items with new items and adding an `onClick` event handler
- Added a new `Dropdown` component with key `heatmap dropdown` and menu items
- Updated the `Tooltip` component with key `affordable housing tooltip` to display a message based on user group access
- Updated the `Button` component with key `heatmap button` inside the `chain stores dropdown`
- Added a new `Tooltip` component with key `btr submission tooltip` and a `Button` component to trigger the submission of the BTR Pipeline

### Reason for Change:
- The changes seem to involve refactoring and updating the UI components in the `

---

## Commit 4f2ddbf
- **Date**: 2025-02-28 17:01:55 UTC
- **Message**: chore: bump map version (add bfr cluster back
- **Explanation**:
### Change in package.json

- **Changed Dependency Version**: Updated the dependency version of `@spatiallaser/map` from `^1.0.178` to `^1.0.179`.

### Reason for the Change

- This change might have been made to incorporate bug fixes, security patches, or additional features provided in the newer version of the `@spatiallaser/map` package. It is essential to stay updated with the latest versions of dependencies to ensure the application runs smoothly and efficiently.

---

## Commit 7a42868
- **Date**: 2025-02-28 16:57:26 UTC
- **Message**: feat: SecondAvenue affordable housing
- **Explanation**:
## Summary of Changes:
- Added `'SecondAvenue'` to the list of user groups that trigger a condition in the `ResultTable` component.
- Updated the condition to include `'SecondAvenue'` as a valid user group along with `'demo-users'` and `'HON'`.
- This change was likely made to ensure that the new user group `'SecondAvenue'` is included in the condition to trigger specific behavior in the component.

---

## Commit f4caaba
- **Date**: 2025-02-27 22:48:27 UTC
- **Message**: feat: BTR Tab HOTTTTTTT
- **Explanation**:
### Summary of Changes:

1. Updated `@spatiallaser/map` dependency version in `package.json` from `"1.0.178"` to `"^1.0.178"`.
2. Modified the UI elements in the `HeaderBar.tsx` component:
   - Replaced the `BadgeWrapper` component with a `span` element for the "Batch Comp" button.
   - Replaced the text "BTR" with a `BadgeWrapper` component for the BTR button.

### Reason for Changes:

1. The dependency version update in `package.json` to `"^1.0.178"` indicates a likely desire to include any compatible patch releases automatically in the future.
2. The changes in the `HeaderBar.tsx` component suggest a UI update for better styling and consistency in the application's header section.

---

## Commit 2d02c16
- **Date**: 2025-02-27 22:24:30 UTC
- **Message**: feat: organise btr tab
- **Explanation**:
### Changes Made:
1. Added a new file `BadgeWrapper.tsx` in the `HeaderBar` component folder.
2. Imported `Badge` component from 'antd' in `BadgeWrapper.tsx`.
3. Created `BadgeWrapper` functional component with props `badgeText`, `buttonText`, and `color`.
4. Implemented badge design with dynamic background color, text color, padding, border radius, font size, font weight, and shadow in `BadgeWrapper`.
5. Passed `badgeText`, `buttonText`, and `color` props to `BadgeWrapper` component in `HeaderBar.tsx`.
6. Added a new file `BFRClusterLayer.js` in the `MapCMA` component folder.
7. Implemented BFR Cluster layer with point and label style based on geojson data in `BFRClusterLayer`.
8. Created tooltip HTML content and added mouse event handlers for tooltip in `BFRClusterLayer`.
9. Fetch BFR Cluster data from API in `getBFRClusterData` service function.
10. Updated table columns in `Table.tsx` with filters and rendering logic for Land Crexi data.
11. Added default "Land" option for status filter and populated property type options dynamically in `useTableFilters`.
12. Updated `MapCMA.js` to include `BFRClusterLayer`.
13. Updated `data.ts` to include fetching BFR Cluster data function.

### Reasons for Changes:
1. Added a

---

## Commit 578a2be
- **Date**: 2025-02-27 22:00:12 UTC
- **Message**: chore: bump map version
- **Explanation**:
### Changes Made
1. Updated the version of `"@spatiallaser/map"` from "1.0.175" to "1.0.178" in `package.json`.

### Reason for Change
This change could indicate a bug fix, feature enhancement, or a security update in the `"@spatiallaser/map"` package. Ensuring that the latest version is being used helps in utilizing any new improvements or fixes provided by the package maintainers.

---

## Commit e5e5398
- **Date**: 2025-02-27 17:28:49 UTC
- **Message**: fix: useTableFilters handle null values
- **Explanation**:
### Summary of Changes:

1. **bun.lockb**: The binary file `bun.lockb` was changed, but the specific differences were not provided.

2. **useTableFilters.ts**:
   - The function `createNumericOptions` was modified to accept a parameter `extractFn` that now returns a value of type `number | null`.
   - The return values in the function were also adjusted to handle `null` values properly by using the nullish coalescing operator `??` to provide default values when `null` is encountered.
   - The `value` and `text` properties of the returned objects were updated to handle `null` values by providing a default hyphen `-` if the value is `null`.

### Reason for Changes:

1. **bun.lockb**: Without the specific details of the changes, it is unclear why this binary file was modified.

2. **useTableFilters.ts**:
   - The changes to `createNumericOptions` likely aim to handle `null` values returned by `extractFn` more effectively and ensure proper sorting of numeric values. The adjustments to `value` and `text` properties indicate a desire to display consistent and informative options even when dealing with `null` values.

---

## Commit 77816bb
- **Date**: 2025-02-27 15:55:23 UTC
- **Message**: fix: style.load listener not cleaned up causing comps to linger on map when switching between map themes
- **Explanation**:
### Summary of Changes

- The `styleLoad` function was added to each of the MapLayers components to handle showing/hiding price markers when the map style changes.
- The `map.on('style.load', styleLoad)` method was added to replace the previous `map.once('style.load', ...)` method in each component.
- An `map.off('style.load', styleLoad)` call was added to each component's cleanup function to remove the event listener.
- The `showHidePriceMarkers` function is called within the `styleLoad` function to handle marker display based on the current map style.
- These changes were likely implemented to improve code consistency and maintainability across the different MapLayers components, ensuring that price markers are correctly displayed when the map style changes.

---

## Commit 5c50db1
- **Date**: 2025-02-27 14:27:30 UTC
- **Message**: fix: dedupe secondary portal listings results
- **Explanation**:
### Summary of Changes:
- Added a deduplication process to remove duplicate entries in the `result` array based on the `id` property.
- Updated the `realtorSingleFamilyData` and `realtorSingleFamilyDataForRender` properties in the payload object to use the deduplicated `dedupedResult` array instead of the original `result` array.

### Reason for Change:
It seems that there was a need to prevent duplicate entries in the `realtorSingleFamilyData` and `realtorSingleFamilyDataForRender` arrays. By filtering out duplicates based on the `id` property, the code ensures that only unique entries are stored in these arrays. This change helps maintain data integrity and reduces redundancy in the rendered table components.

---

## Commit 4d4a33e
- **Date**: 2025-02-27 13:28:16 UTC
- **Message**: Merge branch 'main' of https://github.com/USDA-Prime-Ribeye-Tiramisu/CMA-Standalone-v1
- **Explanation**:
## Summary of Changes:

### Commit c4c9c5a
- **Date**: 2025-02-27 13:22:16 UTC
- **Message**: fix: some style.load event listener weren't cleaned up, causing comps to show up in map in MFR tab when map style changes
- **Changes**:
  - Changed `map.on` event listener to `map.once` in various MapLayers and MapCMA component.
  - Ensured event handler function is executed only once for improved performance and to prevent memory leaks.
- **Reason for Change**:
  - To address an issue where markers were not copied correctly in response to Map style changes, improving functionality and performance.


---

## Commit c4c9c5a
- **Date**: 2025-02-27 13:22:16 UTC
- **Message**: fix: some style.load event listener weren't cleaned up, causing comps to show up in map in MFR tab when map style changes
- **Explanation**:
## Changes in MapCMA Component and MapLayers
- The `map.on` event listener was changed to `map.once` in all MapLayers (BTOwnedLayer, HotPadsLayer, MLSLayer, MobileDataLayer, MultiFamilyLayer, NationalOperatorLayer, NewBuildsLayer, PadSplitLayer) and MapCMA component (MapCMA.js).
- This change ensures that the event handler function is executed only once, improving performance and preventing potential memory leaks.
- The change was made in response to Map style changes to ensure markers are copied over correctly.

---

## Commit 77bbf94
- **Date**: 2025-02-27 05:38:49 UTC
- **Message**: fix: layer issue 3
- **Explanation**:
### Summary of Changes:
- The HotPadsLayer and MLSLayer components were updated in this commit.
- The changes allow price markers to be displayed on both "Tab 1" and "Comp Insights" tabs.
- The logic for showing/hiding price markers based on zoom level was also adjusted.
- Price markers are now removed when not on "Tab 1" or "Comp Insights" to improve user experience.

### Reason for Changes:
- The changes were likely made to enhance the user experience by displaying price markers on both tabs, making the functionality more flexible.
- Adjusting the logic for showing/hiding price markers based on tab selection and zoom level helps ensure that the markers are displayed appropriately.
- Removing price markers when not on the relevant tabs ensures a cleaner and more focused interface for users.

---

## Commit f4a307d
- **Date**: 2025-02-27 05:06:14 UTC
- **Message**: fix: layer issue 2
- **Explanation**:
### Summary of Changes:

1. Added layers based on selectedCompTables in MapCMA.js:
   - Added MLSLayer if 'MLS' is selected and searchingMode is not 'Land'.
   - Added NationalOperatorLayer if 'National SFR Operators Listings' is selected.
   - Added HotPadsLayer if 'Portal Listings' is selected.

2. Removed redundancies from the HotPadsLayer.js:
   - Optimized the showHidePriceMarkers function to only update price markers when necessary based on conditions like map zoom level and tab selection.
   - Refactored code to use useRef for storing and accessing values that need to be passed between functions.
   - Improved event handling for mouse interactions and zoom changes on the map.

3. Updated MLSLayer.js:
   - Added a console log for debugging purposes.

### Reason for Changes:

1. The changes in MapCMA.js were made to dynamically add layers based on the selectedCompTables to enhance the user experience and provide more relevant information on the map.

2. The changes in HotPadsLayer.js were made to optimize the performance of the HotPadsLayer component by improving the handling of price markers based on map zoom level and tab selection. Refactoring the code using useRef and optimizing event handling helps in better managing the display of markers.

3. The console log added in MLSLayer.js seems to be for debugging a potential bug related to cmaTabKey, suggesting that it was added to investigate and fix any issues

---

## Commit 404108c
- **Date**: 2025-02-27 03:54:07 UTC
- **Message**: fix: layer issue
- **Explanation**:
## Changes Summary

- In the `MapCMA.js` file:
  - Removed the conditional rendering of `<BuiltForRentLayer />` component based on `props.selectedCompTables.includes('Built For Rent')` and `props.cmaTabKey === '1'` and replaced it with a direct check for `props.cmaTabKey === '9'`.
- In the `HotPadsLayer.js` file:
  - Added a new `cmaTabKey` state using `useSelector`.
  - Updated the logic in `showHidePriceMarkers` to include checking for `cmaTabKey === '1'`.
  - Corrected a typo in the `geojsonData` state name from `geojsonData` to `geojonData`.
  - Refactored the logic regarding the display of price markers based on the zoom level and `cmaTabKey` value.
- In the `MLSLayer.js` file:
  - Added a new constant `zoomLevelToShowPriceMarkers` to define the zoom level threshold for showing price markers.
  - Updated the logic in `showHidePriceMarkers` to include checking if `cmaTabKey === '1'`.
  - Updated the logic in many useEffect hooks to handle the changes in `cmaTabKey` and showing/hiding price markers accordingly.
  - Updated the logic to set price markers based on the current zoom level.
  - Added event listeners for map events and cleanup on unmount

---

## Commit c83a03a
- **Date**: 2025-02-25 16:32:51 UTC
- **Message**: fix: MLS price marker still exist when tab switching
- **Explanation**:
### Change Summary:
- Added a new `cmaTabKey` state variable using the `useSelector` hook to the `MLS Layer` component.
- Added a new `useEffect` hook to check if `cmaTabKey` is not equal to '1' and the `priceMarkersMLS` object is not empty, then remove price markers.
- Updated the `showHidePriceMarkers` function to check if `cmaTabKey` is equal to '1' before showing price markers.
- Modified the return statement to conditionally render the Geojson layers based on the value of `cmaTabKey`.

### Reason for Change:
- The changes seem to be related to displaying or hiding price markers on the map based on the value of `cmaTabKey`. The `cmaTabKey` state variable appears to control the display of certain elements on the map based on its value.
- The modifications were likely made to enhance the user experience by dynamically showing or hiding price markers based on the selected tab in the CMA application.

---

## Commit 8b160da
- **Date**: 2025-02-25 16:24:43 UTC
- **Message**: fix: bookmark fullAddress error
- **Explanation**:
## Summary of Changes
- The `fullAddress` key within the `currentPropertyAddress` object was commented out by adding `//` before it.
- The value assigned to `fullAddress` key was `property.address`.
- This change might have been made to temporarily disable or ignore the `fullAddress` key without deleting it completely.

---

## Commit b33b5a4
- **Date**: 2025-02-25 15:31:36 UTC
- **Message**: fix: tab 9 height
- **Explanation**:
## Changes
- Added a new CSS rule for `#CMA_TAB-panel-9` to set its height to 100%.

## Reason for Change
The change was made to ensure that `#CMA_TAB-panel-9` has a height of 100% in the globalVars.css file, likely to maintain consistency or meet design requirements.

---

## Commit 00f1cfe
- **Date**: 2025-02-24 21:23:51 UTC
- **Message**: fix: restore to old version batch processor
- **Explanation**:
### Summary of Changes:
1. Added a new file `BatchProcessor.js` with significant changes related to importing components, defining constants, handling file uploads, and updating UI elements for batch processing functionality.
2. Deleted the file `BatchProcessor.tsx`, which seems to have been replaced by the new `BatchProcessor.js` file.
3. The changes in `BatchProcessor.js` include updating column options, handling file uploads and processing, updating UI components, and managing batch processing progress.
4. The changes appear to enhance the batch processing feature, making it more robust and user-friendly.

### Reason for Changes:
1. Introducing new file `BatchProcessor.js` for improved functionality and performance related to batch processing in the application.
2. Replacing the old `BatchProcessor.tsx` file with the new implementation to address possible issues or enhance the existing features.
3. Updating constants, components, and logic in `BatchProcessor.js` to streamline batch processing operations and provide a better user experience.
4. Overall, the changes aim to optimize the batch processing feature in the application and enhance user interaction with the functionality.

---

## Commit d7b951a
- **Date**: 2025-02-24 21:14:32 UTC
- **Message**: fix: no api requests before a location is selected
- **Explanation**:
## Summary of Changes:

### src/components/ResultTable/BTRCommunityInfo/hooks/useBTRCommunityData.ts
- Added import statement for `isEmpty` from lodash library.
- Added a conditional check before calling `fetchData()` to only fetch data if `currentPropertyAddress` is not empty.

### src/components/ResultTable/RealtorSingleFamily/Table.tsx
- Added import statement for `isEmpty` from lodash library.
- Added a conditional check before calling `fetchLandComp()` to only fetch data if `currentPropertyAddress` is not empty.

## Reason for Changes:
The changes were made to prevent unnecessary data fetching when `currentPropertyAddress` is empty in both `useBTRCommunityData` and `RealtorSFTable` components. By adding the conditional check, the code ensures that data is only fetched when there is a valid address to fetch data for, improving efficiency and reducing unnecessary API calls.

---

## Commit ec169c9
- **Date**: 2025-02-24 19:32:09 UTC
- **Message**: feat: new batch api
- **Explanation**:
### Summary of Changes:

1. **Added BatchProcessorV2 Component**: 
   - A new component `BatchProcessorV2` was added to the project. This component is an updated version of the existing `BatchProcessor` component with improvements in file upload handling and processing indicators.
  
2. **Updated BuiltForRentTable Component**:
   - The `useTableData` hook in the `BuiltForRentTable` component was optimized using `useMemo` for efficient computation of filtered data based on specified filters.
  
3. **Added V2 API Endpoints**: 
   - New API endpoints `postBatchProcessingFileDataV2` and `getBatchProcessingProgressDataV2` were added to support batch processing of files with improved functionality.

4. **Minor Changes**:
   - Code comments were updated or added for clarification.
   - Console logs and unnecessary dependencies were removed or cleaned up.
  
### Reasons for Changes:

1. **BatchProcessor Update**:
   - The `BatchProcessorV2` component was introduced to enhance the file upload functionality, processing indicators, and overall user experience.
  
2. **BuiltForRentTable Optimization**:
   - The `useTableData` hook in the `BuiltForRentTable` was optimized with `useMemo` to improve performance and reactivity when applying filters.
  
3. **API Enhancements**:
   - The addition of V2 API endpoints reflects improvements in the backend service for batch processing, leading to

---

## Commit bcf5f20
- **Date**: 2025-02-24 17:33:05 UTC
- **Message**: fix: BFR layer not loaded
- **Explanation**:
### Summary of Changes:

- Added a new condition to render the `BuiltForRentLayer` component if `props.cmaTabKey` is equal to `'9'`.
- This change allows for the `BuiltForRentLayer` component to be displayed when the specified tab key is selected.

### Reason for Changes:

- The change was most likely made to enhance the functionality of the component by adding the ability to display the `BuiltForRentLayer` when the corresponding tab key is active. This ensures that the correct layer is rendered based on the selected tab, providing a more interactive and informative user experience.

---

## Commit cb1ecef
- **Date**: 2025-02-24 01:52:26 UTC
- **Message**: Merge branch 'main' of https://github.com/USDA-Prime-Ribeye-Tiramisu/CMA-Standalone-v1
- **Explanation**:
## Commit de3b7c0
- **Date**: 2025-02-24 01:49:55 UTC
- **Message**: chore: add Tricon
- **Explanation**:
### Summary of Changes:

1. **Package.json Changes:**
   - Updated various dependencies to newer versions for improved project functionality and enhancements.
   - Included updates for dependencies like `@ant-design/charts`, `@ant-design/pro-layout`, `@aws-amplify/ui-react`, and others.
   - Updated `framer-motion` and `fuse.js` for performance and stability improvements.

2. **Added Tricon Logo:**
   - Added `Tricon_logo.png` to `public/images/logo/`.
   - Adjusted `Header.tsx` and `header.css` to display the Tricon logo based on client information.

3. **User Group Access Changes:**
   - Added user group access info for the "Tricon" client.
   - Modified `userGroup.js` to incorporate Tricon details and display the logo for the client.

### Reason for Changes:
The changes were made to accommodate the addition of the new "Tricon" client. By updating dependencies and integrating specific client details like the logo and access information, the application can now seamlessly support and cater to the needs of the Tricon client.

---

## Commit de3b7c0
- **Date**: 2025-02-24 01:49:55 UTC
- **Message**: chore: add Tricon
- **Explanation**:
### Summary of Changes:

1. **Package.json Changes:**
   - Updated various dependencies to newer versions, such as `@ant-design/charts`, `@ant-design/pro-layout`, `@aws-amplify/ui-react`, `@mapbox/polyline`, and more, to ensure the project uses the latest features and improvements.
   - Some dependencies like `framer-motion` and `fuse.js` were also updated to their newer versions for better performance and stability.

2. **Added Tricon Logo:**
   - Added a new image file named `Tricon_logo.png` in the `public/images/logo/` directory.
   - Modified the `Header.tsx` file and `header.css` styles to display the Tricon logo based on the client information.

3. **User Group Access Changes:**
   - Added user group access information for the "Tricon" client, including application availability and premium features.
   - Updated the `userGroup.js` file to include details and display the Tricon logo when the client is detected.

### Reason for Changes:
These changes were made to accommodate the addition of a new client, "Tricon," to the application. By updating dependencies and incorporating client-specific details like the logo and access information, the application can now support and cater to the requirements of the Tricon client seamlessly.

---

## Commit ceb026f
- **Date**: 2025-02-22 23:07:02 UTC
- **Message**: chore: bump map version
- **Explanation**:
## Change in `package.json`

- Updated the version of `@spatiallaser/map` from "1.0.173" to "1.0.175".
  
### Reason for the Change:

- The change in version number may indicate bug fixes, performance improvements, security updates, or new features added to the `@spatiallaser/map` package. It is important to stay up-to-date with the latest versions to ensure the software is running smoothly and efficiently.

---

## Commit 598a348
- **Date**: 2025-02-22 23:06:18 UTC
- **Message**: feat: hide land showcase and crexi median on prod
- **Explanation**:
### Summary of Changes
- Commented out rendering of Land Showcase Median and Land Crexi Median sections in the ResultRow component.
- Removed corresponding div elements and class names associated with Land Showcase Median and Land Crexi Median sections.

### Reason for the Change
It seems like the developer decided to temporarily disable the rendering of the Land Showcase Median and Land Crexi Median sections in the ResultRow component. This could have been done for testing purposes, to prioritize other features, or to refine the layout of the component.

---

## Commit 28b77fc
- **Date**: 2025-02-22 17:48:35 UTC
- **Message**: feat: hide land showcase and crexi table on prod
- **Explanation**:
### Summary of Changes:

- Two components, `LandShowcaseTable` and `LandCrexiTable`, were commented out in the `ResultTable` component.
  
### Reason for Change:

- It seems that the two components were temporarily removed from the render logic, possibly to test or troubleshoot an issue related to the `ResultTable` component. The comment indicates that they may be re-added later.

---

## Commit 6b532f1
- **Date**: 2025-02-22 04:07:09 UTC
- **Message**: feat: make All mode the default when opening CMA
- **Explanation**:
## Summary
In this commit, the selected filter for user authentication in the `useUserAuthentication` hook was changed from 'smartFilter' to 'noFilter'. Additionally, the property `selectedPoolAllowed` was updated to evaluate if the userGroup includes 'VentureREI' as true or false.

## Reason for Change
It seems that the decision was made to always set the filter to 'noFilter' regardless of the user's email address. The change in the `selectedPoolAllowed` property was likely made to simplify the logic and make it more explicit by directly checking if the userGroup includes 'VentureREI' instead of using a ternary operator.

---

## Commit ac5391b
- **Date**: 2025-02-22 03:53:59 UTC
- **Message**: feat: merge test to main
- **Explanation**:
Failed to generate documentation: Error code: 400 - {'error': {'message': "This model's maximum context length is 16385 tokens. However, your messages resulted in 68344 tokens. Please reduce the length of the messages.", 'type': 'invalid_request_error', 'param': 'messages', 'code': 'context_length_exceeded'}}

---

## Commit 85ec938
- **Date**: 2025-02-21 19:40:40 UTC
- **Message**: feat: document yml
- **Explanation**:
### Changes Made:
- Added a new Python script `generate_docs.py` in `.github/scripts/` to generate documentation for commits using GitHub API and OpenAI.
- Created a new workflow file `document-RELEASE-CANDIDATE-2.yml` to trigger AI documentation generation on the `RELEASE-CANDIDATE-2` branch.
- Created a new workflow file `document-main.yml` to trigger AI documentation generation on the `main` branch.

### Reason for Changes:
- The new Python script `generate_docs.py` automates the process of generating documentation for commits using GitHub API and OpenAI to provide concise insights into the changes made in a commit.
- The new workflow files `document-RELEASE-CANDIDATE-2.yml` and `document-main.yml` ensure that AI-generated documentation is consistently created for commits on the respective branches, enhancing documentation consistency and accuracy.
