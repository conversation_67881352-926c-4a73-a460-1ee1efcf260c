{"files.exclude": {"amplify/.config": true, "amplify/**/*-parameters.json": true, "amplify/**/amplify.state": true, "amplify/**/transform.conf.json": true, "amplify/#current-cloud-backend": true, "amplify/backend/amplify-meta.json": true, "amplify/backend/awscloudformation": true}, "cSpell.words": ["blockgroup", "careof", "cass", "cbsa", "classcd", "classdscrp", "errorno", "gisacre", "gissqft", "hmstd", "improvval", "juris", "landval", "lbcs", "lowparcelid", "mailadd", "mapgrid", "mapsco", "nbhd", "parval", "parvaltype", "prvassdval", "prv<PERSON><PERSON>r", "revalyr", "saddno", "saddstr", "saddsttyp", "schldscrp", "schltxcd", "scity", "szip", "usecode", "usedesc", "zcta"]}