{"private": true, "scripts": {"dev": "umi dev", "build": "umi build", "postinstall": "umi setup", "setup": "umi setup", "start": "bun run dev", "prepare": "husky"}, "lint-staged": {"*.{js,jsx,less,md,json}": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "dependencies": {"@ant-design/charts": "^1.4.3", "@ant-design/cssinjs": "^1.23.0", "@ant-design/pro-layout": "^7.22.3", "@antv/scale": "^0.4.16", "@aws-amplify/ui-react": "^6.9.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hotjar/browser": "^1.0.9", "@mapbox/polyline": "^1.2.1", "@mapbox/togeojson": "^0.16.2", "@sentry/browser": "^9.40.0", "@sentry/react": "^8.55.0", "@spatiallaser/map": "^1.0.284", "@spatiallaser/market-condition": "^0.0.30", "@spatiallaser/market-condition-history": "^0.0.14", "@spatiallaser/menu-bar": "1.0.5", "@spatiallaser/spreadsheet-upload": "^3.4.0", "@turf/area": "^6.5.0", "@turf/bbox": "^6.5.0", "@turf/boolean-point-in-polygon": "^6.5.0", "@turf/center-of-mass": "^6.5.0", "@turf/circle": "^6.5.0", "@turf/distance": "^6.5.0", "@turf/flatten": "^6.5.0", "@turf/helpers": "^6.5.0", "@turf/intersect": "^6.5.0", "@turf/turf": "^7.2.0", "@turf/union": "^6.5.0", "@types/lodash.debounce": "^4.0.9", "@types/mapbox-gl": "^3.4.1", "antd": "^5.24.1", "aws-amplify": "^6.13.1", "axios": "^1.7.9", "axios-retry": "^4.5.0", "csvjson-json2csv": "^1.0.3", "d3-regression": "^1.3.10", "d3-scale": "^4.0.2", "dayjs": "^1.11.13", "framer-motion": "^6.5.1", "fuse.js": "^7.1.0", "he": "^1.2.0", "html-to-image": "1.11.11", "immutability-helper": "^3.1.1", "install": "^0.13.0", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "jstat": "^1.9.6", "jszip": "^3.10.1", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "lodash.difference": "^4.5.0", "lodash.dropright": "^4.1.1", "lodash.isempty": "^4.4.0", "lodash.isequal": "^4.5.0", "lucide-react": "^0.460.0", "mapbox-gl": "^2.15.0", "moment": "^2.30.1", "papaparse": "^5.5.2", "pdf-lib": "^1.17.1", "query-string": "^9.1.1", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-csv-reader": "^3.5.2", "react-custom-scrollbars-2": "^4.5.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-icons": "^5.5.0", "react-mosaic-component": "^6.1.1", "react-query": "^3.39.3", "react-resizable": "^3.0.5", "react-responsive-carousel": "^3.2.23", "react-virtualized-auto-sizer": "^1.0.25", "react-window": "^1.8.11", "rtree": "^1.4.2", "tailwind-merge": "^3.3.1", "tokml": "^0.4.0", "umi": "^4.4.5", "umi-request": "^1.4.0", "usehooks-ts": "^3.1.1", "web-worker": "^1.5.0", "xml-formatter": "^3.6.4", "zod": "^3.24.2"}, "devDependencies": {"@ant-design/icons": "^4.8.3", "@ant-design/moment-webpack-plugin": "^1.0.0", "@sentry/webpack-plugin": "^2.23.0", "@swc/core": "^1.10.18", "@types/he": "^1.2.3", "@types/lodash.isempty": "^4.4.9", "@types/lodash.isequal": "^4.5.8", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@umijs/plugins": "^4.4.5", "husky": "^9.1.7", "lint-staged": "^15.4.3", "prettier": "^2.8.8", "prettier-plugin-organize-imports": "^3.2.4", "prettier-plugin-packagejson": "^2.5.8", "tailwindcss": "^3.4.17", "typescript": "^5.7.3"}}