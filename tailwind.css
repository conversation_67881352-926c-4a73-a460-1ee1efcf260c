@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 142.1 76.2% 36.3%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --radius: 0rem;
    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);
    --chart-6: oklch(0.752 0.167 19); /* warm coral */
    --chart-7: oklch(0.624 0.155 285); /* muted purple */
    --chart-8: oklch(0.698 0.143 155); /* grassy green */
    --chart-9: oklch(0.558 0.12 220); /* slate blue */
    --chart-10: oklch(0.808 0.182 95); /* goldenrod */
    --chart-11: oklch(0.475 0.09 10); /* burnt orange */
    --chart-12: oklch(0.68 0.12 330); /* soft magenta */
    --chart-13: oklch(0.726 0.16 120); /* fresh green */
    --chart-14: oklch(0.63 0.11 190); /* teal blue */
    --chart-15: oklch(0.574 0.095 270); /* violet gray */
    --font-sans: 'DM Sans', 'Inter', sans-serif;
    --font-serif: 'Crimson Pro', 'Inter', serif;
    --control-panel-width: 300px;
    --grid-panel-width: 400px;
    --data-panel-width: 275px;
    --search-bar-height: 64px;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 20 14.3% 4.1%;
    --foreground: 0 0% 95%;
    --card: 24 9.8% 10%;
    --card-foreground: 0 0% 95%;
    --popover: 0 0% 9%;
    --popover-foreground: 0 0% 95%;
    --primary: 142.1 70.6% 45.3%;
    --primary-foreground: 144.9 80.4% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 15%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 12 6.5% 15.1%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 85.7% 97.3%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
    --chart-6: oklch(0.55 0.23 200); /* bluish teal */
    --chart-7: oklch(0.63 0.25 35); /* warm gold */
    --chart-8: oklch(0.5 0.22 120); /* earthy green */
    --chart-9: oklch(0.6 0.26 250); /* cool indigo */
    --chart-10: oklch(0.68 0.21 5); /* crimson red */
    --chart-11: oklch(0.52 0.21 310); /* moody magenta */
    --chart-12: oklch(0.6 0.24 95); /* amber mustard */
    --chart-13: oklch(0.58 0.2 160); /* sea green */
    --chart-14: oklch(0.48 0.22 225); /* ocean blue */
    --chart-15: oklch(0.62 0.23 280); /* dreamy violet */
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}
