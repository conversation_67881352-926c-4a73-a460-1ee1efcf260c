import moment from 'moment';
import {
  MLSListingSummaryChartCBSAData,
  MLSListingSummaryChartCountyData,
  MLSListingSummaryChartMultipolygonData,
  MLSListingSummaryChartSchoolDistrictData,
  MLSListingSummaryChartSphereData,
  MLSListingSummaryChartZIPCodeData,
  checkAllowedCoordinatesData,
  checkAllowedCountyData,
  checkAllowedMetroData,
  checkAllowedStateData,
  getAPNOwnerNameData,
  getAddressAutoCompleteData,
  getAdjustedRentAndSaleData,
  getBTOwnedPropertiesWithinPolygonsData,
  getBTOwnedPropertiesWithinRadiusData,
  getBatchMLSPropertyImagesData,
  getBrokerOfficeInfoData,
  getClientAllowedAccessCoordinatesData,
  getClientAllowedAccessCountyData,
  getClientAllowedAccessMetroData,
  getClientAllowedAccessStateData,
  getCountyData,
  getCountyOfPointData,
  getDistrictMapScopeData,
  getFilteredParcelAdditionalData,
  getFilteredParcelWithinMetroData,
  getFloodZoneData,
  getGoogleForwardGeocodingData,
  getHotPadsData,
  getHotPadsDataWithinPolygonsData,
  getHouseDetailsData,
  getLeaseExpiryAOIData,
  getLeaseExpiryAOIDataWithinPolygon,
  getLeaseExpiryAOIDataWithinZIPCode,
  getMLSListingSummaryPointWithinLayerData,
  getMLSListingSummaryWithinCountyData,
  getMLSListingSummaryWithinDistrictData,
  getMLSListingSummaryWithinMetroData,
  getMLSListingSummaryWithinPolygonData,
  getMLSListingSummaryWithinSphereData,
  getMLSListingSummaryWithinZIPCodeData,
  getMLSPropertiesWithinPolygonsData,
  getMLSPropertiesWithinRadiusData,
  getMapboxForwardGeocodingData,
  getMapboxIsochroneData,
  getMapboxReverseGeocodingData,
  getMarketRentCompareData,
  getMobileData,
  getMultiFamilyPropertiesWithinPolygonsData,
  getMultiFamilyPropertiesWithinRadiusData,
  getNationalOperatorsPropertiesWithinPolygonsData,
  getNationalOperatorsPropertiesWithinRadiusData,
  getNearestSubdivisionsData,
  getNewBuildsWithinPolygonData,
  getNewBuildsWithinRadiusData,
  getOtherOwnersDetails,
  getPadSplitData,
  getParcelBoundaryMapScopeData,
  getParcelMapScopeData,
  getParcelOwnerSummaryData,
  getParcelOwnerSummaryWithinPolygonsData,
  getPlacekeyData,
  getSavedMLSCompsData,
  getSavedSFRCompsData,
  getSchoolDistrictOfPointData,
  getScorecardAnalyticsByConsultantData,
  getScorecardAnalyticsByCountyData,
  getScorecardAnalyticsByDateData,
  getScorecardAnalyticsByDevManagerData,
  getScorecardAnalyticsBySubmarketData,
  getScorecardData,
  getSingleParcelWithPlacekeyData,
  getUserBatchProcessingRecordData,
  getUserScorecardRecordData,
  getZIPCodeBySearchData,
  getZipCodeData,
  getZipCodeOfPointData,
  saveCompsData,
  sendPropertyToOffMarketData,
} from '../services/data';
// import { message } from 'antd';
import isEmpty from 'lodash.isempty';
import isEqual from 'lodash.isequal';
import {
  filterDataBySubjectPolygon,
  filterTableDataSource,
  generateManualFilterExpression,
  generateSmartFilterExpression,
  generateSmartFilterValues,
} from '../components/Filters/filterFunctions';
import {
  isWithinDistance,
  setHOAFeeColor,
  setPropertyOwnerColor,
  withinSubscribedMetro,
} from '../utils/geography';
import {
  processMLSListingSummaryChartResponse,
  processMLSProperties,
  processMarketConditionResponse,
  processMarketRentCompareResponse,
  processSFRProperties,
} from '../utils/processAPIResponses';
import { capitalize } from '../utils/strings';
// import { xorWith } from 'lodash';
// import { S2L2ALayer, setAuthToken, ApiType, MimeTypes, CRS_EPSG4326, BBox } from '@sentinel-hub/sentinelhub-js';
import {
  processForwardGeocodingResponse,
  processReverseGeocodingResponse,
} from '@/utils/processGeocodingResponses';
import { default as turf_area } from '@turf/area';
import { default as turf_bbox } from '@turf/bbox';
import { default as turf_centerOfMass } from '@turf/center-of-mass';
import { isObject, multiPolygon } from '@turf/helpers';
import { default as turf_intersect } from '@turf/intersect';
import turf_union from '@turf/union';
import { message } from 'antd';
import filterValuesDefault from '../components/Filters/filterValuesDefault.json';
import {
  getParcelBoundariesFromMap,
  injectMapProcessingLoader,
  removeMapProcessingLoader,
} from '../components/MapCMA/MapUtility/general';
import { geojsonTemplate } from '../constants';
import { convertAdjustedParamsFromAPIToUI } from '../utils/convertProperties';
import { generateGeoJSON } from '../utils/generateGeoJSON';

import { default as turf_distance } from '@turf/distance';

import { point } from '@turf/helpers';

let currentSphereAbortController = null;
let compSphereAbortController = null;

let currentZIPCodeAbortController = null;
let compZIPCodeAbortController = null;

let currentDistrictAbortController = null;
let compDistrictAbortController = null;

let currentMetroAbortController = null;
let compMetroAbortController = null;

let fairMarketRentAbortController = null;

const dateFormat = 'YYYY-MM-DD';

const abortFetches = () => {
  if (currentSphereAbortController) currentSphereAbortController.abort();
  if (compSphereAbortController) compSphereAbortController.abort();
  if (currentZIPCodeAbortController) currentZIPCodeAbortController.abort();
  if (compZIPCodeAbortController) compZIPCodeAbortController.abort();
  if (currentDistrictAbortController) currentDistrictAbortController.abort();
  if (compDistrictAbortController) compDistrictAbortController.abort();
  if (currentMetroAbortController) currentMetroAbortController.abort();
  if (compMetroAbortController) compMetroAbortController.abort();
  if (fairMarketRentAbortController) fairMarketRentAbortController.abort();
};

export const DEFAULT_STATE = {
  map: null,
  eventCoordinates: [], // subject property coordinates
  currentRadiusMile: 0.5,
  currentMLSProperties: [],
  currentNationalOperatorsProperties: [],
  currentHotPadsProperties: [],
  currentPadSplitProperties: [],
  currentMLSPropertiesFiltered: [],
  currentNationalOperatorsPropertiesFiltered: [],
  currentHotPadsPropertiesFiltered: [],
  currentPadSplitPropertiesFiltered: [],
  currentMultiFamilyProperties: [],
  currentBTOwnedProperties: [],
  currentMLSGeoJSON: geojsonTemplate,
  currentNationalOperatorsGeoJSON: geojsonTemplate,
  currentHotPadsGeoJSON: geojsonTemplate,
  currentRealtorDotComGeoJSON: geojsonTemplate,
  currentPadSplitGeoJSON: geojsonTemplate,
  currentMultiFamilyGeoJSON: geojsonTemplate,
  currentParcelMapScopeGeoJSON: geojsonTemplate,
  currentParcelBoundaryMapScopeGeoJSON: geojsonTemplate,
  currentDistrictMapScopeGeoJSON: geojsonTemplate,
  currentActivityCenterMapScopeGeoJSON: geojsonTemplate,
  currentBTOwnedGeoJSON: geojsonTemplate,
  showBatchProcessor: false,

  currentPropertyAddress: {},
  chooseList: [],
  currentHighlightCoordinates: [],
  currentMapLayer: '',
  currentStartMLS: moment().subtract(90, 'days').format(dateFormat),
  currentEndMLS: moment().format(dateFormat),
  currentStatusMLS: 'Closed', // Closed, Pending, Active, status
  subjectPropertyParcelData: {},
  isDistrictFilterOn: false,
  currentMapboxFiltersNationalOperators: [], // for filters
  priceHighlightMarker: 0,
  typeHighlightMarker: '',
  hoverPropertyDetails: {}, // properties of listings currently hovered on
  showHoverPropertyDetails: false,
  expDateFilterOn: false,
  UserBatchProcessingRecord: [],
  modeToFindSubjectProperty: '', // for deciding what to use for shareable url: address or lngLat
  userGroup: [],
  selectedUserGroup: '',
  userEmail: '',
  mapExpandedView: false,
  openedMapControllerOption: '',
  currentMapThemeOption: 'Automatic',
  currentMLSPropertyImages: [],
  selectedMLProperty: {},
  openMLSImageModal: false,
  MLSImageModalIsLoading: false,
  currentParcelOwnerSummary: {},
  printMediaContent: false,
  allSubdivisionsFromSearch: [],
  currentSubdivision: {},
  coordinatesSubdivision: [],
  // filter values
  minBeds: filterValuesDefault.minBeds,
  maxBeds: filterValuesDefault.maxBeds,
  relationBeds: 'between',
  minBaths: filterValuesDefault.minBaths,
  maxBaths: filterValuesDefault.maxBaths,
  relationBaths: 'between',
  minSqft: filterValuesDefault.minSqft,
  maxSqft: filterValuesDefault.maxSqft,
  relationSqft: 'between',
  minLotSize: filterValuesDefault.minLotSize,
  maxLotSize: filterValuesDefault.maxLotSize,
  relationLotSize: 'between',
  minYearBuilt: filterValuesDefault.minYearBuilt,
  maxYearBuilt: filterValuesDefault.maxYearBuilt,
  relationYearBuilt: 'between',
  minCumulativeDaysOnMarket: filterValuesDefault.minCumulativeDaysOnMarket,
  maxCumulativeDaysOnMarket: filterValuesDefault.maxCumulativeDaysOnMarket,
  relationCumulativeDaysOnMarket: 'between',
  minCoveredParking: filterValuesDefault.minCoveredParking,
  maxCoveredParking: filterValuesDefault.maxCoveredParking,
  relationCoveredParking: 'between', // used to be 'greaterThan'
  // checkedPropertySubTypes: filterValuesDefault.checkedPropertySubTypes,
  // selectedPoolAllowed: filterValuesDefault.selectedPoolAllowed,
  selectedPoolAllowed: null,
  minRentPrice: filterValuesDefault.minRentPrice,
  maxRentPrice: filterValuesDefault.maxRentPrice,
  relationRentPrice: 'between',
  minATMToRentPrice: filterValuesDefault.minATMToRentPrice,
  maxATMToRentPrice: filterValuesDefault.maxATMToRentPrice,
  relationATMToRentPrice: 'between',
  minSoldPrice: filterValuesDefault.minSoldPrice,
  maxSoldPrice: filterValuesDefault.maxSoldPrice,
  relationSoldPrice: 'between',
  minAVMToSoldPrice: filterValuesDefault.minAVMToSoldPrice,
  maxAVMToSoldPrice: filterValuesDefault.maxAVMToSoldPrice,
  relationAVMToSoldPrice: 'between',
  closedDateWithinOrBetween: 'within',
  // filter match mode shortcut values
  rangeBeds: filterValuesDefault.rangeBeds,
  rangeBaths: filterValuesDefault.rangeBaths,
  rangeSqft: filterValuesDefault.rangeSqft,
  rangeLotSize: filterValuesDefault.rangeLotSize,
  rangeYearBuilt: filterValuesDefault.rangeYearBuilt,
  rangeAVMToSoldPrice: filterValuesDefault.rangeAVMToSoldPrice,
  rangeClosingDate: 3, // not in default filter values; make client-based customization easier; ILE default is 6 months
  // MLS table row select
  selectedRowKeysMLSLease: [],
  selectedRowKeysMLSSale: [],
  // National SFR table row select
  selectedRowKeysNationalOperators: [],
  selectedRowKeysHotPads: [],
  selectedRowKeysPadSplit: [],
  // Table sorters
  ClientTableSort: {},
  NSFRTableSort: {},
  HotPadTableSort: {},
  MLSTableSortLease: {},
  MLSTableSortSale: {},
  MultiFamilyTableSort: {},

  showImportKMLModal: false,
  GeoJSONFromKML: geojsonTemplate,
  showSentinelLayer: false,
  sentinelLayerObjectURL: null,
  showSentinelControl: false,
  daysWithinSentinel: 30,

  tempMLSPropertiesFilteredLease: [],
  tempMLSPropertiesFilteredSales: [],

  // Multi-Family map display
  MultiFamilyDisplayedOnMap: true,
  currentSchoolDistrictProperties: [],
  scorecardModalOpen: false,
  getScorecardLoading: false,
  scorecardDownloadLink: null,
  scorecardRecordList: [],

  currentCountyData: [],
  isCountyFilterOn: false,
  currentZipCodeData: [],
  isZipCodeFilterOn: false,
  showPriceMarkers: false,
  circleBbox: [],
  geocodingData: {},
  subjectPropertyValid: false,

  shouldPanToKML: false, // for returning to location of KML after user moved away on the map

  drawingMode: false,
  drawnCustomPolygons: [],
  validDrawnCustomPolygons: [],

  mobileDataType: null,
  currentMobileGeoJSON: geojsonTemplate,

  measureMode: false,

  currentAOIMarketCondition: {},
  currentAOILoading: false,
  compAOIMarketCondition: {},
  compAOILoading: false,
  currentZIPCodeMarketCondition: {},
  currentZIPCodeLoading: false,
  compZIPCodeMarketCondition: {},
  compZIPCodeLoading: false,
  currentDistrictMarketCondition: {},
  currentDistringLoading: false,
  compDistrictMarketCondition: {},
  compDistrictLoading: false,
  currentCountyMarketCondition: {},
  currentCountyLoading: false,
  compCountyMarketCondition: {},
  compCountyLoading: false,
  currentMetroMarketCondition: {},
  currentMetroLoading: false,
  compMetroMarketCondition: {},
  compMetroLoading: false,
  lastMarketConditionPoint: [],

  fetchAllPropertyDataDone: true,
  fetchCompMLSDone: true,
  fetchCompSFRDone: true,
  fetchCompHotPadsDone: true,
  fetchCompPadSplitDone: true,
  scorecardAnalytics: {},
  zipCodeSearchGeoJSON: geojsonTemplate,

  marketRentCompare: [],

  adjustedRentCompParams: {}, // params to get the same comps that were used to generate the adjusted rent
  adjustedSalesCompParams: {},
  adjustedRentFormula: '',
  adjustedSalesFormula: '',
  marketRentPreference: 'mls', // for adjusted rent preference
  subjectPropertyPlacekey: '', // placekey of current subject property
  adjustedPlacekey: '', // placekey from adjusted response, for checking if we need to re-fetch adjusted rent/sales

  compingMode: 'noFilter',
  showFilterModal: false,
  showFilterSideBar: false,

  selectedRowKeysNewBuilds: [],
  currentNewBuildsProperties: [],
  locateNewBuildId: null,

  firstTimeFetchSFR: false,
  firstTimeFetchHotPads: false,
  cmaTabKey: '1',
  // cmaTabKey: 'Comp Insights',

  currentHouseDetails: {},
  currentAPNOwner: {},

  mapLocateProperty: {},
  currentMLSListingChartData: [],
  // currentMLSListingChartZIPCode: {},
  // currentMLSListingChartCBSA: {},
  // currentMLSListingChartDistrict: {},
  newHomeTabChecked: false,

  currentSingleListingBrokerOfficeInfo: {},

  // for map layer controls
  currentMapLayerOptions: [],

  marketConditionStartDate: moment().subtract(90, 'days').format(dateFormat),
  marketConditionEndDate: moment().format(dateFormat),
  chartArea: ['zipcode', 'metro'],

  // for metro restrictions in trial accounts
  metrosAllowedOnIndividualAccountLevel: [],

  marketRentValue: null,

  doneSendingPropertyToOffMarket: true,
  parcelMode: false,

  savedMLSComps: [],
  savedSFRComps: [],

  insightsLeaseExpiry: {},

  showLandParcelFeature: false,
  landDevelopmentFilteredParcelWithinMetro: [],
  landTypeOptionsWithinMetro: [],
  isLoadingLandDevelopmentParcelFilterResponse: false,
  currentLandDevelopmentParcelGeoJSON: geojsonTemplate,
  // currentLandDevelopmentParcelData: {},
  landFloodCoverage: null,
  trialAccountHasLandParcelSearch: true, // for controlling trial users' access to land parcel search
  trialAccountHasLandDevelopmentTools: true, // for controlling trial users' access to land development tools
  heatmapType: null,
  showLandDevelopmentChains: false,
  currentLandDevelopmentCBSA: [],

  chainLandFilterFn: null,
  landDevChainGeoJSON: geojsonTemplate,
  builtForRentData: [],
  builtForRentGeoJSON: geojsonTemplate,
  builtForRentSelectedRowKeys: [],
  builtForRentMedianPrice: null,
  builtForRentTableRowHover: null,

  landDevelopmentMethod: 'metro',
  landDevelopmentPointSelectMode: false,
  landDevelopmentPointSelectCoordinates: [],

  selectedCompTables: [
    'MLS',
    'National SFR Operators Listings',
    'Built For Rent',
    'Portal Listings',
    'Single-Family',
  ],

  //MK = Market Condition
  MKnumberOfMonths: 3,
  MKendYear: moment().subtract(1, 'month').year(),
  MKendMonth: moment().subtract(1, 'month').month(),

  accessToken: '', // pass to map module

  appViewMode: {
    mode: 'CMA', // CMA | LandDevelopment
    subMode: [], // LandParcelSearch and/or LandMonthlyImages if mode is Land Development
  },

  showAVM: true, // whether completely hide AVM in result row

  // Targets upload
  showTargetsUploadModal: false,
  targetsParsedData: {},
  uploadSuccess: false,
  targetsCsvFile: File,

  newConstructionFilters: {},

  //Last sale from public records
  lastSalePublicRecordData: [],
  lastSalePublicRecordLoading: false,
  lastSalePublicRecordDataForRender: [],
  lastSalePublicRecordSelectedRowKey: [],
  currentPRGeoJSON: geojsonTemplate,
  lastSalePublicRecordMedian: 0,
  lastSalePublicRecordMedianPricePerSqft: 0,
  lastSalePublicRecordHover: null,
  currentCompLastSaleData: null,
  lastSalePublicRecordShowTable: false,
  lastSalePublicRecordShowLayer: false,
  openDemographicsHeatmap: false,

  demopraphicData: null,
  nearbyChainStoreData: [],
  psychographicData: null,

  scorecardAreaType: 'drive_time',
  scorecardImageData: null,

  //Built For Rent
  builtForRentHover: null,
  builtForRentSelectedRowKey: [],

  showAppShortcuts: false,

  // Parcel Owner Summary Other Funds Details
  otherFundsDetails: [],

  // major employers
  majorEmployersData: [],

  // Affordable Housing
  affordableHousingData: [],
  affordableHousingDataForRender: [],
  affordableHousingRowHover: null,
  affordableHousingSelectedRowKey: [],
  affordableHousingMedian: null,
  affordableHousingMedianPricePerSqft: null,
  affordableHousingBedroomsRent: {},

  // Building Permit
  buildingPermitData: [],
  filteredBuildingPermitData: [],
  buildingPermitHover: null,
  buildingPermitSearchParams: { lat: '', lng: '', distance: '', days: 180 },
  buildingPermitLoading: false,
  //Land comps
  isLandMode: false,
  landCompsData: [],
  landCompsDataForRender: [],
  landCompsHover: null,
  landCompsSelectedRowKey: [],
  landCompsMedian: null,
  landCompMedianPricePerSqft: null,
  landCompMedianPricePerAcre: null,

  // Realtor MultiFamily
  realtorMultiFamilyData: [],
  realtorMultiFamilyDataForRender: [],
  realtorMultiFamilyHover: null,
  realtorMultiFamilySelectedRowKey: [],
  realtorMultiFamilyMedian: null,
  realtorMultiFamilyMedianPricePerSqft: null,
  // Realtor Single Family
  realtorSingleFamilyDataForRender: [],
  selectedRowKeysRealtorDotCom: [],
  realtorSingleFamilyHover: null,
  realtorSingleFamilyMedian: null,
  realtorSingleFamilyMedianPricePerSqft: null,
  realtorSingleFamilyTableSorter: {},

  // New Lease / Sale / Land Mode
  searchingMode: 'Lease',

  //Land comps
  landShowcaseData: [],
  landShowcaseDataForRender: [],
  landShowcaseHover: null,
  landShowcaseSelectedRowKey: [],
  landShowcaseMedian: null,
  landShowcaseMedianPricePerSqft: null,
  landShowcaseMedianPricePerAcre: null,

  landCrexiData: [],
  landCrexiDataForRender: [],
  landCrexiHover: null,
  landCrexiSelectedRowKey: [],
  landCrexiMedian: null,
  landCrexiMedianPricePerSqft: null,
  landCrexiMedianPricePerAcre: null,

  //Psychographic Data in Details

  schoolAndDemographicTab: 'Area Based',
  schoolAndDemographicTabLoading: false,
  psychographicDataInDetails: [],
  populationDensityData: null,
  majorEmployersData: null,
  currentHoverSection: null,
  sadChainStoreDistance: 1,
  areaDemographicData: null,

  mlsTableFilters: {},
  sfrTableFilters: {},
  hotpadsTableFilters: {},

  bookmarkIsSaved: null,
  userBookmarks: [],
  bookmarkInputModalOpened: false,
  bookmarkUpdateModalOpened: false,
  bookmarkInputNameValue: '',

  ownersOtherParcelsTrigger: null,
  currentParcelDetails: null,
  ownersOtherParcelsData: null,

  //BTR Pipeline
  BTRPipeLineData: [],
  BTRPipeLineHover: null,
  BTRPipeLineModalOpen: false,
  BTRPipeLineModalDetails: null,
  BTRPipeLineSelectedRowKey: [],

  showBTRSubmissionPanel: false,

  //Newly Subdivided Pipeline
  NewlySubdividedPipeLineData: [],
  NewlySubdividedPipeLineHover: null,
  NewlySubdividedPipeLineSelectedRowKey: [],

  //BTR Community Info
  btrCommunityInfoData: [],
  btrCommunityInfoDataForRender: [],
  btrCommunityInfoHover: null,
  btrCommunityInfoSelectedRowKey: [],
  btrCommunityInfoMedian: null,
  btrCommunityInfoMedianPricePerSqft: null,
  btrCommunityInfoMedianPricePerAcre: null,

  //MLS Sale
  mlsSaleNewConstructionOnly: false,

  //chat
  chatOpened: false,

  //user table columns settings
  mlsLeaseColumnSettings: null,
  mlsSaleColumnSettings: null,
  sftColumnSettings: null,
  portalColumnSettings: null,
  secondaryPortalListingsColumnSettings: null,
  lastSalfePrColumnSettings: null,
  landMlsColumnSettings: null,
  landShowcaseColumnSettings: null,
  landCrexiColumnSettings: null,
  affordableHousingColumnSettings: null,

  //Avenue One - House Detail Skip Trace
  persons: null,

  //Tax Details Modal
  showTaxDetailsModal: false,
  taxDetails: null,

  selectedSgement: null,
  displaySelectedSegmentHeatmap: false,
  selectedSegmentHeatmapMinValue: 1,
  selectedSegmentHeatmapMaxValue: null,
  displaySegmentHeatmapPopup: false,

  //Building Permit Activity
  displayBuildingPermitActivityLayer: false,

  //Buildign Permit Jacksonville experiment
  buildingPermitCurrentTab: 'Building Permits',
  displayBpJacksonvilleLayer: false,
  displayBpJacksonvilleBuilderPermitLayer: false,
  bpJacksonvilleData: null,
  bpJacksonvilleDataGeojson: null,

  bpJacksonvilleSelectedBuilder: null,
  bpJacksonvilleSelctedBuildersPermit: null,
  bpJacksonvilleSelctedBuildersPermitGeoJson: null,
  bpJacksonvilleSelctedBuildersPermitPeriodGeoJson: null,
  selectedPeriodPermitIds: null,

  shareModalOpened: false,
  shareModalLoading: false,

  //Scorecard History
  displayScorecardHistory: false,
  scorecardHistoryLoading: false,
};

export default {
  namespace: 'CMA',
  state: DEFAULT_STATE,
  effects: {
    getMLSPropertiesWithinRadius: [
      function* ({ payload }, { call, put, select }) {
        console.log('getMLSPropertiesWithinRadius', payload);
        // yield put({
        //   type: 'saveCMAStates',
        //   payload: {
        //     fetchCompMLSDone: false,
        //   },
        // });
        let response;
        if (Object.hasOwn(payload, 'body')) {
          response = yield call(getMLSPropertiesWithinPolygonsData, payload);
        } else {
          response = yield call(getMLSPropertiesWithinRadiusData, payload);
        }
        if (response && !response.error) {
          const selectedUserGroup = yield select(
            (state) => state.CMA.selectedUserGroup,
          );
          const currentMLSProperties = processMLSProperties({
            response,
            selectedUserGroup,
          });
          // filter MLS data
          // using smart filter or manual filter
          const compingMode = yield select((state) => state.CMA.compingMode);
          const userGroup = yield select((state) => state.CMA.userGroup);
          if (compingMode === 'smartFilter') {
            yield put({
              type: 'filterMLSAndSFRData',
              payload: {
                dataSourceType: 'MLS',
                mode: payload.mode,
                dataSource: currentMLSProperties,
                selectedUserGroup: selectedUserGroup,
              },
            });
          } else if (
            compingMode === 'noFilter' ||
            [
              'preset',
              'preset-venture',
              'preset-svn',
              'preset-sunroom',
              'BTRFilter',
            ].includes(compingMode)
          ) {
            yield put({
              type: 'filterMLSAndSFRDataManual',
              payload: {
                dataSourceType: 'MLS',
                mode: payload.mode,
                dataSource: currentMLSProperties,
              },
            });
          }
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentMLSProperties: currentMLSProperties,
            },
          });
        } else {
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentMLSProperties: [],
              currentMLSPropertiesFiltered: [],
              currentMLSGeoJSON: geojsonTemplate,
            },
          });
        }
        yield put({
          type: 'saveCMAStates',
          payload: {
            fetchCompMLSDone: true,
          },
        });
      },
      { type: 'takeLatest' },
    ],

    getNationalOperatorsPropertiesWithinRadius: [
      function* ({ payload }, { call, put, select }) {
        // yield put({
        //   type: 'saveCMAStates',
        //   payload: {
        //     fetchCompSFRDone: false,
        //   },
        // });
        let response;
        if (Object.hasOwn(payload, 'body')) {
          response = yield call(
            getNationalOperatorsPropertiesWithinPolygonsData,
            payload,
          );
        } else {
          response = yield call(
            getNationalOperatorsPropertiesWithinRadiusData,
            payload,
          );
        }
        if (response && !response.error) {
          const selectedUserGroup = yield select(
            (state) => state.CMA.selectedUserGroup,
          );
          const currentNationalOperatorsProperties = processSFRProperties({
            response,
            selectedUserGroup,
          });
          const compingMode = yield select((state) => state.CMA.compingMode);
          const userGroup = yield select((state) => state.CMA.userGroup);

          if (compingMode === 'smartFilter') {
            yield put({
              type: 'filterMLSAndSFRData',
              payload: {
                dataSourceType: 'SFR',
                mode: payload.mode,
                dataSource: currentNationalOperatorsProperties,
                selectedUserGroup: selectedUserGroup,
              },
            });
          } else if (
            compingMode === 'noFilter' ||
            [
              'preset',
              'preset-venture',
              'preset-svn',
              'preset-sunroom',
              'BTRFilter',
            ].includes(compingMode)
          ) {
            yield put({
              type: 'filterMLSAndSFRDataManual',
              payload: {
                dataSourceType: 'SFR',
                mode: payload.mode,
                dataSource: currentNationalOperatorsProperties,
              },
            });
          }
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentNationalOperatorsProperties:
                currentNationalOperatorsProperties,
            },
          });
        } else {
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentNationalOperatorsProperties: [],
              currentNationalOperatorsPropertiesFiltered: [],
              currentNationalOperatorsGeoJSON: geojsonTemplate,
            },
          });
        }
        yield put({
          type: 'saveCMAStates',
          payload: {
            fetchCompSFRDone: true,
          },
        });
      },
      { type: 'takeLatest' },
    ],

    getHotPadsPropertiesWithinRadius: [
      function* ({ payload }, { call, put, select }) {
        // yield put({
        //   type: 'saveCMAStates',
        //   payload: {
        //     fetchCompHotPadsDone: false,
        //   },
        // });
        let response;
        if (Object.hasOwn(payload, 'body')) {
          response = yield call(getHotPadsDataWithinPolygonsData, payload);
        } else {
          response = yield call(getHotPadsData, payload);
        }
        if (response && !response.error) {
          const selectedUserGroup = yield select(
            (state) => state.CMA.selectedUserGroup,
          );
          const currentHotPadsProperties = processSFRProperties({
            response,
            selectedUserGroup,
          });
          const compingMode = yield select((state) => state.CMA.compingMode);
          const userGroup = yield select((state) => state.CMA.userGroup);
          if (compingMode === 'smartFilter') {
            yield put({
              type: 'filterMLSAndSFRData',
              payload: {
                dataSourceType: 'HotPads',
                mode: payload.mode,
                dataSource: currentHotPadsProperties,
                selectedUserGroup: selectedUserGroup,
              },
            });
          } else if (
            compingMode === 'noFilter' ||
            [
              'preset',
              'preset-venture',
              'preset-svn',
              'preset-sunroom',
              'BTRFilter',
            ].includes(compingMode)
          ) {
            yield put({
              type: 'filterMLSAndSFRDataManual',
              payload: {
                dataSourceType: 'HotPads',
                mode: payload.mode,
                dataSource: currentHotPadsProperties,
              },
            });
          }
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentHotPadsProperties: currentHotPadsProperties,
            },
          });
        } else {
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentHotPadsProperties: [],
              currentHotPadsPropertiesFiltered: [],
              currentHotPadsGeoJSON: geojsonTemplate,
            },
          });
        }
        yield put({
          type: 'saveCMAStates',
          payload: {
            fetchCompHotPadsDone: true,
          },
        });
      },
      { type: 'takeLatest' },
    ],

    getPadSplitPropertiesWithinRadius: [
      function* ({ payload }, { call, put, select }) {
        let response;
        response = yield call(getPadSplitData, payload);
        if (response && !response.error) {
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentPadSplitProperties: response,
              currentPadSplitPropertiesFiltered: response,
              selectedRowKeysPadSplit: response.map(
                (property) => property.property_id,
              ),
              currentPadSplitGeoJSON: generateGeoJSON(response, 'PadSplit'),
            },
          });
        } else {
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentPadSplitProperties: [],
              currentPadSplitPropertiesFiltered: [],
              selectedRowKeysPadSplit: [],
              currentPadSplitGeoJSON: geojsonTemplate,
            },
          });
        }
        yield put({
          type: 'saveCMAStates',
          payload: {
            fetchCompPadSplitDone: true,
          },
        });
      },
      { type: 'takeLatest' },
    ],

    getMultiFamilyPropertiesWithinRadius: [
      function* ({ payload }, { call, put, select }) {
        const eventCoordinates = yield select(
          (state) => state.CMA.eventCoordinates,
        );
        const searchingMode = yield select((state) => state.CMA.searchingMode);
        const userGroup = yield select((state) => state.CMA.userGroup);
        const userEmail = yield select((state) => state.CMA.userEmail);
        if (
          searchingMode === 'Lease' &&
          (userGroup.includes('BridgeTower') ||
            userGroup.includes('demo-users') ||
            userGroup.includes('dev') ||
            userGroup.includes('Avanta') ||
            (userGroup.includes('demo-CMA-DFW-only') &&
              (userEmail.includes('marchcapitalfund.com') ||
                userEmail.includes('allcommonsenses'))))
        ) {
          const response = yield call(
            getMultiFamilyPropertiesWithinRadiusData,
            payload,
          );
          if (response) {
            let allRowKeyMultiFamily = [],
              geojsonFeaturesMultiFamily = [];
            for (const property of response) {
              // allRowKeyMultiFamily.push(property.uid);
              // generate geojson features
              // generate properties for each feature; copy object except geometry
              // re: https://stackoverflow.com/a/34710102/10039571
              let { geom, ...geojsonProperties } = property;
              geojsonFeaturesMultiFamily.push({
                type: 'Feature',
                geometry: property.geom,
                properties: geojsonProperties,
              });
            }
            if (eventCoordinates.length > 0) {
              yield put({
                type: 'saveCMAStates',
                payload: {
                  currentMultiFamilyProperties: response,
                  currentMultiFamilyGeoJSON: {
                    type: 'FeatureCollection',
                    features: geojsonFeaturesMultiFamily,
                  },
                },
              });
            }
          } else {
            yield put({
              type: 'saveCMAStates',
              payload: {
                currentMultiFamilyProperties: [],
                currentMultiFamilyGeoJSON: geojsonTemplate,
              },
            });
          }
        }
      },
      { type: 'takeLatest' },
    ],

    getBTOwnedPropertiesWithinRadius: [
      function* ({ payload }, { call, put, select }) {
        const eventCoordinates = yield select(
          (state) => state.CMA.eventCoordinates,
        );
        const searchingMode = yield select((state) => state.CMA.searchingMode);
        const userGroup = yield select((state) => state.CMA.userGroup);
        if (searchingMode === 'Lease' && userGroup.includes('BridgeTower')) {
          const response = yield call(
            getBTOwnedPropertiesWithinRadiusData,
            payload,
          );
          if (response) {
            let geojsonFeaturesBTOwned = [];
            for (const property of response) {
              // generate geojson features
              // generate properties for each feature; copy object except geometry
              // re: https://stackoverflow.com/a/34710102/10039571
              let { geography, ...geojsonProperties } = property;
              geojsonFeaturesBTOwned.push({
                type: 'Feature',
                geometry: property.geography,
                properties: geojsonProperties,
              });
            }
            if (eventCoordinates.length > 0) {
              yield put({
                type: 'saveCMAStates',
                payload: {
                  currentBTOwnedProperties: response,
                  currentBTOwnedGeoJSON: {
                    type: 'FeatureCollection',
                    features: geojsonFeaturesBTOwned,
                  },
                },
              });
            }
          } else {
            yield put({
              type: 'saveCMAStates',
              payload: {
                currentBTOwnedProperties: [],
                currentBTOwnedGeoJSON: geojsonTemplate,
              },
            });
          }
        }
      },
      { type: 'takeLatest' },
    ],

    *getParcelMapScope({ payload }, { call, put }) {
      const response = yield call(getParcelMapScopeData, payload);
      if (response) {
        let geojsonFeaturesParcelMapScope = [];
        for (let property of response) {
          // add a value with phase/ph/addition/addn/sec removed from subdivision names
          // there are subdivision names with more than one keywords, i.e. CHASE OAKS ADDITION PHASE TWO
          // get the smallest indexOfPhase
          if (property.subdivision) {
            let indexOfPhase = -1;
            for (const keyword of [
              ' phase ',
              ' ph ',
              ' addition',
              ' addn',
              ' add ',
              ' est ',
              ' estates ',
              ' estate ',
              ' sec ',
              ' section ',
            ]) {
              const currentIndex = property.subdivision
                .toLowerCase()
                .indexOf(keyword);
              if (
                currentIndex > -1 &&
                (indexOfPhase === -1 ||
                  (indexOfPhase > -1 && currentIndex < indexOfPhase))
              ) {
                indexOfPhase = currentIndex;
              }
            }
            if (indexOfPhase > -1) {
              property.subdivisionWithoutPhase = property.subdivision.slice(
                0,
                indexOfPhase,
              );
              // console.log(property.subdivision, property.subdivisionWithoutPhase);
            } else {
              property.subdivisionWithoutPhase = property.subdivision;
            }
          } else {
            property.subdivisionWithoutPhase = '';
            // console.log(property.subdivision, property.subdivisionWithoutPhase);
          }

          property = setPropertyOwnerColor(property);
          property = setHOAFeeColor(property);

          // generate geojson features
          // generate properties for each feature; copy object except geometry
          // re: https://stackoverflow.com/a/34710102/10039571
          let { geog, ...geojsonProperties } = property;
          geojsonFeaturesParcelMapScope.push({
            type: 'Feature',
            geometry: property.geog,
            properties: geojsonProperties,
          });
        }
        yield put({
          type: 'saveCMAStates',
          payload: {
            currentParcelMapScopeGeoJSON: {
              type: 'FeatureCollection',
              features: geojsonFeaturesParcelMapScope,
            },
          },
        });
      }
    },

    *getParcelBoundaryMapScope({ payload }, { call, put }) {
      const response = yield call(getParcelBoundaryMapScopeData, payload);
      if (response) {
        let geojsonFeaturesParcelBoundaryMapScope = [];
        for (const property of response) {
          // generate geojson features
          const { geom, ...properties } = property;
          geojsonFeaturesParcelBoundaryMapScope.push({
            type: 'Feature',
            geometry: geom,
            properties: properties,
          });
        }
        yield put({
          type: 'saveCMAStates',
          payload: {
            currentParcelBoundaryMapScopeGeoJSON: {
              type: 'FeatureCollection',
              features: geojsonFeaturesParcelBoundaryMapScope,
            },
          },
        });
      }
    },

    *getDistrictForWithinFilter({ payload }, { call, put, select }) {
      const eventCoordinates = yield select(
        (state) => state.CMA.eventCoordinates,
      );
      if (eventCoordinates.length != 0) {
        const response = yield call(getSchoolDistrictOfPointData, {
          lat: eventCoordinates[1],
          lng: eventCoordinates[0],
        });

        if (response) {
          yield put({
            type: 'saveCMAStates',
            payload: { currentSchoolDistrictProperties: response },
          });
        }
      }
    },

    *getCountyForWithinFilter({ payload }, { call, put, select }) {
      const eventCoordinates = yield select(
        (state) => state.CMA.eventCoordinates,
      );
      if (eventCoordinates.length != 0) {
        const response = yield call(getCountyOfPointData, {
          lat: eventCoordinates[1],
          lng: eventCoordinates[0],
        });

        if (response) {
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentCountyData: response,
            },
          });
        }
      }
    },

    *getZipCodeForWithinFilter({ payload }, { call, put, select }) {
      const eventCoordinates = yield select(
        (state) => state.CMA.eventCoordinates,
      );
      if (eventCoordinates.length != 0) {
        const response = yield call(getZipCodeOfPointData, {
          lat: eventCoordinates[1],
          lng: eventCoordinates[0],
        });

        if (response) {
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentZipCodeData: response,
            },
          });
        }
      }
    },

    // when clicking on the map
    // use reverse geocoding to get the address
    // then use the address to get Placekey
    // use the Placekey to query for parcel data
    // not in use
    *getMapboxReverseGeocoding({ payload }, { call, put, select }) {
      const response = yield call(getMapboxReverseGeocodingData, payload);
      // console.log('reverse geocoding response', response);
      if (response && response.features.length > 0) {
        const item = response.features[0];
        // get ZIP Code from geocoding response
        const postalCodeContext = item.context.find(
          (subItem) => subItem.id.indexOf('postcode') > -1,
        );
        let postalCode;
        if (postalCodeContext) {
          postalCode = postalCodeContext.text;
        }
        // get city from geocoding response
        const cityContext = item.context.find(
          (subItem) => subItem.id.indexOf('place') > -1,
        );
        let city;
        if (cityContext) {
          city = cityContext.text;
        }
        // get state from geocoding response
        const regionContext = item.context.find(
          (subItem) => subItem.id.indexOf('region') > -1,
        );
        let region;
        if (regionContext) {
          region = regionContext.text;
        }
        const subjectPropertyPlacekeyAPIParams = {
          fullAddress: item.place_name,
          streetAddress: item.address
            ? item.address + ' ' + item.text
            : item.text, // street number + street name; if no street number, use street name only
          postalCode,
          city,
          region,
          latitude: item.center[1],
          longitude: item.center[0],
        };
        // get placekey
        yield put({
          type: 'getPlacekey',
          payload: subjectPropertyPlacekeyAPIParams,
        });
        yield put({
          type: 'saveCMAStates',
          payload: {
            currentPropertyAddress: subjectPropertyPlacekeyAPIParams,
          },
        });
        // so that when sharing this subject property, we'll use lngLat as params
        // yield put({
        //   type: 'saveCMAStates',
        //   payload: {
        //     modeToFindSubjectProperty: 'Click on Map',
        //   },
        // });
      }
    },

    *getAddressAutoComplete({ payload }, { call, put, select }) {
      try {
        const response = yield call(getAddressAutoCompleteData, payload);

        if (response && response.length > 0) {
          yield put({
            type: 'saveCMAStates',
            payload: {
              chooseList: response,
            },
          });
        } else {
          yield put({
            type: 'saveCMAStates',
            payload: {
              chooseList: [],
            },
          });
        }
      } catch (error) {
        yield put({
          type: 'saveCMAStates',
          payload: {
            chooseList: [],
          },
        });
      }
    },

    *getMapboxForwardGeocoding({ payload }, { call, put, select }) {
      try {
        const response = yield call(getMapboxForwardGeocodingData, payload);
        // console.log('forward geocoding response', response);
        if (response && response.features.length > 0) {
          yield put({
            type: 'saveCMAStates',
            payload: {
              geocodingData: response,
            },
          });

          const newlist = [];

          let mapboxRooftop = false;

          for (let i = 0; i < response.features.length; i++) {
            const item = response.features[i];
            let fullAddress;
            let streetAddress;
            let postalCode;
            let city;
            let region;
            let searchType;

            if (
              item.properties &&
              item.properties.accuracy &&
              (item.properties.accuracy === 'rooftop' ||
                item.properties.accuracy === 'parcel')
            ) {
              mapboxRooftop = true;
            }

            if (item.id.includes('address')) {
              // Address Type
              const postalCodeContext = item.context.find(
                (ctx) => ctx.id.indexOf('postcode') > -1,
              );
              postalCode = postalCodeContext ? postalCodeContext.text : '';

              const cityContext = item.context.find(
                (subItem) => subItem.id.indexOf('place') > -1,
              );
              city = cityContext ? cityContext.text : '';

              const regionContext = item.context.find(
                (subItem) => subItem.id.indexOf('region') > -1,
              );
              region = regionContext ? regionContext.text : '';

              fullAddress = item.place_name;
              streetAddress = item.address + ' ' + item.text;
              searchType = 'address';
            } else if (item.id.includes('place')) {
              // City Type
              city = item.text;

              const postalCodeContext = item.context.find(
                (ctx) => ctx.id.indexOf('postcode') > -1,
              );
              postalCode = postalCodeContext ? postalCodeContext.text : '';

              const regionContext = item.context.find(
                (subItem) => subItem.id.indexOf('region') > -1,
              );
              region = regionContext ? regionContext.text : '';

              fullAddress = item.place_name;
              streetAddress = '';
              searchType = 'city';
            } else if (item.id.includes('postcode')) {
              // ZIPcode Type
              postalCode = item.text;

              const cityContext = item.context.find(
                (ctx) => ctx.id.indexOf('place') > -1,
              );
              city = cityContext ? cityContext.text : '';

              const regionContext = item.context.find(
                (ctx) => ctx.id.indexOf('region') > -1,
              );
              region = regionContext ? regionContext.text : '';

              fullAddress = item.place_name;
              streetAddress = '';
              searchType = 'postcode';
            }

            newlist.push({
              fullAddress: fullAddress,
              streetAddress: streetAddress,
              postalCode,
              city,
              region,
              latitude: item.center[1],
              longitude: item.center[0],
              searchType,
              placeType: item.place_type,
              source: 'mapbox',
            });
          }

          if (!mapboxRooftop) {
            const googleRes = yield call(
              getGoogleForwardGeocodingData,
              payload,
            );
            if (googleRes && googleRes.results.length > 0) {
              for (let i = 0; i < 1; i++) {
                const item = googleRes.results[i];
                let fullAddress;
                let streetAddress;
                let postalCode;
                let city;
                let region;
                let searchType;
                let lat, lng;

                if (
                  item.types.includes('street_address') ||
                  item.types.includes('premise') ||
                  item.geometry.location_type === 'ROOFTOP'
                ) {
                  // Address Type
                  const postalCodeContext = item.address_components.find(
                    (ctx) => ctx.types.includes('postal_code'),
                  );
                  postalCode = postalCodeContext
                    ? postalCodeContext.long_name
                    : '';

                  const cityContext = item.address_components.find((subItem) =>
                    subItem.types.includes('locality'),
                  );
                  city = cityContext ? cityContext.long_name : '';

                  const regionContext = item.address_components.find(
                    (subItem) =>
                      subItem.types.includes('administrative_area_level_1'),
                  );
                  region = regionContext ? regionContext.long_name : '';

                  fullAddress = item.formatted_address;
                  streetAddress = item.formatted_address.split(',')[0];
                  searchType = 'address';
                  lat = item.geometry.location.lat;
                  lng = item.geometry.location.lng;

                  if (
                    newlist[0].source === 'mapbox' &&
                    newlist[0].searchType === 'address'
                  ) {
                    newlist.shift();
                  }

                  newlist.unshift({
                    fullAddress: fullAddress,
                    streetAddress: streetAddress,
                    postalCode,
                    city,
                    region,
                    latitude: lat,
                    longitude: lng,
                    searchType,
                    placeType: ['address'],
                    source: 'google',
                  });
                }
              }
            }
          }

          console.log('newlist', newlist);
          yield put({
            type: 'saveCMAStates',
            payload: {
              chooseList: newlist,
            },
          });
          // console.log('newlist', newlist);
        } else {
          yield put({
            type: 'saveCMAStates',
            payload: {
              chooseList: [],
            },
          });
        }
      } catch (err) {
        console.log(err);
      }
    },

    // for subject property address from acquisition portal via URL query
    *getMapboxForwardGeocodingForURLQueryAddress(
      { payload },
      { call, put, select },
    ) {
      const response = yield call(getMapboxForwardGeocodingData, payload);
      // console.log('url query forward geocoding response ', response);
      if (response && response.features.length > 0) {
        yield put({
          type: 'saveCMAStates',
          payload: {
            geocodingData: response,
          },
        });
        const item = response.features[0];
        const postalCodeContext = item.context.find(
          (subItem) => subItem.id.indexOf('postcode') > -1,
        );
        let postalCode;
        if (postalCodeContext) {
          postalCode = postalCodeContext.text;
        }
        // get city from geocoding response
        const cityContext = item.context.find(
          (subItem) => subItem.id.indexOf('place') > -1,
        );
        let city;
        if (cityContext) {
          city = cityContext.text;
        }
        // get state from geocoding response
        const regionContext = item.context.find(
          (subItem) => subItem.id.indexOf('region') > -1,
        );
        let region;
        if (regionContext) {
          region = regionContext.text;
        }
        const currentPropertyAddress = {
          fullAddress: item.place_name,
          streetAddress: item.address + ' ' + item.text, // street number + street name
          postalCode,
          city,
          region,
          latitude: item.center[1],
          longitude: item.center[0],
        };

        yield put({
          type: 'saveCMAStates',
          payload: {
            eventCoordinates: item.center,
            currentPropertyAddress: currentPropertyAddress,
          },
        });
        // so that when sharing this subject property, we'll use address as params
        yield put({
          type: 'saveCMAStates',
          payload: {
            modeToFindSubjectProperty: 'Search an Address',
          },
        });
      }
    },

    *getPlacekey({ payload }, { call, put, select }) {
      const response = yield call(getPlacekeyData, payload);
      console.log('response placekey', response);
      const eventCoordinates = yield select(
        (state) => state.CMA.eventCoordinates,
      );
      if (response && response.placekey) {
        yield put({
          type: 'getSingleParcelWithPlacekey',
          payload: {
            placekey: response.placekey,
            lng: eventCoordinates[0] || undefined,
            lat: eventCoordinates[1] || undefined,
          },
        });
      } else {
        // if no valid placekey was returned, use lngLat

        yield put({
          type: 'getSingleParcelWithPlacekey',
          payload: {
            placekey: 'undefined',
            lng: eventCoordinates[0],
            lat: eventCoordinates[1],
          },
        });
      }
    },

    *getSingleParcelWithPlacekey({ payload }, { call, put, select }) {
      const response = yield call(getSingleParcelWithPlacekeyData, payload);
      if (response && response.length > 0) {
        console.log('single parcel response', response);
        yield put({
          type: 'saveCMAStates',
          payload: {
            subjectPropertyParcelData: response[0],
          },
        });
        // yield put({
        //   type: 'getRentAverageAll',
        // });
      } else {
        yield put({
          type: 'saveCMAStates',
          payload: {
            subjectPropertyParcelData: {},
          },
        });
      }
    },

    *getUserBatchProcessingRecord({ payload }, { call, put, select }) {
      const response = yield call(getUserBatchProcessingRecordData, payload);
      if (response) {
        yield put({
          type: 'saveCMAStates',
          payload: {
            UserBatchProcessingRecord: response,
          },
        });
      }
    },

    *getBatchMLSPropertyImages({ payload }, { call, put, select }) {
      yield put({
        type: 'saveCMAStates',
        payload: {
          currentMLSPropertyImages: [],
          MLSImageModalIsLoading: true,
        },
      });

      const response = yield call(getBatchMLSPropertyImagesData, payload);

      const getURL = (src) => {
        if (
          payload &&
          payload.city &&
          ['bakersfield', 'cincinnati'].includes(payload.city)
        )
          return src;
        return `https://spatiallaserserver-img.s3.amazonaws.com/${src}`;
      };

      if (response) {
        const processedImages = [...response.map((src) => getURL(src))];
        const imageCanBeLoaded = (url) => {
          return new Promise((resolve, reject) => {
            const img = new Image();
            img.src = url;
            img.onload = function () {
              resolve(true);
            };
            img.onerror = function () {
              resolve(false);
            };
          });
        };

        let validImages = [];

        for (let i = 0; i < processedImages.length; i++) {
          const canBeLoaded = yield imageCanBeLoaded(processedImages[i]);

          if (
            !processedImages[i].includes('null.jpeg') &&
            !processedImages[i].includes('null.jpg') &&
            canBeLoaded
          ) {
            validImages.push(processedImages[i]);
          }
        }

        yield put({
          type: 'saveCMAStates',
          payload: {
            currentMLSPropertyImages: validImages,
            MLSImageModalIsLoading: false,
          },
        });
      }
    },

    *getMLSPopupImages({ payload }, { call, put, select }) {
      const response = yield call(getBatchMLSPropertyImagesData, payload);

      const getURL = (src) => {
        // ATTN: batch image api returns full url for Bakersfield and Cincinnati in exp
        // but partial url for s3 the same way as all other metros in prod
        if (
          process.env.UMI_APP_SERVER_TYPE === 'exp' &&
          payload &&
          payload.city &&
          ['bakersfield', 'cincinnati'].includes(payload.city)
        )
          return src;
        return `https://spatiallaserserver-img.s3.amazonaws.com/${src}`;
      };

      if (response) {
        const processedImages = [...response.map((src) => getURL(src))];

        const imageCanBeLoaded = (url) => {
          return new Promise((resolve, reject) => {
            const img = new Image();
            img.src = url;
            img.onload = function () {
              resolve(true);
            };
            img.onerror = function () {
              resolve(false);
            };
          });
        };

        for (let i = 0; i < processedImages.length; i++) {
          const canBeLoaded = yield imageCanBeLoaded(processedImages[i]);

          if (
            !processedImages[i].includes('null.jpeg') &&
            !processedImages[i].includes('null.jpg') &&
            canBeLoaded
          ) {
            return processedImages[i];
          }
        }
      }
      return '';
    },

    *getNearestSubdivisions({ payload }, { call, put, select }) {
      const response = yield call(getNearestSubdivisionsData, payload);
      if (response) {
        yield put({
          type: 'saveCMAStates',
          payload: {
            allSubdivisionsFromSearch: response,
          },
        });
      }
    },

    getParcelOwnerSummary: [
      function* ({ payload }, { call, put, select }) {
        let response = yield call(getParcelOwnerSummaryData, payload);

        if (response) {
          let extraCount = 0;
          if (response.institution_count.length > 0) {
            response.institution_count = response.institution_count.filter(
              function (obj) {
                if (obj.institution === 'Divvy') {
                  extraCount += obj.count;
                }
                return obj.institution !== 'Divvy';
              },
            );
          }

          const objIndex = response.institution_count.findIndex(
            (obj) => obj.institution == 'Other',
          );
          if (objIndex > -1) {
            response.institution_count[objIndex].count += extraCount;
          }

          const otherDetails = yield call(getOtherOwnersDetails, payload);
          console.log('otherFundsDetails', otherDetails, payload);

          yield put({
            type: 'saveCMAStates',
            payload: {
              currentParcelOwnerSummary: response,
              otherFundsDetails: otherDetails,
            },
          });
        }
      },
      { type: 'takeLatest' },
    ],

    *getSubjectPropertyByAddressSearch({ payload }, { call, put, select }) {
      let responseSubjectProperty;
      if (payload.placekey) {
        responseSubjectProperty = yield call(getSingleParcelWithPlacekeyData, {
          placekey: payload.placekey,
          lng: payload.lng,
          lat: payload.lat,
        });
      } else {
        responseSubjectProperty = yield call(getSingleParcelWithPlacekeyData, {
          placekey: 'undefined',
          lng: payload.lng,
          lat: payload.lat,
        });
      }
      if (responseSubjectProperty && responseSubjectProperty.length > 0) {
        yield put({
          type: 'saveCMAStates',
          payload: {
            subjectPropertyParcelData: responseSubjectProperty[0],
          },
        });
      } else {
        yield put({
          type: 'saveCMAStates',
          payload: {
            subjectPropertyParcelData: {},
          },
        });
      }
      return responseSubjectProperty;
    },

    getSubjectProperty: [
      function* ({ payload }, { call, put, select }) {
        // so that when sharing this subject property, we'll use lngLat as params
        // yield put({
        //   type: 'saveCMAStates',
        //   payload: {
        //     modeToFindSubjectProperty: 'Click on Map',
        //   },
        // });

        let responseSubjectProperty;

        if (payload.placekey) {
          try {
            const parcelPayload = {
              lat: payload.lat,
              lng: payload.lng,
              placekey: payload.placekey,
            };

            if (payload.streetAddress) {
              parcelPayload.streetNum = payload.streetAddress.split(' ')[0];
            }

            responseSubjectProperty = yield call(
              getSingleParcelWithPlacekeyData,
              parcelPayload,
            );
          } catch (error) {
            console.log(error);
            responseSubjectProperty = [];
          }
        } else {
          // if no valid placekey was returned, use lngLat
          try {
            responseSubjectProperty = yield call(
              getSingleParcelWithPlacekeyData,
              {
                placekey: 'undefined',
                lng: payload.lng,
                lat: payload.lat,
                ...(payload.streetAddress
                  ? { streetNum: payload.streetAddress.split(' ')[0] }
                  : {}),
              },
            );
          } catch (error) {
            console.log(error);
            responseSubjectProperty = [];
          }
        }

        if (
          isWithinDistance({
            coordinateA: [payload.lng, payload.lat],
            coordinateB: payload.subjectPropertyCoordinates,
            distance: 30, // in meters
          })
        ) {
          // yield put({
          //   type: 'saveCMAStates',
          //   payload: {
          //     currentPropertyAddress: subjectPropertyPlacekeyAPIParams,
          //   },
          // });

          if (responseSubjectProperty && responseSubjectProperty.length > 0) {
            let parcelData = responseSubjectProperty[0];
            // insert user customized property data from url in batch comping results
            if (
              payload.mode &&
              payload.mode === 'from url with lngLat for intelligent comping'
            ) {
              parcelData = {
                ...parcelData,
                beds_count: payload.beds ? payload.beds : parcelData.beds_count,
                baths: payload.baths ? payload.baths : parcelData.baths,
                total_area_sq_ft: payload.sqft
                  ? payload.sqft
                  : parcelData.total_area_sq_ft,
                year_built: payload.yearbuilt
                  ? payload.yearbuilt
                  : parcelData.year_built,
                rentavm: payload.rentavm ? payload.rentavm : parcelData.rent,
                salesavm: payload.salesavm
                  ? payload.salesavm
                  : parcelData.sales,
              };
            }
            yield put({
              type: 'saveCMAStates',
              payload: {
                // subjectPropertyParcelData: responseSubjectProperty[0],
                subjectPropertyParcelData: parcelData,
                subjectPropertyValid: true,
              },
            });
          } else {
            yield put({
              type: 'saveCMAStates',
              payload: {
                subjectPropertyParcelData: {},
                subjectPropertyValid: false,
              },
            });
          }
        } else {
          yield put({
            type: 'saveCMAStates',
            payload: {
              // currentPropertyAddress: {},
              subjectPropertyParcelData: {},
              subjectPropertyValid: false,
            },
          });
        }
        // return responseSubjectProperty; // returned value seems not in use anymore; previously in MapClick.js
      },
      { type: 'takeLatest' },
    ],

    // get MLS and SFR properties data through smart filters or manual filters
    getMLSAndSFRPropertiesFiltered: [
      function* ({ payload }, { all, call, put, select }) {
        // console.log('getMLSAndSFRPropertiesFiltered payload', payload);
        // fetch MLS and SFR properties data within radius
        const searchingMode = yield select((state) => state.CMA.searchingMode);
        const currentStartMLS = yield select(
          (state) => state.CMA.currentStartMLS,
        );
        const currentEndMLS = yield select((state) => state.CMA.currentEndMLS);
        const currentStatusMLS = yield select(
          (state) => state.CMA.currentStatusMLS,
        );
        const currentRadiusMile = yield select(
          (state) => state.CMA.currentRadiusMile,
        );

        const payloadComplete = {
          ...payload,
          startDate: currentStartMLS,
          endDate: currentEndMLS,
          distance: currentRadiusMile * 1609.34,
        };

        // get school disctrict, county, and zip code polygons for filters
        const isDistrictFilterOn = yield select(
          (state) => state.CMA.isDistrictFilterOn,
        );
        const isCountyFilterOn = yield select(
          (state) => state.CMA.isCountyFilterOn,
        );
        const isZipCodeFilterOn = yield select(
          (state) => state.CMA.isZipCodeFilterOn,
        );
        if (isDistrictFilterOn) {
          yield put.resolve({
            type: 'getDistrictForWithinFilter',
          });
        }
        if (isCountyFilterOn) {
          yield put.resolve({
            type: 'getCountyForWithinFilter',
          });
        }
        if (isZipCodeFilterOn) {
          yield put.resolve({
            type: 'getZipCodeForWithinFilter',
          });
        }

        // Metro restriction
        const userGroup = yield select((state) => state.CMA.userGroup);
        const selectedUserGroup = yield select(
          (state) => state.CMA.selectedUserGroup,
        );
        const metrosAllowedOnIndividualAccountLevel = yield select(
          (state) => state.CMA.metrosAllowedOnIndividualAccountLevel,
        );

        let withinClientSubscription;

        if (['demo-CMA-DFW-only'].includes(selectedUserGroup)) {
          // console.log('metrosAllowedOnIndividualAccountLevel', metrosAllowedOnIndividualAccountLevel);
          const { isWithinSubscribedMetro, isWithinAnyMetro } =
            withinSubscribedMetro(
              payload.postalCode,
              selectedUserGroup,
              metrosAllowedOnIndividualAccountLevel,
            );
          withinClientSubscription = isWithinSubscribedMetro;
        } else {
          const responseAllowedCoordinates = yield call(
            getClientAllowedAccessCoordinatesData,
            {
              coordinates: `${
                payload?.lat || payload?.subjectCoordinates?.[1]
              },${payload?.lng || payload?.subjectCoordinates?.[0]}`,
            },
          );
          // console.log('responseAllowedCoordinates', responseAllowedCoordinates);
          withinClientSubscription = responseAllowedCoordinates ? true : false;
        }

        if (!withinClientSubscription) {
          yield put({
            type: 'saveCMAStates',
            payload: {
              fetchCompMLSDone: true,
              currentMLSProperties: [],
              currentMLSPropertiesFiltered: [],
              currentMLSGeoJSON: geojsonTemplate,
            },
          });
        }

        const generateDataFetchArray = (payload) => {
          if (searchingMode === 'Lease') {
            const baseArray = [
              put({
                type: 'getNationalOperatorsPropertiesWithinRadius',
                payload: payload,
              }),
              put({
                type: 'getHotPadsPropertiesWithinRadius',
                payload: payload,
              }),
              put({
                type: 'getPadSplitPropertiesWithinRadius',
                payload: payload,
              }),
            ];
            return withinClientSubscription
              ? [
                  put({
                    type: 'getMLSPropertiesWithinRadius',
                    payload: payload,
                  }),
                  ...baseArray,
                ]
              : baseArray;
          } else {
            return withinClientSubscription
              ? [
                  put({
                    type: 'getMLSPropertiesWithinRadius',
                    payload: payload,
                  }),
                ]
              : [];
          }
        };

        const { drawnCustomPolygons } = yield select(({ CMA }) => {
          return { drawnCustomPolygons: CMA.drawnCustomPolygons };
        });

        const generateDataFromPolgyonFetchArray = (mode) => {
          if (
            [
              'from polygon',
              'change radius',
              'to lease mode',
              'to sales mode',
              'change MLS & SFR status',
              'change MLS & SFR date range',
              'switch to no filter',
              'switch to smart filter',
              'edit property details',
              'switch to preset',
              'switch to BTRFilter',
            ].includes(mode)
          ) {
            const mlsPayload = {
              body: drawnCustomPolygons,
              startDate: moment(currentStartMLS).format(dateFormat),
              endDate: moment(currentEndMLS).format(dateFormat),
              status: currentStatusMLS,
              propertyType:
                searchingMode === 'Lease' ? 'Residential Lease' : 'Residential',
            };

            const nsfr_HotPads_Payload = {
              body: drawnCustomPolygons,
              startDate: moment(currentStartMLS).format(dateFormat),
              endDate: moment(currentEndMLS).format(dateFormat),
              exists: currentStatusMLS,
            };

            if (searchingMode === 'Lease') {
              return [
                put({
                  type: 'getMLSPropertiesWithinRadius',
                  payload: mlsPayload,
                }),
                put({
                  type: 'getNationalOperatorsPropertiesWithinRadius',
                  payload: nsfr_HotPads_Payload,
                }),
                put({
                  type: 'getHotPadsPropertiesWithinRadius',
                  payload: nsfr_HotPads_Payload,
                }),
              ];
            } else {
              // return [call(getMLSPropertiesWithinPolygonsData, mlsPayload)];
              return [
                put({
                  type: 'getMLSPropertiesWithinRadius',
                  payload: mlsPayload,
                }),
              ];
            }
          }
        };

        // let response;
        if (drawnCustomPolygons.length > 0) {
          yield all(generateDataFromPolgyonFetchArray(payload.mode));
          yield put({
            type: 'saveCMAStates',
            payload: {
              firstTimeFetchSFR: true,
              firstTimeFetchHotPads: true,
            },
          });
        } else {
          yield all(generateDataFetchArray(payloadComplete));
        }
      },
      { type: 'takeLatest' },
    ],

    // fetch national SFR operators, BT owned, and multi family data
    getBTAndMultiFamilyProperties: [
      function* ({ payload }, { all, call, put, select }) {
        const userGroup = yield select((state) => state.CMA.userGroup);
        const userEmail = yield select((state) => state.CMA.userEmail);
        const { drawnCustomPolygons } = yield select(({ CMA }) => {
          return { drawnCustomPolygons: CMA.drawnCustomPolygons };
        });

        const searchingMode = yield select((state) => state.CMA.searchingMode);
        const adjustedRentCompParams = yield select(
          (state) => state.CMA.adjustedRentCompParams,
        );
        const adjustedSalesCompParams = yield select(
          (state) => state.CMA.adjustedSalesCompParams,
        );
        const currentRadiusMile = yield select(
          (state) => state.CMA.currentRadiusMile,
        );
        const adjustedCompParams =
          searchingMode === 'Lease'
            ? adjustedRentCompParams
            : adjustedSalesCompParams;

        const compingMode = yield select((state) => state.CMA.compingMode);

        const getDistanceParamByCompingMode = (compingMode) => {
          switch (compingMode) {
            case 'noFilter':
              return currentRadiusMile * 1609.34;
            case 'smartFilter':
              return 0.5 * 1609.34;
            case 'intelligentComping':
              return adjustedCompParams && !isEmpty(adjustedCompParams)
                ? adjustedCompParams.distance * 1609.34
                : currentRadiusMile * 1609.34;
            default:
              return 0.5 * 1609.34;
          }
        };

        const payloadWithDistance = {
          ...payload,
          distance: getDistanceParamByCompingMode(compingMode),
        };

        if (drawnCustomPolygons.length == 0) {
          if (payload.mode === 'change BTOwned expiring only') {
            yield all([
              put({
                type: 'getBTOwnedPropertiesWithinRadius',
                payload: payloadWithDistance,
              }),
            ]);
          } else {
            if (
              userGroup.includes('BridgeTower') ||
              userGroup.includes('demo-users') ||
              userGroup.includes('dev')
            ) {
              yield all([
                put({
                  type: 'getMultiFamilyPropertiesWithinRadius',
                  payload: payloadWithDistance,
                }),
                put({
                  type: 'getBTOwnedPropertiesWithinRadius',
                  payload: payloadWithDistance,
                }),
              ]);
            } else if (
              userGroup.includes('Avanta') ||
              (userGroup.includes('demo-CMA-DFW-only') &&
                (userEmail.includes('marchcapitalfund.com') ||
                  userEmail.includes('allcommonsenses')))
            ) {
              yield put({
                type: 'getMultiFamilyPropertiesWithinRadius',
                payload: payloadWithDistance,
              });
            }
          }
        } else {
          if (payload.mode === 'change BTOwned expiring only') {
            yield all([
              put({
                type: 'getBTOwnedPropertiesWithinPolygons',
                payload: { body: drawnCustomPolygons },
              }),
            ]);
          } else {
            if (
              userGroup.includes('BridgeTower') ||
              userGroup.includes('demo-users') ||
              userGroup.includes('dev')
            ) {
              yield all([
                put({
                  type: 'getMultiFamilyPropertiesWithinPolygons',
                  payload: { body: drawnCustomPolygons },
                }),
                put({
                  type: 'getBTOwnedPropertiesWithinPolygons',
                  payload: { body: drawnCustomPolygons },
                }),
              ]);
            } else if (
              userGroup.includes('Avanta') ||
              (userGroup.includes('demo-CMA-DFW-only') &&
                (userEmail.includes('marchcapitalfund.com') ||
                  userEmail.includes('allcommonsenses')))
            ) {
              yield put({
                type: 'getMultiFamilyPropertiesWithinPolygons',
                payload: { body: drawnCustomPolygons },
              });
            }
          }
        }
      },
      { type: 'takeLatest' },
    ],

    // get all property data
    getAllPropertyData: [
      function* ({ payload }, { all, call, put, select, cancel }) {
        abortFetches();
        yield put({
          type: 'saveCMAStates',
          payload: {
            fetchAllPropertyDataDone: false,
            fetchCompMLSDone: false,
            fetchCompSFRDone: false,
            fetchCompHotPadsDone: false,
            fetchCompPadSplitDone: false,
          },
        });

        let subjectPropertyPlacekey = '';
        let subjectPropertyCoordinates = [];
        let currentPropertyAddress = {};

        if (
          [
            'click on map',
            'from url with lngLat',
            'search coordinates',
            'from url with lngLat for intelligent comping',
          ].includes(payload.mode)
        ) {
          // for payload with lat/lng
          const responseMapboxReverseGeocoding = yield call(
            getMapboxReverseGeocodingData,
            payload,
          );

          subjectPropertyCoordinates =
            responseMapboxReverseGeocoding.features[0].center;
          const processedResult = processReverseGeocodingResponse(
            responseMapboxReverseGeocoding,
          );
          currentPropertyAddress = processedResult;
          // get placekey first for all future usage
          const responsePlacekey = yield call(getPlacekeyData, processedResult);
          subjectPropertyPlacekey =
            responsePlacekey && responsePlacekey.placekey
              ? responsePlacekey.placekey
              : '';
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentPropertyAddress: processedResult,
              subjectPropertyPlacekey: subjectPropertyPlacekey,
              firstTimeFetchSFR: true,
              firstTimeFetchHotPads: true,
              geocodingData: responseMapboxReverseGeocoding,
            },
          });
        } else if (['from url with address'].includes(payload.mode)) {
          // for payload with full address but not lat/lng
          const responseMapboxForwardGeocoding = yield call(
            getMapboxForwardGeocodingData,
            payload,
          );
          const processedResult = processForwardGeocodingResponse(
            responseMapboxForwardGeocoding,
          );
          subjectPropertyCoordinates = processedResult.eventCoordinates;
          currentPropertyAddress = processedResult.currentPropertyAddress;
          // get placekey first for all future usage
          const responsePlacekey = yield call(
            getPlacekeyData,
            processedResult.currentPropertyAddress,
          );
          subjectPropertyPlacekey =
            responsePlacekey && responsePlacekey.placekey
              ? responsePlacekey.placekey
              : '';
          yield put({
            type: 'saveCMAStates',
            payload: {
              geocodingData: processedResult.geocodingData,
              eventCoordinates: processedResult.eventCoordinates,
              currentPropertyAddress: processedResult.currentPropertyAddress,
              subjectPropertyPlacekey: subjectPropertyPlacekey,
              firstTimeFetchSFR: true,
              firstTimeFetchHotPads: true,
            },
          });
        } else if (['search address'].includes(payload.mode)) {
          currentPropertyAddress = yield select(
            (state) => state.CMA.currentPropertyAddress,
          );
          subjectPropertyCoordinates = yield select(
            (state) => state.CMA.eventCoordinates,
          );
          // get placekey first for all future usage
          const responsePlacekey = yield call(
            getPlacekeyData,
            currentPropertyAddress,
          );
          subjectPropertyPlacekey =
            responsePlacekey && responsePlacekey.placekey
              ? responsePlacekey.placekey
              : '';
          yield put({
            type: 'saveCMAStates',
            payload: {
              subjectPropertyPlacekey: subjectPropertyPlacekey,
              firstTimeFetchSFR: true,
              firstTimeFetchHotPads: true,
            },
          });
        } else if (
          ![
            'click on map',
            'from url with lngLat',
            'search coordinates',
            'search address',
            'from url with address',
          ].includes(payload.mode)
        ) {
          // subject property not changing
          // so no need to fetch subject property data
          subjectPropertyPlacekey = yield select(
            (state) => state.CMA.subjectPropertyPlacekey,
          );
          // console.log('subjectPropertyPlacekey', subjectPropertyPlacekey);
          currentPropertyAddress = yield select(
            (state) => state.CMA.currentPropertyAddress,
          );
          // console.log('currentPropertyAddress', currentPropertyAddress);
          subjectPropertyCoordinates = yield select(
            (state) => state.CMA.eventCoordinates,
          );
        }

        const marketRentPreference = yield select(
          (state) => state.CMA.marketRentPreference,
        );

        // get adjusted rent/sales params first
        const compingMode = yield select((state) => state.CMA.compingMode);
        const subjectPropertyParcelData = yield select(
          (state) => state.CMA.subjectPropertyParcelData,
        );
        // console.log('subjectPropertyParcelData', subjectPropertyParcelData);
        const payloadWithPlacekey = {
          ...payload,
          ...currentPropertyAddress,
          placekey: subjectPropertyPlacekey,
          subjectPropertyCoordinates: subjectPropertyCoordinates, // to be used in getSubjectProperty
          preference: payload.preference
            ? payload.preference
            : marketRentPreference,
          beds: payload.beds
            ? payload.beds
            : subjectPropertyParcelData.beds_count,
          baths: payload.baths
            ? payload.baths
            : subjectPropertyParcelData.baths,
          sqft: payload.sqft
            ? payload.sqft
            : subjectPropertyParcelData.total_area_sq_ft,
          yearbuilt: payload.yearbuilt
            ? payload.yearbuilt
            : subjectPropertyParcelData.year_built,
          rentavm: payload.rentavm
            ? payload.rentavm
            : subjectPropertyParcelData.rent,
          salesavm: payload.salesavm
            ? payload.salesavm
            : subjectPropertyParcelData.sales,
        };
        // console.log('payloadWithPlacekey', payloadWithPlacekey);

        // if comping mode is intelligent comping
        // only fetch adjusted rent/sales and subject property
        // because comps for intelligent comping are included in response of /adjusted
        if (compingMode === 'intelligentComping') {
          // call /adjusted with or without a placekey
          yield put.resolve({
            type: 'getAdjustedRentAndSaleForSingleProperty',
            payload: { ...payloadWithPlacekey, mode: payload.mode },
          });
          // console.log('payloadWithPlacekey', payloadWithPlacekey, 'subjectPropertyParcelData', subjectPropertyParcelData, 'subjectPropertyParcelData.placekey', subjectPropertyParcelData.placekey, 'subjectPropertyPlacekey', subjectPropertyPlacekey);
          // only fetch subject property when needed
          // fetching subject property again when there are edited property data on current subject property would cause the edited data to be overwritten
          if (
            [
              'click on map',
              'from url with lngLat',
              'from url with lngLat for intelligent comping',
              'search coordinates',
              'search address',
              'from url with address',
            ].includes(payload.mode)
          ) {
            yield put.resolve({
              type: 'getSubjectProperty',
              payload: {
                placekey: subjectPropertyPlacekey,
                lng: subjectPropertyCoordinates[0],
                lat: subjectPropertyCoordinates[1],
                subjectPropertyCoordinates: subjectPropertyCoordinates,
                streetAddress: payloadWithPlacekey.streetAddress,
                mode: payload.mode ? payload.mode : null, // for passing user customized property data from url in batch comping results to parcel data
                beds: payload.beds ? payload.beds : null,
                baths: payload.baths ? payload.baths : null,
                sqft: payload.sqft ? payload.sqft : null,
                yearbuilt: payload.yearbuilt ? payload.yearbuilt : null,
              },
            });
          }
          yield put({
            type: 'saveCMAStates',
            payload: {
              fetchCompMLSDone: true,
              fetchCompSFRDone: true,
              fetchCompHotPadsDone: true,
              fetchCompPadSplitDone: true,
            },
          });
        } else if (
          // non-intelligent comping starts here
          [
            'click on map',
            'from url with lngLat',
            'search coordinates',
            'search address',
            'from url with address',
          ].includes(payload.mode)
        ) {
          const eventCoordinates = yield select(
            (state) => state.CMA.eventCoordinates,
          );
          // const currentPropertyAddress = yield select(
          //   (state) => state.CMA.currentPropertyAddress,
          // );
          const payloadComplete = {
            ...payload,
            ...currentPropertyAddress,
            lng: subjectPropertyCoordinates[0],
            lat: subjectPropertyCoordinates[1],
            placekey: subjectPropertyPlacekey,
          };
          yield put.resolve({
            type: 'getSubjectProperty',
            payload: {
              placekey: subjectPropertyPlacekey,
              lng: subjectPropertyCoordinates[0],
              lat: subjectPropertyCoordinates[1],
              subjectPropertyCoordinates: subjectPropertyCoordinates,
              streetAddress: payloadComplete.streetAddress,
            },
          });
          yield all([
            put({
              type: 'getMLSAndSFRPropertiesFiltered',
              payload: payloadComplete,
            }),
            put({
              type: 'getBTAndMultiFamilyProperties',
              payload: payloadComplete,
            }),
          ]);
          yield put({
            type: 'saveCMAStates',
            payload: { fetchAllPropertyDataDone: true },
          });
          if (payload.mode === 'from url with address') {
            // const currentRadiusMile = yield select(
            //   (state) => state.CMA.currentRadiusMile,
            // );
            // yield put({
            //   type: 'getParcelOwnerSummary',
            //   payload: {
            //     lng: lng,
            //     lat: lat,
            //     distance: currentRadiusMile * 1609.34,
            //   },
            // });
            return eventCoordinates; // used in MapUtility/general.js for loading from URL using address as query
          }
        } else if (
          ['to sales mode', 'change MLS & SFR status'].includes(payload.mode)
        ) {
          yield all([
            put.resolve({
              type: 'getMLSAndSFRPropertiesFiltered',
              payload: payloadWithPlacekey,
            }),
          ]);
        } else if (['change BTOwned expiring only'].includes(payload.mode)) {
          yield all([
            put.resolve({
              type: 'getBTAndMultiFamilyProperties',
              payload: payloadWithPlacekey,
            }),
          ]);
        } else {
          // include:
          // 'to lease mode',
          // 'switch to no filter',
          // 'switch to smart filter',
          // 'switch to preset'
          yield all([
            put.resolve({
              type: 'getMLSAndSFRPropertiesFiltered',
              payload: payloadWithPlacekey,
            }),
            put.resolve({
              type: 'getBTAndMultiFamilyProperties',
              payload: payloadWithPlacekey,
            }),
          ]);
        }

        const currentRadiusMile = yield select(
          (state) => state.CMA.currentRadiusMile,
        );
        const eventCoordinates = yield select(
          (state) => state.CMA.eventCoordinates,
        );
        let lng, lat;
        if (payload.lng && payload.lat) {
          lng = payload.lng;
          lat = payload.lat;
        } else if (eventCoordinates && eventCoordinates.length > 0) {
          lng = eventCoordinates[0];
          lat = eventCoordinates[1];
        }
        if (lng && lat && currentRadiusMile) {
          yield put({
            type: 'getParcelOwnerSummary',
            payload: {
              lng: lng,
              lat: lat,
              distance: currentRadiusMile * 1609.34,
            },
          });
        }

        yield put({
          type: 'saveCMAStates',
          payload: {
            fetchAllPropertyDataDone: true,
            // fetchCompMLSDone: true,
            // fetchCompSFRDone: true,
            // fetchCompHotPadsDone: true,
          },
        });
      },
      { type: 'takeLatest' },
    ],

    *getNewBuildsProperties({ payload }, { call, put, select }) {
      console.log('getNewBuildsProperties payload: ', payload);
      let response;
      if (payload.type === 'Polygon') {
        response = yield call(getNewBuildsWithinPolygonData, payload);
      } else {
        response = yield call(getNewBuildsWithinRadiusData, payload);
      }
      console.log('getNewBuildsProperties: ', response);
      if (response) {
        const selectedRowKeysNewBuilds = [];
        for (let i = 0; i < response.length; i++) {
          selectedRowKeysNewBuilds.push(response[i].base_id);
        }

        yield put({
          type: 'saveCMAStates',
          payload: {
            currentNewBuildsProperties: response,
            selectedRowKeysNewBuilds: selectedRowKeysNewBuilds,
          },
        });
      } else {
        yield put({
          type: 'saveCMAStates',
          payload: {
            currentNewBuildsProperties: [],
          },
        });
      }
      if (payload.setIsLoading) {
        payload.setIsLoading(false);
      }
    },

    *getScorecard({ payload }, { call, put, select }) {
      yield put({
        type: 'saveCMAStates',
        payload: {
          getScorecardLoading: true,
        },
      });

      const response = yield call(getScorecardData, payload);
      console.log(response);
      if (response) {
        yield put({
          type: 'saveCMAStates',
          payload: {
            scorecardDownloadLink: response,
            getScorecardLoading: false,
          },
        });
      } else {
        yield put({
          type: 'saveCMAStates',
          payload: {
            scorecardDownloadLink: '',
            getScorecardLoading: false,
          },
        });
      }
    },

    *getUserScorecardRecord({ payload }, { call, put, select }) {
      const response = yield call(getUserScorecardRecordData, payload);

      if (response) {
        const result = [];
        for (let i = 0; i < response.length; i++) {
          if (response[i].csv_url != null) {
            result.push(response[i]);
          }
        }

        yield put({
          type: 'saveCMAStates',
          payload: {
            scorecardRecordList: result,
          },
        });
      } else {
        yield put({
          type: 'saveCMAStates',
          payload: {
            scorecardRecordList: [],
          },
        });
      }
    },

    *getParcelOwnerSummaryWithinPolygons({ payload }, { call, put, select }) {
      const { drawnCustomPolygons } = yield select(({ CMA }) => {
        return { drawnCustomPolygons: CMA.drawnCustomPolygons };
      });

      const response = yield call(getParcelOwnerSummaryWithinPolygonsData, {
        body: drawnCustomPolygons,
      });

      if (response) {
        let extraCount = 0;
        if (response.institution_count.length > 0) {
          response.institution_count = response.institution_count.filter(
            function (obj) {
              if (obj.institution === 'Divvy') {
                extraCount += obj.count;
              }
              return obj.institution !== 'Divvy';
            },
          );
        }

        const objIndex = response.institution_count.findIndex(
          (obj) => obj.institution == 'Other',
        );
        if (objIndex > -1) {
          response.institution_count[objIndex].count += extraCount;
        }

        yield put({
          type: 'saveCMAStates',
          payload: {
            currentParcelOwnerSummary: response,
          },
        });
      }
    },

    *getBTOwnedPropertiesWithinPolygons({ payload }, { call, put, select }) {
      const searchingMode = yield select((state) => state.CMA.searchingMode);
      const userGroup = yield select((state) => state.CMA.userGroup);
      const expDateFilterOn = yield select(
        (state) => state.CMA.expDateFilterOn,
      );
      if (searchingMode === 'Lease' && userGroup.includes('BridgeTower')) {
        const response = yield call(getBTOwnedPropertiesWithinPolygonsData, {
          ...payload,
          expDateFilterOn: expDateFilterOn ? 'yes' : 'no',
        });
        if (response) {
          let geojsonFeaturesBTOwned = [];
          for (const property of response) {
            // generate geojson features
            // generate properties for each feature; copy object except geometry
            // re: https://stackoverflow.com/a/34710102/10039571
            let { geography, ...geojsonProperties } = property;
            geojsonFeaturesBTOwned.push({
              type: 'Feature',
              geometry: property.geography,
              properties: geojsonProperties,
            });
          }
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentBTOwnedProperties: response,
              currentBTOwnedGeoJSON: {
                type: 'FeatureCollection',
                features: geojsonFeaturesBTOwned,
              },
            },
          });
        }
      }
    },

    *getMultiFamilyPropertiesWithinPolygons(
      { payload },
      { call, put, select },
    ) {
      const searchingMode = yield select((state) => state.CMA.searchingMode);
      const userGroup = yield select((state) => state.CMA.userGroup);

      if (
        searchingMode === 'Lease' &&
        !userGroup.includes('CommonGroundCapital')
      ) {
        const response = yield call(
          getMultiFamilyPropertiesWithinPolygonsData,
          payload,
        );
        if (response) {
          let allRowKeyMultiFamily = [],
            geojsonFeaturesMultiFamily = [];
          for (const property of response) {
            let { geom, ...geojsonProperties } = property;
            geojsonFeaturesMultiFamily.push({
              type: 'Feature',
              geometry: property.geom,
              properties: geojsonProperties,
            });
          }
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentMultiFamilyProperties: response,
              currentMultiFamilyGeoJSON: {
                type: 'FeatureCollection',
                features: geojsonFeaturesMultiFamily,
              },
            },
          });
        }
      }
    },

    *getSubjectForCustomPolygon({ payload }, { call, put, select, cancel }) {
      // Metro restriction
      const response = yield call(getMapboxReverseGeocodingData, payload);
      if (response) {
        const subjectPropertyCoordinates = response.features[0].center;
        const processedResult = processReverseGeocodingResponse(response);
        const placekeyResponse = yield call(getPlacekeyData, processedResult);

        yield put({
          type: 'saveCMAStates',
          payload: {
            currentPropertyAddress: processedResult,
          },
        });

        // get parcel data
        yield put.resolve({
          type: 'getSubjectProperty',
          payload: {
            ...payload,
            placekey: placekeyResponse
              ? placekeyResponse.placekey
              : 'undefined',
            subjectPropertyCoordinates,
            streetAddress: processedResult.streetAddress,
          },
        });

        const isDistrictFilterOn = yield select(
          (state) => state.CMA.isDistrictFilterOn,
        );
        const isCountyFilterOn = yield select(
          (state) => state.CMA.isCountyFilterOn,
        );
        const isZipCodeFilterOn = yield select(
          (state) => state.CMA.isZipCodeFilterOn,
        );

        if (isDistrictFilterOn) {
          yield put.resolve({
            type: 'getDistrictForWithinFilter',
          });
        }

        if (isCountyFilterOn) {
          yield put.resolve({
            type: 'getCountyForWithinFilter',
          });
        }

        if (isZipCodeFilterOn) {
          yield put.resolve({
            type: 'getZipCodeForWithinFilter',
          });
        }

        const compingMode = yield select((state) => state.CMA.compingMode);
        const userGroup = yield select((state) => state.CMA.userGroup);
        const selectedUserGroup = yield select(
          (state) => state.CMA.selectedUserGroup,
        );
        if (compingMode === 'smartFilter') {
          yield put({
            type: 'filterMLSAndSFRData',
            payload: {
              dataSourceType: 'MLS',
              mode: payload.mode,
              selectedUserGroup: selectedUserGroup,
            },
          });
          yield put({
            type: 'filterMLSAndSFRData',
            payload: {
              dataSourceType: 'SFR',
              mode: payload.mode,
              selectedUserGroup: selectedUserGroup,
            },
          });
          yield put({
            type: 'filterMLSAndSFRData',
            payload: {
              dataSourceType: 'HotPads',
              mode: payload.mode,
              selectedUserGroup: selectedUserGroup,
            },
          });
        } else {
          yield put({
            type: 'filterMLSAndSFRDataManual',
            payload: {
              dataSourceType: 'MLS',
              mode: payload.mode,
            },
          });
          yield put({
            type: 'filterMLSAndSFRDataManual',
            payload: {
              dataSourceType: 'SFR',
              mode: payload.mode,
            },
          });
          yield put({
            type: 'filterMLSAndSFRDataManual',
            payload: {
              dataSourceType: 'HotPads',
              mode: payload.mode,
            },
          });
        }
      }
    },

    *getMobile({ payload }, { call, put }) {
      const response = yield call(getMobileData, payload);
      if (response) {
        let features = [];
        for (const data of response) {
          const { geom, ...geojsonProperties } = data;
          features.push({
            type: 'Feature',
            geometry: geom,
            properties: geojsonProperties,
          });
        }
        yield put({
          type: 'saveCMAStates',
          payload: {
            currentMobileGeoJSON: {
              type: 'FeatureCollection',
              features: features,
            },
          },
        });
      }
    },

    getCurrentMLSListingSummaryWithinSphere: [
      function* ({ payload }, { call, put, select, cancel }) {
        const currentAOILoading = yield select(
          (state) => state.CMA.currentAOILoading,
        );
        if (currentAOILoading) {
          try {
            if (currentSphereAbortController) {
              currentSphereAbortController.abort();
            }
            currentSphereAbortController = new AbortController();
          } catch (error) {
            console.log(error);
          }
        } else {
          currentSphereAbortController = new AbortController();
        }

        yield put({
          type: 'saveCMAStates',
          payload: { currentAOILoading: true, currentAOIMarketCondition: {} },
        });

        let response;
        try {
          response = yield call(getMLSListingSummaryWithinSphereData, {
            ...payload,
            signal: currentSphereAbortController.signal,
          });
        } catch (error) {
          response = null;
          yield cancel();
        }

        if (response) {
          // console.log(
          //   'getCurrentMLSListingSummaryWithinSphere response',
          //   response,
          // );
          // process data
          const processedResponse = processMarketConditionResponse(response);
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentAOIMarketCondition: processedResponse,
              currentAOILoading: false,
            },
          });
        } else {
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentAOIMarketCondition: {},
              currentAOILoading: false,
            },
          });
        }
      },
      { type: 'takeLatest' },
    ],

    getCompMLSListingSummaryWithinSphere: [
      function* ({ payload }, { call, put, select, cancel }) {
        const compAOILoading = yield select(
          (state) => state.CMA.compAOILoading,
        );
        if (compAOILoading) {
          try {
            if (compSphereAbortController) {
              compSphereAbortController.abort();
            }
            compSphereAbortController = new AbortController();
          } catch (error) {
            console.log(error);
          }
        } else {
          compSphereAbortController = new AbortController();
        }

        yield put({
          type: 'saveCMAStates',
          payload: { compAOILoading: true, compAOIMarketCondition: {} },
        });

        let response;
        try {
          response = yield call(getMLSListingSummaryWithinSphereData, {
            ...payload,
            signal: compSphereAbortController.signal,
          });
        } catch (error) {
          response = null;
          yield cancel();
        }

        if (response) {
          // console.log('getCompMLSListingSummaryWithinSphere', response);
          // process data
          const processedResponse = processMarketConditionResponse(response);
          yield put({
            type: 'saveCMAStates',
            payload: {
              compAOIMarketCondition: processedResponse,
              compAOILoading: false,
            },
          });
        } else {
          yield put({
            type: 'saveCMAStates',
            payload: {
              compAOIMarketCondition: {},
              compAOILoading: false,
            },
          });
        }
      },
      { type: 'takeLatest' },
    ],

    getCurrentMLSListingSummaryWithinZIPCode: [
      function* ({ payload }, { call, put, select, cancel }) {
        const currentZIPCodeLoading = yield select(
          (state) => state.CMA.currentZIPCodeLoading,
        );
        if (currentZIPCodeLoading) {
          try {
            if (currentZIPCodeAbortController) {
              currentZIPCodeAbortController.abort();
            }
            currentZIPCodeAbortController = new AbortController();
          } catch (error) {
            console.log(error);
          }
        } else {
          currentZIPCodeAbortController = new AbortController();
        }

        yield put({
          type: 'saveCMAStates',
          payload: {
            currentZIPCodeLoading: true,
            currentZIPCodeMarketCondition: {},
          },
        });

        let response;
        try {
          response = yield call(getMLSListingSummaryWithinZIPCodeData, {
            ...payload,
            signal: currentZIPCodeAbortController.signal,
          });
        } catch (error) {
          response = null;
          yield cancel();
        }

        if (response) {
          // process data
          // console.log('getCurrentMLSListingSummaryWithinZIPCode', response);
          const processedResponse = processMarketConditionResponse(response);
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentZIPCodeMarketCondition: processedResponse,
              currentZIPCodeLoading: false,
            },
          });
        } else {
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentZIPCodeMarketCondition: {},
              currentZIPCodeLoading: false,
            },
          });
        }
      },
      { type: 'takeLatest' },
    ],
    getCompMLSListingSummaryWithinZIPCode: [
      function* ({ payload }, { call, put, select, cancel }) {
        const compZIPCodeLoading = yield select(
          (state) => state.CMA.compZIPCodeLoading,
        );
        if (compZIPCodeLoading) {
          try {
            if (compZIPCodeAbortController) {
              compZIPCodeAbortController.abort();
            }
            compZIPCodeAbortController = new AbortController();
          } catch (error) {
            console.log(error);
          }
        } else {
          compZIPCodeAbortController = new AbortController();
        }

        yield put({
          type: 'saveCMAStates',
          payload: { compZIPCodeLoading: true, compZIPCodeMarketCondition: {} },
        });

        let response;
        try {
          response = yield call(getMLSListingSummaryWithinZIPCodeData, {
            ...payload,
            signal: compZIPCodeAbortController.signal,
          });
        } catch (error) {
          response = null;
          yield cancel();
        }

        if (response) {
          // console.log('getCompMLSListingSummaryWithinZIPCode', response);
          // process data
          const processedResponse = processMarketConditionResponse(response);
          yield put({
            type: 'saveCMAStates',
            payload: {
              compZIPCodeMarketCondition: processedResponse,
              compZIPCodeLoading: false,
            },
          });
        } else {
          yield put({
            type: 'saveCMAStates',
            payload: {
              compZIPCodeMarketCondition: {},
              compZIPCodeLoading: false,
            },
          });
        }
      },
      { type: 'takeLatest' },
    ],

    // currentDistrictAbortController
    getCurrentMLSListingSummaryWithinDistrict: [
      function* ({ payload }, { call, put, select, cancel }) {
        const currentDistrictLoading = yield select(
          (state) => state.CMA.currentDistrictLoading,
        );
        if (currentDistrictLoading) {
          try {
            if (currentDistrictAbortController) {
              currentDistrictAbortController.abort();
            }
            currentDistrictAbortController = new AbortController();
          } catch (error) {
            console.log(error);
          }
        } else {
          currentDistrictAbortController = new AbortController();
        }

        yield put({
          type: 'saveCMAStates',
          payload: {
            currentDistrictLoading: true,
            currentDistrictMarketCondition: {},
          },
        });

        let response;
        try {
          response = yield call(getMLSListingSummaryWithinDistrictData, {
            ...payload,
            signal: currentDistrictAbortController.signal,
          });
        } catch (error) {
          response = null;
          yield cancel();
        }

        if (response) {
          // process data
          // console.log('getCurrentMLSListingSummaryWithinDistrict', response);
          const processedResponse = processMarketConditionResponse(response);
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentDistrictMarketCondition: processedResponse,
              currentDistrictLoading: false,
            },
          });
        } else {
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentDistrictMarketCondition: {},
              currentDistrictLoading: false,
            },
          });
        }
      },
      { type: 'takeLatest' },
    ],
    getCompMLSListingSummaryWithinDistrict: [
      function* ({ payload }, { call, put, select, cancel }) {
        const compDistrictLoading = yield select(
          (state) => state.CMA.compDistrictLoading,
        );
        if (compDistrictLoading) {
          try {
            if (compDistrictAbortController) {
              compDistrictAbortController.abort();
            }
            compDistrictAbortController = new AbortController();
          } catch (error) {
            console.log(error);
          }
        } else {
          compDistrictAbortController = new AbortController();
        }

        yield put({
          type: 'saveCMAStates',
          payload: {
            compDistrictLoading: true,
            compDistrictMarketCondition: {},
          },
        });

        let response;
        try {
          response = yield call(getMLSListingSummaryWithinDistrictData, {
            ...payload,
            signal: compDistrictAbortController.signal,
          });
        } catch (error) {
          response = null;
          yield cancel();
        }

        if (response) {
          // process data
          // console.log('getCompMLSListingSummaryWithinDistrict', response);
          const processedResponse = processMarketConditionResponse(response);
          yield put({
            type: 'saveCMAStates',
            payload: {
              compDistrictMarketCondition: processedResponse,
              compDistrictLoading: false,
            },
          });
        } else {
          yield put({
            type: 'saveCMAStates',
            payload: {
              compDistrictMarketCondition: {},
              compDistrictLoading: false,
            },
          });
        }
      },
      { type: 'takeLatest' },
    ],

    getCurrentMLSListingSummaryWithinCounty: [
      function* ({ payload }, { call, put, select, cancel }) {
        yield put({
          type: 'saveCMAStates',
          payload: {
            currentCountyLoading: true,
            currentCountyMarketCondition: {},
          },
        });

        let response;
        try {
          response = yield call(getMLSListingSummaryWithinCountyData, {
            ...payload,
          });
        } catch (error) {
          response = null;
        }

        if (response) {
          // process data
          // console.log('getCurrentMLSListingSummaryWithinDistrict', response);
          const processedResponse = processMarketConditionResponse(response);
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentCountyMarketCondition: processedResponse,
              currentCountyLoading: false,
            },
          });
        } else {
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentCountyMarketCondition: {},
              currentCountyLoading: false,
            },
          });
        }
      },
      { type: 'takeLatest' },
    ],
    getCompMLSListingSummaryWithinCounty: [
      function* ({ payload }, { call, put, select, cancel }) {
        yield put({
          type: 'saveCMAStates',
          payload: {
            compCountyLoading: true,
            compCountyMarketCondition: {},
          },
        });

        let response;
        try {
          response = yield call(getMLSListingSummaryWithinCountyData, {
            ...payload,
          });
        } catch (error) {
          response = null;
        }

        if (response) {
          // process data
          // console.log('getCompMLSListingSummaryWithinDistrict', response);
          const processedResponse = processMarketConditionResponse(response);
          yield put({
            type: 'saveCMAStates',
            payload: {
              compCountyMarketCondition: processedResponse,
              compCountyLoading: false,
            },
          });
        } else {
          yield put({
            type: 'saveCMAStates',
            payload: {
              compCountyMarketCondition: {},
              compCountyLoading: false,
            },
          });
        }
      },
      { type: 'takeLatest' },
    ],

    // currentMetroAbortController
    getCurrentMLSListingSummaryWithinMetro: [
      function* ({ payload }, { call, put, select, cancel }) {
        const currentMetroLoading = yield select(
          (state) => state.CMA.currentMetroLoading,
        );
        if (currentMetroLoading) {
          try {
            if (currentMetroAbortController) {
              currentMetroAbortController.abort();
            }
            currentMetroAbortController = new AbortController();
          } catch (error) {
            console.log(error);
          }
        } else {
          currentMetroAbortController = new AbortController();
        }

        yield put({
          type: 'saveCMAStates',
          payload: {
            currentMetroLoading: true,
            currentMetroMarketCondition: {},
          },
        });

        let response;
        try {
          response = yield call(getMLSListingSummaryWithinMetroData, {
            ...payload,
            signal: currentMetroAbortController.signal,
          });
        } catch (error) {
          response = null;
          yield cancel();
        }

        if (response) {
          // console.log('getCurrentMLSListingSummaryWithinMetro', response);
          // process data
          const processedResponse = processMarketConditionResponse(response);
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentMetroMarketCondition: processedResponse,
              currentMetroLoading: false,
            },
          });
        } else {
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentMetroMarketCondition: {},
              currentMetroLoading: false,
            },
          });
        }
      },
      { type: 'takeLatest' },
    ],
    getCompMLSListingSummaryWithinMetro: [
      function* ({ payload }, { call, put, select, cancel }) {
        const compMetroLoading = yield select(
          (state) => state.CMA.compMetroLoading,
        );
        if (compMetroLoading) {
          try {
            if (compMetroAbortController) {
              compMetroAbortController.abort();
            }
            compMetroAbortController = new AbortController();
          } catch (error) {
            console.log(error);
          }
        } else {
          compMetroAbortController = new AbortController();
        }

        yield put({
          type: 'saveCMAStates',
          payload: { compMetroLoading: true, compMetroMarketCondition: {} },
        });

        let response;
        try {
          response = yield call(getMLSListingSummaryWithinMetroData, {
            ...payload,
            signal: compMetroAbortController.signal,
          });
        } catch (error) {
          response = null;
          yield cancel();
        }

        if (response) {
          // process data
          // console.log('getCompMLSListingSummaryWithinMetro', response);
          const processedResponse = processMarketConditionResponse(response);
          yield put({
            type: 'saveCMAStates',
            payload: {
              compMetroMarketCondition: processedResponse,
              compMetroLoading: false,
            },
          });
        } else {
          yield put({
            type: 'saveCMAStates',
            payload: {
              compMetroMarketCondition: {},
              compMetroLoading: false,
            },
          });
        }
      },
      { type: 'takeLatest' },
    ],

    getCurrentMLSListingSummaryWithinPolygon: [
      function* ({ payload }, { call, put }) {
        yield put({
          type: 'saveCMAStates',
          payload: { currentAOILoading: true },
        });
        const response = yield call(
          getMLSListingSummaryWithinPolygonData,
          payload,
        );
        if (response) {
          const processedResponse = processMarketConditionResponse(response);
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentAOIMarketCondition: processedResponse,
              currentAOILoading: false,
            },
          });
        } else {
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentAOIMarketCondition: {},
              currentAOILoading: false,
            },
          });
        }
      },
      { type: 'takeLatest' },
    ],
    getCompMLSListingSummaryWithinPolygon: [
      function* ({ payload }, { call, put }) {
        yield put({
          type: 'saveCMAStates',
          payload: { compAOILoading: true },
        });
        const response = yield call(
          getMLSListingSummaryWithinPolygonData,
          payload,
        );
        if (response) {
          const processedResponse = processMarketConditionResponse(response);
          yield put({
            type: 'saveCMAStates',
            payload: {
              compAOIMarketCondition: processedResponse,
              compAOILoading: false,
            },
          });
        } else {
          yield put({
            type: 'saveCMAStates',
            payload: {
              compAOIMarketCondition: {},
              compAOILoading: false,
            },
          });
        }
      },
      { type: 'takeLatest' },
    ],

    getMLSListingSummaryPointWithinLayer: [
      function* ({ payload }, { call, put }) {
        const response = yield call(
          getMLSListingSummaryPointWithinLayerData,
          payload,
        );
        if (response) {
          yield put({
            type: 'saveCMAStates',
            payload: {
              lastMarketConditionPoint: response,
            },
          });
          return response;
        }
      },
      { type: 'takeLatest' },
    ],

    *getScorecardAnalyticsByDate({ payload }, { call, put }) {
      const response = yield call(getScorecardAnalyticsByDateData, payload);
      if (response) {
        yield put({
          type: 'saveCMAStates',
          payload: {
            scorecardAnalytics: response,
          },
        });
      }
    },
    *getScorecardAnalyticsByDevManager({ payload }, { call, put }) {
      const response = yield call(
        getScorecardAnalyticsByDevManagerData,
        payload,
      );
      if (response) {
        yield put({
          type: 'saveCMAStates',
          payload: {
            scorecardAnalytics: response,
          },
        });
      }
    },
    *getScorecardAnalyticsByConsultant({ payload }, { call, put }) {
      const response = yield call(
        getScorecardAnalyticsByConsultantData,
        payload,
      );
      if (response) {
        yield put({
          type: 'saveCMAStates',
          payload: {
            scorecardAnalytics: response,
          },
        });
      }
    },
    *getScorecardAnalyticsBySubmarket({ payload }, { call, put }) {
      const response = yield call(
        getScorecardAnalyticsBySubmarketData,
        payload,
      );
      if (response) {
        yield put({
          type: 'saveCMAStates',
          payload: {
            scorecardAnalytics: response,
          },
        });
      }
    },
    *getScorecardAnalyticsByCounty({ payload }, { call, put }) {
      const response = yield call(getScorecardAnalyticsByCountyData, payload);
      if (response) {
        yield put({
          type: 'saveCMAStates',
          payload: {
            scorecardAnalytics: response,
          },
        });
      }
    },

    *getZIPCodeBySearch({ payload }, { call, put }) {
      const response = yield call(getZIPCodeBySearchData, payload);
      if (response && response.id && response.geom) {
        let features = [];

        const { geom, ...geojsonProperties } = response;
        features.push({
          type: 'Feature',
          geometry: geom,
          properties: geojsonProperties,
        });

        yield put({
          type: 'saveCMAStates',
          payload: {
            zipCodeSearchGeoJSON: {
              type: 'FeatureCollection',
              features: features,
            },
          },
        });
      }
    },

    *getMarketRentCompare({ payload }, { call, put, cancel }) {
      try {
        if (fairMarketRentAbortController) {
          fairMarketRentAbortController.abort();
        }
        fairMarketRentAbortController = new AbortController();
      } catch (err) {
        console.log(err);
      }

      let response;
      try {
        response = yield call(getMarketRentCompareData, {
          ...payload,
          signal: fairMarketRentAbortController.signal,
        });
      } catch (error) {
        response = null;
        yield cancel();
      }

      if (response) {
        yield put({
          type: 'saveCMAStates',
          payload: {
            marketRentCompare: processMarketRentCompareResponse(response),
          },
        });
      }
    },

    getAdjustedRentAndSaleForSingleProperty: [
      function* ({ payload }, { call, put, select }) {
        try {
          const adjustedRentAndSaleRequestBodySingleProperty = {
            // ...payloadPlacekey,
            address: payload.streetAddress,
            city: payload.city,
            postal_code: payload.postalCode,
            latitude: payload.latitude,
            longitude: payload.longitude,
            region: payload.region,
            uid: null,
            placekey: payload.placekey ? payload.placekey : null,
            list: 'MLS',
          };

          if (
            payload.mode &&
            [
              'edit property details',
              'from url with lngLat for intelligent comping',
            ].includes(payload.mode)
          ) {
            adjustedRentAndSaleRequestBodySingleProperty.beds = payload.beds;
            adjustedRentAndSaleRequestBodySingleProperty.baths = payload.baths;
            adjustedRentAndSaleRequestBodySingleProperty.sqft = payload.sqft;
            adjustedRentAndSaleRequestBodySingleProperty.yearbuilt =
              payload.yearbuilt;
            adjustedRentAndSaleRequestBodySingleProperty.rentavm =
              payload.rentavm;
            adjustedRentAndSaleRequestBodySingleProperty.salesavm =
              payload.salesavm;
          }

          const response = yield call(getAdjustedRentAndSaleData, {
            preference: payload.preference,
            body: [adjustedRentAndSaleRequestBodySingleProperty],
          });
          if (response && Array.isArray(response) && response.length > 0) {
            const searchingMode = yield select(
              (state) => state.CMA.searchingMode,
            );
            const adjustedCompParams =
              searchingMode === 'Lease'
                ? response[0].rentParams
                  ? response[0].rentParams
                  : response[0].sfrParams
                : response[0].salesParams;
            const adjustedMLSComp =
              searchingMode === 'Lease'
                ? response[0].rentComp
                : response[0].salesComp;
            const selectedRowKeysMLSLease = response[0].rentComp
              ? response[0].rentComp.map((property) => property.mlsid)
              : [];
            const selectedRowKeysMLSSale = response[0].salesComp
              ? response[0].salesComp.map((property) => property.mlsid)
              : [];
            const selectedRowKeysNationalOperators = response[0].sfrComp
              ? response[0].sfrComp.map((property) => property.base_id)
              : [];
            const selectedRowKeysHotPads = response[0].hotpadComp
              ? response[0].hotpadComp.map((property) => property.base_id)
              : [];

            const adjustedRentCompParams = response[0].rentParams
              ? response[0].rentParams
              : response[0].sfrParams
              ? response[0].sfrParams
              : response[0].hotpadParams
              ? response[0].hotpadParams
              : null;

            // select the biggest distance
            try {
              if (adjustedRentCompParams) {
                adjustedRentCompParams.distance = Math.max(
                  response[0]?.rentParams?.distance || 0,
                  response[0]?.sfrParams?.distance || 0,
                  response[0]?.hotpadParams?.distance || 0,
                );
              }
            } catch (error) {
              console.log('intelligent comp distance: ', error);
            }

            const userGroup = yield select((state) => state.CMA.userGroup);

            yield put({
              type: 'saveCMAStates',
              payload: {
                // adjustedRentCurrentProperty:
                //   processAdjustedRentAndSalesResponseForSingleProperty(response)
                //     .adjustedRent,
                // adjustedSalesCurrentProperty:
                //   processAdjustedRentAndSalesResponseForSingleProperty(response)
                //     .adjustedSales,
                // comps
                currentMLSProperties: adjustedMLSComp
                  ? processMLSProperties({
                      response: adjustedMLSComp,
                      selectedUserGroup,
                    })
                  : [],
                currentMLSPropertiesFiltered: adjustedMLSComp
                  ? processMLSProperties({
                      response: adjustedMLSComp,
                      selectedUserGroup,
                    })
                  : [],
                currentMLSGeoJSON: generateGeoJSON(adjustedMLSComp, 'MLS'),
                selectedRowKeysMLSLease: selectedRowKeysMLSLease,
                selectedRowKeysMLSSale: selectedRowKeysMLSSale,

                currentNationalOperatorsProperties: response[0].sfrComp
                  ? processSFRProperties({
                      response: response[0].sfrComp,
                      selectedUserGroup,
                    })
                  : [],
                currentNationalOperatorsPropertiesFiltered: response[0].sfrComp
                  ? processSFRProperties({
                      response: response[0].sfrComp,
                      selectedUserGroup,
                    })
                  : [],
                currentNationalOperatorsGeoJSON: generateGeoJSON(
                  response[0].sfrComp,
                  'SFR',
                ),
                selectedRowKeysNationalOperators:
                  selectedRowKeysNationalOperators,

                currentHotPadsProperties: response[0].hotpadComp
                  ? processSFRProperties({
                      response: response[0].hotpadComp,
                      selectedUserGroup,
                    })
                  : [],
                currentHotPadsPropertiesFiltered: response[0].hotpadComp
                  ? processSFRProperties({
                      response: response[0].hotpadComp,
                      selectedUserGroup,
                    })
                  : [],
                currentHotPadsGeoJSON: generateGeoJSON(
                  response[0].hotpadComp,
                  'HotPads',
                ),
                selectedRowKeysHotPads: selectedRowKeysHotPads,

                adjustedRentCompParams: adjustedRentCompParams || {},
                adjustedSalesCompParams: response[0].salesParams,
                adjustedRentFormula: response[0].rent_equation || '',
                adjustedSalesFormula: response[0].sales_equation || '',
                adjustedPlacekey: response[0].placekey,
                // save params as filter values
                ...convertAdjustedParamsFromAPIToUI(
                  adjustedCompParams,
                  userGroup,
                ),
                compingMode: 'intelligentComping',
                // currentRadiusMile:
                //     adjustedCompParams && adjustedCompParams.distance
                //       ? adjustedCompParams.distance
                //       : 0.5,
              },
            });
            // get parcel owner summary with radius from response when loading from url in batch comping results
            // if (payload.mode && ['from url with lngLat for intelligent comping'].includes(payload.mode)) {
            //   yield put({
            //     type: 'getParcelOwnerSummary',
            //     payload: {
            //       lng: payload.longitude,
            //       lat: payload.latitude,
            //       distance: (
            //         adjustedCompParams && adjustedCompParams.distance
            //           ? adjustedCompParams.distance
            //           : 0.5
            //         ) * 1609.34,
            //     },
            //   });
            // }
          }
        } catch (error) {
          console.log('getAdjustedRentAndSaleForSingleProperty error:', error);
        }
      },
      { type: 'takeLatest' },
    ],

    getHouseDetailsData: [
      function* ({ payload }, { call, put, select }) {
        const response = yield call(getHouseDetailsData, payload);

        // getBrokerOfficeInfo
        if (response) {
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentHouseDetails: response,
            },
          });

          yield put.resolve({
            type: 'getBrokerOfficeInfo',
            payload: {
              mlsNumber: payload.listingID,
              cityCode: payload.cityCode,
            },
          });
        } else {
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentHouseDetails: {},
            },
          });
        }
      },
      { type: 'takeLatest' },
    ],

    *getAPNOwnerName({ payload }, { call, put }) {
      const response = yield call(getAPNOwnerNameData, payload);

      if (response) {
        yield put({
          type: 'saveCMAStates',
          payload: {
            currentAPNOwner: response,
          },
        });
      } else {
        yield put({
          type: 'saveCMAStates',
          payload: {
            currentAPNOwner: {},
          },
        });
      }
    },

    getMLSListingSummaryChart: [
      function* ({ payload }, { call, put, all }) {
        const { type, ...payloadData } = payload;
        console.log('getMLSListingSummaryChart payload', payload);

        if (Array.isArray(type) && type.length > 0) {
          const calls = type.map((t) => {
            if (t === 'aoi') {
              return call(MLSListingSummaryChartSphereData, payloadData);
            } else if (t === 'multipolygon') {
              return call(MLSListingSummaryChartMultipolygonData, payloadData);
            } else if (t === 'zipcode') {
              return call(MLSListingSummaryChartZIPCodeData, payloadData);
            } else if (t === 'metro') {
              return call(MLSListingSummaryChartCBSAData, payloadData);
            } else if (t === 'district') {
              return call(
                MLSListingSummaryChartSchoolDistrictData,
                payloadData,
              );
            } else if (t === 'county') {
              return call(MLSListingSummaryChartCountyData, payloadData);
            }
          });

          const responses = yield all(calls);
          if (responses && responses.length > 0) {
            const processedResponses = responses.map((response, index) => {
              return {
                type: type[index] === 'multipolygon' ? 'aoi' : type[index],
                data: processMLSListingSummaryChartResponse(
                  response,
                  payload.propertyType,
                ),
              };
            });

            console.log('processedResponses:', processedResponses);

            yield put({
              type: 'saveCMAStates',
              payload: {
                currentMLSListingChartData: processedResponses,
              },
            });
          } else {
            yield put({
              type: 'saveCMAStates',
              payload: {
                currentMLSListingChartData: [],
              },
            });
          }
        } else {
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentMLSListingChartData: [],
            },
          });
        }

        // let response = null;
        // if (type === 'aoi') {
        //   response = yield call(MLSListingSummaryChartSphereData, payloadData);
        // } else if (type === 'multipolygon') {
        //   response = yield call(
        //     MLSListingSummaryChartMultipolygonData,
        //     payloadData,
        //   );
        // } else if (type === 'zipcode') {
        //   response = yield call(MLSListingSummaryChartZIPCodeData, payloadData);
        // } else if (type === 'metro') {
        //   response = yield call(MLSListingSummaryChartCBSAData, payloadData);
        // } else if (type === 'district') {
        //   response = yield call(
        //     MLSListingSummaryChartSchoolDistrictData,
        //     payloadData,
        //   );
        // } else if (type === 'county') {
        //   response = yield call(MLSListingSummaryChartCountyData, payloadData);
        // }

        // if (response) {
        //   const processedResponse = processMLSListingSummaryChartResponse(
        //     response,
        //     payload.propertyType,
        //   );

        //   yield put({
        //     type: 'saveCMAStates',
        //     payload: {
        //       currentMLSListingChartData: processedResponse,
        //     },
        //   });
        // } else {
        //   yield put({
        //     type: 'saveCMAStates',
        //     payload: {
        //       currentMLSListingChartData: {},
        //     },
        //   });
        // }
      },
      { type: 'takeLatest' },
    ],

    getBrokerOfficeInfo: [
      function* ({ payload }, { all, call, put, select }) {
        const response = yield call(getBrokerOfficeInfoData, payload);

        if (response && !isEmpty(response)) {
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentSingleListingBrokerOfficeInfo: response,
            },
          });
        } else {
          // if API returns empty
          yield put({
            type: 'saveCMAStates',
            payload: {
              currentSingleListingBrokerOfficeInfo: {},
            },
          });
        }
      },
      { type: 'takeLatest' },
    ],

    *sendPropertyToOffMarket({ payload }, { call, put, select }) {
      yield put({
        type: 'saveCMAStates',
        payload: {
          doneSendingPropertyToOffMarket: false,
        },
      });
      const response = yield call(sendPropertyToOffMarketData, payload);
      if (response && response === 'Success') {
        message.success(
          'Property has been sent to Off-Market custom one-off portfolio',
        );
      } else {
        message.error('Failed to send property to Off-Market');
      }
      yield put({
        type: 'saveCMAStates',
        payload: {
          doneSendingPropertyToOffMarket: true,
        },
      });
    },

    *saveComps({ payload }, { call, put, select }) {
      const response = yield call(saveCompsData, payload);
      if (response && response.id) {
        message.success('Selected comps have been saved');
      } else {
        message.error('Failed to save selected comps. Please try again.');
      }
    },

    *getSavedComps({ payload }, { call, put, select }) {
      const responseMLS = yield call(getSavedMLSCompsData, payload);
      if (responseMLS && Array.isArray(responseMLS) && !responseMLS.error) {
        yield put({
          type: 'saveCMAStates',
          payload: {
            savedMLSComps: responseMLS,
          },
        });
      } else {
        yield put({
          type: 'saveCMAStates',
          payload: {
            savedMLSComps: [],
          },
        });
      }
      const responseSFR = yield call(getSavedSFRCompsData, payload);
      if (responseSFR && Array.isArray(responseSFR) && !responseSFR.error) {
        yield put({
          type: 'saveCMAStates',
          payload: {
            savedSFRComps: responseSFR,
          },
        });
      } else {
        yield put({
          type: 'saveCMAStates',
          payload: {
            savedSFRComps: [],
          },
        });
      }
    },

    *getLeaseExpiryInsights({ payload }, { call, put, select }) {
      let result = {};
      if (payload.area === 'multipolygon') {
        const response = yield call(
          getLeaseExpiryAOIDataWithinPolygon,
          payload,
        );
        if (response) result = response;
      } else if (payload.area === 'aoi') {
        const response = yield call(getLeaseExpiryAOIData, payload);
        if (response) result = response;
      } else if (payload.area === 'zipcode') {
        const response = yield call(
          getLeaseExpiryAOIDataWithinZIPCode,
          payload,
        );
        if (response) result = response;
      }

      yield put({
        type: 'saveCMAStates',
        payload: { insightsLeaseExpiry: result },
      });
    },

    *getFilteredParcelWithinMetro({ payload }, { call, put, select, all }) {
      let response = yield call(getFilteredParcelWithinMetroData, payload);

      // let response = yield mockData;
      if (response && Object.keys(response).length > 0) {
        // const map = yield select((state) => state.CMA.map);

        // const getParcelFloodData = async (parcelArr) => {
        //   try {
        //     console.log(parcelArr);

        //     const boundaries = [];
        //     const saveLocationPosition = map.getBounds();
        //     for (let i = 0; i < parcelArr.length; i++) {
        //       const parcel = parcelArr[i];
        //       const boundary = await getParcelBoundariesFromMap(map, parcel);
        //       boundaries.push({ parcel, boundary });
        //     }
        //     map.fitBounds(saveLocationPosition, {
        //       animate: false,
        //     });

        //     for (let i = 0; i < boundaries.length; i++) {
        //       if (!boundaries[i].boundary) continue;

        //       const bounds = turf_bbox(boundaries[i].boundary);
        //       const padding = 0.001;

        //       const floodZoneData = await getFloodZoneData({
        //         lat1: bounds[2] + padding,
        //         lng1: bounds[3] + padding,
        //         lat2: bounds[0] - padding,
        //         lng2: bounds[1] - padding,
        //       });

        //       const floodGeometry = floodZoneData.reduce((acc, curr) => {
        //         if (
        //           curr.shape.coordinates.length > 0 &&
        //           curr.shape.coordinates[0].length > 0
        //         )
        //           acc.push(curr.shape);
        //         return acc;
        //       }, []);

        //       if (floodGeometry.length > 0) {
        //         const mergedFloodZone = floodGeometry.reduce((acc, curr) => {
        //           if (isEmpty(acc)) {
        //             return curr;
        //           } else {
        //             acc = turf_union(acc, curr);
        //           }
        //           return acc;
        //         }, {});

        //         const intersect = turf_intersect(
        //           boundaries[i].boundary,
        //           mergedFloodZone,
        //         );

        //         if (intersect) {
        //           const area = turf_area(intersect);
        //           const total_area = turf_area(boundaries[i].boundary);
        //           const percent = (area / total_area) * 100;

        //           // console.log('area', area);
        //           // console.log('percent', percent);
        //           boundaries[i].parcel.flood_coverage = percent;
        //         } else {
        //           boundaries[i].parcel.flood_coverage = null;
        //         }
        //       } else {
        //         boundaries[i].parcel.flood_coverage = null;
        //       }
        //     }

        //     return boundaries.map((boundary) => boundary.parcel);
        //   } catch (e) {
        //     console.log(e);
        //   }
        // };

        // const getAdditionalFilters = async (response) => {
        //   const chunks = [];
        //   let size;

        //   if (response.length / 6 > 1) {
        //     size = Math.ceil(response.length / 6);
        //   } else {
        //     size = 1;
        //   }

        //   while (response.length > 0) {
        //     chunks.push(response.splice(0, size));
        //   }

        //   const additionalFilters = await Promise.all(
        //     chunks.map((chunk) => {
        //       return getFilteredParcelAdditionalData({ body: chunk });
        //     }),
        //   );

        //   const additionalFiltersMerged = additionalFilters.reduce(
        //     (acc, curr) => {
        //       return [...acc, ...curr];
        //     },
        //     [],
        //   );
        //   return additionalFiltersMerged;
        // };

        // // Move this to backend server
        // // response = response.filter((parcel) => {
        // //   return (
        // //     parcel.standardized_land_use_type &&
        // //     !['cemetary', 'homeowners'].includes(
        // //       parcel.standardized_land_use_type.toLowerCase(),
        // //     )
        // //   );
        // // });

        // try {
        //   response = yield getAdditionalFilters(response);
        // } catch (e) {
        //   console.log(e);
        // }

        // get additional filters

        if (response.status === 200) {
          yield put({
            type: 'saveCMAStates',
            payload: {
              landDevelopmentFilteredParcelWithinMetro: response.data,
              isLoadingLandDevelopmentParcelFilterResponse: false,
            },
          });
        }
      } else {
        yield put({
          type: 'saveCMAStates',
          payload: {
            landDevelopmentFilteredParcelWithinMetro: [],
            isLoadingLandDevelopmentParcelFilterResponse: false,
          },
        });
      }
    },
  },

  reducers: {
    saveCMAStates(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },

    // not in use
    getRentAverageAll(state) {
      // console.log('getRentAverageAll payload', payload);
      let rentSumNationalOperators = 0;
      if (state.currentNationalOperatorsPropertiesFiltered.length > 0) {
        state.currentNationalOperatorsPropertiesFiltered.forEach((property) => {
          rentSumNationalOperators += +property.rent;
        });
      }

      let rentSumHotPads = 0;
      if (state.currentHotPadsPropertiesFiltered.length > 0) {
        state.currentHotPadsPropertiesFiltered.forEach((property) => {
          rentSumHotPads += +property.rent;
        });
      }

      let rentSumMLS = 0;
      if (state.currentMLSPropertiesFiltered.length > 0) {
        state.currentMLSPropertiesFiltered.forEach((property) => {
          if (property.propertytype === 'Residential Lease') {
            rentSumMLS += +property.latestPrice;
          }
        });
        console.log('rentSumMLS', rentSumMLS);
      }

      let rentAverageAll = 0;
      if (
        state.currentNationalOperatorsPropertiesFiltered.length +
          state.currentHotPadsPropertiesFiltered.length +
          state.currentMLSPropertiesFiltered.length >
        0
      ) {
        if (!isEmpty(state.subjectPropertyParcelData)) {
          rentAverageAll = (
            (rentSumMLS +
              rentSumNationalOperators +
              rentSumHotPads +
              state.subjectPropertyParcelData.rent) /
            (state.currentMLSPropertiesFiltered.length +
              state.currentNationalOperatorsPropertiesFiltered.length +
              state.currentHotPadsPropertiesFiltered.length +
              1)
          ).toFixed(0);
        } else {
          rentAverageAll = (
            (rentSumMLS + rentSumNationalOperators + rentSumHotPads) /
            (state.currentMLSPropertiesFiltered.length +
              state.currentNationalOperatorsPropertiesFiltered.length +
              state.currentHotPadsPropertiesFiltered.length)
          ).toFixed(0);
        }
      } else {
        rentAverageAll = 0;
      }

      return {
        ...state,
        rentAverageAll: rentAverageAll,
      };
    },

    filterMLSAndSFRData(state, { payload }) {
      // console.log('filterMLSAndSFRData payload', payload);
      const ranges = {
        rangeBeds: state.rangeBeds,
        rangeBaths: state.rangeBaths,
        rangeSqft: state.rangeSqft,
        rangeLotSize: state.rangeLotSize,
        rangeYearBuilt: state.rangeYearBuilt,
      };
      const getDataSource = (inputType) => {
        switch (inputType) {
          case 'MLS':
            return state.currentMLSProperties;
          case 'SFR':
            return state.currentNationalOperatorsProperties;
          case 'HotPads':
            return state.currentHotPadsProperties;
          default:
            return;
        }
      };

      const dataSource = payload.dataSource
        ? payload.dataSource
        : getDataSource(payload.dataSourceType);
      // generate first set of filter values
      // and filter data
      let currentMapboxFilter = generateSmartFilterExpression({
        subjectPropertyParcelData: state.subjectPropertyParcelData,
        cycle: 'first',
        dataSourceType: payload.dataSourceType, // MLS or SFR or HotPads
        ranges: ranges,
        cdomMinMax: {
          min: state.minCumulativeDaysOnMarket,
          max: state.maxCumulativeDaysOnMarket,
        },
        garageMinMax: {
          min: state.minCoveredParking,
          max: state.maxCoveredParking,
        },
        poolAllowed: state.selectedPoolAllowed,
      });
      let filteredDataSource = filterTableDataSource(
        currentMapboxFilter,
        dataSource, // MLS properties or SFR properties or HotPads properties
      );
      // for applying smart filter values to inputs in filter modal
      let currentFilterValues = generateSmartFilterValues(currentMapboxFilter);
      // if the number of comps is fewer than 3
      // generate a second set of more inclusive filter values
      // and filter again
      // and change rangeBeds to 1
      let smartFilterHasSecondCycle = false;
      // ATTN: no 2nd cycle for ILE
      if (
        filteredDataSource.length < 3 &&
        !payload.isCustomRange &&
        !['ILE'].includes(state.selectedUserGroup)
      ) {
        // console.log('second cycle - payload.dataSourceType', payload.dataSourceType);
        currentMapboxFilter = generateSmartFilterExpression({
          subjectPropertyParcelData: state.subjectPropertyParcelData,
          cycle: 'second',
          dataSourceType: payload.dataSourceType, // MLS or SFR or HotPads
          isCustomRange: payload.isCustomRange,
          cdomMinMax: {
            min: state.minCumulativeDaysOnMarket,
            max: state.maxCumulativeDaysOnMarket,
          },
          garageMinMax: {
            min: state.minCoveredParking,
            max: state.maxCoveredParking,
          },
          poolAllowed: state.selectedPoolAllowed,
        });
        filteredDataSource = filterTableDataSource(
          currentMapboxFilter,
          dataSource,
        );
        currentFilterValues = generateSmartFilterValues(currentMapboxFilter);
        smartFilterHasSecondCycle = true;
      }

      // Filter the data by district
      if (state.isDistrictFilterOn && state.eventCoordinates.length > 0) {
        if (state.currentSchoolDistrictProperties.length != 0) {
          filteredDataSource = filterDataBySubjectPolygon({
            subjectCoordinates: state.eventCoordinates,
            data: filteredDataSource,
            polygons: state.currentSchoolDistrictProperties,
          });
        } else {
          console.log('currentSchoolDistrictProperties is empty');
        }
      }

      // Filter the data by county
      if (state.isCountyFilterOn && state.eventCoordinates.length > 0) {
        if (state.currentCountyData.length != 0) {
          filteredDataSource = filterDataBySubjectPolygon({
            subjectCoordinates: state.eventCoordinates,
            data: filteredDataSource,
            polygons: state.currentCountyData,
          });
        } else {
          console.log('currentCountyData is empty');
        }
      }

      // Filter the data by zipcode
      if (state.isZipCodeFilterOn && state.eventCoordinates.length > 0) {
        if (state.currentZipCodeData.length != 0) {
          filteredDataSource = filterDataBySubjectPolygon({
            subjectCoordinates: state.eventCoordinates,
            data: filteredDataSource,
            polygons: state.currentZipCodeData,
          });
        } else {
          console.log('currentZipCodeData is empty');
        }
      }

      // generate geojson
      let geojsonFeatures = [];
      if (filteredDataSource && filteredDataSource.length > 0) {
        for (const property of filteredDataSource) {
          if (payload.dataSourceType === 'MLS') {
            let { geography, ...geojsonProperties } = property;
            geojsonFeatures.push({
              type: 'Feature',
              geometry: property.geography,
              properties: geojsonProperties,
            });
          } else if (['SFR', 'HotPads'].includes(payload.dataSourceType)) {
            let { geom, ...geojsonProperties } = property;
            geojsonFeatures.push({
              type: 'Feature',
              geometry: property.geom,
              properties: geojsonProperties,
            });
          }
        }
      }
      const currentGeoJSON = {
        type: 'FeatureCollection',
        features: geojsonFeatures,
      };

      // Don't save fetched properties if coordinates is 0
      if (
        state.eventCoordinates.length === 0 &&
        state.drawnCustomPolygons.length === 0
      ) {
        return state;
      }
      if (payload.dataSourceType === 'MLS') {
        // compare mlsid only instead of deep compare whole property data
        // for performance
        const tempMLSDataSource =
          state.searchingMode === 'Lease'
            ? state.tempMLSPropertiesFilteredLease
            : state.tempMLSPropertiesFilteredSales;
        const tempMLSDataSourceMLSIdArray = tempMLSDataSource.map(
          (property) => property.mlsid,
        );
        const filteredDataSourceMLSIdArray = filteredDataSource.map(
          (property) => property.mlsid,
        );
        // keep user row selections after lease/sales mode switch
        if (
          ['to lease mode', 'to sales mode'].includes(payload.mode) &&
          isEqual(tempMLSDataSourceMLSIdArray, filteredDataSourceMLSIdArray)
        ) {
          console.log('equal');
          return {
            ...state,
            ...currentFilterValues,
            currentMLSPropertiesFiltered: filteredDataSource,
            currentMLSGeoJSON: currentGeoJSON,
            ...(smartFilterHasSecondCycle && { rangeBeds: 1 }),
          };
        } else {
          const selectAllMLSRows = filteredDataSource.map(
            (property) => property.mlsid,
          );
          return {
            ...state,
            ...currentFilterValues,
            ...(state.searchingMode === 'Lease'
              ? {
                  selectedRowKeysMLSLease: selectAllMLSRows,
                  tempMLSPropertiesFilteredLease: filteredDataSource,
                }
              : {
                  selectedRowKeysMLSSale: selectAllMLSRows,
                  tempMLSPropertiesFilteredSales: filteredDataSource,
                }),
            currentMLSPropertiesFiltered: filteredDataSource,
            currentMLSGeoJSON: currentGeoJSON,
            ...(smartFilterHasSecondCycle && { rangeBeds: 1 }),
          };
        }
      } else if (payload.dataSourceType === 'SFR') {
        // keep user row selections after lease/sales mode switch
        if (['to lease mode', 'to sales mode'].includes(payload.mode)) {
          let selectedRowKeysNationalOperators =
            state.selectedRowKeysNationalOperators;
          if (state.firstTimeFetchSFR) {
            selectedRowKeysNationalOperators = filteredDataSource.map(
              (property) => property.base_id,
            );
          }

          return {
            ...state,
            currentNationalOperatorsPropertiesFiltered: filteredDataSource,
            currentNationalOperatorsGeoJSON: currentGeoJSON,
            selectedRowKeysNationalOperators: selectedRowKeysNationalOperators,
            firstTimeFetchSFR: false,
          };
        } else {
          const selectAllSFRRows = filteredDataSource.map(
            (property) => property.base_id,
          );
          return {
            ...state,
            currentNationalOperatorsPropertiesFiltered: filteredDataSource,
            currentNationalOperatorsGeoJSON: currentGeoJSON,
            selectedRowKeysNationalOperators: selectAllSFRRows,
            firstTimeFetchSFR: false,
          };
        }
      } else if (payload.dataSourceType === 'HotPads') {
        // keep user row selections after lease/sales mode switch
        if (['to lease mode', 'to sales mode'].includes(payload.mode)) {
          let selectedRowKeysHotPads = state.selectedRowKeysHotPads;
          if (state.firstTimeFetchHotPads) {
            selectedRowKeysHotPads = filteredDataSource.map(
              (property) => property.base_id,
            );
          }

          return {
            ...state,
            currentHotPadsPropertiesFiltered: filteredDataSource,
            currentHotPadsGeoJSON: currentGeoJSON,
            selectedRowKeysHotPads: selectedRowKeysHotPads,
            firstTimeFetchHotPads: false,
          };
        } else {
          const selectAllHotPadsRows = filteredDataSource.map(
            (property) => property.base_id,
          );
          return {
            ...state,
            currentHotPadsPropertiesFiltered: filteredDataSource,
            currentHotPadsGeoJSON: currentGeoJSON,
            selectedRowKeysHotPads: selectAllHotPadsRows,
            firstTimeFetchHotPads: false,
          };
        }
      }
    },

    filterMLSAndSFRDataManual(state, { payload }) {
      console.log('filterMLSAndSFRDataManual payload', payload);
      const getDataSource = (inputType) => {
        switch (inputType) {
          case 'MLS':
            return state.currentMLSProperties;
          case 'SFR':
            return state.currentNationalOperatorsProperties;
          case 'HotPads':
            return state.currentHotPadsProperties;
          default:
            return;
        }
      };
      const dataSource = payload.dataSource
        ? payload.dataSource
        : getDataSource(payload.dataSourceType);
      // generate filter expression from manual filter values
      const generateManualFilterValues = (dataSourceType) => {
        const baseFilters = [
          {
            key: dataSourceType === 'MLS' ? 'bed' : 'bed_rooms',
            // name: 'Beds',
            min: state.minBeds,
            max: state.maxBeds,
            relation: state.relationBeds,
          },
          {
            key: dataSourceType === 'MLS' ? 'bath' : 'bath_rooms',
            // name: 'Baths',
            min: state.minBaths,
            max: state.maxBaths,
            relation: state.relationBaths,
          },
          {
            key: dataSourceType === 'MLS' ? 'size' : 'square_feet',
            // name: 'Sqft',
            min: state.minSqft,
            max: state.maxSqft,
            relation: state.relationSqft,
          },
          {
            key: 'area_acres', // MLS comps' lot_size have been converted to area_acres in processMLSProperties in processAPIResponses.js
            // name: 'Lot Size',
            min: state.minLotSize,
            max: state.maxLotSize,
            relation: state.relationLotSize,
          },
          {
            key: 'yearbuilt',
            // name: 'Year Built',
            min: state.minYearBuilt,
            max: state.maxYearBuilt,
            relation: state.relationYearBuilt,
          },
          {
            key: 'cdom',
            // name: 'Days on Market',
            min: state.minCumulativeDaysOnMarket,
            max: state.maxCumulativeDaysOnMarket,
            relation: state.relationCumulativeDaysOnMarket,
          },
          {
            key: 'garage',
            min: state.minCoveredParking,
            max: state.maxCoveredParking,
            relation: state.relationCoveredParking,
          },
          {
            key: 'pool',
            selected: state.selectedPoolAllowed,
          },
          {
            key: dataSourceType === 'MLS' ? 'latestPrice' : 'rent',
            min:
              state.searchingMode === 'Lease'
                ? state.minRentPrice
                : state.minSoldPrice,
            max:
              state.searchingMode === 'Lease'
                ? state.maxRentPrice
                : state.maxSoldPrice,
            relation:
              state.searchingMode === 'Lease'
                ? state.relationRentPrice
                : state.relationSoldPrice,
          },
        ];

        // if (dataSourceType === 'MLS') {
        //   baseFilters.push({
        //     key: 'propertysubtype',
        //     checked: state.checkedPropertySubTypes,
        //   });
        // }
        return baseFilters;
      };

      let currentMapboxFilters = generateManualFilterExpression(
        generateManualFilterValues(payload.dataSourceType),
      );
      console.log('currentMapboxFilters', currentMapboxFilters);
      let filteredDataSource = filterTableDataSource(
        currentMapboxFilters,
        dataSource,
      );

      // filter new construction only
      if (
        state.searchingMode === 'Sale' &&
        payload.dataSourceType === 'MLS' &&
        state.mlsSaleNewConstructionOnly
      ) {
        filteredDataSource = filteredDataSource.filter(
          (property) => property.new_construction,
        );
      }

      // Filter the data by district
      if (state.isDistrictFilterOn && state.eventCoordinates.length > 0) {
        if (state.currentSchoolDistrictProperties.length != 0) {
          filteredDataSource = filterDataBySubjectPolygon({
            subjectCoordinates: state.eventCoordinates,
            data: filteredDataSource,
            polygons: state.currentSchoolDistrictProperties,
          });
        } else {
          console.log('currentSchoolDistrictProperties is empty');
        }
      }

      // Filter the data by county
      if (state.isCountyFilterOn && state.eventCoordinates.length > 0) {
        if (state.currentCountyData.length != 0) {
          filteredDataSource = filterDataBySubjectPolygon({
            subjectCoordinates: state.eventCoordinates,
            data: filteredDataSource,
            polygons: state.currentCountyData,
          });
        } else {
          console.log('currentCountyData is empty');
        }
      }

      // Filter the data by zipcode
      if (state.isZipCodeFilterOn && state.eventCoordinates.length > 0) {
        if (state.currentZipCodeData.length != 0) {
          filteredDataSource = filterDataBySubjectPolygon({
            subjectCoordinates: state.eventCoordinates,
            data: filteredDataSource,
            polygons: state.currentZipCodeData,
          });
        } else {
          console.log('currentZipCodeData is empty');
        }
      }

      console.log('filteredDataSource', filteredDataSource);
      // generate geojson
      let geojsonFeatures = [];
      if (filteredDataSource && filteredDataSource.length > 0) {
        for (const property of filteredDataSource) {
          if (payload.dataSourceType === 'MLS') {
            let { geography, ...geojsonProperties } = property;
            geojsonFeatures.push({
              type: 'Feature',
              geometry: property.geography,
              properties: geojsonProperties,
            });
          } else if (['SFR', 'HotPads'].includes(payload.dataSourceType)) {
            let { geom, ...geojsonProperties } = property;
            geojsonFeatures.push({
              type: 'Feature',
              geometry: property.geom,
              properties: geojsonProperties,
            });
          }
        }
      }
      const currentGeoJSON = {
        type: 'FeatureCollection',
        features: geojsonFeatures,
      };

      // Don't save fetched properties if coordinates is 0
      if (
        state.eventCoordinates.length === 0 &&
        state.drawnCustomPolygons.length === 0
      ) {
        return state;
      }

      if (payload.dataSourceType === 'MLS') {
        // compare mlsid only instead of deep compare whole property data
        // for performance
        const tempMLSDataSource =
          state.searchingMode === 'Lease'
            ? state.tempMLSPropertiesFilteredLease
            : state.tempMLSPropertiesFilteredSales;
        const tempMLSDataSourceMLSIdArray = tempMLSDataSource.map(
          (property) => property.mlsid,
        );
        const filteredDataSourceMLSIdArray = filteredDataSource.map(
          (property) => property.mlsid,
        );
        // keep user row selections after lease/sales mode switch
        if (
          ['to lease mode', 'to sales mode'].includes(payload.mode) &&
          isEqual(tempMLSDataSourceMLSIdArray, filteredDataSourceMLSIdArray)
        ) {
          // console.log('equal');
          return {
            ...state,
            currentMLSPropertiesFiltered: filteredDataSource,
            currentMLSGeoJSON: currentGeoJSON,
          };
        } else {
          const selectAllMLSRows = filteredDataSource.map(
            (property) => property.mlsid,
          );
          return {
            ...state,
            ...(state.searchingMode === 'Lease'
              ? {
                  selectedRowKeysMLSLease: selectAllMLSRows,
                  tempMLSPropertiesFilteredLease: filteredDataSource,
                }
              : {
                  selectedRowKeysMLSSale: selectAllMLSRows,
                  tempMLSPropertiesFilteredSales: filteredDataSource,
                }),
            currentMLSPropertiesFiltered: filteredDataSource,
            currentMLSGeoJSON: currentGeoJSON,
          };
        }
      } else if (payload.dataSourceType === 'SFR') {
        // keep user row selections after lease/sales mode switch
        if (['to lease mode', 'to sales mode'].includes(payload.mode)) {
          let selectedRowKeysNationalOperators =
            state.selectedRowKeysNationalOperators;
          if (state.firstTimeFetchSFR) {
            selectedRowKeysNationalOperators = filteredDataSource.map(
              (property) => property.base_id,
            );
          }
          return {
            ...state,
            currentNationalOperatorsPropertiesFiltered: filteredDataSource,
            currentNationalOperatorsGeoJSON: currentGeoJSON,
            selectedRowKeysNationalOperators: selectedRowKeysNationalOperators,
            firstTimeFetchSFR: false,
          };
        } else {
          const selectAllSFRRows = filteredDataSource.map(
            (property) => property.base_id,
          );
          return {
            ...state,
            currentNationalOperatorsPropertiesFiltered: filteredDataSource,
            currentNationalOperatorsGeoJSON: currentGeoJSON,
            selectedRowKeysNationalOperators: selectAllSFRRows,
            firstTimeFetchSFR: false,
          };
        }
      } else if (payload.dataSourceType === 'HotPads') {
        // keep user row selections after lease/sales mode switch
        if (['to lease mode', 'to sales mode'].includes(payload.mode)) {
          let selectedRowKeysHotPads = state.selectedRowKeysHotPads;
          if (state.firstTimeFetchHotPads) {
            selectedRowKeysHotPads = filteredDataSource.map(
              (property) => property.base_id,
            );
          }
          return {
            ...state,
            currentHotPadsPropertiesFiltered: filteredDataSource,
            currentHotPadsGeoJSON: currentGeoJSON,
            selectedRowKeysHotPads: selectedRowKeysHotPads,
            firstTimeFetchHotPads: false,
          };
        } else {
          const selectAllHotPadsRows = filteredDataSource.map(
            (property) => property.base_id,
          );
          return {
            ...state,
            currentHotPadsPropertiesFiltered: filteredDataSource,
            currentHotPadsGeoJSON: currentGeoJSON,
            selectedRowKeysHotPads: selectAllHotPadsRows,
            firstTimeFetchHotPads: false,
          };
        }
      }
    },

    // only use this function to change filter values
    // so that we can locate where filter values are changed when debugging
    updateFilterValues(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
