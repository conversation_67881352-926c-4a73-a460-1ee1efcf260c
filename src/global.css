/* umi.js global style override */
@import url('./assets/fonts/IBM-Plex/css/ibm-plex-latin1.css');
@import 'antd/dist/reset.css';

html, body { 
  margin: 0; 
  height: 100%; 
  overflow: hidden;
}

body {
  margin: 0;
  font-family: 'IBM Plex Sans', <PERSON><PERSON>, 'sans-serif' !important;
  /* min-width: 1070px; */
  /* min-width: 1250px; */
  min-height: 796px;
}

#headerSearchWrapper
  .ant-select-show-search.ant-select:not(.ant-select-customize-input)
  .ant-select-selector,
#headerSearchWrapper .ant-input {
  height: 32px;
  /* border-color: var(--color-BT-blue-50); */
  border-color: rgba(0, 0, 0, 0 / 0.2);
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
  padding: 0px 12px;
}

#headerSearchWrapper
  .SearchInputZeroCornerRadius.ant-select-show-search.ant-select:not(
    .ant-select-customize-input
  )
  .ant-select-selector {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

#headerAddressSearchInput,
#AutoCompleteSubdivisionName,
#AutoCompleteSubdivisionCity,
#AutoCompleteSubdivisionZIPCode {
  /* margin-top: 5px; */
}

#searchInputWrapper > .ant-input-group > .ant-select-auto-complete > .ant-select-selector {
  min-width: 0px;
}

#searchInputWrapper > .ant-input-group > .ant-select-auto-complete > .ant-select-selector > .ant-select-selection-placeholder {
  min-width: 0px;
}

#AutoCompleteSubdivisionName,
#AutoCompleteSubdivisionCity {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

#headerSearchWrapper
  .ant-select.ant-select-single.ant-select-show-arrow
  .ant-select-selector {
  height: 32px;
  /* border-color: var(--color-BT-blue-50); */
  border-color: rgba(0, 0, 0, 0 / 0.2);
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
  padding: 0px 12px;
}

#leaseSaleModeRadioButtonWrapper {
  display: flex;
  width: fit-content;
}

#leaseSaleModeRadioButtonWrapper > .ant-radio-button-wrapper {
  /* width: 80px; */
  height: auto;
  line-height: 1.5;
  color: var(--color-BT-blue);
  padding: 6px 16px;
  border: none;
  border-radius: 0;
}

#leaseSaleModeRadioButtonWrapper.ant-radio-group-solid
  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
  background-color: var(--color-BT-blue);
  color: #fff;
}

.carousel.carousel-slider .control-arrow,
.carousel .control-arrow {
  background-color: rgba(0, 0, 0, 0.5);
}

.carousel .thumb {
  height: 125px;
}
#radiusSelectWrapper .ant-select-selector {
  border: none;
  color: var(--antd-active-blue);
  background-color: rgba(0, 0, 0, 0);
}

#result-container thead.ant-table-thead {
  font-size: 12px;
}

#result-container tfoot td.ant-table-cell {
  font-size: 13px;
  font-weight: 500;
}

button:disabled#newHouseAVMButton {
  color: rgba(0, 0, 0, 0.25);
  background-color: rgba(0, 0, 0, 0);
  border: none;
  padding: 0;
}

#modeSelectorWrapper
  .ant-select:not(.ant-select-customize-input)
  .ant-select-selector {
  /* font-size: 12px; */
  border: none;
  background-color: rgba(0, 0, 0, 0);
  color: var(--antd-active-blue);
}

#modeSelectorWrapper .ant-select-arrow {
  color: var(--antd-active-blue);
}

#filtersWrapper fieldset {
  width: 100%;
}

#filtersWrapper .ant-select-selector {
  font-size: 12px;
  color: var(--antd-active-blue);
}

#filtersWrapper .ant-select-selector .ant-select-selection-item {
  font-weight: 500;
}

#filtersWrapper .ant-checkbox-wrapper {
  font-size: 12px;
}

/* remove active bar in date range picker */
#filtersWrapper .ant-picker-range .ant-picker-active-bar {
  /* background-color: transparent; */
  display: none;
}

/* set date range picker input font size to 12 and color to --antd-active-blue */
#filtersWrapper .ant-picker-range .ant-picker-input > input {
  font-size: 12px;
  font-weight: 500;
  color: var(--antd-active-blue);
}

/* set number input font size to 12 and color to --antd-active-blue and font weight to 500 */
#filtersWrapper
  .ant-input-number-group-wrapper
  .ant-input-number-input-wrap
  > input,
#filtersWrapper
  .ant-input-number-group-wrapper
  .ant-input-number-wrapper
  .ant-input-number-group-addon {
  font-size: 12px;
  font-weight: 500;
  color: var(--antd-active-blue);
  border: none;
  background-color: rgba(0, 0, 0, 0);
}

/* set suffix color to gray */
#filtersWrapper
  .ant-input-number-group-wrapper
  .ant-input-number-wrapper
  .ant-input-number-group-addon {
  color: #333;
}

/* disabled state */
#filtersWrapper .ant-select-disabled .ant-select-selector,
#filtersWrapper .ant-picker-disabled .ant-picker-input > input,
#filtersWrapper .ant-input-number-disabled input.ant-input-number-input {
  color: rgba(0, 0, 0, 0.25);
}

#MLSImageModalTabs .ant-tabs-content-holder {
  overflow-y: auto;
}

#result-container .ant-spin-nested-loading,
#result-container .ant-spin-container {
  position: static;
}

#mapContainer {
  /* border-radius: 8px !important; */
}

.image-detail-tooltip-container .ant-tooltip-arrow-content {
  --antd-arrow-background-color: white;
  background-color: white !important;
}
.image-detail-tooltip-container .ant-tooltip-inner {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 200px;
  height: 150px;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}
.image-detail-tooltip-container .ant-tooltip-inner {
  padding: 0;
}
.image-detail-tooltip-container img {
  width: 100%;
  height: 100%;
  object-fit: fill;
}

#land-development-parcel-filter-wrapper .ant-form-item {
  margin-bottom: 0;
}

#land-development-parcel-filter-wrapper .ant-col.ant-form-item-label,
#land-development-parcel-filter-wrapper .ant-col.ant-form-item-control {
  text-align: center;
}

#land-development-parcel-filter-table .ant-table-body {
  min-height: 380px;
}

#bfrTableContainer .ant-table-body {
  font-size: 12px;
  font-weight: 450;
}

.landTableRowSelect {
  background-color: rgba(144, 208, 234, 0.8) !important;
  border-color: rgba(144, 208, 234, 0.8) !important;
}

/* CSS for printing - hides map and shows tables section only */
@media print {
  #root {
    /* Disable css print as we are using pdf library */
    display: none !important;
  }
  body {
    print-color-adjust: exact !important;
    -webkit-print-color-adjust: exact !important;
  }
  div.ant-table.ant-table-small {
    font-size: 12px !important;
  }
  #print-tooltip {
    display: none !important;
  }
  #map-container {
    display: none !important;
  }
  #result-container {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100% !important;
    height: 100% !important;
  }
  #resultTableWrapper,
  #resultTableScrollWrapper {
    overflow: visible !important;
    padding: 0 !important;
  }
}

.mosaic-window-body {
  overflow: auto !important;
}

.landDev-input-number .ant-input-number-handler-wrap {
  opacity: 1 !important;
}

.invisible-input {
  border: none !important;
}
.invisible-input:focus {
  outline: none !important;
  box-shadow: none !important;
}

#land-parcel-search .ant-collapse-content-box {
  padding: 0 !important;
}

#land-parcel-search .ant-collapse,
#land-parcel-search .ant-collapse-item {
  border: 0 !important;
}

.table-sticky-title-header .ant-table-header table {
  border: none !important;
}
.table-sticky-title-header .ant-table-header table thead tr {
  border: 1px solid #f0f0f0 !important;
}

/* for CMA filter shortcuts */
#CMARangeShortcutWrapper .ant-radio-button-wrapper {
  font-size: 12px;
  /* font-weight: 500; */
  /* color: var(--antd-active-blue); */
  background: rgba(255, 255, 255, 0.1);
  border-width: 1px;
  border-radius: 8px;
}

#CMARangeShortcutWrapper .ant-radio-button-wrapper.ant-radio-button-wrapper-checked {
  background: var(--antd-active-blue);
  color: #fff;
}

#CMARangeShortcutWrapper .ant-radio-button-wrapper:not(:first-child)::before {
  display: none;
}

#filtersWrapper .ant-input-number .ant-input-number-input {
  font-size: 12px;
  font-weight: 500;
  color: var(--antd-active-blue);
  /* width: 60px; */
}

#filtersWrapper .ant-input-number {
  width: 80px;
}

#root .ant-table-wrapper .ant-table-column-title {
  min-width: auto;
}