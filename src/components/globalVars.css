/* @import url('../assets/fonts/IBM-Plex/css/ibm-plex-latin1.css'); */

:root {
  --map-padding: 8px;
  --card-border-radius: 8px;
  --logo-height: 36px;
  /* --header-margin-bottom: 8px; */
  /* --color-deep-blue: rgb(32, 71, 122); */
  --color-LocateAlpha-green: #007e3f;
  --color-BT-blue: rgb(22, 70, 133);
  --color-BT-blue-50: rgba(22, 70, 133, 0.5);
  --antd-active-blue: #1890ff;
}

body {
  /* font-family: 'IBM Plex Sans' !important; */
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

:global(.flexCenter) {
  display: flex;
  align-items: center;
  justify-content: center;
}

#CMA_TAB {
  height: 100%;
  overflow: hidden;
}

#CMA_TAB .ant-tabs-content-holder {
  /* overflow-y: auto; */
  overflow-y: hidden;
  height: 100%;
}

#CMA_TAB .ant-tabs-content-holder .ant-tabs-content.ant-tabs-content-top {
  height: 100%;
}

#CMA_TAB-panel-1,
#CMA_TAB-panel-2,
#CMA_TAB-panel-3,
#CMA_TAB-panel-4,
#CMA_TAB-panel-5,
#CMA_TAB-panel-6,
#CMA_TAB-panel-7,
#CMA_TAB-panel-Comp\ Insights,
#CMA_TAB-panel-8 {
  height: 100%;
}
#CMA_TAB-panel-9 {
  height: 100%;
}

#listing-card-table .ant-table-thead .ant-table-cell,
#fairmarketrent-table .ant-table-thead .ant-table-cell {
  padding: 5px 10px;
  text-align: center;
}

#listing-card-table .ant-table-tbody .ant-table-cell,
#fairmarketrent-table .ant-table-tbody .ant-table-cell {
  padding: 5px 10px;
  text-align: center;
}

#favorable-brands-table .ant-table-thead .ant-table-cell,
#favorable-brands-table .ant-table-tbody .ant-table-cell {
  padding: 5px 10px !important;
}

#scorecard_progress .ant-progress-inner {
  width: 100% !important;
  height: 100% !important;
}
