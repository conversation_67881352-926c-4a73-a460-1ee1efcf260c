import { serverType } from '@/services/data';
import { getUserToken } from '@/utils/auth';
import { useEffect, useState } from 'react';
import { useSelector } from 'umi';
import BadgeWrapper from './HeaderBar/BadgeWrapper';

const CluesoLink = () => {
  const [token, setToken] = useState('');
  const currentMapLayerOptions = useSelector(
    (state: any) => state.CMA.currentMapLayerOptions,
  );
  const fetchToken = async () => {
    const accessToken = await getUserToken('access');
    const response = await fetch(`/api/sbs/${serverType}/api/v1/clueso-token`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });
    const data = await response.json();
    if (data && data?.token) {
      setToken(data.token);
    } else {
      setToken('');
    }
  };

  useEffect(() => {
    if (!token) {
      fetchToken();
    }
  }, []);

  const baseURL = `https://locate-alpha.clueso.io/`;

  return (
    <BadgeWrapper currentMapLayerOptions={[]}>
      <span className="text-sm font-medium leading-normal text-BT-blue">
        <a
          href={`${baseURL}?token=${token}`}
          target="_blank"
          rel="noopener noreferrer"
          className="text-BT-blue font-medium"
        >
          Help
        </a>
      </span>
    </BadgeWrapper>
  );
};

export default CluesoLink;
