import {
  DeleteOutlined,
  FilterOutlined,
  SaveOutlined,
} from '@ant-design/icons';
import { Button, Col, Input, message, Modal, Row, Select, Tooltip } from 'antd';
import isEmpty from 'lodash/isEmpty';
import isEqual from 'lodash/isEqual';
import React, { ReactNode, useEffect, useState } from 'react';
import { useDispatch, useSearchParams, useSelector } from 'umi';
import type {
  createNewFilterDataParams,
  deleteSavedFilterDataParams,
  updateSavedFilterDataParams,
} from '../../services/data';
import {
  createNewFilterData,
  deleteSavedFilterData,
  getSavedFiltersData,
  updateSavedFilterData,
} from '../../services/data';
import SavedFiltersSaveModal from './SavedFiltersSaveModal';

const getPayloadMode = (compingMode: string): string => {
  switch (compingMode) {
    case 'noFilter':
      if (document.querySelector('#radiusSelectWrapper')?.style?.display) {
        document.querySelector('#radiusSelectWrapper').style.display = 'flex';
      }
      return 'switch to no filter';
    case 'smartFilter':
      if (document.querySelector('#radiusSelectWrapper')?.style?.display) {
        document.querySelector('#radiusSelectWrapper').style.display = 'flex';
      }
      return 'switch to smart filter';
    case 'intelligentComping':
      if (document.querySelector('#radiusSelectWrapper')?.style?.display) {
        document.querySelector('#radiusSelectWrapper').style.display = 'none';
      }
      return 'switch to intelligent comping';
    case 'preset-venture':
    case 'preset-svn':
    case 'preset-sunroom':
      if (document.querySelector('#radiusSelectWrapper')) {
        document.querySelector('#radiusSelectWrapper').style.display = 'flex';
      }
      return 'switch to preset';
    case 'BTRFilter':
      if (document.querySelector('#radiusSelectWrapper')) {
        document.querySelector('#radiusSelectWrapper').style.display = 'flex';
      }
      return 'switch to BTRFilter';
    default:
      return '';
  }
};

const SavedFilters = (props: any) => {
  const minRentPrice = useSelector((state: any) => state.CMA.minRentPrice);
  const maxRentPrice = useSelector((state: any) => state.CMA.maxRentPrice);
  const minSoldPrice = useSelector((state: any) => state.CMA.minSoldPrice);
  const maxSoldPrice = useSelector((state: any) => state.CMA.maxSoldPrice);
  const minBeds = useSelector((state: any) => state.CMA.minBeds);
  const maxBeds = useSelector((state: any) => state.CMA.maxBeds);
  const minBaths = useSelector((state: any) => state.CMA.minBaths);
  const maxBaths = useSelector((state: any) => state.CMA.maxBaths);
  const minSqft = useSelector((state: any) => state.CMA.minSqft);
  const maxSqft = useSelector((state: any) => state.CMA.maxSqft);
  const minLotSize = useSelector((state: any) => state.CMA.minLotSize);
  const maxLotSize = useSelector((state: any) => state.CMA.maxLotSize);
  const minYearBuilt = useSelector((state: any) => state.CMA.minYearBuilt);
  const maxYearBuilt = useSelector((state: any) => state.CMA.maxYearBuilt);
  const minCumulativeDaysOnMarket = useSelector(
    (state: any) => state.CMA.minCumulativeDaysOnMarket,
  );
  const maxCumulativeDaysOnMarket = useSelector(
    (state: any) => state.CMA.maxCumulativeDaysOnMarket,
  );
  const minCoveredParking = useSelector(
    (state: any) => state.CMA.minCoveredParking,
  );
  const maxCoveredParking = useSelector(
    (state: any) => state.CMA.maxCoveredParking,
  );
  const selectedPoolAllowed = useSelector(
    (state: any) => state.CMA.selectedPoolAllowed,
  );
  const relationBeds = useSelector((state: any) => state.CMA.relationBeds);
  const relationBaths = useSelector((state: any) => state.CMA.relationBaths);
  const relationSqft = useSelector((state: any) => state.CMA.relationSqft);
  const relationLotSize = useSelector(
    (state: any) => state.CMA.relationLotSize,
  );
  const relationYearBuilt = useSelector(
    (state: any) => state.CMA.relationYrBuilt,
  );
  const relationCumulativeDaysOnMarket = useSelector(
    (state: any) => state.CMA.relationCumulativeDaysOnMarket,
  );
  const relationCoveredParking = useSelector(
    (state: any) => state.CMA.relationCoveredParking,
  );
  const rangeBeds = useSelector((state: any) => state.CMA.rangeBeds);
  const rangeBaths = useSelector((state: any) => state.CMA.rangeBaths);
  const rangeSqft = useSelector((state: any) => state.CMA.rangeSqft);
  const rangeLotSize = useSelector((state: any) => state.CMA.rangeLotSize);
  const rangeYearBuilt = useSelector((state: any) => state.CMA.rangeYearBuilt);
  const rangeClosingDate = useSelector(
    (state: any) => state.CMA.rangeClosingDate,
  );

  const currentStatusMLS = useSelector(
    (state: any) => state.CMA.currentStatusMLS,
  );
  const closedDateWithinOrBetween = useSelector(
    (state: any) => state.CMA.closedDateWithinOrBetween,
  );
  const currentStartMLS = useSelector(
    (state: any) => state.CMA.currentStartMLS,
  );
  const currentEndMLS = useSelector((state: any) => state.CMA.currentEndMLS);
  const isDistrictFilterOn = useSelector(
    (state: any) => state.CMA.isDistrictFilterOn,
  );
  const isCountyFilterOn = useSelector(
    (state: any) => state.CMA.isCountyFilterOn,
  );
  const isZipCodeFilterOn = useSelector(
    (state: any) => state.CMA.isZipCodeFilterOn,
  );

  // const isLeaseMode = useSelector((state: any) => state.CMA.isLeaseMode);
  const searchingMode = useSelector((state: any) => state.CMA.searchingMode);
  const compingMode = useSelector((state: any) => state.CMA.compingMode);
  const marketRentPreference = useSelector(
    (state: any) => state.CMA.marketRentPreference,
  );
  const currentRadiusMile = useSelector(
    (state: any) => state.CMA.currentRadiusMile,
  );

  const eventCoordinates = useSelector(
    (state: any) => state.CMA.eventCoordinates,
  );
  const expDateFilterOn = useSelector(
    (state: any) => state.CMA.expDateFilterOn,
  );

  const map = useSelector((state: any) => state.CMA.map);

  const [selectedFilterId, setSelectedFilterId] = useState<number | null>(null);
  const [allFilters, setAllFilters] = useState<any>([]);
  const [filterName, setFilterName] = useState<string>('');
  const [openModal, setOpenModal] = useState<boolean>(false);

  const dispatch = useDispatch();

  // add a search param to the url when a filter is selected
  // in order to avoid triggering onFilterReset() in Filters2.js
  const [searchParams, setSearchParams] = useSearchParams();

  const getSavedFilters = async () => {
    const response = await getSavedFiltersData();
    if (response) {
      console.log('getSavedFilters:', response);
      setAllFilters(response);
    }
  };

  const createNewFilter = async (params: createNewFilterDataParams) => {
    const response = await createNewFilterData(params);
    if (response) {
      getSavedFilters();
      message.success('Filter saved successfully');
    }
  };

  const updateFilter = async (params: updateSavedFilterDataParams) => {
    console.log('update filter params:', params);
    const response = await updateSavedFilterData(params);
    if (response) {
      getSavedFilters();
      message.success('Filter updated successfully');
    }
  };

  const deleteFilter = async (params: deleteSavedFilterDataParams) => {
    const response = await deleteSavedFilterData(params);
    if (response) {
      getSavedFilters();
      message.success('Filter deleted successfully');
    }
  };

  const onSelectFilter = (value: number) => {
    setSelectedFilterId(value);
    const selectedFilter = allFilters.find(
      (filter: any) => filter.id === value,
    );
    if (
      selectedFilter &&
      selectedFilter?.filters &&
      !isEmpty(selectedFilter?.filters)
    ) {
      const selectedFilterValues = JSON.parse(selectedFilter?.filters);
      // console.log('selectedFilterValues:', selectedFilterValues);
      const { currentStatusMLS, isLeaseMode, compingMode, currentRadiusMile } =
        selectedFilterValues;
      // older saved filters have isLeaseMode
      // newer saved filters have searchingMode
      if (typeof isLeaseMode === 'boolean' && isLeaseMode === true) {
        selectedFilterValues.searchingMode = 'Lease';
      } else if (typeof isLeaseMode === 'boolean' && isLeaseMode === false) {
        selectedFilterValues.searchingMode = 'Sale';
      }
      // apply saved filter values
      dispatch({
        type: 'CMA/updateFilterValues',
        payload: selectedFilterValues,
      });
      // fetch comps
      if (eventCoordinates.length === 2) {
        dispatch({
          type: 'CMA/getAllPropertyData',
          payload: {
            mode: getPayloadMode(compingMode),
            lng: eventCoordinates[0],
            lat: eventCoordinates[1],
            status: currentStatusMLS,
            propertyType:
              selectedFilterValues.searchingMode === 'Lease'
                ? 'Residential Lease'
                : 'Residential',
            exists: currentStatusMLS,
            expDateFilterOn: expDateFilterOn ? 'yes' : 'no',
          },
        });
      }
      // set radius on map
      if (currentRadiusMile && !isNaN(currentRadiusMile)) {
        map.fire('selectRadius.setRadius', {
          payload: { currentRadiusMile: +currentRadiusMile },
        });
      }
      setFilterName(selectedFilter?.name);
      console.log('current saved filter:', JSON.parse(selectedFilter?.filters));
      // add a search param to the url when a filter is selected
      // in order to avoid triggering onFilterReset() in Filters2.js
      searchParams.set('selectedFilter', selectedFilter?.name);
      setSearchParams(searchParams);
    }
  };

  const getFilterData = (compingMode: string) => {
    switch (compingMode) {
      case 'intelligentComping':
        return {
          compingMode,
          searchingMode,
        };
      case 'smartFilter':
        return {
          minRentPrice,
          maxRentPrice,
          minSoldPrice,
          maxSoldPrice,
          rangeBeds,
          rangeBaths,
          rangeSqft,
          rangeLotSize,
          rangeYearBuilt,
          rangeClosingDate,
          minCoveredParking,
          maxCoveredParking,
          selectedPoolAllowed,
          relationBeds,
          relationBaths,
          relationSqft,
          relationLotSize,
          relationYearBuilt,
          relationCumulativeDaysOnMarket,
          relationCoveredParking,
          currentStatusMLS,
          closedDateWithinOrBetween,
          currentStartMLS,
          currentEndMLS,
          isDistrictFilterOn,
          isCountyFilterOn,
          isZipCodeFilterOn,
          searchingMode,
          compingMode,
          marketRentPreference,
          currentRadiusMile,
        };
      case 'noFilter':
      default:
        return {
          minRentPrice,
          maxRentPrice,
          minSoldPrice,
          maxSoldPrice,
          minBeds,
          maxBeds,
          minBaths,
          maxBaths,
          minSqft,
          maxSqft,
          minLotSize,
          maxLotSize,
          minYearBuilt,
          maxYearBuilt,
          minCumulativeDaysOnMarket,
          maxCumulativeDaysOnMarket,
          minCoveredParking,
          maxCoveredParking,
          selectedPoolAllowed,
          relationBeds,
          relationBaths,
          relationSqft,
          relationLotSize,
          relationYearBuilt,
          relationCumulativeDaysOnMarket,
          relationCoveredParking,
          currentStatusMLS,
          closedDateWithinOrBetween,
          currentStartMLS,
          currentEndMLS,
          isDistrictFilterOn,
          isCountyFilterOn,
          isZipCodeFilterOn,
          searchingMode,
          compingMode,
          marketRentPreference,
          currentRadiusMile,
        };
    }
  };

  // check if input name is empty or already exists
  const checkFilterNameInput = (nameInput: string) => {
    if (!nameInput) {
      return 'Please enter a name for the filter';
    } else if (allFilters.find((filter: any) => filter.name === nameInput)) {
      return 'Filter name already exists, please enter a different name';
    } else {
      return '';
    }
  };

  // NOT WORKING YET
  // fetch all saved filters
  // get name of current selected filter
  // for display
  const refreshFilterDisplayData = async () => {
    await getSavedFilters();
    // select the first filter by default
    if (!selectedFilterId && allFilters.length > 0) {
      console.log('allFilters:', allFilters);
      onSelectFilter(allFilters[0]?.id);
    }
    const selectedFilterName = allFilters.find(
      (filter: any) => filter.id === selectedFilterId,
    )?.name;
    if (selectedFilterName) {
      setFilterName(selectedFilterName);
    }
  };

  useEffect(() => {
    getSavedFilters();
  }, []);

  return (
    <>
      <SavedFiltersSaveModal
        open={openModal}
        onCancel={() => setOpenModal(false)}
        onUpdateFilter={() => {
          if (!selectedFilterId || !filterName) {
            return;
          }
          updateFilter({
            id: selectedFilterId,
            name: filterName,
            body: getFilterData(compingMode),
          });
          setOpenModal(false);
          getSavedFilters();
        }}
        onCreateNewFilter={() => {
          if (!filterName) {
            return;
          }
          createNewFilter({
            name: filterName,
            body: getFilterData(compingMode),
          });
          setOpenModal(false);
          getSavedFilters();
        }}
        filterName={filterName}
        setFilterName={setFilterName}
        checkFilterNameInput={checkFilterNameInput}
        selectedFilterId={selectedFilterId}
      />
      <div
        style={{
          display: 'flex',
          justifyContent: 'flex-start',
          alignItems: 'center',
        }}
      >
        <Select
          placeholder="Select a saved filter"
          popupMatchSelectWidth={false}
          // bordered={false}
          variant="borderless"
          value={selectedFilterId}
          onSelect={onSelectFilter}
          options={allFilters.map((filter: any) => ({
            label: filter.name,
            value: filter.id,
          }))}
        />
        <Tooltip title="Save filter">
          <Button
            icon={<SaveOutlined />}
            type="text"
            size="small"
            onClick={() => {
              setOpenModal(true);
            }}
          />
        </Tooltip>
        <Tooltip title="Delete selected filter">
          <Button
            icon={<DeleteOutlined />}
            type="text"
            size="small"
            onClick={() => {
              if (selectedFilterId) {
                Modal.confirm({
                  title: 'Delete Filter',
                  content: 'Are you sure you want to delete this filter?',
                  onOk: () => {
                    deleteFilter({
                      id: selectedFilterId,
                    });
                    getSavedFilters();
                    setSelectedFilterId(null);
                    setFilterName('');
                  },
                });
              }
            }}
            disabled={!selectedFilterId}
          />
        </Tooltip>
      </div>
    </>
  );
};

export default SavedFilters;
