import { Col, Switch } from 'antd';
import { useEffect } from 'react';
import { connect } from 'umi';
import styles from './filters.css';

const SingleFilterSameZIPCode = connect(({ CMA }) => ({
  isZipCodeFilterOn: CMA.isZipCodeFilterOn,
  compingMode: CMA.compingMode,
  currentNationalOperatorsProperties: CMA.currentNationalOperatorsProperties,
  currentHotPadsProperties: CMA.currentHotPadsProperties,
  currentMLSProperties: CMA.currentMLSProperties,
  currentZipCodeData: CMA.currentZipCodeData,
  isLeaseMode: CMA.isLeaseMode,
  searchingMode: CMA.searchingMode,
}))(function (props) {
  const onChangeZipCodeFilter = (checked) => {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        isZipCodeFilterOn: checked,
      },
    });
  };

  useEffect(() => {
    if (
      props.searchingMode === 'Lease' &&
      props.currentNationalOperatorsProperties.length == 0 &&
      props.currentHotPadsProperties.length == 0 &&
      props.currentMLSProperties.length == 0
    ) {
      return;
    } else if (
      props.searchingMode === 'Sale' &&
      props.currentMLSProperties.length == 0
    ) {
      return;
    }

    if (props.isZipCodeFilterOn) {
      props.dispatch({ type: 'CMA/getZipCodeForWithinFilter' }).then(() => {
        filterData();
      });
    } else {
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          currentZipCodeData: [],
        },
      });
      filterData();
    }
  }, [props.isZipCodeFilterOn]);

  const filterData = () => {
    if (props.compingMode === 'smartFilter') {
      props.dispatch({
        type: 'CMA/filterMLSAndSFRData',
        payload: {
          dataSourceType: 'MLS',
        },
      });
      props.dispatch({
        type: 'CMA/filterMLSAndSFRData',
        payload: {
          dataSourceType: 'SFR',
        },
      });
      props.dispatch({
        type: 'CMA/filterMLSAndSFRData',
        payload: {
          dataSourceType: 'HotPads',
        },
      });
    } else {
      props.dispatch({
        type: 'CMA/filterMLSAndSFRDataManual',
        payload: {
          dataSourceType: 'MLS',
        },
      });
      props.dispatch({
        type: 'CMA/filterMLSAndSFRDataManual',
        payload: {
          dataSourceType: 'SFR',
        },
      });
      props.dispatch({
        type: 'CMA/filterMLSAndSFRDataManual',
        payload: {
          dataSourceType: 'HotPads',
        },
      });
    }
  };
  return (
    <>
      <div
        key="same zip code filter"
        className={styles.singleFilterSwitchWrapper}
      >
        <label
          htmlFor="zip code filter switch"
          className={styles.filterLegendSmall}
        >
          ZIP code
        </label>

        <Switch
          id="zip code filter switch"
          defaultChecked={props.isZipCodeFilterOn}
          checked={props.isZipCodeFilterOn}
          onChange={onChangeZipCodeFilter}
          disabled={props.compingMode === 'intelligentComping'}
          size="small"
        />
      </div>
    </>
  );
});

export default SingleFilterSameZIPCode;
