import React, { useState, useEffect, useRef } from 'react';
import { connect } from 'umi';
import {
  Table,
  Row,
  Modal,
  Divider,
  Button,
  InputNumber,
  Form,
  Select,
} from 'antd';
import styles from './filters.css';
import isEqual from 'lodash.isequal';
import isEmpty from 'lodash.isempty';
import SingleFilterSameSchoolDistrict from './SingleFilterSameSchoolDistrict';
import SingleFilterSameCounty from './SingleFilterSameCounty';
import SingleFilterSameZIPCode from './SingleFilterSameZIPCode';

const ComboFiltersWithinSame = (props) => {
  return (
    <fieldset key="combo filters within same">
      <legend
        key="combo filters within same legend"
        className={styles.filterLegendSmall}
      >
        Within Same
      </legend>
      <Divider style={{ margin: '0 0' }} />
      <SingleFilterSameSchoolDistrict />
      <SingleFilterSameCounty />
      <SingleFilterSameZIPCode />
    </fieldset>
  );
};

export default ComboFiltersWithinSame;
