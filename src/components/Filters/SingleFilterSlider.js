/* eslint-disable */
import React, { useState } from 'react';
import { connect } from 'umi';
import { Slider } from 'antd';
import isEqual from 'lodash.isequal';

function SingleFilterSlider(props) {
  // console.log('SingleFilterSlider props', props);
  // console.log('mapboxFilterArray', props.mapboxFilterArray);
  // extract current filter value to use as initial value for the slider

  const getSingleFilterInitialValue = () => {
    let initialValue = [];
    for (const mapboxFilterExpression of props.mapboxFilterArray) {
      if (mapboxFilterExpression[1] === props.filter.name) {
        initialValue.push(Math.round(mapboxFilterExpression[2]));
      }
    }
    let initialValueWithinRange = [];
    if (initialValue.length > 0) {
      // make sure min is before max
      initialValue.sort((a, b) => a - b);
      // make sure max and min is within range
      // pick min
      if (initialValue[0] < props.filter.range[0]) {
        initialValueWithinRange.push(props.filter.range[0]);
      } else {
        initialValueWithinRange.push(initialValue[0]);
      }
      // pick max
      if (initialValue[1] > props.filter.range[1]) {
        initialValueWithinRange.push(props.filter.range[1]);
      } else {
        initialValueWithinRange.push(initialValue[1]);
      }
    } else {
      initialValueWithinRange = props.filter.range;
    }
    return initialValueWithinRange;
  };

  const initialValue = getSingleFilterInitialValue();

  const [silderValue, setSliderValue] = useState(initialValue);

  // const [prevUniqueId, setPrevUniqueId] = useState(props.uniqueId);
  // if (prevUniqueId !== props.uniqueId) {
  //   // console.log('uniqueId changed', prevUniqueId, props.uniqueId);
  //   setPrevUniqueId(props.uniqueId);
  //   setSliderValue(initialValue);
  // }

  const [prevMapboxFilterArray, setPrevMapboxFilterArray] = useState(
    props.mapboxFilterArray,
  );
  if (!isEqual(props.mapboxFilterArray, prevMapboxFilterArray)) {
    setPrevMapboxFilterArray(props.mapboxFilterArray);
    setSliderValue(initialValue);
  }

  const { filter } = props;

  return (
    <fieldset
      key={'single filter wrapper ' + filter.name}
      style={{ marginTop: 32 }}
    >
      <legend
        key="label wrapper"
        style={{
          display: 'flex',
          alignItems: 'baseline',
          justifyContent: 'space-between',
        }}
      >
        <div key="title" style={{ fontSize: 16, fontWeight: 500 }}>
          {filter.title}
        </div>
        <div key="value" style={{ fontSize: 15, fontWeight: 400 }}>
          {silderValue[0]} - {silderValue[1]}
        </div>
      </legend>
      <Slider
        key={'slider ' + filter.name}
        range={true}
        step={filter.step}
        min={filter.range[0]}
        max={filter.range[1]}
        value={silderValue}
        onAfterChange={(value) =>
          props.onChangeComplete(
            props.mapboxFilterArray,
            value,
            filter.name,
            'slider',
            'hasUnlimitedMax' in filter && value[1] === filter.range[1],
            'hasUnlimitedMin' in filter && value[0] === filter.range[0],
            'valueFn' in filter ? filter.valueFn : null,
          )
        }
        onChange={(value) => setSliderValue(value)}
        // tooltipVisible={true}
        // marks={}
        // tipFormatter={}
      />
    </fieldset>
  );
}

export default SingleFilterSlider;
