import { Col, Switch } from 'antd';
import { useEffect } from 'react';
import { connect } from 'umi';
import styles from './filters.css';

const SingleFilterSameCounty = connect(({ CMA }) => ({
  isCountyFilterOn: CMA.isCountyFilterOn,
  compingMode: CMA.compingMode,
  currentNationalOperatorsProperties: CMA.currentNationalOperatorsProperties,
  currentHotPadsProperties: CMA.currentHotPadsProperties,
  currentMLSProperties: CMA.currentMLSProperties,
  currentCountyData: CMA.currentCountyData,
  isLeaseMode: CMA.isLeaseMode,
  searchingMode: CMA.searchingMode,
}))(function (props) {
  const onChangeCountyFilter = (checked) => {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        isCountyFilterOn: checked,
      },
    });
  };

  useEffect(() => {
    if (
      props.searchingMode === 'Lease' &&
      props.currentNationalOperatorsProperties.length == 0 &&
      props.currentHotPadsProperties.length == 0 &&
      props.currentMLSProperties.length == 0
    ) {
      return;
    } else if (
      props.searchingMode === 'Sale' &&
      props.currentMLSProperties.length == 0
    ) {
      return;
    }

    if (props.isCountyFilterOn) {
      props.dispatch({ type: 'CMA/getCountyForWithinFilter' }).then(() => {
        filterData();
      });
    } else {
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          currentCountyData: [],
        },
      });
      filterData();
    }
  }, [props.isCountyFilterOn]);

  const filterData = () => {
    if (props.compingMode === 'smartFilter') {
      props.dispatch({
        type: 'CMA/filterMLSAndSFRData',
        payload: {
          dataSourceType: 'MLS',
        },
      });
      props.dispatch({
        type: 'CMA/filterMLSAndSFRData',
        payload: {
          dataSourceType: 'SFR',
        },
      });
      props.dispatch({
        type: 'CMA/filterMLSAndSFRData',
        payload: {
          dataSourceType: 'HotPads',
        },
      });
    } else {
      props.dispatch({
        type: 'CMA/filterMLSAndSFRDataManual',
        payload: {
          dataSourceType: 'MLS',
        },
      });
      props.dispatch({
        type: 'CMA/filterMLSAndSFRDataManual',
        payload: {
          dataSourceType: 'SFR',
        },
      });
      props.dispatch({
        type: 'CMA/filterMLSAndSFRDataManual',
        payload: {
          dataSourceType: 'HotPads',
        },
      });
    }
  };

  return (
    <>
      <div
        key="same county filter"
        className={styles.singleFilterSwitchWrapper}
      >
        <label
          htmlFor="county filter switch"
          className={styles.filterLegendSmall}
        >
          County
        </label>
        <Switch
          id="county filter switch"
          defaultChecked={props.isCountyFilterOn}
          checked={props.isCountyFilterOn}
          onChange={onChangeCountyFilter}
          disabled={props.compingMode === 'intelligentComping'}
          size="small"
        />
      </div>
    </>
  );
});

export default SingleFilterSameCounty;
