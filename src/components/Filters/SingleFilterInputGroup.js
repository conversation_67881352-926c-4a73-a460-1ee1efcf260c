/* eslint-disable */
import React, { useState } from 'react';
import { connect } from 'umi';
import { Row, Col, Input, InputNumber, Select } from 'antd';
import isEqual from 'lodash.isequal';
import styles from './filters.css';

const { Option } = Select;

const SingleFilterInputGroup = (props) => {
  const onChangeRelation = (value) => {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        ['relation' + props.propsKey]: value,
      },
    });
  };

  const onChangeMin = (value) => {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        ['min' + props.propsKey]: value,
      },
    });
  };

  const onChangeMax = (value) => {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        ['max' + props.propsKey]: value,
      },
    });
  };

  return (
    <fieldset
      key={'single filter wrapper ' + props.name}
      style={{ marginTop: 32 }}
    >
      <legend
        key="label wrapper"
        style={{
          display: 'flex',
          alignItems: 'baseline',
          justifyContent: 'space-between',
        }}
      >
        <div key="title" style={{ fontSize: 14, fontWeight: 600 }}>
          {props.name}
        </div>
      </legend>
      <Input.Group key={'input group ' + props.name} compact>
        <Select
          value={props.relation}
          onChange={onChangeRelation}
          disabled={props.disabled}
        >
          <Option value="between">Between</Option>
          <Option value="equalTo">Equal to</Option>
          <Option value="greaterThan">Greater than or Equal to</Option>
          {['Rent', 'Sales'].includes(props.name) ? (
            <Option value="lessThan">Less than or Equal to</Option>
          ) : (
            <Option value="fewerThan">Fewer than or Equal to</Option>
          )}
        </Select>
        {(props.relation === 'between' ||
          props.relation === 'greaterThan' ||
          props.relation === 'equalTo') && (
          <InputNumber
            style={{
              width: 100,
              textAlign: 'center',
            }}
            placeholder="Minimum"
            value={props.min}
            onChange={onChangeMin}
            disabled={props.disabled}
          />
        )}
        {props.relation === 'between' && (
          <Input
            // className="site-input-split"
            className={styles.siteInputSplit}
            style={{
              width: 30,
              borderLeft: 0,
              borderRight: 0,
              pointerEvents: 'none',
            }}
            placeholder="~"
            disabled
          />
        )}
        {(props.relation === 'between' ||
          props.relation === 'fewerThan' ||
          props.relation === 'lessThan') && (
          <InputNumber
            // className="site-input-right"
            className={styles.siteInputRight}
            style={{
              width: 100,
              textAlign: 'center',
            }}
            placeholder="Maximum"
            value={props.max}
            onChange={onChangeMax}
            disabled={props.disabled}
          />
        )}
      </Input.Group>
    </fieldset>
  );
};

export default SingleFilterInputGroup;
