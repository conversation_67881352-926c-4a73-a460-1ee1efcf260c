import React, { useState, useEffect, useRef } from 'react';
import { connect } from 'umi';
import { Table, Row, Modal, Divider, Button } from 'antd';
// import isEqual from 'lodash.isequal';
import { formatter } from '../../utils/money';
import SingleFilterSlider from './SingleFilterSlider';
import SingleFilterInputGroup from './SingleFilterInputGroup';
import SingleFilterCheckboxGroup from './SingleFilterCheckboxGroup';
import SingleFilterSelect from './SingleFilterSelect';
import isEqual from 'lodash.isequal';
import isEmpty from 'lodash.isempty';
import proFormaPropertySubTypesByMetro from './proFormaPropertySubTypesByMetro.json';
import { getPropertySubTypesForCurrentMetro } from './filterFunctions';
import FilterRowMLSAndSFR from '../ResultTable/FilterRowMLSAndSFR/FilterRowMLSAndSFR';
import Draggable from 'react-draggable';

// for manual filtering
const Filters = (props) => {
  const [prevCompingMode, setPrevCompingMode] = useState(props.compingMode);

  const generateFilterProps = () => {
    return [
      {
        key: 'Beds',
        name: 'Beds',
        min: 'minBeds',
        max: 'maxBeds',
        relation: 'relationBeds',
        type: 'inputGroup',
      },
      {
        key: 'Baths',
        name: 'Baths',
        min: 'minBaths',
        max: 'maxBaths',
        relation: 'relationBaths',
        type: 'inputGroup',
      },
      {
        key: 'Sqft',
        name: 'Sqft',
        min: 'minSqft',
        max: 'maxSqft',
        relation: 'relationSqft',
        type: 'inputGroup',
      },
      {
        key: 'YearBuilt',
        name: 'Year Built',
        min: 'minYearBuilt',
        max: 'maxYearBuilt',
        relation: 'relationYearBuilt',
        type: 'inputGroup',
      },
      {
        key: 'CumulativeDaysOnMarket',
        name: 'Cumulative Days on Market',
        min: 'minCumulativeDaysOnMarket',
        max: 'maxCumulativeDaysOnMarket',
        relation: 'relationCumulativeDaysOnMarket',
        type: 'inputGroup',
      },
      {
        key: 'CoveredParking',
        name: 'Covered Parking',
        min: 'minCoveredParking',
        max: 'maxCoveredParking',
        relation: 'relationCoveredParking',
        type: 'inputGroup',
      },
      {
        key: 'PoolAllowed',
        name: 'Pool Allowed',
        options: [
          { label: 'Yes', value: true },
          { label: 'No', value: false },
        ],
        selected: 'selectedPoolAllowed',
        type: 'select',
      },
      {
        key: 'PropertySubTypes',
        name: 'Property Subtypes',
        checked: 'checkedPropertySubTypes',
        type: 'checkboxGroup',
      },
    ];
  };

  const onChangeComplete = () => {
    props.dispatch({
      type: 'CMA/filterMLSAndSFRDataManual',
      payload: {
        dataSourceType: 'MLS',
      },
    });
    props.dispatch({
      type: 'CMA/filterMLSAndSFRDataManual',
      payload: {
        dataSourceType: 'SFR',
      },
    });
    props.dispatch({
      type: 'CMA/filterMLSAndSFRDataManual',
      payload: {
        dataSourceType: 'HotPads',
      },
    });
    // close modal
    // hideFilterModal();
  };

  if (props.compingMode !== prevCompingMode) {
    setPrevCompingMode(props.compingMode);
    if (props.compingMode === 'noFilter') {
      onChangeComplete();
    } else {
      props.dispatch({
        type: 'CMA/filterMLSAndSFRData',
        payload: {
          dataSourceType: 'MLS',
        },
      });
      props.dispatch({
        type: 'CMA/filterMLSAndSFRData',
        payload: {
          dataSourceType: 'SFR',
        },
      });
      props.dispatch({
        type: 'CMA/filterMLSAndSFRData',
        payload: {
          dataSourceType: 'HotPads',
        },
      });
    }
  }

  // connect the necessary filte value states to each SingleFilterInputGroup
  // therefore, when a filter value changes, only one SingleFilterInputGroup will re-render
  const generateSingleFilterInputGroupArray = generateFilterProps().map(
    (item) => {
      if (item.type === 'inputGroup') {
        const mapStateToProps = ({ CMA }) => {
          return {
            propsKey: item.key,
            name: item.name,
            min: CMA[item.min],
            max: CMA[item.max],
            relation: CMA[item.relation],
            disabled: props.compingMode === 'intelligentComping',
          };
        };
        const SingleFilterInputGroupConnected = connect(mapStateToProps)(
          SingleFilterInputGroup,
        );
        return <SingleFilterInputGroupConnected key={item.key} />;
      } else if (item.type === 'checkboxGroup') {
        const mapStateToProps = ({ CMA }) => {
          return {
            propsKey: item.key,
            name: item.name,
            checked: CMA[item.checked],
            options: getPropertySubTypesForCurrentMetro(
              props.currentPropertyAddress,
              proFormaPropertySubTypesByMetro,
            ),
            disabled: props.compingMode === 'intelligentComping',
          };
        };
        const SingleFilterCheckboxGroupConnected = connect(mapStateToProps)(
          SingleFilterCheckboxGroup,
        );
        return <SingleFilterCheckboxGroupConnected key={item.key} />;
      } else if (item.type === 'select') {
        const mapStateToProps = ({ CMA }) => {
          return {
            propsKey: item.key,
            name: item.name,
            selected: CMA[item.selected],
            options: item.options,
            disabled: props.compingMode === 'intelligentComping',
          };
        };
        const SingleFilterSelectConnected =
          connect(mapStateToProps)(SingleFilterSelect);
        return <SingleFilterSelectConnected key={item.key} />;
      }
    },
  );

  const hideFilterModal = () => {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        showFilterModal: false,
      },
    });
  };

  return (
    <Draggable
    // defaultPosition={{ x: 0, y: 0 }}
    >
      <div
        key="filters modal"
        style={{
          position: 'absolute',
          left: 0, // default position
          bottom: 0,
          width: 500,
          backgroundColor: '#fff',
          zIndex: 1000,
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
          cursor: 'move',
        }}
      >
        <div
          key="filters modal content"
          style={{
            padding: 24,
          }}
        >
          <FilterRowMLSAndSFR />
          <div key="filters wrapper all">
            {generateSingleFilterInputGroupArray}
          </div>
        </div>
        <Divider style={{ margin: 0 }} />
        <Row
          key="filters modal footer"
          justify="end"
          align="middle"
          style={{
            padding: 16,
          }}
        >
          <Button
            key="filters modal footer cancel"
            onClick={hideFilterModal}
            type="default"
            style={{ marginRight: 8 }}
          >
            Close
          </Button>
          <Button
            key="filters modal footer ok"
            onClick={onChangeComplete}
            type="primary"
          >
            See Results
          </Button>
        </Row>
      </div>
    </Draggable>
  );
};

export default connect(({ CMA }) => ({
  eventCoordinates: CMA.eventCoordinates,
  isLeaseMode: CMA.isLeaseMode,
  compingMode: CMA.compingMode,
  currentPropertyAddress: CMA.currentPropertyAddress,
}))(Filters);
