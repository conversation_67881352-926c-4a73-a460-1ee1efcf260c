/* eslint-disable */
import React, { useState } from 'react';
import { connect } from 'umi';
import { Row, Col, Input, InputNumber, Select, Checkbox } from 'antd';
import isEqual from 'lodash.isequal';
import styles from './filters.css';

const SingleFilterCheckboxGroup = (props) => {
  const onChangeCheckboxGroup = (checkedValues) => {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        ['checked' + props.propsKey]: checkedValues,
      },
    });
  };

  const generateCheckboxGroupOptions = () => {
    const options = [];
    for (let i = 0; i < props.options.length; i++) {
      options.push({
        label: props.options[i],
        value: props.options[i],
      });
    }
    return options;
  };

  return (
    <fieldset
      key={'single filter wrapper ' + props.name}
      style={{ marginTop: 32 }}
    >
      <legend
        key="label wrapper"
        style={{
          display: 'flex',
          alignItems: 'baseline',
          justifyContent: 'space-between',
        }}
      >
        <div key="title" style={{ fontSize: 14, fontWeight: 600 }}>
          {props.name}
        </div>
      </legend>
      <Checkbox.Group
        key={'checkbox group ' + props.name}
        options={generateCheckboxGroupOptions()}
        value={props.checked}
        onChange={onChangeCheckboxGroup}
        disabled={props.disabled}
      />
    </fieldset>
  );
};

export default SingleFilterCheckboxGroup;
