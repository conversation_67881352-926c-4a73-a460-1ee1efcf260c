/* eslint-disable */
import React, { useState } from 'react';
import { connect } from 'umi';
import { Row, Col, Input, InputNumber, Select, Checkbox } from 'antd';
import isEqual from 'lodash.isequal';
import styles from './filters.css';

const SingleFilterSelect = (props) => {
  const onChangeSelected = (value) => {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        ['selected' + props.propsKey]: value,
      },
    });
  };

  return (
    <fieldset
      key={'single filter wrapper ' + props.name}
      style={{ marginTop: 32 }}
    >
      <legend
        key="label wrapper"
        style={{
          display: 'flex',
          alignItems: 'baseline',
          justifyContent: 'space-between',
        }}
      >
        <div key="title" style={{ fontSize: 14, fontWeight: 600 }}>
          {props.name}
        </div>
      </legend>
      <Select
        key={'checkbox group ' + props.name}
        options={props.options}
        value={props.selected}
        onChange={onChangeSelected}
        disabled={props.disabled}
      />
    </fieldset>
  );
};

export default SingleFilterSelect;
