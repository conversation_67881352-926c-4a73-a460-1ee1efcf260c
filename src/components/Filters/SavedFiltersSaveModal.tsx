import { Button, Input, Modal } from 'antd';
import React, { useEffect, useState } from 'react';

interface SavedFiltersSaveModalProps {
  open: boolean;
  filterName: string;
  setFilterName: (filterName: string) => void;
  onCancel: () => void;
  onUpdateFilter: () => void;
  onCreateNewFilter: () => void;
  checkFilterNameInput: (nameInput: string) => string;
  selectedFilterId: number | null;
}

const SavedFiltersSaveModal = (props: SavedFiltersSaveModalProps) => {
  const [isSaveAsNewFilter, setIsSaveAsNewFilter] = useState(false);

  console.log('props.filterName', props.filterName);

  return (
    <Modal
      open={props.open}
      title="Save Filter"
      footer={[
        <Button key="cancel" onClick={props.onCancel}>
          Cancel
        </Button>,
        ...(props.selectedFilterId
          ? [
              <Button key="save" onClick={props.onUpdateFilter}>
                Save
              </Button>,
            ]
          : []),
        <Button
          key="save as"
          onClick={() => {
            if (props.checkFilterNameInput(props.filterName) === '') {
              props.onCreateNewFilter();
              setIsSaveAsNewFilter(false);
            } else {
              setIsSaveAsNewFilter(true);
            }
          }}
        >
          Save As New Filter
        </Button>,
      ]}
      onCancel={props.onCancel}
    >
      <Input
        placeholder="Filter Name"
        value={props.filterName}
        onChange={(e) => {
          props.setFilterName(e.target.value);
        }}
        allowClear
        status={
          isSaveAsNewFilter && props.checkFilterNameInput(props.filterName)
            ? 'error'
            : ''
        }
      />
      <div style={{ color: 'red' }}>
        {isSaveAsNewFilter && props.checkFilterNameInput(props.filterName)}
      </div>
    </Modal>
  );
};

export default SavedFiltersSaveModal;
