import filterValuesDefault from './filterValuesDefault.json';
import proFormaPropertySubTypesByMetro from './proFormaPropertySubTypesByMetro.json';

const generateLabel = (i, min, max, additionalLabelText) => {
  if (i === min) {
    return 'No Min';
  } else if (i === max) {
    return 'No Max';
  } else {
    return i + '' + additionalLabelText; // convert to string
  }
};

const generateSelectOpitonsContinuous = (
  min,
  max,
  additionalLabelText,
  shouldAddHalf,
) => {
  let options = [];
  for (let i = min; i <= max; i++) {
    const singleOption = {
      label: generateLabel(i, min, max, additionalLabelText),
      value: i,
    };
    options.push(singleOption);
    // add half bathroom options
    if (shouldAddHalf && i < max) {
      const halfOption = {
        label: generateLabel(i + 0.5, min, max, additionalLabelText),
        value: i + 0.5,
      };
      options.push(halfOption);
    }
  }
  return options;
};

export const optionsBeds = generateSelectOpitonsContinuous(
  filterValuesDefault.minBeds,
  filterValuesDefault.maxBeds,
  '',
);

export const optionsBaths = generateSelectOpitonsContinuous(
  filterValuesDefault.minBaths,
  filterValuesDefault.maxBaths,
  '',
  true,
);

export const optionsYearBuilt = generateSelectOpitonsContinuous(
  filterValuesDefault.minYearBuilt,
  filterValuesDefault.maxYearBuilt,
  '',
);

const generateSelectOpitonsSqft = () => {
  let options = [];
  for (let sqft = 0; sqft <= 10000; sqft = sqft + 100) {
    options.push({
      label:
        sqft === 0
          ? 'No Min'
          : sqft === filterValuesDefault.maxSqft
          ? 'No Max'
          : sqft,
      value: sqft,
    });
  }
  options.push({
    label: 'No Max',
    value: filterValuesDefault.maxSqft,
  });
  return options;
};
export const optionsSqft = generateSelectOpitonsSqft();

export const optionsLotSize = [
  {
    label: 'No Min',
    value: 0,
  },
  {
    label: '0.25',
    value: 0.25,
  },
  {
    label: '0.5',
    value: 0.5,
  },
  {
    label: '1',
    value: 1,
  },
  {
    label: '5',
    value: 5,
  },
  {
    label: '10',
    value: 10,
  },
  {
    label: '25',
    value: 25,
  },
  {
    label: '50',
    value: 50,
  },
  {
    label: '100',
    value: 100,
  },
  {
    label: 'No Max',
    value: 10000,
  },
];

const generateSelectOpitonsCDOM = () => {
  // let options = [];
  const cdomValues = [0, 7, 15, 30, 45, 60, 90, 120, 180, 270, 360, 999];
  const options = cdomValues.map((cdom) => {
    return {
      label: cdom === 0 ? 'No Min' : cdom === 999 ? 'No Max' : cdom,
      value: cdom,
    };
  });
  return options;
};

export const optionsCDOM = generateSelectOpitonsCDOM();

export const optionsPool = [
  {
    label: 'Yes',
    value: true,
  },
  {
    label: 'No',
    value: false,
  },
];

export const optionsCoveredParking = [
  {
    label: 'No Min',
    value: 0,
  },
  {
    label: '1',
    value: 1,
  },
  {
    label: '2',
    value: 2,
  },
  {
    label: '3',
    value: 3,
  },
  {
    label: '4',
    value: 4,
  },
  {
    label: '5',
    value: 5,
  },
  {
    label: '6',
    value: 6,
  },
  {
    label: 'No Max',
    value: 10,
  },
];

export const generateSelectOptionsPropertySubType = (cityCode) => {
  if (!cityCode) {
    return [];
  }
  const currentMetro = proFormaPropertySubTypesByMetro.find(
    (item) => item.cityCode === cityCode,
  );
  let propertySubTypesArray = [];
  if (currentMetro) {
    propertySubTypesArray = currentMetro.propertySubTypes;
  }
  return propertySubTypesArray.map((item) => {
    return {
      label: item,
      value: item,
    };
  });
};

export const optionsRangeBeds = [
  {
    label: '0',
    value: 0,
  },
  {
    label: '1',
    value: 1,
  },
  {
    label: '2',
    value: 2,
  },
  {
    label: '3',
    value: 3,
  },
  {
    label: <>&#8734;</>,
    value: filterValuesDefault.maxBeds,
  },
  // {
  //   label: 'Custom',
  //   value: null,
  // }
];

export const optionsRangeBaths = [
  {
    label: '0',
    value: 0,
  },
  {
    label: '1',
    value: 1,
  },
  {
    label: '2',
    value: 2,
  },
  {
    label: '3',
    value: 3,
  },
  {
    label: <>&#8734;</>,
    value: filterValuesDefault.maxBaths,
  },
  // {
  //   label: 'Custom',
  //   value: null,
  // }
];

export const optionsRangeSqft = [
  {
    label: '5%',
    value: 0.05,
  },
  {
    label: '10%',
    value: 0.1,
  },
  {
    label: '15%',
    value: 0.15,
  },
  {
    label: '20%',
    value: 0.2,
  },
  {
    label: '25%',
    value: 0.25,
  },
  {
    label: <>&#8734;</>,
    value: 10000,
  },
  // {
  //   label: 'Custom',
  //   value: null,
  // }
];

export const optionsRangeLotSize = [
  {
    label: '5%',
    value: 0.05,
  },
  {
    label: '10%',
    value: 0.1,
  },
  {
    label: '15%',
    value: 0.15,
  },
  {
    label: '20%',
    value: 0.2,
  },
  {
    label: '25%',
    value: 0.25,
  },
  {
    label: <>&#8734;</>,
    value: 10000,
  },
  // {
  //   label: 'Custom',
  //   value: null,
  // }
];

export const optionsRangeYearBuilt = [
  {
    label: '5',
    value: 5,
  },
  {
    label: '15',
    value: 15,
  },
  {
    label: '25',
    value: 25,
  },
  {
    label: '30',
    value: 30,
  },
  {
    label: '35',
    value: 35,
  },
  {
    label: <>&#8734;</>,
    value: filterValuesDefault.maxYearBuilt,
  },
  // {
  //   label: 'Custom',
  //   value: null,
  // }
];

export const optionsRangeClosingDate = [
  {
    label: '3 Mo.',
    value: 3,
  },
  {
    label: '6 Mo.',
    value: 6,
  },
  {
    label: '9 Mo.',
    value: 9,
  },
  {
    label: '12 Mo.',
    value: 12,
  },
  {
    label: '24 Mo.',
    value: 24,
  },
  {
    label: <>&#8734;</>,
    value: 120,
  },
  // {
  //   label: 'Custom',
  //   value: null,
  // },
];
