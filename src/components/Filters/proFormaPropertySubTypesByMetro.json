[{"cityCode": "01", "label": "Dallas-Fort Worth-Arlington, TX Metro", "metro": "Dallas", "tz": "America/Chicago", "abbrev": "Dallas", "cbsaId": "19100", "propertySubTypes": ["Single Family Residence", "Townhouse"], "zipcode": [750, 751, 752, 753, 754, 756, 757, 760, 761, 762, 763], "county": ["<PERSON>", "Dallas", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Rockwall", "<PERSON>", "<PERSON>", "Tarrant", "<PERSON>"]}, {"cityCode": "02", "label": "Houston-The Woodlands-Sugar Land, TX Metro", "metro": "Houston", "tz": "America/Chicago", "abbrev": "Houston", "cbsaId": "26420", "propertySubTypes": ["Single Family Detached", "Townhouse"], "zipcode": [770, 771, 772, 773, 774, 775, 776, 777], "county": ["Austin", "Brazoria", "Chambers", "Fort Bend", "Galveston", "<PERSON>", "Liberty", "<PERSON>", "<PERSON>"]}, {"cityCode": "03", "label": "Atlanta-Sandy Springs-Roswell, GA Metro", "metro": "Atlanta", "tz": "America/New_York", "abbrev": "Atlanta", "cbsaId": "12060", "propertySubTypes": ["Single Family Residence", "Townhouse"], "zipcode": [300, 301, 302, 303, 305, 306, 307, 310, 318, 362], "county": ["<PERSON>", "Madison", "Oconee", "Oglethorpe", "Barrow", "<PERSON><PERSON>", "Butts", "<PERSON>", "Cherokee", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>sy<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Paulding", "<PERSON>ens", "Pike", "Rockdale", "Spalding", "Walton"]}, {"cityCode": "04", "label": "Austin-Round Rock, TX Metro", "metro": "Austin", "tz": "America/Chicago", "abbrev": "Austin", "cbsaId": "12420", "propertySubTypes": ["Single Family Residence", "Townhouse"], "zipcode": [786, 787], "county": ["Bastrop", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"]}, {"cityCode": "05", "label": "Tampa-Orlando, FL Metro", "metro": "Stellar", "tz": "America/New_York", "abbrev": "Tampa-Orlando", "cbsaId": "36740", "propertySubTypes": ["Single Family Residence", "Townhouse", "Villa"], "zipcode": [335, 336, 337, 338, 342, 344, 346, 321, 327, 328, 329, 335, 338, 344, 347, 349, 335, 338, 342, 347, 335, 344, 346, 321, 335, 338, 344, 346, 347, 335, 338, 339, 342], "county": ["Lake", "Orange", "Osceola", "Seminole", "<PERSON><PERSON><PERSON>", "Hillsborough", "Pasco", "Pinellas"]}, {"cityCode": "06", "label": "Nashville-Davidson-Murfreesboro, TN Metro", "metro": "Nashville", "tz": "America/Chicago", "abbrev": "Nashville", "cbsaId": "34980", "propertySubTypes": ["Single Family Residence", "Townhouse"], "zipcode": [370, 371, 372, 384, 385, 421, 422], "county": ["<PERSON>", "<PERSON>eatham", "<PERSON>", "<PERSON>", "Macon", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Trousdale", "<PERSON>", "<PERSON>"]}, {"cityCode": "07", "label": "San Antonio-New Braunfels, TX Metro", "metro": "San Antonio", "tz": "America/Chicago", "abbrev": "San Antonio", "cbsaId": "41700", "propertySubTypes": [], "zipcode": [780, 781, 782], "county": ["Atascosa", "Bandera", "Bexar", "Comal", "Guadalupe", "<PERSON>", "Medina", "<PERSON>"]}, {"cityCode": "08", "label": "Charlotte-Concord-Gastonia, NC-SC Metro", "metro": "North Carolina", "tz": "America/New_York", "abbrev": "Charlotte", "cbsaId": "16740", "propertySubTypes": ["Single Family Residence", "Townhouse", "Rental"], "zipcode": [280, 281, 282, 297], "county": ["<PERSON><PERSON>", "Cabarrus", "<PERSON>", "<PERSON><PERSON><PERSON>", "Lincoln", "Mecklenburg", "<PERSON>", "Union", "Chester", "Lancaster", "York"]}, {"cityCode": "09", "label": "Jacksonville, FL Metro", "metro": "Jacksonville", "tz": "America/New_York", "abbrev": "Jacksonville", "cbsaId": "27260", "propertySubTypes": ["Single Family Residence", "Townhouse"], "zipcode": [315, 316, 320, 321, 322, 326], "county": ["<PERSON>", "<PERSON>", "<PERSON>", "Nassau", "St. Johns"]}, {"cityCode": "10", "label": "Minneapolis-St<PERSON>-Bloomington, MN-WI Metro", "metro": "Minneapolis", "tz": "America/Chicago", "abbrev": "Minneapolis", "cbsaId": "33460", "propertySubTypes": ["Single Family Residence", "Townhouse Detached", "Townhouse Quad/4 Corners", "Townhouse Side x Side"], "zipcode": [553, 562, 550, 563, 554, 551, 564, 560, 540, 548, 547, 566, 561, 559, 557, 565], "county": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Dakota", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Le Sueur", "Mille Lacs", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Washington", "<PERSON>", "<PERSON>", "St. Croix"]}, {"cityCode": "11", "label": "Detroit-Warren-Ann Arbor, MI", "metro": "Detroit", "tz": "America/Detroit", "abbrev": "Detroit", "cbsaId": "19820", "propertySubTypes": [], "zipcode": [480, 481, 482, 483, 484, 487, 488], "county": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Livingston", "Macomb", "Oakland", "St. Clair"]}, {"cityCode": "12", "label": "Phoenix-Mesa-Scottsdale, AZ", "metro": "Phoenix", "tz": "America/Phoenix", "abbrev": "Phoenix", "cbsaId": "38060", "propertySubTypes": ["Single Family - Detached", "Townhouse", "Apartment Style/Flat"], "zipcode": [850, 851, 852, 853, 856], "county": ["Maricopa", "Pinal", "Gila", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "La Paz", "<PERSON><PERSON>", "Cochise", "Santa Cruz", "<PERSON>", "<PERSON><PERSON>", "Navajo", "Apache", "Coconino"]}, {"cityCode": "13", "label": "Bakersfield, CA Metro", "metro": "Bakersfield", "tz": "America/Los_Angeles", "abbrev": "Bakersfield", "cbsaId": "12540", "propertySubTypes": ["Single Family Residence", "2 homes on 1 Lot", "New Construction", "3 Units", "4 Units", "5 - 10 Units", "11 Plus"], "zipcode": [932, 933], "county": ["<PERSON>"]}, {"cityCode": "14", "label": "Miami-Fort Lauderdale-West Palm Beach, FL Metro", "metro": "Miami", "tz": "America/New_York", "abbrev": "Miami", "cbsaId": "33100", "propertySubTypes": ["Single Family Residence", "Townhouse", "Villa"], "zipcode": [330, 331, 332, 333, 334], "county": ["<PERSON><PERSON><PERSON>", "Miami-Dade", "Palm Beach", "<PERSON>", "<PERSON>"]}, {"cityCode": "15", "label": "Indianapolis-Carmel-Anderson, IN Metro", "metro": "Indianapolis", "tz": "America/Indiana/Indianapolis", "abbrev": "Indianapolis", "cbsaId": "26900", "propertySubTypes": ["Single Family Residence", "Townhouse"], "zipcode": [460, 461, 462, 472, 474], "county": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Madison", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"cityCode": "16", "label": "Charleston-North Charleston, SC Metro", "metro": "Charleston", "tz": "America/New_York", "abbrev": "Charleston", "cbsaId": "16700", "propertySubTypes": ["Single Family Detached", "Single Family Attached"], "zipcode": [294], "county": ["Berkeley", "Charleston", "Dorchester"]}, {"cityCode": "17", "label": "Raleigh-Durham-Cary, NC Metro", "metro": "Triangle", "tz": "America/New_York", "abbrev": "Raleigh-Durham-Cary", "cbsaId": "39580", "propertySubTypes": ["Detached", "Attached"], "zipcode": [272, 273, 275, 276, 277], "county": ["Chatham", "Durham", "<PERSON>", "Granville", "<PERSON>", "Orange", "Person", "<PERSON>", "Wake", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"]}, {"cityCode": "18", "label": "Kansas City, MO-KS Metro", "metro": "Heartland", "tz": "America/Chicago", "abbrev": "Kansas City", "cbsaId": "28140", "propertySubTypes": ["Single Family Residence", "Townhouse"], "zipcode": [640, 641, 644, 645, 646, 647, 653, 660, 661, 662, 664, 665, 666, 667], "county": ["<PERSON>", "Leavenworth", "<PERSON><PERSON>", "Miami", "Wyandotte", "<PERSON>", "<PERSON>", "Cass", "<PERSON>", "<PERSON>", "<PERSON>", "Lafayette", "Platte", "<PERSON>"]}, {"cityCode": "19", "label": "Oklahoma City, OK Metro", "metro": "Oklahoma", "tz": "America/Chicago", "abbrev": "Oklahoma City", "cbsaId": "36420", "propertySubTypes": ["SingleFamilyResidence", "Townhouse"], "zipcode": [730, 731, 740, 748], "county": ["Canadian", "Cleveland", "<PERSON>", "Lincoln", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Oklahoma"]}, {"cityCode": "21", "label": "Las Vegas-Henderson-Paradise, NV Metro", "metro": "Las Vegas", "tz": "America/Los_Angeles", "abbrev": "Las Vegas", "cbsaId": "29820", "propertySubTypes": ["Single Family Residence", "Townhouse"], "zipcode": [890, 891], "county": ["<PERSON>"]}, {"cityCode": "24", "label": "Memphis, TN-MS-AR Metro", "metro": "Memphis", "tz": "America/Chicago", "abbrev": "Memphis", "cbsaId": "32820", "propertySubTypes": ["Attached Single Family", "Detached Single Family"], "zipcode": [380, 381], "county": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Tunica", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"]}, {"cityCode": "25", "label": "Tulsa, OK Metro", "metro": "Tulsa", "tz": "America/Chicago", "abbrev": "Tulsa", "cbsaId": "46140", "propertySubTypes": ["SingleFamilyResidence", "Townhouse"], "zipcode": [740, 741, 744, 746], "county": ["Creek", "Okmulgee", "Osage", "Pawnee", "<PERSON>", "Tulsa", "<PERSON><PERSON><PERSON>"]}, {"cityCode": "26", "label": "Columbus, OH Metro", "metro": "Columbus", "tz": "America/New_York", "abbrev": "Columbus", "cbsaId": "18140", "propertySubTypes": ["Single Family Shared Wall", "Build to Suit Condo", "Leasehold", "Condo Shared Wall", "Build to Suit Single Family", "Other Residential without Deeded Land", "Other Residential with Deeded Land", "Single Family Freestanding", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Condo Freestanding"], "zipcode": [430, 431, 432, 433, 437, 438, 448, 449, 453, 456, 457], "county": ["Delaware", "Fairfield", "<PERSON>", "Hocking", "Licking", "Madison", "<PERSON>", "<PERSON>", "Pickaway", "Union"]}, {"cityCode": "33", "label": "St. Louis, MO-IL Metro", "metro": "Saint Louis", "tz": "America/Chicago", "abbrev": "St. Louis", "cbsaId": "41180", "propertySubTypes": ["Multi-Family 5+", "Multi-Family 2-4", "Condo/Coop/Villa", "Residential", "New Construction", "Acreage & Farms", "Residential Lots"], "zipcode": [620, 622, 623, 625, 626, 628, 630, 631, 633, 636, 650, 654], "county": ["Bond", "<PERSON>", "<PERSON>", "Jersey", "<PERSON><PERSON><PERSON>", "Madison", "<PERSON>", "St. Clair", "<PERSON>", "<PERSON>", "Lincoln", "St. Charles", "St. Louis", "<PERSON>", "St. Louis city"]}, {"cityCode": "34", "label": "Huntsville, AL Metro", "metro": "Valley", "tz": "America/Chicago", "abbrev": "Huntsville", "cbsaId": "26620", "propertySubTypes": ["Single Family Residence", "Townhouse", "Investment Property"], "zipcode": [356, 357, 358], "county": ["Limestone", "Madison"]}, {"cityCode": "36", "label": "Louisville/Jefferson County, KY-IN Metro", "metro": "Louisville", "tz": "America/New_York", "abbrev": "Louisville", "cbsaId": "31140", "propertySubTypes": ["Single Family Residence", "Townhouse"], "zipcode": [400, 401, 402], "county": ["<PERSON>", "<PERSON>", "<PERSON>", "Washington", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Oldham", "<PERSON>", "<PERSON>"]}, {"cityCode": "37", "label": "Cincinnati, OH-KY-IN Metro", "metro": "Cincinnati", "tz": "America/New_York", "abbrev": "Cincinnati", "cbsaId": "17140", "propertySubTypes": ["Single Family Residence", "Townhouse"], "zipcode": [403, 410, 450, 451, 452, 453, 454, 456, 470, 473], "county": ["Dearborn", "<PERSON>", "Ohio", "Union", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Clermont", "<PERSON>", "<PERSON>"]}, {"cityCode": "38", "label": "Pittsburgh, PA Metro", "metro": "Pittsburgh", "tz": "America/New_York", "abbrev": "Pittsburgh", "cbsaId": "38300", "propertySubTypes": ["SingleFamilyResidence", "Townhouse"], "zipcode": [150, 151, 152, 153, 154, 156, 157, 159, 160, 161, 162], "county": ["Allegheny", "<PERSON>", "Beaver", "<PERSON>", "<PERSON><PERSON>", "Washington", "Westmoreland"]}, {"cityCode": "40", "label": "Columbia, SC Metro", "metro": "Columbia", "tz": "America/New_York", "abbrev": "Columbia", "cbsaId": "17900", "propertySubTypes": ["Single Family", "Townhouse"], "zipcode": [290, 291, 292], "county": ["<PERSON>", "Fairfield", "<PERSON><PERSON><PERSON>", "Lexington", "Richland", "Saluda"]}, {"cityCode": "41", "label": "Cleveland-Elyria, OH Metro", "metro": "Cleveland", "tz": "America/New_York", "abbrev": "Cleveland", "cbsaId": "17460", "propertySubTypes": ["SingleFamilyResidence", "Townhouse"], "zipcode": [440, 441, 442, 443, 444, 445, 446], "county": ["Cuyahoga", "Geauga", "Lake", "<PERSON><PERSON>", "Medina"]}, {"cityCode": "42", "label": "Akron, OH Metro", "metro": "Akron", "tz": "America/New_York", "abbrev": "Akron", "cbsaId": "10420", "propertySubTypes": ["SingleFamilyResidence", "Townhouse"], "zipcode": [440, 442, 443, 444], "county": ["Portage", "Summit"]}, {"cityCode": "44", "label": "Dayton, OH Metro", "metro": "Dayton", "tz": "America/New_York", "abbrev": "Dayton", "cbsaId": "19380", "propertySubTypes": ["Single Family", "Townhouse", "Investment"], "zipcode": [453, 454], "county": ["<PERSON>", "Miami", "<PERSON>"]}, {"cityCode": "45", "label": "Albuquerque, NM Metro", "metro": "Albuquerque", "tz": "America/Denver", "abbrev": "Albuquerque", "cbsaId": "10740", "propertySubTypes": ["Single Family Residence", "Townhouse"], "zipcode": [870, 871, 883], "county": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Torrance", "Valencia"]}, {"cityCode": "55", "label": "Southern California Inland, CA Metro", "metro": "South California Inland", "tz": "America/Los_Angeles", "abbrev": "Inland Empire", "cbsaId": "40140", "propertySubTypes": ["Single Family Residence", "Townhouse"], "zipcode": [900, 902, 903, 904, 905, 906, 907, 908, 910, 911, 912, 913, 914, 915, 916, 917, 918, 919, 920, 921, 922, 923, 924, 925, 926, 927, 928, 930, 932, 934, 935, 936, 937, 950, 953, 954, 955, 959], "county": ["Los Angeles", "Orange", "Riverside", "San Bernardino", "San Diego", "Ventura"]}]