import {
  But<PERSON>,
  DatePicker,
  Divider,
  Form,
  InputNumber,
  Modal,
  Radio,
  Row,
  Select,
  Table,
} from 'antd';
import dayjs from 'dayjs';
import isEmpty from 'lodash.isempty';
import isEqual from 'lodash.isequal';
import moment from 'moment';
import React, { useEffect, useRef, useState } from 'react';
import { connect } from 'umi';
import { optionsRangeYearBuilt } from './filterOptions';
import styles from './filters.css';

const SingleFilterYearBuilt = (props) => {
  const filterAllCompsManual = () => {
    props.dispatch({
      type: 'CMA/filterMLSAndSFRDataManual',
      payload: {
        dataSourceType: 'MLS',
      },
    });
    props.dispatch({
      type: 'CMA/filterMLSAndSFRDataManual',
      payload: {
        dataSourceType: 'SFR',
      },
    });
    props.dispatch({
      type: 'CMA/filterMLSAndSFRDataManual',
      payload: {
        dataSourceType: 'HotPads',
      },
    });
  };

  const onChangeFilterValueYearBuilt = (value) => {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        minYearBuilt: value[0].year(),
        maxYearBuilt: value[1].year(),
        rangeYearBuilt: null, // after user manually changed filter value, set range as custom
      },
    });
    filterAllCompsManual();
  };

  return (
    <fieldset key="year built filter">
      <legend
        key="year built filter legend"
        className={styles.filterLegendSmall}
      >
        Year Built
      </legend>
      <Divider style={{ margin: '0 0' }} />
      {props.compingMode === 'smartFilter' && (
        <>
          <div
            key="relative to subject property hint"
            style={{
              fontSize: 12,
              color: 'rgba(0,0,0,.7)',
            }}
          >
            Relative to subject property
          </div>
          <div
            key="range shortcut wrapper"
            id="CMARangeShortcutWrapper"
            style={{
              display: 'flex',
              justifyContent: 'flex-start',
              alignItems: 'flex-start',
              gap: 8,
              marginTop: 8,
              width: '100%',
            }}
          >
            <span style={{ height: 24 }}>+/-</span>
            <Radio.Group
              value={props.rangeYearBuilt}
              onChange={(e) =>
                props.onChangeRangeValue(`rangeYearBuilt`, e.target.value)
              }
              size="small"
              buttonStyle="solid"
              style={{
                fontSize: 12,
                display: 'flex',
                gap: 8,
                flexWrap: 'wrap',
              }}
            >
              {optionsRangeYearBuilt.map((option) => (
                <Radio.Button
                  key={option.label}
                  value={option.value}
                  type="text"
                >
                  {option.label}
                </Radio.Button>
              ))}
            </Radio.Group>
          </div>
        </>
      )}
      <DatePicker.RangePicker
        key="year built range picker"
        aria-label="Year Built"
        picker="year"
        value={[
          props.minYearBuilt != 0
            ? dayjs(props.minYearBuilt + '')
            : dayjs('1900'),
          dayjs(props.maxYearBuilt + ''),
        ]}
        allowClear={false}
        allowEmpty={[true, false]}
        onChange={onChangeFilterValueYearBuilt}
        variant={'borderless'}
        size="small"
        // style={{ flexWrap: 'wrap' }}
        suffixIcon={null}
        disabled={props.compingMode === 'intelligentComping'}
      />
    </fieldset>
  );
};

export default connect(({ CMA }) => ({
  minYearBuilt: CMA.minYearBuilt,
  maxYearBuilt: CMA.maxYearBuilt,
  compingMode: CMA.compingMode,
  rangeYearBuilt: CMA.rangeYearBuilt,
}))(SingleFilterYearBuilt);
