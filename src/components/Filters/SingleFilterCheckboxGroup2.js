import React, { useState, useEffect, useRef } from 'react';
import { connect } from 'umi';
import {
  Table,
  Row,
  Modal,
  Divider,
  Button,
  InputNumber,
  Form,
  Select,
  Checkbox,
} from 'antd';
import styles from './filters.css';
import isEqual from 'lodash.isequal';
import isEmpty from 'lodash.isempty';
import { generateSelectOptionsPropertySubType } from './filterOptions';

const SingleFilterCheckboxGroup = (props) => {
  const options = {
    PropertySubTypes: generateSelectOptionsPropertySubType(props.cityCode),
  };

  return (
    <fieldset key={`filter ${props.filterName}`}>
      <legend
        key={`filter ${props.filterName} legend`}
        className={styles.filterLegendSmall}
      >
        {props.filterLabel ? props.filterLabel : props.filterName}
      </legend>
      <Divider style={{ margin: '0 0' }} />
      <Checkbox.Group
        key={`${props.filterName} select`}
        aria-label={`${props.filterName}`}
        value={props.value}
        options={options[`${props.filterName}`]}
        onChange={(value) => {
          // console.log('value', value);
          props.onChangeFilterValue(`checked${props.filterName}`, value);
        }}
        disabled={props.disabled}
      />
    </fieldset>
  );
};

export default SingleFilterCheckboxGroup;
