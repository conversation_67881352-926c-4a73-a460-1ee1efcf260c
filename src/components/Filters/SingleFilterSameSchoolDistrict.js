import { Col, Switch } from 'antd';
import { useEffect } from 'react';
import { connect } from 'umi';
import styles from './filters.css';

const SingleFilterSameSchoolDistrict = connect(({ CMA }) => ({
  isDistrictFilterOn: CMA.isDistrictFilterOn,
  compingMode: CMA.compingMode,
  currentNationalOperatorsProperties: CMA.currentNationalOperatorsProperties,
  currentHotPadsProperties: CMA.currentHotPadsProperties,
  currentMLSProperties: CMA.currentMLSProperties,
  currentSchoolDistrictProperties: CMA.currentSchoolDistrictProperties,
  isLeaseMode: CMA.isLeaseMode,
  searchingMode: CMA.searchingMode,
}))(function (props) {
  const onChangeDistrictFilter = (checked) => {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        isDistrictFilterOn: checked,
      },
    });
  };

  useEffect(() => {
    if (
      props.searchingMode === 'Lease' &&
      props.currentNationalOperatorsProperties.length == 0 &&
      props.currentHotPadsProperties.length == 0 &&
      props.currentMLSProperties.length == 0
    ) {
      return;
    } else if (
      props.searchingMode === 'Sale' &&
      props.currentMLSProperties.length == 0
    ) {
      return;
    }

    if (props.isDistrictFilterOn) {
      props.dispatch({ type: 'CMA/getDistrictForWithinFilter' }).then(() => {
        filterData();
      });
    } else {
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          currentSchoolDistrictProperties: [],
        },
      });
      filterData();
    }
  }, [props.isDistrictFilterOn]);

  const filterData = () => {
    if (props.compingMode === 'smartFilter') {
      props.dispatch({
        type: 'CMA/filterMLSAndSFRData',
        payload: {
          dataSourceType: 'MLS',
        },
      });
      props.dispatch({
        type: 'CMA/filterMLSAndSFRData',
        payload: {
          dataSourceType: 'SFR',
        },
      });
      props.dispatch({
        type: 'CMA/filterMLSAndSFRData',
        payload: {
          dataSourceType: 'HotPads',
        },
      });
    } else {
      props.dispatch({
        type: 'CMA/filterMLSAndSFRDataManual',
        payload: {
          dataSourceType: 'MLS',
        },
      });
      props.dispatch({
        type: 'CMA/filterMLSAndSFRDataManual',
        payload: {
          dataSourceType: 'SFR',
        },
      });
      props.dispatch({
        type: 'CMA/filterMLSAndSFRDataManual',
        payload: {
          dataSourceType: 'HotPads',
        },
      });
    }
  };

  return (
    <>
      <div
        key="same school distric filter"
        className={styles.singleFilterSwitchWrapper}
      >
        <label
          htmlFor="district filter switch"
          className={styles.filterLegendSmall}
        >
          School District
        </label>
        <Switch
          id="district filter switch"
          defaultChecked={props.isDistrictFilterOn}
          checked={props.isDistrictFilterOn}
          onChange={onChangeDistrictFilter}
          disabled={props.compingMode === 'intelligentComping'}
          size="small"
        />
      </div>
    </>
  );
});

export default SingleFilterSameSchoolDistrict;
