import {
  Col,
  DatePicker,
  Divider,
  InputNumber,
  Modal,
  Radio,
  Row,
  Select,
  Switch,
} from 'antd';
import dayjs from 'dayjs';
import isEqual from 'lodash.isequal';
import { useRef, useState } from 'react';
import { connect } from 'umi';
import { dateFormat } from '../ResultTable/ResultTable';
import { optionsRangeClosingDate } from './filterOptions';
import styles from './filters.css';

const SingleFilterClosingDate = (props) => {
  const [prevCurrentEndMLS, setPrevCurrentEndMLS] = useState(
    props.currentEndMLS,
  );

  // after user manually changed filter value, set range as custom
  const setRangeAsCustom = () => {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        rangeClosingDate: null,
      },
    });
  };

  if (prevCurrentEndMLS !== props.currentEndMLS && props.currentEndMLS) {
    setPrevCurrentEndMLS(props.currentEndMLS);
    if (!dayjs().isSame(props.currentEndMLS, 'day')) {
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          closedDateWithinOrBetween: 'between',
        },
      });
      setRangeAsCustom();
    } else {
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          closedDateWithinOrBetween: 'within',
        },
      });
    }
  }

  const generateDatePickerLabel = (currentStatus, withinOrBetween) => {
    switch (true) {
      case currentStatus === 'Closed':
        if (withinOrBetween === 'within') {
          return 'Closed within';
        } else {
          return 'Closed between';
        }
      case ['Active', 'Pending'].includes(currentStatus):
        if (withinOrBetween === 'within') {
          return 'Listed within';
        } else {
          return 'Listed between';
        }
      case currentStatus === 'status':
        if (withinOrBetween === 'within') {
          return 'Within';
        } else {
          return 'Between';
        }
    }
  };

  const generateOptionsWithinOrBetween = () => {
    return [
      {
        label: generateDatePickerLabel(props.currentStatusMLS, 'within'),
        value: 'within',
      },
      {
        label: generateDatePickerLabel(props.currentStatusMLS, 'between'),
        value: 'between',
      },
    ];
  };

  const getWithinDaysValue = (dateRange) => {
    // end date must be today for within
    if (dayjs().isSame(dateRange[1], 'day')) {
      const numberOfDays = dayjs(dateRange[1]).diff(dateRange[0], 'days');
      return numberOfDays;
    } else {
      return null;
    }
  };

  const onChangeRangeValueClosingDate = (e) => {
    if (e.target.value) {
      const dateRange = [dayjs().subtract(e.target.value, 'months'), dayjs()];
      getAllPropertyDataAndSaveDateRange(dateRange);
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          rangeClosingDate: e.target.value,
        },
      });
    }
  };

  const onChangeWithin = (value) => {
    if (value && typeof value === 'number') {
      const dateRange = [dayjs().subtract(value, 'days'), dayjs()];
      console.log('dateRange', dateRange);
      getAllPropertyDataAndSaveDateRange(dateRange);
      setRangeAsCustom();
    }
  };

  const onChangeBetween = (dateRange) => {
    getAllPropertyDataAndSaveDateRange(dateRange);
    setRangeAsCustom();
  };

  const getAllPropertyDataAndSaveDateRange = (dateRange) => {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        currentStartMLS: dateRange[0].format(dateFormat),
        currentEndMLS: dateRange[1].format(dateFormat),
      },
    });
    if (props.drawnCustomPolygons.length === 0) {
      if (props.eventCoordinates.length === 2) {
        // fetch all data
        props.dispatch({
          type: 'CMA/getAllPropertyData',
          payload: {
            mode: 'change MLS & SFR date range',
            lng: props.eventCoordinates[0],
            lat: props.eventCoordinates[1],
            status: props.currentStatusMLS,
            propertyType:
              props.searchingMode === 'Lease'
                ? 'Residential Lease'
                : 'Residential',
            startDate: dateRange[0].format(dateFormat),
            endDate: dateRange[1].format(dateFormat),
            distance: props.currentRadiusMile * 1609.34,
            exists: props.currentStatusMLS,
            expDateFilterOn: props.expDateFilterOn ? 'yes' : 'no',
          },
        });
      }
    } else {
      props.dispatch({
        type: 'CMA/getAllPropertyData',
        payload: {
          mode: 'change MLS & SFR date range',
          status: props.currentStatusMLS,
        },
      });
    }
  };

  return (
    <fieldset key="closing date filter">
      <legend
        key="closing date filter legend"
        className={styles.filterLegendSmall}
      >
        Date Range
      </legend>
      <Divider style={{ margin: '0 0' }} />
      {props.compingMode === 'smartFilter' && (
        <>
          <div
            key="relative to subject property hint"
            style={{
              fontSize: 12,
              color: 'rgba(0,0,0,.7)',
            }}
          >
            Relative to subject property
          </div>
          <div
            key="range shortcut wrapper"
            id="CMARangeShortcutWrapper"
            style={{
              display: 'flex',
              justifyContent: 'flex-start',
              alignItems: 'flex-start',
              gap: 8,
              marginTop: 8,
              width: '100%',
            }}
          >
            <span
              style={{
                fontSize: 12,
                lineHeight: '24px',
                height: 24,
              }}
            >
              Within
            </span>
            <Radio.Group
              value={props.rangeClosingDate}
              onChange={onChangeRangeValueClosingDate}
              size="small"
              buttonStyle="solid"
              style={{
                fontSize: 12,
                display: 'flex',
                gap: 8,
                flexWrap: 'wrap',
              }}
            >
              {optionsRangeClosingDate.map((option) => (
                <Radio.Button
                  key={option.label}
                  value={option.value}
                  type="text"
                >
                  {option.label}
                </Radio.Button>
              ))}
            </Radio.Group>
          </div>
        </>
      )}
      <Select
        key="closing date within or between select"
        aria-label="closing date within or between select"
        value={props.closedDateWithinOrBetween}
        options={generateOptionsWithinOrBetween()}
        onChange={(value) => {
          props.dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              closedDateWithinOrBetween: value,
            },
          });
        }}
        popupMatchSelectWidth={false}
        variant={'borderless'}
        size="small"
        suffixIcon={null}
        disabled={props.compingMode === 'intelligentComping'}
      />
      {props.closedDateWithinOrBetween === 'within' ? (
        <InputNumber
          key="date range input number"
          min={0}
          max={999}
          value={getWithinDaysValue([
            props.currentStartMLS,
            props.currentEndMLS,
          ])}
          onChange={onChangeWithin}
          precision={0} // no decimal
          step={30}
          addonAfter="Days"
          style={{ width: 120, textAlign: 'right' }}
          variant={'borderless'}
          size="small"
          disabled={props.compingMode === 'intelligentComping'}
        />
      ) : (
        <DatePicker.RangePicker
          key="closing date range picker"
          value={[dayjs(props.currentStartMLS), dayjs(props.currentEndMLS)]}
          format={dateFormat}
          allowClear={false}
          allowEmpty={[false, false]}
          onChange={onChangeBetween}
          variant={'borderless'}
          size="small"
          style={{ flexWrap: 'wrap' }}
          suffixIcon={null}
          disabled={props.compingMode === 'intelligentComping'}
        />
      )}
      {/* </div> */}
    </fieldset>
  );
};

export default connect(({ CMA }) => ({
  currentStatusMLS: CMA.currentStatusMLS,
  currentStartMLS: CMA.currentStartMLS,
  currentEndMLS: CMA.currentEndMLS,
  currentRadiusMile: CMA.currentRadiusMile,
  eventCoordinates: CMA.eventCoordinates,
  searchingMode: CMA.searchingMode,
  expDateFilterOn: CMA.expDateFilterOn,
  drawnCustomPolygons: CMA.drawnCustomPolygons,
  compingMode: CMA.compingMode,
  rangeClosingDate: CMA.rangeClosingDate,
  closedDateWithinOrBetween: CMA.closedDateWithinOrBetween,
}))(SingleFilterClosingDate);
