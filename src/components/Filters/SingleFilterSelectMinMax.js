import {
  Button,
  Divider,
  Form,
  InputNumber,
  Modal,
  Radio,
  Row,
  Select,
  Table,
} from 'antd';
import isEmpty from 'lodash.isempty';
import isEqual from 'lodash.isequal';
import React, { useEffect, useRef, useState } from 'react';
import { connect } from 'umi';
import {
  generateSelectOptionsPropertySubType,
  optionsBaths,
  optionsBeds,
  optionsCDOM,
  optionsCoveredParking,
  optionsLotSize,
  optionsPool,
  optionsRangeBaths,
  optionsRangeBeds,
  optionsRangeLotSize,
  optionsRangeSqft,
  optionsSqft,
} from './filterOptions';
import styles from './filters.css';

const options = {
  Beds: optionsBeds,
  Baths: optionsBaths,
  Sqft: optionsSqft,
  LotSize: optionsLotSize,
  CoveredParking: optionsCoveredParking,
  CumulativeDaysOnMarket: optionsCDOM,
  Pool: optionsPool,
  propertySubType: generateSelectOptionsPropertySubType(),
};

const optionsRange = {
  Beds: optionsRangeBeds,
  Baths: optionsRangeBaths,
  Sqft: optionsRangeSqft,
  LotSize: optionsRangeLotSize,
};

// for a single filter that contains 2 select inputs, one min and one max
const SingleFilterSelectMinMax = (props) => {
  return (
    <fieldset key={`filter ${props.filterName}`}>
      <legend
        key={`filter ${props.filterName} legend`}
        className={styles.filterLegendSmall}
      >
        {props.filterLabel ? props.filterLabel : props.filterName}
      </legend>
      <Divider style={{ margin: '0 0' }} />
      {props.compingMode === 'smartFilter' &&
        ['Beds', 'Baths', 'Sqft', 'LotSize'].includes(props.filterName) && (
          <>
            <div
              key="relative to subject property hint"
              style={{
                fontSize: 12,
                color: 'rgba(0,0,0,.7)',
              }}
            >
              Relative to subject property
            </div>
            <div
              key="range shortcut wrapper"
              id="CMARangeShortcutWrapper"
              style={{
                display: 'flex',
                justifyContent: 'flex-start',
                alignItems: 'flex-start',
                gap: 8,
                marginTop: 8,
                width: '100%',
              }}
            >
              <span style={{ height: 24 }}>+/-</span>
              <Radio.Group
                value={props.range}
                onChange={(e) =>
                  props.onChangeRangeValue(
                    `range${props.filterName}`,
                    e.target.value,
                  )
                }
                size="small"
                buttonStyle="solid"
                style={{
                  fontSize: 12,
                  display: 'flex',
                  gap: 8,
                  flexWrap: 'wrap',
                }}
              >
                {optionsRange[props.filterName].map((option) => (
                  <Radio.Button
                    key={option.label}
                    value={option.value}
                    type="text"
                  >
                    {option.label}
                  </Radio.Button>
                ))}
              </Radio.Group>
            </div>
          </>
        )}
      <Select
        key={`min${props.filterName} select`}
        aria-label={`Min ${props.filterName}`}
        value={props.min}
        options={options[`${props.filterName}`]}
        onChange={(value) =>
          props.onChangeFilterValue(
            `min${props.filterName}`,
            value,
            `range${props.filterName}`,
          )
        }
        popupMatchSelectWidth={false}
        variant={'borderless'}
        size="small"
        suffixIcon={null}
        disabled={props.disabled}
      />
      <span
        style={{
          display: 'inline-block',
          fontSize: 16,
          lineHeight: '24px',
          color: 'rgba(0,0,0,.5)',
        }}
      >
        -
      </span>
      <Select
        key={`max${props.filterName} select`}
        aria-label={`Max ${props.filterName}`}
        value={props.max}
        options={options[`${props.filterName}`]}
        onChange={(value) =>
          props.onChangeFilterValue(`max${props.filterName}`, value)
        }
        popupMatchSelectWidth={false}
        variant={'borderless'}
        size="small"
        suffixIcon={null}
        disabled={props.disabled}
      />
    </fieldset>
  );
};

export default SingleFilterSelectMinMax;
