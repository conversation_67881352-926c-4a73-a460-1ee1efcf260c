import {
  But<PERSON>,
  Di<PERSON><PERSON>,
  <PERSON>,
  InputN<PERSON>ber,
  Modal,
  Row,
  Select,
  Table,
} from 'antd';
import isEmpty from 'lodash.isempty';
import isEqual from 'lodash.isequal';
import React, { useEffect, useRef, useState } from 'react';
import { connect } from 'umi';
import { optionsPool } from './filterOptions';
import styles from './filters.css';

const options = {
  PoolAllowed: optionsPool,
};

// for a single filter that contains only one select input
const SingleFilterSelect = (props) => {
  if (props.filterName === 'PoolAllowed') {
    console.log('props.selectedPoolAllowed', props.value);
  }
  return (
    <fieldset key={`filter ${props.filterName}`}>
      <legend
        key={`filter ${props.filterName} legend`}
        className={styles.filterLegendSmall}
      >
        {props.filterLabel ? props.filterLabel : props.filterName}
      </legend>
      <Divider style={{ margin: '0 0' }} />
      <Select
        key={`${props.filterName} select`}
        aria-label={props.filterName}
        value={props.value}
        options={options[props.filterName]}
        onChange={(value) =>
          props.onChangeFilterValue(`selected${props.filterName}`, value)
        }
        popupMatchSelectWidth={false}
        variant={'borderless'}
        size="small"
        // suffixIcon={null}
        disabled={props.disabled}
      />
    </fieldset>
  );
};

export default SingleFilterSelect;
