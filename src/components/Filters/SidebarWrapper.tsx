import { CloseOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import { sideBarWidth } from '../ResultTable/ResultTable';
import styles from '../ResultTable/resultTable.css';
import Filters from './Filters2';

const SidebarWrapper = () => {
  const showFilterSideBar = useSelector(
    (state: any) => state.CMA.showFilterSideBar,
  );

  const dispatch = useDispatch();

  const hideFilterModal = () => {
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        showFilterSideBar: false,
      },
    });
  };

  return (
    <div
      key="filter side bar wrapper"
      className={styles.cardWrapper}
      style={{
        width: showFilterSideBar ? sideBarWidth : 0,
        // padding needs to be 0 when width is 0
        // otherwise width would be 0 in number but the sum of paddings in computed
        // because width can't be negative
        // ref: https://stackoverflow.com/a/28908411
        padding: `24px ${showFilterSideBar ? 16 : 0}px`,
        // marginTop: 62,
        // marginBottom: 0,
        overflowY: 'auto',
        transition: 'width 0.3s ease-in-out',
        // aniamtion: 'slideIn 0.5s ease-in-out',
        // animationFillMode: 'forwards',
      }}
    >
      <div
        key="hide sidebar button wrapper"
        style={{
          position: 'absolute',
          top: 8,
          right: 8,
        }}
      >
        <Button
          key="hide sidebar"
          // type="parimary"
          // shape='square'
          size="small"
          icon={<CloseOutlined />}
          onClick={hideFilterModal}
        />
      </div>
      <Filters />
    </div>
  );
};

export default SidebarWrapper;
