import {
  Button,
  Divider,
  Form,
  InputNumber,
  Modal,
  Row,
  Select,
  Table,
} from 'antd';
import isEmpty from 'lodash.isempty';
import isEqual from 'lodash.isequal';
import moment from 'moment';
import React, { useEffect, useRef, useState } from 'react';
import { connect, useSelector } from 'umi';
import { dateFormat } from '../../constants';
import styles from './filters.css';

// for status filter
const SingleFilterSelectStatus = (props) => {
  const searchingMode = useSelector((state) => state.CMA.searchingMode);
  const optionsStaus = [
    { label: 'Closed', value: 'Closed' },
    { label: 'Pending', value: 'Pending' },
    { label: 'Active', value: 'Active' },
    { label: 'All', value: 'status' },
  ];

  const getStatusOptions = () => {
    if (searchingMode === 'Land') {
      return [
        { label: 'Closed', value: 'Closed' },
        { label: 'Active', value: 'Active' },
        { label: 'Withdrawn', value: 'Withdrawn' },
        { label: 'Pending', value: 'Pending' },
        { label: 'All', value: 'status' },
      ];
    } else {
      return [
        { label: 'Closed', value: 'Closed' },
        { label: 'Pending', value: 'Pending' },
        { label: 'Active', value: 'Active' },
        { label: 'All', value: 'status' },
      ];
    }
  };
  const onChangeFilterValue = (value) => {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        currentStatusMLS: value,
      },
    });
    if (props.drawnCustomPolygons.length === 0) {
      if (props.eventCoordinates.length === 2) {
        // fetch all data
        props.dispatch({
          type: 'CMA/getAllPropertyData',
          payload: {
            mode: 'change MLS & SFR status',
            lng: props.eventCoordinates[0],
            lat: props.eventCoordinates[1],
            // geofence: getGeofenceParamForGeocodingAPI(props.userGroup),
            status: value,
            propertyType:
              props.searchingMode === 'Lease'
                ? 'Residential Lease'
                : 'Residential',
            startDate: moment(props.currentStartMLS).format(dateFormat),
            endDate: moment(props.currentEndMLS).format(dateFormat),
            distance: props.currentRadiusMile * 1609.34,
            exists: value,
            expDateFilterOn: props.expDateFilterOn ? 'yes' : 'no',
          },
        });
      }
    } else {
      props.dispatch({
        type: 'CMA/getAllPropertyData',
        payload: {
          mode: 'change MLS & SFR status',
          status: value,
        },
      });
    }
  };

  return (
    <fieldset key="status filter">
      <legend key="status filter legend" className={styles.filterLegendSmall}>
        Status
      </legend>
      <Divider style={{ margin: '0 0' }} />
      <Select
        key="status select"
        aria-label="status select"
        value={props.currentStatusMLS}
        options={getStatusOptions()}
        onChange={onChangeFilterValue}
        popupMatchSelectWidth={false}
        variant={'borderless'}
        size="small"
        suffixIcon={null}
        disabled={props.compingMode === 'intelligentComping'}
      />
    </fieldset>
  );
};

export default connect(({ CMA }) => ({
  currentStatusMLS: CMA.currentStatusMLS,
  currentStartMLS: CMA.currentStartMLS,
  currentEndMLS: CMA.currentEndMLS,
  currentRadiusMile: CMA.currentRadiusMile,
  eventCoordinates: CMA.eventCoordinates,
  compingMode: CMA.compingMode,
  // isLeaseMode: CMA.isLeaseMode,
  searchingMode: CMA.searchingMode,
  drawnCustomPolygons: CMA.drawnCustomPolygons,
}))(SingleFilterSelectStatus);
