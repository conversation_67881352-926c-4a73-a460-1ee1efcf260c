import {
  Button,
  Divider,
  Form,
  Input,
  InputNumber,
  Modal,
  Radio,
  Row,
  Select,
  Table,
} from 'antd';
import isEmpty from 'lodash.isempty';
import isEqual from 'lodash.isequal';
import React, { useEffect, useRef, useState } from 'react';
import { connect } from 'umi';
import styles from './filters.css';
import filterValuesDefault from './filterValuesDefault.json';

type numberInputUnit = 'dollar' | 'percent';

interface SingleFilterInputMinMaxProps {
  filterName: string;
  filterLabel?: string;
  min: number;
  max: number;
  range?: number;
  compingMode: string;
  disabled?: boolean;
  onChangeFilterValue: (key: string, value: string, range: string) => void;
  onChangeRangeValue?: (key: string, value: string) => void;
  unit: numberInputUnit;
  isLeaseMode?: boolean;
  subjectPropertyAVM?: number;
}

type minOrMax = 'min' | 'max';

// for a single filter that contains 2 inputs, one min and one max
const SingleFilterInputMinMax = (props: SingleFilterInputMinMaxProps) => {
  const [tempMin, setTempMin] = useState<number>(props.min);
  const [tempMax, setTempMax] = useState<number>(props.max);

  // update min/max values after switching between lease/sale mode
  // otherwise the values will be stuck at the last mode's values
  // also when switching to ALL mode, the values should be reset to default
  useEffect(() => {
    setTempMin(props.min);
    setTempMax(props.max);
  }, [props.isLeaseMode, props.compingMode]);

  const formatterMoney = (value) => {
    if (+value === filterValuesDefault[`max${props.filterName}`]) {
      return 'No Max';
    } else {
      return `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  };
  const parserMoney = (value) => {
    if (value === 'No Max') {
      return filterValuesDefault[`max${props.filterName}`];
    } else if (
      +value!.replace(/\$\s?|(,*)/g, '') === 0 &&
      !['0', '$ 0', '$', '$ '].includes(value)
    ) {
      // for example, when a user wants to change 1000 to 2000, and they select the 1 and type 2
      // we want to keep the trailing 0 in the string
      // instead of parsing and converting it to 0
      return value;
    } else {
      return value!.replace(/\$\s?|(,*)/g, '');
    }
  };

  const formatterPercent = (value) => {
    if (+value === filterValuesDefault[`max${props.filterName}`]) {
      return 'No Max';
    } else {
      return `${value * 100}%`;
    }
  };
  const parserPercent = (value) => {
    if (value === 'No Max') {
      return filterValuesDefault[`max${props.filterName}`];
    } else {
      return value!.replace('%', '') / 100;
    }
  };

  const getFormatter = (unit: numberInputUnit) => {
    switch (unit) {
      case 'dollar':
        return formatterMoney;
      case 'percent':
        return formatterPercent;
      default:
        return formatterMoney;
    }
  };

  const getParser = (unit: numberInputUnit) => {
    switch (unit) {
      case 'dollar':
        return parserMoney;
      case 'percent':
        return parserPercent;
      default:
        return parserMoney;
    }
  };

  const onApplyFilterValue = (value: string, minOrMax: minOrMax) => {
    let valueForParser = value;
    if (['$ ', ' ', '$', ''].includes(value)) {
      switch (minOrMax) {
        case 'min':
          setTempMin(filterValuesDefault[`min${props.filterName}`]);
          valueForParser = getFormatter(props.unit)(
            filterValuesDefault[`min${props.filterName}`],
          );
          break;
        case 'max':
          setTempMax(filterValuesDefault[`max${props.filterName}`]);
          valueForParser = getFormatter(props.unit)(
            filterValuesDefault[`max${props.filterName}`],
          );
          break;
      }
    }
    props.onChangeFilterValue(
      `${minOrMax}${props.filterName}`,
      +getParser(props.unit)(valueForParser),
    );
  };

  return (
    <fieldset key={`filter ${props.filterName}`}>
      <legend
        key={`filter ${props.filterName} legend`}
        className={styles.filterLegendSmall}
      >
        {props.filterLabel ? props.filterLabel : props.filterName}
      </legend>
      <Divider style={{ margin: '0 0' }} />
      {props.filterName === 'AVMToSoldPrice' && (
        <div
          style={{
            fontSize: 12,
            color: 'rgba(0,0,0,.5)',
          }}
        >
          AVM: {formatterMoney(+props.subjectPropertyAVM)}
        </div>
      )}
      <InputNumber
        key={`min${props.filterName} select`}
        aria-label={`Min ${props.filterName}`}
        // value={props.min}
        value={tempMin}
        onChange={
          (value) => setTempMin(value)
          // (value) => props.onChangeFilterValue(`min${props.filterName}`, value)
        }
        onFocus={(e) => {
          if (
            +getParser(props.unit)(e.target.value) ===
            filterValuesDefault[`min${props.filterName}`]
          ) {
            setTempMin('');
          }
        }}
        onPressEnter={(e) => {
          onApplyFilterValue(e.target.value, 'min');
        }}
        onBlur={(e) => {
          onApplyFilterValue(e.target.value, 'min');
        }}
        variant="filled"
        // bordered={false}
        size="small"
        controls={false}
        disabled={props.disabled}
        formatter={getFormatter(props.unit)}
        parser={getParser(props.unit)}
      />
      <span
        style={{
          display: 'inline-block',
          fontSize: 16,
          lineHeight: '24px',
          color: 'rgba(0,0,0,.5)',
        }}
      >
        -
      </span>
      <InputNumber
        key={`max${props.filterName} select`}
        aria-label={`Max ${props.filterName}`}
        // value={props.max}
        value={tempMax}
        onChange={(value) => setTempMax(value)}
        onFocus={(e) => {
          if (
            +getParser(props.unit)(e.target.value) ===
            filterValuesDefault[`max${props.filterName}`]
          ) {
            setTempMax('');
          }
        }}
        onPressEnter={(e) => {
          onApplyFilterValue(e.target.value, 'max');
        }}
        onBlur={(e) => {
          onApplyFilterValue(e.target.value, 'max');
        }}
        variant="filled"
        // bordered={false}
        size="small"
        controls={false}
        disabled={props.disabled}
        formatter={getFormatter(props.unit)}
        parser={getParser(props.unit)}
      />
    </fieldset>
  );
};

export default SingleFilterInputMinMax;
