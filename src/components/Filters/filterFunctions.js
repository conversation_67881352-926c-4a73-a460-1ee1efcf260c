import booleanPointInPolygon from '@turf/boolean-point-in-polygon';
import { point } from '@turf/helpers';
import isEmpty from 'lodash.isempty';
import filterValuesDefault from './filterValuesDefault.json';
import proFormaPropertySubTypesByMetro from './proFormaPropertySubTypesByMetro.json';

const currentYear = new Date().getFullYear();

// sample filter array
const sampleFilterArray = [
  ['>=', 'bed', 1],
  ['<=', 'bed', 4],
  // [
  //   'in',
  //   'propertysubtype',
  //   ['Single Family Residence', 'Townhouse', 'Duplex', 'Triplex', 'Quadruplex'],
  // ],
];

const arrayFilterCallbackFn = (item, mapboxFilter) => {
  // console.log(item);
  for (const filterExpression of mapboxFilter) {
    // if beds is null, keep it in the results
    // if sqft is null, keep it in the results
    // if baths is null, keep it in the results
    // if year built is null, keep it in the results
    if (filterExpression[0] === '==') {
      if (
        !(item[filterExpression[1]] === filterExpression[2]) &&
        !(item[filterExpression[1]] === null) &&
        !(item[filterExpression[1]] === 0) && // for the Triangle, NC, where many parcels show 0 beds
        Object.hasOwn(item, filterExpression[1])
      ) {
        console.log(
          filterExpression[1],
          item[filterExpression[1]],
          'NOT',
          '==',
          filterExpression[2],
        );
        return false;
      }
    } else if (filterExpression[0] === '>=') {
      if (
        !(item[filterExpression[1]] >= filterExpression[2]) &&
        !(item[filterExpression[1]] === null) &&
        !(item[filterExpression[1]] === 0) &&
        Object.hasOwn(item, filterExpression[1])
      ) {
        console.log(
          filterExpression[1],
          item[filterExpression[1]],
          'NOT',
          '>=',
          filterExpression[2],
        );
        return false;
      }
    } else if (filterExpression[0] === '>') {
      if (
        !(item[filterExpression[1]] > filterExpression[2]) &&
        !(item[filterExpression[1]] === null) &&
        !(item[filterExpression[1]] === 0) &&
        Object.hasOwn(item, filterExpression[1])
      ) {
        console.log(
          filterExpression[1],
          item[filterExpression[1]],
          'NOT',
          '>',
          filterExpression[2],
        );
        return false;
      }
    } else if (filterExpression[0] === '<=') {
      if (
        !(item[filterExpression[1]] <= filterExpression[2]) &&
        !(item[filterExpression[1]] === null) &&
        !(item[filterExpression[1]] === 0) &&
        Object.hasOwn(item, filterExpression[1])
      ) {
        console.log(
          filterExpression[1],
          item[filterExpression[1]],
          'NOT',
          '<=',
          filterExpression[2],
        );
        return false;
      }
    } else if (filterExpression[0] === '<') {
      if (
        !(item[filterExpression[1]] < filterExpression[2]) &&
        !(item[filterExpression[1]] === null) &&
        !(item[filterExpression[1]] === 0) &&
        Object.hasOwn(item, filterExpression[1])
      ) {
        console.log(
          filterExpression[1],
          item[filterExpression[1]],
          'NOT',
          '<',
          filterExpression[2],
        );
        return false;
      }
    } else if (filterExpression[0] === 'in') {
      const arrayToLowerCase = (targetArray) => {
        return targetArray.map((item) => item.toLowerCase());
      };
      const itemToLowerCase = (targetItem) => {
        if (
          targetItem === null ||
          targetItem === '' ||
          targetItem === undefined
        ) {
          return targetItem;
        } else {
          return targetItem.toLowerCase();
        }
      };
      if (
        !arrayToLowerCase(filterExpression[2]).includes(
          itemToLowerCase(item[filterExpression[1]]),
        ) &&
        !(
          item[filterExpression[1]] === null || item[filterExpression[1]] === ''
        ) &&
        Object.hasOwn(item, filterExpression[1])
      ) {
        console.log(
          filterExpression[1],
          item[filterExpression[1]],
          'NOT',
          'in',
          filterExpression[2],
        );
        return false;
      }
    }
  }
  // console.log('item passed all filters');
  return true;
};

export const filterTableDataSource = (mapboxFilter, dataSource) => {
  console.log('mapboxFilter', mapboxFilter, 'dataSource', dataSource);
  let dataSourceFiltered = [];
  if (dataSource && dataSource.length > 0) {
    dataSourceFiltered = dataSource.filter((item) =>
      arrayFilterCallbackFn(item, mapboxFilter),
    );
    // console.log('filterFunctions dataSourceFiltered', dataSourceFiltered);
    // sort using mlsid or base_id
    // because same API requests could return same set of properties but in different orders
    // which would result in false from isEqual()
    dataSourceFiltered.sort((a, b) => {
      if (Object.hasOwn(a, 'mlsid')) {
        return a.mlsid - b.mlsid;
      } else if (Object.hasOwn(a, 'base_id')) {
        return a.base_id - b.base_id;
      }
    });
  }
  return dataSourceFiltered;
};

// get all available property subtypes for the metro that the subject property is in
export const getPropertySubTypesForCurrentMetro = (
  currentPropertyAddress,
  proFormaPropertySubTypesByMetro,
) => {
  const defaultPropertySubTypes = [
    'Single Family Residence',
    'Townhouse',
    'Duplex',
    'Triplex',
    'Quadruplex',
    'Rental',
  ];
  if (
    currentPropertyAddress &&
    !isEmpty(currentPropertyAddress) &&
    currentPropertyAddress.postalCode
  ) {
    const code = Number(currentPropertyAddress.postalCode.substring(0, 3));
    const currentMetroItem = proFormaPropertySubTypesByMetro.find((metro) =>
      metro.zipcode.includes(code),
    );
    if (currentMetroItem) {
      return currentMetroItem.propertySubTypes;
    } else {
      return defaultPropertySubTypes;
    }
  } else {
    return defaultPropertySubTypes;
  }
};

// check if range values have been changed under smart filter mode
// if changed, smart filter should only run one cycle instead of potentially two
// ATTN: not in use - replaced by a field in payload
// export const checkIsCustomRange = (ranges) => {
//   if (
//     ranges &&
//     Object.hasOwn(ranges, 'rangeBeds') &&
//     Object.hasOwn(ranges, 'rangeBaths') &&
//     Object.hasOwn(ranges, 'rangeSqft') &&
//     Object.hasOwn(ranges, 'rangeYearBuilt')
//   ) {
//     const { rangeBeds, rangeBaths, rangeSqft, rangeYearBuilt } = ranges;
//     switch (true) {
//       case rangeBeds !== filterValuesDefault.rangeBeds:
//       case rangeBaths !== filterValuesDefault.rangeBaths:
//       case rangeSqft !== filterValuesDefault.rangeSqft:
//       case rangeYearBuilt !== filterValuesDefault.rangeYearBuilt:
//         return true;
//       default:
//         return false;
//     }
//   } else {
//     return false;
//   }
// };

// generate smart filter expression based on subject property
// if there are fewer than 3 results within 0.5 miles after filtering
// widen beds to +/- 1
export const generateSmartFilterExpression = ({
  subjectPropertyParcelData,
  cycle,
  dataSourceType, // MLS or SFR
  isLeaseMode,
  selectedUserGroup,
  isCustomRange,
  ranges,
  cdomMinMax,
  garageMinMax,
  poolAllowed,
  rentPriceMinMax,
  AVMToRentPriceMinMax,
  soldPriceMinMax,
  AVMToSoldPriceMinMax,
}) => {
  let rangeBeds, rangeBaths, rangeSqft, rangeLotSize, rangeYearBuilt;
  if (
    ranges &&
    Object.hasOwn(ranges, 'rangeBeds') &&
    Object.hasOwn(ranges, 'rangeBaths') &&
    Object.hasOwn(ranges, 'rangeSqft') &&
    Object.hasOwn(ranges, 'rangeLotSize') &&
    Object.hasOwn(ranges, 'rangeYearBuilt')
  ) {
    rangeBeds = ranges.rangeBeds;
    rangeBaths = ranges.rangeBaths;
    rangeSqft = ranges.rangeSqft;
    rangeLotSize = ranges.rangeLotSize;
    rangeYearBuilt = ranges.rangeYearBuilt;
  } else {
    rangeBeds = filterValuesDefault.rangeBeds;
    rangeBaths = filterValuesDefault.rangeBaths;
    rangeSqft = filterValuesDefault.rangeSqft;
    rangeLotSize = filterValuesDefault.rangeLotSize;
    rangeYearBuilt = filterValuesDefault.rangeYearBuilt;
  }
  const keyBedrooms = dataSourceType === 'MLS' ? 'bed' : 'bed_rooms';
  const keyBathrooms = dataSourceType === 'MLS' ? 'bath' : 'bath_rooms';
  const keySize = dataSourceType === 'MLS' ? 'size' : 'square_feet';
  const keyLotSize = 'area_acres';
  const keyYearBuilt = 'yearbuilt';
  const keyPrice = 'MLS' ? 'latestPrice' : 'rent';
  // const optionsPropertySubtype = filterValuesDefault.checkedPropertySubTypes;
  // const optionsPropertySubtype = getPropertySubTypesForCurrentMetro(
  //   currentPropertyAddress,
  //   proFormaPropertySubTypesByMetro,
  // );
  // when parcel data is empty, don't filter and show everything
  if (!isEmpty(subjectPropertyParcelData)) {
    const { total_area_sq_ft, area_acres, year_built, beds_count, baths } =
      subjectPropertyParcelData;
    const filterExpressionBase = [
      [
        '>=',
        keySize,
        total_area_sq_ft && total_area_sq_ft * (1 - rangeSqft) >= 0
          ? Math.round((total_area_sq_ft * (1 - rangeSqft)) / 10) * 10
          : 0,
      ], // round sqft to nearest 10
      [
        '<=',
        keySize,
        total_area_sq_ft &&
        total_area_sq_ft * (1 + rangeSqft) <= filterValuesDefault.maxSqft
          ? Math.round((total_area_sq_ft * (1 + rangeSqft)) / 10) * 10
          : filterValuesDefault.maxSqft,
      ],
      [
        '>=',
        keyLotSize,
        area_acres &&
        area_acres * (1 - rangeLotSize) >= filterValuesDefault.minLotSize
          ? Math.round(area_acres * (1 - rangeLotSize) * 1000) / 1000
          : filterValuesDefault.minLotSize,
      ],
      [
        '<=',
        keyLotSize,
        area_acres &&
        area_acres * (1 + rangeLotSize) <= filterValuesDefault.maxLotSize
          ? Math.round(area_acres * (1 + rangeLotSize) * 1000) / 1000
          : filterValuesDefault.maxLotSize,
      ],
      [
        '>=',
        keyYearBuilt,
        year_built &&
        year_built - rangeYearBuilt >= filterValuesDefault.minYearBuilt
          ? year_built - rangeYearBuilt
          : filterValuesDefault.minYearBuilt,
      ],
      [
        '<=',
        keyYearBuilt,
        year_built && year_built + rangeYearBuilt <= currentYear
          ? year_built + rangeYearBuilt
          : currentYear,
      ],
      [
        '>=',
        keyBathrooms,
        baths && baths - rangeBaths >= 0 ? baths - rangeBaths : 0,
      ], // make sure bath is not negative
      [
        '<=',
        keyBathrooms,
        baths && baths + rangeBaths <= filterValuesDefault.maxBaths
          ? baths + rangeBaths
          : filterValuesDefault.maxBaths,
      ], // show No Max if baths + rangeBaths > maxBaths
      ['>=', 'cdom', cdomMinMax?.min ? cdomMinMax.min : 0],
      [
        '<=',
        'cdom',
        cdomMinMax?.max
          ? cdomMinMax.max
          : filterValuesDefault.maxCumulativeDaysOnMarket,
      ], // don't filter by cdom
      ['>=', 'garage', garageMinMax?.min ? garageMinMax.min : 0],
      [
        '<=',
        'garage',
        garageMinMax?.max
          ? garageMinMax.max
          : filterValuesDefault.maxCoveredParking,
      ],
      // only generate filter expression when pool is not allowed
      // because when pool is allowed, we don't need to filter
      ...(poolAllowed ? [] : [['==', 'pool', false]]),
      // ['in', 'propertysubtype', optionsPropertySubtype],
      ...(isLeaseMode
        ? [
            ['>=', keyPrice, rentPriceMinMax?.min ? rentPriceMinMax.min : 0],
            [
              '<=',
              keyPrice,
              rentPriceMinMax?.max
                ? rentPriceMinMax.max
                : filterValuesDefault.maxRentPrice,
            ],
            // ['>=', 'avmToRentPrice', AVMToRentPriceMinMax?.min ? AVMToRentPriceMinMax.min : 0],
            // ['<=', 'avmToRentPrice', AVMToRentPriceMinMax?.max ? AVMToRentPriceMinMax.max : 100],
          ]
        : [
            // in sale mode, only MLS comps are filtered, therefore we use 'latestPrice' instead of keyPrice
            [
              '>=',
              'latestPrice',
              soldPriceMinMax?.min ? soldPriceMinMax.min : 0,
            ],
            [
              '<=',
              'latestPrice',
              soldPriceMinMax?.max
                ? soldPriceMinMax.max
                : filterValuesDefault.maxSoldPrice,
            ],
            // ['>=', 'avmToSoldPrice', AVMToSoldPriceMinMax?.min ? AVMToSoldPriceMinMax.min : 0],
            // ['<=', 'avmToSoldPrice', AVMToSoldPriceMinMax?.max ? AVMToSoldPriceMinMax.max : 100],
          ]),
    ];
    // if (dataSourceType === 'MLS') {
    //   filterExpressionBase.push([
    //     'in',
    //     'propertysubtype',
    //     optionsPropertySubtype,
    //   ]);
    // }

    if (cycle === 'first') {
      return [
        ...filterExpressionBase,
        [
          '>=',
          keyBedrooms,
          beds_count && beds_count - rangeBeds >= 0
            ? beds_count - rangeBeds
            : 0,
        ], // make sure bed is not negative
        [
          '<=',
          keyBedrooms,
          beds_count && beds_count + rangeBeds <= filterValuesDefault.maxBeds
            ? beds_count + rangeBeds
            : filterValuesDefault.maxBeds,
        ], // show No Max if beds + rangeBeds > maxBeds
      ];
    } else if (cycle === 'second' && !isCustomRange) {
      return [
        ...filterExpressionBase,
        ['>=', keyBedrooms, beds_count ? beds_count - 1 : 1],
        [
          '<=',
          keyBedrooms,
          beds_count ? beds_count + 1 : filterValuesDefault.maxBeds,
        ],
      ];
    }
  } else {
    return [];
  }
};

// for applying smart filter values to inputs in filter modal
// filterExpression is for MLS
export const generateSmartFilterValues = (filterExpression) => {
  let smartFilterValues = {
    minBeds: filterValuesDefault.minBeds,
    maxBeds: filterValuesDefault.maxBeds,
    relationBeds: 'between',
    minBaths: filterValuesDefault.minBaths,
    maxBaths: filterValuesDefault.maxBaths,
    relationBaths: 'between',
    minSqft: filterValuesDefault.minSqft,
    maxSqft: filterValuesDefault.maxSqft,
    relationSqft: 'between',
    minLotSize: filterValuesDefault.minLotSize,
    maxLotSize: filterValuesDefault.maxLotSize,
    relationLotSize: 'between',
    minYearBuilt: filterValuesDefault.minYearBuilt,
    maxYearBuilt: filterValuesDefault.maxYearBuilt,
    relationYearBuilt: 'between',
    // minCumulativeDaysOnMarket: filterValuesDefault.minCumulativeDaysOnMarket,
    // maxCumulativeDaysOnMarket: filterValuesDefault.maxCumulativeDaysOnMarket,
    // relationCumulativeDaysOnMarket: 'between',
    // minCoveredParking: filterValuesDefault.minCoveredParking,
    // maxCoveredParking: filterValuesDefault.maxCoveredParking,
    // relationCoveredParking: 'greaterThan',
    // checkedPropertySubTypes: filterValuesDefault.checkedPropertySubTypes,
    // selectedPoolAllowed: filterValuesDefault.selectedPoolAllowed,
  };
  // console.log('smartFilterValues', smartFilterValues);
  for (const item of filterExpression) {
    if (item[0] === '>=') {
      if (item[1] === 'bed') {
        smartFilterValues.minBeds = item[2];
      } else if (item[1] === 'bath') {
        smartFilterValues.minBaths = item[2];
      } else if (item[1] === 'size') {
        smartFilterValues.minSqft = item[2];
      } else if (item[1] === 'area_acres') {
        smartFilterValues.minLotSize = item[2];
      } else if (item[1] === 'yearbuilt') {
        smartFilterValues.minYearBuilt = item[2];
      } else if (item[1] === 'cdom') {
        smartFilterValues.minCumulativeDaysOnMarket = item[2];
      } else if (item[1] === 'garage') {
        smartFilterValues.minCoveredParking = item[2];
      }
    } else if (item[0] === '<=') {
      if (item[1] === 'bed') {
        smartFilterValues.maxBeds = item[2];
      } else if (item[1] === 'bath') {
        smartFilterValues.maxBaths = item[2];
      } else if (item[1] === 'size') {
        smartFilterValues.maxSqft = item[2];
      } else if (item[1] === 'area_acres') {
        smartFilterValues.maxLotSize = item[2];
      } else if (item[1] === 'yearbuilt') {
        smartFilterValues.maxYearBuilt = item[2];
      } else if (item[1] === 'cdom') {
        smartFilterValues.maxCumulativeDaysOnMarket = item[2];
      } else if (item[1] === 'garage') {
        smartFilterValues.maxCoveredParking = item[2];
      }
    } else if (item[0] === '==') {
      if (item[1] === 'pool') {
        smartFilterValues.selectedPoolAllowed = item[2];
      }
    } else if (item[0] === 'in') {
      // remove subtype filters
      // if (item[1] === 'propertysubtype') {
      //   smartFilterValues.checkedPropertySubTypes = item[2];
      // }
    }
  }
  // if pool is allowed, there will be no filter expression for pool
  // therefore we need to add it to the values
  // if (filterExpression.some((item) => item[1] === 'pool')) {
  //   smartFilterValues.selectedPoolAllowed = true;
  // }
  return smartFilterValues;
};

// generate filter expression from manual filter values
// used in report.js
// not used in filters
// there's a function with the same name in cma.js and that's the one used in filters
export const generateManualFilterValues = (dataSourceType, options) => {
  const baseFilters = [
    {
      key: dataSourceType === 'MLS' ? 'bed' : 'bed_rooms',
      // name: 'Beds',
      min: options.minBeds,
      max: options.maxBeds,
      relation: options.relationBeds,
    },
    {
      key: dataSourceType === 'MLS' ? 'bath' : 'bath_rooms',
      // name: 'Baths',
      min: options.minBaths,
      max: options.maxBaths,
      relation: options.relationBaths,
    },
    {
      key: dataSourceType === 'MLS' ? 'size' : 'square_feet',
      // name: 'Sqft',
      min: options.minSqft,
      max: options.maxSqft,
      relation: options.relationSqft,
    },
    {
      key: 'area_acres',
      // name: 'Lot Size',
      min: options.minLotSize,
      max: options.maxLotSize,
      relation: options.relationLotSize,
    },
    {
      key: 'yearbuilt',
      // name: 'Year Built',
      min: options.minYearBuilt,
      max: options.maxYearBuilt,
      relation: options.relationYearBuilt,
    },
    {
      key: 'cdom',
      // name: 'Days on Market',
      min: options.minCumulativeDaysOnMarket,
      max: options.maxCumulativeDaysOnMarket,
      relation: options.relationCumulativeDaysOnMarket,
    },
    {
      key: 'garage',
      min: options.minCoveredParking,
      max: options.maxCoveredParking,
      relation: options.relationCoveredParking,
    },
    // {
    //   key: 'propertysubtype',
    // },
  ];
  return baseFilters;
};

// generate filter expression from the manual filter
export const generateManualFilterExpression = (manualFilterValues) => {
  console.log('manualFilterValues', manualFilterValues);
  let filterExpression = [];
  manualFilterValues.forEach((item) => {
    if (Object.hasOwn(item, 'relation')) {
      switch (item.relation) {
        case 'equalTo':
          filterExpression.push(['==', item.key, item.min]);
          break;
        case 'greaterThan':
          filterExpression.push(['>=', item.key, item.min]);
          break;
        case 'lessThan':
        case 'fewerThan':
          filterExpression.push(['<=', item.key, item.max]);
          break;
        case 'between':
          filterExpression.push(
            ['>=', item.key, item.min],
            ['<=', item.key, item.max],
          );
          break;
        default:
          break;
      }
    } else if (Object.hasOwn(item, 'checked')) {
      filterExpression.push(['in', item.key, item.checked]);
    } else if (Object.hasOwn(item, 'selected')) {
      if (item.key === 'pool') {
        // only generate filter expression when pool is not allowed
        // because when pool is allowed, we don't need to filter
        if (item.selected === false) {
          filterExpression.push(['==', item.key, item.selected]);
        }
      } else {
        filterExpression.push(['==', item.key, item.selected]);
      }
    }
  });
  return filterExpression;
};

export const filterDataBySubjectPolygon = ({
  subjectCoordinates,
  data,
  polygons,
}) => {
  let subjectPolygon = {};
  let filteredData = [];

  // 1. Find the subject property district
  const subjectPoint = point([subjectCoordinates[0], subjectCoordinates[1]]);
  for (let i = 0; i < polygons.length; i++) {
    if (booleanPointInPolygon(subjectPoint, polygons[i].geom)) {
      subjectPolygon = polygons[i];
    }
  }

  // 2. Find which property data falls in subject property's district
  for (let i = 0; i < data.length; i++) {
    const propertyPoint = data[i].geography ? data[i].geography : data[i].geom;
    if (booleanPointInPolygon(propertyPoint, subjectPolygon.geom)) {
      filteredData.push(data[i]);
    }
  }

  return filteredData;
};
