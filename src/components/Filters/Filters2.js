import isEmpty from 'lodash.isempty';
import isEqual from 'lodash.isequal';
import moment from 'moment';
import queryString from 'query-string';
import React, { useEffect, useRef, useState } from 'react';
import { connect, history } from 'umi';
import { dateFormat } from '../../constants';
import { getCityCodeViaZIPCode } from '../../utils/geography';
import ComboFiltersWithinSame from './ComboFiltersWithinSame';
import SingleFilterClosingDate from './SingleFilterClosingDate';
import SingleFilterInputMinMax from './SingleFilterInputMinMax';
import SingleFilterSelect from './SingleFilterSelect2';
import SingleFilterSelectMinMax from './SingleFilterSelectMinMax';
import SingleFilterSelectStatus from './SingleFilterSelectStatus';
import SingleFilterYearBuilt from './SingleFilterYearBuilt';
import filterValuesDefault from './filterValuesDefault.json';

// for manual filtering
const Filters = (props) => {
  const [prevCompingMode, setPrevCompingMode] = useState('');
  const [currentCityCode, setCurrentCityCode] = useState('');

  const query = queryString.parse(history.location.search);

  const filterAllCompsManual = () => {
    props.dispatch({
      type: 'CMA/filterMLSAndSFRDataManual',
      payload: {
        dataSourceType: 'MLS',
      },
    });
    props.dispatch({
      type: 'CMA/filterMLSAndSFRDataManual',
      payload: {
        dataSourceType: 'SFR',
      },
    });
    props.dispatch({
      type: 'CMA/filterMLSAndSFRDataManual',
      payload: {
        dataSourceType: 'HotPads',
      },
    });
  };

  // isCustomRange is to indicate whether user changed the range value manually
  // if so the 2nd cycle in // so the 2nd cycle in CMA/filterMLSAndSFRData should be skipped
  const filterAllCompsSmartFilter = ({ isCustomRange }) => {
    props.dispatch({
      type: 'CMA/filterMLSAndSFRData',
      payload: {
        dataSourceType: 'MLS',
        mode: 'smartFilter',
        isCustomRange: isCustomRange,
      },
    });
    props.dispatch({
      type: 'CMA/filterMLSAndSFRData',
      payload: {
        dataSourceType: 'SFR',
        mode: 'smartFilter',
        isCustomRange: isCustomRange,
      },
    });
    props.dispatch({
      type: 'CMA/filterMLSAndSFRData',
      payload: {
        dataSourceType: 'HotPads',
        mode: 'smartFilter',
        isCustomRange: isCustomRange,
      },
    });
  };

  const onChangeFilterValue = (key, value, rangeKey) => {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        [key]: value,
        ...(rangeKey && { [rangeKey]: null }), // after user manually changed filter value, set range as custom
      },
    });
    filterAllCompsManual();
  };

  const onFilterReset = () => {
    console.log('reset all filters');
    props.dispatch({
      type: 'CMA/updateFilterValues',
      payload: {
        ...filterValuesDefault,
        minRentPrice: filterValuesDefault.minRentPrice,
        maxRentPrice: filterValuesDefault.maxRentPrice,
        minSoldPrice: filterValuesDefault.minSoldPrice,
        maxSoldPrice: filterValuesDefault.maxSoldPrice,
        isDistrictFilterOn: false,
        isZipCodeFilterOn: false,
        isCountyFilterOn: false,
        selectedPoolAllowed: true,
        currentStatusMLS: 'status',
        currentStartMLS: moment().subtract(365, 'days').format(dateFormat),
        currentEndMLS: moment().format(dateFormat),
      },
    });
    filterAllCompsManual();
  };

  const onChangeRangeValue = (key, value) => {
    console.log('onChangeRangeValue', key, value);
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        [key]: value,
      },
    });
    filterAllCompsSmartFilter({
      isCustomRange: true, // so the 2nd cycle will be skipped in CMA/filterMLSAndSFRData
    });
  };

  if (props.compingMode !== prevCompingMode) {
    // console.log('prevCompingMode', prevCompingMode);
    if (props.compingMode === 'noFilter') {
      // if url has queries
      // it means current subject property is loaded from url
      // therefore, do not overwrite filter values from url with default filter values
      if (
        !(
          Object.hasOwn(history.location, 'search') &&
          Object.hasOwn(query, 'maxBeds')
        ) &&
        !Object.hasOwn(query, 'selectedFilter')
      ) {
        onFilterReset();
      }
    } else {
      filterAllCompsManual();
    }
    setPrevCompingMode(props.compingMode);
  }

  useEffect(() => {
    if (isEmpty(props.currentPropertyAddress)) {
      return;
    } else if (Object.hasOwn(props.currentPropertyAddress, 'postalCode')) {
      const incomingCityCode = getCityCodeViaZIPCode(
        props.currentPropertyAddress.postalCode,
      );
      if (incomingCityCode !== currentCityCode) {
        setCurrentCityCode(incomingCityCode);
      }
    }
  }, [props.currentPropertyAddress]);

  const hideFilterModal = () => {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        showFilterSideBar: false,
      },
    });
  };

  return (
    <div
      key="filters wrapper"
      id="filtersWrapper"
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-start',
        justifyContent: 'flex-start',
        gap: 16,
      }}
    >
      <SingleFilterSelectStatus />

      <SingleFilterClosingDate />

      {props.searchingMode != 'Land' && <ComboFiltersWithinSame />}

      {props.compingMode !== 'intelligentComping' &&
        props.searchingMode != 'Land' && (
          <>
            <SingleFilterInputMinMax
              filterName={
                props.searchingMode === 'Lease' ? 'RentPrice' : 'SoldPrice'
              }
              filterLabel={
                props.searchingMode === 'Lease' ? 'Rent Price' : 'Sold Price'
              }
              min={
                props.searchingMode === 'Lease'
                  ? props.minRentPrice
                  : props.minSoldPrice
              }
              max={
                props.searchingMode === 'Lease'
                  ? props.maxRentPrice
                  : props.maxSoldPrice
              }
              onChangeFilterValue={onChangeFilterValue}
              disabled={props.compingMode === 'intelligentComping'}
              compingMode={props.compingMode}
              unit="dollar"
              isLeaseMode={props.searchingMode === 'Lease'}
            />
          </>
        )}

      {props.searchingMode != 'Land' && (
        <SingleFilterSelectMinMax
          filterName="Beds"
          min={props.minBeds}
          max={props.maxBeds}
          onChangeFilterValue={onChangeFilterValue}
          disabled={props.compingMode === 'intelligentComping'}
          compingMode={props.compingMode}
          range={props.rangeBeds}
          onChangeRangeValue={onChangeRangeValue}
        />
      )}

      {props.searchingMode != 'Land' && (
        <SingleFilterSelectMinMax
          filterName="Baths"
          min={props.minBaths}
          max={props.maxBaths}
          onChangeFilterValue={onChangeFilterValue}
          disabled={props.compingMode === 'intelligentComping'}
          compingMode={props.compingMode}
          range={props.rangeBaths}
          onChangeRangeValue={onChangeRangeValue}
        />
      )}

      {props.searchingMode != 'Land' && (
        <SingleFilterSelectMinMax
          filterName="Sqft"
          min={props.minSqft}
          max={props.maxSqft}
          onChangeFilterValue={onChangeFilterValue}
          disabled={props.compingMode === 'intelligentComping'}
          compingMode={props.compingMode}
          range={props.rangeSqft}
          onChangeRangeValue={onChangeRangeValue}
        />
      )}

      {props.searchingMode != 'Land' && (
        <SingleFilterSelectMinMax
          filterLabel="Lot Size"
          filterName="LotSize"
          min={props.minLotSize}
          max={props.maxLotSize}
          onChangeFilterValue={onChangeFilterValue}
          disabled={props.compingMode === 'intelligentComping'}
          compingMode={props.compingMode}
          range={props.rangeLotSize}
          onChangeRangeValue={onChangeRangeValue}
        />
      )}

      {props.searchingMode != 'Land' && (
        <SingleFilterYearBuilt onChangeRangeValue={onChangeRangeValue} />
      )}
      {props.searchingMode != 'Land' && (
        <SingleFilterSelectMinMax
          filterLabel="Cumulative DOM"
          filterName="CumulativeDaysOnMarket"
          min={props.minCumulativeDaysOnMarket}
          max={props.maxCumulativeDaysOnMarket}
          onChangeFilterValue={onChangeFilterValue}
          disabled={props.compingMode === 'intelligentComping'}
        />
      )}

      {props.searchingMode != 'Land' && (
        <SingleFilterSelectMinMax
          filterLabel="Covered Parking"
          filterName="CoveredParking"
          min={props.minCoveredParking}
          max={props.maxCoveredParking}
          onChangeFilterValue={onChangeFilterValue}
          disabled={props.compingMode === 'intelligentComping'}
        />
      )}
      {props.searchingMode != 'Land' && (
        <SingleFilterSelect
          filterLabel="Pool Allowed"
          filterName="PoolAllowed"
          value={props.selectedPoolAllowed}
          onChangeFilterValue={onChangeFilterValue}
          disabled={props.compingMode === 'intelligentComping'}
        />
      )}
    </div>
  );
};

export default connect(({ CMA }) => ({
  eventCoordinates: CMA.eventCoordinates,
  // isLeaseMode: CMA.isLeaseMode,
  searchingMode: CMA.searchingMode,
  compingMode: CMA.compingMode,
  currentPropertyAddress: CMA.currentPropertyAddress,
  // filter values
  minBeds: CMA.minBeds,
  maxBeds: CMA.maxBeds,
  relationBeds: CMA.relationBeds,
  minBaths: CMA.minBaths,
  maxBaths: CMA.maxBaths,
  relationBaths: CMA.relationBaths,
  minSqft: CMA.minSqft,
  maxSqft: CMA.maxSqft,
  relationSqft: CMA.relationSqft,
  minLotSize: CMA.minLotSize,
  maxLotSize: CMA.maxLotSize,
  relationLotSize: CMA.relationLotSize,
  minYearBuilt: CMA.minYearBuilt,
  maxYearBuilt: CMA.maxYearBuilt,
  relationYearBuilt: CMA.relationYearBuilt,
  minCumulativeDaysOnMarket: CMA.minCumulativeDaysOnMarket,
  maxCumulativeDaysOnMarket: CMA.maxCumulativeDaysOnMarket,
  relationCumulativeDaysOnMarket: CMA.relationCumulativeDaysOnMarket,
  minCoveredParking: CMA.minCoveredParking,
  maxCoveredParking: CMA.maxCoveredParking,
  relationCoveredParking: CMA.relationCoveredParking,
  minRentPrice: CMA.minRentPrice,
  maxRentPrice: CMA.maxRentPrice,
  minAVMToRentPrice: CMA.minAVMToRentPrice,
  maxAVMToRentPrice: CMA.maxAVMToRentPrice,
  minSoldPrice: CMA.minSoldPrice,
  maxSoldPrice: CMA.maxSoldPrice,
  minAVMToSoldPrice: CMA.minAVMToSoldPrice,
  maxAVMToSoldPrice: CMA.maxAVMToSoldPrice,
  rangeBeds: CMA.rangeBeds,
  rangeBaths: CMA.rangeBaths,
  rangeSqft: CMA.rangeSqft,
  rangeLotSize: CMA.rangeLotSize,
  selectedPoolAllowed: CMA.selectedPoolAllowed,
  userGroup: CMA.userGroup,
  selectedUserGroup: CMA.selectedUserGroup,
}))(Filters);
