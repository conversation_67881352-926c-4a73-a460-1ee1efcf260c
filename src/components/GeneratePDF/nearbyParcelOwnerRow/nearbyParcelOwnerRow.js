import { toPng } from 'html-to-image';
import {
  doc,
  pdfPageWidth,
  pdfSideMargin,
  pdfGap,
  pdfContentPadding,
  lastYAxisPos,
  setLastYAxisPos,
} from '../GeneratePDF';

export const createNearbyParcelOwnerSummary = async (content) => {
  const rowStartY = lastYAxisPos + pdfContentPadding;
  const contentStartX = pdfSideMargin + pdfContentPadding;
  const contentEndX = pdfPageWidth - pdfSideMargin - pdfContentPadding;
  let text, lineY, lineX;

  doc.setFontSize(14);
  doc.setFont('helvetica', 'normal');
  text = `${content.rowName}`;
  lineY = rowStartY + pdfContentPadding + doc.getTextDimensions(text).h - 2;
  doc.text(text, contentStartX, lineY);

  doc.setFontSize(10);
  doc.setFont('helvetica', 'bold');
  text = `${content.renter.count}(${Math.round(content.renter.percent)}%) / ${
    content.owner.count
  }(${Math.round(content.owner.percent)}%)`;
  lineX = contentEndX - doc.getTextDimensions(text).w;
  doc.text(text, lineX, lineY);

  doc.setFontSize(8);
  doc.setFont('helvetica', 'normal');
  text = 'Renter/Owner Occupied';
  lineX = lineX - 5 - doc.getTextDimensions(text).w;
  doc.text(text, lineX, lineY);

  doc.setFontSize(10);
  doc.setFont('helvetica', 'bold');
  text = `${content.totalParcels}`;
  lineX = lineX - doc.getTextDimensions(text).w - 10;
  doc.text(text, lineX, lineY);

  doc.setFontSize(8);
  doc.setFont('helvetica', 'normal');
  text = 'Total ';
  lineX = lineX - 5 - doc.getTextDimensions(text).w;
  doc.text(text, lineX, lineY);

  lineY = lineY + 5;

  const leftHeaderLineAxis = [contentStartX, lineY];
  const rightHeaderLineAxis = [contentEndX, lineY];
  doc.line(...leftHeaderLineAxis, ...rightHeaderLineAxis); // Header text underline

  doc.setFontSize(10);
  text = '% Based on renter occupied';
  lineY = lineY + doc.getTextDimensions(text).h + 4;
  doc.text(text, contentStartX, lineY);

  const { institutions } = content;
  const containerWidth = contentEndX - contentStartX;
  const positionIncrement = containerWidth / institutions.length;
  lineX = positionIncrement / 2;
  lineY = lineY + 10;
  const progressWidth = 30;
  const progressHeight = progressWidth;

  for (let i = 0; i < institutions.length; i++) {
    const dataURL = await toPng(
      document.querySelector(
        `#${institutions[i].institution.split(' ')[0]}_progress_donut`,
      ),
    );
    if (dataURL) {
      const img = new Image();
      img.src = dataURL;
      doc.addImage(
        img,
        'png',
        lineX + progressWidth / 2,
        lineY,
        progressWidth,
        progressHeight,
        '',
        'FAST',
      );

      doc.setFontSize(8);
      text = `${institutions[i].institution}`;
      text =
        doc.getTextDimensions(text).w > progressWidth + 5
          ? `${institutions[i].institution.replace(/\s/g, `\n`)}`
          : text;
      text = `${text}\n${institutions[i].percent.toFixed(1)}%`;
      doc.text(
        text,
        lineX + progressWidth,
        lineY + progressHeight + 10,
        'center',
      );

      lineX += positionIncrement;
    }
  }

  const topLeftAxis = [pdfSideMargin, rowStartY];
  const topRightAxis = [pdfPageWidth - pdfSideMargin, rowStartY];
  const bottomLeftAxis = [
    pdfSideMargin,
    lineY + progressHeight + 25 + pdfContentPadding,
  ];
  const bottomRightAxis = [
    pdfPageWidth - pdfSideMargin,
    lineY + progressHeight + 25 + pdfContentPadding,
  ];

  doc.line(...topLeftAxis, ...topRightAxis); // Top border
  doc.line(...topRightAxis, ...bottomRightAxis); // Right border
  doc.line(...bottomRightAxis, ...bottomLeftAxis); // Bottom border
  doc.line(...bottomLeftAxis, ...topLeftAxis); // Left border

  setLastYAxisPos(lineY + progressHeight + 25 + pdfContentPadding);
};
