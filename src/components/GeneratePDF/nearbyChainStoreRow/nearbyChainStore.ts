import autoTable from 'jspdf-autotable';
import {
  doc,
  footerHeight,
  lastYAxisPos,
  pdfContentPadding,
  pdfGap,
  pdfPageHeight,
  pdfPageWidth,
  pdfSideMargin,
  setLastYAxisPos,
} from '../GeneratePDF';

interface Chainstore {
  hash_id: string;
  chain_id: string;
  chain_name: string;
  address: string;
  category: string;
  first_appeared: Date;
  geometry: Geometry;
  distance: number;
}

interface Geometry {
  type: string;
  coordinates: number[];
}

interface ChainStoreContent {
  selectedMiles: number;
  data: Chainstore[];
}

type StoreTuple = [string, string];

function formatter(content: ChainStoreContent) {
  let stores: StoreTuple[] = [];
  if (content.data.length > 0) {
    content.data.forEach((store: Chainstore) => {
      stores.push([store.chain_name, store.distance.toFixed(2) + ' mi']);
    });
  }
  return stores;
}

export const createNearbyChainStoreRow = (content: ChainStoreContent) => {
  if (!content) return;

  const formattedContent = formatter(content);
  const numberOfRows = formattedContent.length;
  const tableHeaderRowHeight = 12.5;
  const tableBodyRowHeight = 15;
  // Basic configuration
  const spaceNeededForRow = 115; // You might not need this hardcoded value anymore
  let rowStartY;

  // Page management: checking for need to add new page
  if (
    pdfPageHeight - lastYAxisPos - (pdfSideMargin * 2 + 10) <
    spaceNeededForRow
  ) {
    rowStartY = pdfSideMargin * 2 + 10;
    doc.addPage();
  } else {
    rowStartY =
      lastYAxisPos + (lastYAxisPos === 0 ? pdfSideMargin * 2 + 10 : pdfGap);
  }

  // Setting initial line position for top border
  let topLeftAxis = [pdfSideMargin, rowStartY];
  let topRightAxis = [pdfPageWidth - pdfSideMargin, rowStartY];

  // Draw the top border of the section
  doc.line(...topLeftAxis, ...topRightAxis);

  // Content starting positions
  const contentStartX = pdfSideMargin + pdfContentPadding;
  const contentEndX = pdfPageWidth - pdfSideMargin - pdfContentPadding;
  let lineY = rowStartY + pdfContentPadding;

  // Writing section header
  doc.setFontSize(14);
  doc.setFont('helvetica', 'normal');
  console.log('favorable ', content);
  let text = `Favorable Brands Nearby within ${content.selectedMiles} ${
    content.selectedMiles <= 1 ? 'mile' : 'miles'
  } (${content.data.length} results)`;
  lineY = rowStartY + pdfContentPadding + doc.getTextDimensions(text).h - 2;
  doc.text(text, contentStartX, lineY);

  lineY = lineY + 5;
  const leftHeaderLineAxis = [contentStartX, lineY];
  const rightHeaderLineAxis = [contentEndX, lineY];
  doc.line(...leftHeaderLineAxis, ...rightHeaderLineAxis); // Header text underline
  lineY += 5; // Space after the header
  let startY = lineY + 5;
  // Initialize table
  autoTable(doc, {
    startY: lineY,
    headStyles: {
      fillColor: [229, 229, 229],
      textColor: [0, 0, 0],
      minCellHeight: tableHeaderRowHeight,
      valign: 'middle',
      lineWidth: 1,
      // cellPadding: content.rowName === 'Multi-Family' || 2,
    },
    bodyStyles: {
      fontSize: 8,
      minCellHeight: tableBodyRowHeight,
      valign: 'middle',
      lineWidth: 1,
      // cellPadding: content.rowName === 'Multi-Family' || 2,
    },
    margin: { horizontal: pdfSideMargin + pdfContentPadding },
    head: [['Chain Name', 'Distance']],
    body: formattedContent,
  });
  lineY =
    startY +
    pdfContentPadding +
    pdfContentPadding +
    tableBodyRowHeight * numberOfRows +
    tableHeaderRowHeight;

  const bottomLeftAxis = [pdfSideMargin, lineY];
  const bottomRightAxis = [pdfPageWidth - pdfSideMargin, lineY];

  doc.setDrawColor(0, 0, 0);

  doc.line(...topLeftAxis, ...bottomLeftAxis); // Left Border
  doc.line(...topRightAxis, ...bottomRightAxis); // Right Border
  doc.line(...bottomRightAxis, ...bottomLeftAxis); // Bottom border

  setLastYAxisPos(lineY);
};
