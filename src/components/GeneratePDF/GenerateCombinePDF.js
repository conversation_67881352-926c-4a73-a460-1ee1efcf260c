import styles from '@/components/ResultTable/resultTable.css';
import { userGroupHasAccess } from '@/utils/userGroup';
import { Tooltip } from 'antd';
import isEmpty from 'lodash.isempty';
import { PDFDocument, PDFPage } from 'pdf-lib';
import { useEffect, useState } from 'react';
import { useSelector } from 'umi';
import { serverType } from '../../services/data';
import GeneratePDF from './GeneratePDF';
import InsightsPDF from './InsightsPDF';

const mergePdfs = async (pdfsToMerge) => {
  const mergedPdf = await PDFDocument.create();

  const createInnerPromise = async (arrayBuffer) => {
    const pdf = await PDFDocument.load(arrayBuffer);
    return await mergedPdf.copyPages(pdf, pdf.getPageIndices());
  };

  const outerPromise = pdfsToMerge.map((arrayBuffer) => {
    const innerPromise = createInnerPromise(arrayBuffer);
    return innerPromise;
  });

  const resultOuterPromise = await Promise.all(outerPromise);

  resultOuterPromise.forEach((pageArray) => {
    pageArray.forEach((page) => {
      mergedPdf.addPage(page);
    });
  });

  return (await mergedPdf.save()).buffer;
};

const GenerateCombinePDF = () => {
  const [pdfArray, setPdfArray] = useState([]);
  const [generateQueue, setGenerateQueue] = useState(null); // null, '1', '2', 'done'
  const eventCoordinates = useSelector((state) => state.CMA.eventCoordinates);
  const currentRadiusMile = useSelector((state) => state.CMA.currentRadiusMile);
  const drawnCustomPolygons = useSelector(
    (state) => state.CMA.drawnCustomPolygons,
  );
  const searchingMode = useSelector((state) => state.CMA.searchingMode);
  const marketConditionStartDate = useSelector(
    (state) => state.CMA.marketConditionStartDate,
  );
  const marketConditionEndDate = useSelector(
    (state) => state.CMA.marketConditionEndDate,
  );
  const chartArea = useSelector((state) => state.CMA.chartArea);
  const userGroup = useSelector((state) => state.CMA.userGroup);
  const selectedUserGroup = useSelector((state) => state.CMA.selectedUserGroup);

  const startGenerating = (e) => {
    e.preventDefault();
    setPdfArray([]);
    setGenerateQueue('1');
  };

  const {
    MKnumberOfMonths: numberOfMonths,
    MKendYear: endYear,
    MKendMonth: endMonth,
  } = useSelector((state) => state.CMA);

  useEffect(() => {
    if (generateQueue && generateQueue === 'done' && pdfArray.length === 2) {
      const mergePDF = async () => {
        const result = await mergePdfs(pdfArray);
        const pdfUrl = URL.createObjectURL(
          new Blob([result], { type: 'application/pdf' }),
        );

        window.open(pdfUrl);
        setPdfArray([]);
        setGenerateQueue(null);
      };
      mergePDF();
    }
  }, [pdfArray, generateQueue]);

  return (
    <div>
      <Tooltip
        id="print-tooltip"
        title={
          !userGroupHasAccess(selectedUserGroup, 'Comp PDF')
            ? 'Please contact us for a demo'
            : isEmpty(eventCoordinates)
            ? 'Please get a comp first'
            : 'Print or Save PDF'
        }
      >
        {/* <button className={styles.buttonWrapperSummaryRow} onClick={createPDF}> */}
        <button
          className={styles.buttonWrapperSummaryRowText}
          // shape="circle"
          onClick={startGenerating}
          disabled={
            isEmpty(eventCoordinates) ||
            !userGroupHasAccess(selectedUserGroup, 'Comp PDF')
          }
        >
          <span className={styles.headerBarButton}>PDF Report</span>
        </button>
      </Tooltip>

      <GeneratePDF
        source="combine"
        combineState={{ generateQueue, setPdfArray, setGenerateQueue }}
      />
      <InsightsPDF
        source="combine"
        combineState={{ generateQueue, setPdfArray, setGenerateQueue }}
        lat={eventCoordinates[1] || undefined}
        lng={eventCoordinates[0] || undefined}
        radius={currentRadiusMile || undefined}
        customPolygon={drawnCustomPolygons || undefined}
        isLeaseMode={searchingMode === 'Lease'}
        // marketStartDate={marketConditionStartDate || undefined}
        // marketEndDate={marketConditionEndDate || undefined}
        numberOfMonths={numberOfMonths || undefined}
        endYear={endYear || undefined}
        endMonth={endMonth || undefined}
        // chartArea={chartArea || undefined}
        chartArea={'aoi'}
        serverType={serverType}
        userGroup={userGroup.includes('Sunroom') ? 'Sunroom' : 'demo-users'} // Sunroom uses their own logo, while all other clients use LocateAlpha logo
      />
    </div>
  );
};

export default GenerateCombinePDF;
