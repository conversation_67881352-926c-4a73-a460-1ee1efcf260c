import {
  doc,
  lastYAxisPos,
  pdfContentPadding,
  pdfGap,
  pdfPageWidth,
  pdfSideMargin,
  setLastYAxisPos,
} from '../GeneratePDF';

export const createNearbySaleSummary = async (dom, content) => {
  // console.log(content);
  const rowStartY = lastYAxisPos + pdfGap;
  const contentStartX = pdfSideMargin + pdfContentPadding;
  const contentEndX = pdfPageWidth - pdfSideMargin - pdfContentPadding;
  let text, lineY, lineX, yIncrement;

  doc.setFontSize(14);
  doc.setFont('helvetica', 'normal');
  text = `${content.rowName} | Median DOM: ${dom}`;
  lineY = rowStartY + pdfContentPadding + doc.getTextDimensions(text).h - 2;
  doc.text(text, contentStartX, lineY);

  lineY = lineY + 5;
  const leftHeaderLineAxis = [contentStartX, lineY];
  const rightHeaderLineAxis = [contentEndX, lineY];
  doc.line(...leftHeaderLineAxis, ...rightHeaderLineAxis); // Header text underline

  const saleValues = content.content;
  const containerWidth = contentEndX - contentStartX;
  const positionIncrement = containerWidth / saleValues.length;
  const topBottomContentMargin = 15;
  lineX = contentStartX + positionIncrement / 2;
  lineY = lineY + topBottomContentMargin;

  for (let i = 0; i < saleValues.length; i++) {
    if (saleValues[i].title.includes('Market')) {
      if (saleValues[i].text === 'Manual Review') {
        doc.setTextColor(255, 0, 0);
      } else {
        doc.setTextColor(0, 126, 63);
      }
    }
    doc.setFontSize(9);
    doc.setFont('helvetica', 'normal');
    text = saleValues[i].title;
    doc.text(text, lineX, lineY, 'center');

    yIncrement = doc.getTextDimensions(text).h;
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    text = saleValues[i].text;
    yIncrement += doc.getTextDimensions(text).h + 3;
    // text === 'Manual Review' && doc.setTextColor(255, 0, 0);
    doc.text(text, lineX, lineY + yIncrement, 'center');
    // text === 'Manual Review' && doc.setTextColor(0, 0, 0);

    doc.setFontSize(9);
    doc.setFont('helvetica', 'normal');
    text = saleValues[i].subText;
    yIncrement += doc.getTextDimensions(text).h + 3;
    doc.text(text, lineX, lineY + yIncrement, 'center');
    doc.setTextColor(0, 0, 0);

    if (saleValues[i].title.includes('Market')) {
      text = saleValues[i].formula;
      yIncrement += doc.getTextDimensions(text).h + 3;
      doc.text(text, lineX, lineY + yIncrement, 'center');
    }

    lineX += positionIncrement;
  }

  const topLeftAxis = [pdfSideMargin, rowStartY];
  const topRightAxis = [pdfPageWidth - pdfSideMargin, rowStartY];
  const bottomLeftAxis = [
    pdfSideMargin,
    lineY + yIncrement + topBottomContentMargin - doc.getTextDimensions(text).h,
  ];
  const bottomRightAxis = [
    pdfPageWidth - pdfSideMargin,
    lineY + yIncrement + topBottomContentMargin - doc.getTextDimensions(text).h,
  ];

  doc.line(...topLeftAxis, ...topRightAxis); // Top border
  doc.line(...topRightAxis, ...bottomRightAxis); // Right border
  doc.line(...bottomRightAxis, ...bottomLeftAxis); // Bottom border
  doc.line(...bottomLeftAxis, ...topLeftAxis); // Left border

  setLastYAxisPos(
    lineY + yIncrement + topBottomContentMargin - doc.getTextDimensions(text).h,
  );
};
