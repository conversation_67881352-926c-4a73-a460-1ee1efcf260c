import autoTable from 'jspdf-autotable';
import {
  doc,
  pdfPageWidth,
  pdfPageHeight,
  pdfSideMargin,
  pdfGap,
  pdfContentPadding,
  footerHeight,
  lastYAxisPos,
  setLastYAxisPos,
} from '../GeneratePDF';
import { formatCurrency } from '@/utils/lastSalePublicRecord';

export interface Content {
  raw_fld_zone: string;
  fld_zone: string;
  medianhhincome: string;
  fiveyearpopgrowth: number;
  fiveyearincomegrowth: number;
  bachelorsandabove: number;
  crime_score: number;
  crime: number;
  white: number;
  black: number;
  asian: number;
  hispanic: number;
  school: School;
}

export interface School {
  elem: Elem;
  high: Elem;
  middle: Elem;
}

export interface Elem {
  score_2022: number;
  score_2023: number;
  name: string;
}

function dataFormatter(content: Content) {
  let risk = {
    floodZone: '',
    crime_score: `${content.crime_score} out of 10`,
  };
  let demographics = {
    medianhhincome: formatCurrency(content.medianhhincome),
    fiveyearpopgrowth: content.fiveyearpopgrowth.toFixed(1) + '%',
    fiveyearincomegrowth: content.fiveyearincomegrowth.toFixed(1) + '%',
    bachelorsandabove: content.bachelorsandabove.toFixed(1) + '%',
  };
  let school = {
    elem: {
      name: content.school.elem.name,
      score_2022: content.school.elem.score_2022,
      score_2023: content.school.elem.score_2023,
    },
    middle: {
      name: content.school.middle.name,
      score_2022: content.school.middle.score_2022,
      score_2023: content.school.middle.score_2023,
    },
    high: {
      name: content.school.high.name,
      score_2022: content.school.high.score_2022,
      score_2023: content.school.high.score_2023,
    },
  };
  if (content.fld_zone === 'N') risk.floodZone = 'No';
  if (content.fld_zone === 'Y') risk.floodZone = 'Yes';

  return {
    demographics: [
      ['Median Household Income', demographics.medianhhincome],
      ['5-Year Income Growth', demographics.fiveyearincomegrowth],
      ['5-Year Population Growth', demographics.fiveyearpopgrowth],
      ['Bachelors and Above ', demographics.bachelorsandabove],
    ],
    risk: [
      ['Flood Zone', risk.floodZone],
      ['Crime Score', risk.crime_score],
    ],
    school: [
      [
        'Elementary',
        // school.elem.name,
        school.elem.score_2023,
        school.elem.score_2022,
      ],
      [
        'Middle',
        // school.middle.name,
        school.middle.score_2023,
        school.middle.score_2022,
      ],
      [
        'High',
        // school.high.name,
        school.high.score_2023,
        school.high.score_2022,
      ],
    ],
  };
}

export const createSchoolScoreDemographicRow = (content: Content) => {
  console.log('testv5, createSchoolScoreDemographicRow content: ', content);
  if (!content) return;

  const formattedContent = dataFormatter(content);
  console.log(
    'testv5, createSchoolScoreDemographicRow content: ',
    formattedContent,
  );

  const spaceNeededForRow = 115;
  let topLeftAxis, topRightAxis;
  let rowStartY;

  // Check whether a new page needs to be added and set the y position
  if (
    pdfPageHeight - lastYAxisPos - (pdfSideMargin * 2 + 10) <
    spaceNeededForRow
  ) {
    rowStartY = pdfSideMargin * 2 + 10;
    doc.addPage();
  } else {
    rowStartY =
      lastYAxisPos + (lastYAxisPos === 0 ? pdfSideMargin * 2 + 10 : pdfGap); //+ 10;
  }

  topLeftAxis = [pdfSideMargin, rowStartY];
  topRightAxis = [pdfPageWidth - pdfSideMargin, rowStartY];

  doc.line(...topLeftAxis, ...topRightAxis); // Top border

  const contentStartX = pdfSideMargin + pdfContentPadding;
  const contentEndX = pdfPageWidth - pdfSideMargin - pdfContentPadding;
  let text, lineY, lineX, yIncrement;

  doc.setFontSize(14);
  doc.setFont('helvetica', 'normal');
  text = 'School Score and Demographics';
  lineY = rowStartY + pdfContentPadding + doc.getTextDimensions(text).h - 2;
  doc.text(text, contentStartX, lineY);

  lineY = lineY + 5;
  const leftHeaderLineAxis = [contentStartX, lineY];
  const rightHeaderLineAxis = [contentEndX, lineY];
  doc.line(...leftHeaderLineAxis, ...rightHeaderLineAxis); // Header text underline

  const tableHeaderRowHeight = 12.5;
  const tableBodyRowHeight = 15;

  let startY = lineY + 5;

  // console.log(pdfPageWidth);
  // page width(459) - two side margin(2*25) - two spacing (2*10) / 3 (tables) = 129.6 per table
  const tableWidth = 129.6;

  const table1Start = pdfSideMargin + pdfContentPadding;
  const table1End =
    pdfPageWidth - (pdfSideMargin + pdfContentPadding + tableWidth);
  const table2Start = table1Start + tableWidth + pdfContentPadding;
  const table2End = table1End - pdfContentPadding - tableWidth;
  const table3Start = table2Start + tableWidth + pdfContentPadding;
  const table3End = pdfSideMargin + pdfContentPadding;

  autoTable(doc, {
    headStyles: {
      fillColor: [229, 229, 229],
      textColor: [0, 0, 0],
      minCellHeight: tableHeaderRowHeight,
      valign: 'middle',
      lineWidth: 1,
      // cellPadding: content.rowName === 'Multi-Family' || 2,
    },
    bodyStyles: {
      fontSize: 8,
      minCellHeight: tableBodyRowHeight,
      valign: 'middle',
      lineWidth: 1,
      // cellPadding: content.rowName === 'Multi-Family' || 2,
    },
    startY: startY,
    margin: {
      left: pdfSideMargin + pdfContentPadding,
      right: pdfPageWidth - (pdfSideMargin + pdfContentPadding + tableWidth),
    },
    head: getTableHeaderData('School Scores'),
    body: formattedContent.school,
  });

  autoTable(doc, {
    headStyles: {
      fillColor: [229, 229, 229],
      textColor: [0, 0, 0],
      minCellHeight: tableHeaderRowHeight,
      valign: 'middle',
      lineWidth: 1,
      // cellPadding: content.rowName === 'Multi-Family' || 2,
    },
    bodyStyles: {
      fontSize: 8,
      minCellHeight: tableBodyRowHeight,
      valign: 'middle',
      lineWidth: 1,
      // cellPadding: content.rowName === 'Multi-Family' || 2,
    },
    startY: startY,
    margin: {
      left: table2Start,
      right: table2End,
    },
    head: getTableHeaderData('Risk Factors'),
    body: formattedContent.risk,
  });

  autoTable(doc, {
    headStyles: {
      fillColor: [229, 229, 229],
      textColor: [0, 0, 0],
      minCellHeight: tableHeaderRowHeight,
      valign: 'middle',
      lineWidth: 1,
      // cellPadding: content.rowName === 'Multi-Family' || 2,
    },
    bodyStyles: {
      fontSize: 8,
      minCellHeight: tableBodyRowHeight,
      valign: 'middle',
      lineWidth: 1,
      // cellPadding: content.rowName === 'Multi-Family' || 2,
    },
    startY: startY,
    margin: {
      left: table3Start,
      right: table3End,
    },
    head: getTableHeaderData('Demographics'),
    body: formattedContent.demographics,
  });

  lineY =
    startY +
    tableHeaderRowHeight +
    4 * tableBodyRowHeight +
    pdfContentPadding +
    4;

  const bottomLeftAxis = [pdfSideMargin, lineY];
  const bottomRightAxis = [pdfPageWidth - pdfSideMargin, lineY];

  doc.setDrawColor(0, 0, 0);

  doc.line(...topLeftAxis, ...bottomLeftAxis); // Left Border
  doc.line(...topRightAxis, ...bottomRightAxis); // Right Border
  doc.line(...bottomRightAxis, ...bottomLeftAxis); // Bottom border

  setLastYAxisPos(lineY);
};

const getTableHeaderData = (tableName: string) => {
  if (tableName === 'School Scores') {
    return [
      [
        {
          colSpan: 1,
          content: 'School',
          styles: { halign: 'center' },
        },
        {
          colSpan: 1,
          content: '2023',
          styles: { halign: 'center' },
        },
        {
          colSpan: 1,
          content: '2022',
          styles: { halign: 'center' },
        },
      ],
    ];
  } else if (tableName === 'Risk Factors') {
    return [
      [
        {
          colSpan: 2,
          content: 'Risk Factors',
          styles: { halign: 'center' },
        },
      ],
    ];
  } else if (tableName === 'Demographics') {
    return [
      [
        {
          colSpan: 2,
          content: 'Demographics',
          styles: { halign: 'center' },
        },
      ],
    ];
  }
};
