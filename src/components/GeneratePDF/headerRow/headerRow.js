import { doc, pdfPageWidth, setLastYAxisPos } from '../GeneratePDF';
// import { getClientInformation } from '../../../utils/userGroup';

const processImage = (src) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = src;
    img.onload = () => resolve(img);
    img.onerror = reject;
  });
};

export const createHeader = async (content) => {
  const { clientInfo } = content;
  // const clientInfo = getClientInformation(userGroup);
  // const clientInfo = getClientInformation(['BridgeTower']);
  // const clientInfo = getClientInformation(['CommonGroundCapital']);
  // const clientInfo = getClientInformation(['MarketplaceHomes']);
  // const clientInfo = getClientInformation(['DivvyHomes']);
  // const clientInfo = getClientInformation(['RealCo']);

  // Adding the client logo
  const bottomPosition = 60;
  let width,
    height,
    yPos = 0;

  if (clientInfo && clientInfo.clientName) {
    const img = await processImage(clientInfo.png);

    // Max logo width 150
    const headerEndY = 60;
    const maxLogoWidth = 150;
    const maxLogoHeight = headerEndY - 15;

    if (img.width > maxLogoWidth) {
      img.height = img.height * (maxLogoWidth / img.width);
      img.width = maxLogoWidth;
    }

    if (img.height > maxLogoHeight) {
      img.width = img.width * (maxLogoHeight / img.height);
      img.height = maxLogoHeight;
    }

    let yPos = headerEndY - img.height;

    // prettier-ignore
    doc.addImage(img,'png',15,yPos,img.width,img.height,'','FAST');
  }

  // Adding the report title
  doc.setFontSize(16);
  doc.setFont('helvetica', 'bold');
  doc.text(content.title, pdfPageWidth / 2, bottomPosition, 'center');

  doc.setFontSize(12);
  doc.setFont('helvetica', 'normal');
  const lineHeight = 10.5;
  const { generatedText } = content;
  const splitText = generatedText.split(`\n`);
  const longest = splitText.reduce((a, b) => (a.length > b.length ? a : b));
  const textWidth = doc.getTextWidth(longest);

  doc.text(
    generatedText,
    pdfPageWidth - textWidth / 2 - 15,
    bottomPosition - lineHeight * 2, // 2 new lines
    'center',
  );

  setLastYAxisPos(bottomPosition);
};
