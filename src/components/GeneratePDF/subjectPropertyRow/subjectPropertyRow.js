import {
  doc,
  lastYAxisPos,
  pdfContentPadding,
  pdfGap,
  pdfPageWidth,
  pdfSideMargin,
  setLastYAxisPos,
} from '../GeneratePDF';

export const createSubjectProperty = async (content) => {
  const rowStartY = lastYAxisPos + pdfGap;
  const {
    propertyImage,
    propertyAddress,
    propertyDetail,
    finalRentOpinion,
    finalSalesPriceOpinion,
    comments,
    userGroup,
  } = content;

  const pdfImgWidth = 140;
  const pdfImgHeight =
    propertyImage.height / (propertyImage.width / pdfImgWidth);
  const imgXPos = pdfSideMargin + pdfContentPadding;
  const imgYPos = rowStartY + pdfContentPadding;
  const img = new Image();
  img.src = propertyImage.src;
  doc.addImage(
    img,
    'png',
    imgXPos,
    imgYPos,
    pdfImgWidth,
    pdfImgHeight,
    '',
    'FAST',
  );

  const addressStartX = imgXPos + pdfImgWidth + pdfContentPadding;
  const addressStartY = rowStartY + 25;
  let prevLineWidth, prevLineHeight, prevLineX, prevLineY, lineY, text;

  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  text = `${propertyAddress.streetAddress}`;
  doc.text(text, addressStartX, addressStartY);
  prevLineY = addressStartY;
  prevLineHeight = doc.getTextDimensions(text).h;

  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  text = `${propertyAddress.city}, ${propertyAddress.region} ${propertyAddress.postalCode}`;
  doc.text(text, addressStartX, prevLineY + prevLineHeight);
  prevLineY = prevLineY + prevLineHeight;
  prevLineHeight = doc.getTextDimensions(text).h;

  const detailStartX = imgXPos + pdfImgWidth + pdfContentPadding;
  const detailStartY = prevLineY + prevLineHeight * 2;

  // Line 1
  doc.setFont('helvetica', 'bold');
  text = `${propertyDetail.beds}`;
  doc.text(text, detailStartX, detailStartY);
  prevLineX = detailStartX;
  prevLineWidth = doc.getTextDimensions(text).w;

  doc.setFont('helvetica', 'normal');
  text = ` Beds `;
  doc.text(text, prevLineX + prevLineWidth, detailStartY);
  prevLineX = prevLineX + prevLineWidth;
  prevLineWidth = doc.getTextDimensions(text).w;

  doc.setFont('helvetica', 'bold');
  text = `${propertyDetail.baths}`;
  doc.text(text, prevLineX + prevLineWidth, detailStartY);
  prevLineX = prevLineX + prevLineWidth;
  prevLineWidth = doc.getTextDimensions(text).w;

  doc.setFont('helvetica', 'normal');
  text = ` Baths `;
  doc.text(text, prevLineX + prevLineWidth, detailStartY);
  prevLineX = prevLineX + prevLineWidth;
  prevLineWidth = doc.getTextDimensions(text).w;

  doc.setFont('helvetica', 'bold');
  text = `${propertyDetail.sqft}`;
  doc.text(text, prevLineX + prevLineWidth, detailStartY);
  prevLineX = prevLineX + prevLineWidth;
  prevLineWidth = doc.getTextDimensions(text).w;

  doc.setFont('helvetica', 'normal');
  text = ` Sqft Built in `;
  doc.text(text, prevLineX + prevLineWidth, detailStartY);
  prevLineX = prevLineX + prevLineWidth;
  prevLineWidth = doc.getTextDimensions(text).w;

  doc.setFont('helvetica', 'bold');
  text = `${propertyDetail.builtYear}`;
  doc.text(text, prevLineX + prevLineWidth, detailStartY);

  // Line 2
  prevLineY = detailStartY;
  prevLineHeight = doc.getTextDimensions(text).h;
  lineY = prevLineY + prevLineHeight + 3;

  doc.setFont('helvetica', 'bold');
  text = `${propertyDetail.subdivision} `;
  doc.text(text, detailStartX, lineY);
  prevLineX = detailStartX;
  prevLineWidth = doc.getTextDimensions(text).w;

  doc.setFont('helvetica', 'normal');
  text = propertyDetail.subdivision ? ` | HOA fee ` : `HOA fee `;
  doc.text(text, prevLineX + prevLineWidth, lineY);
  prevLineX = prevLineX + prevLineWidth;
  prevLineWidth = doc.getTextDimensions(text).w;

  doc.setFont('helvetica', 'bold');
  text = `${propertyDetail.hoa}`;
  doc.text(text, prevLineX + prevLineWidth, lineY);

  // Line 3
  prevLineY = lineY;
  prevLineHeight = doc.getTextDimensions(text).h;
  lineY = prevLineY + prevLineHeight + 2;

  doc.setFontSize(8);
  doc.setFont('helvetica', 'normal');
  text = `${propertyDetail.legalDescription}`;
  doc.text(text, detailStartX, lineY);

  const showAVM = !userGroup.includes('Sunroom');

  doc.setFontSize(10);
  prevLineY = lineY + doc.getTextDimensions(text).h;
  lineY = lineY + doc.getTextDimensions(text).h * 2;

  let avmWidthRatio = showAVM ? 0.75 : 0;
  let medianDOMWidthRatio = 0.25;
  let finalRentOpinionWidthRatio = 0;
  if (finalRentOpinion != undefined) {
    avmWidthRatio = showAVM ? 0.5 : 0;
    medianDOMWidthRatio = showAVM ? 0.2 : 0.25;
    finalRentOpinionWidthRatio = 0.3;
  }

  const sectionStartX = detailStartX;
  const paddingNeeded = finalRentOpinion != undefined ? 3 : 2; // 2 is default when final opinion is undefined
  const sectionEndX =
    pdfPageWidth - pdfSideMargin - pdfContentPadding * paddingNeeded;
  const avmContentEndX =
    sectionStartX + (sectionEndX - sectionStartX) * avmWidthRatio;
  const medianDOMStartX = avmContentEndX + (showAVM ? pdfContentPadding : 0);
  const medianDOMEndX =
    medianDOMStartX + (sectionEndX - sectionStartX) * medianDOMWidthRatio;
  const medianDOMStartY = lineY;
  const finalRentOpinionStartX = medianDOMEndX + pdfContentPadding;
  const finalRentOpinionEndX =
    finalRentOpinionStartX +
    (sectionEndX - sectionStartX) * finalRentOpinionWidthRatio;
  const finalRentOpinionStartY = lineY;

  console.log('showAVM', showAVM);

  if (comments) {
    doc.setFont('helvetica', 'bold');
    text = `Comments`;
    doc.text(text, detailStartX, lineY);
    const underlineStart = [detailStartX, lineY + 3];
    const underlineEnd = [avmContentEndX, lineY + 3];
    doc.line(...underlineStart, ...underlineEnd);

    prevLineY = lineY;
    prevLineHeight = doc.getTextDimensions(text).h;
    lineY = prevLineY + prevLineHeight + 6;

    doc.setFont('helvetica', 'normal');
    doc.text(comments, detailStartX, lineY, {
      maxWidth: avmContentEndX - detailStartX,
    });

    prevLineY = lineY;
    prevLineHeight = doc.getTextDimensions(comments).h;
    lineY = prevLineY + prevLineHeight + 4;
  }

  // Median DOM
  const finalOpinionStartX = comments ? medianDOMStartX : detailStartX;
  lineY = medianDOMStartY;
  doc.setFont('helvetica', 'bold');
  text = `Final Opinion`;
  doc.text(text, finalOpinionStartX, lineY);
  const medianUnderlineStart = [finalOpinionStartX, lineY + 3];
  const medianUnderlineEnd = [medianDOMEndX, lineY + 3];
  doc.line(...medianUnderlineStart, ...medianUnderlineEnd);

  prevLineY = lineY;
  prevLineHeight = doc.getTextDimensions(text).h;
  lineY = prevLineY + prevLineHeight + 6;

  // Render Final Sales Opinion
  if (finalSalesPriceOpinion) {
    doc.setFont('helvetica', 'bold');
    doc.text(
      `Sales: ${formatToCurrency(finalSalesPriceOpinion)}`,
      finalOpinionStartX,
      lineY,
    );
    prevLineY = lineY;
    prevLineHeight = doc.getTextDimensions(
      `Final Sales Opinion: ${finalSalesPriceOpinion}`,
    ).h;
    lineY = prevLineY + prevLineHeight + 6;
  }

  // Render Final Rent Opinion
  if (finalRentOpinion) {
    doc.text(
      `Rent: ${formatToCurrency(finalRentOpinion)}`,
      finalOpinionStartX,
      lineY,
    );
  }

  const topLeftAxis = [pdfSideMargin, 75];
  const topRightAxis = [pdfPageWidth - pdfSideMargin, 75];
  const bottomLeftAxis = [
    pdfSideMargin,
    imgYPos + pdfImgHeight + pdfContentPadding,
  ];
  const bottomRightAxis = [
    pdfPageWidth - pdfSideMargin,
    imgYPos + pdfImgHeight + pdfContentPadding,
  ];

  doc.line(...topLeftAxis, ...topRightAxis); // Top border
  doc.line(...topRightAxis, ...bottomRightAxis); // Right border
  doc.line(...bottomRightAxis, ...bottomLeftAxis); // Bottom border
  doc.line(...bottomLeftAxis, ...topLeftAxis); // Left border

  setLastYAxisPos(imgYPos + pdfImgHeight + pdfContentPadding);
};

export function formatToCurrency(num) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(num);
}
