import { serverType } from '@/services/data';
import {
  MarketCondition,
  useMarketCondition,
} from '@spatiallaser/market-condition';
import {
  MarketConditionHistoryProvider,
  MarketConditionHistoryQueryProvider,
  useMarketConditionHistory,
} from '@spatiallaser/market-condition-history';
import { Button, Checkbox, Modal, Select, Spin } from 'antd';
import { toPng } from 'html-to-image';
import isEmpty from 'lodash.isempty';
import React, { useContext, useEffect, useRef, useState } from 'react';
import { useSelector } from 'umi';
import InsightsContext from '../context/InsightsContext';
import MarketConditionGraph from './MarketConditionHistory/MarketConditionGraph';
import ParcelOwnerSummary from './ParcelOwnerSummary/ParcelOwnerSummary';
import SubjectSummary from './SubjectSummary/SubjectSummary';

function PreviewModal({ isPreviewModalOpen, closePreviewModal }) {
  const { isLeaseMode } = useSelector((state) => state.CMA);
  const {
    dataFetchingLoading,
    inputData,
    setInputData,
    subjectData,
    currentParcelOwnerSummary,
    // currentMarketCondition,
    currentMarketConditionHistory,
    selectedItems,
    setSelectedItems,
    setInsightsDataURL,
    setStartGeneratingPDF,
    marketConditionHistoryLoading,
  } = useContext(InsightsContext);
  const { currentMarketCondition, priorMarketCondition } = useMarketCondition();
  const { eventCoordinates, currentRadiusMile, drawnCustomPolygons } =
    useSelector((state) => state.CMA);

  const marketConditionAreaData = drawnCustomPolygons.length
    ? [drawnCustomPolygons]
    : eventCoordinates.length
    ? {
        latitude: eventCoordinates[1],
        longitude: eventCoordinates[0],
        radius: currentRadiusMile * 1609.34,
      }
    : undefined;

  const previewContentRef = useRef(null);

  const handleOk = async () => {
    let dataURLState = {};
    const getParcelOwnerImg = async () => {
      if (selectedItems.includes('parcelOwnerSummary')) {
        const institutions = currentParcelOwnerSummary.institution_count;
        for (let i = 0; i < institutions.length; i++) {
          const node = previewContentRef.current.querySelector(
            `#${institutions[i].institution.split(' ')[0]}_progress_donut`,
          );
          if (node) {
            // exclude #None_progress
            const dataURL = await toPng(node);
            dataURLState[institutions[i].institution.split(' ')[0]] = dataURL;
          }
          // var image = new Image();
          // image.src = dataURL;

          // var w = window.open('');
          // w.document.write(image.outerHTML);
          // console.log(dataURL);
        }
      }
    };

    const getMarketConditionGraphImg = async () => {
      const graphContainerId = [
        'marketConditionHistory-priceFull',
        'marketConditionHistory-pricePerSqft',
        'marketConditionHistory-ListedClosed',
        'marketConditionHistory-DOM',
      ];
      for (let i = 0; i < graphContainerId.length; i++) {
        if (selectedItems.includes(graphContainerId[i])) {
          // const nodes = previewContentRef.current.querySelectorAll(
          //   `#${graphContainerId[i]} canvas`,
          // );

          // if (nodes.length > 0) {
          //   dataURLState[graphContainerId[i].split('-')[1]] = [];
          //   for (let j = 0; j < nodes.length; j++) {
          //     const dataURL = await toPng(nodes[j]);

          //     // setInsightsDataURL({
          //     //   ...insightsDataURL,
          //     //   [graphContainerId[i].split('-')[1]]: [
          //     //     ...insightsDataURL[graphContainerId[i].split('-')[1]],
          //     //     dataURL,
          //     //   ],
          //     // });
          //     dataURLState[graphContainerId[i].split('-')[1]].push(dataURL);

          //     // var image = new Image();
          //     // image.src = dataURL;

          //     // var w = window.open('');
          //     // w.document.write(image.outerHTML);
          //   }
          // }
          const node = previewContentRef.current.querySelector(
            `#${graphContainerId[i]}`,
          );

          if (node) {
            // dataURLState[graphContainerId[i].split('-')[1]] = [];
            // for (let j = 0; j < nodes.length; j++) {
            // const dataURL = await toPng(nodes[j]);
            const dataURL = await toPng(node);

            // setInsightsDataURL({
            //   ...insightsDataURL,
            //   [graphContainerId[i].split('-')[1]]: [
            //     ...insightsDataURL[graphContainerId[i].split('-')[1]],
            //     dataURL,
            //   ],
            // });
            dataURLState[graphContainerId[i].split('-')[1]] = dataURL;

            // var image = new Image();
            // image.src = dataURL;

            // var w = window.open('');
            // w.document.write(image.outerHTML);
          }
        }
      }
    };

    await getParcelOwnerImg();
    await getMarketConditionGraphImg();
    setInsightsDataURL(dataURLState);
    setStartGeneratingPDF(true);
  };

  const handleCancel = () => {
    closePreviewModal();
  };

  const handleCheckboxChange = (checked, item) => {
    if (checked) {
      if (!selectedItems.includes(item)) {
        setSelectedItems([...selectedItems, item]);
      }
    } else {
      setSelectedItems(selectedItems.filter((i) => i !== item));
    }
  };

  const handleChartAreaChange = (value) => {
    setInputData({ ...inputData, chartArea: value });
  };

  const marketConditionIsLoading = React.useMemo(() => {
    if (currentMarketCondition && Array.isArray(currentMarketCondition)) {
      const currentLoading = currentMarketCondition.find((item) =>
        Object.keys(item).find((key) => item[key] === 'loading'),
      );
      if (currentLoading) return true;
    }
    if (priorMarketCondition && Array.isArray(priorMarketCondition)) {
      const priorLoading = priorMarketCondition.find((item) =>
        Object.keys(item).find((key) => item[key] === 'loading'),
      );
      if (priorLoading) return true;
    }
    return false;
  }, [currentMarketCondition, priorMarketCondition]);

  return (
    <Modal
      title="Generate Insights Report"
      open={isPreviewModalOpen}
      // onOk={handleOk}
      onCancel={handleCancel}
      width={1000}
      centered={true}
      styles={{
        body: {
          height: '75vh',
          overflowY: 'auto',
          backgroundColor: '#f4f4f4',
        },
      }}
      footer={[
        <Button onClick={handleCancel}>Cancel</Button>,
        <Button
          loading={dataFetchingLoading || marketConditionHistoryLoading}
          type="primary"
          onClick={handleOk}
          disabled={dataFetchingLoading || marketConditionHistoryLoading}
        >
          Generate
        </Button>,
      ]}
    >
      <div
        ref={previewContentRef}
        style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}
      >
        <Spin
          spinning={dataFetchingLoading && isEmpty(subjectData)}
          tip="Loading..."
          size="large"
        >
          <SubjectSummary
            isLeaseMode={inputData.isLeaseMode}
            subjectData={subjectData}
          />
        </Spin>
        <Spin
          spinning={dataFetchingLoading && isEmpty(currentParcelOwnerSummary)}
          tip="Loading..."
          size="large"
        >
          <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
            <Checkbox
              checked={selectedItems.includes('parcelOwnerSummary')}
              onChange={(e) =>
                handleCheckboxChange(e.target.checked, 'parcelOwnerSummary')
              }
            >
              Include Parcel Owner Summary
            </Checkbox>
            <ParcelOwnerSummary
              currentParcelOwnerSummary={currentParcelOwnerSummary}
            />
          </div>
        </Spin>
        <Spin spinning={marketConditionIsLoading} tip="Loading..." size="large">
          <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
            <Checkbox
              checked={selectedItems.includes('marketCondition')}
              onChange={(e) =>
                handleCheckboxChange(e.target.checked, 'marketCondition')
              }
            >
              Include Market Condition
            </Checkbox>
            <MarketCondition />
            {/* <MarketCondition
              marketConditionDates={inputData.marketConditionDates}
              currentMarketCondition={currentMarketCondition}
            /> */}
          </div>
        </Spin>
        <Spin
          spinning={
            (dataFetchingLoading && isEmpty(currentMarketConditionHistory)) ||
            marketConditionHistoryLoading
          }
          tip="Loading..."
          size="large"
        >
          <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
            {/* <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    gap: '10px',
                    alignItems: 'center',
                  }}
                >
                  <span>Chart Area: </span>
                  <div style={{ width: '125px' }}>
                    <Select
                      value={inputData.chartArea}
                      onChange={handleChartAreaChange}
                      options={[
                        { value: 'aoi', label: 'AOI' },
                        { value: 'zipcode', label: 'ZIP Code' },
                        { value: 'district', label: 'School District' },
                        { value: 'county', label: 'County' },
                        { value: 'metro', label: 'Metro' },
                      ]}
                      style={{ width: '100%' }}
                    />
                  </div>
                </div> */}
            <MarketConditionHistorySourceSelector />
            {isPreviewModalOpen && (
              <MarketConditionGraph
                currentMarketConditionHistory={currentMarketConditionHistory}
                selectedItems={selectedItems}
                handleCheckboxChange={handleCheckboxChange}
                isLeaseMode={inputData.isLeaseMode}
              />
            )}
          </div>
        </Spin>
      </div>
    </Modal>
  );
}

const MarketConditionHistorySourceSelector = () => {
  const [chartArea, setChartArea] = React.useState('aoi');
  const { selectedDataSource, setSelectedDataSource } =
    useMarketConditionHistory();

  return (
    <div className="flex flex-row items-center gap-2">
      <span>Chart Area: </span>
      <div style={{ width: '125px' }}>
        <Select
          value={chartArea}
          onChange={(value) => {
            setChartArea(value);
            setSelectedDataSource([value]);
          }}
          options={[
            { value: 'aoi', label: 'AOI' },
            { value: 'zipcode', label: 'ZIP Code' },
            { value: 'schoolDistrict', label: 'School District' },
            { value: 'county', label: 'County' },
            { value: 'metro', label: 'Metro' },
          ]}
          style={{ width: '100%' }}
        />
      </div>
    </div>
  );
};

export default PreviewModal;
