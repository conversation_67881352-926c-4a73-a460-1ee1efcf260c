import React from 'react';

function SubjectSummary({ isLeaseMode, subjectData }) {
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'row',
        gap: '20px',
        padding: '24px 32px',
        boxShadow: 'rgba(0, 0, 0, 0.15) 0px 5px 15px 0px',
        borderRadius: '5px',
        backgroundColor: 'white',
      }}
    >
      <div style={{ display: 'flex', flexDirection: 'column' }}>
        {subjectData.subjectImage && (
          <img
            src={subjectData.subjectImage.src}
            width="300px"
            height="257px"
            alt="Subject Image"
          />
        )}
      </div>
      <div style={{ display: 'flex', flexDirection: 'column' }}>
        {subjectData.aoi && (
          <>
            <div>
              <span>
                Place:{' '}
                <strong style={{ fontSize: '16px' }}>
                  {subjectData.aoi.placeName}
                </strong>
              </span>
            </div>
            {subjectData.aoi.type === 'point' && (
              <>
                <div>
                  <span>
                    Latitude:{' '}
                    <strong style={{ fontSize: '16px' }}>
                      {subjectData.aoi.lat.toFixed(5)}
                    </strong>{' '}
                    Longitude:{' '}
                    <strong style={{ fontSize: '16px' }}>
                      {subjectData.aoi.lng.toFixed(5)}
                    </strong>
                  </span>
                </div>
                <div>
                  <span>
                    Radius:{' '}
                    <strong style={{ fontSize: '16px' }}>
                      {subjectData.aoi.radius} miles
                    </strong>
                  </span>
                </div>
              </>
            )}
          </>
        )}
        {subjectData.zipcode && (
          <div>
            <span>
              ZIP Code:{' '}
              <strong style={{ fontSize: '16px' }}>
                {subjectData.zipcode}
              </strong>
            </span>
          </div>
        )}
        {subjectData.district && (
          <div>
            <span style={{ textTransform: 'capitalize' }}>
              School District:{' '}
              <strong style={{ fontSize: '16px' }}>
                {subjectData.district.toLowerCase()}
              </strong>
            </span>
          </div>
        )}
        {subjectData.metro && (
          <div>
            <span>
              Metro:{' '}
              <strong style={{ fontSize: '16px' }}>{subjectData.metro}</strong>
            </span>
          </div>
        )}
        <div>
          <span>
            Lease/Sale Mode:{' '}
            <strong style={{ fontSize: '16px' }}>
              {isLeaseMode ? 'Lease' : 'Sale'}
            </strong>
          </span>
        </div>
      </div>
    </div>
  );
}

export default SubjectSummary;
