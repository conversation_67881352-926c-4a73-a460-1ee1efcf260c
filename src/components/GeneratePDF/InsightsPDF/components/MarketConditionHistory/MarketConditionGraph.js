import {
  <PERSON><PERSON><PERSON><PERSON>,
  Invent<PERSON><PERSON><PERSON>,
  PriceFull<PERSON>hart,
  PricePSFChart,
} from '@spatiallaser/market-condition-history';
import { Checkbox } from 'antd';
import { useEffect, useRef } from 'react';
// import DOMChart from './DOMChart';
// import ListClosedChart from './ListClosedChart';
// import PriceChart from './PriceChart';
// import YoYChart from './YoYChart';

export let sliderDataRef;

function MarketConditionGraph({
  currentMarketConditionHistory,
  selectedItems,
  handleCheckboxChange,
  isLeaseMode,
}) {
  const { priceChartData, listClosedChartData, domChartData } =
    currentMarketConditionHistory;

  const priceDataFull = (priceChartData && priceChartData.data) || [];
  const priceDataPSF = (priceChartData && priceChartData.psfData) || [];
  const priceDataFullYoY = (priceChartData && priceChartData.yoy) || [];
  const priceDataPSFYoY = (priceChartData && priceChartData.psfYoY) || [];
  const listClosedData =
    (listClosedChartData && listClosedChartData.data) || [];
  const listClosedDataYoY =
    (listClosedChartData && listClosedChartData.yoy) || [];
  const domData = (domChartData && domChartData.data) || [];
  const domDataYoY = (domChartData && domChartData.yoy) || [];

  sliderDataRef = useRef({});
  useEffect(() => {
    if (priceDataFull && priceDataFull.length > 0) {
      sliderDataRef.current = {
        minText: priceDataFull[0].date,
        maxText: priceDataFull[priceDataFull.length - 1].date,
        start: 0,
        end: 1,
      };
    }
  }, [priceDataFull]);

  const onReady = () => {};
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
      <div
        style={{
          padding: '24px 32px',
          borderRadius: '5px',
          boxShadow: 'rgba(0, 0, 0, 0.15) 0px 5px 15px 0px',
          backgroundColor: 'white',
        }}
      >
        <Checkbox
          checked={selectedItems.includes('marketConditionHistory-priceFull')}
          onChange={(e) =>
            handleCheckboxChange(
              e.target.checked,
              'marketConditionHistory-priceFull',
            )
          }
        >
          Include Market Condition History{' '}
          <strong>{isLeaseMode ? 'Rent' : 'Price'} Graph</strong>
        </Checkbox>
        <div
          id="marketConditionHistory-priceFull"
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            gap: '20px',
          }}
        >
          <div style={{ width: '50%' }}>
            <div className="relative">
              <PriceFullChart />
            </div>
          </div>
          <div style={{ width: '50%' }}>
            <div className="relative">
              <PriceFullChart showYoY={true} />
            </div>
            {/* <YoYChart
              data={priceDataFullYoY}
              loading={false}
              onReady={onReady}
            /> */}
          </div>
        </div>
      </div>
      <div
        style={{
          padding: '24px 32px',
          borderRadius: '5px',
          boxShadow: 'rgba(0, 0, 0, 0.15) 0px 5px 15px 0px',
          backgroundColor: 'white',
        }}
      >
        <Checkbox
          checked={selectedItems.includes(
            'marketConditionHistory-pricePerSqft',
          )}
          onChange={(e) =>
            handleCheckboxChange(
              e.target.checked,
              'marketConditionHistory-pricePerSqft',
            )
          }
        >
          Include Market Condition History{' '}
          <strong>{isLeaseMode ? 'Rent' : 'Price'} Per Sqft Graph</strong>
        </Checkbox>
        <div
          id="marketConditionHistory-pricePerSqft"
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            gap: '20px',
          }}
        >
          <div style={{ width: '50%' }}>
            {/* <PriceChart data={priceDataPSF} loading={false} onReady={onReady} /> */}
            <div className="relative">
              <PricePSFChart />
            </div>
          </div>
          <div style={{ width: '50%' }}>
            <div className="relative">
              <PricePSFChart showYoY={true} />
            </div>
            {/* <YoYChart
              data={priceDataPSFYoY}
              loading={false}
              onReady={onReady}
            /> */}
          </div>
        </div>
      </div>
      <div
        style={{
          padding: '24px 32px',
          borderRadius: '5px',
          boxShadow: 'rgba(0, 0, 0, 0.15) 0px 5px 15px 0px',
          backgroundColor: 'white',
        }}
      >
        <Checkbox
          checked={selectedItems.includes(
            'marketConditionHistory-ListedClosed',
          )}
          onChange={(e) =>
            handleCheckboxChange(
              e.target.checked,
              'marketConditionHistory-ListedClosed',
            )
          }
        >
          Include Market Condition History <strong>Active Closed Graph</strong>
        </Checkbox>
        <div
          id="marketConditionHistory-ListedClosed"
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            gap: '20px',
            height: '400px',
            overflow: 'hidden',
          }}
        >
          <div style={{ width: '50%', height: '467px' }}>
            <div className="relative">
              <InventoryChart />
            </div>
            {/* <ListClosedChart
              data={listClosedData}
              loading={false}
              onReady={onReady}
            /> */}
          </div>
          <div style={{ width: '50%' }}>
            <div className="relative">
              <InventoryChart showYoY={true} />
            </div>
            {/* <YoYChart
              data={listClosedDataYoY}
              loading={false}
              onReady={onReady}
            /> */}
          </div>
        </div>
      </div>
      <div
        style={{
          padding: '24px 32px',
          borderRadius: '5px',
          boxShadow: 'rgba(0, 0, 0, 0.15) 0px 5px 15px 0px',
          backgroundColor: 'white',
        }}
      >
        <Checkbox
          checked={selectedItems.includes('marketConditionHistory-DOM')}
          onChange={(e) =>
            handleCheckboxChange(e.target.checked, 'marketConditionHistory-DOM')
          }
        >
          Include Market Condition History <strong>DOM Graph</strong>
        </Checkbox>
        <div
          id="marketConditionHistory-DOM"
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            gap: '20px',
          }}
        >
          <div style={{ width: '50%' }}>
            <div className="relative">
              <DOMChart />
            </div>
            {/* <DOMChart data={domData} loading={false} onReady={onReady} /> */}
          </div>
          <div style={{ width: '50%' }}>
            <div className="relative">
              <DOMChart showYoY={true} />
            </div>
            {/* <YoYChart data={domDataYoY} loading={false} onReady={onReady} /> */}
          </div>
        </div>
      </div>
    </div>
  );
}

export default MarketConditionGraph;
