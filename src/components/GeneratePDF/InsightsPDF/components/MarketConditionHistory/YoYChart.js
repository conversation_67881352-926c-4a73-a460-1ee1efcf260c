import React, { memo } from 'react';
import isEqual from 'lodash.isequal';
import { Line } from '@ant-design/charts';
import { sliderDataRef } from './MarketConditionGraph';

const getMaxValue = (data, key) => {
  if (!data || data.length === 0) return 0;
  return Math.max(...data.map((item) => item[key]));
};

const getMinValue = (data, key) => {
  if (!data || data.length === 0) return 0;
  return Math.min(...data.map((item) => item[key]));
};

const YoYChart = memo(
  ({ data, loading, onReady }) => {
    const config = {
      data: data && data.length > 0 ? data : [],
      xField: 'date',
      yField: 'value',
      seriesField: 'type',
      slider: {
        start: sliderDataRef.current.start,
        end: sliderDataRef.current.end,
      },
      meta: {
        value: {
          min: getMinValue(data, 'value') - 0.05,
          max: getMaxValue(data, 'value') + 0.05,
          formatter: (value) => `${value}%`,
        },
      },
      tooltip: {
        customItems: (originalItems) => {
          const modifiedItems = originalItems.map((item) => ({
            ...item,
            name: item.data.type,
            value: `${Math.round(item.data.value)}%`,
          }));
          return modifiedItems;
        },
      },
      point: {
        shape: 'circle',
        size: 3,
      },
      smooth: true,
    };

    return <Line {...config} loading={loading} onReady={onReady} />;
  },
  (pre, next) => {
    return isEqual(pre, next);
  },
);

export default YoYChart;
