import React, { memo, useRef } from 'react';
import isEqual from 'lodash.isequal';
import { Column } from '@ant-design/charts';
import { DualAxes } from '@ant-design/charts';
import { sliderDataRef } from './MarketConditionGraph';

const getMaxValue = (data, key) => {
  if (!data || data.length === 0) return 0;
  return Math.max(
    ...data.map((item) => item[key]).filter((value) => value !== null),
  );
};

const getMinValue = (data, key) => {
  if (!data || data.length === 0) return 0;
  return Math.min(
    ...data.map((item) => item[key]).filter((value) => value !== null),
  );
};

const getRatioAxisRangeValue = (data) => {
  if (!data || data.length === 0) return 0;
  const maxValue = getMaxValue(data, 'ratio');
  const minValue = getMinValue(data, 'ratio');
  const diff = maxValue - minValue;

  return diff > 0
    ? { max: maxValue, min: minValue - diff / 4 }
    : { max: maxValue + 0.5, min: minValue - 0.5 };
};

const ListClosedChart = memo(
  ({ data, loading, onReady }) => {
    let listCloseData = [];
    let ratioData = [];

    if (data && data.length > 0) {
      listCloseData = data[0];
      ratioData = data[1];
      for (let i = 0; i < ratioData.length; i++) {
        ratioData[i].ratio = ratioData[i].value;
      }
    }

    const config = {
      data: [listCloseData, ratioData],
      xField: 'date',
      yField: ['value', 'ratio'],
      meta: {
        ratio: {
          formatter: (value) => Math.round(value * 100) / 100,
          ...getRatioAxisRangeValue(ratioData),
        },
        date: {
          sync: false,
        },
      },
      geometryOptions: [
        {
          geometry: 'column',
          isGroup: true,
          seriesField: 'type',
        },
        {
          geometry: 'line',
          seriesField: 'type',
          lineStyle: {
            lineWidth: 2,
          },
          smooth: true,
        },
      ],
      slider: {
        start: sliderDataRef.current ? sliderDataRef.current.start : 0,
        end: sliderDataRef.current ? sliderDataRef.current.end : 0,
      },
      limitInPlot: false,
      padding: [20, 20, 70, 20],
      height: 470,
    };

    return <DualAxes {...config} loading={loading} onReady={onReady} />;
  },
  (pre, next) => {
    return isEqual(pre, next);
  },
);

export default ListClosedChart;
