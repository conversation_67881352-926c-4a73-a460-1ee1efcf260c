import { Line } from '@ant-design/charts';
import isEqual from 'lodash.isequal';
import React, { memo } from 'react';
import { sliderDataRef } from './MarketConditionGraph';

const formatterWhole = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
  maximumFractionDigits: 0,
});

const formatter = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
});

const getMaxValue = (data, key) => {
  if (!data || data.length === 0) return 0;
  return Math.max(
    ...data.map((item) => item[key]).filter((value) => value !== null),
  );
};

const getMinValue = (data, key) => {
  if (!data || data.length === 0) return 0;
  return Math.min(
    ...data.map((item) => item[key]).filter((value) => value !== null),
  );
};

const PriceChart = memo(
  ({ data, loading, onReady }) => {
    // const min = getMinValue(data, 'value');
    // const max = getMaxValue(data, 'value');
    const config = {
      data: data && data.length > 0 ? data : [],
      xField: 'date',
      yField: 'value',
      seriesField: 'type',
      slider: {
        start: sliderDataRef.current ? sliderDataRef.current.start : 0,
        end: sliderDataRef.current ? sliderDataRef.current.end : 0,
      },
      meta: {
        value: {
          // min: min - min * 0.1,
          // minLimit: min - min * 0.1,
          // max: max + max * 0.1,
          // maxLimit: max + max * 0.1,
          min: getMinValue(data, 'value') - 0.1,
          max: getMaxValue(data, 'value') + 0.1,
          formatter: (value) =>
            value < 10 ? formatter.format(value) : formatterWhole.format(value),
        },
      },
      point: {
        shape: (datum) => {
          return 'circle';
        },
        style: (datum) => ({ r: 3.5 }),
      },
      smooth: true,
    };

    return <Line {...config} loading={loading} onReady={onReady} />;
  },
  (pre, next) => {
    return isEqual(pre, next);
  },
);

export default PriceChart;
