import { LoadingOutlined } from '@ant-design/icons';
import { Spin, Tooltip } from 'antd';
import moment from 'moment';
import { useContext, useEffect, useState } from 'react';
import InsightsContext from '../context/InsightsContext';
import PDFContext from '../context/PDFContext';
import styles from '../styles.css';
import { setServerType } from '../utils/api';
import {
  dateFormat,
  defaultMarketConditionDates,
  isLatitude,
  isLongitude,
} from '../utils/utils';
import PreviewModal from './PreviewModal';

const antIcon = <LoadingOutlined style={{ fontSize: 16 }} spin />;

interface MarketConditionDates {
  numberOfMonths: number;
  endYear: number;
  month: number;
  compEndYear: number;
  compEndMonth: number;
}

function InsightsPDF({
  userGroup = 'demo-users',
  lat,
  lng,
  radius,
  customPolygon,
  isLeaseMode,
  marketStartDate,
  marketEndDate,
  numberOfMonths,
  endYear,
  endMonth,
  chartArea = 'aoi',
  isIcon = true,
  serverType = 'exp',
  source,
  combineState,
}: any) {
  const [isGeneratable, setIsGeneratable] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);

  const {
    inputData,
    setInputData,
    fetchInsightsData,
    setCustomPolygonMode,
    // generatePDF,
    startGeneratingPDF,
    setStartGeneratingPDF,
  }: any = useContext(InsightsContext);

  const { generatePDF }: any = useContext(PDFContext);

  useEffect(() => {
    if (serverType === 'exp' || serverType === 'prod') {
      setServerType(serverType);
    }

    let state = {};
    state = { ...state, userGroup };
    state = { ...state, isLeaseMode };

    lat = Number(lat);
    lng = Number(lng);

    if (isLatitude(lat) && isLongitude(lng) && radius && radius > 0) {
      setIsGeneratable(true);
    } else if (customPolygon && customPolygon.length > 0) {
      setIsGeneratable(true);
    } else {
      setIsGeneratable(false);
    }

    if (customPolygon && customPolygon.length > 0) {
      setCustomPolygonMode(true);
    } else {
      setCustomPolygonMode(false);
    }
    state = { ...state, lat, lng, radius, customPolygon };

    if (numberOfMonths && endYear && endMonth) {
      const marketConditionDates: MarketConditionDates = {
        numberOfMonths,
        endYear,
        month: endMonth, //
        compEndYear: moment()
          .subtract(numberOfMonths + 1, 'month')
          .year(),
        compEndMonth: moment()
          .subtract(numberOfMonths + 1, 'month')
          .month(),
      };

      state = {
        ...state,
        marketConditionDates: marketConditionDates,
      };
    } else {
      state = {
        ...state,
        marketConditionDates: defaultMarketConditionDates,
      };
    }

    state = { ...state, chartArea };

    setInputData(state);
  }, [
    userGroup,
    lat,
    lng,
    radius,
    customPolygon,
    isLeaseMode,
    marketStartDate,
    marketEndDate,
    numberOfMonths,
    endYear,
    endMonth,
    chartArea,
    isIcon,
    serverType,
  ]);

  useEffect(() => {
    if (startGeneratingPDF) {
      const generate = async () => {
        try {
          await generatePDF();
          setLoading(false);
          console.log('Insights PDF Generated');
        } catch (error) {
          console.log('Error generating PDF', error);
        }
      };
      setIsPreviewModalOpen(false);
      setStartGeneratingPDF(false);
      generate();
    }
  }, [startGeneratingPDF]);

  useEffect(() => {
    if (!combineState) return;

    if (source === 'combine') {
      const { generateQueue } = combineState;

      if (generateQueue && generateQueue === '2') {
        openPreviewModal();
      }
    }
  }, [combineState]);

  const openPreviewModal = () => {
    setIsPreviewModalOpen(true);
    setLoading(true);
    fetchInsightsData();
  };

  const closePreviewModal = async () => {
    setLoading(false);
    setIsPreviewModalOpen(false);

    if (combineState) {
      const { setPdfArray, setGenerateQueue } = combineState;
      setGenerateQueue(null);
      setPdfArray([]);
    }
  };

  return (
    <>
      {source !== 'combine' && (
        <Tooltip
          title={
            isGeneratable
              ? 'Generate Insights Report'
              : 'Please select a location on the map to get Insights'
          }
        >
          <button
            className={styles.pdfButton}
            onClick={openPreviewModal}
            disabled={!isGeneratable}
          >
            {isIcon ? (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                width={20}
                height={20}
                fill={isGeneratable ? '#1890ff' : '#757575'}
                style={{ transform: 'translateY(3.5px)' }}
              >
                <path d="M15 5.833H5V3.375q0-.354.26-.615.261-.26.615-.26h8.25q.354 0 .615.26.26.261.26.615Zm0 4.813q.354 0 .615-.261.26-.26.26-.614t-.26-.615q-.261-.26-.615-.26t-.615.26q-.26.261-.26.615t.26.614q.261.261.615.261ZM6.75 15.75h6.5v-2.917h-6.5v2.917Zm0 1.75q-.729 0-1.24-.51Q5 16.479 5 15.75v-1.312H2.542q-.354 0-.615-.261-.26-.26-.26-.615V9.271q0-1.063.75-1.813t1.812-.75h11.542q1.062 0 1.812.75t.75 1.813v4.291q0 .355-.26.615-.261.261-.615.261H15v1.312q0 .729-.51 1.24-.511.51-1.24.51Z" />
              </svg>
            ) : (
              'Insights Report'
            )}
            {loading && <Spin indicator={antIcon} />}
          </button>
        </Tooltip>
      )}
      <PreviewModal
        isPreviewModalOpen={isPreviewModalOpen}
        closePreviewModal={closePreviewModal}
      />
    </>
  );
}

export default InsightsPDF;
