import { Col, Progress, Row } from 'antd';
import { useEffect, useRef, useState } from 'react';
import styles from '../../styles.css';

export let totalParcels, occupied, renter;
export let institutions;

const OWNER_COLOR = {
  ownerOccupiedColor: '#8e8d8f',
  AH4RColor: '#f5222d',
  cerberusColor: '#1890ff',
  invitationHomes: '#52c41a',
  progressResColor: '#08979c',
  momAndPopColor: '#8c8c8c',
  othersColor: '#faad14',
};

export const getStrokeColor = (institutionName) => {
  if (institutionName.includes('AH4R')) {
    return OWNER_COLOR.AH4RColor;
  } else if (institutionName.includes('Amherst')) {
    return OWNER_COLOR.amherstColor;
  } else if (institutionName.includes('Cerberus')) {
    return OWNER_COLOR.cerberusColor;
  } else if (institutionName.includes('Invitation')) {
    return OWNER_COLOR.invitationHomes;
  } else if (institutionName.includes('Progress')) {
    return OWNER_COLOR.progressResColor;
  } else if (institutionName.includes('Tricon')) {
    return OWNER_COLOR.triconColor;
  } else if (institutionName.includes('Mom & Pop')) {
    return OWNER_COLOR.momAndPopColor;
  } else if (institutionName.includes('Other')) {
    return OWNER_COLOR.othersColor;
  } else {
    return OWNER_COLOR.othersColor;
  }
};

// const ParcelOwnerSummary = connect(({ CMA }) => ({
//   currentParcelOwnerSummary: CMA.currentParcelOwnerSummary,
// }))(function (props) {

const ParcelOwnerSummary = (props) => {
  const institutionContainer = useRef(null);
  const [institutionContainerWidth, setInstitutionContainerWidth] = useState(0);

  totalParcels = 0;
  occupied = 0;
  renter = 0;
  institutions = [];

  // Process fetched parcel summary information
  if (props.currentParcelOwnerSummary) {
    const summary = props.currentParcelOwnerSummary;

    totalParcels = summary.total_parcels || 0;

    if (summary.owner_count) {
      summary.owner_count.forEach((owner) => {
        if (owner['owner_occupied_sl'] === 'Yes') {
          occupied = owner.count;
        } else {
          renter = owner.count;
        }
      });
    }

    // Extract instutitions from fetched data
    if (summary.institution_count) {
      let tempOtherInstitution;
      let tempMomAndPopInstitution;

      for (let i = 0; i < summary.institution_count.length; i++) {
        if (
          summary.institution_count[i].institution != 'None' &&
          summary.institution_count[i].institution != 'Mom & Pop'
        ) {
          if (summary.institution_count[i].institution != 'Other') {
            institutions.push(summary.institution_count[i]);
          } else {
            tempOtherInstitution = {
              institution: 'Other Funds',
              count: summary.institution_count[i].count,
            };
          }
        }
        if (summary.institution_count[i].institution === 'Mom & Pop') {
          tempMomAndPopInstitution = summary.institution_count[i];
        }
      }

      if (tempOtherInstitution) {
        institutions.push(tempOtherInstitution);
      }
      // Move mom and pop to end of institution list
      if (tempMomAndPopInstitution) {
        institutions.push(tempMomAndPopInstitution);
      }
    }
    console.log('institutions', institutions);
  }

  const resizeObserver = new ResizeObserver((entries) => {
    for (const entry of entries) {
      if (entry.contentRect) {
        setInstitutionContainerWidth(entry.contentRect.width);
      }
    }
  });

  useEffect(() => {
    if (institutionContainer != null && institutionContainer.current != null) {
      resizeObserver.observe(institutionContainer.current);
    }
  });
  return (
    <>
      <div
        style={{ boxShadow: 'rgba(0, 0, 0, 0.15) 0px 5px 15px 0px', margin: 0 }}
        className={styles.cardWrapper}
      >
        <Row
          key="owner summary row"
          align="bottom"
          justify="space-between"
          wrap={true}
          gutter={[0, 8]}
        >
          <Col key="title col" className={styles.cardTitleH2}>
            Parcel Owner Summary
          </Col>
          <Col key="data col">
            <Row
              key="data col row wrapper"
              align="middle"
              justify="end"
              gutter={24}
            >
              <Col key="total col">
                <span
                  key="total col label"
                  className={styles.cardSubtitle}
                  style={{ marginRight: 8 }}
                >
                  Total
                </span>
                <span key="total col content" className={styles.cardDataValue}>
                  {totalParcels || '-'}
                </span>
              </Col>
              <Col key="ratio col">
                <span
                  key="ratio col label"
                  className={styles.cardSubtitle}
                  style={{ marginRight: 8 }}
                >
                  Renter/Owner Occupied
                </span>
                <span key="ratio col content" className={styles.cardDataValue}>
                  {occupied && renter
                    ? `${renter}(${Math.round(
                        (renter / totalParcels) * 100,
                      )}%) / ${occupied}(${Math.round(
                        (occupied / totalParcels) * 100,
                      )}%)`
                    : '-'}
                </span>
              </Col>
            </Row>
          </Col>
        </Row>
        {institutions && institutions.length > 0 && (
          <>
            <div key="divider 1" className={styles.dividerCardHeader} />
            <p key="ratio label" style={{ margin: 0, marginTop: '10px' }}>
              % Based on renter occupied
            </p>
            <div key="wrapper" style={{ padding: '0 10px' }}>
              <div
                key="inner wrapper"
                ref={institutionContainer}
                id="test-int"
                style={{
                  display: 'grid',
                  gridTemplateColumns:
                    institutionContainerWidth > 660
                      ? 'repeat(8, minmax(0, 1fr))'
                      : 'repeat(4, minmax(0, 1fr))',
                }}
              >
                {institutions.map((institution) => (
                  <div
                    key={institution.institution}
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      paddingTop: '20px',
                    }}
                  >
                    <Progress
                      id={`${
                        institution.institution.split(' ')[0]
                      }_progress_donut`}
                      key="progress"
                      type="circle"
                      percent={(institution.count / renter) * 100}
                      width={80}
                      format={() => `${institution.count}`}
                      strokeColor={getStrokeColor(institution.institution)}
                    />
                    <p
                      key="content"
                      style={{ width: '80px', textAlign: 'center', margin: 0 }}
                    >
                      {`${
                        institution.institution === 'Cerberus'
                          ? 'Firstkey'
                          : institution.institution
                      } (${((institution.count / renter) * 100).toFixed(1)}%)`}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default ParcelOwnerSummary;
