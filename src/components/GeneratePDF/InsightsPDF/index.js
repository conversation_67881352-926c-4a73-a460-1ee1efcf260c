import { serverType } from '@/services/data';
import {
  MarketCondition,
  MarketConditionProvider,
  MarketConditionQueryProvider,
} from '@spatiallaser/market-condition';
import {
  MarketConditionHistoryProvider,
  MarketConditionHistoryQueryProvider,
} from '@spatiallaser/market-condition-history';

import { useSelector } from 'umi';
import InsightsPDF from './components/InsightsPDF';
import { InsightsProvider } from './context/InsightsContext';
import { PDFProvider } from './context/PDFContext';

const component = (props) => {
  const {
    eventCoordinates,
    currentRadiusMile,
    isLeaseMode,
    searchingMode,
    drawnCustomPolygons,
  } = useSelector((state) => state.CMA);

  const marketConditionAreaData = drawnCustomPolygons.length
    ? [drawnCustomPolygons]
    : eventCoordinates.length
    ? {
        latitude: eventCoordinates[1],
        longitude: eventCoordinates[0],
        radius: currentRadiusMile * 1609.34,
      }
    : undefined;

  const marketConditionHistoryAreaData = drawnCustomPolygons.length
    ? {
        type: 'multipolygon',
        coordinates: [drawnCustomPolygons],
      }
    : eventCoordinates.length
    ? {
        type: 'point',
        lat: eventCoordinates[1],
        lng: eventCoordinates[0],
        distance: currentRadiusMile * 1609.34,
      }
    : undefined;

  return (
    <MarketConditionQueryProvider>
      <MarketConditionProvider
        serverType={serverType}
        isLeaseMode={searchingMode === 'Lease'}
        areaData={marketConditionAreaData}
        showPrior={true}
        responsive={false}
        showContainerHeader={false}
      >
        <MarketConditionHistoryQueryProvider>
          <MarketConditionHistoryProvider
            serverType={serverType}
            isLeaseMode={searchingMode === 'Lease'}
            areaData={marketConditionHistoryAreaData}
            showPrior={true}
            responsive={false}
            showContainerHeader={false}
            defaultDataSource={['aoi']}
          >
            <InsightsProvider>
              <PDFProvider
                source={props.source}
                combineState={props.combineState}
              >
                <InsightsPDF {...props} />
              </PDFProvider>
            </InsightsProvider>
          </MarketConditionHistoryProvider>
        </MarketConditionHistoryQueryProvider>
      </MarketConditionProvider>
    </MarketConditionQueryProvider>
  );
};

export default component;
