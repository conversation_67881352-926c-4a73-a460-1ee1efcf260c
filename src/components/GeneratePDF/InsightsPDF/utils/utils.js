import isEmpty from 'lodash.isempty';
import moment from 'moment';

export const dateFormat = 'YYYY-MM-DD';
export const MAPBOX_TOKEN =
  'pk.**************************************************************************.-T2S1ZeAEBGxjC4rC0CZzA';
export const isLatitude = (num) => isFinite(num) && Math.abs(num) <= 90;
export const isLongitude = (num) => isFinite(num) && Math.abs(num) <= 180;
export const defaultMarketConditionDates = {
  // startDate: moment().subtract(90, 'days').format(dateFormat),
  // endDate: moment().format(dateFormat),
  // compStartDate: moment().subtract(180, 'days').format(dateFormat),
  // compEndDate: moment().subtract(90, 'days').format(dateFormat),

  numberOfMonths: 3,
  endYear: moment().subtract(1, 'month').year(),
  month: moment().subtract(1, 'month').month(),
  compEndYear: moment()
    .subtract(3 + 1, 'month')
    .year(),
  compEndMonth: moment()
    .subtract(3 + 1, 'month')
    .month(),
};

// process market condition data
export const processMarketConditionResponse = (response) => {
  if (!response || isEmpty(response)) return {};
  return {
    active: response.active,
    activeMedianPSF: response.active_median_current_price_and_size_ratio
      ? (
          Math.round(
            response.active_median_current_price_and_size_ratio * 100,
          ) / 100
        ).toFixed(2)
      : 0,
    closed: response.closed,
    closedMedianPSF: response.closed_median_close_price_and_size_ratio
      ? (
          Math.round(response.closed_median_close_price_and_size_ratio * 100) /
          100
        ).toFixed(2)
      : 0,
    medianDOM: response.closed_dom_median
      ? +response.closed_dom_median.toFixed(0)
      : 0,
    monthsOfInventory: response.ratio
      ? Math.round(response.ratio * 100) / 100
      : 0,
  };
};
// process graph data
export const processMLSListingSummaryChartResponse = (
  response,
  propertyType,
) => {
  if (!response || response.length === 0) return [];

  let priceDataPSF = [];
  let priceDataFull = [];
  let listClosedData = [];
  let ratioData = [];
  let domData = [];

  for (let i = response.length - 1; i >= 0; i--) {
    if (!response[i].mlsListingSummary) continue;

    const { year, month, mlsListingSummary } = response[i];
    const date = moment(`${year}-${month}-15`).format('MMM YYYY');
    for (const key in mlsListingSummary) {
      const item = {
        date: date,
        value: mlsListingSummary[key],
        type: processMLSListingKey(key, propertyType),
      };
      if (
        key === 'active_median_current_price_and_size_ratio' ||
        key === 'closed_median_close_price_and_size_ratio'
      ) {
        priceDataPSF.push(item);
      } else if (
        key === 'closed_median_close_price' ||
        key === 'active_median_current_price'
      ) {
        priceDataFull.push(item);
      } else if (key === 'active' || key === 'closed') {
        listClosedData.push(item);
      } else if (key === 'ratio') {
        ratioData.push(item);
      } else if (key === 'closed_dom_median') {
        domData.push(item);
      }
    }
  }

  // list prices should come before closed prices
  priceDataPSF = swapDataOrder(priceDataPSF);
  priceDataFull = swapDataOrder(priceDataFull);

  const priceDataPSFYoY = generateYearOnYearData(priceDataPSF);
  const priceDataFullYoY = generateYearOnYearData(priceDataFull);
  const listClosedDataYoY = generateYearOnYearData(
    [...listClosedData, ...ratioData].sort(
      (a, b) => moment(a.date, 'MMM YYYY') - moment(b.date, 'MMM YYYY'),
    ),
  );
  const domDataYoY = generateYearOnYearData(domData);

  return {
    priceChartData: {
      data: priceDataFull,
      psfData: priceDataPSF,
      yoy: priceDataFullYoY,
      psfYoY: priceDataPSFYoY,
    },
    listClosedChartData: {
      data: [listClosedData, ratioData],
      yoy: listClosedDataYoY,
    },
    domChartData: {
      data: domData,
      yoy: domDataYoY,
    },
  };

  // return { priceDataPSF, priceDataFull, listClosedData, domData };
};

const swapDataOrder = (data) => {
  const result = [];
  for (let i = 0; i < data.length; i += 2) {
    result.push(data[i + 1]);
    result.push(data[i]);
  }
  return result;
};

const generateYearOnYearData = (data) => {
  const result = [];

  for (let i = 0; i < data.length; i++) {
    const firstType = data[i].type;
    const firstDate = moment(data[i].date, 'MMM YYYY');
    firstDate.add(1, 'y');
    for (let j = i; j < data.length; j++) {
      const secondType = data[j].type;
      const secondDate = moment(data[j].date, 'MMM YYYY');

      if (firstDate.isSame(secondDate) && firstType === secondType) {
        const yearOnYearValue =
          data[i].value > 0 ? (data[j].value / data[i].value - 1) * 100 : 0;
        data[i].yearOnYearValue = yearOnYearValue;
        result.push({
          date: secondDate.format('MMM YYYY'),
          value: yearOnYearValue,
          type: `${firstType} YoY`,
        });
      }
    }
  }
  return result;
};

const processMLSListingKey = (key, propertyType) => {
  switch (key) {
    case 'active':
      return 'Active';
    case 'active_median_current_price':
      if (propertyType === 'Residential Lease') {
        return 'Active Median Rent';
      }
      return 'Active Median Price';
    case 'active_median_current_price_and_size_ratio':
      if (propertyType === 'Residential Lease') {
        return 'Active Median RSF';
      }
      return 'Active Median PSF';
    case 'closed':
      return 'Closed';
    case 'closed_median_close_price':
      if (propertyType === 'Residential Lease') {
        return 'Closed Median Rent';
      }
      return 'Closed Median Price';
    case 'closed_median_close_price_and_size_ratio':
      if (propertyType === 'Residential Lease') {
        return 'Closed Median RSF';
      }
      return 'Closed Median PSF';
    case 'closed_dom_median':
      return 'Median DOM';
    case 'ratio':
      return 'Months of Inventory';
    default:
      // console.log('key not found in processMLSListingKey');
      return '';
  }
};
