import { getUserToken } from '@/utils/auth';
import { notification } from 'antd';

type ServerType = 'prod' | 'exp';

let serverType: ServerType = 'exp';

export const setServerType = (type: ServerType) => {
  serverType = type;
};

const handleError = (error: any) => {
  const { res = {} } = error.cause;
  const statusText = res.statusText || 'Network Exception';
  const { status, url } = res;

  notification.error({
    message: `Request error ${status}: ${url}`,
    description: statusText,
  });
};

const processHeaders = async (url: string, options: any) => {
  if (options.method === 'POST') {
    options.header = { 'Content-Type': 'application/json' };
  }

  switch (true) {
    case url.includes(`/api/cma/${serverType}/`):
      const { header } = options;
      const Authorization = `Bearer ${await getUserToken('access')}`;
      const parameters = { headers: { Authorization, ...header }, ...options };
      return parameters;
    default:
      return options;
  }
};

const pathRewrite = (path: string) => {
  if (path.includes('/api/cma/prod')) {
    return path.replace(
      '/api/cma/prod',
      'http://ec2-44-222-3-252.compute-1.amazonaws.com:8080',
      // "https://dzovy6wv7g.execute-api.us-east-1.amazonaws.com/production"
    );
  } else if (path.includes('/api/cma/exp')) {
    return path.replace(
      '/api/cma/exp',
      'http://ec2-3-235-170-15.compute-1.amazonaws.com:8080',
      // "https://v72b1ngqw2.execute-api.us-east-1.amazonaws.com/expriment"
    );
  }

  return path;
};

const encodeURLParams = (url: string) => {
  const urlSplit = url.split('=');

  let encodedURL: string | string[] = [urlSplit[0]];

  for (let i = 1; i < urlSplit.length; i++) {
    let param: string | string[] = urlSplit[i];
    if (param.includes('&')) {
      param = param.split('&');
      if (param[0] === decodeURIComponent(param[0])) {
        param[0] = encodeURIComponent(param[0]);
      }
      param = param.join('&');
    } else {
      if (param === decodeURIComponent(param)) {
        param = encodeURIComponent(param);
      }
    }
    encodedURL.push(param);
  }

  encodedURL = encodedURL.join('=');
  return encodedURL;
};

const request = async (url: string, options: any) => {
  const headers = await processHeaders(url, options);
  url = process.env.NODE_ENV === 'development' ? pathRewrite(url) : url;
  url = encodeURLParams(url);

  try {
    const res = await fetch(url, headers);
    if (!res.ok) {
      throw new Error('Bad response', { cause: { res } });
    }

    const data = await res.json();
    return data;
  } catch (err) {
    handleError(err);
  }
};

export async function getParcelOwnerSummary(params: {
  lng: number;
  lat: number;
  distance: number;
}) {
  return request(
    `/api/cma/${serverType}/mls/summary?lng=${params.lng}&lat=${params.lat}&distance=${params.distance}`,
    {
      method: 'GET',
    },
  );
}

// Mapbox geocoding
export const getGeocodingData = async (params: {
  lng: number;
  lat: number;
}) => {
  return request(
    `https://api.mapbox.com/geocoding/v5/mapbox.places/${params.lng},${params.lat}.json?access_token=pk.eyJ1Ijoic3hieGNoZW4iLCJhIjoiYjRhNWMyMmI0NzVjZjEzZjYyZGUzZDM0NmFhZTcyNjEifQ.-T2S1ZeAEBGxjC4rC0CZzA`,
    { method: 'GET' },
  );
};

// get data layer of point
export const getMLSListingSummaryPointWithinLayerData = async (params: {
  lng: number;
  lat: number;
}) => {
  return await request(
    `/api/cma/${serverType}/mls-listing-summary/point-within-data-layer?lng=${params.lng}&lat=${params.lat}`,
    { method: 'GET' },
  );
};

// Parcel Owner
export const getParcelOwnerSummaryWithinPolygon = async (params: {
  body: any;
}) => {
  return await request(`/api/cma/${serverType}/mls/summary/multipolygon`, {
    method: 'POST',
    body: params.body,
  });
};

// Market Condition History Chart
export const MLSListingSummaryChartSphereData = async (params: {
  propertyType: string;
  lng: number;
  lat: number;
  distance: number;
}) => {
  return await request(
    `/api/cma/${serverType}/mls-listing-summary/chart?propertyType=${params.propertyType}&lng=${params.lng}&lat=${params.lat}&distance=${params.distance}`,
    { method: 'GET' },
  );
};

export const MLSListingSummaryChartMultipolygonData = async (params: {
  propertyType: string;
  body: any;
}) => {
  return await request(
    `/api/cma/${serverType}/mls-listing-summary/chart/multipolygon?propertyType=${params.propertyType}`,
    { method: 'POST', body: params.body },
  );
};

export const MLSListingSummaryChartZIPCodeData = async (params: {
  propertyType: string;
  lng: number;
  lat: number;
}) => {
  return await request(
    `/api/cma/${serverType}/mls-listing-summary/chart/zipcode?propertyType=${params.propertyType}&lng=${params.lng}&lat=${params.lat}`,
    { method: 'GET' },
  );
};

export const MLSListingSummaryChartCBSAData = async (params: {
  propertyType: string;
  lng: number;
  lat: number;
}) => {
  return await request(
    `/api/cma/${serverType}/mls-listing-summary/chart/metro?propertyType=${params.propertyType}&lng=${params.lng}&lat=${params.lat}`,
    { method: 'GET' },
  );
};

export const MLSListingSummaryChartSchoolDistrictData = async (params: {
  propertyType: string;
  lng: number;
  lat: number;
}) => {
  return await request(
    `/api/cma/${serverType}/mls-listing-summary/chart/school-district?propertyType=${params.propertyType}&lng=${params.lng}&lat=${params.lat}`,
    { method: 'GET' },
  );
};

export const MLSListingSummaryChartCountyData = async (params: {
  propertyType: string;
  lng: number;
  lat: number;
}) => {
  return await request(
    `/api/cma/${serverType}/mls-listing-summary/chart/county?propertyType=${params.propertyType}&lng=${params.lng}&lat=${params.lat}`,
    { method: 'GET' },
  );
};
