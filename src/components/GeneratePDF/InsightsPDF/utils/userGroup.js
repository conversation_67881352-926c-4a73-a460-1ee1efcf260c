export const getClientInformation = (userGroup) => {
  let userInfo = {};

  if (userGroup) {
    if (userGroup.includes('AlliedDev')) {
      userInfo.clientName = 'AlliedDev';
      userInfo.shortName = 'AlliedDev';
      userInfo.png = '/images/logo/AlliedDev-Logo.webp';
      userInfo.flex = '350px';
    } else if (userGroup.includes('AMH')) {
      userInfo.clientName = 'AMH';
      userInfo.shortName = 'AMH';
      userInfo.png = '/images/logo/logo-amh.png';
      userInfo.flex = '350px';
      userInfo.svg = (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          xmlSpace="preserve"
          width="1010"
          height="310"
          viewBox="0 0 1010 310"
          version="1.1"
        >
          <style>{`.st0{fill:#004879} .st1{fill:#dd1c23}`}</style>
          <path
            d="M111.8 286.7c-5.7 6.6-12.9 11.8-21.5 15.3-8.7 3.6-18.3 5.3-29 5.3-5.9 0-12.2-.8-18.7-2.5s-12.6-4.5-18.2-8.4c-5.6-3.9-10.2-9.2-13.9-15.8C6.8 274 5 265.7 5 255.7c0-9.7 2-18.4 5.9-26 3.9-7.6 9.3-13.9 16.2-19.1 6.9-5.1 15-9 24.4-11.6 9.4-2.6 19.5-3.9 30.5-3.9 5.9 0 11.9.1 18 .4 6.1.2 10 .6 11.9 1.1V193c0-4.3-.9-7.9-2.7-10.9-1.8-3-4.2-5.5-7.1-7.5-3-2-6.4-3.4-10.2-4.3-3.8-.8-7.8-1.2-12.1-1.2-11.4 0-20.8 1.4-28.3 4.3-7.5 2.8-13.8 6.1-19.1 9.6l-21-31.3c3.6-2.6 7.6-5.2 12.1-7.8 4.5-2.6 9.8-5 16-7.1 6.2-2.1 13.1-3.9 20.8-5.2 7.7-1.3 16.6-2 26.5-2 25.4 0 44.2 6.8 56.3 20.5 12.1 13.7 18.2 32 18.2 55v97.2h-48.1l-1.4-15.6zm0-58.1c-1.2-.2-3.4-.5-6.8-.9-3.3-.4-7.1-.5-11.4-.5-14.2 0-24.3 2-30.3 6.1-5.9 4-8.9 9.7-8.9 17.1 0 13.8 8.3 20.7 24.9 20.7 4 0 8-.7 11.9-2 3.9-1.3 7.4-3.1 10.3-5.5 3-2.4 5.4-5.3 7.3-8.9 1.9-3.6 2.8-7.7 2.8-12.5v-13.6z"
            className="st0"
          />
          <path
            d="M181.8 135.3h49.1l1.8 18.5c5.2-6.4 11.9-11.8 19.9-16 8.1-4.3 17.6-6.4 28.5-6.4 6.6 0 12.6.7 17.8 2.1 5.2 1.4 9.8 3.3 13.7 5.5 3.9 2.3 7.4 4.8 10.3 7.5 3 2.7 5.4 5.4 7.3 8 6.9-7.4 15.3-13.1 25.1-17.1 9.9-4 20.1-6.1 30.8-6.1 21.4 0 37.7 6.1 49 18.2 11.3 12.1 16.9 28.6 16.9 49.5v103.3h-51.3v-95.1c0-10-2.2-17.7-6.6-23.1-4.4-5.5-11-8.2-19.8-8.2-10 0-17.9 3.4-23.9 10.3-5.9 6.9-8.9 15.7-8.9 26.4v89.7H292v-95.1c0-10-2.2-17.7-6.6-23.1-4.4-5.5-11-8.2-19.8-8.2-9.7 0-17.6 3.4-23.7 10.3-6.1 6.9-9.1 15.7-9.1 26.4v89.7h-51.3v-167z"
            className="st0"
          />
          <path
            d="M472.5 64.8h50.9v86.5c5.5-6.4 12.7-11.3 21.7-14.8 9-3.4 18.2-5.2 27.4-5.2 19.2 0 34.6 5.7 46.1 17.1 11.5 11.4 17.3 28.3 17.3 50.6v103.3H585v-95.4c0-9.7-2.5-17.3-7.5-22.8s-11.6-8.2-19.9-8.2c-5.5 0-10.3 1-14.6 3-4.3 2-7.8 4.7-10.7 8-2.8 3.3-5 7.2-6.4 11.6-1.4 4.4-2.1 9.1-2.1 14.1v89.7h-51.3V64.8z"
            className="st0"
          />
          <path
            d="M995.6 246.2c-9-7.9-18.3-12.3-27-15.9-21.4-8.7-44.8-13.5-71.7-14.6-45.1-1.8-90.8 5.8-143.9 24.1-10.4 3.6-21.1 6.4-32 8.4-8.6 1.6-17.4 2.5-26.1 2.9l-4.3.1v51.1s11.9.1 18.3 0c2.1 0 4.3 0 6.4-.1 13.2-.6 26.7-2.7 41.2-6.4 23.5-5.9 45.8-15.1 66.7-24 28-12 56.8-19.8 85.4-23.2 12.2-1.5 24.5-2.3 36.7-2.5 12.3-.2 24.5.4 36.7 1.7 5.8.6 11.5 1.4 17.3 2l-3.7-3.6z"
            className="st1"
          />
          <path
            d="M717.8 228.9c5.2-1 10.4-2.1 15.6-3.5v-115l91.2-59.9 91.4 58.4v88.5c15.2 1.6 29.4 4.5 42.9 8.6v-5.4l-.1-68.8h35.1c5.1 0 9.4-3.2 10.8-8.1 1.4-4.9-.4-9.9-4.7-12.7L835 5.7c-6.4-4.1-14.6-4.1-21 0L649.2 111c-4.3 2.8-6.2 7.7-4.7 12.6 1.4 4.9 5.7 8.1 10.8 8.1h35.4l-.1 99.8 3.7-.1c7.9-.3 15.7-1.1 23.5-2.5z"
            className="st0"
          />
          <path
            d="M975.7 261.3c-1-.4-1.7-.7-2.2-1-.5-.3-.8-.6-1-.9-.2-.3-.3-.8-.3-1.3 0-.7.3-1.3.8-1.7.5-.4 1.3-.6 2.2-.6 1.2 0 2.5.3 3.8.8l.2.1.7-1.9-.1-.1c-1.4-.6-2.9-.9-4.5-.9s-2.9.4-3.8 1.1c-1 .8-1.5 1.8-1.5 3.1 0 1.2.3 2.2 1 3 .7.8 1.8 1.4 3.3 2 1.1.4 1.9.7 2.4 1 .5.3.9.6 1.1.9.2.3.3.8.3 1.3 0 .8-.3 1.4-.9 1.9-.6.5-1.5.7-2.7.7-.8 0-1.5-.1-2.3-.2-.8-.2-1.5-.4-2.1-.7l-.2-.1v2.2h.1c1 .5 2.6.8 4.5.8 1.8 0 3.2-.4 4.3-1.2 1.1-.8 1.6-2 1.6-3.5 0-1.1-.3-2.1-1-2.8-.8-.7-2-1.4-3.7-2zM996.4 254.1l-5.1 13.2-5.1-13.2h-3.1v16.5h2.1v-10.4c0-1.2 0-2.3-.1-3.3l5.2 13.7h1.7l5.3-13.7c-.1 1.1-.1 2.2-.1 3.1v10.6h2.2v-16.5h-3z"
            className="st1"
          />
        </svg>
      );
    } else if (userGroup.includes('Arabella')) {
      userInfo.clientName = 'Arabella';
      userInfo.shortName = 'Arabella';
      userInfo.png = '/images/logo/Arabella-logo.webp';
      userInfo.flex = '350px';
    } else if (userGroup.includes('BridgeTower')) {
      userInfo.clientName = 'BridgeTower';
      userInfo.shortName = 'BT';
      userInfo.png = '/images/logo/BT-Logo-new-transparent.png';
      userInfo.flex = '350px';
    } else if (userGroup.includes('CommonGroundCapital')) {
      userInfo.clientName = 'CommonGroundCapital';
      userInfo.shortName = 'CGC';
      userInfo.png = '/images/logo/CGC-Logo.png';
      userInfo.flex = '450px';
      userInfo.svg = (
        <svg
          key="CGC logo"
          viewBox="4.7 4.721 320.56 50.03"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M49.66 6.62h-30.6q-.66 0-1.32.06l-.38.05c-.32 0-.64.08-.95.14l-.41.07q-.6.12-1.2.3c-.4.12-.79.26-1.18.42l-.35.15-.82.38-.35.18c-.33.18-.65.37-1 .58L11 9a11.5 11.5 0 0 0-1 .71l-.24.29c-.22.18-.43.37-.64.56l-.3.29c-.24.25-.48.5-.71.77l-.11.12c-.25.3-.48.61-.7.92L7 13c-.15.23-.3.46-.43.7l-.22.37q-.24.45-.45.93l-.09.17a13.81 13.81 0 0 0-1.11 5.41v20.17a14.15 14.15 0 0 0 14.32 14H36.5v-8.59H21.42a7.82 7.82 0 0 1-7.91-7.72V22.9a7.82 7.82 0 0 1 7.91-7.72H54V10.9a4.33 4.33 0 0 0-4.38-4.28"
            fill="#cb8129"
          />
          <path
            d="M48.44 26.38H25v8.56h17.84a2.4 2.4 0 0 1 2.43 2.37v17.42h4.39A4.34 4.34 0 0 0 54 50.45V31.84a5.52 5.52 0 0 0-5.6-5.46"
            fill="#b06027"
          />
          <path
            d="M77.44 30a11.51 11.51 0 0 1-5.61-1.32A9.55 9.55 0 0 1 68.06 25a10.64 10.64 0 0 1-1.36-5.46 12.2 12.2 0 0 1 .94-4.83 11.82 11.82 0 0 1 6.52-6.43 12.85 12.85 0 0 1 4.91-.92 12.43 12.43 0 0 1 6.57 1.67L85 14.22c-1 0-1.59-.2-1.63-.61L83 10.8a8.94 8.94 0 0 0-4.79-1.19A6.66 6.66 0 0 0 72.85 12a9.85 9.85 0 0 0-2 6.51 12.59 12.59 0 0 0 .9 4.94 7.5 7.5 0 0 0 2.56 3.27 6.52 6.52 0 0 0 3.84 1.15 7.91 7.91 0 0 0 2.71-.46 6.22 6.22 0 0 0 2.19-1.27l.44-4.14a5.14 5.14 0 0 1 1.32.13.76.76 0 0 1 .55.42l.28 4.78a9.44 9.44 0 0 1-3.58 2 14.6 14.6 0 0 1-4.62.73m18.94-1.46a3.49 3.49 0 0 0 3.11-1.71 9 9 0 0 0 1.1-4.84 9.5 9.5 0 0 0-1.07-4.93 3.37 3.37 0 0 0-3-1.75 3.44 3.44 0 0 0-3.09 1.72A9.19 9.19 0 0 0 92.31 22a9.46 9.46 0 0 0 1 4.91 3.33 3.33 0 0 0 3 1.71M95.65 30a7.33 7.33 0 0 1-5.4-1.95 7.15 7.15 0 0 1-2-5.31 9.13 9.13 0 0 1 1.13-4.54 8.21 8.21 0 0 1 3.13-3.14A9.22 9.22 0 0 1 97.14 14a7.46 7.46 0 0 1 5.44 2 7.13 7.13 0 0 1 2 5.34 8.92 8.92 0 0 1-1.15 4.55 8.2 8.2 0 0 1-3.16 3.08A9.42 9.42 0 0 1 95.65 30m10.84-.38c0-.91.19-1.42.56-1.53l1.6-.61V16.59l-1.6-.34c-.37-.09-.56-.58-.56-1.49l2.38-.34a13.5 13.5 0 0 1 1.38-.1 2 2 0 0 1 1.51.48 2.15 2.15 0 0 1 .47 1.56v.88h.24a10.9 10.9 0 0 1 3.13-2.41 7.34 7.34 0 0 1 3.33-.88c2 0 3.2 1.1 3.62 3.29h.1a11.92 11.92 0 0 1 3.2-2.41 7.29 7.29 0 0 1 3.3-.88 3.27 3.27 0 0 1 2.9 1.34 7.06 7.06 0 0 1 .93 4v8.1l2.15.58a2.68 2.68 0 0 1-.14.93 2 2 0 0 1-.49.7h-7.12c0-.91.19-1.42.56-1.53l1.21-.41v-8c0-2.11-.75-3.16-2.26-3.16a4.49 4.49 0 0 0-2.34.68 6.88 6.88 0 0 0-1.86 1.56 5.22 5.22 0 0 1 0 .58v8.24l1.81.44a2.68 2.68 0 0 1-.14.93 1.91 1.91 0 0 1-.49.7h-6.77c0-.91.18-1.42.55-1.53l1.22-.41v-8c0-2.11-.75-3.16-2.26-3.16a4.59 4.59 0 0 0-2.36.68 6.59 6.59 0 0 0-1.81 1.49v8.89l1.81.44a2.12 2.12 0 0 1-.66 1.63zm30.31 0c0-.91.18-1.42.55-1.53l1.6-.61V16.59l-1.6-.34c-.37-.09-.55-.58-.55-1.49 1-.16 1.83-.27 2.38-.34a13.14 13.14 0 0 1 1.37-.1 2 2 0 0 1 1.51.48 2.15 2.15 0 0 1 .47 1.56v.88h.24a10.9 10.9 0 0 1 3.13-2.41 7.35 7.35 0 0 1 3.34-.88c2 0 3.2 1.1 3.61 3.29h.15a11.88 11.88 0 0 1 3.19-2.41 7.29 7.29 0 0 1 3.31-.88 3.28 3.28 0 0 1 2.9 1.34 7.15 7.15 0 0 1 .92 4v8.1l2.15.58a2.46 2.46 0 0 1-.14.93 2 2 0 0 1-.48.7h-7.13c0-.91.19-1.42.56-1.53l1.22-.41v-8c0-2.11-.76-3.16-2.26-3.16a4.53 4.53 0 0 0-2.35.68 7.2 7.2 0 0 0-1.89 1.55 5.11 5.11 0 0 1 0 .58v8.24l1.8.44a2.46 2.46 0 0 1-.14.93 2 2 0 0 1-.48.7h-6.78c0-.91.19-1.42.56-1.53l1.21-.41v-8c0-2.11-.75-3.16-2.25-3.16a4.62 4.62 0 0 0-2.37.68 6.74 6.74 0 0 0-1.81 1.49v8.89l1.81.44a2.12 2.12 0 0 1-.66 1.63zm38.09-1.02a3.49 3.49 0 0 0 3.11-1.71 9 9 0 0 0 1.09-4.84 9.5 9.5 0 0 0-1.09-4.93 3.38 3.38 0 0 0-3-1.75 3.44 3.44 0 0 0-3.09 1.72 9.19 9.19 0 0 0-1.09 4.91 9.56 9.56 0 0 0 1 4.91 3.35 3.35 0 0 0 3 1.71m-.66 1.38a7.34 7.34 0 0 1-5.41-1.95 7.14 7.14 0 0 1-2-5.31 9.13 9.13 0 0 1 1.13-4.54 8.14 8.14 0 0 1 3.13-3.14 9.2 9.2 0 0 1 4.6-1.13 7.46 7.46 0 0 1 5.44 2 7.12 7.12 0 0 1 2 5.34 8.91 8.91 0 0 1-1.05 4.55 8.16 8.16 0 0 1-3.17 3.08 9.36 9.36 0 0 1-4.67 1.1m10.84-.38c0-.91.18-1.42.56-1.53l1.59-.61V16.59l-1.59-.34c-.38-.09-.56-.58-.56-1.49 1-.16 1.83-.27 2.38-.34a13.14 13.14 0 0 1 1.37-.1 2 2 0 0 1 1.51.48 2.11 2.11 0 0 1 .47 1.56v.88h.27a11.6 11.6 0 0 1 3.23-2.41 7.61 7.61 0 0 1 3.4-.88c2.69 0 4 1.78 4 5.36v8.1l2.15.58a3 3 0 0 1-.12.93 1.84 1.84 0 0 1-.47.7h-7.12c0-.91.18-1.42.55-1.53l1.22-.44v-8c0-2.11-.8-3.16-2.4-3.16a5.17 5.17 0 0 0-2.54.68 6.85 6.85 0 0 0-1.9 1.53v8.85l1.8.44a2.12 2.12 0 0 1-.66 1.63zm38.12.38a11.73 11.73 0 0 1-5.63-1.29 9.22 9.22 0 0 1-3.73-3.62 10.75 10.75 0 0 1-1.34-5.46 13 13 0 0 1 .89-4.89 11.62 11.62 0 0 1 2.48-3.88 11.3 11.3 0 0 1 3.77-2.56 12.12 12.12 0 0 1 4.75-.92 14.91 14.91 0 0 1 3.66.45 12 12 0 0 1 3.15 1.22l-.66 5.15c-1.07-.05-1.63-.28-1.67-.71l-.25-2.68a7.07 7.07 0 0 0-2.15-.88 10.65 10.65 0 0 0-2.71-.34 6.37 6.37 0 0 0-5.27 2.41 10.42 10.42 0 0 0-1.89 6.63 10.55 10.55 0 0 0 2 6.78 6.55 6.55 0 0 0 5.41 2.44 6.22 6.22 0 0 0 3.68-1.05v-5.15l-2.92-.58a2.58 2.58 0 0 1 .14-.9 2.16 2.16 0 0 1 .52-.73H233c0 .88-.19 1.4-.56 1.56l-1.18.41v6.24a13.58 13.58 0 0 1-3.86 1.76 15.37 15.37 0 0 1-4.31.61m12.16-.4c0-.89.19-1.39.56-1.53l1.6-.61V16.59l-1.6-.34c-.37-.09-.56-.58-.56-1.49l2.4-.34a12.81 12.81 0 0 1 1.35-.1 2.07 2.07 0 0 1 1.52.49 2.43 2.43 0 0 1 .47 1.68v1h.17a7.69 7.69 0 0 1 2.19-2.52 4.35 4.35 0 0 1 2.57-1 2.85 2.85 0 0 1 2 .61 2.15 2.15 0 0 1 .65 1.63 2 2 0 0 1-.52 1.37 1.75 1.75 0 0 1-1.33.56 3 3 0 0 0-.95-1.19 2.23 2.23 0 0 0-1.16-.3 3.19 3.19 0 0 0-1.74.61 6.84 6.84 0 0 0-1.67 1.56v8.58l2.61.57a2.23 2.23 0 0 1-.63 1.67zm21.48-1.02a3.49 3.49 0 0 0 3.11-1.71 9 9 0 0 0 1.09-4.84 9.61 9.61 0 0 0-1.05-4.93 3.39 3.39 0 0 0-3.05-1.75 3.44 3.44 0 0 0-3.09 1.72 9.19 9.19 0 0 0-1.08 4.91 9.46 9.46 0 0 0 1.05 4.91 3.33 3.33 0 0 0 3 1.71M256 30a7.31 7.31 0 0 1-5.4-1.95 7.15 7.15 0 0 1-2-5.31 9.13 9.13 0 0 1 1.13-4.54 8.21 8.21 0 0 1 3.13-3.14A9.24 9.24 0 0 1 257.5 14a7.43 7.43 0 0 1 5.43 2 7.13 7.13 0 0 1 2 5.34 8.82 8.82 0 0 1-1.15 4.55 8.2 8.2 0 0 1-3.16 3.08A9.42 9.42 0 0 1 256 30m16.54 0a3.64 3.64 0 0 1-3-1.27 6.28 6.28 0 0 1-1-4V16.7l-1.63-.34c-.35-.05-.52-.55-.52-1.5l2.32-.3a11.24 11.24 0 0 1 1.36-.1c1.51 0 2.26.66 2.26 2v8c0 2.08.86 3.12 2.57 3.12a4.14 4.14 0 0 0 2.1-.52 6.67 6.67 0 0 0 1.55-1.21V16.7l-1.63-.34c-.35-.05-.53-.55-.53-1.5a34.25 34.25 0 0 1 3.69-.4c1.5 0 2.26.66 2.26 2V26.7c0 .79.32 1.19 1 1.19a2.51 2.51 0 0 0 1.5-.51l.31 1.15a5.34 5.34 0 0 1-1.7 1.09 5.27 5.27 0 0 1-1.95.4 2.58 2.58 0 0 1-2-.73 2.78 2.78 0 0 1-.72-2.05h-.21a8.21 8.21 0 0 1-6 2.78m14.05-.4c0-.91.18-1.42.55-1.53l1.6-.61V16.59l-1.6-.34c-.37-.09-.55-.58-.55-1.49 1-.16 1.83-.27 2.38-.34a13.14 13.14 0 0 1 1.37-.1 2 2 0 0 1 1.51.48 2.11 2.11 0 0 1 .47 1.56v.88h.25a11.46 11.46 0 0 1 3.23-2.41 7.58 7.58 0 0 1 3.4-.88c2.69 0 4 1.78 4 5.36v8.1l2.16.58a3 3 0 0 1-.12.93 1.84 1.84 0 0 1-.47.7h-7.1c0-.91.19-1.42.56-1.53l1.22-.44v-8c0-2.11-.8-3.16-2.4-3.16a5.17 5.17 0 0 0-2.54.68 6.85 6.85 0 0 0-1.94 1.53v8.85l1.8.44a2.12 2.12 0 0 1-.66 1.63zm28.25-1.87a3.93 3.93 0 0 0 2.17-.58 7.83 7.83 0 0 0 1.69-1.42v-9a4.51 4.51 0 0 0-1.44-.82 5.79 5.79 0 0 0-1.86-.27 3.92 3.92 0 0 0-3.43 1.63 8 8 0 0 0-1.2 4.73 7.42 7.42 0 0 0 1 4.27 3.46 3.46 0 0 0 3 1.46m-2 2.27a5.66 5.66 0 0 1-3.2-.9 6 6 0 0 1-2.12-2.54 9 9 0 0 1-.76-3.82 9.88 9.88 0 0 1 1-4.56 8 8 0 0 1 2.82-3.12A7.61 7.61 0 0 1 314.8 14a11.72 11.72 0 0 1 1.93.17 9.4 9.4 0 0 1 1.93.54V7l-1.81-.34c-.37-.09-.55-.59-.55-1.49 1-.16 1.81-.27 2.39-.34a12.27 12.27 0 0 1 1.43-.1 2.61 2.61 0 0 1 1.75.49 2 2 0 0 1 .58 1.61V26.7c0 .74.31 1.12.94 1.12a2.54 2.54 0 0 0 1.53-.44l.34 1.15a6.58 6.58 0 0 1-1.73 1.09 5 5 0 0 1-1.95.4 2.6 2.6 0 0 1-2-.74 2.92 2.92 0 0 1-.72-2.11h-.21a6.53 6.53 0 0 1-2.41 2.06 7.3 7.3 0 0 1-3.39.79"
            fill="#0b0a0b"
          />
          <path
            d="m86.53 46.74 1.33 3.08h-2.65zm-.7-3.25-4.46 10.42h2.1l1-2.3h4.18l1 2.3h2.1l-4.5-10.42zm11.65 4.4a1.58 1.58 0 0 0 1.26-.42 1.27 1.27 0 0 0 .34-.88 1.3 1.3 0 0 0-.34-.89 1.55 1.55 0 0 0-1.26-.42h-1.82v2.61zm-1.82 1.78v4.24h-2V43.5h3.92a3.4 3.4 0 0 1 2.7.93 3.25 3.25 0 0 1 0 4.32 3.44 3.44 0 0 1-2.7.92zm9.68-6.17v10.42h-2.01V43.5h2.01zm2.31-.01v1.79h2.75v8.63h2.01v-8.63h2.75v-1.79h-7.51zm12.98 3.25 1.37 3.08h-2.7zm-.7-3.25-4.47 10.42h2.1l1-2.3h4.19l1 2.3h2.1l-4.47-10.42zm7.81 0v10.42h6.41v-1.78h-4.4v-8.64h-2.01zm-55.6 5.21a3.5 3.5 0 0 0 3.55 3.58 3.64 3.64 0 0 0 3-1.45L80 52.07a5.52 5.52 0 0 1-4.38 2 5.36 5.36 0 1 1 0-10.71 5.44 5.44 0 0 1 3.81 1.48l-1.29 1.36a3.62 3.62 0 0 0-2.54-1.07 3.48 3.48 0 0 0-3.46 3.57"
            fill="#b06027"
          />
        </svg>
      );
    } else if (userGroup.includes('DivvyHomes')) {
      userInfo.clientName = 'DivvyHomes';
      userInfo.shortName = 'DH';
      userInfo.png = '/images/logo/Divvy-Logo.png';
      userInfo.svg = (
        <svg
          key="Divvy logo"
          role="img"
          viewBox="0 0 124 44"
          fill="#1c2430"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M11.5289 8.76445C11.5289 7.23769 10.2912 6 8.76445 6C7.23769 6 6 7.23769 6 8.76445C6 10.2912 7.23769 11.5289 8.76445 11.5289C10.2912 11.5289 11.5289 10.2912 11.5289 8.76445ZM38.4362 11.3446H48.7983C50.7833 11.3446 59.2617 12.452 59.2617 21.3493C59.2617 30.2466 53.1252 32.3544 48.7983 32.3544H38.4362V11.3446ZM53.7607 21.7008C53.7607 18.5567 51.7563 16.0665 47.5649 16.0665H43.6142V27.7363H47.5649C51.5548 27.7363 53.7607 24.8666 53.7607 21.7008ZM65.5628 14.6398C65.5628 16.0139 64.4489 17.1278 63.0748 17.1278C61.7007 17.1278 60.5868 16.0139 60.5868 14.6398C60.5868 13.2657 61.7007 12.1518 63.0748 12.1518C64.4489 12.1518 65.5628 13.2657 65.5628 14.6398ZM60.5868 32.3544V18.3479H65.5628V32.3544H60.5868ZM83.3649 18.3479L77.5867 32.3544H72.5307L66.7782 18.3479H71.8084L75.1619 26.9017L78.6959 18.3479H83.3649ZM118.414 18.3479L112.378 33.1371C111.673 34.8853 110.813 36.1133 109.798 36.8213C108.784 37.5293 107.545 37.8833 106.084 37.8833C105.344 37.8833 104.6 37.7653 103.852 37.5293C103.104 37.2933 102.498 36.983 102.034 36.5984L103.736 33.1109C104.011 33.3731 104.342 33.5785 104.729 33.7271C105.116 33.8757 105.507 33.95 105.903 33.95C106.367 33.95 106.75 33.8539 107.051 33.6616C107.352 33.4693 107.606 33.1634 107.812 32.7438L101.827 18.3479H106.857L110.288 27.0274L113.745 18.3479H118.414ZM94.9456 32.3544L100.724 18.3479H96.0548L92.5208 26.9017L89.1673 18.3479H84.1371L89.8896 32.3544H94.9456ZM17.971 6.5529L29.9585 18.5592V32.5387H6V18.5592L17.971 6.5529ZM17.9259 13.4887L24.977 20.5672V27.9098L10.8682 27.9248V20.5672L17.9259 13.4887ZM15.9853 22.2765C17.0653 23.3586 18.8163 23.3586 19.8962 22.2765C20.9762 21.1944 20.9762 19.44 19.8962 18.3579C18.8163 17.2758 17.0653 17.2758 15.9853 18.3579C14.9053 19.44 14.9053 21.1944 15.9853 22.2765Z"
          ></path>
        </svg>
      );
    } else if (userGroup.includes('MarketplaceHomes')) {
      userInfo.clientName = 'MarketplaceHomes';
      userInfo.shortName = 'MPH';
      userInfo.png = '/images/logo/Marketplace-Logo.png';
      userInfo.flex = '400px';
      userInfo.svg = (
        <svg
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 156 30"
        >
          <path
            d="M58.7 8.59998 61.5 14.2l2.8-5.60002h1.6V17h-1.3v-6.3l-2.7 5.4H61l-2.7-5.4V17H57V8.59998h1.7ZM74.3999 15.1h-4.2l-.8 1.9h-1.5l3.7-8.40002h1.4L76.6999 17h-1.5l-.8-1.9Zm-.5-1.3-1.6-3.7-1.6 3.7h3.2ZM84.0998 17l-1.5-2.5h-2.4V17h-1.4V8.59998h3.4c2.2 0 3.4 1 3.4 2.90002 0 1.3-.6 2.3-1.7 2.7l1.9 2.8h-1.7Zm-1.9-3.8c1.3 0 2-.5 2-1.7 0-1.1-.7-1.60003-2-1.60003h-2V13.2h2ZM93.8998 17l-2.8-3.7-1.4 1.6V17h-1.4V8.59998h1.4V12.9l4-4.30002h1.7l-3.2 3.60002 3.5 4.8h-1.8ZM103.8 8.59998v1.29999h-4.6002V12.2H103.3v1.3h-4.1002v2.3H103.9V17h-6.1002V8.59998H103.8ZM112.3 8.59998v1.29999h-2.7V17h-1.4V9.89997h-2.6V8.59998h6.7ZM121.1 11.5c0 1.9-1.2 3-3.3 3h-1.9V17h-1.4V8.59998h3.4c2 0 3.2 1 3.2 2.90002Zm-1.3 0c0-1.1-.7-1.60003-2-1.60003h-1.9V13.2h1.9c1.2 0 2-.6 2-1.7ZM124.8 8.59998V15.7h3.6V17h-5.1V8.59998h1.5ZM136.2 15.1H132l-.8 1.9h-1.5l3.7-8.40002h1.4L138.5 17H137l-.8-1.9Zm-.5-1.3-1.6-3.7-1.6 3.7h3.2ZM144.1 9.8c-1.7 0-3 1.3-3 2.9 0 1.6 1.3 3 3 3 .8 0 1.7-.4 2.3-1l.8.9c-.9.8-2.1 1.4-3.2 1.4-2.5 0-4.4-1.9-4.4-4.3s1.9-4.2 4.4-4.2c1.2 0 2.4.5 3.2 1.3l-.8 1c-.6-.6-1.5-1-2.3-1ZM155.6 8.59998v1.29999H151V12.2h4.1v1.3H151v2.3h4.7V17h-6.1V8.59998h6ZM58.4001 20.6v3.7h4.5v-3.7h1.4V29h-1.4v-3.5h-4.4V29h-1.4v-8.4h1.3ZM75.5998 24.8c0 2.4-1.9 4.3-4.4 4.3-2.5 0-4.4-1.9-4.4-4.3s1.9-4.2 4.4-4.2c2.5 0 4.4 1.8 4.4 4.2Zm-7.5 0c0 1.7 1.4 3 3 3s3-1.3 3-3-1.3-3-3-3-3 1.3-3 3ZM79.7 20.6l2.7999 5.6L85.3 20.6h1.6999V29H85.7v-6.3l-2.7001 5.4h-.9L79.4 22.7V29h-1.3001v-8.4H79.7ZM96 20.6v1.3h-4.6v2.3h4.1v1.3h-4.1v2.3h4.7V29H90v-8.4h6ZM101.4 21.9c-.8 0-1.4001.3-1.4001.9 0 1.8 4.5001.8 4.5001 3.9 0 1.5-1.3 2.4-3.2 2.4-1.3001 0-2.6001-.5-3.4001-1.3l.6-1.2c.9.8 1.9001 1.2 2.8001 1.2 1 0 1.6-.4 1.6-1 0-1.9-4.5001-.8-4.5001-3.8 0-1.4 1.2-2.3 3.1001-2.3 1.1 0 2.2.3 2.9.9l-.6 1.2c-.8-.7-1.7-.9-2.4-.9Z"
            fill="#173257"
          />
          <path
            d="m3.8 15 12-10.1 5.8 4.9 2.9-2.4L15.8 0 .199999 13.2 0 13.3V30h16.9v-3.7H3.8V15Z"
            fill="#F4752A"
          />
          <path
            d="m35.4001 0-9.3 7.8-3.4 2.9-.8.7-.3.2-2 1.7V30h11.9V13.3l-2.1-1.8-2.9 2.5 1.2 1v11.3h-4.3V15l1.7-1.5 3.8-3.1 6.5-5.5 11.9 10.1v11.3h-13.1V30h16.9V13.3L35.4001 0Z"
            fill="#F4752A"
          />
        </svg>
      );
    } else if (userGroup.includes('SVN')) {
      userInfo.clientName = 'SVN';
      userInfo.shortName = 'SVN';
      userInfo.png = '/images/logo/SVN_logo.png';
      userInfo.flex = '350px';
      userInfo.svg = (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 122 46">
          <defs>
            <path
              id="a"
              d="M.29329135.55155842h7.31756826V31.5845791H.29329135z"
            />
          </defs>
          <g fill="none" fill-rule="evenodd">
            <path
              d="M119.735717 8.26421212c.285038 0 .437288-.1519934.437288-.36226997v-.01371369c0-.24341799-.17171-.36912681-.451025-.36912681h-.551762v.74511047h.565499Zm-1.128708-1.2410889h1.134431c.346855 0 .611288.09942425.789867.27770221.140802.1382797.21292.33027135.21292.55540442v.01142808c0 .41026787-.224368.66739955-.550617.78625153l.622735.91081754h-.655932l-.543749-.81825014h-.446446V9.564727h-.563209V7.02312322Zm3.113676 1.34051314v-.01371369c0-1.16794922-.910063-2.11190817-2.117757-2.11190817-1.214563 0-2.132639.95767264-2.132639 2.12562186v.01257089c0 1.16909202.910063 2.11190815 2.117757 2.11190815 1.216852 0 2.132639-.95652982 2.132639-2.12447904Zm-4.528567.01257089v-.01257089c0-1.30051488 1.062313-2.38961039 2.41081-2.38961039 1.347351 0 2.397072 1.07538182 2.397072 2.3758967v.01371369c0 1.29937208-1.062313 2.38961044-2.411954 2.38961044-1.347352 0-2.395928-1.07652467-2.395928-2.37703955Z"
              fill="#0B2A6D"
            />
            <path
              d="M32.4730729 12.1509434c6.2369474.6111016 10.6217209 2.1848436 11.581162 5.7803558 1.5986819 5.9913918-6.9237541 13.3076836-19.0356831 16.3400569-12.1119291 3.0335018-23.22729229.6342366-24.82597412-5.3577195-1.15144536-4.3155175 2.95199267-9.2590646 9.86182502-12.8534483-5.6452727 3.1875469-8.90818462 7.4872648-7.92554057 11.1708028 1.43916168 5.392704 11.42512237 6.8344074 22.32585847 4.1050457 10.9013162-2.7299259 18.5890268-8.5960504 17.1498651-13.9893187-.7912199-2.9629684-4.1672461-4.2218491-9.1239719-4.9576538l-.0075409-.2381209Z"
              fill="#F26C21"
            />
            <path
              d="M42.5117967 26.0377358c-2.5562443 4.4915346-9.2936147 8.8141085-17.8102363 10.9739875-8.126124 2.0601922-15.77370262 1.2745257-20.36362196-1.2598825 5.48469061 1.2024359 12.84983026 2.0173889 20.51684796-.1002499 7.5429504-2.0821571 12.9636063-4.6723223 17.6570103-9.6138551M44.2469721 30.6663824l3.6119214-3.0078486c1.3548992 1.8288129 3.612493 3.454677 6.1576456 3.454677 2.8321396 0 4.474026-1.259192 4.474026-3.332453 0-2.1539857-1.3543276-3.4143146-4.2282004-4.6746436l-2.2981836-.9755185c-4.3922747-1.9101061-6.5681172-4.6740751-6.5681172-8.4135626 0-4.63371276 4.2282004-7.6415614 8.6616367-7.6415614 3.3649523 0 6.0335894 1.13867337 8.1682704 3.65819429l-3.2837728 3.37395231c-1.5195453-1.789019-2.8327113-2.5610202-4.9256591-2.5610202-1.970035 0-3.8588904 1.1381048-3.8588904 3.1295042 0 1.8697437 1.3137378 3.2113659 4.2282005 4.4313324l2.2575937 1.0153124c3.9812315 1.7071573 6.6904584 4.2272467 6.6904584 8.2515447 0 4.8775924-3.6530829 8.2106139-9.2762007 8.2106139-4.3922748 0-7.9636063-1.9516055-9.8107285-4.9185233M62.4663136 6.0754717h5.2320609l8.2155482 18.6589572 8.2559657-18.6589572h5.1916435l-13.4476092 29.509434zM95.7850706 16.8467448v18.4124133h-4.6883633V6.0754717L110.700799 24.813064V6.40065079h4.688364V35.5849057zM31.2331568 27.2181033V9.54716981l-3.5562505 2.37906439-1.6492756 1.1029379v16.4654497l.0005206.0148122c1.8897949-.6716755 3.6395472-1.4430484 5.2013613-2.2844943l.0036442-.0068364ZM8.67587688 31.509874c1.53706272.4404926 3.24415922.669502 5.18212702.5865445l.0233991-2.9736171V9.54716981l-1.6992967 1.16666279-3.50050963 2.4045989v16.6540088l-.00571979 1.7374337ZM23.4248676 30.5726415c.2489977-.0642264.4971277-.1293207.7443902-.195283"
              fill="#0B2A6D"
            />
            <g transform="translate(17.351754)">
              <path
                d="M.37978317 31.5845791c2.4644162-.2039471 5.19611614-.7324065 7.23107644-1.194386l-.00360383-.0174651V.5513894L.29329136 4.75934639"
                fill="#0B2A6D"
                mask="url(#b)"
              />
            </g>
            <path
              d="M46.5892544 43.0790648c-.8252453-.2522578-1.517037-.382591-1.517037-.9811778 0-.5407777.5189863-.7714885 1.0847385-.7714885.4716502 0 1.1634419.1802593 1.6350921.4041381l.3142434-.4619472c-.5030176-.2449002-1.2968956-.4761366-1.9727184-.4761366-.998621 0-1.7685458.4908517-1.7685458 1.3416963 0 .8797491.8412142 1.182984 1.587756 1.4063373.9273317.2885198 1.6738735.4976836 1.6738735 1.1178174 0 .5481352-.5657521.8077505-1.1634418.8077505-.6284868 0-1.4143804-.2879943-1.9019994-.5407777l-.3142434.4619471C44.757974 45.6683861 45.6305555 46 46.4632149 46c.9826521 0 1.8632179-.4687791 1.8632179-1.3847903 0-.9591052-.9666833-1.2980766-1.7371784-1.5361449Zm6.2803049-1.6659526v-.5413033H49.379804v5.048835h.6838073v-2.2577074h2.2401959v-.5334202h-2.2401959v-1.7164041h2.805948Zm4.4250714 4.5075317h.7545263l-1.5010681-2.1709938c.8799955-.1082606 1.493654-.5192306 1.493654-1.4205268 0-1.0747234-.8571829-1.4573144-2.0594062-1.4573144h-2.1381096v5.048835h.6838073v-2.1420892h1.3442316l1.4223647 2.1420892Zm.0553206-3.5915206c0 .6921324-.581721.9375582-1.3915678.9375582h-1.4303491v-1.8824739h1.4697008c.7704951 0 1.3522161.2522578 1.3522161.9449157Zm5.2896684 3.5915206h.6758228v-5.048835h-.6758228v2.1352573h-2.7432135v-2.1352573h-.6838073v5.048835h.6838073v-2.3801575h2.7432135v2.3801575Zm6.2329688-5.048835h-.6758228v3.2241702c0 .7572989-.2515088 1.3632432-1.3835834 1.3632432-1.1394887 0-1.383013-.6059443-1.383013-1.3632432v-3.2241702h-.6838073v3.2026231c0 1.0820809.4716502 1.925568 2.0668203 1.925568 1.5883263 0 2.0594062-.8434871 2.0594062-1.925568v-3.2026231Zm5.5497319 3.7003068c0-.9522732-.8018624-1.2696977-1.2501297-1.3275067.3695639-.0572836.9512848-.417802.9512848-1.1393645 0-.7930355-.6838072-1.2334356-1.9019993-1.2334356H70.303508v5.048835h2.2949461c1.2107781 0 1.8238663-.5118731 1.8238663-1.3485282Zm-.9672536-2.3801576c0 .5770398-.5497834.8366551-1.2729424.8366551h-1.1948092v-1.6517631h1.234161c.8566126 0 1.2335906.3389714 1.2335906.815108Zm.2988448 2.2860864c0 .5407777-.3849624.9375582-1.2261765.9375582h-1.5404199v-1.9040209h1.2495595c.9826521 0 1.5170369.3247819 1.5170369.9664627Zm7.4825283 1.4425994h.7391278l-2.1295549-5.048835h-.7391278l-2.1301253 5.048835h.7391278l.4870487-1.2329101h2.546455l.4870487 1.2329101Zm-1.759991-4.4717952 1.0687696 2.7264865h-2.146094l1.0773244-2.7264865Zm7.7186385 1.9476405c0-1.8031178-.9906366-2.5246803-2.5937911-2.5246803h-1.7058111v5.048835h1.7058111c1.6031545 0 2.5937911-.7210369 2.5937911-2.5241547Zm-.6997761 0c0 1.3916222-.6130883 1.983377-1.9413511 1.983377h-.9746677v-3.966754h.9746677c1.3282628 0 1.9413511.5985867 1.9413511 1.983377Zm1.9807028-2.5246803h-.7699248l2.1301252 5.048835h.7385575l2.1221408-5.048835h-.7625107l-1.7371784 4.3997967-1.7212096-4.3997967Zm5.1408163 5.048835h.6838072v-5.048835h-.6838072v5.048835Zm4.1188124-2.8415791c-.8258156-.2522578-1.517037-.382591-1.517037-.9811778 0-.5407777.518416-.7714885 1.0841682-.7714885.4722205 0 1.1634419.1802593 1.6350921.4041381l.3142434-.4619472c-.5030176-.2449002-1.2968956-.4761366-1.9727184-.4761366-.9980507 0-1.7685458.4908517-1.7685458 1.3416963 0 .8797491.8412141 1.182984 1.587756 1.4063373.9273316.2885198 1.6744438.4976836 1.6744438 1.1178174 0 .5481352-.5663224.8077505-1.1634418.8077505-.6290571 0-1.4149507-.2879943-1.9019994-.5407777l-.3148137.4619471C95.9037921 45.6683861 96.7763735 46 97.6096033 46c.9826521 0 1.8626476-.4687791 1.8626476-1.3847903 0-.9591052-.9666833-1.2980766-1.7366081-1.5361449Zm4.9440582-2.286612c-1.753148 0-2.413002 1.1687946-2.413002 2.6040364 0 1.4278843.659854 2.6035108 2.413002 2.6035108 1.760561 0 2.420415-1.1756265 2.420415-2.6035108 0-1.4352418-.659854-2.6040364-2.420415-2.6040364Zm0 4.6667695c-1.281498 0-1.713796-.9160112-1.713796-2.0627331 0-1.146722.432298-2.0627332 1.713796-2.0627332 1.296895 0 1.721209.9160112 1.721209 2.0627332 0 1.1467219-.424314 2.0627331-1.721209 2.0627331Zm7.097565.4614216h.754527l-1.501069-2.1709938c.879996-.1082606 1.493084-.5192306 1.493084-1.4205268 0-1.0747234-.856612-1.4573144-2.059406-1.4573144h-2.137539v5.048835h.683807v-2.1420892h1.343661l1.422935 2.1420892Zm.054751-3.5915206c0 .6921324-.581151.9375582-1.390998.9375582h-1.430349v-1.8824739h1.469701c.770495 0 1.351646.2522578 1.351646.9449157Zm3.819967.7499415c-.824675-.2522578-1.516467-.382591-1.516467-.9811778 0-.5407777.518416-.7714885 1.084739-.7714885.47165 0 1.162872.1802593 1.634522.4041381l.314813-.4619472c-.503587-.2449002-1.296895-.4761366-1.973288-.4761366-.998051 0-1.768546.4908517-1.768546 1.3416963 0 .8797491.841214 1.182984 1.587756 1.4063373.927902.2885198 1.674444.4976836 1.674444 1.1178174 0 .5481352-.565752.8077505-1.163442.8077505-.628487 0-1.414951-.2879943-1.901999-.5407777l-.314814.4619471c.511002.2811624 1.383583.6127763 2.216813.6127763.982652 0 1.862648-.4687791 1.862648-1.3847903 0-.9591052-.966684-1.2980766-1.737179-1.5361449Z"
              fill="#0B2A6D"
            />
          </g>
        </svg>
      );
    } else if (userGroup.includes('Castle')) {
      userInfo.clientName = 'Castle';
      userInfo.shortName = 'Castle';
      userInfo.png = '/images/logo/Castle_logo.png';
      userInfo.flex = '350px';
      userInfo.svg = (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          xmlSpace="preserve"
          viewBox="129.69 106.35 623.29 426.96"
        >
          <path
            d="M494.22 106.35v31.76h-27.43v-31.76h-50.93v31.76h-27.43v-31.76h-49.59v196.34h64.71v57.08H543.8V106.35h-49.58zm38.47 242.3H414.68v-45.98h64.27v-77.66c0-20.79-16.91-37.7-37.7-37.7-20.79 0-37.7 16.91-37.7 37.7v66.55h-53.58V117.48h27.34v31.76h49.67v-31.76h28.68v31.76h49.67v-31.76h27.35v231.17zm-118.01-57.1v-66.53c0-14.65 11.92-26.57 26.57-26.57s26.57 11.92 26.57 26.57v66.53h-53.14z"
            fill="#092032"
          />
          <path d="M181.54 476.59c-1.68 1.34-3.53 2.48-5.53 3.44s-4.19 1.68-6.57 2.18c-2.38.49-5.05.74-8.01.74-4.47 0-8.62-.81-12.45-2.44-3.83-1.62-7.14-3.84-9.93-6.66-2.79-2.81-4.96-6.14-6.53-9.97-1.57-3.83-2.35-7.92-2.35-12.28v-.17c0-4.35.78-8.43 2.35-12.23 1.57-3.8 3.74-7.14 6.53-10.01s6.12-5.14 10.01-6.79c3.89-1.65 8.18-2.48 12.89-2.48 2.84 0 5.44.23 7.79.7 2.35.46 4.48 1.1 6.4 1.91 1.92.81 3.69 1.8 5.31 2.96 1.62 1.16 3.13 2.44 4.53 3.83l-8.53 9.84c-2.38-2.15-4.8-3.83-7.27-5.05-2.47-1.22-5.24-1.83-8.31-1.83-2.55 0-4.92.49-7.1 1.48-2.18.99-4.05 2.35-5.62 4.09-1.57 1.74-2.79 3.76-3.66 6.05-.87 2.29-1.31 4.75-1.31 7.36v.17c0 2.61.44 5.08 1.31 7.4.87 2.32 2.07 4.35 3.61 6.09 1.54 1.74 3.4 3.12 5.57 4.14s4.57 1.52 7.18 1.52c3.48 0 6.43-.64 8.84-1.92 2.41-1.28 4.8-3.02 7.18-5.22l8.53 8.62c-1.55 1.69-3.18 3.2-4.86 4.53zm115.89 5.31-5.57-13.67h-25.77l-5.57 13.67h-13.67l26.12-61.38h12.36l26.12 61.38h-14.02zm-18.46-45.27-8.1 19.76h16.19l-8.09-19.76zm139.23 35.3c-1.1 2.35-2.62 4.32-4.59 5.92-1.96 1.6-4.3 2.82-7.02 3.66-2.72.84-5.73 1.26-9.04 1.26-4.65 0-9.2-.8-13.67-2.39-4.47-1.6-8.53-4.05-12.19-7.36l7.92-9.49c2.79 2.26 5.64 4.06 8.58 5.4 2.93 1.34 6.14 2 9.62 2 2.79 0 4.95-.51 6.49-1.52 1.54-1.02 2.31-2.42 2.31-4.22v-.17c0-.87-.16-1.64-.48-2.31-.32-.67-.93-1.29-1.83-1.87-.9-.58-2.15-1.16-3.75-1.74-1.6-.58-3.68-1.19-6.24-1.83-3.09-.75-5.88-1.6-8.38-2.52-2.5-.93-4.63-2.08-6.37-3.44-1.75-1.36-3.1-3.06-4.06-5.09-.96-2.03-1.44-4.56-1.44-7.58v-.17c0-2.79.52-5.3 1.56-7.53 1.04-2.23 2.49-4.16 4.37-5.79 1.88-1.62 4.11-2.87 6.72-3.74 2.6-.87 5.47-1.31 8.6-1.31 4.47 0 8.58.67 12.32 2 3.74 1.34 7.18 3.25 10.32 5.75l-6.97 10.1c-2.73-1.86-5.4-3.32-8.01-4.4-2.61-1.07-5.22-1.61-7.84-1.61s-4.57.51-5.88 1.52c-1.31 1.02-1.96 2.28-1.96 3.79v.17c0 .99.19 1.84.57 2.57.38.73 1.06 1.38 2.05 1.96.99.58 2.34 1.13 4.06 1.65 1.72.52 3.88 1.13 6.5 1.83 3.08.81 5.83 1.73 8.25 2.74 2.41 1.02 4.45 2.23 6.11 3.66 1.66 1.42 2.91 3.09 3.75 5.01.84 1.91 1.27 4.21 1.27 6.88v.17c-.01 3.01-.55 5.69-1.65 8.04zm95.85-38.61v48.58h-13.41v-48.58H482.1v-12.36h50.5v12.36h-18.55zm83.76 48.58v-60.95h13.41v48.85h30.39v12.1h-43.8zm108.84 0v-60.95h45.89v11.93h-32.65v12.36h28.73v11.93h-28.73v12.8h33.09v11.93h-46.33zM151 530.62c-.73.56-1.51 1.03-2.35 1.43-.84.4-1.74.7-2.71.93-.96.22-2.04.33-3.23.33-1.88 0-3.61-.35-5.19-1.04-1.58-.69-2.95-1.64-4.12-2.84-1.16-1.2-2.07-2.61-2.73-4.25-.66-1.63-.98-3.39-.98-5.27 0-1.85.33-3.6.98-5.23.66-1.63 1.56-3.06 2.73-4.28 1.16-1.22 2.55-2.19 4.15-2.89 1.61-.7 3.36-1.06 5.27-1.06 1.19 0 2.26.1 3.21.3.95.2 1.84.48 2.65.83.82.36 1.57.79 2.26 1.3.69.51 1.36 1.07 2 1.69l-2 2.15c-1.11-1.04-2.31-1.89-3.58-2.56-1.27-.67-2.8-1-4.58-1-1.43 0-2.76.27-3.99.82-1.22.54-2.29 1.3-3.19 2.26-.9.96-1.61 2.1-2.11 3.39-.51 1.3-.76 2.7-.76 4.21 0 1.51.25 2.92.76 4.23.51 1.31 1.21 2.45 2.11 3.41.9.96 1.97 1.72 3.19 2.28 1.22.56 2.55.83 3.99.83 1.78 0 3.31-.33 4.6-.98 1.28-.66 2.55-1.58 3.78-2.76l1.93 1.89c-.66.7-1.36 1.33-2.09 1.88zm42.85-5.63c-.64 1.62-1.55 3.05-2.71 4.28-1.16 1.24-2.56 2.22-4.19 2.95-1.63.73-3.44 1.09-5.41 1.09-1.98 0-3.78-.36-5.4-1.08-1.62-.72-3.01-1.69-4.17-2.91-1.16-1.22-2.06-2.65-2.69-4.27-.63-1.62-.95-3.33-.95-5.14 0-1.8.32-3.52.96-5.14.64-1.62 1.54-3.05 2.71-4.28 1.16-1.24 2.56-2.22 4.19-2.95 1.63-.73 3.44-1.09 5.41-1.09s3.78.36 5.4 1.08c1.62.72 3.01 1.69 4.17 2.91 1.16 1.22 2.06 2.65 2.69 4.26.63 1.62.95 3.33.95 5.14.01 1.82-.31 3.53-.96 5.15zm-2.83-9.25c-.51-1.31-1.22-2.45-2.13-3.41-.91-.96-2-1.73-3.25-2.3-1.25-.57-2.61-.85-4.1-.85s-2.85.28-4.1.83c-1.25.56-2.32 1.32-3.21 2.28-.89.96-1.59 2.09-2.1 3.39-.51 1.3-.76 2.69-.76 4.17s.25 2.88.76 4.19c.51 1.31 1.22 2.45 2.13 3.41.91.97 2 1.73 3.24 2.3 1.25.57 2.61.85 4.1.85s2.85-.28 4.1-.83c1.25-.56 2.32-1.32 3.21-2.28.89-.96 1.59-2.1 2.1-3.39.51-1.3.76-2.69.76-4.17s-.25-2.88-.75-4.19zm41.1-8.83h2.86v25.96h-2.34l-16.76-21.29v21.29h-2.86v-25.96h2.74l16.35 20.8v-20.8zm24.78 8.13c.2.5.56.95 1.08 1.36.52.41 1.24.78 2.15 1.11.91.33 2.09.65 3.52.95 2.89.64 5.03 1.54 6.4 2.68 1.37 1.14 2.06 2.71 2.06 4.72 0 1.11-.22 2.13-.66 3.05-.44.92-1.05 1.7-1.82 2.34-.77.64-1.7 1.14-2.79 1.48-1.09.35-2.27.52-3.56.52-2.08 0-3.97-.34-5.69-1.01-1.72-.67-3.36-1.71-4.91-3.11l1.82-2.15c1.36 1.24 2.73 2.16 4.12 2.76 1.38.61 2.98.91 4.78.91 1.75 0 3.16-.41 4.21-1.23 1.05-.82 1.58-1.89 1.58-3.23 0-.62-.1-1.17-.3-1.65-.2-.48-.54-.92-1.04-1.32s-1.18-.75-2.06-1.08c-.88-.32-2-.63-3.36-.93-1.49-.32-2.78-.69-3.88-1.11-1.1-.42-2.01-.93-2.73-1.52-.72-.59-1.25-1.29-1.6-2.08-.35-.79-.52-1.73-.52-2.82 0-1.04.21-1.99.63-2.86.42-.87 1.01-1.62 1.78-2.25.77-.63 1.67-1.13 2.71-1.49s2.18-.54 3.41-.54c1.9 0 3.57.26 4.99.79 1.42.52 2.8 1.31 4.13 2.37l-1.71 2.26c-1.21-.99-2.43-1.71-3.65-2.15-1.22-.45-2.5-.67-3.84-.67-.84 0-1.6.11-2.28.33-.68.22-1.26.52-1.74.89s-.85.82-1.11 1.34c-.26.52-.39 1.08-.39 1.67-.03.62.07 1.17.27 1.67zm54.4 6.74c0 1.9-.26 3.57-.78 5.01-.52 1.43-1.26 2.63-2.21 3.6-.95.96-2.09 1.69-3.41 2.17s-2.8.72-4.43.72c-1.61 0-3.07-.24-4.39-.72-1.32-.48-2.46-1.2-3.41-2.15-.95-.95-1.69-2.13-2.21-3.54-.52-1.41-.78-3.03-.78-4.86v-15.09h2.93v14.91c0 2.82.7 4.98 2.1 6.49 1.4 1.51 3.34 2.26 5.84 2.26 2.4 0 4.3-.72 5.71-2.15 1.41-1.43 2.11-3.57 2.11-6.42v-15.09h2.93v14.86zm31.41 3.97h-.15l-9.46-13.87v20.99h-2.86v-25.96h2.97l9.46 14.17 9.46-14.17h2.97v25.96h-2.93v-21.03l-9.46 13.91zm50.51-16.17h-15.84v8.86h14.17v2.67h-14.17v9.09h16.02v2.67h-18.95v-25.96h18.77v2.67zm35.97 23.29-7.53-10.09h-7.42v10.09h-2.93v-25.96h11.16c1.43 0 2.73.19 3.88.56 1.15.37 2.13.9 2.95 1.58.82.68 1.45 1.5 1.89 2.45.44.95.67 2.01.67 3.17 0 1.09-.17 2.06-.52 2.91-.35.85-.83 1.6-1.46 2.23-.63.63-1.38 1.16-2.24 1.58-.87.42-1.81.72-2.82.89l7.97 10.6h-3.6zm-2-21.9c-1.16-.9-2.8-1.36-4.91-1.36h-8.03v10.57h7.99c.97 0 1.86-.12 2.68-.37.82-.25 1.53-.6 2.12-1.06.59-.46 1.06-1.02 1.39-1.69.33-.67.5-1.43.5-2.27 0-1.64-.58-2.91-1.74-3.82zm70.28 21.9h-3.15l-3.04-6.86h-14.13l-3.08 6.86h-3l11.83-26.14h2.74l11.83 26.14zm-13.24-22.7-5.93 13.24h11.83l-5.9-13.24zm52.18 14.82c-.67 1.6-1.61 2.97-2.84 4.13s-2.68 2.08-4.38 2.74c-1.69.67-3.55 1-5.58 1h-9.01V506.9h9.01c2.03 0 3.89.33 5.58.98 1.69.66 3.15 1.56 4.38 2.73 1.22 1.16 2.17 2.53 2.84 4.12.67 1.58 1 3.29 1 5.12s-.34 3.54-1 5.14zm-2.8-9.11c-.51-1.26-1.22-2.35-2.15-3.28s-2.06-1.66-3.39-2.19c-1.34-.53-2.82-.8-4.45-.8h-6.08v20.55h6.08c1.63 0 3.11-.26 4.45-.78s2.47-1.24 3.39-2.17c.93-.93 1.64-2.01 2.15-3.24.51-1.24.76-2.58.76-4.04 0-1.44-.26-2.79-.76-4.05zm39.88-8.97h3.15l-11.16 26.14h-2.6l-11.16-26.14h3.26l9.23 22.36 9.28-22.36zm23.48 0v25.96h-2.93v-25.96h2.93zm22.17 8.13c.2.5.56.95 1.08 1.36.52.41 1.24.78 2.15 1.11.91.33 2.09.65 3.52.95 2.89.64 5.03 1.54 6.4 2.68 1.37 1.14 2.06 2.71 2.06 4.72 0 1.11-.22 2.13-.66 3.05-.44.92-1.05 1.7-1.82 2.34-.77.64-1.7 1.14-2.79 1.48-1.09.35-2.27.52-3.56.52-2.08 0-3.97-.34-5.69-1.01-1.72-.67-3.36-1.71-4.91-3.11l1.82-2.15c1.36 1.24 2.73 2.16 4.12 2.76 1.38.61 2.98.91 4.78.91 1.75 0 3.16-.41 4.21-1.23 1.05-.82 1.58-1.89 1.58-3.23 0-.62-.1-1.17-.3-1.65-.2-.48-.54-.92-1.04-1.32s-1.18-.75-2.06-1.08c-.88-.32-2-.63-3.36-.93-1.49-.32-2.78-.69-3.88-1.11-1.1-.42-2.01-.93-2.73-1.52-.72-.59-1.25-1.29-1.6-2.08-.35-.79-.52-1.73-.52-2.82 0-1.04.21-1.99.63-2.86s1.01-1.62 1.78-2.25c.77-.63 1.67-1.13 2.71-1.49s2.18-.54 3.41-.54c1.9 0 3.57.26 4.99.79 1.42.52 2.8 1.31 4.13 2.37l-1.71 2.26c-1.21-.99-2.43-1.71-3.65-2.15-1.22-.45-2.5-.67-3.84-.67-.84 0-1.6.11-2.28.33-.68.22-1.26.52-1.74.89s-.85.82-1.11 1.34c-.26.52-.39 1.08-.39 1.67-.02.62.07 1.17.27 1.67zm57.56 9.95c-.64 1.62-1.55 3.05-2.71 4.28-1.16 1.24-2.56 2.22-4.19 2.95-1.63.73-3.44 1.09-5.41 1.09-1.98 0-3.78-.36-5.4-1.08-1.62-.72-3.01-1.69-4.17-2.91-1.16-1.22-2.06-2.65-2.69-4.27-.63-1.62-.95-3.33-.95-5.14 0-1.8.32-3.52.96-5.14.64-1.62 1.54-3.05 2.71-4.28s2.56-2.22 4.19-2.95c1.63-.73 3.44-1.09 5.41-1.09 1.98 0 3.78.36 5.4 1.08 1.62.72 3.01 1.69 4.17 2.91 1.16 1.22 2.06 2.65 2.69 4.26.63 1.62.95 3.33.95 5.14 0 1.82-.32 3.53-.96 5.15zm-2.84-9.25c-.51-1.31-1.22-2.45-2.13-3.41-.91-.96-2-1.73-3.25-2.3-1.25-.57-2.61-.85-4.1-.85s-2.85.28-4.1.83c-1.25.56-2.32 1.32-3.21 2.28-.89.96-1.59 2.09-2.1 3.39-.51 1.3-.76 2.69-.76 4.17s.25 2.88.76 4.19c.51 1.31 1.22 2.45 2.13 3.41.91.97 2 1.73 3.24 2.3 1.25.57 2.61.85 4.1.85s2.85-.28 4.1-.83c1.25-.56 2.32-1.32 3.21-2.28.89-.96 1.59-2.1 2.1-3.39.51-1.3.76-2.69.76-4.17s-.24-2.88-.75-4.19zm39.89 17.13-7.53-10.09h-7.42v10.09h-2.93v-25.96h11.16c1.43 0 2.73.19 3.88.56 1.15.37 2.13.9 2.95 1.58.82.68 1.45 1.5 1.89 2.45.44.95.67 2.01.67 3.17 0 1.09-.17 2.06-.52 2.91s-.83 1.6-1.46 2.23c-.63.63-1.38 1.16-2.24 1.58-.87.42-1.81.72-2.82.89l7.97 10.6h-3.6zm-2.01-21.9c-1.16-.9-2.8-1.36-4.91-1.36h-8.03v10.57h7.99c.97 0 1.86-.12 2.68-.37.82-.25 1.53-.6 2.12-1.06.59-.46 1.06-1.02 1.39-1.69.33-.67.5-1.43.5-2.27.01-1.64-.58-2.91-1.74-3.82zm25.52 4.07c.2.5.56.95 1.08 1.36.52.41 1.24.78 2.15 1.11.91.33 2.09.65 3.52.95 2.89.64 5.03 1.54 6.4 2.68 1.37 1.14 2.06 2.71 2.06 4.72 0 1.11-.22 2.13-.66 3.05-.44.92-1.05 1.7-1.82 2.34-.77.64-1.7 1.14-2.79 1.48-1.09.35-2.27.52-3.56.52-2.08 0-3.97-.34-5.69-1.01-1.72-.67-3.36-1.71-4.91-3.11l1.82-2.15c1.36 1.24 2.73 2.16 4.12 2.76 1.38.61 2.98.91 4.78.91 1.75 0 3.16-.41 4.21-1.23 1.05-.82 1.58-1.89 1.58-3.23 0-.62-.1-1.17-.3-1.65-.2-.48-.54-.92-1.04-1.32s-1.18-.75-2.06-1.08c-.88-.32-2-.63-3.36-.93-1.49-.32-2.78-.69-3.88-1.11-1.1-.42-2.01-.93-2.73-1.52-.72-.59-1.25-1.29-1.6-2.08-.35-.79-.52-1.73-.52-2.82 0-1.04.21-1.99.63-2.86s1.01-1.62 1.78-2.25c.77-.63 1.67-1.13 2.71-1.49s2.18-.54 3.41-.54c1.9 0 3.57.26 4.99.79 1.42.52 2.8 1.31 4.13 2.37l-1.71 2.26c-1.21-.99-2.43-1.71-3.65-2.15-1.22-.45-2.5-.67-3.84-.67-.84 0-1.6.11-2.28.33-.68.22-1.26.52-1.74.89s-.85.82-1.11 1.34c-.26.52-.39 1.08-.39 1.67-.03.62.07 1.17.27 1.67z" />
        </svg>
      );
    } else if (userGroup.includes('Darwin')) {
      userInfo.clientName = 'Darwin';
      userInfo.shortName = 'Darwin';
      userInfo.png = '/images/logo/Darwin_logo.png';
      userInfo.flex = '350px';
      userInfo.svg = (
        <svg
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 110 28"
        >
          <path
            d="M23.6449 13.4401C23.6449 4.8054 19.1461 0 11.7232 0H0v11.667l3.38824-2.20994V3.56651h8.14756c6.2232 0 8.3257 4.69276 8.3257 9.87359 0 4.4242-2.4016 8.4651-6.6205 9.551-.7215.1857-.7964.285-1.7052.285H9.80814v3.5665h1.99006c.7579 0 .7489-.0497 1.4428-.1481 6.1018-.8658 10.4039-5.5014 10.4039-13.2544Z"
            fill="#000"
          />
          <path
            d="M9.80814 23.2761h1.72766c.9088 0 .9837-.0993 1.7052-.285v-7.1533l-6.62051-4.5426-3.23225 2.2099v3.9288l3.23225-2.0872 3.18765 2.0872v5.8422Z"
            fill="#000"
          />
          <path
            d="M0 15.8378v11.0048h3.38824V13.5051L0 15.8378ZM39.5386 10.0263c-1.0085-1.50186-2.876-2.89108-5.8642-2.89108-4.8557 0-8.5162 3.82978-8.5162 10.13758s3.6605 10.1 8.5162 10.1c2.9882 0 4.8557-1.4268 5.8642-2.7785h.0747v2.2528h3.6979V7.69842h-3.6979v2.32788h-.0747ZM34.272 24.3315c-3.5857 0-5.3039-3.3041-5.3039-7.0587 0-3.7547 1.7182-7.0588 5.3039-7.0588 3.287 0 5.3413 2.7034 5.3413 7.0588 0 4.3178-2.0543 7.0587-5.3413 7.0587ZM46.5698 7.69842V26.8471h3.6605v-9.9122c0-3.6421 2.3905-6.1577 5.7148-6.1577.4109 0 .7471.0376 1.2326.1127V7.3605c-.5602-.07509-1.0832-.11264-1.7555-.11264-2.5773 0-4.146 1.68959-5.1172 4.09254h-.0747V7.69842h-3.6605ZM57.5314 7.69842 63.433 26.8471h3.7725l4.1087-14.7557h.0747l4.1087 14.7557h3.7726l5.9389-19.14868h-3.8472l-4.034 14.83088h-.1121L73.2565 7.69842h-3.6978L65.5994 22.5293h-.0747l-4.034-14.83088h-3.9593ZM86.4668.301758V4.28169h4.146V.301758h-4.146Zm.2615 7.396662V26.8471h3.6604V7.69842h-3.6604ZM93.6395 26.8471H97.3v-11.752c0-2.8911 2.017-4.806 5.155-4.806 2.726 0 3.847 1.7272 3.847 4.2803v12.2777H110V13.5557c0-4.39297-2.839-6.42048-6.35-6.42048-3.287 0-5.2668 1.50186-6.2753 2.96618H97.3V7.69842h-3.6605V26.8471Z"
            fill="#000"
          />
        </svg>
      );
    } else if (userGroup.includes('Truehold')) {
      userInfo.clientName = 'Truehold';
      userInfo.shortName = 'Truehold';
      // userInfo.png = '/images/logo/Truehold_logo.png';
      userInfo.flex = '350px';
      userInfo.svg = (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 151 28"
        >
          <g clip-path="url(#a)" fill="#144036">
            <path d="M11.5364 24.9793c-.3827.0951-.7752.1444-1.1693.1471-.90604 0-1.55655-.3416-1.95882-1.0296-.40226-.688-.604-1.9047-.604-3.6466v-8.8419h5.01742V9.09825H7.79885V4.49742l-1.02318-.6649-.06583.02553-.08456.03342-3.8052 1.51943v3.68735H0v2.51005h2.82128v9.7244c0 1.6697.15221 2.9888.45663 3.9572.302.9724.8003 1.6653 1.48403 2.0846.68372.4194 1.59697.6254 2.74457.6254 1.07514 0 2.14379-.1675 3.16799-.4965 1.0383-.3307 1.8724-.7427 2.5072-1.2356l-.4373-1.6586c-.394.1504-.7975.2741-1.208.3701ZM25.5124 9.26543c-.661.37757-1.2548.86337-1.7571 1.43737-.5481.6186-1.015 1.3056-1.3892 2.0439V9.35964l-1.0268-.62662-6.1149 2.65108v1.1049l1.2449.4425c.5122.1471.8659.405 1.0612.7737.1953.3687.2929.9826.2929 1.8415v7.9947c0 .6393-.0914 1.1122-.2742 1.4185-.1828.3063-.5428.6074-1.0799.9031l-1.3548.626v1.142h11.1668v-1.142l-1.501-.626c-.7816-.3687-1.3064-.7131-1.5746-1.0332-.2682-.3201-.4025-.75-.4029-1.2897v-8.84c.5372-.6385 1.0681-1.1424 1.5928-1.5115.5246-.3691 1.0312-.5533 1.5196-.5525.6093 0 1.1279.1597 1.5559.479.4281.3192.935.909 1.5209 1.7692H30.2l.5128-4.8269c-.9765-.88374-2.087-1.32579-3.3317-1.3262-.6112 0-1.2341.17808-1.8687.53423ZM53.6449 23.8191c-.6845-.1479-1.1301-.3383-1.3367-.5713-.2065-.233-.3102-.6442-.311-1.2338V9.39489l-1.0268-.66369-6.516 2.6158v1.142l1.2449.4054c.5122.1969.8599.4672 1.0431.8108.1832.3436.2748.9329.2748 1.768v7.2209c-.4467.4969-.9884.8979-1.5928 1.1791-.6221.2946-1.214.4421-1.7757.4425-1.2451 0-2.1543-.3625-2.7277-1.0873-.5734-.7249-.8601-1.8853-.8601-3.4814V9.39489l-1.0618-.66369-6.5172 2.6134v1.142l1.2449.4054c.5122.1969.8601.4672 1.0437.8108.1836.3436.275.9329.2742 1.768v5.1946c0 2.4076.5436 4.2309 1.6308 5.4699 1.0872 1.2391 2.6677 1.8592 4.7414 1.8604 1.0918-.0038 2.1662-.275 3.1305-.7901 1.0191-.5331 1.9121-1.2808 2.6184-2.1922v2.9841h.9126l6.9943-2.7787-.2198-1.1791-1.208-.2206ZM69.7668 9.72441c-1.2326-.66287-2.6788-.99451-4.3385-.99492-1.7335 0-3.2896.42362-4.6684 1.27081-1.371.8394-2.4907 2.0365-3.2404 3.4643-.7816 1.4615-1.1722 3.1378-1.1718 5.0287 0 3.0215.8178 5.361 2.4535 7.0186 1.6356 1.6576 3.8567 2.4864 6.6633 2.4864 1.5861 0 3.0323-.2634 4.3385-.7901 1.3198-.5374 2.5207-1.3324 3.5334-2.3394l-1.1349-1.4367c-.6805.4533-1.4117.8241-2.1786 1.1049-.7364.2525-1.5101.3771-2.288.3683-1.8804 0-3.3502-.5648-4.4092-1.6945-1.059-1.1296-1.5913-2.7754-1.5969-4.9375v-.2948H73.665v-.8472c0-1.7925-.3423-3.3091-1.0268-4.5498-.6574-1.2155-1.6561-2.2092-2.8714-2.85709Zm-7.9269 6.19139c.1711-1.7188.5617-2.9951 1.1718-3.829.61-.8338 1.4399-1.2514 2.4896-1.2526.9761 0 1.745.4421 2.3067 1.3262.5617.8841.8545 2.0996.8782 3.6466l-6.8463.1088ZM95.1753 24.9599c-.1832-.3063-.2747-.7791-.2742-1.4185v-7.4756c0-2.3095-.5436-4.1085-1.6308-5.397-1.0872-1.28847-2.5813-1.93432-4.4823-1.93756-1.0498 0-2.1239.29476-3.2224.8843-1.0984.58956-2.1118 1.43616-3.0399 2.53986V.663078L81.5007 0l-6.554 2.61341v1.14444l1.2448.44185c.5126.1726.8666.4307 1.0619.7743.1953.34359.2929.93292.2929 1.768v16.7994c0 .6394-.0853 1.1122-.2561 1.4185-.1707.3063-.5369.6074-1.0987.9032l-1.3559.626v1.142H85.051v-1.142l-1.3548-.626c-.4638-.2456-.7751-.5221-.9337-.8296-.1587-.3076-.238-.8049-.238-1.4921V14.626c.4826-.5842 1.0813-1.0603 1.7576-1.3979.6846-.344 1.3681-.516 2.0506-.516 1.1714 0 2.0623.3683 2.6727 1.105.6105.7366.9155 1.8174.9151 3.2424v6.4819c0 .6888-.0733 1.1862-.2199 1.4921-.1466.3059-.4639.5824-.9519.8296l-1.3548.626v1.142h10.2155v-1.142l-1.3548-.626c-.5363-.2946-.8961-.5956-1.0793-.9032ZM112.827 9.92851c-1.428-.79821-3.093-1.19731-4.997-1.19731-1.369 0-2.633.23946-3.79.71839-1.145.46931-2.184 1.16451-3.057 2.04451-.894.8983-1.6006 1.9672-2.0785 3.1439-.4778 1.1767-.7173 2.4376-.7044 3.7087 0 1.9413.4027 3.6361 1.208 5.0847.7829 1.4276 1.9579 2.598 3.3849 3.3713 1.453.7986 3.131 1.1977 5.035 1.1973 1.781 0 3.398-.4236 4.851-1.2709 1.425-.8198 2.612-2.001 3.442-3.426.843-1.4368 1.264-3.0763 1.264-4.9187 0-1.94-.403-3.6346-1.208-5.084-.777-1.4205-1.938-2.5899-3.35-3.37189Zm-1.959 13.97869c-.732 1.327-1.745 1.9903-3.038 1.9899-1.319 0-2.35-.6686-3.094-2.0057-.744-1.3371-1.116-3.1853-1.117-5.5447 0-2.3816.373-4.2298 1.117-5.5446.745-1.3149 1.776-1.9717 3.094-1.9704 1.293 0 2.306.663 3.038 1.9892.733 1.3261 1.099 3.1805 1.099 5.5629-.002 2.3582-.368 4.2001-1.1 5.5259l.001-.0025ZM126.254 24.9794c-.183-.2942-.275-.7731-.275-1.4368V.663078L124.954 0 118.4 2.61341v1.14444l1.245.44185c.512.1726.866.42442 1.062.75546.196.33103.293.92664.293 1.78684v16.7994c0 .6637-.092 1.1426-.275 1.4368-.183.2941-.543.5891-1.08.8849l-1.355.626v1.142h10.399v-1.142l-1.355-.626c-.537-.2946-.897-.5892-1.08-.8837ZM150.819 24.0398l-1.282-.2207c-.659-.1215-1.092-.3057-1.3-.5524-.207-.2468-.311-.6643-.311-1.2526V.626004L146.899 0l-6.554 2.57877v1.14261l1.245.44185c.513.1722.867.40559 1.062.70015.195.29457.293.84703.293 1.65739v2.8389c-.464-.20445-.949-.35887-1.446-.46069-.499-.10783-1.009-.16343-1.52-.16592-2.123 0-3.954.41754-5.491 1.25261-1.511.80863-2.748 2.05303-3.552 3.57373-.83 1.5474-1.245 3.414-1.244 5.6 0 1.6207.311 3.0996.933 4.4367.622 1.3371 1.501 2.406 2.636 3.2066 1.135.7986 2.471 1.1977 4.01 1.1973 1.952 0 3.892-1.0192 5.82-3.0577V28h.916L151 25.2188l-.181-1.179Zm-7.874-1.3456c-.419.4655-.906.8632-1.446 1.179-.581.3461-1.246.5246-1.922.516-1.464 0-2.587-.5956-3.369-1.7868-.781-1.1912-1.172-2.8061-1.171-4.8446 0-1.3751.189-2.585.568-3.6296.378-1.0445.915-1.8549 1.611-2.431.659-.5636 1.496-.8707 2.361-.8655.976 0 1.769.3008 2.379.9025.611.6017.94 1.4797.989 2.6341v8.3259Z" />
          </g>
          <defs>
            <clipPath id="a">
              <path fill="#144036" d="M0 0h151v28H0z" />
            </clipPath>
          </defs>
        </svg>
      );
    } else if (userGroup.includes('Fundrise')) {
      userInfo.clientName = 'Fundrise';
      userInfo.shortName = 'Fundrise';
      userInfo.png = '/images/logo/Fundrise_Logo.png';
      userInfo.flex = '350px';
      userInfo.svg = (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 137 27">
          <g fill="none" fill-rule="evenodd">
            <g fill="#22262A">
              <path d="M49 7.15912v11.3652h2.21578538v-4.54679997h5.77433119v-1.98540001h-5.77433119V9.14542h6.50985014v-1.9863zM130.3607601 16.57042001v-2.79900006h5.7411087v-1.95389991h-5.7411087V9.11302h6.4766269v-1.9539h-8.6933347v11.3652h8.7745466v-1.95389999zM67.7410593 13.68763001c0 1.94129997-.9856138 3.01049999-2.7768839 3.01049999-1.7746586 0-2.7916496-1.12679999-2.7916496-3.09060005V7.15993h-2.2167083v6.52770001c0 3.18509997 1.8134187 5.01209999 4.9760579 5.01209999 3.1940162 0 5.0258922-1.86210002 5.0258922-5.10750004V7.15993h-2.2167083v6.52770001ZM80.9211525 14.68824999 74.9705552 7.19845l-.0295315-.0387h-2.0182935v11.3643h2.1844082v-7.75170002l6.129632 7.71390002.0304544.0378h1.837413V7.15975h-2.1834853zM90.6215121 16.53874002h-2.1548767V9.14524h2.1548767c2.2794627 0 3.8723179 1.52639998 3.8723179 3.71250004 0 2.20139994-1.5568637 3.68099998-3.8723179 3.68099998m0-9.37890002H86.25085v11.3643h4.3706621c3.5843858 0 6.1868493-2.3958 6.1868493-5.69789997 0-3.28320003-2.6024635-5.66640003-6.1868493-5.66640003M111.7922759 18.5245h2.2157852V7.1602h-2.2157852zM121.8125907 11.85199c-2.2379343-.52109996-2.6366091-.89189997-2.6366091-1.67400002 0-.74159998.7078329-1.24019998 1.7617377-1.24019998 1.0068401 0 2.0035282.369 3.0463595 1.12950004l.0839804.06029994L125.2640847 8.48419l-.074752-.0594c-1.2246344-.9585-2.6052316-1.4247-4.2183898-1.4247-2.3625206 0-4.01167 1.3788-4.01167 3.35339998 0 2.12670003 1.3667551 2.85210003 3.8898523 3.44699998 2.1438027.48240007 2.5055638.89100005 2.5055638 1.62540008 0 .80189996-.7558222 1.32029995-1.9260075 1.32029995-1.2846207 0-2.3791324-.44009998-3.5474716-1.4274l-.0793667-.06660001-1.3399913 1.55519998.0738285.06480006c1.3612178 1.18619998 3.0362074 1.81169998 4.8440893 1.81169998 2.5461688 0 4.1907045-1.3545 4.1907045-3.44969998 0-1.76850004-1.0880519-2.74859998-3.7578835-3.38220002M106.3398381 14.25408999c1.7211328-.51120001 2.667063-1.73339995 2.667063-3.45329995 0-2.24640004-1.6694524-3.64140004-4.3558958-3.64140004h-5.2049272v11.3652h2.216708v-9.3798h2.8257955c1.4627326 0 2.2693117.6282 2.2693117 1.76760001 0 1.09889997-.8637968 1.78199999-2.2536225 1.78199999h-.4780415v1.93769998l2.7824209 3.85020002.0313775.0423h2.6218433l-3.1220329-4.27050001Z" />
            </g>
            <g fill="#F4633A">
              <path d="M14 24.17V8c0-.55228475.4477153-1 1-1h10c.5522847 0 1 .44771525 1 1v16.18c-1.93-.12-3.94-.18-6-.18s-4.07.06-6 .17ZM16 16h3v5h-3v-5Zm5 0h3v5h-3v-5Zm-5-7h3v5h-3V9Zm5 0h3v5h-3V9ZM28 24.32V1c0-.55228475.4477153-1 1-1h9.99c.5522847 0 1 .44771525 1 1v25.3c-3.2-.91-7.33-1.61-11.99-1.98ZM33 16v5h-3v-5h3Zm5 0v5h-3v-5h3Zm-5-7v5h-3V9h3Zm5 0v5h-3V9h3Zm-5-7v5h-3V2h3Zm5 0v5h-3V2h3ZM0 26.29V15c0-.5522847.44771525-1 1-1h10c.5522847 0 1 .4477153 1 1v9.31c-4.67.37-8.8 1.07-12 1.98ZM2 16h3v5H2v-5Zm5 0h3v5H7v-5Z" />
            </g>
          </g>
        </svg>
      );
    } else if (userGroup.includes('JLL')) {
      userInfo.clientName = 'JLL';
      userInfo.shortName = 'JLL';
      userInfo.flex = '350px';
      userInfo.svg = (
        <svg viewBox="0 0 580.94 250.72">
          <defs />
          <path d="M350.66 50.17H287V56a60.15 60.15 0 0 1 7.59 1.09 12.32 12.32 0 0 1 5.41 2.44c3.81 3.24 3.73 9.31 3.73 20.3v73.79c0 15 1.34 36.28-19.93 36-9.13-.12-18.49-6.62-21.4-18.49h-7.32c3.1 19.89 13.69 33.38 39.37 33.38h.12c21.81 0 41.28-10 41.25-44.08v-80.6c0-11-.08-16.81 3.72-20.06C342 57.7 344 56.67 350.66 56ZM423.74 50.17H360V56c6.66.65 8.67 1.67 11.1 3.75 3.05 2.6 3.6 6.86 3.7 14.1v103.02c-.1 7.25-.65 11.5-3.7 14.1-2.43 2.08-4.44 3.1-11.1 3.75v5.86h96.27l10.15-29.47H459c-7.4 16.19-18.84 18.08-29.28 18.08-8.53 0-14.21-.48-18.24-3.09-3.25-2.08-4.46-7.56-4.52-16.85V79.83c0-11-.08-17.06 3.72-20.3 2.43-2.08 6.43-2.85 13.09-3.5v-5.86Z" />
          <path d="M529.9 50.17h-63.71V56c6.65.65 8.66 1.67 11.09 3.75 3.05 2.6 3.6 6.86 3.7 14.1v103.02c-.1 7.25-.65 11.5-3.7 14.1-2.43 2.08-4.44 3.1-11.09 3.75v5.86h96.26l10.16-29.47h-7.48c-7.4 16.19-18.84 18.08-29.28 18.08-8.53 0-14.21-.48-18.24-3.09-3.25-2.08-4.46-7.56-4.52-16.85V79.83c0-11-.08-17.06 3.73-20.3 2.43-2.08 6.43-2.85 13.08-3.5v-5.86Z" />
          <path
            fill="#e30613"
            d="M102.63 246a56.33 56.33 0 0 0 8.48-4.87c.57-.4 1.16-.77 1.71-1.19a73.33 73.33 0 0 0 9.4-8.47 93.67 93.67 0 0 0 10.28-13.08 125.55 125.55 0 0 0 6.64-11.39 164.11 164.11 0 0 0 15.4-49.09 211.91 211.91 0 0 0 2.44-32.5 212 212 0 0 0-2.44-32.51 164 164 0 0 0-15.4-49.08c-.91-1.78-1.88-3.54-2.87-5.28a149.35 149.35 0 0 0-10.35 23.86 187.63 187.63 0 0 1 9.08 46.77c.37 5.37.51 10.78.52 16.19 0 5.41-.13 10.82-.5 16.19a181.34 181.34 0 0 1-12.81 56.64 141.2 141.2 0 0 1-10.28 20.17c-.93 1.52-1.8 3.07-2.79 4.55-2 3-4.2 5.94-6.52 8.81a107.57 107.57 0 0 1-8.48 9.39 87.52 87.52 0 0 1-10.25 8.63c-.29.21-.56.43-.86.64v.34a50.12 50.12 0 0 0 19.6-4.72Zm0-14.22Z"
          />
          <path
            fill="#e30613"
            d="M141.8 246a56.43 56.43 0 0 0 8.49-4.87c.56-.4 1.15-.77 1.71-1.19 11.36-8.51 19.9-20.42 26.31-33a163.82 163.82 0 0 0 15.4-49.09 211.91 211.91 0 0 0 2.44-32.5 213.28 213.28 0 0 0-2.43-32.51 163.78 163.78 0 0 0-15.41-49.08c-5.92-11.59-13.68-22.61-23.79-30.93a118.59 118.59 0 0 0-9 11q1.45 2 2.82 4c16.16 24 23.91 52.73 25.88 81.36q.56 8.06.55 16.19c0 5.41-.18 10.82-.55 16.19-2 28.63-9.73 57.37-25.88 81.36-2 3-4.2 5.94-6.52 8.81a110.59 110.59 0 0 1-8.47 9.39 88.35 88.35 0 0 1-10.25 8.63l-.86.64v.34A50.12 50.12 0 0 0 141.8 246Z"
          />
          <path
            fill="#e30613"
            d="M191.17 239.92c11.37-8.5 19.9-20.42 26.31-33a163.84 163.84 0 0 0 15.41-49.09 210.63 210.63 0 0 0 2.43-32.5 212 212 0 0 0-2.44-32.51 163.78 163.78 0 0 0-15.41-49.08c-6.41-12.55-14.94-24.47-26.31-33C182.69 4.45 172.06.27 161.4 0v.34c10.38 7.23 19.15 17.13 26.1 27.47 16.16 24 23.91 52.73 25.89 81.36q.54 8.06.55 16.19t-.55 16.19c-2 28.63-9.73 57.37-25.89 81.36-7 10.34-15.72 20.24-26.1 27.47v.34c10.66-.27 21.29-4.45 29.77-10.8ZM141.81 4.73a56.38 56.38 0 0 0-8.48 4.88c-.57.39-1.16.77-1.71 1.19a74 74 0 0 0-9.4 8.46 94.35 94.35 0 0 0-10.28 13.09 125.55 125.55 0 0 0-6.64 11.42 164 164 0 0 0-15.4 49.08 212 212 0 0 0-2.44 32.51 211.91 211.91 0 0 0 2.44 32.5A164.11 164.11 0 0 0 105.3 207c.91 1.78 1.88 3.54 2.87 5.28a148.45 148.45 0 0 0 10.35-23.91 187.63 187.63 0 0 1-9.11-46.77c-.37-5.37-.51-10.78-.52-16.19 0-5.41.13-10.82.5-16.19a181.34 181.34 0 0 1 12.81-56.64 141.84 141.84 0 0 1 10.3-20.23c.93-1.51 1.8-3.06 2.79-4.54 2-3 4.2-5.94 6.52-8.81a107.57 107.57 0 0 1 8.48-9.39A86.71 86.71 0 0 1 160.54 1c.29-.21.56-.44.86-.64V0a50.11 50.11 0 0 0-19.59 4.73Zm0 14.23Z"
          />
          <path
            fill="#e30613"
            d="M102.64 4.73a56.49 56.49 0 0 0-8.49 4.88c-.56.39-1.15.77-1.71 1.19-11.36 8.5-19.9 20.42-26.31 33a163.76 163.76 0 0 0-15.4 49.08 212 212 0 0 0-2.44 32.51 211.89 211.89 0 0 0 2.43 32.5A164.13 164.13 0 0 0 66.13 207c5.92 11.59 13.68 22.61 23.79 30.93a119.93 119.93 0 0 0 9-10.95q-1.45-2-2.82-4c-16.15-24-23.91-52.73-25.88-81.36-.37-5.37-.55-10.78-.55-16.19s.18-10.82.55-16.19c2-28.63 9.73-57.37 25.88-81.36 2-3 4.2-5.94 6.52-8.81a110.59 110.59 0 0 1 8.47-9.39A88.35 88.35 0 0 1 121.36 1c.29-.21.57-.44.86-.64V0a50.11 50.11 0 0 0-19.58 4.73Z"
          />
          <path
            fill="#e30613"
            d="M53.27 10.8C41.9 19.3 33.37 31.22 27 43.77a163.78 163.78 0 0 0-15.45 49.08 210.76 210.76 0 0 0-2.43 32.51 211.91 211.91 0 0 0 2.44 32.5A163.84 163.84 0 0 0 27 207c6.41 12.55 14.94 24.47 26.31 33 8.48 6.35 19.11 10.53 29.77 10.8v-.34C72.67 243.15 63.9 233.25 57 222.91c-16.16-24-23.91-52.73-25.89-81.36-.36-5.37-.54-10.78-.54-16.19s.18-10.82.54-16.19C33 80.54 40.79 51.8 57 27.81 63.9 17.47 72.67 7.57 83.05.34V0c-10.67.27-21.3 4.45-29.78 10.8ZM129.09 237.88a116.84 116.84 0 0 0 9-11q-1.46-2-2.82-4c-1-1.48-1.86-3-2.79-4.54a94.35 94.35 0 0 1-10.28 13.09 77.2 77.2 0 0 0 6.87 6.42M150.29 241.11a56 56 0 0 1-8.48 4.88 50.11 50.11 0 0 0 19.58 4.73v-.34a87.27 87.27 0 0 1-11.1-9.27M118.52 250.46c1.06-.11 2.12-.29 3.17-.47-1.07-.76-2.13-1.54-3.17-2.36a91.4 91.4 0 0 1-7.41-6.52 56.38 56.38 0 0 1-8.48 4.88 53 53 0 0 0 12.72 4c1.06.18 2.11.36 3.17.47m3.7.26v-.34c-.18-.13-.35-.27-.53-.39-1 .18-2.11.36-3.17.47 1.23.13 2.47.23 3.7.26ZM115.35 12.84a118.43 118.43 0 0 0-9 11q1.45 2 2.82 4c1 1.48 1.86 3 2.8 4.54a94.28 94.28 0 0 1 10.27-13.09 75.85 75.85 0 0 0-6.87-6.42"
          />
          <path
            fill="#e30613"
            d="M94.16 9.61a55.89 55.89 0 0 1 8.48-4.88A50.2 50.2 0 0 0 83.05 0v.34a87.36 87.36 0 0 1 11.11 9.27M141.81 4.73a53 53 0 0 0-12.72-4C128 .55 127 .37 125.92.26c-1.23-.13-2.46-.23-3.7-.26v.34c.18.13.35.27.53.39 1.07.76 2.13 1.54 3.17 2.35a91.53 91.53 0 0 1 7.41 6.53 56.38 56.38 0 0 1 8.48-4.88"
          />
        </svg>
      );
    } else if (userGroup.includes('Sunroom')) {
      userInfo.clientName = 'Sunroom';
      userInfo.shortName = 'Sunroom';
      userInfo.flex = '450px';
      userInfo.png = '/images/logo/SRlogo.png';
      userInfo.svg = (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 281 70">
          <path
            fill="#FFCE06"
            d="m34.956 3.181 6.725 6.725 9.185-2.461 2.461 9.186 9.186 2.461-2.461 9.185L66.776 35l-6.725 6.725 2.461 9.186-9.186 2.461-2.461 9.186-9.186-2.461-6.724 6.723-6.723-6.724-9.187 2.462-2.461-9.186-9.186-2.461 2.461-9.187L3.136 35l6.724-6.724-2.461-9.186 9.186-2.461 2.461-9.186 9.185 2.461 6.725-6.723m0-3c-.796 0-1.559.316-2.121.879l-5.5 5.5-7.512-2.013a3.012 3.012 0 0 0-2.276.3 3 3 0 0 0-1.398 1.822l-2.013 7.512-7.513 2.013a3 3 0 0 0-2.122 3.674l2.013 7.513-5.499 5.5a3 3 0 0 0 0 4.241l5.5 5.499-2.013 7.514a2.998 2.998 0 0 0 2.122 3.673l7.513 2.013 2.013 7.513a2.996 2.996 0 0 0 3.674 2.12l7.514-2.014 5.498 5.499c.586.586 1.354.879 2.122.879s1.535-.293 2.121-.879l5.499-5.498 7.513 2.013a2.99 2.99 0 0 0 3.674-2.121l2.013-7.513 7.513-2.013a2.996 2.996 0 0 0 2.122-3.673L63.4 42.621l5.5-5.5a3 3 0 0 0 0-4.243l-5.5-5.5 2.013-7.512a3 3 0 0 0-2.122-3.674l-7.513-2.013-2.013-7.512a3 3 0 0 0-3.674-2.122l-7.512 2.013-5.5-5.5a3.013 3.013 0 0 0-2.123-.877z"
          />
          <path
            fill="#FFCE06"
            d="M35.456 52.001a13.49 13.49 0 0 1-13.06-10.125 1.5 1.5 0 0 1 2.906-.75c1.196 4.637 5.372 7.875 10.154 7.875s8.958-3.238 10.154-7.875a1.5 1.5 0 1 1 2.906.75 13.49 13.49 0 0 1-13.06 10.125z"
          />
          <path
            fill="#222"
            d="M97.562 32.112c-2.1-.712-4.688-1.538-4.688-3.601 0-1.988 1.726-3.076 3.901-3.076 2.025 0 4.126 1.05 5.477 2.25l2.738-3.338c-2.55-1.913-5.213-2.926-8.477-2.926-4.313 0-8.29 2.326-8.29 7.314 0 4.538 3.713 6.264 7.239 7.539 2.738.976 5.401 1.913 5.401 4.426 0 2.738-2.325 3.863-4.651 3.863-2.701 0-4.426-1.013-6.714-3.226l-3.151 3.038c2.851 2.813 5.852 4.201 9.827 4.201 4.351 0 9.602-2.363 9.602-8.14 0-5.286-4.351-7.011-8.214-8.324zm29.364 4.463c0 5.326-2.213 7.914-5.777 7.914-3.525 0-5.738-2.588-5.738-7.914V21.872h-4.651v13.915c0 8.252 3.113 12.791 10.315 12.791 7.389 0 10.502-4.539 10.502-12.791V21.872h-4.65v14.703zm28.05 3.076-13.053-17.779h-4.089v26.256h4.651V30.387l12.978 17.741h4.163V21.872h-4.65v17.779zM184 29.599c0-5.588-5.176-7.727-9.339-7.727h-8.103v26.256h4.651V37.4h1.65l7.502 10.728h5.775l-8.139-11.178c3.077-.862 6.003-3.113 6.003-7.351zm-12.79 3.826v-7.577h3.338c2.701 0 4.539 1.538 4.539 3.863 0 2.625-2.251 3.713-5.289 3.713h-2.588zm31.239-12.003c-7.727 0-13.615 6.077-13.615 13.578 0 7.502 5.889 13.578 13.615 13.578 7.764 0 13.652-6.076 13.652-13.578.001-7.501-5.888-13.578-13.652-13.578zm0 23.067c-5.026 0-8.702-4.163-8.702-9.489s3.676-9.49 8.702-9.49c5.063 0 8.739 4.164 8.739 9.49s-3.675 9.489-8.739 9.489zm30.938-23.067c-7.728 0-13.616 6.077-13.616 13.578 0 7.502 5.889 13.578 13.616 13.578 7.764 0 13.652-6.076 13.652-13.578 0-7.501-5.889-13.578-13.652-13.578zm0 23.067c-5.026 0-8.702-4.163-8.702-9.489s3.676-9.49 8.702-9.49c5.063 0 8.739 4.164 8.739 9.49s-3.676 9.489-8.739 9.489zm40.539-22.617-7.614 19.242-7.427-19.242h-7.014v26.256h4.65v-20.48l8.102 20.479h3.301l8.29-20.404v20.404h4.65V21.872h-6.938z"
          />
        </svg>
      );
    } else if (userGroup.includes('DRHorton')) {
      userInfo.clientName = 'DRHorton';
      userInfo.shortName = 'DRHorton';
      userInfo.flex = '450px';
      userInfo.png = '/images/logo/DRHorton_logo.png';
      userInfo.svg = (
        <svg
          viewBox="0 0 355 207"
          xmlns="http://www.w3.org/2000/svg"
          fillRule="evenodd"
          clipRule="evenodd"
          strokeLinejoin="round"
          strokeMiterlimit="1.41421"
        >
          <g fill="#00205b">
            <g fillRule="nonzero">
              <path d="M24 66.6c1.4.3 1.2 1.3 1.2 1.3s-.1 35.4 0 40.3c-.7 4.7-8.2 4.3-8.2 4.3V66.6h7Zm-6-2.3H1.7s-1.6.3-1.4 1.2c.2.9.9 1.2 2.5 1.2v45.2l-.7.7s-1.7 0-2.1 1c-.4 1 .9 1.3 1.9 1.3h23.2s14.1-.6 14.1-9.9V73.4s-1.9-8.8-11.2-9.1H18ZM48.4 92.1c2.1 0 3.8-1.6 3.8-3.6s-1.7-3.6-3.8-3.6c-2.1 0-3.8 1.6-3.8 3.6s1.7 3.6 3.8 3.6ZM101.8 92.1c2.1 0 3.8-1.6 3.8-3.6s-1.7-3.6-3.8-3.6c-2.1 0-3.8 1.6-3.8 3.6.1 2 1.8 3.6 3.8 3.6ZM84.4 64.5c2.6 0 7.3 4.4 7.7 7.9.3 2.3.4 5.2.4 7.2 0 7.6-7.1 9.4-7.1 9.4 1.2.4 3.9.5 6.3 2 2.4 1.4 3 4.7 3 4.7V112l.7.6h2.4c.4 0 .6-.******* 1.4-1.2 1.3-1.2 1.3H82.5c-1.6 0-2.8-2.8-2.8-2.8V92.8c0-1.5-2.9-1.7-2.9-1.7H72v20.8l.4.5h1.8c.3 0 .9-.3.9 1 .1 1.5-.6 1.1-.6 1.1H55.6c-.5 0-.9-.1-.9-.9 0-.7.9-1.2.9-1.2h1.3l1-1V67.2l-.7-.6h-1.5c-1.1 0-.6-.4-.6-1.4-.1-.9.9-.7.9-.7h28.4Zm-9 2.1h-3.3v21.8h6.1s1.5-.1 1.5-2V68s-.4-1.5-2-1.5c-1.5.1-2.3.1-2.3.1ZM224.9 64.5c2.6 0 7.3 4.4 7.7 7.9.3 2.3.4 5.2.4 7.2 0 7.6-7 9.4-7 9.4 1.2.4 3.9.5 6.3 2 2.4 1.4 3 4.7 3 4.7V112l.7.6h2.4c.4 0 .6-.******* 1.4-1.2 1.3-1.2 1.3h-14.7c-1.6 0-2.8-2.8-2.8-2.8V92.8c0-1.5-3-1.7-3-1.7h-4.8v20.8l.4.5h1.8c.3 0 .9-.3.9 1 .1 1.5-.6 1.1-.6 1.1h-18.9c-.5 0-.9-.1-.9-.9 0-.7.9-1.2.9-1.2h1.3l1-1V67.2l-.7-.6h-1.5c-1.1 0-.6-.4-.7-1.4-.1-.9.9-.7.9-.7h28.4Zm-9 2.1h-3.3v21.8h6.1s1.5-.1 1.5-2V68s-.4-1.5-2-1.5c-1.6.1-2.3.1-2.3.1ZM130.5 91.3h3.2l.7.6s-.1 21.3 0 22.1c.1.8.7.9.7.9h15.5c.9 0 1.3-.6 1.3-.9 0-.3-.1-1.4-1.7-1.4-1.6 0-1.4-1.1-1.4-1.1s-.1-43.5 0-44.2c.1-.7 0-.8 1.3-.8s1.5-.3 1.5-1-1-1.2-1-1.2h-15.7c-.5 0-.6.9-.6.9v21.2l-.7.7h-7.2l-.7-.7V65.2s-.1-.9-.6-.9h-15.7s-1 .5-1 1.2.2 1 1.5 1 1.2.1 1.3.8c.1.7 0 44.2 0 44.2s.1 1.1-1.4 1.1-1.7 1.1-1.7 1.4c0 .3.4.9 1.3.9h15.5s.6-.1.7-.9c.1-.8 0-22.1 0-22.1l.7-.6h4.2M173.6 115.1c-8.8 0-11.4-1.3-14.5-4.2-3-2.9-3.5-8.5-3.7-10.9-.2-2.3-.2-3.9-.2-12.1 0-6.5.5-9.8 1.6-14.3 1.4-5.9 3.9-7 7-8.4 3-1.4 10.1-1.5 10.1-1.5l1-.1s6.4.2 9.4 1.6c3.1 1.4 5.6 2.6 7 8.4 1.1 4.5 1.6 7.8 1.6 14.3 0 8.2 0 9.7-.2 12.1-.2 2.3-.6 7.9-3.7 10.9-3 2.9-5.4 4.2-14.2 4.2h-1.2Zm.6-1.5c5.3 0 4.5-2.8 4.5-2.8V69.1c0-3.8-3.1-4-4.3-4h-.7c-1.1 0-4.6.2-4.6 4v41.7s-.9 2.8 4.4 2.8h.7ZM285.4 115.1c-8.8 0-11.4-1.3-14.5-4.2-3-2.9-3.5-8.5-3.7-10.9-.2-2.3-.2-3.9-.2-12.1 0-6.5.5-9.8 1.6-14.3 1.4-5.9 3.9-7 7-8.4 3-1.4 10.1-1.5 10.1-1.5l1-.1s6.4.2 9.4 1.6c3.1 1.4 5.6 2.6 7 8.4 1.1 4.5 1.6 7.8 1.6 14.3 0 8.2 0 9.7-.2 12.1-.2 2.3-.7 7.9-3.7 10.9-3 2.9-5.4 4.2-14.3 4.2h-1.1Zm.7-1.5c5.2 0 4.5-2.8 4.5-2.8V69.1c0-3.8-3.2-4-4.3-4h-.7c-1.1 0-4.6.2-4.6 4v41.7s-.9 2.8 4.4 2.8h.7Z" />
              <path d="M251.6 64.4h17.3s.7.6.7 1.1v5.3s-.1 1.2-1.1 1.2c-1 .1-1.2-.4-1.2-1.4-.1-1.1 0-1.6 0-1.6h-8.2s-1-.1-1 .8v41.8s-.1 1 1.7 1c1 0 1 .4 1 1.1 0 .7-.3 1.1-1.1 1.1H241.9c-.8 0-1.1-.4-1.1-1.1 0-.7-.1-1.1.9-1.1 1.8 0 1.7-1 1.7-1V69.8c0-.9-1-.8-1-.8h-8.2s.1.6 0 1.6c-.1 1.1-.3 1.6-1.2 1.4-1-.1-1.1-1.2-1.1-1.2v-5.3c0-.5.7-1.1.7-1.1h19ZM329.3 114.9h11.4V67.2s-.3-.7 1.2-.7 1.5-.9 1.5-1.3-.6-1-1.3-1h-6.5s-1 0-1 1.1c0 .9.3 1.3.8 1.3s1.4-.1 1.5.7c.1 1.1 0 29.1 0 29.1L324.8 67s.3-.5 1.2-.5c.9 0 1.4-.5 1.4-1.3s-.5-1-.9-1h-18.4s-1 .4-1 1.1c0 .7 0 1.1.8 1.1s1.7.2 1.7.6v44.9s.3.7-1.2.7-1.5.9-1.5 1.3.6 1 1.3 1h6.5s1 0 1-1.1c0-.9-.3-1.3-.8-1.3s-1.4.1-1.5-.7c-.1-1 0-36.8 0-36.8l15.9 39.9Z" />
            </g>
            <path
              d="M350.7 72.6c-2 0-3.8-1.4-3.8-3.8s1.8-3.8 3.8-3.8c2 0 3.8 1.4 3.8 3.8s-1.8 3.8-3.8 3.8Zm0-6.8c-1.5 0-2.7 1.2-2.7 2.9 0 1.8 1.2 2.9 2.7 2.9 1.5 0 2.7-1.2 2.7-2.9s-1.2-2.9-2.7-2.9Zm-.7 5h-.9v-4.1h1.5c1.1 0 1.7.3 1.7 1.2 0 .8-.4 1-1 1.1l1 1.8h-1l-.9-1.7h-.4v1.7Zm.8-2.4c.4 0 .6-.1.6-.5s-.5-.5-.8-.5h-.6v1h.8Z"
              fill-rule="nonzero"
            />
            <path d="M0 49.1h344.6v3.5H0z" />
            <path
              d="M47.2 154.8c-.2 0-.3.2-.4.5-.3.8-.8 1.6-1.5 2.5-.7.8-1.4 1.6-2.2 2.3-.8.7-1.6 1.2-2.4 1.6-.2-1.2-.3-2.4-.3-3.7.1-3.1.4-6.5.9-10.2l.1-1v-.2l2.8-.3 2.9-.3h.5c.7-.1 1-.3.8-.7-.1-.3-.5-.5-1-.6-.7-.1-2.5-.1-5.4 0h-.3l.1-.2.6-3.6c.3-1.8.8-4 1.4-6.8.2-.9.2-1.6 0-2.1-.3-.7-.8-1.3-1.6-1.9-.8-.6-1.4-.9-2-.9-.3 0-.6.2-1.1.5l-.5.4c-.6.5-1.5 1.1-2.5 2-1.1.9-2.3 1.9-3.6 3.1l-5.3 4.6-6 5.4c-.1 0-.2.1-.4.3h-.3c-1.4.1-3 .2-4.5.2h-3.2c-.6-.1-1.2 0-1.9-.3.5.9.9 1.6 1.2 2.1.3.5.6.7.9 1 .3.3.7.5 1.2.7.4.2.9.2 1.3.2.2 0 .5 0 1.1-.1l1-.1h.2l-.1.1c-.7.8-1.5 1.6-2.3 2.3-.3.3-.7.7-1.3 1.2-.5.5-1.3 1.2-2.1 2.1-.6.6-1.2 1.2-1.7 1.6-.5.5-.9.8-1.1 1.1-5.3 5-7.2 3.9-8.5 5.9-.4.7.4 2.1 1.4 2.9 1 .8 2.7 1.1 3.5 1.1.6 0 1.2-.2 1.9-.6.5-.4 1.3-1.1 2.2-2.1 1.1-1.2 7.8-9.1 5.3-6.2-.7.9.8-.7 4.1-5l2.1-2.6 2.1-2.6 3.8-.4 3.8-.4 2.7-.3 2.7-.3-.5 3.3-.5 3.3c-.5 3.2-.6 5.5-.3 7.1.1.6.2 1.1.4 1.4.3.7.8 1.4 1.4 2.1.7.8 1.4 1.4 2.1 1.9.8.5 1.4.7 1.9.7s.9-.3 1.4-.7c2.5-2.8 4.3-6 5.4-9.7.1-.5.2-.8.1-1 0-.5-.2-.6-.5-.6ZM39 135.5c-.3 1.3-.6 2.6-.9 4l-.5 2.6-.6 2.5-3.6.1-3.6.1-2.2.1-2.2.1.1-.2c2.2-2.6 5.2-5.5 8.9-8.9 1-.9 1.9-1.7 2.7-2.3.8-.6 1.8-1.2 2.9-1.9-.4 1.3-.7 2.5-1 3.8ZM115.2 149.9c.2-.6.7-1.3 1.5-2.4l1.2-1.5c.3-.5.4-.9.3-1.3-.2-.5-.5-.9-1-1.3-.5-.3-1-.5-1.4-.5-.5 0-1.1.2-1.7.6-.6.4-1.7 1.3-3.1 2.6l-3.2 2.8c-.5.4-1 .9-1.6 1.5l-2.3 2.3c.6-1.5 1.3-2.7 2-3.6l2-2.8c.4-.5.4-1 .2-1.5s-.5-1-1-1.3c-.4-.3-.9-.5-1.4-.5-.3 0-.5 0-.6.1-.1.1-.3.3-.6.8l-.4.8c-.8 1.2-2.3 3.2-5.7 5.8-.9.7-5 3.9-7.1 4.8-2.7 1.1-4.1 1.1-5.7.9-.6-.1-1.4-.6-1.7-1-.4-.5-.5-.9-.2-1.7.3-.8.8-1.9 1.2-1.8 1.2.4 2.3 1 4.4 1.1 1.9.1 2.7-.3 3.7-.9 1-.5 2.1-1.4 2.9-2.5.9-1.3 1.4-2.4 1.1-3.6-.1-.4-.3-.9-.7-1.3-.5-.4-1-.8-1.6-1-.7-.2-1.4-.2-1.8-.2-.9 0-1.8.1-3.4.8-1.6.7-3 1.5-4.3 2.5-1.6 1.2-2.8 2.5-3.6 3.9-.3 0-.7.1-1.1.2-2.4.4-4.4 1.2-6.1 2.4.9-2.4 2.1-4.7 3.8-7.1.3-.4.3-.8.1-1.2-.2-.5-.5-.9-1-1.3-.4-.3-.9-.5-1.4-.5-.3 0-.7.2-1.2.5-.8.6-2.1 1.6-3.8 3.1l-3.3 2.8c-1.2 1.1-2.3 2.2-3.3 3.3.2-.5.5-1.2.8-1.9.2-.5.4-.9.6-1.2.2-.3.4-.7.6-1 .2-.4.8-1.3 1.6-2.6.4-.6.5-1.1.4-1.5-.2-.4-.5-.8-.9-1.1-.5-.3-.9-.5-1.4-.5-.4 0-.9.2-1.5.6-1.3.9-3.1 2.4-5.4 4.4-1.8 1.5-3.6 3.2-5.3 5 .8-1.7 1.5-2.9 2.2-4.1l.5-1c.5-1.1.4-1.3.7-2.3.1-.2.3-1.2.1-2-.1-.4-.3-.8-.7-1.2-.2-.1-.4-.3-.8-.4-.3-.1-1-.3-1.1.2-.1.3 2.1.3.4 2.8l-2.4 3c-1.3 1.7-2.4 3.6-3.4 5.8-.8 1.8-1 3.1-.7 3.8.2.4.6.9 1.1 1.3.6.4 1.1.6 1.5.6.3 0 .5 0 .6-.1.1-.1.3-.4.5-.8 1.1-2.2 2.4-4.2 3.9-6 1.5-1.7 3.1-3 4.6-3.7.4-.2.6-.2.7-.2.1 0 .2 0 .3.1 0 .1 0 .2-.1.4l-.2.3-.2.3c-1.5 2.8-2.3 5.1-2.5 6.8 0 .4 0 .7.1.9.2.4.6.9 1.3 1.4.6.4 1 .6 1.5.6.3 0 .6-.2.8-.6.7-2 1.9-3.9 3.7-6 1.8-2 3.4-3.4 4.9-4 .5-.2.7-.3.9-.3.2 0 .3.1.3.2s0 .2-.1.3l-.2.3c-.4.7-.9 1.7-1.4 3-.5 1.3-.9 2.3-1 3-.2.9-.2 1.6 0 2.1.2.4.6.9 1.2 1.3s1.2.6 1.7.6c.2 0 .4 0 .5-.1.1-.1.2-.3.3-.5 0-.1.1-.2.2-.4s.2-.4.3-.7c.8-1.8 1.6-3 2.5-3.8.8-.7 1.8-1.3 2.9-1.7.5-.2 1.1-.4 1.6-.5-.6 1.5-.8 3-.3 4 .4.9 1 2.1 2.7 2.9 1.2.5 3.5.5 5.4.1 2.8-.6 4.4-2 5.6-3 1.3-1 3.8-3.3 4.7-4.1.8-.6 1.9-1.9 3.1-2.8-1.4 2.3-2.5 4.6-3.5 7.1-.2.6-.3 1.1-.1 1.5.2.4.6.8 1.2 1.3s1.2.7 1.6.7c.4 0 .8-.4 1.3-1.3 1.4-2.8 3.1-5.1 5-6.9 1.3-1.2 2.9-2.2 4.8-3-.5.9-.6 1.5-.5 1.8.2.5.7.9 1.4 1.3.8.4 1.6.7 2.6.8-.2-.2-.3-.5-.4-.7-.4-.3-.4-.8-.2-1.4Zm-25-2.7c1.5-.7 2.6-1.1 3.4-1.1.3 0 .5.1.6.3.1.3 0 .9-.5 1.7-.5.8-1.3 2.4-3.3 3.1-1.8.6-4.3.3-5-.3 1-.9 3.3-2.9 4.8-3.7ZM343.5 144.4c-.2-.5-.5-.9-1-1.3-.5-.3-1-.5-1.4-.5-.5 0-1.1.2-1.7.6-.6.4-1.7 1.3-3.1 2.6l-3.2 2.8c-.5.4-1 .9-1.6 1.5l-2.3 2.3c.6-1.5 1.3-2.7 2-3.6l2-2.8c.4-.5.4-1 .2-1.5s-.6-1-1-1.3c-.4-.3-.9-.5-1.4-.5-.3 0-.5 0-.6.1-.1.1-.3.3-.6.8l-.4.8c-.8 1.2-2.3 3.2-5.7 5.8-.9.7-5 3.9-7.1 4.8-2.7 1.1-4.1 1.1-5.7.9-.6-.1-1.4-.6-1.7-1-.4-.5-.5-.9-.2-1.7.3-.8.8-1.9 1.2-1.8 1.2.4 2.3 1 4.4 1.1 1.9.1 2.7-.3 3.7-.9 1-.5 2.1-1.4 2.9-2.5.9-1.3 1.4-2.4 1.1-3.6-.1-.4-.3-.9-.7-1.3-.5-.4-1-.8-1.6-1-.7-.2-1.4-.2-1.8-.2-.9 0-1.8.2-3.4.8-1.6.7-3 1.5-4.3 2.5-1.5 1.1-2.7 2.4-3.5 3.7-.3 0-.7.1-1.2.2-.9.1-1.6.3-2.1.4-1.6.4-3.2 1.1-4.9 2 .3-1 .7-1.8 1-2.5.4-.7.9-1.6 1.6-2.6 1.8-2.7 5.9-6.1 7.7-8l3.9-4.4c.5-.5 1.1-1.3.8-1.8-.4-.7-1-.8-1.5-.8-.4 0-1.2.6-1.7 1.1-1.9 1.6-4.2 3.7-4.2 3.7-1.6 1.6-6.3 5.8-7.3 7.2l-.1.1c-.3-.6-.8-1.1-1.4-1.4-.6-.3-1.3-.5-2.1-.5-.5 0-1.1.1-1.8.3-1.5.5-3 1.3-4.6 2.3-1.2.7-2.3 1.5-3.4 2.4-2 .9-4 1.9-4.2 2.1-1.4.7-4 2-5.7 2.8.5-1.6 1.4-3.3 2.6-5.1.5-.8 1.1-1.7 1.8-2.5.7-.9 1.3-1.7 2-2.5.6-.8 1.2-1.4 1.7-1.9l2.7-2.8c.5-.5 1.2-1.1 1-1.6-.1-.2-.2-.3-.3-.5-.4-.4-.7-.4-1.1-.4-.4 0-.8.3-1.3.7-.7.6-3.2 2.4-9.1 9.2-.4.5-.8 1-1.1 1.5-1.3 1-3.1 2.4-5.2 3.7-2.7 1.8-5.1 3.5-5 3.3.4-1.5 1.9-4.3 2.6-5.5l.2-.3c.8-1.1 1.2-1.7 1.3-1.9.3-.4.5-.7.5-.9 0-.2 0-.4-.1-.8-.2-.4-.5-.8-.8-1.2-.4-.3-.8-.5-1.1-.5-.3 0-.7.2-1 .6l-2 3.1c-1.3.9-3.4 2.5-5.5 4-2.7 1.9-3.4 2.5-5 3.3 0 0-.7.6.1-.9.1-.2 1.3-2.7 2.2-4 .1-.2.3-.4.4-.6.1-.2.3-.5.5-.7.2-.3.3-.5.4-.6.6-1 1-1.5 1-1.5.1-.3.1-.6 0-.8-.1-.4-.5-.8-1.1-1.2-.6-.4-1.1-.6-1.6-.6-.4 0-.7.2-1 .7-.9 1.3-1.5 2.3-2 3-.5.7-1.1 1.5-1.8 2.3-.7.8-1.2 1.4-1.6 1.8-.4.4-1 1-1.9 1.8-.5.4-1.1.9-1.8 1.5s-1.3 1-1.8 1.2c-.2.1-.4.2-.6.2-.2 0-.4-.1-.4-.3-.1-.3.1-1.1.6-2.3.6-1.2 1.3-2.5 2.3-3.9.5-.7.9-1.4 1.3-2 .4-.6.6-1 .7-1.2.1-.2.1-.4.1-.5 0-.1 0-.3-.1-.5-.2-.5-.5-.9-1-1.2-.5-.4-.9-.5-1.3-.5s-.8.2-1.2.7l-2.4 4.2c-.8 1.1-1.4 2.2-1.9 3.3-.8-.4-1.9-.8-3.4-1.1 0-.4.8-2.3-1.3-4.5-3.4-3.5-9.4-2.9-9.4-2.9 10.9-5 3.9-11-2.7-11.2-12.3-.3-19.9 9-19.9 9 20-13.8 22.8-4.1 22.8-4.1.6 1.6-.8 4.2-4.1 5.5-1.5.6-2 .7-3.4 1.1-.8.2-2.6.6-3 .7l-3.6.6h-.2l.1-.1c.2-.2.4-.5.6-.9.3-.4.5-.7.6-.9 1-1.4 1.7-2.3 2-2.6.4-.5.5-1 .4-1.3-.1-.4-.7-.9-1.8-1.4-.5-.3-.9-.4-1.2-.4-.4 0-.8.3-1.3.8-1.4 1.7-2.6 3.3-3.7 5-1 1.6-2 3.2-2.9 4.8-.9 1.6-1.7 3.1-2.3 4.5l-.1.2c-2.5.6-4.4 1.3-5.9 2.2-1.6 1-2.2 2.2-1.6 3.6.4 1 1.1 2.1 2.1 3.2s2.1 2 3.2 2.7c1.5.8 2.9 1.3 4.4 1.3 4.1 0 9.1-.9 15-2.8 2.9-.9 5.3-1.8 7.3-2.7 1.9-.9 3.7-1.9 5.2-3.1 1.1-.8 2-1.7 2.9-2.7.9-1 1.4-1.8 1.5-2.4h.2c1.2.2 2.2.4 2.8.6h.1c-.2.4-.3.7-.5 1-.5 1.2-.8 2-.9 2.5-.1.5-.1.9.1 1.3.3.6.8 1.2 1.5 1.8.7.6 1.4.9 2 .9.4 0 .9-.2 1.4-.6 1.5-1.2 3.1-2.8 4.8-4.7l2.3-2.5c.1-.1.3-.3.7-.6-.7 1.5-1.3 2.9-1.6 4.1-.4 1.2-.4 2-.3 2.4 0 0 .7.9 1.4 1.4.7.5 1.2.7 1.6.7.2 0 .4-.1.5-.2.1-.1.6-.4.9-.9 1-1.3 1.8-2.2 2.1-2.6.8-1 1.1-1.4 1.9-2.1 2.1-2.2 3.8-3.3 5.3-4.4-.8 1.4-1.3 2.4-1.7 3.2-.5 1-.8 2.2-1.1 3.5-.1.7-.1 1.2 0 1.5.2.4.6.8 1.4 1.3.7.4 1.2.7 1.6.7.2 0 .4-.1.5-.2.1-.1.5-.4.9-.9 1-1.3 1.8-2.2 2.1-2.6.8-1 1.1-1.4 1.9-2.2 2-2.1 3.6-3.2 5.1-4.2-.2.5-.5 1-.7 1.4-.9 1.7-1.5 3.4-1.9 5.1-.2.6-.1 1.1 0 1.5.2.4.6.9 1.4 1.3.7.4 1.2.7 1.6.7.2 0 .4-.1.5-.2.1-.1.4-.3.7-.8 1-1.3 1.6-2.2 1.9-2.6.8-.9 1.9-1.9 2.7-2.6.4-.3 1.4-1 2.7-1.8-1.3 1.7-1.7 3.2-1.2 4.5.4.9.9 1.7 1.7 2.4.8.7 1.6 1.1 2.2 1.1.4 0 .7-.1 1.1-.3.4-.2 1-.6 1.8-1.2 2.5-1.8 5-4.3 7.7-7.4l.2-.2-.1.2c-1 1.7-1.6 3.5-2 5.4-.1.7-.1 1.2 0 1.5.2.4.6.9 1.4 1.3.7.4 1.2.7 1.6.7.2 0 .4-.1.5-.2.1-.1.3-.4.6-.9.9-1.6 1.6-2.6 2.1-3.3.5-.6 1.2-1.2 2-1.8 1.2-.8 2.7-1.4 4.5-1.8-.7 1.6-.9 3.1-.4 4.2.4.9 1 2.1 2.7 2.9 1.2.5 3.5.5 5.4.1 2.8-.6 4.4-2 5.6-3 1.3-1 3.8-3.3 4.7-4.1.8-.6 1.9-1.9 3.1-2.8-1.4 2.3-2.5 4.6-3.5 7.1-.2.6-.3 1.1-.1 1.5.2.4.6.8 1.2 1.3s1.2.7 1.6.7c.4 0 .8-.4 1.3-1.3 1.4-2.8 3.1-5.1 5-6.9 1.3-1.2 2.9-2.2 4.8-3.1-.5.9-.6 1.5-.5 1.8.2.5.7.9 1.4 1.3.8.4 1.6.7 2.6.8-.2-.2-.3-.5-.4-.7-.2-.5-.2-1 0-1.6.2-.6.7-1.3 1.5-2.4l1.2-1.5c.3-.4.4-.8.3-1.2Zm-28 2.4c1.5-.7 2.6-1.1 3.5-1.1.3 0 .5.1.6.3.1.3 0 .9-.5 1.7-.5.8-1.3 2.4-3.3 3.1-1.8.6-4.3.3-5-.3 1-1 3.3-3 4.7-3.7Zm-96.1 10.5c-2.7 1-5.8 1.9-9.1 2.6-3.3.7-6 1.1-7.9 1.1-2.9 0-4.5-.6-4.9-1.7-.2-.6-.1-1.3.5-2s1.4-1.4 2.6-1.9v.2c-.2 1-.2 2 .2 2.8.3.7.7 1.3 1.4 1.7l.3-.5 2.8-5.2.1-.2.1-.2c2.1-.8 4.4-1.4 7.1-2 2.6-.6 5.4-1 8.3-1.3 2.9-.3 5.9-.5 8.8-.5h1.4c-2.4 2.8-6.3 5.1-11.7 7.1Zm12.4-8.4h-.3c-1.8-.1-3.6-.2-5.6-.2-2.2 0-4.4.1-6.5.2-2.1.1-4.2.3-6.1.6-2 .3-3.8.6-5.5 1l-.3.1-.3.1.1-.2.7-1.2c.2-.3.5-.8.9-1.4l.9-1.4.3-.1.5-.2c1-.3 2.3-.7 4.1-1 1.8-.4 3.5-.6 5.2-.8 1.7-.2 3.1-.3 4.4-.3 2.1 0 3.8.2 5.2.7 1.4.5 2.2 1.1 2.5 1.9.2.6.2 1.4-.2 2.2Zm64.4-2.3c-.2.6-.8 1.5-2 2.7-1.9 1.9-4.1 3.4-6.6 4.5-1.2.5-2 .8-2.5.8-.3 0-.5-.1-.5-.3 0-.1 0-.3.1-.6.5-1.1 1.4-2.2 2.7-3.4 1.3-1.2 2.7-2.1 4.3-2.9.6-.3 1.3-.6 2.2-.9.9-.3 1.5-.4 1.8-.4.3 0 .4.1.5.2.1.1.1.2 0 .3ZM167.3 150.2c-.4 0-1.2.1-2.2.3-2.3.4-4.2 1-5.7 1.8.4-1 .8-1.7 1-2l1.1-1.9c.2-.4.3-.7.1-1-.1-.2-.3-.5-.8-.7.5-.7.6-1.3.4-1.8-.2-.6-.6-1.1-1.2-1.4-.6-.4-1.3-.6-2-.6-1.1 0-2.7.4-4.7 1.3-2 .8-3.8 1.8-5.4 3-1.3.9-2.4 1.8-3.3 2.7-2.1 2.2-4.4 4.1-7.2 4.9-1.2.4-4.6 1.4-5.3-.2-.3-.6.1-1.4.7-2.7 2.1-4.6 6.8-5.9 7.2-6.1.1 0 .4-.1.5 0 .2.2-1.1 1.7-.8 2.3.8-1.1 1.7-1.9 2.7-2.5.6-.4.9-.8.7-1.2-.2-.4-.5-.7-1-1-.5-.3-1-.4-1.6-.4-.4 0-.9.1-1.6.3-1 .3-1.9.7-2.7 1.1-.8.4-1.6.9-2.5 1.5-.5.3-.9.7-1.3 1-1.1.7-3.5 2-6.1 3.9-2.7 1.9-3.4 2.5-5 3.3.7-1.7 1.9-4.3 2.6-5.5l.2-.3c.8-1.1 1.2-1.7 1.3-1.9.3-.4.5-.7.5-.9 0-.2 0-.4-.1-.8-.2-.5-.5-.8-.9-1.2-.4-.3-.8-.5-1.1-.5-.3 0-.7.2-1 .6l-2.6 4.2-.1.1c-1.2 2.1-2 3.6-2.5 4.6-.5 1-.8 2.2-1.1 3.5-.1.7-.1 1.2 0 1.5.2.4.6.8 1.4 1.3.7.4 1.2.7 1.6.7.2 0 .4-.1.5-.2.1-.1.5-.4.9-.9 1-1.3 1.8-2.2 2.1-2.6.8-1 1.1-1.4 1.9-2.1 1.9-1.9 3.4-3.1 4.8-4-.4.6-.8 1.2-1 1.8-.5 1.3-.6 2.5-.2 3.5.4 1 1.1 2.5 2.7 3.3 2.4 1.1 4.3.6 4.7.5 1.8-.3 3.7-1.1 6-3.4 0 .3.1.5.2.7.3.8.9 1.6 1.8 2.3.9.7 1.6 1.1 2.3 1.1.5 0 1.3-.4 2.3-1.2 2.2-1.7 4.5-4 7.1-6.8l.6-.6-.3.7c-.7 1.7-1.2 3-1.5 3.9-.3 1-.3 1.6-.2 2 .2.5.8 1 1.7 1.5.6.3 1 .5 1.4.5.3 0 .5-.1.6-.2.1-.1.2-.3.3-.6.4-1.2.8-2.3 1.4-3.1.5-.8 1.1-1.5 1.9-2.1.7-.6 1.6-1 2.6-1.4 1-.4 2.2-.6 3.4-.8.7-.1 1-.3.9-.6-.4-.3-.7-.5-1.1-.5Zm-9.9-3c-.2.6-.9 1.5-2 2.7-1.9 1.9-4.1 3.4-6.6 4.4-1.2.5-2.1.8-2.5.8-.3 0-.5-.1-.5-.3-.1-.3.2-.8.8-1.7.4-.6 1-1.2 1.7-1.9s1.5-1.3 2.3-1.9c.8-.6 1.6-1 2.4-1.4.7-.4 1.4-.7 2.2-1 .8-.3 1.4-.4 1.8-.4.3 0 .4.1.5.2-.1.2-.1.3-.1.5ZM174.5 139.5c.3-.5.5-.9.5-1.4 0-.4-.1-.7-.4-1-.3-.3-.7-.4-1.1-.5-.7 0-1.5.3-2.4.9-1.3.9-1.8 2.7-3.3 4.5-1.3 1.5-2.8 2.2-2.8 2.6 0 .3.2.5.5.5.5 0 1.5-.4 3.1-1.3s2.9-1.7 4-2.6c.9-.7 1.5-1.3 1.9-1.7ZM183.7 145c-.3-.2-.6-.3-1-.3-.9 0-2.3.3-4.2.8-1.4.4-2.7.9-4.1 1.5-1.3.6-2.3 1.3-3 1.9-.5.5-.7 1-.5 1.5.2.6.7 1 1.5 1.3.5.2 1.8.3 3.7.3 1.4 0 2.4.1 3.2.2-1.3.5-2.3.8-2.9 1.1-2 .8-3.9 1.7-5.6 2.5-1 .5-1.7 1-1.9 1.2-.3.3-.3.6-.2.9.1.3.4.7 1 1 .4.3.8.4 1 .4.2 0 .4-.1.6-.3.9-.7 2.7-1.6 5.2-3 2.2-1.1 3.4-1.7 5.4-2.5 0 0 1.6-.5.5-1.6-.2-.2-.6-.4-1.1-.6-.5-.1-1.4-.2-2.6-.3-.6 0-1.2-.1-1.9-.2s-1.2-.2-1.4-.3c-.2-.1-.4-.2-.5-.4-.1-.2 0-.3.3-.5 1.1-.9 2.6-1.6 4.5-1.9l3-.6c1.2-.2 1.4-.5 1.6-.7.1-.2.2-.3 0-.7 0-.2-.3-.5-.6-.7Z"
              fill-rule="nonzero"
            />
            <path
              d="M270.8 140.1c.4.3.7.4 1.1.4.2 0 .5-.1.8-.3.3-.2.7-.5 1.3-1 .5-.4.8-.7 1.1-.9.5-.4.7-.8.5-1.1-.2-.4-.5-.8-1-1.1-.5-.3-.9-.5-1.4-.5-.5 0-1.2.5-2.1 1.6-.8 1-1.1 1.7-.9 2.2 0 .2.3.5.6.7ZM127.6 140.6c.4.2.7.4 1 .4.2 0 .5-.1.8-.3.3-.2.7-.5 1.3-1 .5-.4.8-.7 1.1-.9.5-.4.7-.8.5-1.1-.2-.4-.5-.8-1-1.1-.5-.3-.9-.5-1.4-.5-.5 0-1.2.5-2.1 1.6-.8 1-1.1 1.7-.9 2.2.1.2.3.4.7.7Z"
              fill-rule="nonzero"
            />
          </g>
        </svg>
      );
    } else if (userGroup.includes('Lennar')) {
      userInfo.clientName = 'Lennar';
      userInfo.shortName = 'Lennar';
      userInfo.png = '/images/logo/Lennar-Logo.png';
      userInfo.flex = '350px';
      userInfo.svg = (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="#235ca3"
          viewBox="0 0 102 10"
        >
          <path d="M4.127 0H0v9.367h11.508V7.561H4.127V0zM14.152 9.367H25.66V7.561h-7.381V5.574h7.381V3.768h-7.381V1.806h7.381V0H14.152v9.367zM41.091 6.22L33.623 0h-4.679v9.367h3.778V3.122l7.294 6.245h4.853V0h-3.778v6.22zM98.455 4.258c.552-.439.814-1.007.814-1.703 0-.49-.175-.93-.523-1.265-.35-.335-.843-.593-1.424-.826-.582-.206-1.22-.335-1.918-.412A61.39 61.39 0 0092.992 0h-7.963v9.367h4.127V1.73h2.528c.494 0 .9 0 1.279.026.377.026.697.051.959.129.406.103.668.232.842.387.175.155.233.361.233.593 0 .258-.058.465-.204.62-.116.155-.348.31-.61.438-.29.13-.668.233-1.104.258-.436.026-.988.052-1.627.052h-1.54l5.433 5.135h5.057l-4.272-4c1.017-.31 1.802-.67 2.325-1.11zM72.33 0L66.52 9.367h4.213l3.836-6.554 1.657 2.838h-2.5L72.65 7.458h4.65l1.104 1.91h4.214L76.806 0H72.33zM60.328 6.22L52.859 0h-4.678v9.367h3.777V3.122l7.294 6.245h4.853V0h-3.777v6.22zM100.372.438h.61c.204 0 .407.078.407.31 0 .103-.058.232-.174.258.116.02*************.258 0 .052 0 .207.058.232h-.319c-.029-.025-.029-.129-.029-.154 0-.104-.029-.207-.175-.207h-.232v.387h-.32V.438zm.32.465h.261c.087 0 .146-.052.146-.13 0-.076-.059-.102-.146-.102h-.261v.232z"></path>
          <path
            fill-rule="evenodd"
            d="M100.895 1.793c.506 0 .916-.363.916-.812 0-.45-.41-.813-.916-.813-.505 0-.915.364-.915.813 0 .449.41.812.915.812zm0 .168c.61 0 1.105-.439 1.105-.98 0-.542-.495-.981-1.105-.981s-1.104.439-1.104.98c0 .542.494.981 1.104.981z"
            clip-rule="evenodd"
          ></path>
        </svg>
      );
    } else if (userGroup.includes('LedgerTC')) {
      userInfo.clientName = 'LedgerTC';
      userInfo.shortName = 'LedgerTC';
      userInfo.png = '/images/logo/LedgerTC-logo.png';
      userInfo.flex = '350px';
      userInfo.svg = (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 1111.3445 903.7067"
        >
          <path
            d="M52.9615 821.6713v6.6895H30.5772v73.9611H22.5v-73.9611H0v-6.6895h52.9615ZM122.1902 902.322l-19.9618-33.6907H87.346v33.6907h-8.0772v-80.6506h24.2287c9.0024 0 15.8283 2.1527 20.4807 6.4612 4.6554 4.3084 6.9831 9.9601 6.9831 16.961 0 5.8444-1.6753 10.826-5.0201 14.9417-3.3477 4.1157-8.2907 6.7874-14.826 8.0179l20.7683 34.2689h-9.6933Zm-34.8441-40.2675h16.2671c6.307 0 11.0573-1.5389 14.2508-4.6168 3.1906-3.0749 4.7888-7.1906 4.7888-12.3442 0-5.3077-1.5389-9.4056-4.6168-12.2878-3.0749-2.8851-7.923-4.3292-14.5384-4.3292H87.3461v33.578ZM207.2234 883.1697h-36.6915l-7.0364 19.1523h-8.5398l29.4237-79.6128h9.115l29.3051 79.6128h-8.5368l-7.0394-19.1523Zm-2.4225-6.6925-15.9232-43.6152-15.9232 43.6152h31.8463ZM296.0669 826.5165c6.3841 3.2291 11.2678 7.8845 14.654 13.9602 3.3833 6.0787 5.0764 13.3078 5.0764 21.6934 0 8.3055-1.6931 15.4813-5.0764 21.5185-3.3863 6.0401-8.27 10.654-14.654 13.8445-6.3841 3.1935-14.0373 4.7888-22.9596 4.7888h-23.8847v-80.6506h23.8847c8.9223 0 16.5755 1.6131 22.9596 4.8451Zm2.7132 60.2856c5.883-5.883 8.8245-14.0936 8.8245-24.632 0-10.6154-2.9415-18.9032-8.8245-24.8662-5.8859-5.9601-14.4435-8.9431-25.6728-8.9431h-15.8075v67.2687h15.8075c11.2292 0 19.7868-2.9415 25.6728-8.8274ZM352.2606 828.2452v30.1176h30.5742v6.6895h-30.5742v30.5772h34.0376v6.6925h-42.1148v-80.7663h42.1148v6.6895h-34.0376ZM522.2114 902.322l-10.9594-11.1907c-3.9259 4.3084-8.1187 7.4812-12.5784 9.5183-4.4626 2.0371-9.5005 3.0571-15.1137 3.0571-5.3848 0-10.1558-1.02-14.3071-3.0571-4.1543-2.0371-7.3863-4.9045-9.6933-8.5961-2.3069-3.6917-3.4604-7.9616-3.4604-12.8067 0-5.6932 1.6131-10.654 4.8451-14.8853 3.2291-4.2284 7.9616-7.5376 14.1915-9.9216-2.6924-3.0008-4.6554-5.7881-5.883-8.3678-1.2335-2.5738-1.8473-5.3641-1.8473-8.3648 0-5.1506 1.8473-9.3256 5.539-12.5191 3.6917-3.1906 8.6139-4.7888 14.7667-4.7888 5.7703 0 10.4612 1.6576 14.0788 4.9638 3.6146 3.3062 5.3077 7.6146 5.0764 12.9224h-8.0772c.0771-3.4634-.9815-6.1914-3.1728-8.1929-2.1913-1.9985-4.943-3.0008-8.2492-3.0008-3.5405 0-6.4048.9815-8.5961 2.9444-2.1943 1.96-3.2884 4.4804-3.2884 7.5553 0 2.4641.6731 4.8481 2.0193 7.155 1.3432 2.3069 3.4782 5.0379 6.4019 8.1929l26.5386 26.8826c1.0764-1.4589 2.5382-3.7688 4.3855-6.9238l7.3834-12.3442h8.6584l-8.774 15.1137c-2.0756 3.6176-4.1543 6.8496-6.2299 9.6933l16.8483 16.961h-10.5028Zm-26.4793-8.3085c3.576-1.7673 6.9801-4.5368 10.2122-8.3055l-25.7321-26.076c-10.6925 3.923-16.0388 10.3842-16.0388 19.3836 0 3.3863.8273 6.4019 2.4819 9.0557 1.6546 2.6539 3.9408 4.7503 6.8645 6.2892s6.2684 2.3069 10.0402 2.3069c4.5368 0 8.5961-.8836 12.1722-2.6539ZM603.9028 840.6516c3.4574-6.2684 8.1899-11.1551 14.1915-14.654 5.9956-3.4989 12.6911-5.2514 20.0744-5.2514 9.0024 0 16.7119 2.1349 23.1345 6.4048 6.4226 4.2699 11.0958 10.2893 14.0195 18.0581h-9.459c-2.3129-5.4619-5.8296-9.7318-10.5621-12.8097-4.7265-3.0749-10.4375-4.6139-17.133-4.6139-5.9245 0-11.25 1.3848-15.9825 4.1543-4.7265 2.7695-8.439 6.7488-11.1314 11.9409-2.6924 5.1921-4.0386 11.2114-4.0386 18.0581s1.3462 12.8453 4.0386 17.9988c2.6924 5.1535 6.4048 9.115 11.1314 11.8845 4.7325 2.7695 10.058 4.1543 15.9825 4.1543 6.6954 0 12.4064-1.5182 17.133-4.5575 4.7325-3.0393 8.2492-7.2499 10.5621-12.6347h9.459c-2.9237 7.6917-7.6146 13.6548-14.0788 17.8832-6.4582 4.2313-14.15 6.3455-23.0752 6.3455-7.3834 0-14.0788-1.7465-20.0744-5.2484-6.0016-3.4989-10.734-8.3648-14.1915-14.5947-3.4634-6.2329-5.195-13.3078-5.195-21.2309s1.7317-15.0188 5.195-21.2872ZM751.5879 883.1697h-36.6915l-7.0394 19.1523h-8.5398l29.4267-79.6128h9.115l29.3022 79.6128h-8.5339l-7.0394-19.1523Zm-2.4256-6.6925-15.9232-43.6152-15.9232 43.6152h31.8463ZM838.3557 861.5919c-4.5368 4.2313-11.3449 6.3455-20.4184 6.3455h-16.2731v34.3845h-8.0772v-80.6506h24.3503c8.9964 0 15.7868 2.1142 20.365 6.3455 4.5723 4.2313 6.8615 9.8474 6.8615 16.8453 0 6.9238-2.2654 12.5013-6.8081 16.7297Zm-1.5004-16.7297c0-5.3848-1.4945-9.462-4.4953-12.2315-3.0008-2.7695-7.8104-4.1543-14.4228-4.1543h-16.2731v32.5402h16.2731c12.614 0 18.918-5.3848 18.918-16.1544ZM880.2363 821.6713v80.6506h-8.0772v-80.6506h8.0772ZM959.2709 821.6713v6.6895h-22.3814v73.9611h-8.0772v-73.9611h-22.5v-6.6895h52.9586ZM1032.3099 883.1697h-36.6915l-7.0394 19.1523h-8.5339l29.4208-79.6128h9.115l29.3081 79.6128h-8.5398l-7.0394-19.1523Zm-2.4197-6.6925-15.9232-43.6152-15.9232 43.6152h31.8463ZM1082.3803 895.7451h28.9641v6.5768h-37.0414v-80.6506h8.0772v74.0738ZM622.9959 276.6652H494.0994V147.4206L365.4547 19.5965v257.1373l40.4571 38.8164V116.4471l47.7305 47.7311v197.1679l40.4571 38.8169 251.7903.0091-122.8938-123.5069Zm-128.8964 88.1877v-49.2875l110.7667.0162 49.1023 49.2713h-159.869Z"
            fill="#153235"
          />
          <path
            d="M745.9504 128.6443 707.134 88.1877H509.9655l-47.73-47.7311H661.338L622.5212 0H365.3849L493.208 128.6443h129.2451v128.8965l123.5064 122.8938-.0091-251.7903ZM661.369 239.4114l-.0426-110.7672h47.8792l1.4352 159.8695-49.2718-49.1023Z"
            fill="#e1a521"
          />
          <path
            d="M883.4079 724.9328H734.7016V503.8602h146.5502v40.6395h-99.7559v46.4934h90.5174v40.3386h-90.5174v52.9617h101.912v40.6395ZM167.3086 724.9222l-38.9126-42.2897H74.6523v-178.772H27.8584v221.0723l139.4502-.0106zM205.3706 684.2928v-52.962h90.5173v-40.3382h-90.5173v-46.4938h99.7559v-40.6394H158.5761v184.9648l33.2244 36.1086h115.482v-40.64H205.3706zM511.0962 724.9299h.0231v-.0231l-.0231.0231zM511.1198 681.6998l.0041-136.8489c.0007-24.3105-19.7326-44.0048-44.0431-43.9558l-139.3875.2808v223.7569l140.1948-.002c12.0686-.0002 23.5877-5.045 31.7719-13.9145 7.3681-7.985 11.4594-18.4515 11.4597-29.3165Zm-134.3914 1.7534V542.6549h72.051c11.5241 0 20.8707 9.3437 20.8707 20.869v119.9293h-92.9217ZM621.748 604.3867v37.5813h51.4248v33.1631c0 4.5966-3.7263 8.3229-8.3229 8.3229H588.574c-4.5966 0-8.3229-3.7263-8.3229-8.3229v-120.925c0-6.3791 5.1713-11.5504 11.5504-11.5504h60.5052c11.524 0 20.8661 9.3421 20.8661 20.8661v14.5401h41.4785v-33.2102c0-24.3108-19.7337-44.0048-44.0445-43.9558l-100.4425.2024c-21.5232.0434-38.9483 17.5036-38.9483 39.0269v143.4994c0 10.3824 3.9097 20.3841 10.9507 28.0141 7.8202 8.4744 18.8267 13.2943 30.3581 13.2941l114.4959-.0016c15.2574-.0002 27.6259-12.3689 27.6259-27.6263v-55.3368h.1984v-37.5813h-93.097ZM1026.5134 639.6427c31.4144-10.4671 49.8908-34.1714 49.8908-64.0429 0-45.8794-32.3288-71.7395-88.0606-71.7395h-86.5193v221.0724h46.7952V542.657h37.5689c28.3296 0 43.4099 11.0811 43.4099 32.9427 0 21.5606-15.0803 32.642-43.4099 32.642h-32.9125l24.5401 38.4833h.0605l49.8778 78.2077h55.7318l-56.9726-85.2901Z"
            fill="#153235"
          />
        </svg>
      );
    } else if (userGroup.includes('Homebound')) {
      userInfo.clientName = 'Homebound';
      userInfo.shortName = 'Homebound';
      userInfo.png = '/images/logo/Homebound-Logo.png';
      userInfo.flex = '350px';
      userInfo.svg = (
        <svg
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 600 100"
        >
          <path
            d="M179.613 39.925h6.483v25.1572h35.433V39.925h6.409v57.9433h-6.409V71.0962h-35.433v26.7712h-6.483V39.925ZM235.75 78.7241c0-11.8088 7.956-20.1703 19.153-20.1703s19.153 8.3615 19.153 20.1703c0 11.8816-7.955 20.3167-19.153 20.3167-11.197 0-19.153-8.4342-19.153-20.3167Zm31.898 0c0-8.6544-5.304-14.7422-12.744-14.7422-7.441 0-12.671 6.0878-12.671 14.7422 0 8.7281 5.23 14.8895 12.671 14.8895 7.439 0 12.744-6.1614 12.744-14.8895ZM280.98 59.7275h6.336v6.8948c2.726-4.8413 7.293-8.0685 13.629-8.0685 6.777 0 11.124 2.9343 12.965 8.7281 3.094-5.3544 8.251-8.7281 14.733-8.7281 9.209 0 13.997 5.5746 13.997 16.1361v23.1772h-6.335V74.91c0-6.6746-3.315-10.9281-9.135-10.9281-7.293 0-12.228 6.4544-12.228 15.329v18.5562h-6.336V74.91c0-6.6746-3.241-10.9281-9.06-10.9281-7.367 0-12.229 6.4544-12.229 15.329v18.5562h-6.336V59.7275h-.001ZM349.725 78.7241c0-11.8088 7.955-20.1703 19.3-20.1703 8.988 0 16.943 6.6746 16.943 19.5098v2.7868h-29.687c.516 7.6281 5.157 12.9089 12.597 12.9089 5.672 0 9.503-3.3737 10.314-6.601h6.261c-1.179 6.3808-7.587 11.8817-16.428 11.8817-11.344.0009-19.3-8.4334-19.3-20.3159Zm29.908-3.007c-.294-7.0413-4.788-11.7352-10.829-11.7352-7.072 0-11.492 4.6211-12.45 11.7352h23.279ZM399.951 91.5604v6.3079h-6.336V39.925h6.336v26.1107c2.726-4.6939 7.146-7.4816 12.671-7.4816 10.092 0 17.311 8.3614 17.311 20.0966 0 11.9553-7.219 20.3904-17.311 20.3904-5.525.0737-9.945-2.7868-12.671-7.4807Zm23.646-12.836c0-8.6545-4.935-14.7422-12.007-14.7422-6.998 0-12.008 6.0877-12.008 14.7422 0 8.7281 5.01 14.8895 12.008 14.8895 7.072 0 12.007-6.1614 12.007-14.8895ZM435.24 78.7241c0-11.8088 7.956-20.1703 19.153-20.1703s19.153 8.3615 19.153 20.1703c0 11.8816-7.955 20.3167-19.153 20.3167-11.197 0-19.153-8.4342-19.153-20.3167Zm31.897 0c0-8.6544-5.304-14.7422-12.744-14.7422-7.44 0-12.671 6.0878-12.671 14.7422 0 8.7281 5.231 14.8895 12.671 14.8895 7.44 0 12.744-6.1614 12.744-14.8895ZM513.359 97.868h-6.336v-6.8947c-2.726 4.8412-7.293 8.0676-13.629 8.0676-9.208 0-13.997-5.5746-13.997-16.1361V59.7284h6.336v22.9571c0 6.6746 3.241 10.9281 9.134 10.9281 7.294 0 12.155-6.4544 12.155-15.329V59.7284h6.336V97.868h.001ZM522.493 59.7275h6.336v6.8948c2.726-4.8413 7.293-8.0685 13.628-8.0685 9.282 0 13.997 5.5746 13.997 16.1361v23.1772h-6.335V74.91c0-6.6746-3.242-10.9281-9.061-10.9281-7.366 0-12.228 6.4544-12.228 15.329v18.5562h-6.336V59.7275h-.001ZM563.45 78.6515c0-11.7351 7.219-20.0965 17.312-20.0965 5.524 0 9.945 2.7868 12.671 7.4816V39.925h6.335v57.9433h-6.335v-6.3079c-2.726 4.6938-7.146 7.5544-12.671 7.4816-10.093-.0009-17.312-8.4352-17.312-20.3905Zm30.351.0728c0-8.6544-5.01-14.7421-12.008-14.7421-7.072 0-12.007 6.0877-12.007 14.7421 0 8.7281 4.935 14.8896 12.007 14.8896 6.998 0 12.008-6.1615 12.008-14.8896ZM159.52 97.8374H1.63036L1.3916 39.9521 78.1557.230835 146.11 38.9012l-3.06 5.3316L78.0157 7.22385 7.57379 43.6731l.19823 48.0239H159.52v6.1404Z"
            fill="#353535"
          />
        </svg>
      );
    } else if (userGroup.includes('Hawkhill')) {
      userInfo.clientName = 'Hawkhill';
      userInfo.shortName = 'Hawkhill';
      userInfo.png = '/images/logo/Hawkhill-Logo.jpeg';
      userInfo.flex = '350px';
      userInfo.svg = (
        <svg
          dataBbox="0 0 1144.18 487.99"
          viewBox="0 0 1144.18 487.99"
          xmlns="http://www.w3.org/2000/svg"
          shapeRendering="geometricPrecision"
          textRendering="geometricPrecision"
          imageRendering="optimizeQuality"
          fillRule="evenodd"
          clipRule="evenodd"
          dataType="color"
          ariaLabel="Hawkhill Homes: Single family homes Real estate investment"
        >
          <path
            d="M0 314.02h111.93v-81.54C51.03 228.76 2.79 178.2 2.79 116.36 2.79 52.09 54.88 0 119.14 0c52.27 0 96.47 34.46 111.16 81.89 15.34-11.06 34.18-17.59 54.54-17.59 51.54 0 93.33 41.79 93.33 93.33 0 49.37-38.34 89.78-86.87 93.1v63.29h121.21v-67.63h-14.25c-3.56 0-6.47-2.91-6.47-6.46V220.5c0-2.11 1.06-4.11 2.79-5.31l132.97-92.46v-69.5c0-3.14 2.26-5.77 5.23-6.35 1.67-.33 3.28-.09 4.7.88l92.04 63.35V84.16c.05-3.63 2.86-6.41 6.46-6.46h40.83c.01-1.29 6.39.79 6.46 6.15v56.07L848.39 37.7a6.486 6.486 0 0 1 6.95.09l213.29 130.48c3.05 1.87 3.94 5.9 2.09 8.9-1.17 1.89-3.28 3.05-5.5 3.05h-40.14v133.8h104.18c3.56 0 6.46 2.9 6.46 6.46 0 3.55-2.9 6.46-6.46 6.46h-110.64c-3.59 0-6.42-2.88-6.47-6.46V173.76c0-3.59 2.91-6.41 6.47-6.46h23.65L851.81 50.78 688.65 151.79l31.32 21.89a6.455 6.455 0 0 1 3.34 5.65v21.03c0 3.56-2.91 6.46-6.46 6.46h-10.36v113.66c0 3.55-2.91 6.46-6.46 6.46-3.56 0-6.47-2.91-6.47-6.46V200.36c0-3.59 2.91-6.41 6.47-6.46h10.35v-11.18l-36.24-25.32a6.434 6.434 0 0 1-2.83-2.48 6.401 6.401 0 0 1-.96-3.38V90.62h-27.91v32.76c0 4.77-5.57 8.47-9.86 5.51l-92.11-63.4v60.75c0 3.94-1.91 3.99-3.8 5.88l-131.95 91.76v9.59h14.25c3.62.05 6.41 2.85 6.46 6.46v80.55c-.05 3.6-2.83 6.41-6.46 6.46H0v-12.92zm111.93-95.26v-11.78l-57.14-74.69c-2.15-2.81-1.53-6.93 1.31-9.04 2.84-2.12 6.87-1.5 9.05 1.31l46.78 60.51v-69.24c0-3.55 2.91-6.46 6.46-6.46 3.56 0 6.47 2.91 6.47 6.46v38.38l44.09-48.07c2.4-2.62 6.51-2.81 9.13-.43 2.62 2.39 2.8 6.52.42 9.13-16.13 17.77-36.97 43.71-53.64 59.37v44.65c28.05-1.54 53.09-14.34 70.7-33.96a93.374 93.374 0 0 1-4.05-27.27c0-25.75 10.43-49.07 27.3-65.95-11.05-44.78-51.48-77.98-99.67-77.98-56.69 0-102.65 45.96-102.65 102.66 0 54.27 42.11 98.7 95.44 102.4zm12.93 13.81v81.45h153.52v-63.29c-33.96-2.32-62.92-22.8-77.25-51.81-19.78 19.64-46.56 32.22-76.27 33.65zm153.52 4.75v-17.05l-46.29-57.73c-2.22-2.77-2.2-6.73.42-9.13 2.63-2.39 6.91-2.35 9.13.43l36.74 45.81v-68.64c0-3.56 2.9-6.46 6.46-6.46 3.55 0 6.46 2.9 6.46 6.46v68.64l36.73-45.81c2.23-2.78 6.51-2.82 9.13-.43 2.63 2.4 2.65 6.36.42 9.13l-46.28 57.73v17.05c41.14-3.29 73.49-37.71 73.49-79.69 0-44.16-35.79-79.95-79.95-79.95-44.16 0-79.95 35.79-79.95 79.95 0 41.98 32.35 76.4 73.49 79.69zm206.18 29.35h44.98v-43.88h-44.98v43.88zm51.45 12.93H478.1c-3.63-.05-6.41-2.86-6.46-6.46v-56.81c0-3.59 2.9-6.41 6.46-6.46h57.91c3.62.05 6.41 2.86 6.46 6.46v56.81c0 3.59-2.91 6.41-6.46 6.46zm392.02-12.93h-44.98v-43.88h44.98v43.88zm-51.44 12.93h57.9c3.63-.05 6.41-2.86 6.46-6.46v-56.81c0-3.58-2.88-6.41-6.46-6.46h-57.9c-3.63.05-6.41 2.86-6.47 6.46v56.81c0 3.58 2.88 6.41 6.47 6.46zm-306.21-68.83h58.97c3.63.05 6.41 2.86 6.46 6.46v109.71h-12.92V223.69h-46.05v103.25h-12.92V217.23c0-3.59 2.9-6.41 6.46-6.46zm275.43 0h-58.97c-3.63.05-6.41 2.86-6.46 6.46v109.71h12.92V223.69h46.05v103.25h12.92V217.23c0-3.59-2.9-6.41-6.46-6.46z"
            fill="#01213b"
            dataColor="1"
          />
          <path
            d="M0 377.07h21.09v41.12h36.42v-41.12h20.94v108.95H57.51v-47.8H21.09v47.8H0V377.07zm160.71 28.38h20.18v80.57h-20.18v-8.58c-3.94 3.75-7.91 6.43-11.88 8.07-3.97 1.64-8.3 2.48-12.93 2.48-10.44 0-19.44-4.02-27.05-12.09-7.62-8.04-11.41-18.05-11.41-30.02 0-12.41 3.69-22.58 11.05-30.52 7.36-7.92 16.29-11.89 26.81-11.89 4.85 0 9.38.91 13.63 2.71 4.25 1.8 8.17 4.53 11.78 8.12v-8.85zm-21.27 16.54c-6.27 0-11.48 2.2-15.6 6.62-4.15 4.4-6.22 10.07-6.22 16.97 0 6.95 2.1 12.67 6.32 17.17 4.23 4.48 9.41 6.73 15.58 6.73 6.35 0 11.63-2.2 15.81-6.63 4.19-4.4 6.29-10.19 6.29-17.35 0-7-2.1-12.67-6.29-17.02-4.18-4.32-9.49-6.49-15.89-6.49zm52.94-16.54H212l16.34 45.84 17.45-45.84h12.16l17.12 45.31 16.24-45.31h19.72l-29.16 80.57h-12.82l-17.35-45.93-17.65 45.93h-12.69l-28.98-80.57zm130.39-31.11h20.18v64.31l29.84-33.2H398l-34.52 38.46 38.87 42.11h-25.11l-34.29-37.15v37.15h-20.18V374.34zm88 0h20.18v39.35c3.95-3.41 7.89-5.97 11.89-7.66 3.97-1.7 8.01-2.56 12.09-2.56 7.96 0 14.69 2.76 20.15 8.3 4.68 4.78 7.01 11.81 7.01 21.06v53.19h-19.88v-35.28c0-9.31-.43-15.63-1.32-18.92-.88-3.31-2.4-5.76-4.55-7.41-2.12-1.62-4.78-2.42-7.91-2.42-4.07 0-7.59 1.36-10.52 4.07-2.91 2.7-4.93 6.39-6.07 11.07-.58 2.43-.89 7.94-.89 16.57v32.32h-20.18V374.34zm98.93-1.97c3.54 0 6.58 1.29 9.11 3.84 2.55 2.56 3.82 5.64 3.82 9.28 0 3.59-1.27 6.65-3.77 9.18-2.51 2.53-5.52 3.8-9.03 3.8-3.59 0-6.65-1.29-9.18-3.87-2.55-2.58-3.82-5.72-3.82-9.41 0-3.54 1.27-6.55 3.77-9.05 2.5-2.51 5.54-3.77 9.1-3.77zm-10.14 33.08h20.18v80.57h-20.18v-80.57zm36.37-31.11h20.18v111.68h-20.18V374.34zm36.36 0h20.19v111.68h-20.19V374.34z"
            fill="#01213b"
            fillRule="nonzero"
            dataColor="1"
          />
          <path
            d="M655.37 377.07h21.09v41.12h36.42v-41.12h20.94v108.95h-20.94v-47.8h-36.42v47.8h-21.09V377.07zm138.99 26.4c7.61 0 14.77 1.9 21.47 5.69 6.73 3.8 11.94 8.93 15.7 15.43 3.77 6.52 5.64 13.53 5.64 21.07 0 7.58-1.89 14.66-5.66 21.26-3.79 6.6-8.95 11.76-15.48 15.48-6.55 3.72-13.73 5.59-21.6 5.59-11.58 0-21.47-4.1-29.63-12.29-8.2-8.22-12.29-18.18-12.29-29.89 0-12.57 4.62-23.04 13.88-31.41 8.12-7.28 17.42-10.93 27.97-10.93zm.3 18.97c-6.29 0-11.53 2.18-15.7 6.53-4.17 4.37-6.27 9.93-6.27 16.74 0 6.98 2.07 12.64 6.19 16.97 4.12 4.32 9.36 6.5 15.71 6.5 6.32 0 11.58-2.21 15.8-6.58 4.2-4.38 6.3-10.02 6.3-16.89 0-6.91-2.07-12.5-6.2-16.82-4.12-4.3-9.4-6.45-15.83-6.45zm58.77-16.99h20.19v9.4c3.46-3.81 7.3-6.67 11.55-8.54 4.23-1.9 8.85-2.84 13.86-2.84 5.03 0 9.61 1.24 13.66 3.72 4.07 2.48 7.33 6.12 9.81 10.88 3.21-4.76 7.16-8.4 11.83-10.88 4.68-2.48 9.82-3.72 15.36-3.72 5.74 0 10.79 1.34 15.14 4 4.38 2.65 7.49 6.14 9.41 10.42 1.9 4.3 2.86 11.28 2.86 20.94v47.19h-20.18V445.3c0-9.1-1.14-15.27-3.44-18.48-2.3-3.22-5.77-4.83-10.35-4.83-3.51 0-6.65.98-9.4 2.95-2.78 1.95-4.86 4.68-6.2 8.15-1.34 3.46-2.02 9.03-2.02 16.71v36.22h-20.18v-38.87c0-7.18-.53-12.39-1.62-15.61-1.06-3.23-2.68-5.61-4.81-7.2-2.15-1.57-4.75-2.35-7.79-2.35-3.38 0-6.47.98-9.23 2.98-2.78 2-4.83 4.78-6.22 8.37-1.36 3.59-2.04 9.26-2.04 16.97v35.71h-20.19v-80.57zm223.43 45.97H1012c.93 5.74 3.44 10.32 7.48 13.73 4.05 3.39 9.23 5.09 15.5 5.09 7.54 0 13.99-2.66 19.4-7.97l16.95 8.02c-4.23 6.02-9.31 10.47-15.2 13.38-5.92 2.88-12.93 4.32-21.04 4.32-12.6 0-22.87-3.97-30.78-11.94-7.92-7.94-11.89-17.9-11.89-29.86 0-12.27 3.97-22.46 11.89-30.55 7.91-8.12 17.83-12.17 29.79-12.17 12.67 0 22.99 4.05 30.93 12.12 7.94 8.09 11.91 18.74 11.91 32.01l-.08 3.82zm-20.1-15.78c-1.34-4.4-3.97-7.99-7.89-10.75-3.95-2.75-8.5-4.12-13.71-4.12-5.64 0-10.57 1.54-14.84 4.65-2.66 1.93-5.14 5.34-7.41 10.22h43.85zm85.91-18.79-12.5 12.57c-5.06-5.06-9.66-7.59-13.81-7.59-2.25 0-4.02.48-5.31 1.45-1.29.96-1.92 2.14-1.92 3.56 0 1.09.41 2.07 1.22 2.98.81.92 2.83 2.15 6.04 3.75l7.38 3.69c7.79 3.82 13.15 7.74 16.06 11.71 2.91 3.99 4.35 8.65 4.35 14.01 0 7.13-2.63 13.1-7.89 17.85-5.26 4.78-12.31 7.16-21.16 7.16-11.74 0-21.15-4.58-28.15-13.76l12.47-13.55c2.37 2.75 5.15 5 8.37 6.7 3.18 1.69 6.02 2.55 8.49 2.55 2.66 0 4.83-.63 6.45-1.92 1.65-1.29 2.46-2.75 2.46-4.42 0-3.11-2.94-6.15-8.83-9.08l-6.8-3.42c-13.05-6.55-19.58-14.74-19.58-24.58 0-6.35 2.46-11.78 7.36-16.28 4.91-4.51 11.18-6.76 18.82-6.76 5.23 0 10.14 1.17 14.77 3.47 4.6 2.3 8.49 5.61 11.71 9.91z"
            fill="#0068bc"
            fillRule="nonzero"
            dataColor="2"
          />
        </svg>
      );
    } else if (userGroup.includes('GreatGulf')) {
      userInfo.clientName = 'GreatGulf';
      userInfo.shortName = 'GreatGulf';
      userInfo.png = '/images/logo/greatgulf-logo.png';
      userInfo.flex = '350px';
      userInfo.svg = (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 749 83">
          <path
            d="M525.2 80.38c8.68 0 18.17-1.93 24-5.63V44.21h-15.16v21.06c-2.799792.9168021-5.734597 1.3529656-8.68 1.29-12.86 0-22.66-9.65-22.66-24.92 0-14.79 9.16-24.44 22.66-24.44 7.769786-.0406226 15.276763 2.8110306 21.06 8V8.68c-5.95-3.86-13.34-5.62-21.86-5.62-22.19 0-37.62 15.75-37.62 38.74 0 23.47 15.11 38.58 38.26 38.58Zm115.45-1.28h46.78V64.95h-31.21V4.18h-15.57V79.1ZM749.3 18.17v-14h-46.46v74.75h15.59V50.8h28.46v-14h-28.46V18.17h30.87ZM623.43 52.41V4.02h-15.6v44.85c0 13.35-6.75 17.36-12.86 17.36-6.11 0-12.86-4-12.86-17.36V4.02h-15.59v48.71c0 21.06 15.59 27.33 28.29 27.33 13.02 0 28.62-6.43 28.62-27.65ZM269.59 79.1h47.27v-14H284.7V48.55h28.78V34.89h-28.61V18.17h31.34v-14h-46.78v74.75h.16v.18Zm104.34-28.94h-17.37l8.85-24.6 8.52 24.6Zm27.16 28.94L372.65 4.18h-14.48l-28.29 74.75h16.24l5.46-15.75h26.69l5.78 15.75h17v.17h.04Zm34.89-60.93h21.67v-14h-58.81v14h21.71V79.1h15.43V18.17Zm-282 62.21c8.68 0 18.16-1.93 23.95-5.63V44.21h-15.08v21.06c-2.799969.9160512-5.734646 1.3521956-8.68 1.29-12.86 0-22.67-9.65-22.67-24.92 0-14.79 9.16-24.44 22.67-24.44 7.76944-.0387059 15.27575 2.8126941 21.06 8V8.68c-5.95-3.86-13.35-5.62-21.87-5.62-22-.16-37.61 15.75-37.61 38.74 0 23.47 15.11 38.58 38.26 38.58h-.03Zm100.79-1.28-19.12-29.26c8.68-4 14.15-11.1 14.15-21.54 0-15.44-11.09-24.28-27-24.28h-27.48v74.75h15.44v-26.5h10l16.56 26.52H255v.33l-.23-.02Zm-44-61.41h10.28c7.56-.17 12.7 4.17 12.7 10.61 0 6.26-5.14 10.61-12.7 10.61h-10.29l.01-21.22ZM0 0v68.32h63.5V29.9H30.7v8.2h24.12v21.7H8.36V8.2H63.5V0H0Zm15.43 14.95v38.42h32.8v-8.36H23.79V23.15h46.46v51.6H15.43v8.36h63.5V14.95h-63.5Z"
            fill="#0C2340"
            fillRule="nonzero"
          />
        </svg>
      );
    } else if (userGroup.includes('Pathway')) {
      userInfo.clientName = 'Pathway';
      userInfo.shortName = 'Pathway';
      userInfo.png = '/images/logo/Pathway_logo.png';
      userInfo.flex = '450px';
      userInfo.svg = (
        <svg
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 286 47"
        >
          <path
            d="M49.013 37.901c.616-.374 1.012-.836 1.188-1.386.176-.572.264-1.474.264-2.706V17.21c0-1.276-.088-2.178-.264-2.706-.176-.55-.572-1.012-1.188-1.386v-.099h8.019c6.358 0 9.537 2.112 9.537 6.336 0 2.244-.913 3.916-2.739 5.016-1.826 1.078-4.323 1.617-7.491 1.617h-2.97v7.821c0 1.232.088 2.134.264 2.706.198.55.616 1.012 1.254 1.386V38h-5.874v-.099Zm7.161-13.233c2.332 0 4.147-.429 5.445-1.287 1.32-.858 1.98-2.2 1.98-4.026 0-3.344-2.244-5.016-6.732-5.016h-3.498v10.329h2.805Zm14.8121 13.761c-1.408 0-2.552-.352-3.432-1.056-.858-.726-1.287-1.771-1.287-3.135 0-.946.231-1.727.693-2.343.484-.616 1.155-1.133 2.013-1.551.88-.418 2.057-.836 3.531-1.254 1.254-.352 2.178-.649 2.772-.891.616-.242 1.1-.572 1.452-.99.352-.418.528-.99.528-1.716 0-.924-.286-1.628-.858-2.112-.572-.506-1.397-.759-2.475-.759-1.254 0-2.321.385-3.201 1.155-.858.748-1.298 1.892-1.32 3.432h-2.541c.066-1.254.44-2.321 1.122-3.201.682-.88 1.562-1.54 2.64-1.98 1.078-.44 2.255-.66 3.531-.66 1.738 0 3.124.418 4.158 1.254 1.034.814 1.551 2.101 1.551 3.861v7.623c0 1.782.517 2.673 1.551 2.673.484 0 .902-.143 1.254-.429v.99c-.66.616-1.441.924-2.343.924-.924 0-1.661-.33-2.211-.99-.55-.66-.825-1.639-.825-2.937v-.759c-.44 1.408-1.199 2.574-2.277 3.498-1.078.902-2.42 1.353-4.026 1.353Zm-2.145-4.521c0 .946.264 1.65.792 2.112.55.462 1.254.693 2.112.693 1.078 0 2.035-.297 2.871-.891.836-.594 1.485-1.408 1.947-2.442.462-1.034.693-2.2.693-3.498v-1.848c-.242.462-.649.825-1.221 1.089-.55.242-1.518.594-2.904 1.056-1.518.484-2.618.99-3.3 1.518-.66.506-.99 1.243-.99 2.211Zm21.1752 4.389c-2.926 0-4.389-1.76-4.389-5.28v-9.768h-3.465v-1.32h3.465v-4.554l2.475-2.046h.165v6.6h5.445v1.32h-5.445v9.735c0 1.364.231 2.31.693 2.838.484.528 1.144.792 1.98.792 1.496 0 2.64-.836 3.432-2.508l.33 1.023c-.418 1.012-1.045 1.793-1.881 2.343-.814.55-1.749.825-2.805.825Zm6.1287-.396c.572-.374.946-.847 1.122-1.419.176-.572.264-1.463.264-2.673V16.484c0-1.056-.088-1.848-.264-2.376-.154-.528-.473-.968-.957-1.32v-.099l4.092-.495.033.099c-.088.506-.154 1.034-.198 1.584-.022.528-.033 1.397-.033 2.607v9.042c.528-1.21 1.309-2.2 2.343-2.97 1.056-.77 2.321-1.155 3.795-1.155 3.344 0 5.016 2.024 5.016 6.072v6.336c0 1.21.077 2.101.231 2.673.176.572.528 1.045 1.056 1.419V38h-5.181v-.099c.506-.374.836-.836.99-1.386.154-.572.231-1.474.231-2.706v-6.303c0-1.54-.286-2.662-.858-3.366-.572-.726-1.452-1.089-2.64-1.089-1.276 0-2.354.418-3.234 1.254-.858.814-1.441 1.848-1.749 3.102v6.402c0 1.232.077 2.134.231 2.706.176.55.55 1.012 1.122 1.386V38h-5.412v-.099Zm19.552-12.87c-.308-.902-.594-1.573-.858-2.013-.264-.462-.616-.781-1.056-.957v-.132h5.181v.132c-.506.198-.759.572-.759 1.122 0 .396.121.957.363 1.683l3.201 9.504 3.597-11.682h1.848l3.729 11.913 3.201-9.702c.264-.836.396-1.43.396-1.782 0-.55-.253-.902-.759-1.056v-.132h3.861v.132c-.396.242-.737.583-1.023 1.023-.286.418-.561 1.034-.825 1.848l-3.96 11.418c-.264.66-.418 1.21-.462 1.65h-1.815l-3.828-12.276-3.201 10.428c-.242.792-.396 1.408-.462 1.848h-1.848l-4.521-12.969Zm27.477 13.398c-1.408 0-2.552-.352-3.432-1.056-.858-.726-1.287-1.771-1.287-3.135 0-.946.231-1.727.693-2.343.484-.616 1.155-1.133 2.013-1.551.88-.418 2.057-.836 3.531-1.254 1.254-.352 2.178-.649 2.772-.891.616-.242 1.1-.572 1.452-.99.352-.418.528-.99.528-1.716 0-.924-.286-1.628-.858-2.112-.572-.506-1.397-.759-2.475-.759-1.254 0-2.321.385-3.201 1.155-.858.748-1.298 1.892-1.32 3.432h-2.541c.066-1.254.44-2.321 1.122-3.201.682-.88 1.562-1.54 2.64-1.98 1.078-.44 2.255-.66 3.531-.66 1.738 0 3.124.418 4.158 1.254 1.034.814 1.551 2.101 1.551 3.861v7.623c0 1.782.517 2.673 1.551 2.673.484 0 .902-.143 1.254-.429v.99c-.66.616-1.441.924-2.343.924-.924 0-1.661-.33-2.211-.99-.55-.66-.825-1.639-.825-2.937v-.759c-.44 1.408-1.199 2.574-2.277 3.498-1.078.902-2.42 1.353-4.026 1.353Zm-2.145-4.521c0 .946.264 1.65.792 2.112.55.462 1.254.693 2.112.693 1.078 0 2.035-.297 2.871-.891.836-.594 1.485-1.408 1.947-2.442.462-1.034.693-2.2.693-3.498v-1.848c-.242.462-.649.825-1.221 1.089-.55.242-1.518.594-2.904 1.056-1.518.484-2.618.99-3.3 1.518-.66.506-.99 1.243-.99 2.211Zm15.011 12.21c-.682 0-1.32-.099-1.914-.297v-2.277c.528.242 1.254.363 2.178.363 1.188 0 2.2-.451 3.036-1.353.858-.88 1.716-2.398 2.574-4.554h-.66l-4.686-12.177c-.418-1.1-.781-1.925-1.089-2.475-.286-.55-.66-.979-1.122-1.287v-.132h5.016v.132c-.374.242-.561.616-.561 1.122 0 .44.22 1.287.66 2.541l3.465 9.537 3.399-9.537c.396-1.122.594-1.925.594-2.409 0-.594-.22-1.012-.66-1.254v-.132h3.993v.132c-.418.308-.792.748-1.122 1.32-.33.55-.693 1.331-1.089 2.343L163.366 38c-.748 1.98-1.485 3.553-2.211 4.719-.704 1.166-1.474 2.024-2.31 2.574-.814.55-1.749.825-2.805.825Zm24.619-8.217c.616-.374 1.012-.836 1.188-1.386.176-.572.264-1.474.264-2.706V17.21c0-1.276-.088-2.178-.264-2.706-.176-.55-.572-1.012-1.188-1.386v-.099h5.808v.099c-.616.374-1.012.836-1.188 1.386-.176.528-.264 1.43-.264 2.706v7.029h12.672V17.21c0-1.276-.088-2.178-.264-2.706-.154-.55-.55-1.012-1.188-1.386v-.099h5.841v.099c-.638.374-1.045.836-1.221 1.386-.154.528-.231 1.43-.231 2.706v16.599c0 1.232.088 2.134.264 2.706.176.55.572 1.012 1.188 1.386V38h-5.841v-.099c.616-.374 1.012-.836 1.188-1.386.176-.572.264-1.474.264-2.706v-8.184h-12.672v8.184c0 1.232.088 2.134.264 2.706.176.55.572 1.012 1.188 1.386V38h-5.808v-.099Zm32.531.627c-1.628 0-3.058-.352-4.29-1.056-1.21-.726-2.156-1.738-2.838-3.036-.66-1.298-.99-2.794-.99-4.488 0-1.672.33-3.157.99-4.455.682-1.298 1.628-2.299 2.838-3.003 1.232-.726 2.662-1.089 4.29-1.089 1.628 0 3.047.363 4.257 1.089 1.232.704 2.178 1.705 2.838 3.003.66 1.298.99 2.783.99 4.455 0 1.694-.33 3.19-.99 4.488-.66 1.298-1.606 2.31-2.838 3.036-1.21.704-2.629 1.056-4.257 1.056Zm-5.379-8.58c0 2.288.473 4.081 1.419 5.379.968 1.298 2.288 1.947 3.96 1.947 1.672 0 2.981-.649 3.927-1.947.968-1.32 1.452-3.113 1.452-5.379 0-2.266-.484-4.048-1.452-5.346-.946-1.298-2.255-1.947-3.927-1.947-1.672 0-2.992.649-3.96 1.947-.946 1.276-1.419 3.058-1.419 5.346Zm16.275 7.953c.55-.374.902-.847 1.056-1.419.176-.572.264-1.463.264-2.673v-7.656c0-1.276-.088-2.178-.264-2.706-.154-.55-.506-1.012-1.056-1.386v-.132h4.422c-.176.396-.297.847-.363 1.353s-.11 1.254-.132 2.244c.528-1.21 1.298-2.2 2.31-2.97 1.034-.77 2.266-1.155 3.696-1.155 2.794 0 4.411 1.463 4.851 4.389.506-1.276 1.276-2.321 2.31-3.135 1.034-.836 2.299-1.254 3.795-1.254 3.3 0 4.95 2.024 4.95 6.072v6.336c0 1.232.077 2.134.231 2.706.154.55.495 1.012 1.023 1.386V38h-5.082v-.099c.506-.374.825-.836.957-1.386.154-.572.231-1.474.231-2.706v-6.303c0-1.54-.286-2.662-.858-3.366-.55-.726-1.408-1.089-2.574-1.089-1.232 0-2.277.418-3.135 1.254-.836.814-1.419 1.859-1.749 3.135V33.809c0 1.232.077 2.134.231 2.706.154.55.495 1.012 1.023 1.386V38h-5.082v-.099c.506-.374.825-.836.957-1.386.154-.55.231-1.452.231-2.706v-6.303c0-1.54-.286-2.662-.858-3.366-.55-.726-1.408-1.089-2.574-1.089-1.232 0-2.277.418-3.135 1.254-.836.814-1.419 1.859-1.749 3.135v6.369c0 1.232.077 2.134.231 2.706.154.55.495 1.012 1.023 1.386V38h-5.181v-.099Zm37.533.66c-1.518 0-2.871-.341-4.059-1.023-1.166-.704-2.079-1.694-2.739-2.97-.66-1.276-.99-2.761-.99-4.455 0-1.716.341-3.234 1.023-4.554.682-1.32 1.628-2.343 2.838-3.069 1.21-.726 2.574-1.089 4.092-1.089 1.474 0 2.761.319 3.861.957 1.122.638 1.98 1.529 2.574 2.673.616 1.122.935 2.409.957 3.861h-12.639c-.022.176-.033.429-.033.759 0 2.134.473 3.872 1.419 5.214.968 1.32 2.365 1.98 4.191 1.98 1.518 0 2.75-.451 3.696-1.353.946-.902 1.595-2.09 1.947-3.564h1.485c-.374 1.958-1.21 3.553-2.508 4.785-1.298 1.232-3.003 1.848-5.115 1.848Zm4.884-10.923c-.11-1.474-.583-2.673-1.419-3.597-.814-.946-1.925-1.419-3.333-1.419-1.364 0-2.486.451-3.366 1.353-.88.902-1.452 2.123-1.716 3.663h9.834Zm12.402 10.923c-2.068 0-3.762-.517-5.082-1.551-1.298-1.034-2.057-2.453-2.277-4.257h2.475c.176 1.43.671 2.541 1.485 3.333.836.792 1.98 1.188 3.432 1.188 1.1 0 1.991-.231 2.673-.693.682-.484 1.023-1.155 1.023-2.013 0-.616-.176-1.122-.528-1.518-.33-.418-.803-.77-1.419-1.056-.616-.308-1.595-.726-2.937-1.254-1.188-.462-2.156-.891-2.904-1.287-.726-.418-1.32-.924-1.782-1.518-.44-.616-.66-1.353-.66-2.211 0-1.32.539-2.365 1.617-3.135 1.078-.792 2.486-1.188 4.224-1.188 1.848 0 3.355.429 4.521 1.287 1.166.836 1.881 2.013 2.145 3.531h-2.508c-.132-1.1-.561-1.969-1.287-2.607-.704-.638-1.694-.957-2.97-.957-1.034 0-1.859.22-2.475.66-.594.44-.891 1.045-.891 1.815 0 .572.165 1.067.495 1.485.352.396.825.748 1.419 1.056.616.286 1.485.649 2.607 1.089 1.342.506 2.409.979 3.201 1.419.792.44 1.408.968 1.848 1.584.462.616.693 1.375.693 2.277 0 1.43-.572 2.541-1.716 3.333-1.144.792-2.618 1.188-4.422 1.188ZM11.218 29.269c-3.14415 2.6834-5.89959 5.7912-8.18706 9.234C14.5619 35.811 18.154 25.02 18.154 25.02c-2.4192 1.2334-4.7383 2.6541-6.936 4.249ZM17.491 23.9211l-.06-.025C10.1883 21.9038 3.96367 17.2529 0 10.8721c2.629 8.187 4.61498 9.9 6.82898 11.9 1.06745.9388 2.32908 1.6303 3.69472 2.0251 1.3656.3948 2.8016.4832 4.2053.2589.9823-.2098 1.9189-.5946 2.765-1.136l-.003.001Z"
            fill="#004F68"
          />
          <path
            d="M23.695 18.42c-.329 1.31-1.907 5.747-3.46 9.728-1.5148 3.6566-4.1703 6.726-7.571 8.751 2.6721-.1829 5.2481-1.0698 7.4664-2.5706 2.2183-1.5008 3.9999-3.5621 5.1636-5.9744.092-.177.987-2.147 1.054-2.332.9358-2.4609 2.064-4.8443 3.374-7.128 1.2741-1.969 2.8939-3.6914 4.781-5.084.3453-.2487.6907-.4837 1.036-.705 2.1955-1.4901 4.8106-2.2376 7.462-2.133 0 0-2.947-1.52699-9.612 2.15-2.087-.21-4.117-.933-5.85-.844-2.091.108-3.454 1.2-3.807 2.767v-.006c-.443 1.1-.255 4.032-2.579 4.714C15.93 21.285 12.524 11.8 2.83398 3c0 0 3.154 16.233 14.15402 18.572 1.3076.2507 2.661.0808 3.8659-.4855 1.205-.5663 2.1996-1.4998 2.8411-2.6665Z"
            fill="#004F68"
          />
        </svg>
      );
    } else if (userGroup.includes('USLegacy')) {
      userInfo.clientName = 'USLegacy';
      userInfo.shortName = 'USLegacy';
      userInfo.png = '/images/logo/USLegacy_logo.png';
      userInfo.flex = '450px';
      userInfo.svg = (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 211.84 33.02">
          <path
            d="M59.28 22.87A8.75 8.75 0 0 1 53.19 25a8.75 8.75 0 0 1-6.09-2.13 7.45 7.45 0 0 1-2.36-5.77V1.99h3.67v14.87a4.54 4.54 0 0 0 1.18 3.29 4.86 4.86 0 0 0 3.6 1.23 4.75 4.75 0 0 0 3.57-1.23 4.48 4.48 0 0 0 1.16-3.29V1.99h3.67v15.1a7.45 7.45 0 0 1-2.31 5.78Zm9.15 1.39a2.37 2.37 0 0 1-3.38 0 2.34 2.34 0 0 1-.7-1.71 2.4 2.4 0 0 1 .67-1.71 2.33 2.33 0 0 1 1.72-.71 2.29 2.29 0 0 1 1.69.71 2.43 2.43 0 0 1 0 3.41Zm11.71.74a9.79 9.79 0 0 1-5.49-1.49 7.81 7.81 0 0 1-3.13-4.07l3.16-1.87a5.35 5.35 0 0 0 5.55 3.81 4.94 4.94 0 0 0 3.09-.81 2.64 2.64 0 0 0 1.05-2.16 2.33 2.33 0 0 0-1.12-2.1 16.61 16.61 0 0 0-4-1.61 26.61 26.61 0 0 1-2.58-.9 13.07 13.07 0 0 1-2.11-1.16 5 5 0 0 1-1.63-1.9 6 6 0 0 1-.54-2.65 5.83 5.83 0 0 1 2.11-4.74 7.61 7.61 0 0 1 5.07-1.77 8.33 8.33 0 0 1 4.69 1.32 8.62 8.62 0 0 1 3.15 3.61l-3.09 1.84a4.81 4.81 0 0 0-4.69-3.16 3.94 3.94 0 0 0-2.55.77 2.43 2.43 0 0 0-1 2 2.4 2.4 0 0 0 1 2 13.65 13.65 0 0 0 3.54 1.55q1.28.42 1.85.65t1.66.65a6.49 6.49 0 0 1 1.63.9 11.07 11.07 0 0 1 1.16 1.12 3.79 3.79 0 0 1 .89 1.55 7 7 0 0 1 .25 2 6 6 0 0 1-2.2 4.87 8.73 8.73 0 0 1-5.72 1.74Zm14-.74a2.37 2.37 0 0 1-3.38 0 2.35 2.35 0 0 1-.7-1.71 2.4 2.4 0 0 1 .67-1.71 2.33 2.33 0 0 1 1.72-.71 2.29 2.29 0 0 1 1.69.71 2.43 2.43 0 0 1 .02 3.41Zm16.23-3.23h9.09v3.55h-12.74V1.99h3.67v19Zm13.8-3a4.5 4.5 0 0 0 1.75 2.8 5.66 5.66 0 0 0 3.35 1 4.66 4.66 0 0 0 4.12-2l2.86 1.68a7.87 7.87 0 0 1-7 3.52 8.51 8.51 0 0 1-6.28-2.39 8.32 8.32 0 0 1-2.39-6.1 8.3 8.3 0 0 1 2.34-6.03 8 8 0 0 1 6.06-2.45 7.37 7.37 0 0 1 5.77 2.45 8.63 8.63 0 0 1 2.26 6 10.92 10.92 0 0 1-.13 1.45h-12.69Zm0-2.84h9.38a4.63 4.63 0 0 0-1.63-3 4.54 4.54 0 0 0-2.93-1 4.64 4.64 0 0 0-4.83 3.96Zm28.49-6.71h3.38v15.35a6.9 6.9 0 0 1-2.4 5.68 8.84 8.84 0 0 1-5.8 2q-5.52 0-7.56-3.9l3-1.74q1.24 2.48 4.66 2.48a4.82 4.82 0 0 0 3.48-1.19 4.18 4.18 0 0 0 1.28-3.26v-2a6.35 6.35 0 0 1-5.55 2.81 7.62 7.62 0 0 1-5.71-2.42 8.27 8.27 0 0 1-2.33-5.9 8.16 8.16 0 0 1 2.28-5.88 7.57 7.57 0 0 1 5.71-2.45 6.35 6.35 0 0 1 5.55 2.81V8.44Zm-8.74 11.48a5 5 0 0 0 3.64 1.45 5 5 0 0 0 5.07-5.07 5 5 0 0 0-5.07-5.06 5.12 5.12 0 0 0-3.64 8.68Zm28.73-11.52h3.45v16.14h-3.45v-2.33a6.43 6.43 0 0 1-5.58 2.74 7.46 7.46 0 0 1-5.59-2.48 8.5 8.5 0 0 1-2.33-6 8.43 8.43 0 0 1 2.33-6 7.46 7.46 0 0 1 5.61-2.45 6.45 6.45 0 0 1 5.58 2.71V8.44Zm-8.64 11.78a5.2 5.2 0 0 0 7.21 0 5.13 5.13 0 0 0 1.45-3.71 5 5 0 0 0-1.43-3.68 5.12 5.12 0 0 0-7.21 0 5 5 0 0 0-1.44 3.68 5.12 5.12 0 0 0 1.44 3.71Zm23.63 4.77a8.14 8.14 0 0 1-6-2.45 8.3 8.3 0 0 1-2.39-6 8.24 8.24 0 0 1 2.39-6 8.6 8.6 0 0 1 10.28-1.39 7.26 7.26 0 0 1 2.87 3l-3 1.74a4 4 0 0 0-1.66-1.81 4.66 4.66 0 0 0-2.52-.68 4.78 4.78 0 0 0-3.54 1.45 5 5 0 0 0-1.4 3.61 5 5 0 0 0 1.4 3.61 5 5 0 0 0 6.06.77 4.37 4.37 0 0 0 1.79-1.75l3 1.71a7.64 7.64 0 0 1-3 3.07 8.19 8.19 0 0 1-4.26 1.12Zm20.51-16.55h3.67l-6.12 16.65q-2.3 6.26-7.78 5.94v-3.26a3.65 3.65 0 0 0 2.61-.71 5.53 5.53 0 0 0 1.63-2.45l.16-.32-6.76-15.84h3.76l4.75 11.71ZM33.62 33.02v-33h1.58v33h-1.58Z"
            fill="#4b4c4d"
          />
          <path
            d="M0 17.97v14.41l24.2-17.33 7 4.85V5.65l-7-5Z"
            fill="#1c75bc"
          />
        </svg>
      );
    } else if (userGroup.includes('GEMRC')) {
      userInfo.clientName = 'GEMRC';
      userInfo.shortName = 'GEMRC';
      userInfo.png = '/images/logo/GEMRC_logo.png';
      userInfo.flex = '450px';
    } else if (userGroup.includes('Avanta')) {
      userInfo.clientName = 'Avanta';
      userInfo.shortName = 'Avanta';
      userInfo.png = '/images/logo/Avanta-Logo.png';
      userInfo.flex = '350px';
    } else if (userGroup.includes('BridgeFull')) {
      userInfo.clientName = 'Bridge';
      userInfo.shortName = 'Bridge';
      userInfo.png = '/images/logo/Bridge_IG_Logo_main_Logo.jpg';
      userInfo.flex = '350px';
    } else if (userGroup.includes('BridgeAdmin')) {
      userInfo.clientName = 'Bridge';
      userInfo.shortName = 'Bridge';
      userInfo.png = '/images/logo/Bridge_IG_Logo_main_Logo.jpg';
      userInfo.flex = '350px';
    } else if (userGroup.includes('Camillo')) {
      userInfo.clientName = 'Camillo';
      userInfo.shortName = 'Camillo';
      userInfo.png = '/images/logo/Camillo-Logo.png';
      userInfo.flex = '450px';
    } else if (userGroup.includes('ILE')) {
      userInfo.clientName = 'ILE';
      userInfo.shortName = 'ILE';
      userInfo.png = '/images/logo/ILE_logo.png';
      userInfo.flex = '300px';
      userInfo.svg = (
        <svg
          data-bbox="0.812 0.507 151.68 57.18"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0.812 0.507 151.68 57.18"
          data-type="color"
          aria-hidden="true"
        >
          <path
            fill="#C70000"
            d="M16.121 27.398c0 1.445-1.104 2.357-2.313 2.357-1.208 0-2.43-.912-2.43-2.357 0-1.445 1.222-2.464 2.43-2.464 1.209 0 2.313 1.02 2.313 2.464Z"
            data-color="1"
          />
          <path
            d="M12.064 34.032h3.501v23.13h-3.5V34.031ZM19.214 57.161h3.505v-33.97h-3.505v33.97ZM39.062 52.385c-1.195.765-2.908 1.626-5.178 1.626-1.991 0-3.545-1.052-4.301-3.11-.438-1.244-.558-3.397-.399-4.402 7.13.144 12.825-1.483 12.825-7.56 0-3.3-1.991-5.932-5.656-5.932-6.213 0-10.514 7.895-10.514 14.88 0 5.12 2.23 9.282 7.328 9.282 2.589 0 4.939-.813 6.373-1.818l-.478-2.966Zm-.24-13.302c0 3.589-4.062 4.402-9.24 4.354.797-3.636 3.147-7.32 6.294-7.32 1.752 0 2.947 1.053 2.947 2.966Zm.295-14.15-5.252 5.514 2.672.81 4.545-4.33-1.965-1.993Z"
            data-color="2"
          />
          <path
            fill="#00478A"
            d="M23.817 1.132a3.725 3.725 0 0 1 4.131 0l21.346 14.222a3.725 3.725 0 0 1 1.66 3.1v38.938h-3.45V18.454a.275.275 0 0 0-.122-.229L26.035 4.003a.275.275 0 0 0-.305 0L4.384 18.225a.275.275 0 0 0-.122.23v38.95H.812v-38.95c0-1.247.623-2.41 1.66-3.1l.956 1.435-.957-1.436L23.817 1.132Z"
            clip-rule="evenodd"
            fill-rule="evenodd"
            data-color="3"
          />
          <path
            d="M60.062 57.21h3.443V42.943c0-.668.04-1.336.202-1.908.608-2.29 2.187-4.008 4.253-4.008 2.998 0 3.97 2.958 3.97 6.44V57.21h3.402V42.992c0-7.3-3.524-9.351-6.157-9.351-1.255 0-2.39.43-3.321 1.097-.932.62-1.742 1.575-2.268 2.672h-.081V23.69h-3.443v33.52ZM86.218 33.64c-4.78 0-8.79 4.247-8.79 12.167 0 7.538 3.727 11.88 8.507 11.88 4.212 0 8.79-3.531 8.79-12.167 0-7.347-3.525-11.88-8.507-11.88Zm-.121 3.102c3.807 0 5.144 5.057 5.144 8.874 0 4.723-1.944 9.017-5.144 9.017-3.281 0-5.185-4.246-5.185-8.922 0-4.15 1.458-8.97 5.185-8.97ZM96.872 57.21h3.362V43.086c0-.715.081-1.431.243-2.051.526-2.195 2.025-4.056 3.969-4.056 2.593 0 3.686 2.672 3.686 6.012v14.217h3.362V42.753c0-.763.081-1.479.243-2.099.567-2.052 1.985-3.674 3.808-3.674 2.713 0 3.807 2.72 3.807 6.775V57.21h3.362V43.087c0-7.347-3.321-9.446-5.954-9.446-1.58 0-2.755.477-3.808 1.431-.81.62-1.539 1.622-2.146 2.863h-.081c-.811-2.624-2.674-4.294-5.104-4.294-2.916 0-4.618 1.86-5.63 3.96h-.081l-.203-3.483H96.71c.081 1.908.162 3.673.162 6.154V57.21ZM136.831 53.058c-1.134.716-2.673 1.48-4.739 1.48-1.904 0-3.281-1.002-3.969-3.054-.405-1.288-.446-3.578-.324-4.485 6.683.143 11.989-1.717 11.989-7.633 0-3.101-1.742-5.725-5.225-5.725-6.076 0-10.045 8.301-10.045 15.076 0 4.866 1.984 8.922 6.845 8.922 2.349 0 4.455-.716 5.914-1.718l-.446-2.863Zm-.121-13.549c0 3.626-3.889 4.58-8.506 4.532.891-4.007 3.118-7.347 5.873-7.347 1.579 0 2.633 1.002 2.633 2.815ZM140.584 56.112c1.215.906 3.078 1.526 5.023 1.526 4.212 0 6.885-2.671 6.885-6.917 0-3.388-1.741-5.391-4.82-6.966-2.349-1.288-3.321-2.147-3.321-3.96 0-1.67 1.053-3.1 3.038-3.1 1.58 0 2.835.715 3.605 1.287l.85-2.958c-1.012-.763-2.551-1.383-4.415-1.383-3.848 0-6.359 2.958-6.359 6.68 0 2.718 1.58 4.913 4.861 6.535 2.39 1.288 3.281 2.386 3.281 4.294 0 2.004-1.175 3.435-3.524 3.435-1.62 0-3.322-.81-4.253-1.527l-.851 3.054Z"
            data-color="2"
          />
        </svg>
      );
    } else if (userGroup.includes('InvitationHomes')) {
      userInfo.clientName = 'InvitationHomes';
      userInfo.shortName = 'InvitationHomes';
      userInfo.png = '/images/logo/InvitationHomes-Logo.png';
      userInfo.flex = '450px';
      userInfo.svg = (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 517.21 100.89">
          <path
            d="m86.54 50.45 9.47-9.47c1.98-1.98 1.98-5.2 0-7.18L67.89 5.68c-1.98-1.98-5.2-1.98-7.18 0l-9.47 9.47-9.47-9.47c-1.98-1.98-5.2-1.98-7.18 0L6.48 33.8c-1.98 1.98-1.98 5.2 0 7.18l9.47 9.47-9.47 9.47c-1.98 1.98-1.98 5.2 0 7.18L34.6 95.21c1.98 1.98 5.2 1.98 7.18 0l9.47-9.47 9.47 9.47c1.98 1.98 5.2 1.98 7.18 0l28.12-28.12c1.98-1.98 1.98-5.2 0-7.18l-9.48-9.46z"
            fill="#9fcc3b"
          />
          <path
            d="M40.52 75.02c-1.25-1.25-1.76-3.06-1.35-4.77l2.45-10.18-10.18 2.45c-1.72.41-3.53-.1-4.77-1.35L15.94 50.45l-9.47 9.47c-1.98 1.98-1.98 5.2 0 7.18L34.6 95.21c1.98 1.98 5.2 1.98 7.18 0l9.47-9.47-10.73-10.72z"
            fill="#04a54f"
          />
          <path d="M57.86 47.58c-.45-1.85-1.89-3.3-3.75-3.75l-22.67-5.45c-1.72-.41-3.53.1-4.78 1.35L15.94 50.44l10.72 10.72c1.25 1.25 3.06 1.76 4.78 1.35l22.67-5.45c1.85-.45 3.3-1.89 3.75-3.75l.69-2.87-.69-2.86z" />
          <path d="M48.37 43.83c-1.85.45-3.3 1.89-3.75 3.75l-5.45 22.67c-.41 1.72.1 3.53 1.35 4.78l10.72 10.72 10.72-10.72c1.25-1.25 1.76-3.06 1.35-4.78l-5.45-22.67c-.45-1.85-1.89-3.3-3.75-3.75l-2.87-.69-2.87.69z" />
          <path d="M44.62 53.32c.45 1.85 1.89 3.3 3.75 3.75l22.67 5.45c1.72.41 3.53-.1 4.78-1.35l10.72-10.72-10.72-10.73c-1.25-1.25-3.06-1.76-4.78-1.35l-22.67 5.45c-1.85.45-3.3 1.89-3.75 3.75l-.69 2.87.69 2.88z" />
          <path d="M54.11 57.06c1.85-.45 3.3-1.89 3.75-3.75l5.45-22.67c.41-1.72-.1-3.53-1.35-4.78L51.24 15.15 40.52 25.87c-1.25 1.25-1.76 3.06-1.35 4.78l5.45 22.67c.45 1.85 1.89 3.3 3.75 3.75l2.87.69 2.87-.7z" />
          <path
            d="M44.62 53.32c.45 1.85 1.89 3.3 3.75 3.75l2.87.69 2.87-.69c1.85-.45 3.3-1.89 3.75-3.75l3-12.49-12.49 3c-1.85.45-3.3 1.89-3.75 3.75l-.69 2.87.69 2.87z"
            fill="#3cae49"
          />
          <path d="M54.11 57.06c1.85-.45 3.3-1.89 3.75-3.75l.69-2.87-.69-2.87c-.45-1.85-1.89-3.3-3.75-3.75l-12.49-3 3 12.49c.45 1.85 1.89 3.3 3.75 3.75l2.87.69 2.87-.69z" />
          <path d="M48.37 43.83c-1.85.45-3.3 1.89-3.75 3.75l-.69 2.87.69 2.87c.45 1.85 1.89 3.3 3.75 3.75l12.49 3-3-12.49c-.45-1.85-1.89-3.3-3.75-3.75l-2.87-.69-2.87.69z" />
          <path d="M57.86 47.58c-.45-1.85-1.89-3.3-3.75-3.75l-2.87-.69-2.87.69c-1.85.45-3.3 1.89-3.75 3.75l-3 12.49 12.49-3c1.85-.45 3.3-1.89 3.75-3.75l.69-2.87-.69-2.87z" />
          <path
            d="M57.86 47.58c-.45-1.85-1.89-3.3-3.75-3.75l-2.87-.69-2.87.69c-1.85.45-3.3 1.89-3.75 3.75l-.69 2.87.69 2.87c.45 1.85 1.89 3.3 3.75 3.75l2.87.69 2.87-.69c1.86-.45 3.3-1.89 3.75-3.75l.69-2.87-.69-2.87z"
            fill="#0b763c"
          />
          <path d="M507.04 40.81h-1.02v-.52h2.61v.52h-1.02v2.7h-.57zM509.06 40.29h.6l.98 1.52.98-1.52h.6v3.22h-.56V41.2l-1.02 1.52h-.02l-1-1.51v2.3h-.56zM126.43 32.7c0-1.82 1.52-2.98 3.48-2.98 1.97 0 3.48 1.16 3.48 2.98v.3c0 1.82-1.51 3.03-3.48 3.03s-3.48-1.21-3.48-3.03v-.3z" />
          <path d="M126.88 42.65c0-1.72 1.31-3.08 3.03-3.08 1.72 0 3.08 1.36 3.08 3.08v21.12c0 1.72-1.36 3.03-3.08 3.03-1.67 0-3.03-1.31-3.03-3.03V42.65zM139.01 42.65c0-1.72 1.31-3.08 3.03-3.08 1.72 0 3.08 1.36 3.08 3.08v1.31c1.72-2.48 4.19-4.65 8.33-4.65 6.01 0 9.5 4.04 9.5 10.2v14.25c0 1.72-1.31 3.03-3.03 3.03-1.72 0-3.08-1.31-3.08-3.03V51.39c0-4.14-2.07-6.52-5.71-6.52-3.54 0-6.01 2.48-6.01 6.62v12.27c0 1.72-1.36 3.03-3.08 3.03-1.67 0-3.03-1.31-3.03-3.03V42.65zM178.53 67.05h-.3c-1.67 0-2.78-1.06-3.49-2.68l-8.89-20.36c-.15-.45-.35-.96-.35-1.52 0-1.52 1.36-2.93 3.03-2.93 1.67 0 2.53.96 3.03 2.22l6.87 17.68 6.97-17.78c.46-1.06 1.26-2.12 2.88-2.12 1.67 0 2.98 1.26 2.98 2.93 0 .56-.2 1.16-.35 1.46l-8.89 20.41c-.71 1.58-1.82 2.69-3.49 2.69" />
          <path d="M194.24 32.7c0-1.82 1.51-2.98 3.48-2.98s3.48 1.16 3.48 2.98v.3c0 1.82-1.52 3.03-3.48 3.03-1.97 0-3.48-1.21-3.48-3.03v-.3z" />
          <path d="M194.7 42.65c0-1.72 1.31-3.08 3.03-3.08 1.72 0 3.08 1.36 3.08 3.08v21.12c0 1.72-1.36 3.03-3.08 3.03-1.67 0-3.03-1.31-3.03-3.03V42.65zM207.81 59.17V45.12h-1.01c-1.46 0-2.63-1.16-2.63-2.63 0-1.46 1.16-2.63 2.63-2.63h1.01v-4.55c0-1.67 1.36-3.03 3.08-3.03 1.67 0 3.03 1.36 3.03 3.03v4.55h4.8c1.46 0 2.68 1.16 2.68 2.63s-1.21 2.63-2.68 2.63h-4.8V58.2c0 2.37 1.21 3.33 3.28 3.33.71 0 1.31-.15 1.52-.15 1.36 0 2.57 1.11 2.57 2.53 0 1.11-.76 2.02-1.62 2.37-1.31.46-2.58.71-4.19.71-4.49.01-7.67-1.96-7.67-7.82M223.42 58.86v-.1c0-5.76 4.5-8.59 11.01-8.59 2.98 0 5.1.46 7.17 1.11v-.66c0-3.79-2.32-5.81-6.62-5.81-2.32 0-4.24.4-5.91 1.06-.35.1-.66.15-.96.15-1.41 0-2.58-1.11-2.58-2.53 0-1.11.76-2.07 1.67-2.42 2.53-.96 5.1-1.57 8.59-1.57 3.99 0 6.97 1.06 8.84 2.98 1.97 1.92 2.88 4.75 2.88 8.23v13.13c0 1.67-1.31 2.93-2.98 2.93-1.77 0-2.98-1.21-2.98-2.58v-1.01c-1.82 2.17-4.6 3.89-8.69 3.89-4.99.03-9.44-2.85-9.44-8.21m18.29-1.92v-1.82c-1.57-.61-3.64-1.06-6.06-1.06-3.94 0-6.26 1.67-6.26 4.45v.1c0 2.58 2.27 4.04 5.2 4.04 4.04 0 7.12-2.32 7.12-5.71M253.9 59.17V45.12h-1.01c-1.47 0-2.63-1.16-2.63-2.63 0-1.46 1.16-2.63 2.63-2.63h1.01v-4.55c0-1.67 1.36-3.03 3.08-3.03 1.67 0 3.03 1.36 3.03 3.03v4.55h4.8c1.46 0 2.68 1.16 2.68 2.63s-1.21 2.63-2.68 2.63h-4.8V58.2c0 2.37 1.21 3.33 3.28 3.33.71 0 1.31-.15 1.52-.15 1.36 0 2.58 1.11 2.58 2.53 0 1.11-.76 2.02-1.62 2.37-1.31.46-2.58.71-4.19.71-4.49.01-7.68-1.96-7.68-7.82" />
          <path d="M270.61 32.7c0-1.82 1.52-2.98 3.49-2.98s3.48 1.16 3.48 2.98v.3c0 1.82-1.51 3.03-3.48 3.03s-3.49-1.21-3.49-3.03v-.3z" />
          <path d="M271.06 42.65c0-1.72 1.31-3.08 3.03-3.08 1.72 0 3.08 1.36 3.08 3.08v21.12c0 1.72-1.36 3.03-3.08 3.03-1.67 0-3.03-1.31-3.03-3.03V42.65zM281.59 53.36v-.1c0-7.63 6.06-13.94 14.25-13.94 8.18 0 14.19 6.21 14.19 13.84v.1c0 7.58-6.06 13.89-14.29 13.89-8.14 0-14.15-6.22-14.15-13.79m22.33 0v-.1c0-4.7-3.38-8.59-8.18-8.59-4.9 0-8.03 3.84-8.03 8.49v.1c0 4.65 3.38 8.54 8.13 8.54 4.95-.01 8.08-3.85 8.08-8.44M314.49 42.65c0-1.72 1.31-3.08 3.03-3.08 1.72 0 3.08 1.36 3.08 3.08v1.31c1.72-2.48 4.19-4.65 8.33-4.65 6.01 0 9.5 4.04 9.5 10.2v14.25c0 1.72-1.31 3.03-3.03 3.03-1.72 0-3.08-1.31-3.08-3.03V51.39c0-4.14-2.07-6.52-5.71-6.52-3.54 0-6.01 2.48-6.01 6.62v12.27c0 1.72-1.36 3.03-3.08 3.03-1.67 0-3.03-1.31-3.03-3.03V42.65z" />
          <path d="M349.27 32.46c0-1.67 1.31-3.03 3.03-3.03 1.72 0 3.08 1.36 3.08 3.03v11.51c1.72-2.47 4.19-4.65 8.33-4.65 6.01 0 9.49 4.04 9.49 10.2v14.24c0 1.72-1.31 3.03-3.03 3.03-1.72 0-3.08-1.31-3.08-3.03V51.39c0-4.14-2.07-6.51-5.7-6.51-3.53 0-6.01 2.47-6.01 6.61v12.27c0 1.72-1.36 3.03-3.08 3.03-1.67 0-3.03-1.31-3.03-3.03v-31.3zM377.38 53.36v-.1c0-7.62 6.06-13.93 14.24-13.93s14.19 6.21 14.19 13.83v.1c0 7.57-6.06 13.88-14.29 13.88-8.13 0-14.14-6.21-14.14-13.78m22.31 0v-.1c0-4.7-3.38-8.58-8.18-8.58-4.9 0-8.03 3.84-8.03 8.48v.1c0 4.64 3.38 8.53 8.13 8.53 4.95 0 8.08-3.84 8.08-8.43M410.36 42.66c0-1.72 1.31-3.08 3.03-3.08 1.72 0 3.08 1.36 3.08 3.08v1.26c1.72-2.37 4.04-4.59 8.13-4.59 3.84 0 6.56 1.87 7.98 4.7 2.12-2.83 4.95-4.7 8.99-4.7 5.81 0 9.34 3.69 9.34 10.2v14.24c0 1.72-1.31 3.03-3.03 3.03-1.72 0-3.08-1.31-3.08-3.03V51.39c0-4.24-1.97-6.51-5.4-6.51-3.33 0-5.7 2.32-5.7 6.61v12.27c0 1.72-1.36 3.03-3.03 3.03-1.72 0-3.08-1.31-3.08-3.03V51.34c0-4.14-2.02-6.46-5.4-6.46-3.38 0-5.7 2.52-5.7 6.61v12.27c0 1.72-1.36 3.03-3.08 3.03-1.67 0-3.03-1.31-3.03-3.03v-21.1zM468.98 67.14c-7.72 0-13.68-5.6-13.68-13.83v-.1c0-7.62 5.4-13.88 13.02-13.88 8.48 0 12.67 6.97 12.67 13.13 0 1.72-1.31 2.93-2.88 2.93H461.4c.66 4.34 3.74 6.76 7.67 6.76 2.57 0 4.59-.91 6.31-2.32.45-.35.86-.56 1.56-.56 1.36 0 2.42 1.06 2.42 2.47 0 .76-.35 1.41-.81 1.87-2.4 2.17-5.43 3.53-9.57 3.53m6.06-15.8c-.4-3.94-2.73-7.02-6.76-7.02-3.74 0-6.36 2.88-6.92 7.02h13.68zM484.45 63.86c-.66-.35-1.16-1.11-1.16-2.12 0-1.36 1.06-2.47 2.47-2.47.5 0 .96.15 1.31.35 2.58 1.72 5.25 2.57 7.67 2.57 2.62 0 4.14-1.11 4.14-2.88v-.1c0-2.07-2.83-2.78-5.96-3.73-3.94-1.11-8.33-2.73-8.33-7.83v-.1c0-5.05 4.19-8.13 9.49-8.13 2.83 0 5.81.81 8.38 2.17.86.45 1.46 1.26 1.46 2.32 0 1.41-1.11 2.47-2.52 2.47-.5 0-.81-.1-1.21-.3-2.17-1.11-4.39-1.82-6.26-1.82-2.37 0-3.74 1.11-3.74 2.63V47c0 1.97 2.88 2.78 6.01 3.79 3.89 1.21 8.28 2.98 8.28 7.77v.1c0 5.6-4.34 8.38-9.9 8.38-3.36 0-7.05-1.06-10.13-3.18" />
        </svg>
      );
    } else if (userGroup.includes('MMG')) {
      userInfo.clientName = 'MMG';
      userInfo.shortName = 'MMG';
      userInfo.png = '/images/logo/MMG-logo.png';
      userInfo.flex = '350px';
      userInfo.svg = (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 144 69.32">
          <defs>
            <style>{`.cls-1{fill:#4e738a}.cls-2{fill:#74c3d5}`}</style>
          </defs>
          <g id="Layer_2" data-name="Layer 2">
            <g id="Layer_1-2" data-name="Layer 1">
              <path
                d="M102.45 0 69.38 33.54l-17.74-17.9h-.2v38.1l9.76-9.82v-6.63l2.52 2.19.92 1 5.81-5.84 32-32.49Z"
                class="cls-1"
              />
              <path
                d="M97.18 15.64 46.42 67.21v2.11L69.6 45.81c1.6 1.72 3.2 3.45 4.81 5.15 3.59-3.78 7.17-7.63 10.69-11.48l2.52-2.19v26.88h9.76V15.64Z"
                class="cls-2"
              />
              <path
                d="m12.28 39.47-2.52-2.19v26.89H0V15.64h.2L23 38.61l22.77-23h.2v42.12l-9.76 8.84V37.28l-2.52 2.19C30.14 43.33 26.56 47.18 23 51c-3.61-3.82-7.13-7.67-10.72-11.53Z"
                class="cls-1"
              />
              <path
                d="M127.73 64.74C112.6 64.74 102 53.91 102 39.9s10.69-24.83 25.5-24.83a35.41 35.41 0 0 1 15 3.25v9.43a33 33 0 0 0-14.34-3.32c-9.89 0-15.87 7-15.87 15.47s6 15.54 15.4 15.54a23.28 23.28 0 0 0 6.51-.93V41.76h9.8v19.32a37.5 37.5 0 0 1-16.27 3.66Z"
                class="cls-2"
              />
            </g>
          </g>
        </svg>
      );
    } else if (userGroup.includes('SecondAvenue')) {
      userInfo.clientName = 'SecondAvenue';
      userInfo.shortName = 'SecondAvenue';
      userInfo.png = '/images/logo/SecondAvenue_Logo.png';
      userInfo.flex = '450px';
      userInfo.svg = (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          xmlSpace="preserve"
          id="Layer_1"
          x="0"
          y="0"
          version="1.1"
          viewBox="0 0 1570 292"
        >
          <path
            d="M1517.64 30.1c8.48 0 15.36 6.88 15.36 15.36v201.56c0 8.48-6.88 15.36-15.36 15.36H51.42c-8.48 0-15.36-6.88-15.36-15.36V45.46c0-8.48 6.88-15.36 15.36-15.36h1466.22m0-15H51.42c-16.74 0-30.36 13.62-30.36 30.36v201.56c0 16.74 13.62 30.36 30.36 30.36h1466.22c16.74 0 30.36-13.62 30.36-30.36V45.46c0-16.74-13.62-30.36-30.36-30.36z"
            fill="#106dab"
          />
          <path
            d="M161.15 112.09c0-3.87-2.11-5.98-5.99-5.98h-25.35c-3.87 0-5.99 2.11-5.99 5.98v11.97c0 3.87 2.11 5.11 5.99 5.99l32.74 7.04c13.73 2.99 25.35 11.27 25.35 25.35v22.88c0 14.79-10.21 25-25 25h-39.43c-14.79 0-25-10.21-25-25v-16.2h24.64v11.27c0 3.87 2.11 5.99 5.99 5.99h27.46c3.87 0 5.99-2.11 5.99-5.99v-13.38c0-3.87-2.11-5.11-5.99-5.99l-32.74-7.04c-13.73-2.99-25.35-11.27-25.35-25.35v-21.48c0-14.79 10.21-25 25-25h37.32c14.79 0 25 10.21 25 25v14.79h-24.64v-9.85zM294.95 82.87v23.94h-57.57v26.05h46.65v23.94h-46.65v28.87h57.57v23.94h-82.92V82.87h82.92zM377.69 112.09c0-3.87-2.11-5.98-5.99-5.98h-27.46c-3.87 0-5.99 2.11-5.99 5.98v68.3c0 3.87 2.11 5.99 5.99 5.99h27.46c3.87 0 5.99-2.11 5.99-5.99V164.9h25.35v20.42c0 14.79-10.21 25-25 25H337.9c-14.79 0-25-10.21-25-25v-78.16c0-14.79 10.21-25 25-25h40.14c14.79 0 25 10.21 25 25v20.42h-25.35v-15.49zM490 82.16c14.79 0 25 10.21 25 25v78.16c0 14.79-10.21 25-25 25h-42.42c-14.79 0-25-10.21-25-25v-78.16c0-14.79 10.21-25 25-25H490zm-.35 29.93c0-3.87-2.11-5.98-5.99-5.98h-29.75c-3.87 0-5.99 2.11-5.99 5.98v68.3c0 3.87 2.11 5.99 5.99 5.99h29.75c3.87 0 5.99-2.11 5.99-5.99v-68.3zM637.53 82.87v126.75h-27.29l-46.65-86.44v86.44h-23.76V82.87h27.29l46.65 86.44V82.87h23.76zM731.36 82.87c14.79 0 25 10.21 25 25v76.75c0 14.79-10.21 25-25 25h-66.01V82.87h66.01zm-6.33 102.8c3.87 0 5.99-2.11 5.99-5.99v-66.9c0-3.87-2.11-5.99-5.99-5.99H690.7v78.87h34.33z"
            fill="#006cae"
          />
          <path
            d="M839.63 209.62H813.4l40.84-126.75h29.57l40.84 126.75h-27.29l-8.27-28.52h-41.02l-8.44 28.52zm15.5-52.46h27.11l-13.38-46.47-13.73 46.47zM986.81 209.62h-29.57L918.68 82.87h28.87l24.47 96.29 25.17-96.29h28.17l-38.55 126.75zM1125.35 82.87v23.94h-57.57v26.05h46.65v23.94h-46.65v28.87h57.57v23.94h-82.92V82.87h82.92zM1245.42 82.87v126.75h-27.29l-46.65-86.44v86.44h-23.76V82.87H1175l46.65 86.44V82.87h23.77zM1361.78 185.32c0 14.79-10.21 25-25 25h-40.49c-14.79 0-25-10.21-25-25V82.87h25.35v97.53c0 3.87 2.11 5.99 5.99 5.99h27.81c3.87 0 5.99-2.11 5.99-5.99V82.87h25.35v102.45zM1470.58 82.87v23.94h-57.57v26.05h46.65v23.94h-46.65v28.87h57.57v23.94h-82.92V82.87h82.92z"
            fill="#838585"
          />
        </svg>
      );
    } else if (userGroup.includes('SmithDouglas')) {
      userInfo.clientName = 'SmithDouglas';
      userInfo.shortName = 'SmithDouglas';
      userInfo.png = '/images/logo/SmithDouglas-logo.png';
      userInfo.flex = '350px';
    } else if (userGroup.includes('Tricon')) {
      userInfo.clientName = 'Tricon';
      userInfo.shortName = 'Tricon';
      userInfo.png = '/images/logo/Tricon_logo.png';
      userInfo.flex = '350px';
      userInfo.svg = (
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 136 48">
          <path
            fill="#002357"
            d="M5.33 9.5 0 12.24v18.28h5.33zm9.22-4.75L9.22 7.49v23h5.33zm3.88-2v27.77h5.33V0zm10.79 16.19h6.59v11.59h4V18.94h6.59v-3.68H29.22zm36.85 11.58v-4.45a3.48 3.48 0 0 0-.83-2.41 3.52 3.52 0 0 0 1-2.54v-1.06c0-2.89-.93-4.81-5.48-4.81H49.21v15.27h4v-5.15h7.49c1.42 0 1.46.12 1.46 1.17v4zm-5.51-8.83h-7.33v-2.75h6.9c2.1 0 2.1.22 2.1.8v1.19c0 .42 0 .76-1.68.76zm9.19-6.4h4.01v15.23h-4.01zM114.5 21c0-2.58-.6-6-5.76-6h-7c-5.17 0-5.77 3.42-5.77 6v3.56c0 2.58.6 6 5.77 6h7c5.16 0 5.76-3.43 5.76-6zm-4 4.23c0 1.13-.28 1.64-1.93 1.64h-6.67c-1.66 0-1.94-.51-1.94-1.64v-4.9c0-1.13.28-1.64 1.94-1.64h6.67c1.65 0 1.93.51 1.93 1.64zM132 15.25v9.93l-10.35-9.93h-4.2v15.27h4V20.6l10.35 9.92h4.2V15.25zM82.82 30.52h10.37v-3.69H83c-1.65 0-1.93-.51-1.93-1.64v-4.9c0-1.13.28-1.64 1.93-1.64h10.19V15H82.82c-5.16 0-5.76 3.42-5.76 6v3.56c0 2.53.59 5.96 5.76 5.96zM131.88 38l-2.07 1v8.8H136V46h-4.11zm-13.77.09-3.76 9.74h2.28l.61-1.68h4.35l.61 1.68h2.27l-3.76-9.74zm-.32 6.26 1.62-4.47 1.59 4.47zm-11.2 3.48h2.08V38l-2.08 1zm-13.4-7.92H96v7.91h2.08v-7.91H101v-1.82h-7.81zm-8.32 4.48-4.51-6.3h-2.13v9.73h2.08v-6.54L85 47.82h2v-9.73h-2.12zm-20.21 3.43h6.89V46h-4.82v-2.24h4.71V42h-4.71v-2.13h4.82v-1.78h-6.9zm-10.74-9.73h-3.84v9.74h3.84c3.06 0 5.17-1.93 5.17-4.87s-2.09-4.87-5.17-4.87zm0 7.91h-1.76v-6.09h1.76A2.86 2.86 0 0 1 57 42.54 3.29 3.29 0 0 1 54.19 46zM40.9 47.83H43V38l-2.1 1zM31 41.87c-1.12-.26-1.94-.47-1.94-1.13s.5-1 1.43-1a4.16 4.16 0 0 1 2.79 1.07l1.15-1.52a5.4 5.4 0 0 0-3.8-1.36c-2.29 0-3.69 1.31-3.69 2.94 0 2.17 2.05 2.61 3.64 3 1.12.26 2 .51 2 1.24 0 .55-.56 1.09-1.7 1.09a4.26 4.26 0 0 1-3.1-1.31l-1.13 1.57A5.59 5.59 0 0 0 30.76 48c2.63 0 3.9-1.34 3.9-3.12 0-2.17-2.04-2.65-3.66-3.01zm-16.91 5.95H21V46h-4.83v-2.24h4.71V42h-4.71v-2.13H21v-1.78h-6.9zm-6.26-6.59a3 3 0 0 0-2.92-3.15 2 2 0 0 0-.35 0H0v9.74h2.08v-3.48h1.51l1.92 3.49h2.38L5.7 44.12a2.81 2.81 0 0 0 2.13-2.89zm-3.58 1.32H2.08v-2.68h2.17a1.34 1.34 0 1 1 .22 2.68.81.81 0 0 1-.22 0z"
          />
        </svg>
      );
    } else if (userGroup.includes('Rithm')) {
      userInfo.clientName = 'Rithm';
      userInfo.shortName = 'Rithm';
      userInfo.png = '/images/logo/Rithm_logo.png';
      userInfo.flex = '350px';
      userInfo.svg = (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          aria-label="Rithm Logo"
          viewBox="0 0 85 29"
        >
          <path
            fill="#332e4d"
            d="M11.292 10.842H7.668c-2.988 0-4.382 2.546-4.382 6.474V29H0V7.937h2.768l.339 3.567c.577-2.205 2.15-3.567 4.381-3.567h3.366zm2.33-8.637c0-1.323.995-2.205 2.31-2.205 1.314 0 2.27.882 2.27 2.205 0 1.322-.956 2.244-2.27 2.244-1.315 0-2.31-.922-2.31-2.244zM17.565 29h-3.286V7.936h3.286zm14.697-2.906L31.824 29H28.14c-2.59 0-4.302-1.683-4.302-4.309V10.823h-3.585V7.917h3.585v-5.25l3.286-1.584v6.834h4.72v2.906h-4.72v13.428c0 1.102.737 1.843 1.792 1.843zM51.8 15.331V29h-3.285V15.953c0-3.387-2.012-5.431-5.159-5.431-3.365 0-5.377 2.905-5.377 6.613V29h-3.266V1.504h3.286v10.301c1.135-2.766 3.426-4.309 6.274-4.309 4.54 0 7.528 2.986 7.528 7.836zm33.2 0V29h-3.286V15.953c0-3.387-1.872-5.472-4.939-5.472-3.286 0-5.298 2.946-5.298 6.654v11.864h-3.246V15.953c0-3.387-1.892-5.472-4.959-5.472-3.286 0-5.258 2.946-5.258 6.654v11.864h-3.286V7.936h2.768l.399 4.229c1.055-3.006 3.326-4.67 6.273-4.67 3.406 0 5.915 1.764 6.871 4.81 1.235-3.207 3.625-4.81 6.612-4.81 4.461 0 7.35 2.986 7.35 7.836z"
          />
        </svg>
      );
    } else if (userGroup.includes('Nhimble')) {
      userInfo.clientName = 'Nhimble';
      userInfo.shortName = 'Nhimble';
      userInfo.png = '/images/logo/Nhimble_logo.png';
      userInfo.flex = '350px';
    } else if (userGroup.includes('TrellyGroup')) {
      userInfo.clientName = 'TrellyGroup';
      userInfo.shortName = 'Trelly';
      userInfo.png = '/images/logo/Trelly_Logo_RGB-Pos.webp';
      userInfo.flex = '350px';
    } else if (userGroup.includes('HON')) {
      userInfo.clientName = 'HON';
      userInfo.shortName = 'HON';
      userInfo.png = '/images/logo/HONP-Final-Final.png';
      userInfo.flex = '450px';
    } else if (userGroup.includes('VentureREI')) {
      userInfo.clientName = 'VentureREI';
      userInfo.shortName = 'VentureREI';
      userInfo.png = '/images/logo/VentureREI-Logo.png';
      userInfo.flex = '350px';
    } else if (userGroup.includes('BlueRiver')) {
      userInfo.clientName = 'BlueRiver';
      userInfo.shortName = 'BlueRiver';
      userInfo.png = '/images/logo/BlueRiver-Logo.png';
      userInfo.flex = '350px';
    } else if (userGroup.includes('UrbanRowGroup')) {
      userInfo.clientName = 'UrbanRowGroup';
      userInfo.shortName = 'URG';
      userInfo.png = '/images/logo/UrbanRowGroup_logo.png';
      userInfo.flex = '350px';
    } else if (userGroup.includes('Vertica')) {
      userInfo.clientName = 'Vertica';
      userInfo.shortName = 'Vertica';
      userInfo.png = '/images/logo/VerticaPartners-Logo.png';
      userInfo.flex = '350px';
    } else if (userGroup.includes('JCGLand')) {
      userInfo.clientName = 'JCGLand';
      userInfo.shortName = 'JCGLand';
      userInfo.png = '/images/logo/Crawford_JCGLand-logo.png';
      userInfo.flex = '350px';
    } else if (userGroup.includes('Kairos')) {
      userInfo.clientName = 'Kairos';
      userInfo.shortName = 'Kairos';
      userInfo.png = '/images/logo/kairos_living_logo.png';
      userInfo.flex = '450px';
    } else if (userGroup.includes('Evergreen')) {
      userInfo.clientName = 'Evergreen';
      userInfo.shortName = 'Evergreen';
      userInfo.png = '/images/logo/Evergreen_logo.png';
      userInfo.flex = '350px';
    } else if (userGroup.includes('UpAndUp')) {
      userInfo.clientName = 'UpAndUp';
      userInfo.shortName = 'UpAndUp';
      userInfo.png = '/images/logo/UpAndUp_logo.png';
      userInfo.flex = '350px';
    } else if (userGroup.includes('Greystar')) {
      userInfo.clientName = 'Greystar';
      userInfo.shortName = 'Greystar';
      userInfo.png = '/images/logo/Greystar_logo.png';
      userInfo.flex = '350px';
    } else if (userGroup.includes('GACapital')) {
      userInfo.clientName = 'GACapital';
      userInfo.shortName = 'GACapital';
      userInfo.png = '/images/logo/GACapital_logo.png';
      userInfo.flex = '450px';
    } else if (userGroup.includes('HunterQuinn')) {
      userInfo.clientName = 'HunterQuinn';
      userInfo.shortName = 'HunterQuinn';
      userInfo.png = '/images/logo/HunterQuinn_logo.png';
      userInfo.flex = '350px';
    } else if (userGroup.includes('BeaconRidge')) {
      userInfo.clientName = 'BeaconRidge';
      userInfo.shortName = 'BeaconRidge';
      userInfo.png = '/images/logo/BeaconRidge_logo.png';
      userInfo.flex = '350px';
    } else if (userGroup.includes('REMAXFine')) {
      userInfo.clientName = 'AveOne';
      userInfo.shortName = 'AveOne';
      userInfo.png = '/images/logo/Avenue_One_logo.png';
      userInfo.flex = '350px';
    } else if (userGroup.includes('WebCity')) {
      userInfo.clientName = 'WebCity';
      userInfo.shortName = 'WebCity';
      userInfo.png = '/images/logo/webcity_logo.png';
      userInfo.flex = '350px';
    } else if (userGroup.includes('Heyday')) {
      userInfo.clientName = 'Heyday';
      userInfo.shortName = 'Heyday';
      userInfo.png = '/images/logo/Heyday-logo.png';
      userInfo.flex = '350px';
    } else {
      userInfo.clientName = 'RealCo';
      userInfo.shortName = 'RC';
      userInfo.png = '/images/logo/LocateAlpha_logotype_and_wordmark.png';
      // userInfo.png = '/images/logo/RealCo-Logo.png';
      userInfo.flex = '350px';
      // userInfo.svg = (
      //   <svg
      //     key="logo"
      //     viewBox="0 0 512 512"
      //     xmlns="http://www.w3.org/2000/svg"
      //   >
      //     <g fill="#164685">
      //       <path d="M256.77 44.59c116.85 0 210.64 95.328 210.64 212.18 0 116.85-93.789 210.64-210.64 210.64-116.85 0-212.18-93.789-212.18-210.64 0-116.85 95.328-212.18 212.18-212.18zm0 30.75c-99.941 0-181.43 81.488-181.43 181.43 0 99.941 81.488 179.89 181.43 179.89 99.941 0 179.89-79.953 179.89-179.89 0-99.941-79.953-181.43-179.89-181.43z" />
      //       <path
      //         d="m359.79 158.37-61.5 121.46c-3.0742 6.1484-15.375 16.914-26.137 23.062l-104.55 52.277c-10.762 6.1484-18.449 4.6133-13.836-1.5391l59.965-121.46c4.6133-6.1484 15.375-16.914 27.676-21.527l103.02-53.812c12.301-4.6133 18.449-4.6133 15.375 1.5391zm-103.02 72.266c13.836 0 26.137 10.762 26.137 26.137 0 13.836-12.301 26.137-26.137 26.137-15.375 0-26.137-12.301-26.137-26.137 0-15.375 10.762-26.137 26.137-26.137z"
      //         fillRule="evenodd"
      //       />
      //     </g>
      //   </svg>
      // );
    }

    return userInfo;
  }
};
