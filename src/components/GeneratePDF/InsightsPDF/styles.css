.pdfButton,
.pdfButton button {
  all: unset;
  cursor: pointer;
  display: flex;
  flex-direction: row;
  gap: 3px;
  display: inline;
}

/* for parcel owner - temp until it is packaged */
.cardWrapper {
  position: relative;
  width: 100%;
  /* max-height: 50vh; */
  background-color: #fff;
  padding: 24px 32px;
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 0 4px rgba(19, 16, 204, 0.1);
  /* overflow-x: auto; */
}
.cardTitleH2 {
  font-size: 16px;
  font-weight: 500;
}
.cardSubtitle {
  font-size: 14px;
  font-weight: 400;
}
.cardDataValue {
  font-size: 14px;
  font-weight: 500;
}
.dividerCardHeader {
  width: 100%;
  height: 1px;
  background-color: rgba(0, 0, 0, 0.1);
  margin-top: 12px;
}
