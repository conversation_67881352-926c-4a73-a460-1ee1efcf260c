import polyline from '@mapbox/polyline';
import { default as turf_centerOfMass } from '@turf/center-of-mass';
import { default as turf_circle } from '@turf/circle';
import { multiPolygon } from '@turf/helpers';
import isEqual from 'lodash.isequal';
import { createContext, useEffect, useRef, useState } from 'react';
import {
  getGeocodingData,
  getMLSListingSummaryPointWithinLayerData,
  getParcelOwnerSummary,
  getParcelOwnerSummaryWithinPolygon,
  MLSListingSummaryChartCBSAData,
  MLSListingSummaryChartCountyData,
  MLSListingSummaryChartMultipolygonData,
  MLSListingSummaryChartSchoolDistrictData,
  MLSListingSummaryChartSphereData,
  MLSListingSummaryChartZIPCodeData,
} from '../utils/api';
import {
  defaultMarketConditionDates,
  MAPBOX_TOKEN,
  processMarketConditionResponse,
  processMLSListingSummaryChartResponse,
} from '../utils/utils';

const InsightsContext = createContext();

function usePrevious(value) {
  const ref = useRef();
  useEffect(() => {
    ref.current = value;
  });
  return ref.current;
}

export const InsightsProvider = ({ children }) => {
  const [dataFetchingLoading, setDataFetchingLoading] = useState(false);
  const [customPolygonMode, setCustomPolygonMode] = useState(false);
  const [inputData, setInputData] = useState({
    marketConditionDates: defaultMarketConditionDates,
  });
  const [selectedItems, setSelectedItems] = useState([
    'parcelOwnerSummary',
    'marketCondition',
    'marketConditionHistory-priceFull',
    'marketConditionHistory-pricePerSqft',
    'marketConditionHistory-ListedClosed',
    'marketConditionHistory-DOM',
  ]);
  const [insightsDataURL, setInsightsDataURL] = useState({});
  const [startGeneratingPDF, setStartGeneratingPDF] = useState(false);

  const [subjectData, setSubjectData] = useState({});
  const [currentParcelOwnerSummary, setCurrentParcelOwnerSummary] = useState(
    {},
  );
  const [currentMarketConditionHistory, setCurrentMarketConditionHistory] =
    useState({});

  const [marketConditionHistoryLoading, setMarketConditionHistoryLoading] =
    useState(false);

  const inputDataRef = usePrevious(inputData);

  useEffect(() => {
    const { chartArea, ...input } = inputData;
    let prevChartArea;
    let prevInput = {};
    if (inputDataRef) {
      let { chartArea: prevChartArea, ...prevIn } = inputDataRef;
      prevInput = { ...prevIn };
    }
    if (!isEqual(input, prevInput)) {
      setSubjectData({});
      setCurrentParcelOwnerSummary({});
      setCurrentMarketConditionHistory({});
    } else if (chartArea !== prevChartArea) {
      // const fetchMarketHistoryData = async () => {
      //   setMarketConditionHistoryLoading(true);
      //   await getMarketConditionHistory();
      //   setMarketConditionHistoryLoading(false);
      // };
      // fetchMarketHistoryData();
    }
  }, [inputData]);

  // useEffect(() => {
  //   console.log('HERE');
  //   const fetchMarketHistoryData = async () => {
  //     setMarketConditionHistoryLoading(true);
  //     await getMarketConditionHistory();
  //     setMarketConditionHistoryLoading(false);
  //   };
  //   // fetchMarketHistoryData();
  // }, [inputData.chartArea]);

  const getSubjectData = async () => {
    const { lat, lng, radius, customPolygon } = inputData;
    const imgWidth = 350;
    const imgHeight = 300;
    const SCREENSHOT_ZOOM = 19;

    let subject = {};
    let subjectImage = {};
    let dataLayer = [];
    if (!customPolygonMode && lat && lng && radius) {
      const geocodeResponse = await getGeocodingData({ lat, lng });

      const circle = turf_circle([lng, lat], radius, {
        units: 'miles',
      });
      circle.properties.fill = '#c4edff';
      // circle.properties.opacity = '#c4edff';

      // console.log('circle: ', circle);

      const marker = {
        type: 'Feature',
        properties: {
          // 'marker-size': 's',
          // 'marker-symbol': 'airport',
          'marker-color': '#e200ff',
        },
        geometry: {
          type: 'Point',
          coordinates: [lng, lat],
        },
      };

      // function getStarGeoJSON(lat, lng, radius) {
      //   const points = [];

      //   // radius of the star in miles
      //   radius = radius * 0.125; // reduce size of star

      //   for (let i = 0; i < 5; i++) {
      //     const angle1 = ((i * 72 - 90 + 18) * Math.PI) / 180;
      //     const angle2 = ((i * 72 - 54 + 18) * Math.PI) / 180;
      //     const lat1 = lat + (radius / 69) * Math.cos(angle1);
      //     const lng1 =
      //       lng +
      //       (radius / (69 * Math.cos((lat * Math.PI) / 180))) *
      //         Math.sin(angle1);
      //     const lat2 = lat + (radius / 69 / 2) * Math.cos(angle2);
      //     const lng2 =
      //       lng +
      //       (radius / (69 * Math.cos((lat * Math.PI) / 180)) / 2) *
      //         Math.sin(angle2);
      //     points.push([lng1, lat1], [lng2, lat2]);
      //   }

      //   points.push(points[0]);

      //   const feature = {
      //     type: 'Feature',
      //     properties: {
      //       fill: '#e200ff',
      //       'fill-opacity': 1,
      //       stroke: '#fff',
      //       'stroke-width': ,
      //     },
      //     geometry: {
      //       type: 'Polygon',
      //       coordinates: [[...points]],
      //     },
      //   };

      //   return feature;
      // }

      // const marker = getStarGeoJSON(lat, lng, radius);

      const geojson = {
        type: 'FeatureCollection',
        features: [circle, marker],
      };

      subjectImage = {
        src: `https://api.mapbox.com/styles/v1/mapbox/satellite-v9/static/geojson(${encodeURIComponent(
          JSON.stringify(geojson),
        )})/auto/${imgWidth}x${imgHeight}?access_token=${MAPBOX_TOKEN}`,
        width: imgWidth,
        height: imgHeight,
      };

      dataLayer = await getMLSListingSummaryPointWithinLayerData({
        lat: lat,
        lng: lng,
      });

      subject.aoi = {
        type: 'point',
        placeName: geocodeResponse.features[0].place_name,
        lat: lat,
        lng: lng,
        radius: radius,
      };
    } else if (customPolygonMode) {
      const coordinates = [];
      for (let i = 0; i < customPolygon[0].length; i++) {
        // polyline.encode uses lat, lng
        const longitude = customPolygon[0][i][0];
        const latitude = customPolygon[0][i][1];
        coordinates.push([latitude, longitude]);
      }

      const pathPolyline = encodeURIComponent(polyline.encode(coordinates));
      const strokeWidth = 5;
      const strokeColor = 'fd0927';
      subjectImage = {
        src: `https://api.mapbox.com/styles/v1/mapbox/satellite-streets-v11/static/path-${strokeWidth}+${strokeColor}(${pathPolyline})/auto/${imgWidth}x${imgHeight}?before_layer=waterway-label&access_token=${MAPBOX_TOKEN}`,
        width: imgWidth,
        height: imgHeight,
      };

      const centerOfMass = turf_centerOfMass(multiPolygon([customPolygon]));
      dataLayer = await getMLSListingSummaryPointWithinLayerData({
        lat: centerOfMass.geometry.coordinates[1],
        lng: centerOfMass.geometry.coordinates[0],
      });

      subject.aoi = {
        type: 'polygon',
        placeName: 'Custom Polygon',
      };
    }

    subject.subjectImage = subjectImage;
    for (let i = 0; i < dataLayer.length; i++) {
      if (dataLayer[i].type === 'metro') {
        subject.metro = dataLayer[i].name;
      } else if (dataLayer[i].type === 'district') {
        subject.district = dataLayer[i].obj_name;
      } else if (dataLayer[i].type === 'zipcode') {
        subject.zipcode = dataLayer[i].key;
      }
    }

    console.log('subject: ', subject);
    setSubjectData(subject);
  };

  const getParcelOwner = async () => {
    try {
      let response;
      if (!customPolygonMode) {
        response = await getParcelOwnerSummary({
          lat: inputData.lat,
          lng: inputData.lng,
          distance: inputData.radius * 1609.34,
        });
      } else {
        response = await getParcelOwnerSummaryWithinPolygon({
          body: JSON.stringify(inputData.customPolygon),
        });
      }
      console.log('parcel owner response: ', response);
      if (response) {
        let extraCount = 0;
        if (response.institution_count.length > 0) {
          response.institution_count = response.institution_count.filter(
            function (obj) {
              if (obj.institution === 'Divvy') {
                extraCount += obj.count;
              }
              return obj.institution !== 'Divvy';
            },
          );
        }

        const objIndex = response.institution_count.findIndex(
          (obj) => obj.institution == 'Other',
        );
        if (objIndex > -1) {
          response.institution_count[objIndex].count += extraCount;
        }

        setCurrentParcelOwnerSummary(response);
      } else {
        setCurrentParcelOwnerSummary({});
      }
    } catch (err) {
      console.log(err);
      setCurrentParcelOwnerSummary({});
    }
  };

  const getMarketConditionHistory = async () => {
    const { chartArea, lat, lng, radius, isLeaseMode, customPolygon } =
      inputData;

    const propertyType = isLeaseMode ? 'Residential Lease' : 'Residential';

    if (!lat || !lng || !radius) return;

    try {
      let params = {
        lat: lat,
        lng: lng,
        distance: radius * 1609.34,
        propertyType: propertyType,
        body: JSON.stringify(customPolygon),
      };

      if (customPolygonMode) {
        const centerOfMass = turf_centerOfMass(multiPolygon([customPolygon]));
        params.lat = centerOfMass.geometry.coordinates[1];
        params.lng = centerOfMass.geometry.coordinates[0];
      }

      let response = null;
      if (chartArea === 'aoi') {
        if (!customPolygonMode) {
          response = await MLSListingSummaryChartSphereData(params);
        } else {
          response = await MLSListingSummaryChartMultipolygonData(params);
        }
      } else if (chartArea === 'zipcode') {
        response = await MLSListingSummaryChartZIPCodeData(params);
      } else if (chartArea === 'metro') {
        response = await MLSListingSummaryChartCBSAData(params);
      } else if (chartArea === 'district') {
        response = await MLSListingSummaryChartSchoolDistrictData(params);
      } else if (chartArea === 'county') {
        response = await MLSListingSummaryChartCountyData(params);
      }

      if (response) {
        response = processMLSListingSummaryChartResponse(
          response,
          propertyType,
        );
        console.log('graph response: ', response);
        setCurrentMarketConditionHistory(response);
      } else {
        setCurrentMarketConditionHistory({});
      }
    } catch (err) {
      console.log(err);
      setCurrentMarketConditionHistory({});
    }
  };

  const fetchInsightsData = async () => {
    setDataFetchingLoading(true);

    await getSubjectData();
    await getParcelOwner();
    // await getMarketConditionHistory();

    setInsightsDataURL({});
    setDataFetchingLoading(false);
  };

  return (
    <InsightsContext.Provider
      value={{
        inputData,
        setInputData,
        dataFetchingLoading,
        fetchInsightsData,
        setCustomPolygonMode,
        subjectData,
        currentParcelOwnerSummary,
        currentMarketConditionHistory,
        selectedItems,
        setSelectedItems,
        insightsDataURL,
        setInsightsDataURL,
        // generatePDF,
        startGeneratingPDF,
        setStartGeneratingPDF,
        marketConditionHistoryLoading,
      }}
    >
      {children}
    </InsightsContext.Provider>
  );
};

export default InsightsContext;
