import { useMarketCondition } from '@spatiallaser/market-condition';
import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';
import moment from 'moment';
import { createContext, useContext, useState } from 'react';
import { getClientInformation } from '../utils/userGroup';
import { dateFormat } from '../utils/utils';
import InsightsContext from './InsightsContext';

const PDFContext = createContext();

export let doc;
export let pdfPageWidth, pdfPageHeight;
const pdfMargin = 15;
const pdfGap = 15;
const pdfContentPadding = 10;
const topBottomMargin = 40;
let lastYAxisPos = 0;

export const PDFProvider = ({ source, combineState, children }) => {
  const {
    inputData,
    subjectData,
    currentParcelOwnerSummary,
    insightsDataURL,
    selectedItems,
  } = useContext(InsightsContext);
  const marketConditionCtx = useMarketCondition();

  const generatePDF = async () => {
    // console.log('subjectData', subjectData);
    // console.log('currentParcelOwnerSummary', currentParcelOwnerSummary);
    // console.log('currentMarketCondition', currentMarketCondition);
    // console.log('insightsDataURL', insightsDataURL);

    doc = new jsPDF('portrait', 'px', 'letter');
    pdfPageWidth = doc.internal.pageSize.getWidth();
    pdfPageHeight = doc.internal.pageSize.getHeight();

    console.log('pdfPageWidth', pdfPageWidth);
    console.log('pdfPageHeight', pdfPageHeight);

    await generateHeader();
    await generateSubject();
    if (selectedItems.includes('parcelOwnerSummary')) {
      await generateNearbyParcelOwnerSummary();
    }
    if (selectedItems.includes('marketCondition')) {
      await generateMarketConditions();
    }
    await generateMarketConditionsHistory();
    await generateFooter();

    if (source === 'combine') {
      const { generateQueue, setPdfArray, setGenerateQueue } = combineState;
      if (generateQueue && generateQueue === '2') {
        const pdfBuffer = doc.output('arraybuffer');
        setGenerateQueue('done');
        setPdfArray((prev) => [...prev, pdfBuffer]);
      }
    } else {
      doc.save(`Insights Report.pdf`);
    }
  };

  const processImage = (src) => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.src = src;
      img.onload = () => resolve(img);
      img.onerror = reject;
    });
  };

  const generateHeader = async () => {
    const clientInfo = getClientInformation(inputData.userGroup);

    if (clientInfo && clientInfo.clientName) {
      const img = await processImage(clientInfo.png);
      const headerEndY = 60;

      const maxLogoWidth = 150;
      const maxLogoHeight = headerEndY - pdfMargin;

      if (img.width > maxLogoWidth) {
        img.height = img.height * (maxLogoWidth / img.width);
        img.width = maxLogoWidth;
      }

      if (img.height > maxLogoHeight) {
        img.width = img.width * (maxLogoHeight / img.height);
        img.height = maxLogoHeight;
      }

      let yPos = headerEndY - img.height;

      // prettier-ignore
      doc.addImage(img,'png',pdfMargin,yPos,img.width,img.height,'','FAST');

      // Adding the report title
      doc.setFontSize(16);
      doc.setFont('helvetica', 'bold');
      doc.text('Insights Report', pdfPageWidth / 2, headerEndY, 'center');

      // date
      const today = new Date();
      const dd = String(today.getDate()).padStart(2, '0');
      const mm = String(today.getMonth() + 1).padStart(2, '0'); //January is 0!
      const yyyy = today.getFullYear();
      const dateFormatted = mm + '/' + dd + '/' + yyyy;

      doc.setFontSize(12);
      doc.setFont('helvetica', 'normal');
      const lineHeight = 10.5;
      const generatedText = inputData.userGroup.includes('Sunroom')
        ? `Generated on\n${dateFormatted}`
        : `Generated on\n${dateFormatted}\nby Spatial Laser`;
      const splitText = generatedText.split(`\n`);
      const longest = splitText.reduce((a, b) => (a.length > b.length ? a : b));
      const textWidth = doc.getTextWidth(longest);

      doc.text(
        generatedText,
        pdfPageWidth - textWidth / 2 - 15,
        headerEndY - lineHeight * 2, // 2 new lines
        'center',
      );
    }
  };

  const generateSubject = async () => {
    const yStartPos = 60 + pdfGap;
    const xStartPos = pdfMargin;

    const img = new Image();
    img.src = subjectData.subjectImage.src;
    img.width = 143;
    img.height =
      subjectData.subjectImage.height / (subjectData.subjectImage.width / 143);
    let imgXpos = pdfMargin + pdfContentPadding;
    let imgYpos = yStartPos + pdfContentPadding;
    // prettier-ignore
    doc.addImage(img,'png',imgXpos,imgYpos,img.width,img.height,'','FAST');

    // subject text details
    let xPos = xStartPos + pdfContentPadding * 2 + img.width;
    let yPos = yStartPos + pdfContentPadding;
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    let text = 'Place:';
    yPos = yPos + doc.getTextDimensions(text).h;
    doc.text(text, xPos, yPos);

    xPos = xPos + doc.getTextDimensions(text).w + 5;
    let maxTextLength = pdfPageWidth - pdfMargin - pdfContentPadding - xPos;
    text = doc.splitTextToSize(subjectData.aoi.placeName, maxTextLength);
    doc.setFont('helvetica', 'bold');
    doc.text(text, xPos, yPos);
    // text = subjectData.aoi.placeName;
    if (subjectData.aoi.type === 'point') {
      // coordinates
      xPos = xStartPos + pdfContentPadding * 2 + img.width;
      yPos = yPos + doc.getTextDimensions(text).h + 5;
      text = 'Latitude:';
      doc.setFont('helvetica', 'normal');
      doc.text(text, xPos, yPos);
      xPos = xPos + doc.getTextDimensions(text).w + 5;
      doc.setFont('helvetica', 'bold');
      text = subjectData.aoi.lat.toFixed(5);
      doc.text(text, xPos, yPos);
      xPos = xPos + doc.getTextDimensions(text).w + 10;
      text = 'Longitude:';
      doc.setFont('helvetica', 'normal');
      doc.text(text, xPos, yPos);
      xPos = xPos + doc.getTextDimensions(text).w + 5;
      doc.setFont('helvetica', 'bold');
      text = subjectData.aoi.lng.toFixed(5);
      doc.text(text, xPos, yPos);

      // radius
      xPos = xStartPos + pdfContentPadding * 2 + img.width;
      yPos = yPos + doc.getTextDimensions(text).h + 5;
      text = 'Radius:';
      doc.setFont('helvetica', 'normal');
      doc.text(text, xPos, yPos);
      xPos = xPos + doc.getTextDimensions(text).w + 5;
      doc.setFont('helvetica', 'bold');
      text = `${subjectData.aoi.radius} miles`;
      doc.text(text, xPos, yPos);
    }

    // zip code
    xPos = xStartPos + pdfContentPadding * 2 + img.width;
    yPos = yPos + doc.getTextDimensions(text).h + 5;
    text = 'ZIP Code:';
    doc.setFont('helvetica', 'normal');
    doc.text(text, xPos, yPos);
    xPos = xPos + doc.getTextDimensions(text).w + 5;
    doc.setFont('helvetica', 'bold');
    text = subjectData.zipcode;
    doc.text(text, xPos, yPos);

    // district
    xPos = xStartPos + pdfContentPadding * 2 + img.width;
    yPos = yPos + doc.getTextDimensions(text).h + 5;
    text = 'School District:';
    doc.setFont('helvetica', 'normal');
    doc.text(text, xPos, yPos);
    xPos = xPos + doc.getTextDimensions(text).w + 5;
    maxTextLength = pdfPageWidth - pdfMargin - pdfContentPadding - xPos;
    text = doc.splitTextToSize(subjectData.district, maxTextLength);
    doc.setFont('helvetica', 'bold');
    doc.text(text, xPos, yPos);

    // metro
    xPos = xStartPos + pdfContentPadding * 2 + img.width;
    yPos = yPos + doc.getTextDimensions(text).h + 5;
    text = 'Metro:';
    doc.setFont('helvetica', 'normal');
    doc.text(text, xPos, yPos);
    xPos = xPos + doc.getTextDimensions(text).w + 5;
    maxTextLength = pdfPageWidth - pdfMargin - pdfContentPadding - xPos;
    text = doc.splitTextToSize(subjectData.district, maxTextLength);
    doc.setFont('helvetica', 'bold');
    doc.text(text, xPos, yPos);

    // lease mode
    xPos = xStartPos + pdfContentPadding * 2 + img.width;
    yPos = yPos + doc.getTextDimensions(text).h + 5;
    text = 'Lease/Sale Mode:';
    doc.setFont('helvetica', 'normal');
    doc.text(text, xPos, yPos);
    xPos = xPos + doc.getTextDimensions(text).w + 5;
    text = inputData.isLeaseMode ? 'Lease' : 'Sale';
    doc.setFont('helvetica', 'bold');
    doc.text(text, xPos, yPos);

    // borders
    const topLeft = [pdfMargin, yStartPos];
    const topRight = [pdfPageWidth - pdfMargin, yStartPos];
    // prettier-ignore
    const bottomLeft = [pdfMargin, yStartPos + pdfContentPadding*2 + img.height];
    // prettier-ignore
    const bottomRight = [pdfPageWidth - pdfMargin, yStartPos + pdfContentPadding*2 + img.height];
    doc.line(...topLeft, ...topRight); // Top border
    doc.line(...topRight, ...bottomRight); // Right border
    doc.line(...bottomRight, ...bottomLeft); // Bottom border
    doc.line(...bottomLeft, ...topLeft); // Left border

    lastYAxisPos = yStartPos + pdfContentPadding * 2 + img.height;
  };

  const generateNearbyParcelOwnerSummary = async () => {
    const generate = async () => {
      const yStartPos = lastYAxisPos + pdfGap;
      const xStartPos = pdfMargin;

      let xPos, yPos, text;
      doc.setFontSize(14);
      doc.setFont('helvetica', 'normal');
      text = `Nearby Parcel Owner Summary`;
      yPos = yStartPos + pdfContentPadding + doc.getTextDimensions(text).h - 2;
      xPos = xStartPos + pdfContentPadding;
      doc.text(text, xPos, yPos);

      let renterCount, ownerCount;
      for (let i = 0; i < currentParcelOwnerSummary.owner_count.length; i++) {
        const owner = currentParcelOwnerSummary.owner_count[i];
        if (owner.owner_occupied_sl === 'No') {
          renterCount = owner.count;
        } else if (owner.owner_occupied_sl === 'Yes') {
          ownerCount = owner.count;
        }
      }
      const ownerPercent = Math.round(
        (ownerCount / (ownerCount + renterCount)) * 100,
      );
      const renterPercent = Math.round(
        (renterCount / (ownerCount + renterCount)) * 100,
      );
      doc.setFontSize(10);
      doc.setFont('helvetica', 'bold');
      text = `${renterCount}(${renterPercent}%) / ${ownerCount}(${ownerPercent}%)`;
      // prettier-ignore
      xPos = pdfPageWidth - pdfMargin - pdfContentPadding - doc.getTextDimensions(text).w;
      doc.text(text, xPos, yPos);
      doc.setFontSize(8);
      doc.setFont('helvetica', 'normal');
      text = 'Renter/Owner Occupied';
      xPos = xPos - 5 - doc.getTextDimensions(text).w;
      doc.text(text, xPos, yPos);

      doc.setFontSize(10);
      doc.setFont('helvetica', 'bold');
      text = `${renterCount + ownerCount}`;
      xPos = xPos - doc.getTextDimensions(text).w - 10;
      doc.text(text, xPos, yPos);

      doc.setFontSize(8);
      doc.setFont('helvetica', 'normal');
      text = 'Total ';
      xPos = xPos - 5 - doc.getTextDimensions(text).w;
      doc.text(text, xPos, yPos);

      yPos += 5;
      const leftHeaderLineAxis = [xStartPos + pdfContentPadding, yPos];
      //prettier-ignore
      const rightHeaderLineAxis = [pdfPageWidth - pdfMargin - pdfContentPadding, yPos];
      doc.line(...leftHeaderLineAxis, ...rightHeaderLineAxis); // Header text underline

      doc.setFontSize(10);
      text = '% Based on renter occupied';
      yPos = yPos + doc.getTextDimensions(text).h + 4;
      doc.text(text, xStartPos + pdfContentPadding, yPos);

      const { institution_count } = currentParcelOwnerSummary;
      const containerWidth =
        pdfPageWidth - pdfMargin * 2 - pdfContentPadding * 2;
      const positionIncrement = containerWidth / (institution_count.length - 1);
      xPos = positionIncrement / 2;
      yPos = yPos + 10;
      const progressWidth = 30;
      const progressHeight = progressWidth;

      // TODO: switch other funds and progress positions
      for (let i = 0; i < institution_count.length; i++) {
        try {
          const institution = institution_count[i];
          if (institution.institution === 'None') continue;
          const img = new Image();
          img.src = insightsDataURL[institution.institution.split(' ')[0]];

          // prettier-ignore
          doc.addImage(img, 'png', xPos + progressWidth / 2, yPos, progressWidth, progressHeight, '', 'FAST');
          doc.setFontSize(8);
          text = institution.institution;
          text =
            doc.getTextDimensions(text).w > progressWidth + 5
              ? `${institution.institution.replace(/\s/g, `\n`)}`
              : text;

          const institutionPercent = (institution.count / renterCount) * 100;
          if (text === 'Other') text = 'Other Funds';
          text = `${text}\n(${institutionPercent.toFixed(1)}%)`;
          doc.text(
            text,
            xPos + progressWidth,
            yPos + progressHeight + 10,
            'center',
          );

          xPos += positionIncrement;
        } catch (err) {
          console.log(err);
        }
      }

      yPos = yPos + progressHeight + 25 + pdfContentPadding;
      // borders
      const topLeft = [pdfMargin, yStartPos];
      const topRight = [pdfPageWidth - pdfMargin, yStartPos];
      // prettier-ignore
      const bottomLeft = [pdfMargin, yPos];
      // prettier-ignore
      const bottomRight = [pdfPageWidth - pdfMargin, yPos];
      doc.line(...topLeft, ...topRight); // Top border
      doc.line(...topRight, ...bottomRight); // Right border
      doc.line(...bottomRight, ...bottomLeft); // Bottom border
      doc.line(...bottomLeft, ...topLeft); // Left border

      lastYAxisPos = yPos;
    };

    let saveLastYAxisPos = lastYAxisPos;
    const initPageCount = doc.internal.getNumberOfPages();
    doc.addPage(); // temp page
    await generate();
    if (lastYAxisPos <= pdfPageHeight - topBottomMargin) {
      // fits in current page
      doc.deletePage(doc.internal.getNumberOfPages());
      lastYAxisPos = saveLastYAxisPos;
      await generate();
    } else {
      const newPages = doc.internal.getNumberOfPages() - initPageCount;
      for (let i = 0; i < newPages; i++) {
        doc.deletePage(doc.internal.getNumberOfPages()); // delete temp pages
      }
      doc.setPage(doc.internal.getNumberOfPages());
      doc.addPage();
      lastYAxisPos = topBottomMargin;
      await generate();
    }
  };

  const generateMarketConditions = async () => {
    const generate = async () => {
      const yStartPos = lastYAxisPos + pdfGap;
      const xStartPos = pdfMargin;

      let xPos, yPos, text;
      doc.setFontSize(14);
      doc.setFont('helvetica', 'normal');
      text = `Market Conditions`;
      yPos = yStartPos + pdfContentPadding + doc.getTextDimensions(text).h - 2;
      xPos = xStartPos + pdfContentPadding;
      doc.text(text, xPos, yPos);

      yPos += 5;
      const leftHeaderLineAxis = [xStartPos + pdfContentPadding, yPos];
      //prettier-ignore
      const rightHeaderLineAxis = [pdfPageWidth - pdfMargin - pdfContentPadding, yPos];
      doc.line(...leftHeaderLineAxis, ...rightHeaderLineAxis); // Header text underline

      const tableContent = getMarketConditionTableContent();

      const tableHeaderRowHeight = 12.5;
      const tableBodyRowHeight = 15;
      const tableWidth = 194;

      yPos += 15;

      // const startDate = moment(
      //   inputData.marketConditionDates.startDate,
      //   dateFormat,
      // );
      // const endDate = moment(
      //   inputData.marketConditionDates.endDate,
      //   dateFormat,
      // );
      // const numberOfDays = moment(endDate).diff(startDate, 'days');
      const numberOfMonths = marketConditionCtx.months;

      text = `Previous completed ${numberOfMonths == 1 ? 'month' : 'months'}:`;
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');
      xPos = xStartPos + pdfContentPadding + tableWidth / 2;
      doc.text(text, xPos, yPos, 'center');
      xPos = xPos + doc.getTextDimensions(text).w / 2 + 5;
      text = `${numberOfMonths}`;
      doc.setFont('helvetica', 'bold');
      doc.text(text, xPos, yPos);

      text = `Previous prior completed ${
        numberOfMonths === 1 ? 'month' : 'months'
      }: `;
      xPos =
        pdfMargin + pdfContentPadding + tableWidth + pdfGap + tableWidth / 2;
      doc.setFont('helvetica', 'normal');
      doc.text(text, xPos, yPos, 'center');
      xPos = xPos + doc.getTextDimensions(text).w / 2 + 5;
      text = `${numberOfMonths}`;
      doc.setFont('helvetica', 'bold');
      doc.text(text, xPos, yPos);

      yPos += 5;

      autoTable(doc, {
        headStyles: {
          fillColor: [229, 229, 229],
          textColor: [0, 0, 0],
          fontSize: 8,
          minCellHeight: tableHeaderRowHeight,
          valign: 'middle',
          lineWidth: 1,
        },
        bodyStyles: {
          fontSize: 8,
          minCellHeight: tableBodyRowHeight,
          valign: 'middle',
          lineWidth: 1,
        },
        startY: yPos,
        margin: {
          left: pdfMargin + pdfContentPadding,
          right: pdfPageWidth - (pdfMargin + pdfContentPadding + tableWidth),
        },
        head: tableContent.header[0],
        body: tableContent.content[0],
      });

      autoTable(doc, {
        headStyles: {
          fillColor: [229, 229, 229],
          textColor: [0, 0, 0],
          fontSize: 8,
          minCellHeight: tableHeaderRowHeight,
          valign: 'middle',
          lineWidth: 1,
        },
        bodyStyles: {
          fontSize: 8,
          minCellHeight: tableBodyRowHeight,
          valign: 'middle',
          lineWidth: 1,
        },
        startY: yPos,
        margin: {
          left: pdfMargin + pdfContentPadding + tableWidth + pdfGap,
          right: pdfMargin + pdfContentPadding,
        },
        head: tableContent.header[1],
        body: tableContent.content[1],
      });

      yPos =
        yPos +
        tableHeaderRowHeight * 2 +
        tableBodyRowHeight * 6 +
        pdfContentPadding * 2;
      // borders
      const topLeft = [pdfMargin, yStartPos];
      const topRight = [pdfPageWidth - pdfMargin, yStartPos];
      // prettier-ignore
      const bottomLeft = [pdfMargin, yPos];
      // prettier-ignore
      const bottomRight = [pdfPageWidth - pdfMargin, yPos];
      doc.line(...topLeft, ...topRight); // Top border
      doc.line(...topRight, ...bottomRight); // Right border
      doc.line(...bottomRight, ...bottomLeft); // Bottom border
      doc.line(...bottomLeft, ...topLeft); // Left border

      lastYAxisPos = yPos;
    };

    let saveLastYAxisPos = lastYAxisPos;
    const initPageCount = doc.internal.getNumberOfPages();
    doc.addPage(); // temp page
    await generate();
    if (lastYAxisPos <= pdfPageHeight - topBottomMargin) {
      // fits in current page
      doc.deletePage(doc.internal.getNumberOfPages());
      lastYAxisPos = saveLastYAxisPos;
      await generate();
    } else {
      const newPages = doc.internal.getNumberOfPages() - initPageCount;
      for (let i = 0; i < newPages; i++) {
        doc.deletePage(doc.internal.getNumberOfPages()); // delete temp pages
      }
      doc.setPage(doc.internal.getNumberOfPages());
      doc.addPage();
      lastYAxisPos = topBottomMargin;
      await generate();
    }
  };

  const getMarketConditionTableContent = (current, comp) => {
    const { currentMarketCondition, priorMarketCondition } = marketConditionCtx;

    const typeValueFormatter = (value) => {
      switch (value) {
        case 'active':
          return 'Active';
        case 'closed':
          return 'Closed';
        case 'active_median_psf':
        case 'closed_median_psf':
          return 'Median PSF';
        case 'median_dom':
          return 'Median DOM';
        case 'months_of_inventory':
          return 'Months of Inventory';
        default:
          return value;
      }
    };

    const formatValue = (value, record) => {
      if (['active', 'closed', 'median_dom'].includes(record.type)) {
        return Math.round(value).toLocaleString();
      } else if (
        record.type === 'active_median_psf' ||
        record.type === 'closed_median_psf'
      ) {
        return `$${(Math.round(value * 100) / 100).toLocaleString()}`;
      } else {
        return (Math.round(value * 100) / 100).toLocaleString();
      }
    };

    const currentContent = currentMarketCondition.map((item) => {
      return [
        typeValueFormatter(item.type),
        formatValue(item.aoi, item),
        formatValue(item.zipcode, item),
        formatValue(item.schoolDistrict, item),
        formatValue(item.county, item),
        formatValue(item.metro, item),
      ];
    });

    const compContent = priorMarketCondition.map((item) => {
      return [
        typeValueFormatter(item.type),
        formatValue(item.aoi, item),
        formatValue(item.zipcode, item),
        formatValue(item.schoolDistrict, item),
        formatValue(item.county, item),
        formatValue(item.metro, item),
      ];
    });

    // prettier-ignore
    // const currentContent = [
    //   ['Active', aoi.active, zipCode.active, schoolDistrict.active, county.active, metro.active],
    //   ['Median PSF', `$${aoi.activeMedianPSF}`, `$${zipCode.activeMedianPSF}`, `$${schoolDistrict.activeMedianPSF}`, `$${county.activeMedianPSF}`, `$${metro.activeMedianPSF}`],
    //   ['Closed', aoi.closed, zipCode.closed, schoolDistrict.closed, county.closed, metro.closed],
    //   ['Median PSF', `$${aoi.closedMedianPSF}`, `$${zipCode.closedMedianPSF}`, `$${schoolDistrict.closedMedianPSF}`, `$${county.closedMedianPSF}`, `$${metro.closedMedianPSF}`],
    //   ['Median DOM', `${aoi.medianDOM}`, `${zipCode.medianDOM}`, `${schoolDistrict.medianDOM}`, `${county.medianDOM}`, `${metro.medianDOM}`],
    //   ['Months of Inventory', `${aoi.monthsOfInventory}`, `${zipCode.monthsOfInventory}`, `${schoolDistrict.monthsOfInventory}`, `${county.monthsOfInventory}`, `${metro.monthsOfInventory}`],
    // ]
    // aoi = comp.aoi;
    // zipCode = comp.zipCode;
    // schoolDistrict = comp.schoolDistrict;
    // county = comp.county;
    // metro = comp.metro;
    // // prettier-ignore
    // const compContent = [
    //   [ 'Active', aoi.active, zipCode.active, schoolDistrict.active, county.active,  metro.active ],
    //   [ 'Median PSF', `$${aoi.activeMedianPSF}`, `$${zipCode.activeMedianPSF}`, `$${schoolDistrict.activeMedianPSF}`, `$${county.activeMedianPSF}`, `$${metro.activeMedianPSF}` ],
    //   [ 'Closed', aoi.closed, zipCode.closed, schoolDistrict.closed, county.closed, metro.closed ],
    //   [ 'Median PSF', `$${aoi.closedMedianPSF}`, `$${zipCode.closedMedianPSF}`, `$${schoolDistrict.closedMedianPSF}`, `$${county.closedMedianPSF}`, `$${metro.closedMedianPSF}` ],
    //   [ 'Median DOM', `${aoi.medianDOM}`, `${zipCode.medianDOM}`, `${schoolDistrict.medianDOM}`,`${county.medianDOM}`, `${metro.medianDOM}` ],
    //   [ 'Months of Inventory', `${aoi.monthsOfInventory}`, `${zipCode.monthsOfInventory}`, `${schoolDistrict.monthsOfInventory}`, `${county.monthsOfInventory}`, `${metro.monthsOfInventory}` ],
    // ]

    const currentHeader = [
      [' ', 'AOI', 'Zip Code', 'School District', 'County', 'Metro'],
    ];
    const compHeader = [
      [' ', 'AOI', 'Zip Code', 'School District', 'County', 'Metro'],
    ];
    return {
      header: [currentHeader, compHeader],
      content: [currentContent, compContent],
    };
  };

  const generateMarketConditionsHistory = async () => {
    const graphContainerId = [
      'marketConditionHistory-priceFull',
      'marketConditionHistory-pricePerSqft',
      'marketConditionHistory-ListedClosed',
      'marketConditionHistory-DOM',
    ];

    let chartAreaAdded = false;
    let firstGraphAdded = false;

    const addChartAreaLabel = () => {
      let xPos = pdfMargin;
      let yPos = lastYAxisPos + pdfGap;

      let text = 'Chart Area: ';
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(12);
      doc.text(text, xPos, yPos);
      xPos = xPos + doc.getTextDimensions(text).w + 5;
      if (inputData.chartArea === 'aoi') {
        text = 'AOI';
      } else if (inputData.chartArea === 'zipcode') {
        text = 'ZIP Code';
      } else if (inputData.chartArea === 'district') {
        text = 'School District';
      } else if (inputData.chartArea === 'county') {
        text = 'County';
      } else if (inputData.chartArea === 'metro') {
        text = 'Metro';
      }
      doc.setFont('helvetica', 'bold');
      doc.text(text, xPos, yPos);
      lastYAxisPos = yPos - doc.getTextDimensions(text).h;
    };

    for (let i = 0; i < graphContainerId.length; i++) {
      const id = graphContainerId[i];
      if (!selectedItems.includes(id)) continue;

      const generate = async () => {
        if (!chartAreaAdded && !firstGraphAdded) {
          addChartAreaLabel();
          chartAreaAdded = true;
        }

        let yStartPos = lastYAxisPos + pdfGap;
        let xStartPos = pdfMargin;
        let xPos, yPos, text;

        doc.setFontSize(14);
        doc.setFont('helvetica', 'normal');

        const dataURL = insightsDataURL[id.split('-')[1]];

        let graphType;
        if (i === 0)
          inputData.isLeaseMode
            ? (graphType = 'Rent Graph')
            : (graphType = 'Price Graph');
        else if (i === 1)
          inputData.isLeaseMode
            ? (graphType = 'Rent Per Sqft Graph')
            : (graphType = 'Price Per Sqft Graph');
        else if (i === 2) graphType = 'Listed/Closed Graph';
        else if (i === 3) graphType = 'DOM Graph';

        text = `Market Conditions History ${graphType}`;
        yPos =
          yStartPos + pdfContentPadding + doc.getTextDimensions(text).h - 2;
        xPos = xStartPos + pdfContentPadding;
        doc.text(text, xPos, yPos);

        yPos += 5;
        const leftHeaderLineAxis = [xStartPos + pdfContentPadding, yPos];
        //prettier-ignore
        const rightHeaderLineAxis = [pdfPageWidth - pdfMargin - pdfContentPadding, yPos];
        doc.line(...leftHeaderLineAxis, ...rightHeaderLineAxis); // Header text underline

        xPos = xStartPos + pdfContentPadding;
        yPos += pdfContentPadding;

        let maxWidth = pdfPageWidth - pdfMargin * 2 - pdfContentPadding * 2;

        const img = await processImage(dataURL);

        img.height = (img.height * maxWidth) / img.width;
        img.width = maxWidth;

        doc.addImage(img, 'png', xPos, yPos, img.width, img.height, '', 'FAST');

        yPos = yPos + img.height + pdfContentPadding;
        // borders
        const topLeft = [pdfMargin, yStartPos];
        const topRight = [pdfPageWidth - pdfMargin, yStartPos];
        // prettier-ignore
        const bottomLeft = [pdfMargin, yPos];
        // prettier-ignore
        const bottomRight = [pdfPageWidth - pdfMargin, yPos];
        doc.line(...topLeft, ...topRight); // Top border
        doc.line(...topRight, ...bottomRight); // Right border
        doc.line(...bottomRight, ...bottomLeft); // Bottom border
        doc.line(...bottomLeft, ...topLeft); // Left border

        lastYAxisPos = yPos;
      };

      let saveLastYAxisPos = lastYAxisPos;
      const initPageCount = doc.internal.getNumberOfPages();
      doc.addPage(); // temp page
      await generate();
      if (!firstGraphAdded) chartAreaAdded = false;
      if (lastYAxisPos <= pdfPageHeight - topBottomMargin) {
        // fits in current page
        doc.deletePage(doc.internal.getNumberOfPages());
        lastYAxisPos = saveLastYAxisPos;
        await generate();
      } else {
        const newPages = doc.internal.getNumberOfPages() - initPageCount;
        for (let i = 0; i < newPages; i++) {
          doc.deletePage(doc.internal.getNumberOfPages()); // delete temp pages
        }
        doc.setPage(doc.internal.getNumberOfPages());
        doc.addPage();
        lastYAxisPos = topBottomMargin;
        await generate();
      }

      firstGraphAdded = true;
    }
  };

  const generateFooter = async () => {
    const xStartPos = pdfMargin;
    const yStartPos = pdfPageHeight - topBottomMargin;
    const totalPages = doc.internal.getNumberOfPages();
    doc.setFontSize(10);

    for (let i = 0; i < totalPages; i++) {
      doc.setPage(i + 1);
      let xPos, yPos, text;
      const clientInfo = getClientInformation(inputData.userGroup);
      if (clientInfo && clientInfo.clientName) {
        const img = await processImage(clientInfo.png);

        const maxHeight = 20;
        img.width = img.width * (maxHeight / img.height);
        img.height = maxHeight;

        xPos = xStartPos;
        yPos = yStartPos + pdfContentPadding;
        //prettier-ignore
        doc.addImage(img, 'png', xPos, yPos, img.width, img.height, '', 'FAST');

        text = `Page ${i + 1} of ${totalPages}`;
        yPos = yPos + 10 + doc.getTextDimensions(text).h / 2;
        xPos = pdfPageWidth / 2;
        doc.text(text, xPos, yPos, 'center');

        if (!['Sunroom'].includes(clientInfo.clientName)) {
          text = 'Locate Alpha';
          xPos = pdfPageWidth - pdfMargin - doc.getTextDimensions(text).w;
          doc.text(text, xPos, yPos);

          const locatealphaLogo = await processImage(
            '/images/logo/LocateAlpha_Logo.png',
          );
          locatealphaLogo.width = 10;
          locatealphaLogo.height = 10;

          xPos = xPos - 10 - 5;
          yPos = yPos - 7.5;
          //prettier-ignore
          doc.addImage(locatealphaLogo, 'png', xPos, yPos, locatealphaLogo.width, locatealphaLogo.height, '', 'FAST');
        }
      }
    }
  };

  return (
    <PDFContext.Provider
      value={{
        generatePDF,
      }}
    >
      {children}
    </PDFContext.Provider>
  );
};

export default PDFContext;
