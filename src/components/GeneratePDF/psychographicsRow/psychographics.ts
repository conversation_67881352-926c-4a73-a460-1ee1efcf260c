import autoTable from 'jspdf-autotable';
import {
  doc,
  pdfPageWidth,
  pdfPageHeight,
  pdfSideMargin,
  pdfGap,
  pdfContentPadding,
  footerHeight,
  lastYAxisPos,
  setLastYAxisPos,
} from '../GeneratePDF';
import downloadLink from '@/components/ResultTable/SchoolScore/downloadLink';

export interface PsychoGraphicsContent {
  address: string;
  city: string;
  zip: string;
  Segment: string;
  Family: string;
  'Match Level': string;
}

function formatter(content: PsychoGraphicsContent) {
  //   return {
  //     downloadLink: downloadLink[content.Segment.split(' - ')[0]],
  //     Segment: content.Segment,
  //     Family: content.Family,
  //     'Match Level': content['Match Level'],
  //   };
  return [
    ['Family', content.Family],
    ['Segment', content.Segment],
    ['Match Level', content['Match Level']],
    ['Segment Overview', downloadLink[content.Segment.split(' - ')[0]]],
  ];
}

export const createPsychographicsRow = (content: PsychoGraphicsContent) => {
  if (!content) return;
  const formattedContent = formatter(content);
  const tableHeaderRowHeight = 12.5;
  const tableBodyRowHeight = 15;
  // Basic configuration
  const spaceNeededForRow = 115; // You might not need this hardcoded value anymore
  let rowStartY;

  // Page management: checking for need to add new page
  if (
    pdfPageHeight - lastYAxisPos - (pdfSideMargin * 2 + 10) <
    spaceNeededForRow
  ) {
    rowStartY = pdfSideMargin * 2 + 10;
    doc.addPage();
  } else {
    rowStartY =
      lastYAxisPos + (lastYAxisPos === 0 ? pdfSideMargin * 2 + 10 : pdfGap);
  }

  // Setting initial line position for top border
  let topLeftAxis = [pdfSideMargin, rowStartY];
  let topRightAxis = [pdfPageWidth - pdfSideMargin, rowStartY];

  // Draw the top border of the section
  doc.line(...topLeftAxis, ...topRightAxis);
  // Content starting positions
  const contentStartX = pdfSideMargin + pdfContentPadding;
  const contentEndX = pdfPageWidth - pdfSideMargin - pdfContentPadding;
  let lineY = rowStartY + pdfContentPadding;

  // Writing section header
//   doc.setFontSize(14);
//   let headerText = 'Psychographics Data '
//   lineY += doc.getTextDimensions(headerText).h;
//   doc.text(headerText, contentStartX, lineY);
//   const leftHeaderLineAxis = [contentStartX, lineY];
//   const rightHeaderLineAxis = [contentEndX, lineY];
//   doc.line(...leftHeaderLineAxis, ...rightHeaderLineAxis);

  doc.setFontSize(14);
  doc.setFont('helvetica', 'normal');
  let text = 'Psychographics Data';
  lineY = rowStartY + pdfContentPadding + doc.getTextDimensions(text).h - 2;
  doc.text(text, contentStartX, lineY);

  lineY = lineY + 5;
  const leftHeaderLineAxis = [contentStartX, lineY];
  const rightHeaderLineAxis = [contentEndX, lineY];
  doc.line(...leftHeaderLineAxis, ...rightHeaderLineAxis); // Header text underline

  lineY += 5; // Space after the header
  let startY = lineY + 5;
  // Initialize table
  autoTable(doc, {
    startY: lineY,
    headStyles: {
        fillColor: [229, 229, 229],
        textColor: [0, 0, 0],
        minCellHeight: tableHeaderRowHeight,
        valign: 'middle',
        lineWidth: 1,
        // cellPadding: content.rowName === 'Multi-Family' || 2,
      },
      bodyStyles: {
        fontSize: 8,
        minCellHeight: tableBodyRowHeight,
        valign: 'middle',
        lineWidth: 1,
      },
    margin: { horizontal: pdfSideMargin + pdfContentPadding },
    head: [['Item', 'Data']],
    body: formattedContent,
  });

  lineY =
  startY +
  tableHeaderRowHeight +
  4 * tableBodyRowHeight +
  pdfContentPadding +
  4;

const bottomLeftAxis = [pdfSideMargin, lineY];
const bottomRightAxis = [pdfPageWidth - pdfSideMargin, lineY];

doc.setDrawColor(0, 0, 0);

doc.line(...topLeftAxis, ...bottomLeftAxis); // Left Border
doc.line(...topRightAxis, ...bottomRightAxis); // Right Border
doc.line(...bottomRightAxis, ...bottomLeftAxis); // Bottom border

setLastYAxisPos(lineY);
};
