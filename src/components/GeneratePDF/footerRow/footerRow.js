import {
  doc,
  lastYAxisPos,
  pdfContentPadding,
  pdfGap,
  pdfPageHeight,
  pdfPageWidth,
  pdfSideMargin,
  setLastYAxisPos,
} from '../GeneratePDF';

const processImage = (src) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = src;
    img.onload = () => resolve(img);
    img.onerror = reject;
  });
};

export const createFooter = async (clientInfo) => {
  const rowStartY = lastYAxisPos;
  let text, lineY, lineX, yIncrement;

  const totalPages = doc.internal.getNumberOfPages();

  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  lineY = pdfPageHeight - pdfSideMargin;

  // const img = await processImage(clientInfo.png);
  // const width = (10 / img.height) * img.width;
  // const height = 10;
  // let yPos = lineY - img.height + 2;

  // console.log(clientInfo);
  // console.log(img);
  // console.log(width);
  // console.log(height);
  // console.log(yPos);

  for (let i = 0; i < totalPages; i++) {
    doc.setPage(i + 1);
    if (i + 1 === doc.internal.getCurrentPageInfo().pageNumber) {
      // add client logo
      let width, height;

      if (
        clientInfo &&
        clientInfo.clientName &&
        clientInfo.clientName !== 'demo-users'
      ) {
        // const img = new Image();
        // img.src = clientInfo.png;
        // if (clientInfo.clientName === 'BridgeTower') {
        //   width = 24.58;
        //   height = 10;
        // } else if (clientInfo.clientName === 'CommonGroundCapital') {
        //   width = 58.02;
        //   height = 10;
        // } else if (clientInfo.clientName === 'MarketplaceHomes') {
        //   width = 47;
        //   height = 10;
        // } else if (clientInfo.clientName === 'DivvyHomes') {
        //   width = 28.18;
        //   height = 10;
        // } else if (clientInfo.clientName === 'RealCo') {
        //   width = 9.05;
        //   height = 10;
        // } else if (clientInfo.clientName === 'Avanta') {
        //   width = 42.65;
        //   height = 10;
        // } else if (clientInfo.clientName === 'Nhimble') {
        //   width = 46.88;
        //   height = 10;
        // } else if (clientInfo.clientName === 'Bridge') {
        //   width = 16.6;
        //   height = 10;
        // }

        const img = await processImage(clientInfo.png);
        width = (10 / img.height) * img.width;
        height = 10;

        doc.addImage(
          img,
          'png',
          pdfSideMargin,
          lineY - height + 2,
          width,
          height,
          '',
          'FAST',
        );
        // prettier-ignore
        // doc.addImage(img,'png',pdfSideMargin,lineY - height + 2,width,height,'','FAST');
      }

      // add page number
      const pageNumber = doc.internal.getCurrentPageInfo().pageNumber;
      text = `Page ${pageNumber} of ${totalPages}`;
      doc.text(text, pdfPageWidth / 2, lineY, 'center');

      if (clientInfo && !['Sunroom'].includes(clientInfo?.clientName)) {
        // spatial laser logo
        text = `CMA Portal`;
        lineX = pdfPageWidth - pdfSideMargin - doc.getTextDimensions(text).w;
        doc.text(text, lineX, lineY);

        const imgWxH = 10;
        const locatealphaLogo = new Image();
        locatealphaLogo.src = '/images/logo/LocateAlpha_Logo.png';
        lineX = lineX - 2 - imgWxH;

        doc.addImage(
          locatealphaLogo,
          'png',
          lineX,
          lineY - imgWxH + 2,
          imgWxH,
          imgWxH,
          '',
          'FAST',
        );
      }
    }
  }
};
