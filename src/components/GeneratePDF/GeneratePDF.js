import styles from '@/components/ResultTable/resultTable.css';
import userGroupAccess from '@/userGroupAccess.json';
import { userGroupHasAccess } from '@/utils/userGroup';
import { LoadingOutlined, WarningOutlined } from '@ant-design/icons';
import {
  Button,
  Checkbox,
  Input,
  InputNumber,
  Modal,
  Space,
  Spin,
  Tooltip,
} from 'antd';
import { jsPDF } from 'jspdf';
import isEmpty from 'lodash.isempty';
import { useEffect, useState } from 'react';
import { connect } from 'umi';
import { createCompImagesSection } from './compGeographicRow/compGeographicRow';
import { createDisclaimer } from './disclaimerRow/disclaimerRow';
import { createFooter } from './footerRow/footerRow';
import { createHeader } from './headerRow/headerRow';
import { createLocationScoresRow } from './locationScores/locationScores';
import { createNearbyChainStoreRow } from './nearbyChainStoreRow/nearbyChainStore';
import { createNearbyLeaseSummary } from './nearbyLeaseRow/nearbyLeaseRow';
import { createNearbyParcelOwnerSummary } from './nearbyParcelOwnerRow/nearbyParcelOwnerRow';
import { createNearbySaleSummary } from './nearbySaleRow/nearbySaleRow';
import { createPsychographicsRow } from './psychographicsRow/psychographics';
import { createSchoolScoreDemographicRow } from './schoolScoreDemographic/schoolScoreDemographic';
import {
  createSubjectProperty,
  formatToCurrency,
} from './subjectPropertyRow/subjectPropertyRow';
import { createTableRow } from './tableRow/tableRow';
const { TextArea } = Input;
// PDF PAGE DIMENSIONS
// Orientation: Portrait
// Width: 459 px
// Height: 594 px
// Format: letter

const defaultModalOptions = [
  'Demographics',
  'BT Owned Properties',
  'National SFR Operators Listings',
  'Rental Listings from 3rd Party',
  'MLSLease',
  'MLSSale',
  'Multi-Family Properties',
  'New Construction Homes',
];

export let doc;
export let pdfPageWidth, pdfPageHeight;
export const pdfSideMargin = 15;
export const pdfGap = 15;
export const pdfContentPadding = 10;
export const footerHeight = 40;
export let lastYAxisPos = 0;
let currentPage, totalPages;

export const setLastYAxisPos = (yAxis) => {
  lastYAxisPos = yAxis;
};

const antIcon = <LoadingOutlined style={{ fontSize: 16 }} spin />;

let pdfSourceGenerating = null;

const GeneratePDF = connect(({ CMA, report }) => ({
  map: CMA.map,
  pdfContent: report.pdfContent,
  pdfLoading: report.pdfLoading,
  currentBTOwnedProperties: CMA.currentBTOwnedProperties,
  currentNationalOperatorsPropertiesFiltered:
    CMA.currentNationalOperatorsPropertiesFiltered,
  currentHotPadsPropertiesFiltered: CMA.currentHotPadsPropertiesFiltered,
  currentMLSPropertiesFiltered: CMA.currentMLSPropertiesFiltered,
  currentMultiFamilyProperties: CMA.currentMultiFamilyProperties,
  subjectPropertyParcelData: CMA.subjectPropertyParcelData,
  eventCoordinates: CMA.eventCoordinates,
  userGroup: CMA.userGroup,
  selectedUserGroup: CMA.selectedUserGroup,
  compingMode: CMA.compingMode,
  nearbyChainStoreData: CMA.nearbyChainStoreData,
  lastSalePublicRecordDataForRender: CMA.lastSalePublicRecordDataForRender,
  lastSalePublicRecordMedian: CMA.lastSalePublicRecordMedian,
  lastSalePublicRecordSelectedRowKey: CMA.lastSalePublicRecordSelectedRowKey,
  // Include selected MLS row keys so the modal can reflect user selections
  selectedRowKeysMLSLease: CMA.selectedRowKeysMLSLease,
  selectedRowKeysMLSSale: CMA.selectedRowKeysMLSSale,
}))(function (props) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [hasMLSLease, setHasMLSLease] = useState(false);
  const [hasMLSSale, setHasMLSSale] = useState(false);
  const [hasLastSalePublicRecord, setHasLastSalePublicRecord] = useState(false);
  const [checkedRows, setCheckedRows] = useState(defaultModalOptions);
  const [priceOpinion, setPriceOpinion] = useState('');
  const [salesPriceOpinion, setSalesPriceOpinion] = useState('');
  const [comments, setComments] = useState('');
  console.log('pdfContent: ', props.pdfContent);

  useEffect(() => {
    if (
      props.pdfContent &&
      props.pdfContent.length > 0 &&
      pdfSourceGenerating === props.source
    ) {
      doc = new jsPDF('portrait', 'px', 'letter');
      pdfPageWidth = doc.internal.pageSize.getWidth();
      pdfPageHeight = doc.internal.pageSize.getHeight();

      const parcelOwner = document.querySelector('#parcel-owner-temp-solution');
      parcelOwner.style.display = 'block';

      generatePDF({
        ...props,
        checkedRows,
        priceOpinion,
        salesPriceOpinion,
        comments,
        userGroup: props.userGroup,
      }).then((fileName) => {
        closePDFModal();
        pdfSourceGenerating = null;

        if (props.source === 'combine') {
          const { generateQueue, setPdfArray, setGenerateQueue } =
            props.combineState;
          if (generateQueue && generateQueue === '1') {
            const pdfBuffer = doc.output('arraybuffer');
            setGenerateQueue('2');
            setPdfArray((prev) => [...prev, pdfBuffer]);
          }
        } else {
          doc.save(`${fileName}.pdf`);
        }

        parcelOwner.style.display = 'none';

        // Clear pdfContent state after PDF generated
        props.dispatch({
          type: 'report/saveReportStates',
          payload: {
            pdfContent: [],
            pdfLoading: false,
          },
        });
      });
    }
  }, [props.pdfContent]);

  useEffect(() => {
    if (!props.combineState) return;

    if (props.source === 'combine') {
      const { generateQueue } = props.combineState;

      if (generateQueue && generateQueue === '1') {
        openPDFModal();
      }
    }
  }, [props.combineState]);

  const openPDFModal = async () => {
    if (isModalOpen) return;
    console.log('open pdf modal');
    props.dispatch({
      type: 'report/saveReportStates',
      payload: {
        pdfLoading: true,
      },
    });
    let mlsLeaseInformation = [];
    let mlsSaleInformation = [];
    if (props.compingMode == 'intelligentComping') {
      const adjustedResponse = await props.dispatch({
        type: 'report/getAdjustedRentAndSale',
      });
      if (adjustedResponse && adjustedResponse.length > 0) {
        mlsLeaseInformation = adjustedResponse[0].rentComp || [];
        mlsSaleInformation = adjustedResponse[0].salesComp || [];
      }
    } else {
      mlsLeaseInformation = await props.dispatch({
        type: 'report/getMLSInformation',
        payload: {
          propertyType: 'Residential Lease',
        },
      });

      mlsSaleInformation = await props.dispatch({
        type: 'report/getMLSInformation',
        payload: {
          propertyType: 'Residential',
        },
      });
    }

    props.dispatch({
      type: 'report/saveReportStates',
      payload: {
        pdfLoading: false,
      },
    });
    if (
      mlsLeaseInformation.length > 0 ||
      (props.selectedRowKeysMLSLease &&
        props.selectedRowKeysMLSLease.length > 0)
    ) {
      setHasMLSLease(true);
    }
    if (
      mlsSaleInformation.length > 0 ||
      (props.selectedRowKeysMLSSale && props.selectedRowKeysMLSSale.length > 0)
    ) {
      setHasMLSSale(true);
    }
    if (props.lastSalePublicRecordDataForRender.length > 0) {
      setHasLastSalePublicRecord(true);
    }

    if (
      isEmpty(props.subjectPropertyParcelData) &&
      props.currentBTOwnedProperties.length == 0 &&
      props.currentNationalOperatorsPropertiesFiltered.length == 0 &&
      props.currentHotPadsPropertiesFiltered.length == 0 &&
      mlsLeaseInformation.length == 0 &&
      mlsSaleInformation.length == 0 &&
      props.currentMultiFamilyProperties.length == 0
    ) {
      createPDF();
    } else {
      setIsModalOpen(true);
    }
  };

  const closePDFModal = () => {
    setIsModalOpen(false);
    setHasMLSLease(false);
    setHasMLSSale(false);
    setCheckedRows(defaultModalOptions);
    setPriceOpinion('');
    setSalesPriceOpinion('');
    setComments('');

    if (props.combineState) {
      const { setPdfArray, setGenerateQueue } = props.combineState;
      setGenerateQueue(null);
      setPdfArray([]);
    }
  };

  const onChange = (checkedValues) => {
    setCheckedRows(checkedValues);
  };

  const createPDF = () => {
    pdfSourceGenerating = props.source;
    props.dispatch({
      type: 'report/saveReportStates',
      payload: {
        pdfLoading: true,
      },
    });
    props.dispatch({
      type: 'report/createReport',
    });
  };

  const handleTextChange = (e) => {
    const value = e.target.value;
    const lines = value.split('\n');

    if (lines.length <= 5) {
      setComments(value);
    } else {
      setComments(lines.slice(0, 4).join('\n'));
    }
  };

  return (
    <>
      {props.source !== 'combine' && (
        <Tooltip
          id="print-tooltip"
          title={
            !userGroupHasAccess(props.selectedUserGroup, 'Comp PDF')
              ? 'Please contact us for a demo'
              : isEmpty(props.eventCoordinates)
              ? 'Please get a comp first'
              : 'Print or Save PDF'
          }
        >
          {/* <button className={styles.buttonWrapperSummaryRow} onClick={createPDF}> */}
          <button
            className={
              props.isIcon
                ? styles.buttonWrapperSummaryRow
                : styles.buttonWrapperSummaryRowText
            }
            // shape="circle"
            onClick={openPDFModal}
            disabled={
              isEmpty(props.eventCoordinates) ||
              !userGroupHasAccess(props.selectedUserGroup, 'Comp PDF')
            }
          >
            {props.isIcon ? (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                width={20}
                height={20}
                className={styles.printIconSummaryRow}
              >
                <path d="M15 5.833H5V3.375q0-.354.26-.615.261-.26.615-.26h8.25q.354 0 .615.26.26.261.26.615Zm0 4.813q.354 0 .615-.261.26-.26.26-.614t-.26-.615q-.261-.26-.615-.26t-.615.26q-.26.261-.26.615t.26.614q.261.261.615.261ZM6.75 15.75h6.5v-2.917h-6.5v2.917Zm0 1.75q-.729 0-1.24-.51Q5 16.479 5 15.75v-1.312H2.542q-.354 0-.615-.261-.26-.26-.26-.615V9.271q0-1.063.75-1.813t1.812-.75h11.542q1.062 0 1.812.75t.75 1.813v4.291q0 .355-.26.615-.261.261-.615.261H15v1.312q0 .729-.51 1.24-.511.51-1.24.51Z" />
              </svg>
            ) : (
              <span className={styles.headerBarButton}>PDF Report</span>
            )}
            {props.pdfLoading && <Spin indicator={antIcon} />}
          </button>
        </Tooltip>
      )}
      <Modal
        title="Generate Comp PDF"
        open={isModalOpen}
        centered
        width={350}
        onCancel={closePDFModal}
        footer={[
          <Button
            key="cancel"
            onClick={closePDFModal}
            disabled={props.pdfLoading}
          >
            Cancel
          </Button>,
          <Button
            key="generate"
            type="primary"
            onClick={createPDF}
            loading={props.pdfLoading}
          >
            Generate
          </Button>,
        ]}
      >
        <Checkbox.Group
          style={{
            width: '100%',
          }}
          onChange={onChange}
          value={checkedRows}
        >
          <p>Select data for PDF:</p>
          <Space direction="vertical">
            {/* {!isEmpty(props.subjectPropertyParcelData) && ( */}
            {/* <Checkbox value="Demographics" disabled={props.pdfLoading}>
              Demographics
            </Checkbox> */}
            <Checkbox value="SchoolScore" disabled={props.pdfLoading}>
              School Score and Demographics
            </Checkbox>
            <Checkbox value="Psychographics" disabled={props.pdfLoading}>
              Psychographics
            </Checkbox>
            <Checkbox value="FavorableBrands" disabled={props.pdfLoading}>
              Favorable Brands
            </Checkbox>
            {/* {props.nearbyChainStoreData.length === 0 ?
              <Checkbox value="FavorableBrands" disabled>
                  <Tooltip title="No nearby favorable brands found">

              Favorable brands <WarningOutlined />
                  </Tooltip>
            </Checkbox>:   <Checkbox value="FavorableBrands" disabled={props.pdfLoading}>
              Favorable brands
            </Checkbox>
          } */}
            {/* )} */}
            {props.currentBTOwnedProperties.length > 0 && (
              <Checkbox value="BT Owned Properties" disabled={props.pdfLoading}>
                BT Owned Properties
              </Checkbox>
            )}
            {props.currentNationalOperatorsPropertiesFiltered.length > 0 && (
              <Checkbox
                value="National SFR Operators Listings"
                disabled={props.pdfLoading}
              >
                National SFR Operators Listings
              </Checkbox>
            )}
            {props.currentHotPadsPropertiesFiltered.length > 0 && (
              <Checkbox
                value="Rental Listings from 3rd Party"
                disabled={props.pdfLoading}
              >
                Rental Listings from 3rd Party
              </Checkbox>
            )}
            {hasMLSLease && (
              <Checkbox value="MLSLease" disabled={props.pdfLoading}>
                MLS Lease
              </Checkbox>
            )}
            {hasMLSSale && (
              <Checkbox value="MLSSale" disabled={props.pdfLoading}>
                MLS Sale
              </Checkbox>
            )}
            {hasLastSalePublicRecord && (
              <Checkbox value="LSPR" disabled={props.pdfLoading}>
                Last Sales Public Record
              </Checkbox>
            )}
            {props.currentMultiFamilyProperties.length > 0 && (
              <Checkbox
                value="Multi-Family Properties"
                disabled={props.pdfLoading}
              >
                Multi-Family Properties
              </Checkbox>
            )}
            <Checkbox
              value="New Construction Homes"
              disabled={props.pdfLoading}
            >
              New Construction Homes
            </Checkbox>

            {/* Last Checkbox */}
            <Checkbox value="Final Rent Opinion" disabled={props.pdfLoading}>
              <span>Show final rent opinion:</span>
              <div>
                <InputNumber
                  prefix={'$'}
                  disabled={
                    !checkedRows.includes('Final Rent Opinion') ||
                    props.pdfLoading
                  }
                  style={{ width: '150px' }}
                  controls={false}
                  formatter={(value) =>
                    `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  }
                  parser={(value) => value.replace(/\$\s?|(,*)/g, '')}
                  value={priceOpinion}
                  onChange={(value) => setPriceOpinion(value)}
                />
              </div>
            </Checkbox>
            <Checkbox value="Final Sales Opinion" disabled={props.pdfLoading}>
              <span>Show final sales opinion:</span>
              <div>
                <InputNumber
                  prefix={'$'}
                  disabled={
                    !checkedRows.includes('Final Sales Opinion') ||
                    props.pdfLoading
                  }
                  style={{ width: '150px' }}
                  controls={false}
                  formatter={(value) =>
                    `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  }
                  parser={(value) => value.replace(/\$\s?|(,*)/g, '')}
                  value={salesPriceOpinion}
                  onChange={(value) => setSalesPriceOpinion(value)}
                />
              </div>
            </Checkbox>
            <Checkbox value="comments" disabled={props.pdfLoading}>
              <span>Comments</span>
              <div>
                <TextArea
                  disabled={
                    !checkedRows.includes('comments') || props.pdfLoading
                  }
                  style={{ width: '250px' }}
                  value={comments}
                  rows={4}
                  onChange={handleTextChange}
                  autoSize={{ minRows: 0, maxRows: 4 }}
                />
              </div>
            </Checkbox>
          </Space>
        </Checkbox.Group>
      </Modal>
    </>
  );
});

export default GeneratePDF;

const generatePDF = async (props) => {
  try {
    console.log('generatePDF props:', props);
    // PAGE 1
    await createHeader(props.pdfContent[0]);
    if (
      props.checkedRows.includes('Final Rent Opinion') &&
      props.checkedRows.includes('Final Sales Opinion')
    ) {
      createSubjectProperty({
        ...props.pdfContent[1],
        finalRentOpinion: props.priceOpinion,
        finalSalesPriceOpinion: props.salesPriceOpinion,
        comments: props.comments,
        userGroup: props.userGroup,
      });
    } else if (props.checkedRows.includes('Final Sales Opinion')) {
      createSubjectProperty({
        ...props.pdfContent[1],
        finalSalesPriceOpinion: props.salesPriceOpinion,
        comments: props.comments,
        userGroup: props.userGroup,
      });
    } else if (props.checkedRows.includes('Final Rent Opinion')) {
      createSubjectProperty({
        ...props.pdfContent[1],
        finalRentOpinion: props.priceOpinion,
        comments: props.comments,
        userGroup: props.userGroup,
      });
    } else {
      createSubjectProperty({
        ...props.pdfContent[1],
        comments: props.comments,
        userGroup: props.userGroup,
      });
    }

    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    doc.text(
      `Analysis Within ${props.pdfContent[2].radius} Miles Radius`,
      pdfPageWidth / 2,
      lastYAxisPos + pdfGap,
      'center',
    );
    setLastYAxisPos(lastYAxisPos + pdfGap);

    await createNearbyParcelOwnerSummary(props.pdfContent[3]);
    const medianDOM = props.pdfContent[1].medianDOM;
    createNearbyLeaseSummary(medianDOM.lease, props.pdfContent[4]);
    createNearbySaleSummary(medianDOM.sale, props.pdfContent[5]);

    currentPage = doc.internal.getCurrentPageInfo().pageNumber;

    // PAGE 2 AND UP
    doc.addPage(); // check if there is content available first
    setLastYAxisPos(0);

    // if (
    //   props.checkedRows.includes('Demographics')
    //   // &&
    //   // !isEmpty(props.subjectPropertyParcelData)
    // ) {
    //   createLocationScoresRow(props.pdfContent[12]);
    // }
    if (props.checkedRows.includes('SchoolScore')) {
      createSchoolScoreDemographicRow(props.pdfContent[15]);
    }

    if (props.checkedRows.includes('FavorableBrands')) {
      console.log('favorable 16', props.pdfContent[16]);
      createNearbyChainStoreRow(props.pdfContent[16]);
    }
    if (props.checkedRows.includes('Psychographics')) {
      createPsychographicsRow(props.pdfContent[17]);
    }

    createCompImagesSection(props.pdfContent[14]);

    if (
      props.checkedRows.includes('BT Owned Properties') &&
      props.pdfContent[6]
    ) {
      createTableRow(props.pdfContent[6]);
    }
    if (props.checkedRows.includes('MLSLease')) {
      createTableRow(props.pdfContent[9]);
    }
    if (props.checkedRows.includes('MLSSale')) {
      createTableRow(props.pdfContent[10]);
    }
    if (props.checkedRows.includes('LSPR')) {
      console.log('debug - createTableRow ');
      const content = {
        rowName: 'Last Sales From Public Record',
        rowStat: [
          {
            label: 'Total',
            content: `${props.lastSalePublicRecordSelectedRowKey.length}`,
          },
          {
            label: 'Median Last Sale Public Record',
            content: formatToCurrency(props.lastSalePublicRecordMedian),
          },
        ],
        tableData: props.lastSalePublicRecordDataForRender.filter((record) =>
          props.lastSalePublicRecordSelectedRowKey.includes(record.key),
        ),
      };
      createTableRow(content);
    }
    if (props.checkedRows.includes('National SFR Operators Listings')) {
      createTableRow(props.pdfContent[7]);
    }
    if (props.checkedRows.includes('Rental Listings from 3rd Party')) {
      createTableRow(props.pdfContent[8]);
    }
    if (
      props.checkedRows.includes('Multi-Family Properties') &&
      props.pdfContent[11]
    ) {
      createTableRow(props.pdfContent[11]);
    }

    if (
      props.checkedRows.includes('New Construction Homes') &&
      props.pdfContent[13]
    ) {
      createTableRow(props.pdfContent[13]);
    }
    createDisclaimer();

    await createFooter(props.pdfContent[0].clientInfo);

    return new Promise(function (resolve, reject) {
      const today = new Date();
      const dd = String(today.getDate()).padStart(2, '0');
      const mm = String(today.getMonth() + 1).padStart(2, '0'); //January is 0!
      const yyyy = today.getFullYear();
      const dateFormatted = mm + '-' + dd + '-' + yyyy;

      const address = props.pdfContent[1].propertyAddress.streetAddress;

      const clientID = props.pdfContent[0].clientInfo.shortName;

      const fileName = `${clientID} - CMA Report - ${address} - ${dateFormatted}`;
      console.log('FavorableBrands', fileName);
      resolve(fileName);
    });
  } catch (error) {
    console.log(error);
  }
};
