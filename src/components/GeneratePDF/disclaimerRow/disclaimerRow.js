import {
  doc,
  pdfPageWidth,
  pdfPageHeight,
  pdfSideMargin,
  pdfGap,
  pdfContentPadding,
  lastYAxisPos,
  setLastYAxisPos,
  footerHeight,
} from '../GeneratePDF';

export const createDisclaimer = async () => {
  const rowStartY = lastYAxisPos;
  let text, lineY;

  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  text = `THIS IS A COMPARATIVE MARKET ANALYSIS AND \nSHOULD NOT BE CONSIDERED AN APPRAISAL OR OPINION OF VALUE`;
  lineY = rowStartY + doc.getTextDimensions(text).h + 50;
  if (lineY <= pdfPageHeight - footerHeight) {
    doc.text(text, pdfPageWidth / 2, lineY, 'center');
  } else {
    doc.addPage();
    lineY = pdfSideMargin + pdfContentPadding * 2;
    doc.text(text, pdfPageWidth / 2, lineY, 'center');
  }
};
