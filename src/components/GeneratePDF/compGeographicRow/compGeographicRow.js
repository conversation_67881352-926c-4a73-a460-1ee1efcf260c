import {
  doc,
  pdfPageWidth,
  pdfPageHeight,
  pdfSideMargin,
  pdfGap,
  pdfContentPadding,
  footerHeight,
  lastYAxisPos,
  setLastYAxisPos,
} from '../GeneratePDF';

export const createCompImagesSection = (content) => {
  if (!content || content.length === 0) return;

  const spaceNeededForRow = 115;
  let topLeftAxis, topRightAxis;
  let rowStartY;

  // Check whether a new page needs to be added and set the y position
  if (
    pdfPageHeight - lastYAxisPos - (pdfSideMargin * 2 + 10) <
    spaceNeededForRow
  ) {
    rowStartY = pdfSideMargin * 2 + 10;
    doc.addPage();
  } else {
    rowStartY =
      lastYAxisPos + (lastYAxisPos === 0 ? pdfSideMargin * 2 + 10 : pdfGap); //+ 10;
  }

  topLeftAxis = [pdfSideMargin, rowStartY];
  topRightAxis = [pdfPageWidth - pdfSideMargin, rowStartY];

  doc.line(...topLeftAxis, ...topRightAxis); // Top border

  const contentStartX = pdfSideMargin + pdfContentPadding;
  const contentEndX = pdfPageWidth - pdfSideMargin - pdfContentPadding;
  let text, lineY, lineX, yIncrement;

  doc.setFontSize(14);
  doc.setFont('helvetica', 'normal');
  text = 'Comp Geographic Area';
  lineY = rowStartY + pdfContentPadding + doc.getTextDimensions(text).h - 2;
  doc.text(text, contentStartX, lineY);

  lineY = lineY + 5;
  const leftHeaderLineAxis = [contentStartX, lineY];
  const rightHeaderLineAxis = [contentEndX, lineY];
  doc.line(...leftHeaderLineAxis, ...rightHeaderLineAxis); // Header text underline

  let startY = lineY + 5;

  const pdfImgWidth = 199.5; // desired width on pdf

  // Map screenshot image
  const imageScreenshot = content[0];
  const screenshotHeight =
    imageScreenshot.height / (imageScreenshot.width / pdfImgWidth);
  const img1XPos = pdfSideMargin + pdfContentPadding;
  const img1YPos = startY + pdfContentPadding;
  const img1 = new Image();
  img1.src = imageScreenshot.src;
  doc.addImage(
    img1,
    'png',
    img1XPos,
    img1YPos,
    pdfImgWidth,
    screenshotHeight,
    '',
    'FAST',
  );
  // Mapbox API image
  const imageMapbox = content[1];
  const iamgeMapboxHeight =
    imageMapbox.height / (imageMapbox.width / pdfImgWidth);
  const img2XPos =
    pdfSideMargin + pdfContentPadding + pdfImgWidth + pdfContentPadding;
  const img2YPos = startY + pdfContentPadding;
  const img2 = new Image();
  img2.src = imageMapbox.src;
  doc.addImage(
    img2,
    'png',
    img2XPos,
    img2YPos,
    pdfImgWidth,
    iamgeMapboxHeight,
    '',
    'FAST',
  );

  lineY = startY + pdfContentPadding + screenshotHeight + pdfContentPadding;

  const bottomLeftAxis = [pdfSideMargin, lineY];
  const bottomRightAxis = [pdfPageWidth - pdfSideMargin, lineY];

  doc.setDrawColor(0, 0, 0);

  doc.line(...topLeftAxis, ...bottomLeftAxis); // Left Border
  doc.line(...topRightAxis, ...bottomRightAxis); // Right Border
  doc.line(...bottomRightAxis, ...bottomLeftAxis); // Bottom border

  setLastYAxisPos(lineY);
};
