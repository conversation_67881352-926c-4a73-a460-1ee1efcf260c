import autoTable from 'jspdf-autotable';
import {
  doc,
  pdfPageWidth,
  pdfPageHeight,
  pdfSideMargin,
  pdfGap,
  pdfContentPadding,
  footerHeight,
  lastYAxisPos,
  setLastYAxisPos,
} from '../GeneratePDF';

export const createLocationScoresRow = (content) => {
  if (!content || !content.demographics || !content.school || !content.risk)
    return;

  const spaceNeededForRow = 115;
  let topLeftAxis, topRightAxis;
  let rowStartY;

  // Check whether a new page needs to be added and set the y position
  if (
    pdfPageHeight - lastYAxisPos - (pdfSideMargin * 2 + 10) <
    spaceNeededForRow
  ) {
    rowStartY = pdfSideMargin * 2 + 10;
    doc.addPage();
  } else {
    rowStartY =
      lastYAxisPos + (lastYAxisPos === 0 ? pdfSideMargin * 2 + 10 : pdfGap); //+ 10;
  }

  topLeftAxis = [pdfSideMargin, rowStartY];
  topRightAxis = [pdfPageWidth - pdfSideMargin, rowStartY];

  doc.line(...topLeftAxis, ...topRightAxis); // Top border

  const contentStartX = pdfSideMargin + pdfContentPadding;
  const contentEndX = pdfPageWidth - pdfSideMargin - pdfContentPadding;
  let text, lineY, lineX, yIncrement;

  doc.setFontSize(14);
  doc.setFont('helvetica', 'normal');
  text = 'Demographics';
  lineY = rowStartY + pdfContentPadding + doc.getTextDimensions(text).h - 2;
  doc.text(text, contentStartX, lineY);

  lineY = lineY + 5;
  const leftHeaderLineAxis = [contentStartX, lineY];
  const rightHeaderLineAxis = [contentEndX, lineY];
  doc.line(...leftHeaderLineAxis, ...rightHeaderLineAxis); // Header text underline

  const tableHeaderRowHeight = 12.5;
  const tableBodyRowHeight = 15;

  let startY = lineY + 5;

  // console.log(pdfPageWidth);
  // page width(459) - two side margin(2*25) - two spacing (2*10) / 3 (tables) = 129.6 per table
  const tableWidth = 129.6;

  const table1Start = pdfSideMargin + pdfContentPadding;
  const table1End =
    pdfPageWidth - (pdfSideMargin + pdfContentPadding + tableWidth);
  const table2Start = table1Start + tableWidth + pdfContentPadding;
  const table2End = table1End - pdfContentPadding - tableWidth;
  const table3Start = table2Start + tableWidth + pdfContentPadding;
  const table3End = pdfSideMargin + pdfContentPadding;

  autoTable(doc, {
    headStyles: {
      fillColor: [229, 229, 229],
      textColor: [0, 0, 0],
      minCellHeight: tableHeaderRowHeight,
      valign: 'middle',
      lineWidth: 1,
      // cellPadding: content.rowName === 'Multi-Family' || 2,
    },
    bodyStyles: {
      fontSize: 8,
      minCellHeight: tableBodyRowHeight,
      valign: 'middle',
      lineWidth: 1,
      // cellPadding: content.rowName === 'Multi-Family' || 2,
    },
    startY: startY,
    margin: {
      left: pdfSideMargin + pdfContentPadding,
      right: pdfPageWidth - (pdfSideMargin + pdfContentPadding + tableWidth),
    },
    head: getTableHeaderData('School Scores'),
    body: content.school,
  });

  autoTable(doc, {
    headStyles: {
      fillColor: [229, 229, 229],
      textColor: [0, 0, 0],
      minCellHeight: tableHeaderRowHeight,
      valign: 'middle',
      lineWidth: 1,
      // cellPadding: content.rowName === 'Multi-Family' || 2,
    },
    bodyStyles: {
      fontSize: 8,
      minCellHeight: tableBodyRowHeight,
      valign: 'middle',
      lineWidth: 1,
      // cellPadding: content.rowName === 'Multi-Family' || 2,
    },
    startY: startY,
    margin: {
      left: table2Start,
      right: table2End,
    },
    head: getTableHeaderData('Risk Factors'),
    body: content.risk,
  });

  autoTable(doc, {
    headStyles: {
      fillColor: [229, 229, 229],
      textColor: [0, 0, 0],
      minCellHeight: tableHeaderRowHeight,
      valign: 'middle',
      lineWidth: 1,
      // cellPadding: content.rowName === 'Multi-Family' || 2,
    },
    bodyStyles: {
      fontSize: 8,
      minCellHeight: tableBodyRowHeight,
      valign: 'middle',
      lineWidth: 1,
      // cellPadding: content.rowName === 'Multi-Family' || 2,
    },
    startY: startY,
    margin: {
      left: table3Start,
      right: table3End,
    },
    head: getTableHeaderData('Demographics'),
    body: content.demographics,
  });

  lineY =
    startY +
    tableHeaderRowHeight +
    4 * tableBodyRowHeight +
    pdfContentPadding +
    4;

  const bottomLeftAxis = [pdfSideMargin, lineY];
  const bottomRightAxis = [pdfPageWidth - pdfSideMargin, lineY];

  doc.setDrawColor(0, 0, 0);

  doc.line(...topLeftAxis, ...bottomLeftAxis); // Left Border
  doc.line(...topRightAxis, ...bottomRightAxis); // Right Border
  doc.line(...bottomRightAxis, ...bottomLeftAxis); // Bottom border

  setLastYAxisPos(lineY);
};

const getTableHeaderData = (tableName) => {
  if (tableName === 'School Scores') {
    return [
      [
        {
          colSpan: 2,
          content: 'School Scores',
          styles: { halign: 'center' },
        },
      ],
    ];
  } else if (tableName === 'Risk Factors') {
    return [
      [
        {
          colSpan: 2,
          content: 'Risk Factors',
          styles: { halign: 'center' },
        },
      ],
    ];
  } else if (tableName === 'Demographics') {
    return [
      [
        {
          colSpan: 2,
          content: 'Demographics',
          styles: { halign: 'center' },
        },
      ],
    ];
  }
};

// const school = [
//   ['Elementary', 7],
//   ['Middle', 9],
//   ['High', 8],
// ];

// const risk = [
//   ['Flood Zone', 'No'],
//   ['Crime Score', '2'],
// ];

// const demo = [
//   ['Median HH Income', '$91,162'],
//   ['5-Year Population Growth', '-2%'],
//   ['5-Year Income Growth', '16%'],
//   ["Bachelor's and Above", '28%'],
// ];

// const getTableBodyData = (tableName, tableData) => {
//   if (tableName === 'School Scores') {
//     return school;
//   } else if (tableName === 'Risk Factors') {
//     return risk;
//   } else if (tableName === 'Demographics') {
//     return demo;
//   }
// };
