import { Button } from 'antd';
import React from 'react';
import { AiOutlineClose } from 'react-icons/ai';

export const LandParcelSearch = ({ onClose, children }: any) => {
  const handleClose = () => {
    // TODO: reset land parcel search state
    onClose();
  };

  return (
    <Container>
      <Header handleClose={handleClose} />
      <div
        style={{
          height: '100%',
          minHeight: 0,
          display: 'flex',
          flexDirection: 'row',
        }}
      >
        {children}
      </div>
    </Container>
  );
};

const Container = ({ children }: { children: React.ReactNode }) => {
  return (
    <div
      style={{
        backgroundColor: 'white',
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        // width: '500px',
      }}
    >
      {children}
    </div>
  );
};

const Header = ({ handleClose }: { handleClose: () => void }) => {
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        padding: '10px 20px',
        borderBottom: '1px solid #ccc',
      }}
    >
      <h2 style={{ fontSize: '18px', fontWeight: 500, margin: 0 }}>
        Land Search
      </h2>
      <Button
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
        type="text"
        shape="circle"
        onClick={handleClose}
      >
        <AiOutlineClose />
      </Button>
    </div>
  );
};
