.ant-select-item-option-content {
  white-space: break-spaces;
  word-break: break-word;
}

.landDev-input-number .ant-input-number-handler-wrap {
  opacity: 1 !important;
}

.ResultDisplay-view-dropdown .ant-dropdown-menu {
  max-height: 300px;
  overflow-y: auto;
}

.ResultDisplay-row:hover {
  background-color: #e6f7ff;
  cursor: pointer;
}

#land-parcel-search-result-panel {
  position: relative;
}

.result-detail-modal-wrapper {
  position: absolute !important;
  z-index: 990 !important;
  overflow: hidden !important;
}
.result-detail-modal-wrapper .ant-modal-content {
  height: 100% !important;
  padding: 0 !important;
}
.result-detail-modal-wrapper div[tabindex='-1'] {
  height: 100% !important;
}

#land-parcel-search-result-panel .ant-tabs-top > .ant-tabs-nav::before {
  border: none !important;
}

#land-parcel-search-result-panel .ant-tabs-nav {
  margin: 0 !important;
}
#land-parcel-search-result-panel .ant-tabs-content.ant-tabs-content-top,
#land-parcel-search-result-panel .ant-tabs-tabpane.ant-tabs-tabpane-active,
#land-parcel-search-result-panel .ant-tabs-tabpane .ant-spin-nested-loading,
#land-parcel-search-result-panel .ant-tabs-tabpane .ant-spin-container {
  height: 100% !important;
}

#land-parcel-search-mls-listing-collapse .ant-collapse-expand-icon {
  padding: 10px;
}

#land-parcel-search .ant-collapse-expand-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
}
