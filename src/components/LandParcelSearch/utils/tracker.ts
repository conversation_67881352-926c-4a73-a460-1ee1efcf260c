import { postLandSearchCSVExportTracking } from '@/services/data';
import { getUserEmail, getUserGroup, getUsername } from '@/utils/auth';

const hashData = async (input: string) => {
  const encoder = new TextEncoder();
  const dataBuffer = encoder.encode(input);
  const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map((byte) => byte.toString(16).padStart(2, '0')).join('');
};

export async function trackLandSearchCSVExport(
  type: 'parcel' | 'mls' | 'showcase',
  dataToExport: any[],
) {
  if (process.env.NODE_ENV === 'development') return true;

  try {
    const payload = {
      app_name: 'CMA',
      user_id: await getUsername(),
      user_email: await getUserEmail(),
      user_group: ((await getUserGroup()) as string[])[0],
      land_search_type: type,
      row_count: dataToExport.length,
      export_hash: await hashData(JSON.stringify(dataToExport)),
    };

    const response = await postLandSearchCSVExportTracking({ body: payload });

    // @ts-ignore
    if (response === 'Success') return true;
    return false;
  } catch (error) {
    console.error('Error tracking land search csv export', error);
  }
  return false;
}
