// utils/formatting.ts

interface NumberFormatterParams {
  value: number;
  style?: 'decimal' | 'currency' | 'percent' | 'unit';
  unit?:
    | 'acre'
    | 'meter'
    | 'kilometer'
    | 'inch'
    | 'foot'
    | 'yard'
    | 'mile'
    | 'degree'; // for more: https://tc39.es/ecma402/#table-sanctioned-single-unit-identifiers
  minDecimals?: number;
  maxDecimals?: number;
}

const formatDollar = ({
  value,
  minDecimals,
  maxDecimals,
}: NumberFormatterParams) => {
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: minDecimals,
    maximumFractionDigits: maxDecimals,
  });
  return formatter.format(value);
};

const formatPercent = ({
  value,
  minDecimals,
  maxDecimals,
}: NumberFormatterParams) => {
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'percent',
    minimumFractionDigits: minDecimals,
    maximumFractionDigits: maxDecimals,
  });
  return formatter.format(value);
};

const formatNumber = ({
  value,
  style = 'decimal',
  unit,
  minDecimals,
  maxDecimals,
}: NumberFormatterParams) => {
  if (style === 'unit' && !unit)
    throw new Error('unit is required for style=unit');

  const formatParams = {
    style: style,
    minimumFractionDigits: minDecimals,
    maximumFractionDigits: maxDecimals,
  } as any;

  if (style === 'unit') {
    formatParams['unit'] = unit;
  }

  const formatter = new Intl.NumberFormat('en-US', {
    style: style,
    minimumFractionDigits: minDecimals,
    maximumFractionDigits: maxDecimals,
    unit,
  });
  return formatter.format(value);
};
// length-mile

export const valueFormatter = (column: string, value: any) => {
  try {
    value = isNaN(value) ? value : Number(value);

    if (
      [
        'improvement_value',
        'total_value',
        'assessment_value_per_acre',
        'housing_value',
        'medianhhincome',
        'median_rent',
        'median_home_value',
      ].includes(column)
    ) {
      return formatDollar({ value, maxDecimals: 0 });
    }

    if (['improvement_ratio', 'building_coverage'].includes(column))
      return formatPercent({ value: value, maxDecimals: 2 });

    if (['flood_coverage'].includes(column))
      return formatPercent({ value: value / 100, maxDecimals: 0 });

    if (['avg_school'].includes(column))
      return formatNumber({ value, maxDecimals: 2 });

    if (['area_acres'].includes(column))
      return formatNumber({
        value,
        minDecimals: 2,
        maxDecimals: 2,
        style: 'unit',
        unit: 'acre',
      });

    if (
      [
        'nearest_highway_distance_miles',
        'nearest_powerline_distance_miles',
      ].includes(column)
    )
      return formatNumber({
        value,
        minDecimals: 2,
        maxDecimals: 2,
        style: 'unit',
        unit: 'mile',
      });

    if (['population'].includes(column))
      return formatNumber({ value, maxDecimals: 0 });

    if (['mls_listing'].includes(column)) return value;

    if (['sl_land_source'].includes(column)) {
      if (value === 'parcel') return 'Parcel';
      if (value === 'mls') return 'MLS';
      if (value === 'showcase') return 'Showcase';
    }

    if (['mean_slope', 'mean_aspect'].includes(column)) {
      return formatNumber({
        value,
        minDecimals: 0,
        maxDecimals: 2,
        style: 'unit',
        unit: 'degree',
      });
    }

    return value;
  } catch (e) {
    console.error(`Error in valueFormatter(${column}, ${value})`, e);
    throw e;
  }
};

export const valueParser = (column: string, value: any) => {
  try {
    value = isNaN(value) ? value : parseFloat(value);

    if (
      [
        'improvement_value',
        'total_value',
        'assessment_value_per_acre',
        'housing_value',
        'medianhhincome',
        'median_rent',
        'median_home_value',
      ].includes(column)
    ) {
      return formatDollar({ value, maxDecimals: 0 });
    }

    if (['improvement_ratio', 'building_coverage'].includes(column))
      return formatPercent({ value, maxDecimals: 2 });

    if (['flood_coverage'].includes(column))
      return formatPercent({ value: value / 100, maxDecimals: 0 });

    if (['avg_school'].includes(column))
      return formatNumber({ value, maxDecimals: 2 });

    if (['area_acres'].includes(column))
      return formatNumber({
        value,
        minDecimals: 2,
        maxDecimals: 2,
        style: 'unit',
        unit: 'acre',
      });

    if (
      [
        'nearest_highway_distance_miles',
        'nearest_powerline_distance_miles',
      ].includes(column)
    )
      return formatNumber({
        value,
        minDecimals: 2,
        maxDecimals: 2,
        style: 'unit',
        unit: 'mile',
      });

    if (['population'].includes(column))
      return formatNumber({ value, maxDecimals: 0 });

    if (['mean_slope', 'mean_aspect'].includes(column)) {
      return formatNumber({
        value,
        minDecimals: 0,
        maxDecimals: 2,
        style: 'unit',
        unit: 'degree',
      });
    }

    return value;
  } catch (e) {
    console.error(`Error in valueFormatter(${column}, ${value})`, e);
    throw e;
  }
};
