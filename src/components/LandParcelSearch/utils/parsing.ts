// prettier-ignore
type ParseUnit = 'ac' | 'acre' | 'm' | 'meter' | 'km' | 'kilometer' | 'in' | 'inch' | 'ft' | 'foot' | 'yd' | 'yard' | 'mi' | 'mile';

interface ParsedNumberResult {
  value: number;
  style?: 'decimal' | 'currency' | 'percent' | 'unit';
  unit?: ParseUnit;
}

export const valueParser = (formattedString: string): ParsedNumberResult => {
  try {
    // Remove common formatting characters
    const numericString = formattedString.replace(/[^0-9.-]+/g, '');
    const value = Number(numericString);

    if (isNaN(value)) return { value: NaN };

    // Infer style and unit based on original string characteristics
    let style: 'decimal' | 'currency' | 'percent' | 'unit' | undefined;
    let unit: ParseUnit | undefined;

    if (formattedString.includes('$')) {
      style = 'currency';
    } else if (formattedString.includes('%')) {
      style = 'percent';
    } else if (
      formattedString.match(
        /ac|acre|m|meter|km|kilometer|in|inch|ft|foot|yd|yard|mi|mile/,
      )
    ) {
      style = 'unit';
      const unitMatch = formattedString.match(
        /ac|acre|m|meter|km|kilometer|in|inch|ft|foot|yd|yard|mi|mile/,
      );
      if (unitMatch) unit = unitMatch[0] as any;
    } else {
      style = 'decimal';
    }

    return {
      value: style === 'percent' ? value / 100 : value,
      style,
      unit,
    };
  } catch (e) {
    console.error('Error parsing formatted number', e);
    return { value: NaN };
  }
};

// Example usage
const parsed = valueParser('$1,000');
console.log(parsed); // { value: 1000, style: 'currency' }

const parsedPercent = valueParser('50%');
console.log(parsedPercent); // { value: 0.5, style: 'percent' }

const parsedUnit = valueParser('100 ac');
console.log(parsedUnit); // { value: 100, style: 'unit', unit: 'acre' }
