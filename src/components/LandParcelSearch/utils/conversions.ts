import { geometryCollection, multiPolygon, polygon } from '@turf/helpers';
import turf_union from '@turf/union';
import { GeometryCollection } from 'geojson';

export const mergeGeometryCollectionPolygons = (
  geometryCollection: GeometryCollection,
) => {
  try {
    const { geometries } = geometryCollection;

    let merged = null;

    for (let i = 0; i < geometries.length - 1; i++) {
      let toMerge = null;
      const geom = geometries[i];

      if (geom.type === 'Polygon') {
        toMerge = polygon(geom.coordinates);
      } else if (geom.type === 'MultiPolygon') {
        toMerge = multiPolygon(geom.coordinates);
      }

      merged = merged && toMerge ? turf_union(merged, toMerge) : toMerge;
    }

    if (merged) {
      return merged.geometry;
    }

    console.log('No geometry to merge in geometryCollection');
    return geometryCollection;
  } catch (e) {
    console.error('Error merging geometryCollection', e);
    throw e;
  }
};

export const getGeometryFromGeometryCollection = ({
  type,
  geometryCollection,
}: {
  type: 'Point' | 'Polygon';
  geometryCollection: GeometryCollection;
}) => {
  if (geometryCollection.type !== 'GeometryCollection') {
    console.error('Not a GeometryCollection');
    return null;
  }

  const { geometries } = geometryCollection;

  if (type === 'Point') {
    for (let i = 0; i < geometries.length; i++) {
      if (geometries[i].type === 'Point') {
        return geometries[i];
      }
    }
  }

  const polygonsToMerge = [];
  for (let i = 0; i < geometries.length; i++) {
    if (['Polygon', 'MultiPolygon'].includes(geometries[i].type)) {
      polygonsToMerge.push(geometries[i]);
    }
  }
  if (polygonsToMerge.length === 0) {
    console.error('No polygons to merge');
    return null;
  }

  return polygonsToMerge.length > 1
    ? mergeGeometryCollectionPolygons({
        type: 'GeometryCollection',
        geometries: polygonsToMerge,
      })
    : polygonsToMerge[0];
};

export const landDataToFeatures = (landData: any): { features: any[] } => {
  const features = [];

  for (let i = 0; i < landData.length; i++) {
    const land = landData[i];

    const { geog, boundary, ...properties } = land;

    if (!geog) {
      console.log('No geometry for land parcel', land);
      continue;
    }

    let geometry = geog;
    if (boundary) {
      if (boundary.type === 'GeometryCollection') {
        geometry = {
          type: 'GeometryCollection',
          geometries: [geog, mergeGeometryCollectionPolygons(boundary)],
        };
      } else {
        geometry = {
          type: 'GeometryCollection',
          geometries: [geog, boundary],
        };
      }
    }

    const feature = {
      type: 'Feature',
      properties: properties,
      geometry: geometry,
    };

    features.push(feature);
  }
  return { features: features };
};
