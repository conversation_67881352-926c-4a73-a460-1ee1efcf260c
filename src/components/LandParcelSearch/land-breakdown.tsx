import {
  getOwnersOtherParcels,
  getParcelAVMData,
  getParcelDetailData,
  getPropertyTaxData,
  getTaxProperDetailsData,
  getZoningDetailsData,
  postSkipTrace,
} from '@/services/data';
import { AimOutlined } from '@ant-design/icons';
import {
  Button,
  Card,
  Collapse,
  Divider,
  List,
  Segmented,
  Spin,
  Tooltip,
} from 'antd';

import { isEmpty } from 'lodash';
import mapboxgl from 'mapbox-gl';
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { BiMapPin } from 'react-icons/bi';
import { MdOutlineDocumentScanner } from 'react-icons/md';
import { useQuery } from 'react-query';
import { useSelector } from 'umi';
import markerImg from '../../assets/images/mapbox/marker/mapbox-marker-icon-20px-red.png';
import { useLandParcelSearch } from './context';
import ParcelBreakdown, { organizeParcelData } from './ParcelBreakdown';
import { useResultDisplay } from './ResultDisplay';

interface LandBreakdownContext {
  currentView:
    | 'MAIN'
    | 'ZONEOMICS'
    | 'CONTACT'
    | 'OWNERSOTHERPARCELS'
    | 'TAXPROPER';
  setCurrentView: React.Dispatch<
    React.SetStateAction<
      'MAIN' | 'ZONEOMICS' | 'CONTACT' | 'OWNERSOTHERPARCELS' | 'TAXPROPER'
    >
  >;
  zoneomicsData: any;
  setZoneomicsData: React.Dispatch<React.SetStateAction<any>>;
}
const Context = createContext<LandBreakdownContext | undefined>(undefined);

interface LandBreakdownProps {
  openDetails: boolean;
  children: React.ReactNode;
}
export const LandBreakdownProvider = ({
  openDetails,
  children,
}: LandBreakdownProps) => {
  const [currentView, setCurrentView] = useState<
    'MAIN' | 'ZONEOMICS' | 'CONTACT' | 'OWNERSOTHERPARCELS' | 'TAXPROPER'
  >('MAIN');
  const [zoneomicsData, setZoneomicsData] = useState(null);

  useEffect(() => {
    return () => {
      setCurrentView('MAIN');
      setZoneomicsData(null);
    };
  }, []);

  useEffect(() => {
    if (!openDetails && currentView === 'ZONEOMICS') {
      setCurrentView('MAIN');
    }
  }, [openDetails]);

  useEffect(() => {
    const csvBtn = document.querySelector(
      '#parcel-breakdown-export-to-csv',
    ) as HTMLElement;
    if (!csvBtn) return;
    if (
      ['ZONEOMICS', 'TAXPROPER'].includes(currentView) &&
      csvBtn.style.visibility !== 'hidden'
    ) {
      csvBtn.style.visibility = 'hidden';
    } else if (currentView === 'MAIN' && csvBtn.style.visibility === 'hidden') {
      csvBtn.style.visibility = 'visible';
    }
  }, [currentView]);

  const context = useMemo(
    () => ({
      currentView,
      setCurrentView,
      zoneomicsData,
      setZoneomicsData,
    }),
    [currentView, setCurrentView, zoneomicsData, setZoneomicsData],
  );

  return <Context.Provider value={context}>{children}</Context.Provider>;
};

export const useLandBreakdown = () => {
  const context = useContext(Context);
  if (!context) {
    throw new Error(
      'useLandBreakdown must be used within a LandBreakdownProvider',
    );
  }
  return context;
};

export const LandBreakdownContent = ({ data, isLoading }: any) => {
  const { currentView, setCurrentView } = useLandBreakdown();
  console.log('test raw parcel data', data);
  if (!isLoading && data) {
    let coordinates = data.latitude
      ? [data.latitude, data.longitude]
      : [Number(data.lat), Number(data.lon)];
    let address = null;

    if (data) {
      address = `${data?.address || ''}, ${data?.scity || ''}, ${
        data?.state || ''
      } ${data?.zip_code || ''}`;

      if (address.trim() === ', , ') {
        address = null;
      } else {
        address = address.trim() + ', USA';
      }
    }

    if (currentView === 'MAIN') {
      return <ParcelBreakdown data={data} />;
    } else if (currentView === 'ZONEOMICS') {
      return <Zoneomics coordinates={coordinates} />;
    } else if (currentView === 'TAXPROPER') {
      return (
        <ErrorBoundary
          fallback={
            <div>
              <div className="mb-1">
                <Button
                  type="link"
                  className="p-0"
                  onClick={() => setCurrentView('MAIN')}
                >
                  Back
                </Button>
              </div>
              <div className="px-4">
                <div>Something went wrong.</div>
              </div>
            </div>
          }
        >
          <TaxProper address={address} coordinates={coordinates} />
        </ErrorBoundary>
      );
    } else if (currentView === 'CONTACT') {
      return <ContactDetails parcel={data} />;
    } else if (currentView === 'OWNERSOTHERPARCELS') {
      return <OtherParcelsDetails parcel={data} />;
    }
  }

  return null;
};

const Zoneomics = ({ coordinates }: { coordinates: number[] }) => {
  const { zoneomicsData, setZoneomicsData } = useLandBreakdown();
  const { data, isError, isLoading, refetch } = useQuery(
    ['parcel-breakdown-zoning-details', coordinates[0], coordinates[1]],
    async () =>
      await getZoningDetailsData({
        lat: coordinates[0],
        lng: coordinates[1],
      }),
    {
      enabled: coordinates && coordinates.length > 0,
      staleTime: 1000 * 60 * 60 * 24 * 1,
    },
  );

  useEffect(() => {
    if (data) {
      setZoneomicsData(data);
    }
  }, [data]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-[300px] p-[24px]">
        <Spin tip="loading" size="large" />
      </div>
    );
  } else if (isError) {
    return (
      <div className="flex flex-col gap-2 p-[24px]">
        <span>Failed to fetch data.</span>
        <Button type="text" onClick={() => refetch()}>
          Try again?
        </Button>
      </div>
    );
  } else {
    if (!isError && !isLoading && (data != null || zoneomicsData != null)) {
      return <ZoneomicsContent zoneData={zoneomicsData} />;
    } else {
      return <div>No data available.</div>;
    }
  }
};

const ZoneomicsContent = ({ zoneData }: { zoneData: any }) => {
  const { setCurrentView } = useLandBreakdown();
  const [activeTab, setActiveTab] = useState('zone_details');
  const [controlItems, setControlItems] = useState([]);

  useEffect(() => {
    if (!zoneData || !zoneData.data || !zoneData.data.controls) return;
    const controls = zoneData.data.controls;
    const controlItems = Object.entries(controls).reduce(
      (acc: any, [key, value]: any) => {
        if (!value) return acc;
        const standard = value.standard;
        const nonStandard = value['non-standard'];
        const standardItems = !standard
          ? []
          : Object.entries(standard).reduce((acc: any, [key, value]) => {
              acc.push({ key, value });
              return acc;
            }, []);
        const nonStandardItems = !nonStandard
          ? []
          : Object.entries(nonStandard).reduce((acc: any, [key, value]) => {
              acc.push({ key, value });
              return acc;
            }, []);
        acc.push({
          key: key,
          label: (
            <span className="font-semibold text-base">
              {key.split('_').join(' ').toUpperCase()}
            </span>
          ),

          children: (
            <div className="flex flex-col pt-1 px-5 pb-5 gap-2">
              {standardItems.length > 0 || nonStandardItems.length > 0 ? (
                <>
                  {standardItems.map((item: any, idx: number) => {
                    return (
                      <div key={idx} className="flex flex-col gap-1">
                        <span className="font-semibold text-sm">
                          {item.key.split('_').join(' ').toUpperCase()}
                        </span>
                        <span>{item.value ? item.value : 'N/A'}</span>
                      </div>
                    );
                  })}
                  {nonStandardItems.map((item: any, idx: number) => {
                    return (
                      <div key={idx} className="flex flex-col gap-1">
                        <span className="font-semibold text-sm">
                          {item.key.split('_').join(' ').toUpperCase()}
                        </span>
                        <span>{item.value ? item.value : 'N/A'}</span>
                      </div>
                    );
                  })}
                </>
              ) : (
                <span>No data available.</span>
              )}
            </div>
          ),
        });
        return acc;
      },
      [],
    );

    setControlItems(controlItems);
  }, [zoneData]);

  const zoneRender = useCallback((key: any, value: any) => {
    if (key === 'link')
      return (
        <a href={value} target="_blank">
          Click to Open
        </a>
      );
    return <span>{value ? value : 'N/A'}</span>;
  }, []);

  const checkIfAnyPermitted = useCallback(() => {
    if (!zoneData || !zoneData.data) return false;
    if (zoneData.data.permitted_land_uses) {
      return [
        'single_family_permitted',
        'two_family_permitted',
        'multi_family_permitted',
        'commercial_uses_permitted',
        'industrial_uses_permitted',
        'adu_local_permitted',
        'adu_state_permitted',
        'short_term_rentals_permitted',
      ].some((key) => zoneData.data.permitted_land_uses[key] === true);
    }
    return false;
  }, [zoneData]);

  if (!zoneData || !zoneData.data) return null;
  return (
    <>
      <div
        className="sticky top-0 z-10 px-[24px] pb-[24px] bg-white"
        style={{ borderBottom: '1px solid #ddd' }}
      >
        <div className="mb-1">
          <Button
            type="link"
            className="p-0"
            onClick={() => setCurrentView('MAIN')}
          >
            Back
          </Button>
        </div>

        <Segmented
          value={activeTab}
          onChange={(value) => setActiveTab(value)}
          block
          options={[
            { label: 'Zone Details', value: 'zone_details' },
            { label: 'Permitted Uses', value: 'permitted_land_uses' },
            { label: 'Controls', value: 'controls' },
          ]}
        />
      </div>
      <div
        className="flex flex-col gap-8 pt-4 px-[24px] pb-[24px]"
        style={{ color: '#333' }}
      >
        {activeTab === 'zone_details' && (
          <>
            {zoneData && zoneData.data && zoneData.data.zone_details ? (
              <div className="flex flex-col gap-6">
                {Object.entries(zoneData.data.zone_details).map(
                  ([key, value], idx) => (
                    <div key={idx} className="flex flex-col gap-2">
                      <span className="font-semibold text-base">
                        {key.split('_').join(' ').toUpperCase()}
                      </span>
                      {zoneRender(key, value)}
                    </div>
                  ),
                )}
              </div>
            ) : (
              <div>No zone data available.</div>
            )}
          </>
        )}
        {activeTab === 'permitted_land_uses' && (
          <>
            {zoneData.data.permitted_land_uses ? (
              <div className="flex flex-col gap-6">
                <div className="flex flex-col gap-2">
                  <span className="font-semibold text-base uppercase">{`What's permitted?`}</span>
                  {checkIfAnyPermitted() ? (
                    <ul className="pl-4 mt-0">
                      {[
                        'single_family_permitted',
                        'two_family_permitted',
                        'multi_family_permitted',
                        'commercial_uses_permitted',
                        'industrial_uses_permitted',
                        'adu_local_permitted',
                        'adu_state_permitted',
                        'short_term_rentals_permitted',
                      ].reduce((acc: any, key: any) => {
                        if (zoneData.data.permitted_land_uses[key] === true) {
                          acc.push(
                            <li key={key} className="mb-1">
                              {key
                                .split('_')
                                .filter((str: string) => str != 'permitted')
                                .join(' ')}
                            </li>,
                          );
                        }
                        return acc;
                      }, [])}
                    </ul>
                  ) : (
                    <span>No permitted uses available.</span>
                  )}
                </div>
                {zoneData.data.permitted_land_uses.as_of_right && (
                  <div className="flex flex-col gap-2">
                    <span className="font-semibold text-base uppercase">{`As of right`}</span>
                    {zoneData.data.permitted_land_uses.as_of_right.length >
                    0 ? (
                      <ul className="pl-4 mt-0">
                        {zoneData.data.permitted_land_uses.as_of_right.map(
                          (use: any, idx: number) => (
                            <li key={idx} className="mb-1">
                              {use}
                            </li>
                          ),
                        )}
                      </ul>
                    ) : (
                      <span>No as of right available.</span>
                    )}
                  </div>
                )}
                {zoneData.data.permitted_land_uses.conditional_uses && (
                  <div className="flex flex-col gap-2">
                    <span className="font-semibold text-base uppercase">{`Conditional uses`}</span>
                    {zoneData.data.permitted_land_uses.conditional_uses.length >
                    0 ? (
                      <ul className="pl-4 mt-0">
                        {zoneData.data.permitted_land_uses.conditional_uses.map(
                          (use: any, idx: number) => (
                            <li key={idx} className="mb-1">
                              {use}
                            </li>
                          ),
                        )}
                      </ul>
                    ) : (
                      <span>No conditional uses available.</span>
                    )}
                  </div>
                )}
                {zoneData.data.permitted_land_uses.prohibited && (
                  <div className="flex flex-col gap-2">
                    <span className="font-semibold text-base uppercase">{`Prohibited`}</span>
                    {zoneData.data.permitted_land_uses.prohibited.length > 0 ? (
                      <ul className="pl-4 mt-0">
                        {zoneData.data.permitted_land_uses.prohibited.map(
                          (use: any, idx: number) => (
                            <li key={idx} className="mb-1">
                              {use}
                            </li>
                          ),
                        )}
                      </ul>
                    ) : (
                      <span>No prohibited uses available.</span>
                    )}
                  </div>
                )}
              </div>
            ) : (
              <div>No land use data available.</div>
            )}
          </>
        )}
        {activeTab === 'controls' && (
          <>
            {zoneData.data.controls ? (
              <div className="flex flex-col gap-6">
                <Collapse
                  items={controlItems}
                  defaultActiveKey={[...Object.keys(zoneData.data.controls)]}
                  bordered={false}
                  ghost
                />
              </div>
            ) : (
              <div>No controls data available.</div>
            )}
          </>
        )}
      </div>
    </>
  );
};
const ContactDetails = ({ parcel }: any) => {
  const { setCurrentView } = useLandBreakdown();

  console.log('parcelInfo', parcel);

  const { data, isError, isLoading, refetch } = useQuery(
    ['parcel-breakdown-contact-trace', parcel?.parcelnumb],
    () =>
      postSkipTrace({
        body: {
          requests: [
            {
              propertyAddress: {
                city: parcel.scity,
                street: `
                ${parcel.street_number ?? ''} 
                ${parcel.saddpref ?? ''} 
                ${parcel.street_name ?? ''} ${parcel.street_suffix ?? ''}`,
                state: parcel.state,
                zip: parcel.szip5,
              },
            },
          ],
        },
      }),
    {
      enabled: false,
      staleTime: 1000 * 60 * 60 * 24 * 3,
    },
  );
  useEffect(() => {
    if (!data) {
      refetch();
    }
  }, []);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-[300px] p-[24px]">
        <Spin tip="loading" size="large" />
      </div>
    );
  } else if (isError) {
    return (
      <div className="flex flex-col gap-2 p-[24px]">
        <span>Failed to fetch data.</span>
        <Button type="text" onClick={() => refetch()}>
          Try again?
        </Button>
      </div>
    );
  } else {
    if (!isError && !isLoading && data != null) {
      return <ContactDetailsContent contactInfo={data.results} />;
    } else {
      return (
        <>
          <div className="mb-1">
            <Button
              type="link"
              className="p-0"
              onClick={() => setCurrentView('MAIN')}
            >
              Back
            </Button>
          </div>
          <div>No data available.</div>
        </>
      );
    }
  }
};
const ContactDetailsContent = ({ contactInfo }: any) => {
  const { setCurrentView } = useLandBreakdown();
  const [activeTab, setActiveTab] = useState('contact');

  const formatPhoneNumber = (phoneNumber: string) => {
    const cleaned = phoneNumber.replace(/\D/g, '');
    const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
    if (match) {
      return `${match[1]}-${match[2]}-${match[3]}`;
    }
    return phoneNumber;
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <>
      <div
        className="sticky top-0 z-10 px-[24px] pb-[24px] bg-white"
        style={{ borderBottom: '1px solid #ddd' }}
      >
        <div className="mb-1">
          <Button
            type="link"
            className="p-0"
            onClick={() => setCurrentView('MAIN')}
          >
            Back
          </Button>
        </div>
        <Segmented
          value={activeTab}
          onChange={(value) => setActiveTab(value)}
          block
          options={[{ label: 'Contact Information', value: 'contact' }]}
        />
      </div>

      <div
        className="flex flex-col gap-8 pt-4 px-[24px] pb-[24px]"
        style={{ color: '#333' }}
      >
        {contactInfo?.persons.map((person: any, personIndex: number) => (
          <div key={personIndex}>
            {/* Owner Name Section */}
            <div className="mb-6">
              <h4 className="font-bold text-base mb-2">Owner Name</h4>
              <div>
                {person.name
                  ? `${person.name.first || ''} ${person.name.middle || ''} ${
                      person.name.last || ''
                    }`.trim()
                  : 'N/A'}
              </div>
            </div>

            {/* Phone Numbers Section */}
            <div className="mb-6">
              <h4 className="font-bold text-base mb-2">Phone Numbers</h4>
              {person.phoneNumbers && person.phoneNumbers.length > 0 ? (
                person.phoneNumbers.map((phone: any, idx: number) => (
                  <div
                    key={idx}
                    style={{
                      marginBottom: '4px',
                      display: 'flex',
                      gap: '12px',
                      alignItems: 'center',
                    }}
                  >
                    <div>{formatPhoneNumber(phone.number)}</div>
                    <div style={{ fontSize: '0.875rem', color: '#666' }}>
                      (Last Updated: {formatDate(phone.lastReportedDate)})
                    </div>
                  </div>
                ))
              ) : (
                <div>N/A</div>
              )}
            </div>

            {/* Email Addresses Section */}
            <div>
              <h4 className="font-bold text-base mb-2">Email Addresses</h4>
              {person.emails && person.emails.length > 0 ? (
                person.emails.map((email: any, idx: number) => (
                  <div key={idx} style={{ marginBottom: '4px' }}>
                    {email.email}
                  </div>
                ))
              ) : (
                <div>N/A</div>
              )}
            </div>
          </div>
        ))}
      </div>
    </>
  );
};

const TaxProper = ({ address, coordinates }) => {
  const { setCurrentView } = useLandBreakdown();
  const { data: avm, isLoading: avmLoading } = useQuery(
    ['parcel-breakdown-avm', coordinates[0], coordinates[1]],
    async () =>
      await getParcelAVMData({
        lat: coordinates[0],
        lng: coordinates[1],
      }),
    {
      enabled: coordinates && coordinates.length > 0,
    },
  );
  const { data, isError, isLoading, refetch } = useQuery(
    [
      'parcel-breakdown-taxproper',
      coordinates[0],
      coordinates[1],
      // avm?.salesavm,
      address,
    ],
    async () => {
      const res = await getTaxProperDetailsData({
        body: {
          ...(address
            ? { address, latitude: coordinates[0], longitude: coordinates[1] }
            : {
                latitude: coordinates[0],
                longitude: coordinates[1],
              }),
          purchase_price: avm?.salesavm || 500000,
        },
      });

      return res;
    },
    {
      enabled:
        ((coordinates && coordinates.length > 0) || address) &&
        !avmLoading &&
        avm
          ? // && avm.salesavm
            true
          : false,
      staleTime: 1000 * 60 * 60 * 24 * 3, // 3 days unless browser closes, uses react-query-persist-client to persist
    },
  );

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-[300px] p-[24px]">
        <Spin tip="loading" size="large" />
      </div>
    );
  } else if (isError) {
    return (
      <div className="flex flex-col gap-2 p-[24px]">
        <span>Failed to fetch data.</span>
        <Button type="text" onClick={() => refetch()}>
          Try again?
        </Button>
      </div>
    );
  } else {
    if (!isError && !isLoading && data && !data.error) {
      return <TaxProperContent taxData={data} />;
    } else {
      console.log('taxData', data);
      return (
        <>
          <div className="mb-1">
            <Button
              type="link"
              className="p-0"
              onClick={() => setCurrentView('MAIN')}
            >
              Back
            </Button>
          </div>
          <div className="px-4">
            {data && data.error && data.error.message ? (
              <div>{data.error.message}</div>
            ) : (
              <div>No data available.</div>
            )}
          </div>
        </>
      );
    }
  }
};

const TaxProperContent = ({ taxData }) => {
  const { setCurrentView } = useLandBreakdown();
  const [activeTab, setActiveTab] = useState('general');

  console.log('taxData', taxData);

  const parseSnakeCaseToString = (input) => {
    return input
      .replace(/_/g, ' ')
      .split(' ')
      .map((s) => s.charAt(0).toUpperCase() + s.substring(1))
      .join(' ');
  };

  const renderValue = (key, value) => (
    <div key={key} style={{ display: 'grid', gridTemplateColumns: '1fr 1fr' }}>
      <p style={{ fontWeight: 'bold' }}>{parseSnakeCaseToString(key)}</p>
      <p>{value !== null && value !== undefined ? value : '-'}</p>
    </div>
  );

  const segmentedOptions = React.useMemo(() => {
    const options = [
      { label: 'General', value: 'general' },
      { label: 'Projections', value: 'projections' },
      { label: 'Rates', value: 'rate_breakdown' },
    ];

    if (taxData.source_forecasts) {
      options.push({
        label: 'Nearby',
        value: 'source_forecasts',
      });
    }
    return options;
  }, [taxData]);

  return (
    <>
      <div
        className="sticky top-0 z-10 px-[24px] pb-[24px] bg-white"
        style={{ borderBottom: '1px solid #ddd' }}
      >
        <div className="mb-1">
          <Button
            type="link"
            className="p-0"
            onClick={() => setCurrentView('MAIN')}
          >
            Back
          </Button>
        </div>
        <Segmented
          value={activeTab}
          onChange={(value) => setActiveTab(value)}
          block
          options={segmentedOptions}
        />
      </div>

      <div
        className="flex flex-col gap-8 pt-4 px-[24px] pb-[24px]"
        style={{ color: '#333' }}
      >
        {activeTab === 'general' && (
          <>
            <div>
              {renderValue('base_rate', taxData.base_rate?.toFixed(6))}
              {renderValue('tax_cap', taxData.tax_cap?.toFixed(2))}
              {/* assessment_cap is a json object, try phoennix area */}
              {/* {renderValue("assessment_cap", taxData.assessment_cap?.toFixed(2))} */}
            </div>
            <div>
              {Object.entries(taxData.forecast_metadata)
                .filter(
                  ([k, v]) =>
                    ![
                      'messages',
                      'tax_districts',
                      'request_id',
                      'purchase_price',
                    ].includes(k),
                )
                .map(([key, value]) => {
                  if (['rates_last_check', 'rates_next_check'].includes(key)) {
                    return (
                      <div key={key}>
                        {renderValue(key, value && value.split('T')[0])}
                      </div>
                    );
                  }
                  if (
                    [
                      'total_millage_rate',
                      'total_flat_charge',
                      'median_millage_rate',
                      'median_flat_charge',
                      'mean_millage_rate',
                      'median_flat_charge',
                    ].includes(key)
                  ) {
                    return (
                      <div key={key}>
                        {renderValue(key, value && value?.toFixed(6))}
                      </div>
                    );
                  }
                  if (
                    ['purchase_price', 'stabilized_tax_amount'].includes(key)
                  ) {
                    // filtered out, maybe temp
                    return (
                      <div key={key}>
                        {renderValue(
                          key,
                          value && `$ ${Math.round(value).toLocaleString()}`,
                        )}
                      </div>
                    );
                  }
                  return <div key={key}>{renderValue(key, value)}</div>;
                })}
            </div>
          </>
        )}
        {activeTab === 'projections' && (
          <>
            {taxData.tax_projections.map((projection, idx) => (
              <div key={idx}>
                <h3 className="font-bold text-base">Year {projection.year}</h3>

                {projection?.market_value &&
                  renderValue(
                    'market_value',
                    projection?.market_value !== null
                      ? `$ ${Math.round(
                          projection?.market_value,
                        ).toLocaleString()}`
                      : '-',
                  )}
                {projection?.tax_amount &&
                  renderValue(
                    'tax_amount',
                    `$ ${Math.round(projection?.tax_amount).toLocaleString()}`,
                  )}
                {projection?.tax_amount &&
                  renderValue('tax_cap', projection?.tax_cap)}

                {projection?.assessment_method &&
                  renderValue(
                    'assessment_method',
                    `${projection?.assessment_method.split('_').join(' ')}`,
                  )}

                {renderValue(
                  'is_assessment_capped',
                  `${projection.is_assessment_capped ? 'Yes' : 'No'}`,
                )}

                {projection?.assessment &&
                  renderValue(
                    'assessment',
                    `$ ${Math.round(projection?.assessment).toLocaleString()}`,
                  )}
                {projection?.gross_assessment &&
                  renderValue(
                    'gross_assessment',
                    `$ ${Math.round(
                      projection?.gross_assessment,
                    ).toLocaleString()}`,
                  )}
                {projection?.net_assessment &&
                  renderValue(
                    'net_assessment',
                    `$ ${Math.round(
                      projection?.net_assessment,
                    ).toLocaleString()}`,
                  )}
              </div>
            ))}
          </>
        )}
        {activeTab === 'rate_breakdown' && (
          <>
            {taxData.rate_breakdown.map((rate, idx) => (
              <div key={idx}>
                <h3 className="font-bold text-base">{rate.taxing_authority}</h3>
                {renderValue('rate', rate.rate?.toFixed(6))}
                {renderValue(
                  'amount',
                  rate.amount &&
                    `$ ${Math.round(rate.amount).toLocaleString()}`,
                )}
                {renderValue(
                  'flat_charge',
                  rate.flat_charge !== null
                    ? `$ ${Math.round(rate.flat_charge).toLocaleString()}`
                    : '-',
                )}
              </div>
            ))}
          </>
        )}
        {/* {activeTab === "forecast_metadata" && (
          <>
            {Object.entries(taxData.forecast_metadata).map(([key, value]) => (
              <div key={key}>{renderValue(key, value)}</div>
            ))}
          </>
        )} */}
        {taxData.source_forecasts && activeTab === 'source_forecasts' && (
          <>
            {taxData.source_forecasts &&
              taxData.source_forecasts.map((source, idx) => (
                <div key={idx}>
                  {/* <h3 className="font-bold text-base">Forecast #{idx + 1}</h3> */}
                  {source?.location && (
                    <>
                      <h3 className="font-bold text-base">
                        {source?.location?.resolved_address || '-'}
                      </h3>
                      <div
                        className="mb-2"
                        style={{
                          display: 'grid',
                          gridTemplateColumns: '1fr 1fr',
                        }}
                      >
                        <span className="font-bold">Coordinates</span>
                        <span>
                          {source?.location?.latitude || '-'},{' '}
                          {source?.location?.longitude || '-'}
                        </span>
                      </div>
                    </>
                  )}
                  <div className="flex flex-col gap-2">
                    {source.tax_projections.map((estimate, estimateIdx) => (
                      <div key={estimateIdx}>
                        <h3 className="font-bold text-base">
                          Year {estimate.year}
                        </h3>
                        {renderValue(
                          'market_value',
                          estimate.market_value &&
                            `$ ${Math.round(
                              estimate.market_value,
                            ).toLocaleString()}`,
                        )}
                        {renderValue(
                          'gross_assessment',
                          estimate.gross_assessment &&
                            `$ ${Math.round(
                              estimate.gross_assessment,
                            ).toLocaleString()}`,
                        )}
                        {renderValue(
                          'net_assessment',
                          estimate.net_assessment &&
                            `$ ${Math.round(
                              estimate.net_assessment,
                            ).toLocaleString()}`,
                        )}
                        {renderValue(
                          'gross_taxes',
                          estimate.gross_taxes &&
                            `$ ${Math.round(
                              estimate.gross_taxes,
                            ).toLocaleString()}`,
                        )}
                        {renderValue(
                          'net_taxes',
                          estimate.net_taxes &&
                            `$ ${Math.round(
                              estimate.net_taxes,
                            ).toLocaleString()}`,
                        )}
                        {renderValue(
                          'tax_amount',
                          estimate.tax_amount &&
                            `$ ${Math.round(
                              estimate.tax_amount,
                            ).toLocaleString()}`,
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
          </>
        )}
      </div>
    </>
  );
};

interface Props {
  parcel: Parcel;
}

const OtherParcelsDetails = ({ parcel }: Props) => {
  const { setCurrentView } = useLandBreakdown();
  const map = useSelector((state: any) => state.CMA.map);
  const [markers, setMarkers] = useState<mapboxgl.Marker[]>([]);

  const {
    data: ownerParcels,
    isLoading,
    isError,
    refetch,
  } = useQuery({
    queryKey: ['owner-parcels', parcel?.lat, parcel?.lon],
    queryFn: () => getOwnersOtherParcels({ lat: parcel.lat, lng: parcel.lon }),
    enabled: !!parcel?.lat && !!parcel?.lon,
  });

  const handleLocate = useCallback(
    (coordinates: [number, number]) => {
      map?.flyTo({
        center: coordinates,
        zoom: 18,
        duration: 1500,
        essential: true,
      });
    },
    [map],
  );

  useEffect(() => {
    if (!map || !ownerParcels?.features) return;

    // Clean up existing markers
    markers.forEach((marker) => marker.remove());
    const newMarkers: mapboxgl.Marker[] = [];

    ownerParcels.features.forEach((feature: any) => {
      const [lng, lat] = feature.geometry.coordinates;

      // Create marker element with image
      const el = document.createElement('div');
      el.className = 'owner-parcel-marker';
      el.style.backgroundImage = `url(${markerImg})`;
      el.style.backgroundSize = 'contain';
      el.style.backgroundRepeat = 'no-repeat';
      el.style.width = '50px';
      el.style.height = '50px';
      el.style.cursor = 'pointer';

      // Add click handler to element
      el.addEventListener('click', () => {
        handleLocate([lng, lat]);
      });

      const marker = new mapboxgl.Marker(el).setLngLat([lng, lat]).addTo(map);

      newMarkers.push(marker);
    });

    setMarkers(newMarkers);

    // Handle initial camera positioning
    if (ownerParcels.features.length === 1) {
      const [lng, lat] = ownerParcels.features[0].geometry.coordinates;
      map.flyTo({
        center: [lng, lat],
        zoom: 12,
        duration: 1000,
      });
    } else if (ownerParcels.features.length > 1) {
      const coordinates = ownerParcels.features.map(
        (feature: any) => feature.geometry.coordinates,
      );

      const lngs = coordinates.map((coord: number[]) => coord[0]);
      const lats = coordinates.map((coord: number[]) => coord[1]);

      const bbox = [
        [Math.min(...lngs), Math.min(...lats)],
        [Math.max(...lngs), Math.max(...lats)],
      ] as [[number, number], [number, number]];

      map.fitBounds(bbox, {
        padding: 50,
        duration: 1000,
        maxZoom: 12,
      });
    }

    return () => {
      newMarkers.forEach((marker) => marker.remove());
    };
  }, [map, ownerParcels, handleLocate]);

  const clearMarkers = useCallback(() => {
    markers.forEach((marker) => marker.remove());
    setMarkers([]);
  }, [markers]);

  if (isLoading) return <Spin />;
  if (isError) return <Button onClick={() => refetch()}>Try again</Button>;
  if (!ownerParcels) return <div>No other parcels found</div>;

  return (
    <OtherParcelsContent
      otherParcels={ownerParcels}
      onLocate={handleLocate}
      clearMarkers={clearMarkers}
    />
  );
};

const OtherParcelsContent = ({ otherParcels, onLocate, clearMarkers }: any) => {
  const { setCurrentView } = useLandBreakdown();
  const map = useSelector((state: any) => state.CMA.map);
  const handleLocate = useCallback(
    (coordinates: [number, number]) => {
      if (!map) return;
      const [lng, lat] = coordinates;
      if (lat < -90 || lat > 90 || lng < -180 || lng > 180) {
        console.error('Invalid coordinates:', coordinates);
        return;
      }
      map.flyTo({
        center: coordinates,
        zoom: 18,
        duration: 2000,
        essential: true,
      });
    },
    [map],
  );
  const [showDetails, setShowDetails] = useState(false);
  const [selectedParcelId, setSelectedParcelId] = useState<string | null>(null);

  const { data: parcelDetails, isLoading } = useQuery({
    queryKey: ['parcel-details', selectedParcelId],
    queryFn: async () => {
      if (!selectedParcelId) throw new Error('No parcel ID provided');
      const result = await getParcelDetailData({
        ll_uuid: selectedParcelId,
      });
      return result;
    },
    enabled: !!selectedParcelId,
  });

  const handleViewDetails = (feature: any) => {
    handleLocate(feature.geometry.coordinates);
    setSelectedParcelId(feature.properties.ll_uuid);
    setShowDetails(true);
  };

  const getBbox = useCallback(() => {
    const coordinates = otherParcels.features.map(
      (feature: any) => feature.geometry.coordinates,
    );

    const lngs = coordinates.map((coord: number[]) => coord[0]);
    const lats = coordinates.map((coord: number[]) => coord[1]);

    return [
      [Math.min(...lngs), Math.min(...lats)],
      [Math.max(...lngs), Math.max(...lats)],
    ];
  }, [otherParcels]);

  const zoomOutToAll = useCallback(() => {
    if (otherParcels.features.length === 1) {
      const [lng, lat] = otherParcels.features[0].geometry.coordinates;
      map.flyTo({ center: [lng, lat], zoom: 12, duration: 1000 });
    } else {
      map.fitBounds(getBbox(), {
        padding: 50,
        duration: 6000,
        maxZoom: 12,
      });
    }
  }, [map, otherParcels, getBbox]);
  return (
    <>
      <div className="top-0 z-10 px-[24px] bg-white border-b">
        <div className="mb-1">
          <Button
            type="link"
            className="px-4"
            onClick={() => {
              clearMarkers();
              setCurrentView('MAIN');
            }}
          >
            Back
          </Button>
        </div>
      </div>

      <div className="flex flex-col">
        {otherParcels.features?.map((feature: any, index: number) => (
          <div className="border-b border-t hover:bg-[#E6F6FF]">
            <div className="p-4">
              <div className="flex justify-between items-start">
                <h3 className="text-md font-bold">
                  {feature?.properties.address.street_address},{' '}
                  {feature?.properties.address.city},{' '}
                  {feature?.properties.address.state}{' '}
                  {feature?.properties.address.zip_code}
                </h3>
                <div className="flex gap-1">
                  <Tooltip title="View parcel breakdown">
                    <Button
                      shape="circle"
                      size="small"
                      style={{ width: '25px' }}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleViewDetails(feature);
                      }}
                    >
                      <div
                        style={{
                          width: '100%',
                          height: '100%',
                          display: 'flex',
                          flexDirection: 'row',
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}
                      >
                        <MdOutlineDocumentScanner size={16} />
                      </div>
                    </Button>
                  </Tooltip>
                  <Tooltip title="Locate Parcel">
                    <Button
                      shape="circle"
                      size="small"
                      style={{ width: '25px' }}
                      onClick={(e) => {
                        e.stopPropagation();
                        onLocate(feature.geometry.coordinates);
                      }}
                    >
                      <div
                        style={{
                          width: '100%',
                          height: '100%',
                          display: 'flex',
                          flexDirection: 'row',
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}
                      >
                        <BiMapPin className="w-5 h-5 text-gray-600" />
                      </div>
                    </Button>
                  </Tooltip>
                </div>
              </div>

              <p className="text-sm text-gray-600 mt-1">
                Distance: {feature?.properties.miles_away} mi away
              </p>
              <p className="text-sm text-gray-600 mt-1">
                Area Acres: {feature?.properties.lot_size} ac
              </p>
            </div>
          </div>
          // <Card
          //   key={index}
          //   title={feature.properties.address.street_address}
          //   bordered
          //   style={{ width: '100%' }}
          //   size="small"
          // >
          //   <div className="bg-white p-4">
          //     <div className="text-sm text-gray-500 mb-3">
          //       {feature.properties.miles_away} miles away
          //     </div>
          //     <div className="text-sm text-gray-500 mb-3">
          //       {feature.properties.lot_size > 1
          //         ? `${feature.properties.lot_size} acres`
          //         : `${feature.properties.lot_size} acre`}
          //     </div>
          //     <div className="flex gap-2">
          //       <Button
          //         type="primary"
          //         className="bg-green-600"
          //         ghost
          //         onClick={() => onLocate(feature.geometry.coordinates)}
          //       >
          //         <AimOutlined /> Locate
          //       </Button>
          //       <Button
          //         type="primary"
          //         className="bg-green-600"
          //         onClick={() => handleViewDetails(feature)}
          //       >
          //         View Details
          //       </Button>
          //     </div>
          //   </div>
          // </Card>
        ))}
      </div>

      {showDetails && (
        <ParcelDetailsModal
          onClose={() => {
            setShowDetails(false);
            setSelectedParcelId(null);
            zoomOutToAll();
          }}
          parcelData={parcelDetails}
          isLoading={isLoading}
        />
      )}
    </>
  );
};

const ParcelDetailsModal = ({
  onClose,
  parcelData,
  isLoading,
}: {
  onClose: () => void;
  parcelData: any;
  isLoading: boolean;
}) => {
  return (
    <>
      <div
        className={`
          absolute inset-0 bg-white z-[10000] shadow-lg
          transform transition-all duration-300 overflow-auto
          px-6 py-4
        `}
      >
        <div className="top-0 z-1 pb-6 bg-white border-b m-0">
          <Button type="link" className="p-0 m-0" onClick={onClose}>
            {'<'} Back to Owner's Other Parcels
          </Button>
        </div>
        <div className="">
          {parcelData && (
            <LandBreakdownProvider>
              <LandBreakdownContent
                data={parcelData.fields}
                isLoading={isLoading}
              />
            </LandBreakdownProvider>
          )}
        </div>
      </div>
    </>
  );
};
