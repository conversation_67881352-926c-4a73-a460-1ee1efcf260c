import * as mapboxgl from 'mapbox-gl';

declare module 'mapbox-gl' {
  export type AnyLayoutName =
    | keyof BackgroundLayout
    | keyof FillLayout
    | keyof FillExtrusionLayout
    | keyof LineLayout
    | keyof SymbolLayout
    | keyof RasterLayout
    | keyof CircleLayout
    | keyof HeatmapLayout
    | keyof HillshadeLayout
    | keyof SkyLayout;

  export type AnyPaintName =
    | keyof BackgroundPaint
    | keyof FillPaint
    | keyof FillExtrusionPaint
    | keyof LinePaint
    | keyof SymbolPaint
    | keyof RasterPaint
    | keyof CirclePaint
    | keyof HeatmapPaint
    | keyof HillshadePaint
    | keyof SkyPaint;

  interface Map {
    getPaintProperty(layer: string, name: AnyPaintName): any;
    setPaintProperty(
      layer: string,
      name: AnyPaintName,
      value: any,
      options?: FilterOptions,
    ): this;
    getLayoutProperty(layer: string, name: AnyLayoutName): any;
    setLayoutProperty(
      layer: string,
      name: AnyLayoutName,
      value: any,
      options?: mapboxgl.FilterOptions,
    ): this;
  }
}
