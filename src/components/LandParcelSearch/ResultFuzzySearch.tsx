import { Input, Select } from 'antd';
import Fuse from 'fuse.js';
import debounce from 'lodash.debounce';
import isEqual from 'lodash.isequal';
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';
// @ts-ignore
import usePrevious from '../../hooks/usePrevious';
import { LandParcelSearch } from './types/types';

type ResultFuzzySearchContextType =
  | {
      keys: string[];
      setKeys: (keys: string[]) => void;
      searchText: string;
      setSearchText: (searchText: string) => void;
      searchPattern: string;
      setSearchPattern: (searchPattern: string) => void;
      selectedKey: string;
      setSelectedKey: (selectedKey: string) => void;
      applyFuzzySearch: (data: any) => any;
    }
  | undefined;

const ResultFuzzySearchContext =
  createContext<ResultFuzzySearchContextType>(undefined);

export const ResultFuzzySearchProvider = ({ children }: any) => {
  const [keys, setKeys] = useState<string[]>([]);
  const [searchText, setSearchText] = useState<string>('');
  const [searchPattern, setSearchPattern] = useState<string>('');
  const [selectedKey, setSelectedKey] = useState<string>('all');

  const debounceSearchPattern = debounce((value: string) => {
    setSearchPattern(value);
  }, 400);

  useEffect(() => {
    debounceSearchPattern(searchText);

    return () => {
      debounceSearchPattern.cancel();
    };
  }, [searchText, debounceSearchPattern]);

  const applyFuzzySearch = useCallback(
    (data: LandParcelSearch.LandFeature[]) => {
      if (searchPattern.length > 0) {
        const fuse = new Fuse(data, {
          keys:
            selectedKey === 'all'
              ? [...keys.map((k) => `properties.${k}`)]
              : [`properties.${selectedKey}`],
        });
        const list = fuse.search(searchPattern).map((result) => result.item);
        return list;
      } else {
        return data;
      }
    },
    [searchPattern, keys, selectedKey],
  );

  return (
    <ResultFuzzySearchContext.Provider
      value={{
        keys,
        setKeys,
        searchText,
        setSearchText,
        searchPattern,
        setSearchPattern,
        selectedKey,
        setSelectedKey,
        applyFuzzySearch,
      }}
    >
      {children}
    </ResultFuzzySearchContext.Provider>
  );
};

export const useResultFuzzySearch = () => {
  const context = useContext(ResultFuzzySearchContext);
  if (context === undefined) {
    throw new Error(
      'useResultFuzzySearch must be used within a ResultFuzzySearchProvider',
    );
  }
  return context;
};

export const useResultFuzzySearchedData = (data: any) => {
  const { keys, searchPattern, selectedKey } = useResultFuzzySearch();

  if (searchPattern.length > 0) {
    const fuse = new Fuse(data, {
      keys: selectedKey === 'all' ? [...keys] : [selectedKey],
    });
    const list = fuse.search(searchPattern).map((result) => result.item);
    return list;
  } else {
    return data;
  }
};

export const ResultFuzzySearch = ({ columnOptions }: any) => {
  const {
    keys,
    setKeys,
    searchText,
    setSearchText,
    selectedKey,
    setSelectedKey,
  } = useResultFuzzySearch();

  const prevColumnOptions = usePrevious(columnOptions);

  useEffect(() => {
    if (isEqual(prevColumnOptions, columnOptions)) return;
    const values = columnOptions.map((option: any) => option.value);
    setKeys(values);
  }, [columnOptions]);

  return (
    <div style={{ width: '100%' }}>
      {/* <Select
        value={selectedKey}
        options={[
          { label: 'All', value: 'all' },
          ...columnOptions.filter(
            (option: any) =>
              !option.value.toLowerCase().includes('all') &&
              !option.label.toLowerCase().includes('all'),
          ),
        ]}
        onChange={(value) => setSelectedKey(value)}
        placeholder="Column"
        style={{ width: '60px' }}
      /> */}
      <Input
        value={searchText}
        onChange={(e) => setSearchText(e.target.value)}
        placeholder="Search the results"
        style={{ width: '100%' }}
        allowClear={true}
      />
    </div>
  );
};
