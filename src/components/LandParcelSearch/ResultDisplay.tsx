import {
  getLandBreakDownByBoundaryId,
  getLandBreakDownBySpatialLaserId,
  getSpatialLaserIdWithParcelFipsApn,
} from '@/services/data';
import {
  Button,
  Checkbox,
  Dropdown,
  Modal,
  Spin,
  Tabs,
  Tag,
  Tooltip,
} from 'antd';
import isEqual from 'lodash.isequal';
import {
  Dispatch,
  SetStateAction,
  createContext,
  forwardRef,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';
import { FaRegCopy, FaRegTrashAlt } from 'react-icons/fa';
import { GoHeart, GoHeartFill } from 'react-icons/go';
import { IoChevronDownOutline } from 'react-icons/io5';
import { MdOutlineDocumentScanner } from 'react-icons/md';
import { useQuery } from 'react-query';
// @ts-ignore
import usePrevious from '../../hooks/usePrevious';
import { capitalize } from '../../utils/strings';
import ParcelBreakdown from './ParcelBreakdown';
import ParcelMLSListing from './ParcelMLSListing';
import { getTitleOfColumn, useLandParcelSearch } from './context';
import { LandBreakdownContent, LandBreakdownProvider } from './land-breakdown';
import { LandShowcaseDetails } from './land-breakdown-details';
import { LandParcelSearch } from './types/types';
import {
  isLandListingMLSProperties,
  isLandListingShowcaseProperties,
  isLandParcelProperties,
  savedResultSchema,
} from './types/zod-schemas';

type ResultDisplayContextType =
  | {
      selectedColumns: any[];
      setSelectedColumns: Dispatch<SetStateAction<any[]>>;
      openDetails: boolean;
      setOpenDetails: Dispatch<SetStateAction<boolean>>;
      exportDataMode: boolean;
      setExportDataMode: Dispatch<SetStateAction<boolean>>;
      customSelectedResults: any[];
      setCustomSelectedResults: Dispatch<SetStateAction<any[]>>;
      savedResults: { sl_uuid: string }[];
      resultIsSaved: (result: LandParcelSearch.LandFeature) => boolean;
      saveResult: (result: LandParcelSearch.LandFeature) => void;
      unSaveResult: (result: LandParcelSearch.LandFeature) => void;
    }
  | undefined;

const MAX_SELECTION_ROWS = 100;

const ResultDisplayContext = createContext<ResultDisplayContextType>(undefined);

const handleOldSavedResults = async () => {
  const saved = JSON.parse(
    localStorage.getItem('land-parcel-search-saved-results') || '[]',
  );
  try {
    const itemsToUpdate = [];
    for (let i = 0; i < saved.length; i++) {
      const item = saved[i];
      if (item.fips && item.apn) {
        itemsToUpdate.push({
          fips: item.fips,
          apn: item.apn,
        });
      }
    }

    if (!itemsToUpdate.length) return;

    const res = await getSpatialLaserIdWithParcelFipsApn({
      body: itemsToUpdate,
    });

    const results = savedResultSchema.parse(res);

    localStorage.setItem(
      'land-parcel-search-saved-results',
      JSON.stringify(results),
    );
  } catch (e) {
    console.error('Error updating saved results', e);
    console.log('saved', saved);
  }
};

export const ResultDisplayProvider = ({ children }: any) => {
  const [selectedColumns, setSelectedColumns] = useState<any[]>(['area_acres']);
  const [openDetails, setOpenDetails] = useState(false);
  const [exportDataMode, setExportDataMode] = useState(false);

  const [customSelectedResults, setCustomSelectedResults] = useState<string[]>(
    [],
  );
  const [savedResults, setSavedResults] = useState<{ sl_uuid: string }[]>([]);

  useEffect(() => {
    const initializeSavedResults = async () => {
      // backward compatibility - change fips/apn to sl_uuid
      await handleOldSavedResults();

      setSavedResults(
        JSON.parse(
          localStorage.getItem('land-parcel-search-saved-results') || '[]',
        ),
      );
    };

    initializeSavedResults();
  }, []);

  const resultIsSaved = useCallback(
    (result: LandParcelSearch.LandFeature) => {
      return savedResults.some(
        (savedResult) => savedResult.sl_uuid === result.properties.sl_uuid,
      );
    },
    [savedResults],
  );

  const saveResult = useCallback(
    (result: LandParcelSearch.LandFeature) => {
      if (resultIsSaved(result) && result.properties.sl_uuid) return;

      const newSavedResults = [
        ...savedResults,
        { sl_uuid: result.properties.sl_uuid },
      ];
      localStorage.setItem(
        'land-parcel-search-saved-results',
        JSON.stringify(newSavedResults),
      );
      setSavedResults(newSavedResults);
    },
    [savedResults],
  );

  const unSaveResult = useCallback(
    (result: LandParcelSearch.LandFeature) => {
      if (!resultIsSaved(result)) return;

      const newSavedResults = savedResults.filter(
        (savedResult) => savedResult.sl_uuid !== result.properties.sl_uuid,
      );

      localStorage.setItem(
        'land-parcel-search-saved-results',
        JSON.stringify(newSavedResults),
      );
      setSavedResults(newSavedResults);
    },
    [savedResults],
  );

  return (
    <ResultDisplayContext.Provider
      value={{
        selectedColumns,
        setSelectedColumns,
        openDetails,
        setOpenDetails,
        exportDataMode,
        setExportDataMode,
        customSelectedResults,
        setCustomSelectedResults,
        savedResults,
        resultIsSaved,
        saveResult,
        unSaveResult,
      }}
    >
      {children}
    </ResultDisplayContext.Provider>
  );
};

export const useResultDisplay = () => {
  const context = useContext(ResultDisplayContext);
  if (context === undefined) {
    throw new Error(
      'useResultDisplay must be used within a ResultDisplayProvider',
    );
  }
  return context;
};

export const ResultDisplay = ({
  results,
  columnOptions,
  children,
}: {
  results: LandParcelSearch.LandFeature[];
  columnOptions: any;
  children: React.ReactNode;
}) => {
  const {
    selectedColumns,
    setSelectedColumns,
    customSelectedResults,
    setCustomSelectedResults,
    exportDataMode,
  } = useResultDisplay();

  const resultsPrev = usePrevious(results);

  useEffect(() => {
    if (isEqual(resultsPrev, results)) return;

    setCustomSelectedResults([]);
    // setCustomSelectedResults(
    //   results
    //     .slice(0, MAX_SELECTION_ROWS)
    //     .map((result) => result.properties.sl_uuid),
    // );
  }, [results]);

  const items = columnOptions
    .filter(
      (c: any) =>
        ![
          // 'fips',
          // 'apn',
          'street_number',
          'street_name',
          'street_suffix',
          'unit_type',
          'unit_number',
          'formatted_street_address',
          'city',
          'state',
          'zip_code',
          'owner_name',
          'deed_owner_1',
          'owner_street_address',
          'owner_city',
          'owner_state',
          'owner_zip_code',
          'full_address',
          'zipcode',
          'state_or_province',
          'block_group_id',
          'address',
          'address_cc',
          'address_city',
          'address_post',
          'address_sc',
          'address_street_dir',
          'address_street_name',
          'address_street_num',
          'address_street_type',
        ].includes(c.value),
    )
    .map((column: any) => ({
      key: column.value,
      // label: column.label,
      label: (
        <div
          style={{ width: '100%', height: '100%' }}
          onClick={(e) => e.stopPropagation()}
        >
          <Checkbox
            style={{ width: '100%', height: '100%' }}
            checked={selectedColumns.includes(column.value)}
            onChange={(e) => {
              const checked = e.target.checked;
              setSelectedColumns((prevState) => {
                if (checked && !prevState.includes(column.value)) {
                  return [...prevState, column.value];
                } else if (!checked && prevState.includes(column.value)) {
                  return prevState.filter((field) => field !== column.value);
                }
                return prevState;
              });
            }}
          >
            <div className="flex flex-row gap-0.5 w-fit">
              <span className="whitespace-nowrap">{column.label}</span>
              {column.sources.length != 3 &&
                column.sources.map((source: string) => (
                  <Tag key={source}>{source}</Tag>
                ))}
            </div>
          </Checkbox>
        </div>
      ),
    }));

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        minHeight: 0,
      }}
    >
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '0 24px',
          borderBottom: '1px solid #ccc',
        }}
      >
        <span>
          {exportDataMode
            ? `Selected ${customSelectedResults.length.toLocaleString()} of ${results.length.toLocaleString()} results (max 100)`
            : `${results.length.toLocaleString()} results`}
        </span>
        <div>
          <Dropdown
            overlayClassName={'ResultDisplay-view-dropdown'}
            menu={{ items }}
            placement="bottomRight"
            getPopupContainer={(triggerNode) =>
              triggerNode.parentElement as HTMLElement
            }
          >
            <Button type="link" style={{ padding: 0 }}>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  gap: '6px',
                  alignItems: 'center',
                }}
              >
                <span>View</span>
                <IoChevronDownOutline />
              </div>
            </Button>
          </Dropdown>
        </div>
      </div>
      {exportDataMode && (
        <div
          style={{
            padding: '6px 24px',
            display: 'flex',
            flexDirection: 'row',
            gap: '12px',
            borderBottom: '1px solid #ccc',
          }}
        >
          <div
            style={{
              width: '40px',
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Checkbox
              checked={customSelectedResults.length >= MAX_SELECTION_ROWS}
              indeterminate={
                customSelectedResults.length > 0 &&
                customSelectedResults.length < MAX_SELECTION_ROWS
              }
              onChange={(e) => {
                const checked = e.target.checked;
                if (checked) {
                  setCustomSelectedResults(
                    results
                      .slice(0, MAX_SELECTION_ROWS)
                      .map((result) => result.properties.sl_uuid as string),
                  );
                } else {
                  setCustomSelectedResults([]);
                }
              }}
              style={{ margin: 0 }}
            />
          </div>
          <div>
            {customSelectedResults.length < MAX_SELECTION_ROWS && (
              <span>Select first 100 results</span>
            )}
            {customSelectedResults.length >= MAX_SELECTION_ROWS && (
              <span>Unselect all</span>
            )}
          </div>
        </div>
      )}
      <div
        id="land-parcel-result-list-container"
        style={{ height: '100%', overflowY: 'auto' }}
      >
        {children}
      </div>
    </div>
  );
};

export const ResultDetails = ({
  // selectedResult,
  onClose,
  formatter,
  getTitleOfColumn,
}: any) => {
  const { openDetails, setOpenDetails } = useResultDisplay();
  const [currentTab, setCurrentTab] = useState('parcel-breakdown');
  const {
    searchResults,
    setSearchResults,
    setSelectedResult,
    parcelBreakDownId,
    setParcelBreakDownId,
  } = useLandParcelSearch();
  const [landParcelBreakdownData, setLandParcelBreakdownData] =
    useState<any>(null);

  const { data, isLoading, refetch } = useQuery(
    `ParcelBreakdown-${parcelBreakDownId?.sl_uuid}-${parcelBreakDownId?.boundary_id}`,
    () => {
      if (parcelBreakDownId && parcelBreakDownId.sl_uuid) {
        return getLandBreakDownBySpatialLaserId({
          sl_uuid: parcelBreakDownId.sl_uuid,
        });
      } else if (parcelBreakDownId && parcelBreakDownId.boundary_id) {
        return getLandBreakDownByBoundaryId({
          boundary_id: parcelBreakDownId.boundary_id,
        });
      }
    },
    {
      enabled:
        (parcelBreakDownId && parcelBreakDownId.sl_uuid) ||
        (parcelBreakDownId && parcelBreakDownId.boundary_id)
          ? true
          : false,
    },
  );

  useEffect(() => {
    if (!data) return;
    if (Object.keys(data.parcel).length) {
      setCurrentTab('parcel-breakdown');
    } else if (Object.keys(data.mls).length) {
      setCurrentTab('parcel-mls-listing');
    } else if (Object.keys(data.showcase).length) {
      setCurrentTab('parcel-showcase-listing');
    }
  }, [JSON.stringify(data)]);

  useEffect(() => {
    if (!openDetails) return;
    if (currentTab === 'parcel-breakdown') {
      document.getElementById('land-search-detail-parcel-tab')?.scrollTo(0, 0);
    } else if (currentTab === 'parcel-mls-listing') {
      document.getElementById('land-search-detail-mls-tab')?.scrollTo(0, 0);
    } else if (currentTab === 'parcel-showcase-listing') {
      document
        .getElementById('land-search-detail-showcase-tab')
        ?.scrollTo(0, 0);
    }
  }, [openDetails, currentTab]);

  const onCloseHandler = () => {
    setOpenDetails(false);
    setLandParcelBreakdownData(null);
    setParcelBreakDownId(null);
    onClose();
  };

  // console.log('openDetails', openDetails, selectedResult);

  const onTabChange = (key: string) => {
    setCurrentTab(key);
  };

  console.log('data', data);

  const getTabOptions = useCallback(() => {
    if (!data) return [];
    const options = [];

    if (Object.keys(data.regrid).length) {
      const isInResults = searchResults.some((res) => {
        return res.properties.sl_uuid === data.parcel.sl_uuid;
      });

      const addToResults = (parcel: any) => {
        const { boundary, geog, ...properties } = parcel;

        const geometries = [];
        if (boundary) geometries.push(boundary);
        if (geog) geometries.push(geog);

        const parcelFeature = {
          type: 'Feature',
          properties,
          geometry: {
            type: 'GeometryCollection',
            geometries: geometries,
          },
        } as LandParcelSearch.LandFeature;

        setSearchResults((prevState: any) => [...prevState, parcelFeature]);
        setSelectedResult(parcelFeature);
      };

      options.push({
        label: 'Parcel',
        key: 'parcel-breakdown',
        children: (
          <>
            <div
              id="land-search-detail-parcel-tab"
              className="px-6 py-3 h-full min-h-0 overflow-y-auto"
            >
              {/* <ParcelBreakdown
                data={{
                  ...data.parcel,
                  ...data.regrid.fields,
                }}
              /> */}
              <LandBreakdownProvider openDetails={openDetails}>
                {data.parcel && !isInResults && (
                  <div className="flex flex-row justify-end">
                    <Button onClick={() => addToResults(data.parcel)}>
                      Add to results
                    </Button>
                  </div>
                )}
                <LandBreakdownContent
                  data={{
                    ...data.parcel,
                    ...data.regrid.fields,
                    estated: data?.regrid?.estated,
                  }}
                  isLoading={isLoading}
                />
              </LandBreakdownProvider>
            </div>
          </>
        ),
      });
    }
    if (Object.keys(data.mls).length) {
      options.push({
        label: 'MLS',
        key: 'parcel-mls-listing',
        children: (
          <div
            id="land-search-detail-mls-tab"
            className="h-full min-h-0 overflow-y-auto"
          >
            <ParcelMLSListing data={[data.mls]} />
          </div>
        ),
      });
    }
    if (Object.keys(data.showcase).length) {
      options.push({
        label: 'Showcase',
        key: 'parcel-showcase-listing',
        children: (
          <div
            id="land-search-detail-showcase-tab"
            className="h-full min-h-0 overflow-y-auto"
          >
            <LandShowcaseDetails data={data.showcase} />
          </div>
        ),
      });
    }

    return options;
  }, [JSON.stringify(data), refetch, isLoading, searchResults]);

  return (
    <Modal
      open={openDetails}
      onCancel={(e) => {
        e.stopPropagation();
        onCloseHandler();
      }}
      footer={null}
      style={{
        position: 'absolute',
        top: 0,
        bottom: 0,
        paddingBottom: 0,
        height: '100%',
        background: 'white',
      }}
      styles={{ body: { height: '100%' } }}
      width="100%"
      getContainer={
        document.getElementById('land-parcel-search-result-panel') || undefined
      }
      wrapClassName="result-detail-modal-wrapper [&>div>div]:h-full"
      mask={false}
    >
      <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
        {/* <div
          style={{
            height: '81px',
            borderBottom: '1px solid #ccc',
            padding: '12px 12px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'end',
          }}
        >
          <h3 style={{ margin: 0 }}>Details</h3>
        </div> */}

        {!isLoading && !data && (
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
            }}
          >
            <span>No data available</span>
            <Button onClick={() => refetch()}>Try again</Button>
          </div>
        )}

        <Tabs
          style={{ height: '100%', minHeight: 0 }}
          tabBarStyle={{
            padding: '0 24px',
            borderBottom: '1px solid #ccc',
          }}
          activeKey={currentTab}
          onChange={onTabChange}
          items={getTabOptions()}
          // items={[
          //   {
          //     label: 'Parcel Breakdown',
          //     key: 'parcel-breakdown',
          //     children: (
          //       <>
          //         <div
          //           style={{
          //             padding: '12px 24px',
          //             height: '100%',
          //             minHeight: 0,
          //             overflowY: 'auto',
          //           }}
          //         >
          //           {!isLoading && !data && (
          //             <div
          //               style={{
          //                 display: 'flex',
          //                 flexDirection: 'column',
          //                 justifyContent: 'center',
          //                 alignItems: 'center',
          //                 height: '100%',
          //               }}
          //             >
          //               <span>No data available</span>
          //               <Button onClick={() => refetch()}>Try again</Button>
          //             </div>
          //           )}
          //           {isLoading && (
          //             <div
          //               style={{
          //                 width: '100%',
          //                 height: '100%',
          //                 display: 'flex',
          //                 flexDirection: 'row',
          //                 justifyContent: 'center',
          //                 alignItems: 'center',
          //               }}
          //             >
          //               <Spin size="large" />
          //             </div>
          //           )}
          //           {
          //             data && !isLoading && (
          //               <ParcelBreakdown
          //                 data={{
          //                   data: {
          //                     ...(data.parcel || {}),
          //                     ...(data.regrid.fields || {}),
          //                   },
          //                 }}
          //               />
          //             )
          //             // Object.keys(data)
          //             //   .filter((key) => !['geog', 'boundary'].includes(key))
          //             //   .map((key: string, index: number) => {
          //             //     return (
          //             //       <div
          //             //         key={index}
          //             //         style={{
          //             //           display: 'flex',
          //             //           flexDirection: 'row',
          //             //           gap: '12px',
          //             //           justifyContent: 'space-between',
          //             //           borderBottom: '1px solid #ccc',
          //             //           padding: '3px 0',
          //             //         }}
          //             //       >
          //             //         <span>{getTitleOfColumn(key)}:</span>
          //             //         <DetailValue
          //             //           value={formatter(
          //             //             key,
          //             //             `${data[key] !== null ? data[key] : ''}`,
          //             //           )}
          //             //         />
          //             //       </div>
          //             //     );
          //             //   })
          //           }
          //         </div>
          //       </>
          //     ),
          //   },
          //   // parcelBreakDownId &&
          //   //   parcelBreakDownId.mlsListing && {
          //   //     label: 'MLS Listing',
          //   //     key: 'parcel-mls-listing',
          //   //     children: (
          //   //       <div
          //   //         style={{
          //   //           height: '100%',
          //   //           minHeight: 0,
          //   //           overflowY: 'auto',
          //   //         }}
          //   //       >
          //   //         {!mlsIsLoading && !mlsData && (
          //   //           <div
          //   //             style={{
          //   //               display: 'flex',
          //   //               flexDirection: 'column',
          //   //               justifyContent: 'center',
          //   //               alignItems: 'center',
          //   //               height: '100%',
          //   //             }}
          //   //           >
          //   //             <span>No data available</span>
          //   //             <Button onClick={() => mlsRefetch()}>Try again</Button>
          //   //           </div>
          //   //         )}
          //   //         {mlsIsLoading && (
          //   //           <div
          //   //             style={{
          //   //               width: '100%',
          //   //               height: '100%',
          //   //               display: 'flex',
          //   //               flexDirection: 'row',
          //   //               justifyContent: 'center',
          //   //               alignItems: 'center',
          //   //             }}
          //   //           >
          //   //             <Spin size="large" />
          //   //           </div>
          //   //         )}
          //   //         {mlsData && !mlsIsLoading && (
          //   //           <ParcelMLSListing data={mlsData} />
          //   //         )}
          //   //       </div>
          //   //     ),
          //   //   },
          // ]}
        />
      </div>
    </Modal>
  );
};

const DetailValue = ({ value }: { value: string }) => {
  const [mouseOver, setMouseOver] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);

  const onClickHandler = useCallback(() => {
    navigator.clipboard.writeText(value);
    setShowTooltip(true);
    setTimeout(() => setShowTooltip(false), 1500);
  }, [value]);

  return (
    <div
      style={{
        borderRadius: '6px',
        display: 'inline-block',
        textAlign: 'right',
      }}
      onMouseEnter={() => setMouseOver(true)}
      onMouseLeave={() => setMouseOver(false)}
    >
      <Button
        type="link"
        style={{
          padding: 0,
          visibility:
            value && value.length > 0 && mouseOver ? 'visible' : 'hidden',
        }}
        size="small"
        onClick={onClickHandler}
      >
        <FaRegCopy />
      </Button>

      <Tooltip title="Copied to clipboard" open={showTooltip}>
        <span style={{ pointerEvents: 'none' }}>{value}</span>
      </Tooltip>
    </div>
  );
};

const RowUI = ({
  result,
  getOtherDetails,
  leftColumn,
  rightColumn,
  ...props
}: {
  result: LandParcelSearch.LandFeature;
  getOtherDetails: (data: any) => any;
  leftColumn?: any;
  rightColumn?: any;
  disabled?: boolean;
}) => {
  const getSubjectAddress = () => {
    let address = '';

    if (isLandParcelProperties(result.properties)) {
      const { formatted_street_address, city, state, zip_code } =
        result.properties;
      address = `${formatted_street_address || 'N/A'}, ${city || 'N/A'}, ${
        state || 'N/A'
      } ${zip_code || 'N/A'}`;
    } else if (isLandListingMLSProperties(result.properties)) {
      const { full_address, city, state_or_province, zipcode } =
        result.properties;
      address = `${full_address || 'N/A'}, ${city || 'N/A'}, ${
        state_or_province || 'N/A'
      } ${zipcode || 'N/A'}`;
    } else if (isLandListingShowcaseProperties(result.properties)) {
      const {
        address: full_address,
        address_city,
        address_sc,
        address_post,
      } = result.properties;
      address = `${full_address || 'N/A'}, ${address_city || 'N/A'}, ${
        address_sc || 'N/A'
      } ${address_post || 'N/A'}`;
    }
    return capitalize(address);
  };

  const getOwnerAddress = () => {
    if (!isLandParcelProperties(result.properties)) return '';

    const { owner_street_address, owner_city, owner_state, owner_zip_code } =
      result.properties;
    const address = `${owner_street_address || 'N/A'}, ${
      owner_city || 'N/A'
    }, ${owner_state || 'N/A'} ${owner_zip_code || 'N/A'}`;
    return `, ${capitalize(address)}`;
  };

  return (
    <div
      className="ResultDisplay-row"
      style={{
        padding: '6px 24px',
        borderBottom: '1px solid #ccc',
        display: 'flex',
        flexDirection: 'row',
        gap: '12px',
        color: props.disabled ? '#777' : 'unset',
      }}
    >
      {leftColumn && leftColumn}
      <div style={{ width: '100%' }}>
        <div>
          <strong style={{ fontSize: '16px' }}>{getSubjectAddress()}</strong>
        </div>
        <div>
          <span>
            Owner: {result.properties.owner_name || 'N/A'} {getOwnerAddress()}
          </span>
        </div>
        {/* <div>
          <span>
            Fips: {result.fips} APN: {result.apn}
          </span>
        </div> */}
        {getOtherDetails && getOtherDetails(result)}
      </div>
      {rightColumn && rightColumn}
    </div>
  );
};

export const ResultDisplayRow = forwardRef(
  (
    {
      result,
      formatter,
      selected,
      onClick,
    }: {
      result: LandParcelSearch.LandFeature;
      formatter: (column: string, value: any) => any;
      selected: boolean;
      onClick: () => void;
    },
    ref: any,
  ) => {
    const {
      selectedColumns,
      customSelectedResults,
      setCustomSelectedResults,
      setOpenDetails,
      resultIsSaved,
      saveResult,
      unSaveResult,
      exportDataMode,
    } = useResultDisplay();

    const { setSearchResults, setParcelBreakDownId } = useLandParcelSearch();

    const isChecked = customSelectedResults.includes(result.properties.sl_uuid);

    const onCheckboxChange = (e: any) => {
      setCustomSelectedResults((prevState) => {
        const checked = e.target.checked;
        if (checked && !prevState.includes(result.properties.sl_uuid)) {
          return [...prevState, result.properties.sl_uuid];
        } else if (!checked && prevState.includes(result.properties.sl_uuid)) {
          return prevState.filter(
            (value) => value !== result.properties.sl_uuid,
          );
        }
        return prevState;
      });
    };

    const getOtherDetails = (data: LandParcelSearch.LandFeature) => {
      const availableColumns = selectedColumns.filter((c) => {
        return data.properties[c as keyof typeof data.properties] !== undefined;
      });

      return (
        <div>
          {availableColumns.map((column: any, index: number) => {
            return (
              <span key={index} style={{ color: '#777' }}>
                {index > 0 && ' | '}
                {getTitleOfColumn(column)}:{' '}
                {formatter(
                  column,
                  data.properties[column as keyof typeof data.properties],
                )}
              </span>
            );
          })}
        </div>
      );
    };

    let background = 'transparent';
    if (isChecked && exportDataMode) background = '#e6f7ff';
    // if (!isChecked && selected) background = '#f0f0f0';
    if (selected) background = '#f0f0f0';

    return (
      <div
        ref={ref}
        style={{
          background: background,
        }}
        onClick={() => onClick()}
      >
        <RowUI
          result={result}
          getOtherDetails={getOtherDetails}
          disabled={
            exportDataMode &&
            customSelectedResults.length === MAX_SELECTION_ROWS &&
            !isChecked
          }
          leftColumn={
            exportDataMode ? (
              <div
                style={{
                  width: '40px',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <Checkbox
                  checked={isChecked}
                  disabled={
                    exportDataMode &&
                    customSelectedResults.length === MAX_SELECTION_ROWS &&
                    !isChecked
                  }
                  onChange={onCheckboxChange}
                  style={{ margin: 0 }}
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
            ) : null
          }
          rightColumn={
            !exportDataMode ? (
              <div
                style={{
                  width: '40px',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '6px',
                  justifyContent: 'center',
                  padding: '6px 0',
                }}
              >
                <Tooltip title="View parcel breakdown">
                  <Button
                    shape="circle"
                    size="small"
                    style={{ width: '25px' }}
                    onClick={(e) => {
                      e.stopPropagation();
                      onClick();
                      setOpenDetails((prevState: boolean) => !prevState);
                      setParcelBreakDownId({
                        sl_uuid: result.properties.sl_uuid,
                      });
                    }}
                  >
                    <div
                      style={{
                        width: '100%',
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}
                    >
                      <MdOutlineDocumentScanner size={16} />
                    </div>
                  </Button>
                </Tooltip>
                <Tooltip title="Add to saved sites">
                  <Button
                    size="small"
                    style={{ width: '25px' }}
                    onClick={(e) => {
                      e.stopPropagation();
                      if (resultIsSaved(result)) {
                        unSaveResult(result);
                      } else {
                        saveResult(result);
                      }
                    }}
                    shape="circle"
                  >
                    <div
                      style={{
                        width: '100%',
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}
                    >
                      {resultIsSaved(result) ? (
                        <GoHeartFill size={16} />
                      ) : (
                        <GoHeart size={16} />
                      )}
                    </div>
                  </Button>
                </Tooltip>
                <Tooltip title="Delete from results">
                  <Button
                    style={{ width: '25px' }}
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      if (result) {
                        setSearchResults((prevState) =>
                          prevState.filter(
                            (r: any) =>
                              r.properties.sl_uuid !==
                              result.properties.sl_uuid,
                          ),
                        );
                      }
                    }}
                    shape="circle"
                  >
                    <div
                      style={{
                        width: '100%',
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'row',
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}
                    >
                      <FaRegTrashAlt size={16} />
                    </div>
                  </Button>
                </Tooltip>
              </div>
            ) : null
          }
        />
      </div>
    );
  },
);
