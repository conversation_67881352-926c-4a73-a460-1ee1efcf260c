import { getOwnersOtherParcels, getParcelDetailData } from '@/services/data';
import { Button, Spin, Tooltip } from 'antd';
import mapboxgl from 'mapbox-gl';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { BiMapPin } from 'react-icons/bi';
import { MdOutlineDocumentScanner } from 'react-icons/md';
import { useQuery } from 'react-query';
import AutoSizer from 'react-virtualized-auto-sizer';
import { FixedSizeList as List } from 'react-window';
import { useDispatch, useSelector } from 'umi';
import markerImg from '../../assets/images/mapbox/marker/mapbox-marker-icon-20px-red.png';
import { useLandParcelSearch } from './context';
import { LandBreakdownContent, LandBreakdownProvider } from './land-breakdown';

export function parseCoordinates(
  coordString: string,
): { lat: number; lng: number } | null {
  try {
    const [lat, lng] = coordString
      .split(',')
      .map((coord) => parseFloat(coord.trim()));

    if (isNaN(lat) || isNaN(lng)) return null;
    return { lat, lng };
  } catch (error) {
    console.error('Error parsing coordinates:', error);
    return null;
  }
}

// Memoized ParcelRow component
const ParcelRow = React.memo(({ data, index, style }) => {
  const feature = data.features[index];
  const { handleLocate, handleViewDetails } = data;

  return (
    <div style={style}>
      <div className="border-b border-t hover:bg-[#E6F6FF]">
        <div className="p-4">
          <div className="flex justify-between items-start">
            <h3 className="text-md font-bold">
              {feature?.properties.address.street_address},{' '}
              {feature?.properties.address.city},{' '}
              {feature?.properties.address.state}{' '}
              {feature?.properties.address.zip_code}
            </h3>
            <div className="flex gap-1">
              <Tooltip title="View parcel breakdown">
                <Button
                  shape="circle"
                  size="small"
                  style={{ width: '25px' }}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleLocate(feature.geometry.coordinates);
                    handleViewDetails(feature.properties);
                  }}
                >
                  <div className="flex items-center justify-center w-full h-full">
                    <MdOutlineDocumentScanner size={16} />
                  </div>
                </Button>
              </Tooltip>
              <Tooltip title="Locate Parcel">
                <Button
                  shape="circle"
                  size="small"
                  style={{ width: '25px' }}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleLocate(feature.geometry.coordinates);
                  }}
                >
                  <div className="flex items-center justify-center w-full h-full">
                    <BiMapPin className="w-5 h-5 text-gray-600" />
                  </div>
                </Button>
              </Tooltip>
            </div>
          </div>
          <p className="text-sm text-gray-600 mt-1">
            Distance: {feature?.properties.miles_away} mi away
          </p>
          <p className="text-sm text-gray-600 mt-1">
            Area Acres: {feature?.properties.lot_size} ac
          </p>
        </div>
      </div>
    </div>
  );
});

// Memoized ParcelList component
const ParcelList = React.memo(({ data, handleLocate, handleViewDetails }) => {
  if (!data?.features?.length) return null;

  return (
    <AutoSizer>
      {({ height, width }) => (
        <List
          height={height}
          itemCount={data.features.length}
          itemSize={150}
          width={width}
          itemData={{
            features: data.features,
            handleLocate,
            handleViewDetails,
          }}
        >
          {ParcelRow}
        </List>
      )}
    </AutoSizer>
  );
});

// ParcelDetails component
const ParcelDetails = ({ openDetails, setOpenDetails }) => {
  const { parcelBreakDownId } = useLandParcelSearch();

  const {
    data: parcelDetails,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['parcelBreakdown', parcelBreakDownId?.ll_uuid],
    queryFn: async () => {
      if (!parcelBreakDownId?.ll_uuid) {
        throw new Error('No parcel ID provided');
      }
      const result = await getParcelDetailData({
        ll_uuid: parcelBreakDownId.ll_uuid,
      });
      return result;
    },
    enabled: !!parcelBreakDownId?.ll_uuid,
    staleTime: 1000 * 60 * 5,
    cacheTime: 1000 * 60 * 30,
  });

  return (
    <>
      <div
        className={`
          absolute inset-0 bg-black/20 z-40
          transition-opacity duration-300 ease-in-out
          ${openDetails ? 'opacity-100' : 'opacity-0 pointer-events-none'}
        `}
        onClick={(e) => {
          e.stopPropagation();
          setOpenDetails(false);
        }}
      />

      <div
        className={`
          absolute inset-y-0 right-0 bg-white z-50 w-[400px]
          transform transition-all duration-300 ease-in-out
          shadow-lg
          ${openDetails ? 'translate-x-0' : 'translate-x-full'}
        `}
      >
        <div className="flex justify-between items-center px-4 py-3 border-b">
          <h3 className="text-lg font-semibold m-0">Parcel Details</h3>
          <button
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              setOpenDetails(false);
            }}
          >
            <span className="text-gray-500">✕</span>
          </button>
        </div>

        <div className="flex-1 overflow-auto p-4 h-[calc(100%-56px)]">
          {isLoading && (
            <div className="flex items-center justify-center h-full">
              <Spin />
            </div>
          )}

          {error && (
            <div className="flex flex-col items-center justify-center h-full text-red-500 space-y-4">
              <p>Error loading parcel details</p>
              <p className="text-sm">{(error as Error).message}</p>
              <Button
                onClick={() => refetch()}
                type="primary"
                danger
                size="small"
              >
                Try Again
              </Button>
            </div>
          )}

          {!isLoading && !error && parcelDetails && (
            <div className="space-y-4">
              <LandBreakdownProvider openDetails={openDetails}>
                <LandBreakdownContent
                  data={{ ...parcelDetails.fields, ...parcelDetails.apn }}
                  isLoading={isLoading}
                />
              </LandBreakdownProvider>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

// Main OwnersOtherParcelsPanel component
const OwnersOtherParcelsPanel = () => {
  const { setParcelBreakDownId } = useLandParcelSearch();
  const [openDetails, setOpenDetails] = useState(false);
  const dispatch = useDispatch();
  const map = useSelector((state: any) => state.CMA.map);
  const currentParcelDetails = useSelector(
    (state: any) => state.CMA.currentParcelDetails,
  );

  const coordinates = currentParcelDetails?.geographicInformation
    ?.centroidCoordinates
    ? parseCoordinates(
        currentParcelDetails.geographicInformation.centroidCoordinates,
      )
    : null;

  const {
    data: ownersOtherParcelsData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['ownerParcels', coordinates?.lat, coordinates?.lng],
    queryFn: async () => {
      if (!coordinates) throw new Error('No coordinate data available');

      const result = await getOwnersOtherParcels({
        lat: coordinates.lat,
        lng: coordinates.lng,
      });

      if (result) {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            ownersOtherParcelsData: result,
          },
        });
      }

      return result;
    },
    enabled: !!coordinates,
    staleTime: 1000 * 60 * 5,
    cacheTime: 1000 * 60 * 30,
  });

  const handleLocate = useCallback(
    (coordinates: [number, number]) => {
      if (!map) return;
      const [lng, lat] = coordinates;
      if (lat < -90 || lat > 90 || lng < -180 || lng > 180) {
        console.error('Invalid coordinates:', coordinates);
        return;
      }
      map.flyTo({
        center: coordinates,
        zoom: 16,
        duration: 3000,
        essential: true,
      });
    },
    [map],
  );

  const markersRef = useRef(new Map()); // Use ref to store markers

  useEffect(() => {
    if (!map || !ownersOtherParcelsData?.features) return;

    // Clean up existing markers
    markersRef.current.forEach((marker) => marker.remove());
    markersRef.current.clear();

    // Check if the source already exists
    if (map.getSource('parcel-points')) {
      map.removeLayer('parcel-clusters');
      map.removeLayer('cluster-count');
      map.removeSource('parcel-points');
    }

    // Add the source with clustering enabled
    map.addSource('parcel-points', {
      type: 'geojson',
      data: {
        type: 'FeatureCollection',
        features: ownersOtherParcelsData.features.map((feature) => ({
          type: 'Feature',
          properties: {
            ...feature.properties,
            id: feature.properties.ll_uuid,
          },
          geometry: {
            type: 'Point',
            coordinates: feature.geometry.coordinates,
          },
        })),
      },
      cluster: true,
      clusterMaxZoom: 14,
      clusterRadius: 50,
    });

    // Add cluster layer
    map.addLayer({
      id: 'parcel-clusters',
      type: 'circle',
      source: 'parcel-points',
      filter: ['has', 'point_count'],
      paint: {
        'circle-color': '#F34B4B', // Single red color for all clusters
        'circle-radius': [
          'step',
          ['get', 'point_count'],
          10, // Base radius for small clusters
          10,
          15, // Medium clusters (10 or more points)
          30,
          20, // Large clusters (30 or more points)
        ],
      },
    });

    // Add cluster count layer
    map.addLayer({
      id: 'cluster-count',
      type: 'symbol',
      source: 'parcel-points',
      filter: ['has', 'point_count'],
      layout: {
        'text-field': '{point_count_abbreviated}',
        'text-font': ['DIN Offc Pro Medium', 'Arial Unicode MS Bold'],
        'text-size': 13.5,
      },
      paint: {
        'text-color': '#ffffff',
      },
    });

    const updateMarkers = () => {
      // Get unclustered points
      const features = map.querySourceFeatures('parcel-points', {
        filter: ['!', ['has', 'point_count']],
      });

      // Create markers for new points
      features.forEach((feature) => {
        const props = feature.properties;
        const id = props.id;

        // Skip if we already have a marker for this point
        if (markersRef.current.has(id)) return;

        const el = document.createElement('div');
        el.className = 'owner-parcel-marker';
        el.style.backgroundImage = `url(${markerImg})`;
        el.style.backgroundSize = 'contain';
        el.style.backgroundRepeat = 'no-repeat';
        el.style.width = '50px';
        el.style.height = '50px';
        el.style.cursor = 'pointer';

        el.addEventListener('click', () => {
          map.flyTo({
            center: feature.geometry.coordinates,
            zoom: 20,
            duration: 1500,
            essential: true,
          });
          handleViewDetails(props);
        });

        const marker = new mapboxgl.Marker(el)
          .setLngLat(feature.geometry.coordinates)
          .addTo(map);

        markersRef.current.set(id, marker);
      });

      // Remove markers for points that are now clustered
      const visibleIds = new Set(features.map((f) => f.properties.id));
      markersRef.current.forEach((marker, id) => {
        if (!visibleIds.has(id)) {
          marker.remove();
          markersRef.current.delete(id);
        }
      });
    };

    // Update markers initially and on map move
    updateMarkers();
    map.on('moveend', updateMarkers);
    map.on('sourcedata', (e) => {
      if (e.sourceId === 'parcel-points' && e.isSourceLoaded) {
        updateMarkers();
      }
    });

    // Handle click events on clusters
    map.on('click', 'parcel-clusters', (e) => {
      const features = map.queryRenderedFeatures(e.point, {
        layers: ['parcel-clusters'],
      });
      const clusterId = features[0].properties.cluster_id;

      map
        .getSource('parcel-points')
        .getClusterExpansionZoom(clusterId, (err, zoom) => {
          if (err) return;

          map.easeTo({
            center: features[0].geometry.coordinates,
            zoom: zoom,
          });
        });
    });

    // Add hover effects for clusters
    map.on('mouseenter', 'parcel-clusters', () => {
      map.getCanvas().style.cursor = 'pointer';
    });
    map.on('mouseleave', 'parcel-clusters', () => {
      map.getCanvas().style.cursor = '';
    });

    // Initial camera positioning
    if (ownersOtherParcelsData.features.length > 0) {
      const coords = ownersOtherParcelsData.features.map(
        (f) => f.geometry.coordinates,
      );
      const lngs = coords.map((c) => c[0]);
      const lats = coords.map((c) => c[1]);

      map.fitBounds(
        [
          [Math.min(...lngs), Math.min(...lats)],
          [Math.max(...lngs), Math.max(...lats)],
        ],
        {
          padding: 50,
          duration: 1000,
          maxZoom: 12,
        },
      );
    }

    // Cleanup function
    return () => {
      markersRef.current.forEach((marker) => marker.remove());
      markersRef.current.clear();

      if (map.getSource('parcel-points')) {
        map.removeLayer('parcel-clusters');
        map.removeLayer('cluster-count');
        map.removeSource('parcel-points');
      }

      map.off('moveend', updateMarkers);
    };
  }, [map, ownersOtherParcelsData]);
  const handleViewDetails = useCallback(
    (properties: any) => {
      setOpenDetails((prev) => !prev);
      setParcelBreakDownId({ ll_uuid: properties.ll_uuid });
    },
    [setParcelBreakDownId],
  );

  return (
    <div
      id="land-parcel-search-result-panel"
      className="flex flex-col h-full w-[400px] relative"
    >
      <div
        className="p-4 border-b bg-white"
        style={{
          borderBottom: '1px solid #e5e7eb',
          position: 'sticky',
          top: 0,
          zIndex: 10,
        }}
      >
        <h2 className="text-lg font-semibold m-0">
          Owner's Other Parcels
          {ownersOtherParcelsData?.features?.length > 0 && (
            <span className="ml-2 text-sm text-gray-500">
              ({ownersOtherParcelsData.features.length})
            </span>
          )}
        </h2>
      </div>

      <div className="flex-1 overflow-hidden">
        {isLoading && (
          <div className="flex items-center justify-center h-full">
            <Spin />
          </div>
        )}

        {error && (
          <div className="flex items-center justify-center h-full">
            <p className="text-red-500">{(error as Error).message}</p>
          </div>
        )}

        {!isLoading && !error && ownersOtherParcelsData && (
          <ParcelList
            data={ownersOtherParcelsData}
            handleLocate={handleLocate}
            handleViewDetails={handleViewDetails}
          />
        )}
      </div>

      {isLoading && (
        <div className="absolute inset-0 bg-white/70 flex items-center justify-center z-20">
          <Spin />
        </div>
      )}

      <ParcelDetails
        openDetails={openDetails}
        setOpenDetails={setOpenDetails}
      />
    </div>
  );
};

export default OwnersOtherParcelsPanel;
