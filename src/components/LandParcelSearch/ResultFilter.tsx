import { Badge, Button, Input, InputN<PERSON>ber, Popover, Select } from 'antd';
import {
  Dispatch,
  SetStateAction,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';
import { IoClose } from 'react-icons/io5';
import { LandParcelSearch } from './types/types';
import { LandFeature } from './types/zod-schemas';

type ResultFilterContext<T = Object | any> =
  | {
      filters: Array<Filter>;
      setFilters: Dispatch<SetStateAction<Filter[]>>;
      combineType: 'and' | 'or';
      setCombineType: (combineType: 'and' | 'or') => void;
      applyFilters: (
        data: LandParcelSearch.LandFeature[],
      ) => LandParcelSearch.LandFeature[];
    }
  | undefined;

const resultFilterContext = createContext<ResultFilterContext>(undefined);

type Filter = {
  id: string;
  dataType: string | null;
  condition: string | null;
  value: any;
};

type ResultFilterProviderProps<T> = {
  children: React.ReactNode;
};

const getEmptyFilter = (): Filter => ({
  id: crypto.randomUUID(),
  dataType: null,
  condition: null,
  value: null,
});

const filterOperations: any = {
  is: (value: any, filterValue: any) => value === filterValue,
  'is not': (value: any, filterValue: any) => value !== filterValue,
  contains: (value: any, filterValue: any) =>
    value &&
    (value.length === 0 ||
      value.toLowerCase().includes(filterValue.toLowerCase())),
  "doesn't contain": (value: any, filterValue: any) => {
    console.log(
      'doesnt contain',
      value,
      filterValue,
      value &&
        filterValue &&
        !value.toLowerCase().includes(filterValue.toLowerCase()),
    );
    return (
      !value ||
      !filterValue ||
      (value &&
        filterValue &&
        !value.toLowerCase().includes(filterValue.toLowerCase()))
    );
  },
  // value &&
  // (value.length === 0 ||
  //   !value.toLowerCase().includes(filterValue.toLowerCase())),
  'greater than': (value: any, filterValue: any) => value > filterValue,
  'greater than or equal to': (value: any, filterValue: any) =>
    value >= filterValue,
  'less than': (value: any, filterValue: any) => value < filterValue,
  'less than or equal to': (value: any, filterValue: any) =>
    value <= filterValue,
  'is empty': (value: any, filterValue: any) => {
    console.log(
      'is empty',
      _,
      filterValue,
      value == null || (typeof value === 'string' && value.trim().length === 0),
    );
    return (
      value == null || (typeof value === 'string' && value.trim().length === 0)
    );
  },
  'is not empty': (value: any, filterValue: any) =>
    value != null || (typeof value === 'string' && value.trim().length > 0),
  'is between': (value: any, filterValue: any) =>
    value >= filterValue.min && value <= filterValue.max,
  'is not between': (value: any, filterValue: any) =>
    value < filterValue.min || value > filterValue.max,
};

export const ResultFilterProvider = <T extends Object[]>({
  children,
}: ResultFilterProviderProps<T>) => {
  const [filters, setFilters] = useState<Array<Filter>>([getEmptyFilter()]);
  const [combineType, setCombineType] = useState<'and' | 'or'>('and');

  // useEffect(() => {
  //   const fn = (d: any) => {
  //     const validFilters = filters.filter(
  //       (f) =>
  //         (f.dataType != null &&
  //           f.condition != null &&
  //           ['is empty', 'is not empty'].includes(f.condition)) ||
  //         f.value != null ||
  //         (f.value && f.value.min && f.value.max),
  //     );

  //     if (combineType === 'and') {
  //       return validFilters.every((f) => {
  //         const value = d[f.dataType as string];
  //         return filterOperations[f.condition as string](value, f.value);
  //       });
  //     } else {
  //       return validFilters.some((f) => {
  //         const value = d[f.dataType as string];
  //         return filterOperations[f.condition as string](value, f.value);
  //       });
  //     }
  //   };
  //   setFilteredData(unfilteredData.filter(fn));
  // }, [unfilteredData, filters, combineType]);
  const applyFilters = useCallback(
    (data: LandParcelSearch.LandFeature[]) => {
      const fn = (d: LandParcelSearch.LandFeature) => {
        const validFilters = filters.filter(
          (f) =>
            (f.dataType != null &&
              f.condition != null &&
              ['is empty', 'is not empty'].includes(f.condition)) ||
            f.value != null ||
            (f.value && f.value.min && f.value.max),
        );

        if (validFilters.length === 0) return true;
        if (combineType === 'and') {
          return validFilters.every((f) => {
            let value = d.properties[f.dataType as keyof typeof d.properties];
            // if (f.dataType === 'mls_listing') {
            //   value = value?.status;
            // }
            return filterOperations[f.condition as string](value, f.value);
          });
        } else {
          return validFilters.some((f) => {
            let value = d.properties[f.dataType as keyof typeof d.properties];
            // if (f.dataType === 'mls_listing') {
            //   value = value?.status;
            // }
            return filterOperations[f.condition as string](value, f.value);
          });
        }
      };
      return data.filter(fn);
    },
    [filters, combineType],
  );

  return (
    <resultFilterContext.Provider
      value={{
        filters,
        setFilters,
        combineType,
        setCombineType,
        applyFilters,
      }}
    >
      {children}
    </resultFilterContext.Provider>
  );
};

const FilterUI = ({
  filter,
  onUpdateFilter,
  columnOptions,
  columnValueType,
  getAllValuesOfColumn,
  getConditionOptions,
  valueFormatter,
  valueParser,
  filterIndex,
  onDeleteFilter,
}: any) => {
  const { combineType, setCombineType } = useResultFilter();

  const onConditionChange = useCallback(
    (value) => {
      if (['is between', 'is not between'].includes(value)) {
        onUpdateFilter({
          ...filter,
          condition: value,
          value: { min: null, max: null },
        });
      } else {
        onUpdateFilter({ ...filter, condition: value, value: null });
      }
    },
    [filter],
  );

  // Select getPopupContainer reason:
  // https://ant.design/docs/react/faq#Select-Dropdown-DatePicker-TimePicker-Popover-Popconfirm-disappears-when-I-click-another-popup-component-inside-it.-How-do-I-resolve-this
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'row',
        gap: '12px',
        alignItems: 'center',
      }}
    >
      <div style={{ minWidth: '75px', height: '100%' }}>
        {filterIndex === 0 && <strong>Where</strong>}
        {filterIndex === 1 && (
          <Select
            value={combineType}
            onChange={setCombineType}
            options={[
              { value: 'and', label: 'AND' },
              { value: 'or', label: 'OR' },
            ]}
            style={{ width: '100%' }}
            getPopupContainer={(triggerNode) => triggerNode.parentNode}
          />
        )}
        {filterIndex > 1 && (
          <span
            style={{
              display: 'inline-block',
              width: '100%',
              height: '100%',
              padding: '1px 11px',
            }}
          >
            {combineType.toUpperCase()}
          </span>
        )}
      </div>
      <Select
        value={filter.dataType}
        options={columnOptions}
        onChange={(value) =>
          onUpdateFilter({
            ...filter,
            dataType: value,
            condition: filter.dataType != null ? filter.condition : null,
          })
        }
        placeholder="Data Type"
        style={{ minWidth: '150px', maxWidth: '150px' }}
        getPopupContainer={(triggerNode) => triggerNode.parentNode}
      />

      <Select
        value={filter.condition}
        options={getConditionOptions(filter.dataType)}
        onChange={onConditionChange}
        placeholder="Condition"
        style={{ minWidth: '125px', maxWidth: '125px' }}
        dropdownRender={(menu) => {
          if (filter.dataType != null) return menu;
          return (
            <div style={{ padding: '8px 12px' }}>
              <span>Choose a data type</span>
            </div>
          );
        }}
        getPopupContainer={(triggerNode) => triggerNode.parentNode}
      />

      {(!filter.dataType ||
        !filter.condition ||
        (filter.dataType &&
          ['is', 'is not'].includes(filter.condition) &&
          !['is between', 'is not between'].includes(filter.condition))) && (
        <Select
          value={filter.value}
          onChange={(value) => onUpdateFilter({ ...filter, value: value })}
          options={
            filter.dataType != null && filter.condition != null
              ? [
                  ...getAllValuesOfColumn(filter.dataType).map((value) => ({
                    value,
                    label: valueFormatter(filter.dataType, value),
                  })),
                ]
              : []
          }
          placeholder="Value"
          style={{ minWidth: '200px', maxWidth: '200px' }}
          dropdownRender={(menu) => {
            if (filter.dataType != null && filter.condition != null)
              return menu;

            let text = 'Choose a column';
            if (filter.dataType != null) text = 'Choose a condition';

            return (
              <div style={{ padding: '8px 12px' }}>
                <span>{text}</span>
              </div>
            );
          }}
          getPopupContainer={(triggerNode) => triggerNode.parentNode}
        />
      )}
      {[
        'contains',
        "doesn't contain",
        'greater than',
        'greater than or equal to',
        'less than',
        'less than or equal to',
        'is empty',
        'is not empty',
      ].includes(filter.condition) && (
        <>
          {columnValueType(filter.dataType) === 'number' ? (
            <InputNumber
              value={filter.value}
              onChange={(value) => onUpdateFilter({ ...filter, value: value })}
              formatter={(value) => valueFormatter(filter.dataType, value)}
              parser={(value) => valueParser(filter.dataType, value)}
              disabled={['is empty', 'is not empty'].includes(filter.condition)}
              placeholder="Value"
              style={{ minWidth: '200px', maxWidth: '200px' }}
              className="landDev-input-number"
            />
          ) : (
            <Input
              value={filter.value}
              onChange={(e) =>
                onUpdateFilter({ ...filter, value: e.target.value })
              }
              disabled={['is empty', 'is not empty'].includes(filter.condition)}
              placeholder="Value"
              style={{ minWidth: '200px', maxWidth: '200px' }}
            />
          )}
        </>
      )}

      {['is between', 'is not between'].includes(filter.condition) &&
        columnValueType(filter.dataType) && (
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              gap: '12px',
              minWidth: '200px',
              maxWidth: '200px',
            }}
          >
            <InputNumber
              value={filter.value.min}
              onChange={(value) =>
                onUpdateFilter({
                  ...filter,
                  value: { ...filter.value, min: value },
                })
              }
              formatter={(value) => valueFormatter(filter.dataType, value)}
              parser={(value) => valueParser(filter.dataType, value)}
              placeholder="Min value"
              style={{ width: '100%' }}
              className="landDev-input-number"
            />
            <InputNumber
              value={filter.value.max}
              onChange={(value) =>
                onUpdateFilter({
                  ...filter,
                  value: { ...filter.value, max: value },
                })
              }
              formatter={(value) => valueFormatter(filter.dataType, value)}
              parser={(value) => valueParser(filter.dataType, value)}
              placeholder="Max value"
              style={{ width: '100%' }}
              className="landDev-input-number"
            />
          </div>
        )}

      {onDeleteFilter && (
        <Button onClick={onDeleteFilter}>
          <IoClose />
        </Button>
      )}
    </div>
  );
};

export const useResultFilter = () => {
  const context = useContext(resultFilterContext);
  if (context === undefined) {
    throw new Error(
      'useResultFilter must be used within ResultFilterProvider component',
    );
  }
  return context;
};

type ResultFilterProps<T> = {
  columnOptions: Array<{ label: string; value: any }>;
  columnValueType: (column: string) => any;
  getAllValuesOfColumn: (
    column: string,
    valueSorted: null | 'ASC' | 'DESC',
  ) => Array<any>;
  valueFormatter: (column: string, value: any) => any;
  valueParser: (column: string, value: any) => any;
  filteredCount: number;
  unfilteredCount: number;
  children?: React.ReactNode;
};

export const ResultFilter = <T,>({
  columnOptions,
  columnValueType,
  getAllValuesOfColumn,
  valueFormatter,
  valueParser,
  filteredCount,
  unfilteredCount,
}: ResultFilterProps<T>) => {
  const { filters, setFilters } = useResultFilter();
  const [open, setOpen] = useState(false);

  const getConditionOptions = useCallback((column: string) => {
    if (!column) return [];

    const type = columnValueType(column);

    let values: Array<string> = [];
    if (type === 'string') {
      values = [
        'is',
        'is not',
        'contains',
        "doesn't contain",
        'is empty',
        'is not empty',
      ];
    } else if (type === 'number') {
      values = [
        'is',
        'is not',
        'greater than',
        'greater than or equal to',
        'less than',
        'less than or equal to',
        'is empty',
        'is not empty',
        'is between',
        'is not between',
      ];
    } else if (type === 'boolean') {
      values = ['is', 'is not'];
    }
    return values.map((value) => ({ value, label: value }));
  }, []);

  const getFilterCount = useCallback(() => {
    const validFilters = filters.filter(
      (f) =>
        (f.dataType != null &&
          f.condition != null &&
          ['is empty', 'is not empty'].includes(f.condition)) ||
        f.value != null ||
        (f.value && f.value.min && f.value.max),
    );
    return validFilters.length;
  }, [filters]);

  return (
    <Popover
      placement="bottom"
      trigger="click"
      open={open}
      onOpenChange={(open) => setOpen(open)}
      content={
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '12px',
            // width: '500px',
          }}
        >
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: '12px',
                alignItems: 'end',
              }}
            >
              <h4
                style={{
                  margin: 0,
                  display: 'inline-block',
                  fontSize: '16px',
                }}
              >
                Filters
              </h4>
              <span>
                Showing{' '}
                {filteredCount == unfilteredCount
                  ? 'all'
                  : filteredCount.toLocaleString()}{' '}
                of {unfilteredCount.toLocaleString()}
              </span>
            </div>
            <div>
              <Button
                onClick={() => setFilters([getEmptyFilter()])}
                disabled={
                  filters.length <= 1 &&
                  (!filters[0].dataType ||
                    !filters[0].condition ||
                    !filters[0].value)
                }
              >
                Clear All
              </Button>
            </div>
          </div>

          {filters.map((filter, index) => (
            <FilterUI
              key={filter.id}
              filterIndex={index}
              filter={filter}
              onUpdateFilter={(filter: Filter) => {
                setFilters((prev) => {
                  const idx = prev.findIndex((f) => f.id === filter.id);
                  if (idx === -1) return prev;
                  const newFilters = [...prev];

                  if (prev[idx].dataType != filter.dataType) {
                    newFilters[idx] = {
                      ...filter,
                      condition: getConditionOptions(
                        filter.dataType as string,
                      )[0].value,
                      value: null,
                    };
                    return newFilters;
                  }

                  newFilters[idx] = filter;
                  return newFilters;
                });
              }}
              columnOptions={columnOptions}
              columnValueType={columnValueType}
              getAllValuesOfColumn={getAllValuesOfColumn}
              getConditionOptions={getConditionOptions}
              valueFormatter={valueFormatter}
              valueParser={valueParser}
              onDeleteFilter={() =>
                setFilters((prev) =>
                  prev.length === 1
                    ? [getEmptyFilter()]
                    : prev.filter((f) => f.id !== filter.id),
                )
              }
            />
          ))}
          {/* <p>Data Type</p>
        <p>Condition</p>
        <p>Value</p> */}
          <div>
            <Button
              onClick={() => setFilters((prev) => [...prev, getEmptyFilter()])}
            >
              + Add new filter
            </Button>
          </div>
        </div>
      }
    >
      <Button>
        {getFilterCount() === 0 && <span>Filter</span>}
        {getFilterCount() > 0 && (
          <Badge count={getFilterCount()} offset={[12, 0]}>
            <span>Filter</span>
          </Badge>
        )}
      </Button>
    </Popover>
  );
};
