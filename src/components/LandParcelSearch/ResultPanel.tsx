import { Button, Dropdown, Spin, Tabs } from 'antd';
import isEqual from 'lodash.isequal';
import moment from 'moment';
import { useCallback, useEffect, useRef, useState } from 'react';
import AutoSizer from 'react-virtualized-auto-sizer';
import { VariableSizeList as List } from 'react-window';
import tokml from 'tokml';
import xmlFormatter from 'xml-formatter';
import { capitalize } from '../../utils/strings';
import {
  getLandParcelKeyType,
  getTitleOfColumn,
  useLandParcelSearch,
} from './context';
import {
  ResultDetails,
  ResultDisplay,
  ResultDisplayRow,
  useResultDisplay,
} from './ResultDisplay';
import { ResultFilter, useResultFilter } from './ResultFilter';
import {
  ResultFuzzySearch,
  useResultFuzzySearch,
  useResultFuzzySearchedData,
} from './ResultFuzzySearch';
import {
  ResultSorter,
  useResultSortedData,
  useResultSorter,
} from './ResultSorter';

import { valueFormatter } from './utils/formatting';
import { valueParser } from './utils/parsing';

import { LandParcelSearch } from './types/types';
// import { landParcelSchema } from './types/zod-schemas';
import {
  isLandListingMLSProperties,
  isLandListingShowcaseProperties,
  isLandParcelProperties,
  LandFeatureSchema,
  LandListingMLSPropertiesSchema,
  LandListingShowcasePropertiesSchema,
  LandParcelPropertiesSchema,
  landParcelSchema,
} from './types/zod-schemas';

import { getLandFeaturesBySpatialLaserIds } from '@/services/data';

import { getUserEmail, getUserGroup, getUsername } from '@/utils/auth';
import { EyeInvisibleOutlined, EyeOutlined } from '@ant-design/icons';
import he from 'he';
import { useQuery } from 'react-query';
import { getFormattedColumnDefinition } from './core/export/formatting';
import {
  getGeometryFromGeometryCollection,
  mergeGeometryCollectionPolygons,
} from './utils/conversions';
import { trackLandSearchCSVExport } from './utils/tracker';

type LandParcel = LandParcelSearch.LandParcel;

// const parser = (value: any) =>
//   value ? value.replace(/\$\s?|(,*)/g, '') : value;
const parser = (value: any) => {
  const result = value ? valueParser(value).value : value;
  return isNaN(result) ? value : result;
};

const getColumnOptions = () => {
  const parcelKeys = Object.keys(LandParcelPropertiesSchema.shape);
  const mlsKeys = Object.keys(LandListingMLSPropertiesSchema.shape);
  const showcaseKeys = Object.keys(LandListingShowcasePropertiesSchema.shape);
  const columns = [...new Set([...parcelKeys, ...mlsKeys, ...showcaseKeys])];

  const getColumnSources = (column: string) => {
    const sources = [];
    if (parcelKeys.includes(column)) sources.push('parcel');
    if (mlsKeys.includes(column)) sources.push('mls');
    if (showcaseKeys.includes(column)) sources.push('showcase');
    return sources;
  };

  return columns
    .filter(
      (column) =>
        ![
          'latitude',
          'longitude',
          'geog',
          'boundary',
          'block_group_id',
          // 'boundary_lluuid',
          'boundary_id',
          'first_entry_timestamp',
          'modification_timestamp',
          'mls_listing',
          // 'photo',
          // 'source_url',
          'price_change_history',
          'price_change_timestamp',
          'status_change_history',
          'status_change_timestamp',
          'prices',
          'sizes',
          'space_uses',
        ].includes(column),
    )
    .map((column) => ({
      label: getTitleOfColumn(column),
      sources: getColumnSources(column),
      value: column,
    }))
    .sort((a, b) => a.label.localeCompare(b.label));
};

const ResultModifiers = ({
  unfiltered,
  filtered,
}: {
  unfiltered: LandParcelSearch.LandFeature[];
  filtered: LandParcelSearch.LandFeature[];
}) => {
  const getAllValuesOfColumn = useCallback(
    (column: string, valueSorted: null | 'ASC' | 'DESC' = 'ASC') => {
      // if (column === 'mls_listing') {
      //   const values = unfiltered
      //     .filter(
      //       (d: LandParcelSearch.LandFeature) =>
      //         d.properties.sl_land_source === 'parcel',
      //     )
      //     .map(
      //       (d: LandParcelSearch.LandFeature) =>
      //         d.properties.mls_listing?.status,
      //     )
      //     .filter((v) => v != null || v != undefined);
      //   const results = [...new Set(values)].filter((v) => v != null);
      //   if (!valueSorted) return results;
      //   return valueSorted === 'ASC'
      //     ? results.sort()
      //     : results.sort().reverse();
      // }

      const values = unfiltered.map((d: any) => d.properties[column]);
      const results = [...new Set(values)].filter((v) => v != null);
      if (!valueSorted) return results;
      const type = getLandParcelKeyType(column);

      if (type === 'string') {
        return valueSorted === 'ASC'
          ? results.sort()
          : results.sort().reverse();
      } else if (type === 'number') {
        return valueSorted === 'ASC'
          ? results.sort((a, b) => a - b)
          : results.sort((a, b) => b - a);
      } else if (type === 'boolean') {
        return valueSorted === 'ASC'
          ? results.sort()
          : results.sort().reverse();
      }
      return [];
    },
    [unfiltered],
  );

  const columnValueType = useCallback(
    (column: string) => getLandParcelKeyType(column),
    [],
  );

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'row',
        gap: '12px',
        padding: '12px 24px',
      }}
    >
      <ResultFilter<LandParcel>
        columnOptions={getColumnOptions()}
        columnValueType={columnValueType}
        getAllValuesOfColumn={getAllValuesOfColumn}
        valueFormatter={valueFormatter}
        valueParser={(column: string, value: any) => {
          const parsedValue = parser(value);
          if (
            typeof parsedValue === 'number' &&
            ['flood_coverage'].includes(column)
          ) {
            return parsedValue * 100; // flood_coverage is stored as actual percent value
          }

          return parsedValue;
        }}
        filteredCount={filtered.length}
        unfilteredCount={unfiltered.length}
      />
      <ResultSorter
        columnOptions={getColumnOptions()}
        columnValueType={columnValueType}
      />
      <ResultFuzzySearch columnOptions={getColumnOptions()} />
    </div>
  );
};

const ExportDataSelector = ({
  data,
}: {
  data: LandParcelSearch.LandFeature[];
}) => {
  const { customSelectedResults } = useResultDisplay();

  const { resultMeta, filterPayload, activeResultTab } = useLandParcelSearch();

  const getDataToExport = useCallback(() => {
    // if (customSelectedResults.length === data.length) return data;
    if (customSelectedResults.length === 0) return [];

    let processedData = structuredClone(data);

    if (resultMeta && Object.keys(resultMeta).includes('newHomeBuilder')) {
      const newHomeBuilders = resultMeta.newHomeBuilder;

      for (let i = 0; i < newHomeBuilders.length; i++) {
        for (let j = 0; j < processedData.length; j++) {
          if (
            isLandParcelProperties(processedData[j].properties) &&
            newHomeBuilders[i].fips === processedData[j].properties.fips &&
            newHomeBuilders[i].apn === processedData[j].properties.apn
          ) {
            processedData[j].properties.new_home_builder =
              newHomeBuilders[i].builder_name;
            processedData[j].properties.new_home_coordinate =
              newHomeBuilders[i].new_home_geom.coordinates[1] +
              ', ' +
              newHomeBuilders[i].new_home_geom.coordinates[0];
            processedData[j].properties.new_home_miles =
              newHomeBuilders[i].new_home_miles.toFixed(2);
            break;
          }
        }
      }
    }

    processedData = processedData.filter((result) =>
      customSelectedResults.includes(result.properties.sl_uuid),
    );

    const results = processedData.map((result) => {
      return { ...result.properties };
    });

    return results;
  }, [data, customSelectedResults, resultMeta]);

  const onExportCSVRaw = async ({
    type,
  }: {
    type: 'parcel' | 'mls' | 'showcase';
  }) => {
    const dataToExport = getDataToExport();
    let keys = [] as string[];
    if (type === 'parcel') {
      keys = Object.keys(LandParcelPropertiesSchema.shape);
    } else if (type === 'mls') {
      keys = Object.keys(LandListingMLSPropertiesSchema.shape);
    } else {
      keys = Object.keys(LandListingShowcasePropertiesSchema.shape);
    }

    const tracked = await trackLandSearchCSVExport(type, dataToExport);

    if (!tracked) {
      console.log('Error tracking land search csv export');
      return;
    }

    const csvContent =
      // Object.keys(dataToExport[0])
      keys
        .filter((key) => !['geog', 'boundary'].includes(key))
        .join(',')
        .concat('\n') +
      dataToExport
        .filter((row) => row.sl_land_source === type)
        .map((row: any) =>
          keys
            .filter((key) => !['geog', 'boundary'].includes(key))
            .map((col) => {
              let value = row[col];
              if (value === null || value === undefined) return '';
              value = valueFormatter(col, value);
              return JSON.stringify(value);
            })
            .join(','),
        )
        .join('\n');
    // const encodedUri = encodeURI(csvContent);
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const objUrl = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', objUrl);
    link.setAttribute(
      'download',
      `land_search_${`${type}`.replace(' ', '_')}_raw_${moment().format(
        'YYYY-MM-DD HH--mm--ss',
      )}.csv`,
    );
    document.body.appendChild(link); // Required for FF
    link.click();
    document.body.removeChild(link);
  };

  const onExportCSVFormatted = async () => {
    const dataToExport = getDataToExport();

    const columnDef = getFormattedColumnDefinition('Parcel');

    if (resultMeta && Object.keys(resultMeta).includes('newHomeBuilder')) {
      columnDef.splice(columnDef.length - 1, 0, {
        title: 'Builder Name',
        dataIndex: 'new_home_builder',
      });
      columnDef.splice(columnDef.length - 1, 0, {
        title: 'New Home Coordinate',
        dataIndex: 'new_home_coordinate',
      });
      columnDef.splice(columnDef.length - 1, 0, {
        title: 'New Home Miles',
        dataIndex: 'new_home_miles',
      });
    }

    const tracked = await trackLandSearchCSVExport('parcel', dataToExport);

    if (!tracked) {
      console.log('Error tracking land search csv export');
      return;
    }

    const csvHeader = columnDef.map((c) => c.title).join(',') + '\n';
    const csvData = dataToExport
      .filter((d) => d.sl_land_source === 'parcel')
      .map((row: any) => {
        return columnDef
          .map((col) => {
            if (col.dataIndex) {
              let cellValue = row[col.dataIndex];
              if (col.render) {
                cellValue = col.render(row[col.dataIndex], row);
              }
              // Use JSON.stringify to handle special characters, including commas
              return JSON.stringify(cellValue);
            }
            return ''; // Handle missing columns gracefully
          })
          .join(',');
      });

    const csv = csvHeader + csvData.join('\n');
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const objUrl = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', objUrl);
    link.setAttribute(
      'download',
      `land_search_formatted_${moment().format('YYYY-MM-DD HH--mm--ss')}.csv`,
    );
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const onExportKML = (type: 'default' | 'only-boundary') => {
    const geojson = generateGeoJSONForKML('FORMATTED'); // only border

    let kml = tokml(geojson, {
      name: 'name',
      description: 'description',
      documentName: 'Land Search Results',
      documentDescription: `Generated on ${moment().format(
        'YYYY-MM-DD HH--mm--ss',
      )}`,
      simplestyle: true, // MultiGeometry is not supported by simplestyle/KML styling
    });

    kml = kml
      .toString()
      .replace(
        'https://api.tiles.mapbox.com/v3/marker/pin-l+fff.png',
        'http://maps.google.com/mapfiles/kml/paddle/wht-circle.png',
      );

    // var tab = window.open('about:blank', '_blank');
    // tab.document.write(kml); // where 'html' is a variable containing your HTML
    // tab.document.close(); // to finish loading the page

    if (type === 'default') {
      let startIdx = kml.indexOf('boundary</name><ExtendedData>');
      while (startIdx > -1) {
        startIdx = kml.indexOf('<ExtendedData>', startIdx);
        let endIdx = kml.indexOf('</ExtendedData>', startIdx);
        endIdx = endIdx + '</ExtendedData>'.length;
        const boundary = kml.substring(startIdx, endIdx);
        kml = kml.replace(boundary, '');
        startIdx = kml.indexOf('boundary</name><ExtendedData>', endIdx);
      }
    } else {
      let startIdx = kml.indexOf('<ExtendedData>');
      while (startIdx > -1) {
        let endIdx = kml.indexOf('</ExtendedData>', startIdx);
        endIdx = endIdx + '</ExtendedData>'.length;
        const dataToRemove = kml.substring(startIdx, endIdx);
        kml = kml.replace(dataToRemove, '');
        startIdx = kml.indexOf('<ExtendedData>');
      }

      const firstDescIdx = kml.indexOf('<description>');
      let startidx = kml.indexOf('<description>', firstDescIdx + 1);
      while (startidx != firstDescIdx && startidx > -1) {
        let endIdx =
          kml.indexOf('</description>', startidx) + '</description>'.length;
        kml = kml.substring(0, startidx) + kml.substring(endIdx);
        startidx = kml.indexOf('<description>', startidx + 1);
      }

      kml = kml.replace(/ boundary/g, '');
      kml = kml.replace(/ pin/g, '');
    }

    if (resultMeta && Object.keys(resultMeta).includes('newHomeBuilder')) {
      const marketString = `<Style id="mcbluemslarge"><IconStyle><Icon><href>http://maps.google.com/mapfiles/kml/paddle/blu-circle.png</href></Icon></IconStyle><hotSpot xunits="fraction" yunits="fraction" x="0.5" y="0.5"></hotSpot></Style>`;
      const startIdxStyle = kml.indexOf('</Style>') + '</Style>'.length;
      kml =
        kml.slice(0, startIdxStyle) + marketString + kml.slice(startIdxStyle);

      const newHomeBuilders = resultMeta.newHomeBuilder;

      // remove duplicate builder name and locations pins
      const uniqueBuilders = [];
      const map = new Map();
      for (let i = 0; i < newHomeBuilders.length; i++) {
        const { builder_name, new_home_geom } = newHomeBuilders[i];
        const lng = new_home_geom.coordinates[0];
        const lat = new_home_geom.coordinates[1];
        const key = `${lat}_${lng}_${builder_name}`;
        if (!map.has(key)) {
          map.set(key, true); // set any value to Map
          uniqueBuilders.push(newHomeBuilders[i]);
        }
      }

      for (let i = 0; i < uniqueBuilders.length; i++) {
        const builder = uniqueBuilders[i];
        const lng = builder.new_home_geom.coordinates[0];
        const lat = builder.new_home_geom.coordinates[1];
        const placemark = `<Placemark><Point><coordinates>${lng} ${lat}</coordinates></Point><name>${he.encode(
          builder.builder_name || 'No builder name',
        )}</name><styleUrl>#mcbluemslarge</styleUrl></Placemark>`;

        const idx = kml.lastIndexOf('</Document>');
        kml = kml.slice(0, idx) + placemark + kml.slice(idx);
      }
    }

    const kmlFormatted = xmlFormatter(kml, {
      indentation: '  ',
      collapseContent: true,
      lineSeparator: '\n',
    });

    const blob = new Blob([kmlFormatted], {
      type: 'application/vnd.google-earth.kml+xml',
    });
    const link = document.createElement('a');
    link.setAttribute('href', window.URL.createObjectURL(blob));
    link.setAttribute(
      'download',
      `land_search_kml_${moment().format('YYYY-MM-DD HH--mm--ss')}.kml`,
    );
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const onExportGeoJSON = () => {
    const geojson = generateGeoJSON('RAW');
    const jsonContent = JSON.stringify(geojson);
    const blob = new Blob([jsonContent], { type: 'application/json' });
    const link = document.createElement('a');
    link.setAttribute('href', window.URL.createObjectURL(blob));
    link.setAttribute(
      'download',
      `land_search_geojson_${moment().format('YYYY-MM-DD HH--mm--ss')}.json`,
    );
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const generateGeoJSONForKML = (type: 'RAW' | 'FORMATTED') => {
    // const dataToExport = getDataToExport();

    const dataToExport = data.filter((result) =>
      customSelectedResults.includes(result.properties.sl_uuid),
    );

    const geojson = {
      type: 'FeatureCollection',
      features: [] as any,
    };

    let dataType = 'Parcel';
    if (filterPayload && filterPayload.type === 'Listing') {
      dataType = 'Listing';
    }

    const columnDef = getFormattedColumnDefinition(
      dataType as 'Parcel' | 'Listing',
    );

    for (let i = 0; i < dataToExport.length; i++) {
      const result = dataToExport[i];
      // Point Feature
      const pointFeature = {
        type: 'Feature',
        geometry:
          result.geometry.type === 'GeometryCollection'
            ? getGeometryFromGeometryCollection({
                type: 'Point',
                geometryCollection: result.geometry,
              })
            : result.geometry,
        properties:
          type === 'RAW'
            ? {
                ...Object.keys(result.properties).reduce(
                  (acc: any, col: any) => {
                    if (['geog', 'boundary'].includes(col)) return acc;
                    acc[col] =
                      result.properties[col as keyof typeof result.properties];
                    return acc;
                  },
                  {},
                ),
              }
            : {
                ...columnDef.reduce((acc: any, col: any) => {
                  if (Object.keys(result).includes(col.dataIndex)) {
                    acc[col.title] = col.render
                      ? col.render(
                          result[col.dataIndex as keyof typeof result],
                          result,
                        )
                      : result[col.dataIndex as keyof typeof result];
                  }
                  return acc;
                }, {}),
              },
      };

      const getSubjectAddress = () => {
        let address = '';

        if (isLandParcelProperties(result.properties)) {
          const { formatted_street_address, city, state, zip_code } =
            result.properties;
          address = `${formatted_street_address || 'N/A'}, ${city || 'N/A'}, ${
            state || 'N/A'
          } ${zip_code || 'N/A'}`;
        } else if (isLandListingMLSProperties(result.properties)) {
          const { full_address, city, state_or_province, zipcode } =
            result.properties;
          address = `${full_address || 'N/A'}, ${city || 'N/A'}, ${
            state_or_province || 'N/A'
          } ${zipcode || 'N/A'}`;
        } else if (isLandListingShowcaseProperties(result.properties)) {
          const {
            address: full_address,
            address_city,
            address_sc,
            address_post,
          } = result.properties;
          address = `${full_address || 'N/A'}, ${address_city || 'N/A'}, ${
            address_sc || 'N/A'
          } ${address_post || 'N/A'}`;
        }
        return capitalize(address);
      };

      let featureName = '';
      if ((dataType = 'Parcel')) {
        featureName = getFeatureName(result.properties);
      } else {
        featureName = result.properties.sl_uuid;
      }

      pointFeature.properties['title'] = `${featureName} pin`;
      pointFeature.properties['name'] = `${featureName} pin`;
      pointFeature.properties[
        'description'
      ] = `${getSubjectAddress()}, owned by ${result.properties.owner_name}`;
      pointFeature.properties['marker-color'] = '#fff';
      pointFeature.properties['marker-size'] = 'large';
      pointFeature.properties['marker-symbol'] = '';

      if (result.geometry.type === 'GeometryCollection') {
        const boundary = getGeometryFromGeometryCollection({
          type: 'Polygon',
          geometryCollection: result.geometry,
        });
        const boundaryFeature = {
          type: 'Feature',
          geometry: boundary,
          properties: {} as any,
        };
        // simplestyle-spec properties
        boundaryFeature.properties['title'] = `${featureName} boundary`;
        boundaryFeature.properties['name'] = `${featureName} boundary`;
        boundaryFeature.properties['stroke'] = '#fff';
        boundaryFeature.properties['stroke-width'] = 1;
        boundaryFeature.properties['stroke-opacity'] = 1;
        boundaryFeature.properties['fill'] = '#fff';
        boundaryFeature.properties['fill-opacity'] = 0.4;
        geojson.features.push(boundaryFeature);
      }

      geojson.features.push(pointFeature);
    }
    // var tab = window.open('about:blank', '_blank');
    // tab.document.write(JSON.stringify(geojson)); // where 'html' is a variable containing your HTML
    // tab.document.close(); // to finish loading the page

    // if (resultMeta && Object.keys(resultMeta).includes('newHomeBuilder')) {
    //   const newHomeBuilders = resultMeta.newHomeBuilder;

    //   for (let i = 0; i < newHomeBuilders.length; i++) {
    //     const newHomeFeature = {
    //       type: 'Feature',
    //       geometry: newHomeBuilders[i].new_home_geom,
    //       properties: {
    //         title: newHomeBuilders[i].builder_name,
    //         name: newHomeBuilders[i].builder_name,
    //         'marker-color': '#0000FF',
    //         'marker-size': 'large',
    //         'marker-symbol': '',
    //       },
    //     };
    //     geojson.features.push(newHomeFeature);
    //   }
    // }

    return geojson;
  };

  // const getFeatureName = (record: LandParcel): string => {
  const getFeatureName = (record: any): string => {
    const { street_number, street_name, owner_name, area_acres, city, state } =
      record;

    let name = street_number != null ? `${street_number} ` : ``;
    name += street_name != null ? `${street_name}_` : ``;

    if (owner_name != null) {
      const owner =
        owner_name.length > 10 ? owner_name.substring(0, 10) : owner_name;
      name += `${owner}_`;
    }
    name += area_acres != null ? `${area_acres.toFixed(2)} ac_` : ``;
    name += city != null ? `${city}_` : ``;
    name += state != null ? `${state}` : ``;
    return name;
  };

  const generateGeoJSON = (type: 'RAW' | 'FORMATTED') => {
    // const dataToExport = getDataToExport();

    let features = structuredClone(data).filter((result) => {
      return customSelectedResults.includes(result.properties.sl_uuid);
    });

    // isLandListingMLSProperties

    let dataType = 'Parcel';
    if (filterPayload && filterPayload.type === 'Listing') {
      dataType = 'Listing';
    }

    const columnDef = getFormattedColumnDefinition(
      dataType as 'Parcel' | 'Listing',
    );

    const geojson = {
      type: 'FeatureCollection',
      features: features.map((result: LandParcelSearch.LandFeature) => {
        const feature = {
          type: 'Feature',
          geometry: result.geometry,
          // geometry: {
          //   type: 'GeometryCollection',
          //   geometries: [result.geometry],
          // } as any,
          properties:
            type === 'RAW'
              ? {
                  ...Object.keys(result.properties).reduce(
                    (acc: any, col: any) => {
                      if (['geog', 'boundary'].includes(col)) return acc;
                      acc[col] =
                        result.properties[
                          col as keyof typeof result.properties
                        ];
                      return acc;
                    },
                    {},
                  ),
                }
              : {
                  ...columnDef.reduce((acc: any, col: any) => {
                    if (
                      Object.keys(result.properties).includes(col.dataIndex)
                    ) {
                      acc[col.title] = col.render
                        ? col.render(
                            result.properties[
                              col.dataIndex as keyof typeof result.properties
                            ],
                            result.properties,
                          )
                        : result.properties[
                            col.dataIndex as keyof typeof result.properties
                          ];
                    }
                    return acc;
                  }, {}),
                },
        };
        // const boundary = searchResultBoundaries.find(
        //   (b) => b.sl_uuid === result.properties.sl_uuid,
        // );
        // if (boundary) {
        //   feature.geometry.geometries.push(boundary.boundary);
        // } else {
        //   if (feature.geometry.geometries.length > 0) {
        //     feature.geometry = { ...feature.geometry.geometries[0] };
        //   }
        // }

        const getSubjectAddress = () => {
          let address = '';

          if (isLandParcelProperties(result.properties)) {
            const { formatted_street_address, city, state, zip_code } =
              result.properties;
            address = `${formatted_street_address || 'N/A'}, ${
              city || 'N/A'
            }, ${state || 'N/A'} ${zip_code || 'N/A'}`;
          } else if (isLandListingMLSProperties(result.properties)) {
            const { full_address, city, state_or_province, zipcode } =
              result.properties;
            address = `${full_address || 'N/A'}, ${city || 'N/A'}, ${
              state_or_province || 'N/A'
            } ${zipcode || 'N/A'}`;
          } else if (isLandListingShowcaseProperties(result.properties)) {
            const {
              address: full_address,
              address_city,
              address_sc,
              address_post,
            } = result.properties;
            address = `${full_address || 'N/A'}, ${address_city || 'N/A'}, ${
              address_sc || 'N/A'
            } ${address_post || 'N/A'}`;
          }
          return capitalize(address);
        };

        // simplestyle-spec properties
        feature.properties['title'] = `${result.properties.sl_uuid}`;
        feature.properties['name'] = `${result.properties.sl_uuid}`;
        feature.properties['description'] = `${getSubjectAddress()}`;
        // ] = `${result.formatted_street_address}, ${result.city}, ${result.state} ${result.zip_code}, owned by ${result.owner_name}`;
        feature.properties['marker-color'] = '#3e59fb';
        feature.properties['marker-size'] = 'large';
        feature.properties['marker-symbol'] = '';
        feature.properties['stroke'] = '#3e59fb';
        feature.properties['stroke-width'] = 1;
        feature.properties['stroke-opacity'] = 1;
        feature.properties['fill'] = '#3e59fb';
        feature.properties['fill-opacity'] = 0.25;

        return feature;
      }),
    };

    // const geojson = {
    //   type: 'FeatureCollection',
    //   features: dataToExport.map((result: any) => {
    //     const feature = {
    //       type: 'Feature',
    //       geometry: {
    //         type: 'GeometryCollection',
    //         geometries: [result.geog, result.boundary],
    //       },
    //       properties:
    //         type === 'RAW'
    //           ? {
    //               ...Object.keys(result).reduce((acc: any, col: any) => {
    //                 if (['geog', 'boundary'].includes(col)) return acc;
    //                 acc[col] = result[col];
    //                 return acc;
    //               }, {}),
    //             }
    //           : {
    //               ...columnDef.reduce((acc: any, col: any) => {
    //                 if (Object.keys(result).includes(col.dataIndex)) {
    //                   acc[col.title] = col.render
    //                     ? col.render(result[col.dataIndex], result)
    //                     : result[col.dataIndex];
    //                 }
    //                 return acc;
    //               }, {}),
    //             },
    //     };

    //     // simplestyle-spec properties
    //     feature.properties['title'] = `${result.apn}`;
    //     feature.properties['name'] = `${result.apn}`;
    //     feature.properties[
    //       'description'
    //     ] = `${result.formatted_street_address}, ${result.city}, ${result.state} ${result.zip_code}, owned by ${result.owner_name}`;
    //     feature.properties['marker-color'] = '#3e59fb';
    //     feature.properties['marker-size'] = 'large';
    //     feature.properties['marker-symbol'] = '';
    //     feature.properties['stroke'] = '#3e59fb';
    //     feature.properties['stroke-width'] = 1;
    //     feature.properties['stroke-opacity'] = 1;
    //     feature.properties['fill'] = '#3e59fb';
    //     feature.properties['fill-opacity'] = 0.25;

    //     return feature;
    //   }),
    // };

    return geojson;
  };

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '12px',
        padding: '12px 24px',
      }}
    >
      <div>
        <strong>Select an export format:</strong>
      </div>
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'center',
          gap: '16px',
        }}
      >
        <Dropdown
          placement="bottom"
          disabled={customSelectedResults.length === 0}
          menu={{
            items: [
              {
                key: 'csv-raw-parcel',
                disabled: !(
                  filterPayload.type === 'Parcel' ||
                  activeResultTab === 'saved-sites'
                ),
                label: (
                  <div
                    style={{ width: '100%', height: '100%' }}
                    onClick={() => onExportCSVRaw({ type: 'parcel' })}
                  >
                    <span>Parcel</span>
                  </div>
                ),
              },
              {
                key: 'csv-raw-mls-listing',
                disabled: !(
                  filterPayload.type === 'Listing' ||
                  activeResultTab === 'saved-sites'
                ),
                label: (
                  <div
                    style={{ width: '100%', height: '100%' }}
                    onClick={() => onExportCSVRaw({ type: 'mls' })}
                  >
                    <span>MLS Listings</span>
                  </div>
                ),
              },
              {
                key: 'csv-raw-showcase-listing',
                disabled: !(
                  filterPayload.type === 'Listing' ||
                  activeResultTab === 'saved-sites'
                ),
                label: (
                  <div
                    style={{ width: '100%', height: '100%' }}
                    onClick={() => onExportCSVRaw({ type: 'showcase' })}
                  >
                    <span>Showcase Listings</span>
                  </div>
                ),
              },
            ],
          }}
        >
          <Button
            // onClick={() => onExportKML()}
            disabled={customSelectedResults.length === 0}
          >
            CSV Raw
          </Button>
        </Dropdown>
        {/* <Button
          onClick={() => onExportCSVRaw()}
          disabled={customSelectedResults.length === 0}
        >
          CSV Raw
        </Button> */}
        {/* {filterPayload && filterPayload.type === 'Parcel' && (
          <Button
            onClick={() => onExportCSVFormatted()}
            disabled={customSelectedResults.length === 0}
          >
            CSV Formatted
          </Button>
        )} */}
        <Dropdown
          placement="bottom"
          disabled={customSelectedResults.length === 0}
          menu={{
            items: [
              {
                key: 'csv-raw-parcel',
                disabled: !(
                  filterPayload.type === 'Parcel' ||
                  activeResultTab === 'saved-sites'
                ),
                label: (
                  <div
                    style={{ width: '100%', height: '100%' }}
                    onClick={() => onExportCSVFormatted()}
                  >
                    <span>Parcel</span>
                  </div>
                ),
              },
            ],
          }}
        >
          <Button
            // onClick={() => onExportCSVFormatted()}
            disabled={customSelectedResults.length === 0}
          >
            CSV Formatted
          </Button>
        </Dropdown>
        <Dropdown
          placement="bottom"
          disabled={customSelectedResults.length === 0}
          menu={{
            items: [
              {
                key: 'default',
                label: (
                  <div
                    style={{ width: '100%', height: '100%' }}
                    onClick={() => onExportKML('default')}
                  >
                    <span>Default</span>
                  </div>
                ),
              },
              {
                key: 'only-boundaries',
                label: (
                  <div
                    style={{ width: '100%', height: '100%' }}
                    onClick={() => onExportKML('only-boundary')}
                  >
                    <span>Only Boundaries</span>
                  </div>
                ),
              },
            ],
          }}
        >
          <Button
            // onClick={() => onExportKML()}
            disabled={customSelectedResults.length === 0}
          >
            KML
          </Button>
        </Dropdown>
        <Button
          onClick={() => onExportGeoJSON()}
          disabled={customSelectedResults.length === 0}
        >
          GeoJSON
        </Button>
      </div>
    </div>
  );
};

const ResultTabRender = ({
  title,
  exportDataMode,
  setExportDataMode,
  unfilteredData,
  filteredData,
  selectedResult,
  setSelectedResult,
}: {
  title: string;
  exportDataMode: boolean;
  setExportDataMode: (mode: boolean) => void;
  unfilteredData: LandParcelSearch.LandFeature[];
  filteredData: LandParcelSearch.LandFeature[];
  selectedResult: LandParcelSearch.LandFeature | null;
  setSelectedResult: (result: LandParcelSearch.LandFeature) => void;
}) => {
  const { openDetails } = useResultDisplay();
  const listRef = useRef({});
  const rowHeights = useRef({});

  const Row = ({ index, style }) => {
    const rowRef = useRef(null);
    const result = filteredData[index];

    useEffect(() => {
      if (rowRef.current) {
        setRowHeight(index, rowRef.current.clientHeight);
      }
    }, [rowRef]);

    return (
      <div style={style}>
        <ResultDisplayRow
          ref={rowRef}
          // key={index}
          result={result}
          formatter={valueFormatter}
          selected={isEqual(result, selectedResult)}
          onClick={() => {
            setSelectedResult(result);
          }}
        />
      </div>
    );
  };

  useEffect(() => {
    if (!selectedResult) return;

    const idx = filteredData.findIndex((d) => isEqual(d, selectedResult));
    if (idx > -1) {
      try {
        listRef.current.scrollToItem(idx, 'center');
      } catch (e) {
        console.error(e);
      }
    }
  }, [selectedResult, openDetails]);

  const getRowHeight = (index: number) => {
    return rowHeights.current[index as keyof typeof rowHeights.current] || 100;
  };

  const setRowHeight = (index, size) => {
    listRef.current.resetAfterIndex(index);
    rowHeights.current = { ...rowHeights.current, [index]: size };
  };
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        minHeight: 0,
      }}
    >
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'end',
          padding: '0 24px 12px',
          borderBottom: '1px solid #ccc',
        }}
      >
        <h3 style={{ fontSize: '16px', fontWeight: 500, margin: 0 }}>
          {title}
        </h3>
        <Button
          danger={exportDataMode}
          onClick={() => setExportDataMode((prevState) => !prevState)}
        >
          {!exportDataMode ? 'Export Data' : 'Exit Export'}
        </Button>
      </div>
      <div
        style={{
          height: '100%',
          minHeight: 0,
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        {!exportDataMode ? (
          <ResultModifiers
            unfiltered={unfilteredData}
            filtered={filteredData}
          />
        ) : (
          <ExportDataSelector data={filteredData} />
        )}

        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            height: '100%',
            minHeight: 0,
          }}
        >
          {filteredData && filteredData.length > 0 && (
            <>
              <ResultDisplay
                results={filteredData}
                columnOptions={getColumnOptions()}
              >
                <AutoSizer>
                  {({ height, width }) => (
                    <List
                      ref={listRef}
                      height={height}
                      width={width}
                      itemCount={filteredData.length}
                      itemSize={getRowHeight}
                    >
                      {Row}
                    </List>
                  )}
                </AutoSizer>
                <ResultDetails
                  // selectedResult={selectedResult}
                  // onClose={() => setSelectedResult(null)}
                  onClose={() => {}}
                  formatter={valueFormatter}
                  getTitleOfColumn={getTitleOfColumn}
                />
              </ResultDisplay>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export const ResultPanel = () => {
  const {
    searchResults,
    selectedResult,
    setSelectedResult,
    filteredResults,
    setFilteredResults,
    isLoading,
    activeResultTab,
    setActiveResultTab,
    setShowResultPanel,
    showFilterPanel,
    setShowFilterPanel,
  } = useLandParcelSearch();
  const { applyFilters } = useResultFilter();
  const { applySorting } = useResultSorter();
  const { applyFuzzySearch } = useResultFuzzySearch();
  const { savedResults, exportDataMode, setExportDataMode } =
    useResultDisplay();
  const { data: savedResultsData } = useQuery(
    `savedResultsData-${JSON.stringify(savedResults)}`,
    async () => {
      try {
        const data = (await getLandFeaturesBySpatialLaserIds({
          body: savedResults,
        })) as unknown;
        const results = [];
        if (Array.isArray(data) && data.length > 0) {
          for (let i = 0; i < data.length; i++) {
            const feature = LandFeatureSchema.parse(data[i]);
            results.push(feature);
          }
        }
        return results;
      } catch (e) {
        console.error(e);
      }
      return [] as LandParcelSearch.LandFeature[];
    },
    {
      keepPreviousData: true,
      enabled: activeResultTab !== 'all-sites' ? true : false,
    },
  );

  useEffect(() => {
    // const unfilteredData =
    //   activeResultTab === 'all-sites' ? searchResults : savedResults;
    const unfilteredData =
      activeResultTab === 'all-sites' ? searchResults : savedResultsData;

    let data = applyFilters(unfilteredData || []);
    data = applyFuzzySearch(data);
    data = applySorting(data);
    setFilteredResults(data);
  }, [
    applyFilters,
    applySorting,
    applyFuzzySearch,
    searchResults,
    // savedResults,
    savedResultsData,
    activeResultTab,
  ]);
  const showHideFilter = () => {
    setShowFilterPanel(!showFilterPanel);
  };
  return (
    <div
      id="land-parcel-search-result-panel"
      style={{
        width: '500px',
        borderLeft: '1px solid #ccc',
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
      }}
    >
      <div
        style={{
          display: 'flex',
          justifyContent: 'start',
          paddingLeft: '20px',
          paddingTop: '15px',
        }}
      >
        <Button type="link" onClick={showHideFilter} style={{ padding: 0 }}>
          {!showFilterPanel ? (
            <>
              <EyeOutlined /> {' Show Site Filter'}
            </>
          ) : (
            <>
              <EyeInvisibleOutlined />
              {' Hide Site Filter'}
            </>
          )}
        </Button>
      </div>
      <Tabs
        activeKey={activeResultTab}
        onChange={(key) => setActiveResultTab(key)}
        size="small"
        tabBarExtraContent={{ left: <div style={{ width: '24px' }}></div> }}
        style={{ height: '100%' }}
        items={[
          {
            label: `All Sites (${searchResults.length})`,
            key: 'all-sites',
            children: (
              <ResultTabRender
                title="Search Results"
                exportDataMode={exportDataMode}
                setExportDataMode={setExportDataMode}
                unfilteredData={searchResults}
                filteredData={filteredResults}
                selectedResult={selectedResult}
                setSelectedResult={setSelectedResult}
              />
            ),
          },
          {
            label: `Saved Sites (${savedResults?.length || 0})`,
            key: 'saved-sites',
            children: (
              <ResultTabRender
                title="Saved Results"
                exportDataMode={exportDataMode}
                setExportDataMode={setExportDataMode}
                unfilteredData={savedResultsData || []}
                filteredData={filteredResults}
                selectedResult={selectedResult}
                setSelectedResult={setSelectedResult}
              />
            ),
          },
        ]}
      />
    </div>
  );
};
