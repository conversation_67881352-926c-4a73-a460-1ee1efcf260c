import { Badge, Button, Input, InputNumber, Popover, Select } from 'antd';
import type { Identifier, XYCoord } from 'dnd-core';
import update from 'immutability-helper';
import {
  Dispatch,
  SetStateAction,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { GrDrag } from 'react-icons/gr';
import { IoClose } from 'react-icons/io5';
import { LandParcelSearch } from './types/types';

type ResultSorterContext =
  | {
      sorters: Sorter[];
      setSorters: Dispatch<SetStateAction<Sorter[]>>;
      getColumnType: any;
      setGetColumnType: any;
      applySorting: (data: any[]) => any[];
    }
  | undefined;

const resultSorterContext = createContext<ResultSorterContext>(undefined);

export type Sorter = {
  id: string;
  column: string | null;
  direction: 'ACS' | 'DESC';
};

const getEmptySorter = (column: string, direction: 'ACS' | 'DESC'): Sorter => ({
  id: crypto.randomUUID(),
  column: column || null,
  direction: direction || 'ACS',
});

export const ResultSorterProvider = <T,>({ children }: any) => {
  const [sorters, setSorters] = useState<Sorter[]>([]);
  const [getColumnType, setGetColumnType] = useState<any>(null);

  const applySorting = useCallback(
    (data: LandParcelSearch.LandFeature[]) => {
      const sortedData = [...data].sort((a, b) => {
        let result = 0;

        sorters.forEach((sorter) => {
          if (result !== 0) return;

          const { column, direction } = sorter;

          if (column) {
            const type = getColumnType
              ? getColumnType(column)
              : a.properties[column as keyof typeof a.properties] !== null
              ? typeof a.properties[column as keyof typeof a.properties]
              : typeof b.properties[column as keyof typeof a.properties];
            const aValue = a.properties[column as keyof typeof a.properties];
            const bValue = b.properties[column as keyof typeof a.properties];

            if (aValue === null && bValue === null) result = 0;
            else if (aValue === null) result = direction === 'ACS' ? -1 : 1;
            else if (bValue === null) result = direction === 'ACS' ? 1 : -1;
            else {
              if (
                type === 'number' &&
                typeof aValue === 'number' &&
                typeof bValue === 'number'
              )
                result =
                  direction === 'ACS' ? aValue - bValue : bValue - aValue;
              else if (
                type === 'string' &&
                typeof aValue === 'string' &&
                typeof bValue === 'string'
              ) {
                // if (column === 'mls_listing') {
                //   const aValue =
                //     a.properties[column as keyof typeof a.properties]?.status;
                //   const bValue =
                //     b.properties[column as keyof typeof b.properties]?.status;

                //   if (aValue === null && bValue === null) result = 0;
                //   else if (aValue === null)
                //     result = direction === 'ACS' ? -1 : 1;
                //   else if (bValue === null)
                //     result = direction === 'ACS' ? 1 : -1;
                //   else {
                //     const compareResult = aValue
                //       .toLowerCase()
                //       .localeCompare(bValue.toLowerCase());
                //     result =
                //       direction === 'ACS' ? compareResult : -compareResult;
                //   }
                // } else {
                const compareResult = aValue
                  .toLowerCase()
                  .localeCompare(bValue.toLowerCase());
                result = direction === 'ACS' ? compareResult : -compareResult;
                // }
              }
            }
          }
        });

        return result;
      });
      return sortedData;
    },
    [sorters, getColumnType],
  );

  return (
    <resultSorterContext.Provider
      value={{
        sorters,
        setSorters,
        getColumnType,
        setGetColumnType,
        applySorting,
      }}
    >
      {children}
    </resultSorterContext.Provider>
  );
};

export const useResultSorter = () => {
  const context = useContext(resultSorterContext);
  if (context === undefined) {
    throw new Error(
      'useResultSorter must be used within a ResultSorterProvider',
    );
  }
  return context;
};

export const useResultSortedData = <T,>(data: T[], getColumnType?: any) => {
  const { sorters } = useResultSorter();

  const sortedData = [...data].sort((a: any, b: any) => {
    let result = 0;

    sorters.forEach((sorter) => {
      if (result !== 0) return;

      const { column, direction } = sorter;

      if (column) {
        const type = getColumnType
          ? getColumnType(column)
          : a[column] !== null
          ? typeof a[column]
          : typeof b[column];
        const aValue = a[column];
        const bValue = b[column];

        if (aValue === null && bValue === null) result = 0;
        else if (aValue === null) result = direction === 'ACS' ? -1 : 1;
        else if (bValue === null) result = direction === 'ACS' ? 1 : -1;
        else {
          if (type === 'number')
            result = direction === 'ACS' ? aValue - bValue : bValue - aValue;
          else if (type === 'string') {
            const compareResult = aValue
              .toLowerCase()
              .localeCompare(bValue.toLowerCase());
            result = direction === 'ACS' ? compareResult : -compareResult;
          }
        }
      }
    });

    return result;
  });

  return sortedData;
};

const SorterItemUI = ({
  sorter,
  columnOptions,
  onUpdateSorter,
  onDeleteSorter,
  mouseOnDragPad,
}: any) => {
  // Select getPopupContainer reason:
  // https://ant.design/docs/react/faq#Select-Dropdown-DatePicker-TimePicker-Popover-Popconfirm-disappears-when-I-click-another-popup-component-inside-it.-How-do-I-resolve-this
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'row',
        gap: '16px',
        alignItems: 'center',
      }}
    >
      <div style={{ display: 'flex', flexDirection: 'row', gap: '8px' }}>
        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',
            height: '32px',
            cursor: 'move',
          }}
          onMouseEnter={() => mouseOnDragPad(true)}
          onMouseLeave={() => mouseOnDragPad(false)}
        >
          <GrDrag size={20} />
        </div>
        <div style={{ width: '200px' }}>
          <Select
            value={sorter.column}
            options={columnOptions}
            onChange={(value) => onUpdateSorter({ ...sorter, column: value })}
            placeholder="Column"
            style={{ width: '100%' }}
            getPopupContainer={(triggerNode) => triggerNode.parentNode}
          />
        </div>
      </div>
      <div style={{ width: '120px' }}>
        <Select
          value={sorter.direction}
          onChange={(value) => onUpdateSorter({ ...sorter, direction: value })}
          options={[
            { label: 'Ascending', value: 'ACS' },
            { label: 'Descending', value: 'DESC' },
          ]}
          placeholder="Direction"
          style={{ width: '100%' }}
          getPopupContainer={(triggerNode) => triggerNode.parentNode}
        />
      </div>
      {onDeleteSorter && (
        <Button onClick={onDeleteSorter}>
          <IoClose />
        </Button>
      )}
    </div>
  );
};

type DragNDropWrapperProps = {
  id?: any;
  index: number;
  type: string;
  moveFn: (dragIndex: number, hoverIndex: number) => void;
  styleFn?: (params: { isDragging: boolean }) => any;
  allowDrag?: () => boolean;
  children: React.ReactNode;
};

interface DragItem {
  index: number;
  id: string;
  type: string;
}

const DragNDropWrapper = ({
  id,
  index,
  type,
  moveFn,
  styleFn,
  allowDrag,
  children,
}: DragNDropWrapperProps) => {
  const ref = useRef<HTMLDivElement>(null);
  const [{ handlerId }, drop] = useDrop<
    DragItem,
    void,
    { handlerId: Identifier | null }
  >({
    accept: type,
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      };
    },
    hover(item: DragItem, monitor) {
      if (!ref.current) {
        return;
      }
      const dragIndex = item.index;
      const hoverIndex = index;
      // Don't replace items with themselves
      if (dragIndex === hoverIndex) {
        return;
      }
      // Determine rectangle on screen
      const hoverBoundingRect = ref.current?.getBoundingClientRect();
      // Get vertical middle
      const hoverMiddleY =
        (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      // Determine mouse position
      const clientOffset = monitor.getClientOffset();
      // Get pixels to the top
      const hoverClientY = (clientOffset as XYCoord).y - hoverBoundingRect.top;
      // Only perform the move when the mouse has crossed half of the items height
      // When dragging downwards, only move when the cursor is below 50%
      // When dragging upwards, only move when the cursor is above 50%
      // Dragging downwards
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return;
      }
      // Dragging upwards
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return;
      }
      // Time to actually perform the action
      moveFn(dragIndex, hoverIndex);
      // Note: we're mutating the monitor item here!
      // Generally it's better to avoid mutations,
      // but it's good here for the sake of performance
      // to avoid expensive index searches.
      item.index = hoverIndex;
    },
  });
  const [{ isDragging }, drag] = useDrag({
    type: type,
    item: () => {
      return { index };
    },
    ...(allowDrag && {
      canDrag: (monitor) => {
        return allowDrag();
      },
    }),
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  drag(drop(ref));

  return (
    <div
      ref={ref}
      data-handler-id={handlerId}
      style={styleFn && styleFn({ isDragging })}
    >
      <div style={{ visibility: isDragging ? 'hidden' : 'unset' }}>
        {children}
      </div>
    </div>
  );
};

type ResultSorterProps = {
  columnOptions: { label: string; value: string }[];
  columnValueType: ((column: string) => any) | null; // Ensure this function is memoized, not sure how to enforce this
};

export const ResultSorter = ({
  columnOptions,
  columnValueType = null,
}: ResultSorterProps) => {
  const { sorters, setSorters, setGetColumnType } = useResultSorter();

  const onDragPad = useRef<boolean>(false);

  useEffect(() => {
    // Ensure this function is memoized, not sure how to enforce this
    if (!columnValueType) return;
    setGetColumnType(() => columnValueType);
  }, [columnValueType]);

  const addNewSort = () => {
    const column = columnOptions.filter((option) =>
      sorters.length > 0
        ? !sorters.find((s) => s.column === option.value)
        : true,
    )[0]?.value;

    if (!column) return;
    setSorters((prevState) => [...prevState, getEmptySorter(column, 'ACS')]);
  };

  const moveSorter = useCallback((dragIndex: number, hoverIndex: number) => {
    setSorters((prevState) =>
      update(prevState, {
        $splice: [
          [dragIndex, 1],
          [hoverIndex, 0, prevState[dragIndex]],
        ],
      }),
    );
  }, []);

  const allowDrag = () => onDragPad.current;

  const renderSorter = useCallback(
    (sorter: Sorter, index: number) => {
      return (
        <DragNDropWrapper
          key={sorter.id}
          index={index}
          type="result-sorter"
          moveFn={moveSorter}
          allowDrag={allowDrag}
        >
          <SorterItemUI
            sorter={sorter}
            columnOptions={columnOptions.filter(
              (o) =>
                !sorters.find(
                  (s) => s.column === o.value && sorter.column !== o.value,
                ),
            )}
            onUpdateSorter={(newSorter: Sorter) => {
              setSorters((prevState) =>
                prevState.map((s) => (s.id === sorter.id ? newSorter : s)),
              );
            }}
            onDeleteSorter={() => {
              setSorters((prevState) =>
                prevState.filter((s) => s.id !== sorter.id),
              );
            }}
            mouseOnDragPad={(isOnDragPad: boolean) =>
              (onDragPad.current = isOnDragPad)
            }
          />
        </DragNDropWrapper>
      );
    },
    [columnOptions],
  );

  return (
    <Popover
      placement="bottom"
      trigger="click"
      content={
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '12px',
            minWidth: '430px',
            maxWidth: '430px',
          }}
        >
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: '12px',
                alignItems: 'end',
              }}
            >
              <h4
                style={{
                  margin: 0,
                  display: 'inline-block',
                  fontSize: '16px',
                }}
              >
                Sort by
              </h4>
            </div>
            <div>
              <Button
                onClick={() => setSorters([])}
                disabled={sorters.length === 0}
              >
                Clear All
              </Button>
            </div>
          </div>
          <div
            style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}
          >
            {sorters.length === 0 && (
              <div style={{ padding: '12px 24px', textAlign: 'center' }}>
                <span>
                  Sort results based on column priority in ascending or
                  descending mode.
                </span>
              </div>
            )}
            {sorters.length > 0 && (
              <DndProvider backend={HTML5Backend}>
                {sorters.map((sorter, index) => renderSorter(sorter, index))}
              </DndProvider>
            )}
          </div>
          <div>
            <Button onClick={addNewSort}>+ Add new sort</Button>
          </div>
        </div>
      }
    >
      {/* <Button>Sort</Button> */}
      <Button>
        {sorters.length === 0 && <span>Sort</span>}
        {sorters.length > 0 && (
          <Badge count={sorters.length} offset={[12, 0]}>
            <span>Sort</span>
          </Badge>
        )}
      </Button>
    </Popover>
  );
};
