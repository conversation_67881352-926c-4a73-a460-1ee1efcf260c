import { capitalize } from '@/utils/strings';
import { LandParcelSearch } from '../../types/types';
import {
  LandListingMLSPropertiesSchema,
  LandListingShowcasePropertiesSchema,
} from '../../types/zod-schemas';
import { valueFormatter } from '../../utils/formatting';

type LandParcel = LandParcelSearch.LandParcel;

interface FormattedColumns {
  title: string;
  dataIndex: string;
  render?: (text: string, record: LandParcel) => string;
}

export const getFormattedColumnDefinition = (
  type: 'Parcel' | 'Listing',
): Array<FormattedColumns> => {
  if (type === 'Parcel') {
    return getParcelColumns();
  } else {
    // Listing
    return getListingColumns();
  }
};

const getListingColumns = (): Array<FormattedColumns> => {
  const mlsKeys = Object.keys(LandListingMLSPropertiesSchema.shape);
  const showcaseKeys = Object.keys(LandListingShowcasePropertiesSchema.shape);
  const columns = [...new Set([...mlsKeys, ...showcaseKeys])];
  return columns.map((k) => ({
    title: k,
    dataIndex: k,
    render: (text: string) => {
      if (text !== null) {
        return valueFormatter(k, text);
      }
      return '';
    },
  }));
};

const getParcelColumns = (): Array<FormattedColumns> => {
  return [
    {
      title: 'Record Name / Map Pin Name',
      dataIndex: 'street_number',
      render: (_, record: LandParcel) => {
        const {
          street_number,
          street_name,
          owner_name,
          area_acres,
          city,
          state,
        } = record;

        let value = street_number != null ? `${street_number} ` : ``;
        value += street_name != null ? `${street_name}_` : ``;

        if (owner_name != null) {
          const owner =
            owner_name.length > 10 ? owner_name.substring(0, 10) : owner_name;
          value += `${owner}_`;
        }

        value += area_acres != null ? `${area_acres.toFixed(2)} ac_` : ``;
        value += city != null ? `${city}_` : ``;
        value += state != null ? `${state}` : ``;

        return value;
      },
    },
    {
      title: 'fips',
      dataIndex: 'fips',
      render: (text: string) => text,
    },
    {
      title: 'apn',
      dataIndex: 'apn',
      render: (text: string) => text,
    },
    {
      title: 'Sub. Prop. Street [# Name]',
      dataIndex: 'street_number',
      render: (text: string, record: LandParcel) => {
        const { street_number, street_name, street_suffix } = record;

        let value = street_number != null ? `${street_number} ` : ``;
        value += street_name != null ? `${street_name}` : ``;
        value += street_suffix != null ? ` ${street_suffix}` : ``;

        return value;
      },
    },
    {
      title: 'Sub. Prop. City',
      dataIndex: 'city',
      render: (text: string) => (text !== null ? text : ''),
    },
    {
      title: 'Sub. Prop. State',
      dataIndex: 'state',
      render: (text: string) => (text !== null ? text : ''),
    },
    {
      title: 'Sub. Prop. Zip',
      dataIndex: 'zip_code',
      render: (text: string) => (text !== null ? text : ''),
    },
    {
      title: 'Sub. Prop. Zip(+4)',
      dataIndex: 'zip_plus_four_code',
      render: (text: string) => (text !== null ? text : ''),
    },
    {
      title: 'Latitude',
      dataIndex: 'latitude',
      render: (text: string) => (text !== null ? text : ''),
    },
    {
      title: 'Longitude',
      dataIndex: 'longitude',
      render: (text: string) => (text !== null ? text : ''),
    },
    {
      title: 'census_tract',
      dataIndex: 'census_tract',
      render: (text: string) => (text !== null ? text : ''),
    },
    {
      title: 'carrier_code',
      dataIndex: 'carrier_code',
      render: (text: string) => (text !== null ? text : ''),
    },
    {
      title: 'Sub. Prop. Owner Name',
      dataIndex: 'owner_name',
      render: (text: string) => (text !== null ? text : ''),
    },
    {
      title: 'Seller Last Name',
      dataIndex: 'owner_name',
      render: (text: string) => {
        if (text != null) {
          return text.length > 10 ? text.substring(0, 10) : text;
        }
        return text;
      },
    },
    {
      title: 'Seller Last Name 2',
      dataIndex: 'owner_name',
      render: (text: string) => {
        if (text != null) {
          return text.length > 10 ? text.substring(0, 10) : text;
        }
        return text;
      },
    },
    {
      title: 'Seller Name',
      dataIndex: 'owner_name',
      render: (text: string) => (text !== null ? text : ''),
    },
    {
      title: 'Market MSA',
      dataIndex: 'cbsa_name',
      render: (text: string) => capitalize(text),
    },
    {
      title: 'Tax Mailing Street [# Name]',
      dataIndex: 'owner_street_address',
      render: (text: string) => (text !== null ? text : ''),
    },
    {
      title: 'Tax Mailing City',
      dataIndex: 'owner_city',
      render: (text: string) => (text !== null ? text : ''),
    },
    {
      title: 'Tax Mailing State',
      dataIndex: 'owner_state',
      render: (text: string) => (text !== null ? text : ''),
    },
    {
      title: 'Tax Mailing ZIP Code',
      dataIndex: 'owner_zip_code',
      render: (text: string) => (text !== null ? text : ''),
    },
    {
      title: 'county_name',
      dataIndex: 'county_name',
      render: (text: string) => (text !== null ? text : ''),
    },
    {
      title: 'cbsa_name',
      dataIndex: 'cbsa_name',
      render: (text: string) => (text !== null ? text : ''),
    },
    {
      title: 'improvement_value',
      dataIndex: 'improvement_value',
      render: (text: string) =>
        text !== null ? valueFormatter('improvement_value', text) : '',
    },
    {
      title: 'total_value',
      dataIndex: 'total_value',
      render: (text: string) =>
        text !== null ? valueFormatter('total_value', text) : '',
    },
    {
      title: 'elem',
      dataIndex: 'elem',
      render: (text: string) => (text !== null ? text : ''),
    },
    {
      title: 'middle',
      dataIndex: 'middle',
      render: (text: string) => (text !== null ? text : ''),
    },
    {
      title: 'high',
      dataIndex: 'high',
      render: (text: string) => (text !== null ? text : ''),
    },
    {
      title: 'improvement_ratio',
      dataIndex: 'improvement_ratio',
      render: (text: string) =>
        text !== null ? valueFormatter('improvement_ratio', text) : '',
    },
    {
      title: 'assessment_value_per_acre',
      dataIndex: 'assessment_value_per_acre',
      render: (text: string) =>
        text !== null ? valueFormatter('assessment_value_per_acre', text) : '',
    },
    {
      title: 'avg_school',
      dataIndex: 'avg_school',
      render: (text: string) =>
        text !== null ? valueFormatter('avg_school', text) : '',
    },
    {
      title: 'area_acres',
      dataIndex: 'area_acres',
      render: (text: string) =>
        text !== null ? valueFormatter('area_acres', text) : '',
    },
    {
      title: 'standardized_land_use_type',
      dataIndex: 'standardized_land_use_type',
      render: (text: string) => (text !== null ? text : ''),
    },
    {
      title: 'standardized_land_use_category',
      dataIndex: 'standardized_land_use_category',
      render: (text: string) => (text !== null ? text : ''),
    },
    {
      title: 'zoning',
      dataIndex: 'zoning',
      render: (text: string) => (text !== null ? text : ''),
    },
    {
      title: 'median_rent',
      dataIndex: 'median_rent',
      render: (text: string) =>
        text !== null ? valueFormatter('median_rent', text) : '',
    },
    {
      title: 'median_home_value',
      dataIndex: 'median_home_value',
      render: (text: string) =>
        text !== null ? valueFormatter('median_home_value', text) : '',
    },
    {
      title: 'population',
      dataIndex: 'population',
      render: (text: string) =>
        text !== null ? valueFormatter('population', text) : '',
    },
    {
      title: 'nearest_highway_distance_miles',
      dataIndex: 'nearest_highway_distance_miles',
      render: (text: string) =>
        text !== null
          ? valueFormatter('nearest_highway_distance_miles', text)
          : '',
    },
    {
      title: 'nearest_powerline_distance_miles',
      dataIndex: 'nearest_powerline_distance_miles',
      render: (text: string) =>
        text !== null
          ? valueFormatter('nearest_powerline_distance_miles', text)
          : '',
    },
    {
      title: 'flood_coverage',
      dataIndex: 'flood_coverage',
      render: (text: string) =>
        text !== null ? valueFormatter('flood_coverage', text) : '',
    },
    {
      title: 'Municipality Jurisdiction',
      dataIndex: 'municipality_jurisdiction',
      render: (text: string, record: LandParcel) => {
        if (text) {
          return `${text}`;
        }
        return '';
      },
    },
    {
      title: 'Spatial Laser Campaign Name',
      dataIndex: '',
      render: (text: string) => '',
    },
  ];
};
