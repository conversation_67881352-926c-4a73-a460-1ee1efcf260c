// const landParcelToKML = (landParcel: LandParcel): boolean => {
//   try {
//     // 1. Generate

//     return true;
//   } catch (error) {
//     console.error('Error in landParcelToKML', error);
//     return false;
//   }
// }

// const geojson = generateGeoJSONForKML('FORMATTED'); // only border

//     let kml = tokml(geo<PERSON>son, {
//       name: 'name',
//       description: 'description',
//       documentName: 'Land Parcel Search Results',
//       documentDescription: `Generated on ${moment().format(
//         'YYYY-MM-DD HH--mm--ss',
//       )}`,
//       simplestyle: true, // MultiGeometry is not supported by simplestyle/KML styling
//     });

//     const kmlFormatted = xmlFormatter(kml, {
//       indentation: '  ',
//       collapseContent: true,
//       lineSeparator: '\n',
//     });

//     const blob = new Blob([kmlFormatted], {
//       type: 'application/vnd.google-earth.kml+xml',
//     });
//     const link = document.createElement('a');
//     link.setAttribute('href', window.URL.createObjectURL(blob));
//     link.setAttribute(
//       'download',
//       `land_parcel_kml_${moment().format('YYYY-MM-DD HH--mm--ss')}.kml`,
//     );
//     document.body.appendChild(link);
//     link.click();
//     document.body.removeChild(link);
