import { ExclamationCircleFilled } from '@ant-design/icons';
import {
  <PERSON><PERSON>,
  AutoComplete,
  Button,
  Checkbox,
  Collapse,
  DatePicker,
  Empty,
  Form,
  Input,
  InputNumber,
  Modal,
  Popover,
  Segmented,
  Select,
  Slider,
  Switch,
  Tooltip,
} from 'antd';
import { clsx, type ClassValue } from 'clsx';
import isEqual from 'lodash.isequal';
import moment from 'moment';
import React, {
  ChangeEvent,
  SetStateAction,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';
import { BiTrashAlt } from 'react-icons/bi';
import { FaRegTrashAlt } from 'react-icons/fa';
import { IoInformationCircle } from 'react-icons/io5';
import { MdEdit } from 'react-icons/md';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { twMerge } from 'tailwind-merge';
import {
  DEFAULT_FILTER_PAYLOAD,
  ZoningSubtype,
  ZoningType,
  useLandParcelSearch,
} from '.';
import { LAND_DEVELOPMENT_FILTERS, dateFormat } from '../../constants';
import { useDebounce } from '../../hooks';
import {
  deleteLandSearchSavedFilters,
  getDemographicRangeData,
  getLandSearchSavedFilters,
  getNewHomeBuildersData,
  getPOIChainSearchData,
  postLandSearchSavedFilters,
} from '../../services/data';

function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

type SiteFilterProps = {
  children?: React.ReactNode;
};

type SiteFilterContextValues =
  | {
      filters: any;
      setFilters: React.Dispatch<SetStateAction<any>>;
      selectedDemographics: any;
      setSelectedDemographics: React.Dispatch<SetStateAction<any>>;
      chainOperator: 'AND' | 'OR';
      setChainOperator: React.Dispatch<SetStateAction<'AND' | 'OR'>>;
      chains: any[];
      setChains: React.Dispatch<SetStateAction<any[]>>;
      newHomeBuilders: any[];
      setNewHomeBuilders: React.Dispatch<SetStateAction<any[]>>;
      resetFilterStates: () => void;
      didFilterChange: () => boolean;
      activePanels: string[];
      setActivePanels: React.Dispatch<SetStateAction<string[]>>;
      siteFeaturesActive: boolean;
      setSiteFeaturesActive: React.Dispatch<SetStateAction<boolean>>;
      zoningActive: boolean;
      setZoningActive: React.Dispatch<SetStateAction<boolean>>;
      demographicsActive: boolean;
      setDemographicsActive: React.Dispatch<SetStateAction<boolean>>;
      advancedDemographicsActive: boolean;
      setAdvancedDemographicsActive: React.Dispatch<SetStateAction<boolean>>;
      chainStoresActive: boolean;
      setChainStoresActive: React.Dispatch<SetStateAction<boolean>>;
      newHomeActive: boolean;
      setNewHomeActive: React.Dispatch<SetStateAction<boolean>>;
      currentFilterState: Record<string, any>;
    }
  | undefined;

const SiteFilterContext = createContext<SiteFilterContextValues>(undefined);

const isFiltersEqual = (filter1: any, filter2: any) => {
  const filter1ParentKeys = Object.keys(filter1);
  const filter2ParentKeys = Object.keys(filter2);
  if (!isEqual(filter1ParentKeys, filter2ParentKeys)) return false;

  for (let i = 0; i < filter1ParentKeys.length; i++) {
    const parentKey = filter1ParentKeys[i];
    const filter1ChildKeys = Object.keys(filter1[parentKey]);
    const filter2ChildKeys = Object.keys(filter2[parentKey]);

    if (!isEqual(filter1ChildKeys, filter2ChildKeys)) return false;

    for (let j = 0; j < filter1ChildKeys.length; j++) {
      const childKey = filter1ChildKeys[j];

      if (filter1[parentKey][childKey] !== filter2[parentKey][childKey])
        return false;
    }
  }

  return true;
};

const DEFAULT_ACTIVE_PANELS = [] as string[];

export const SiteFilterProvider = ({ children }: any) => {
  const [filters, setFilters] = useState(
    structuredClone(LAND_DEVELOPMENT_FILTERS),
  );
  const [selectedDemographics, setSelectedDemographics] = useState<any[]>([]);
  const [chainOperator, setChainOperator] = useState<'AND' | 'OR'>('AND');
  const [chains, setChains] = useState<any[]>([]);
  const [newHomeBuilders, setNewHomeBuilders] = useState<any[]>([]);

  const [siteFeaturesActive, setSiteFeaturesActive] = useState<boolean>(true);
  const [zoningActive, setZoningActive] = useState<boolean>(false);
  const [demographicsActive, setDemographicsActive] = useState<boolean>(true);
  const [advancedDemographicsActive, setAdvancedDemographicsActive] =
    useState<boolean>(false);
  const [chainStoresActive, setChainStoresActive] = useState<boolean>(false);
  const [newHomeActive, setNewHomeActive] = useState<boolean>(false);

  const [activePanels, setActivePanels] = useState<string[]>(
    DEFAULT_ACTIVE_PANELS,
  );

  const {
    siteSelectionMethod,
    viewOnMapChainStores,
    setViewOnMapChainStores,
    viewOnMapDemographics,
    setViewOnMapDemographics,
    filterPayload,
    setFilterPayload,
  } = useLandParcelSearch();

  useEffect(() => {
    if (!didFilterChange()) return;
    resetFilterStates();
    return () => {
      resetFilterStates();
    };
  }, [siteSelectionMethod]);

  const resetFilterStates = useCallback(() => {
    setFilters(structuredClone(LAND_DEVELOPMENT_FILTERS));
    setSelectedDemographics([]);
    setChainOperator('AND');
    setChains([]);
    setNewHomeBuilders([]);
    setViewOnMapDemographics(false);
    setViewOnMapChainStores(false);
    setSiteFeaturesActive(true);
    setDemographicsActive(true);
    setAdvancedDemographicsActive(false);
    setChainStoresActive(false);
    setZoningActive(false);
    setNewHomeActive(false);
    setActivePanels(DEFAULT_ACTIVE_PANELS);
    setFilterPayload(DEFAULT_FILTER_PAYLOAD);
  }, []);

  const currentFilterState = React.useMemo(() => {
    return {
      filters,
      selectedDemographics,
      chainOperator,
      chains,
      newHomeBuilders,
      viewOnMapDemographics,
      viewOnMapChainStores,
      siteFeaturesActive,
      demographicsActive,
      advancedDemographicsActive,
      chainStoresActive,
      zoningActive,
      newHomeActive,
      activePanels,
      filterPayload,
    };
  }, [
    filters,
    selectedDemographics,
    chainOperator,
    chains,
    newHomeBuilders,
    viewOnMapDemographics,
    viewOnMapChainStores,
    siteFeaturesActive,
    demographicsActive,
    advancedDemographicsActive,
    chainStoresActive,
    zoningActive,
    newHomeActive,
    activePanels,
    filterPayload,
  ]);

  console.log('currentFilterState: ', currentFilterState);

  const didFilterChange = useCallback(() => {
    if (!isFiltersEqual(filters, LAND_DEVELOPMENT_FILTERS)) return true;
    if (selectedDemographics.length > 0) return true;
    if (chainOperator != 'AND') return true;
    if (chains.length > 0) return true;
    if (newHomeBuilders.length > 0) return true;
    if (viewOnMapDemographics || viewOnMapChainStores) return true;
    if (
      !siteFeaturesActive ||
      !demographicsActive ||
      advancedDemographicsActive ||
      chainStoresActive
    )
      return true;
    if (!isEqual(activePanels, DEFAULT_ACTIVE_PANELS)) return true;

    return false;
  }, [
    filters,
    selectedDemographics,
    chainOperator,
    chains,
    viewOnMapChainStores,
    siteFeaturesActive,
    demographicsActive,
    advancedDemographicsActive,
    chainStoresActive,
    activePanels,
  ]);

  return (
    <SiteFilterContext.Provider
      value={{
        filters,
        setFilters,
        selectedDemographics,
        setSelectedDemographics,
        chainOperator,
        setChainOperator,
        chains,
        setChains,
        newHomeBuilders,
        setNewHomeBuilders,
        resetFilterStates,
        didFilterChange,
        activePanels,
        setActivePanels,
        siteFeaturesActive,
        setSiteFeaturesActive,
        zoningActive,
        setZoningActive,
        demographicsActive,
        setDemographicsActive,
        advancedDemographicsActive,
        setAdvancedDemographicsActive,
        chainStoresActive,
        setChainStoresActive,
        newHomeActive,
        setNewHomeActive,
        currentFilterState,
      }}
    >
      {children}
    </SiteFilterContext.Provider>
  );
};

export const useSiteFilter = () => {
  const context = useContext(SiteFilterContext);
  if (context === undefined) {
    throw new Error('useSiteFilter must be used within a SiteFilterProvider');
  }
  return context;
};

export const SiteFilter = ({ children }: SiteFilterProps) => {
  const {
    setShowSiteSelectionPanel,
    setShowFilterPanel,
    setShowResultPanel,
    isLoading,
    handleSearchSubmit,
    filterPayload,
    setFilterPayload,
    setViewOnMapDemographics,
    setViewOnMapChainStores,
  } = useLandParcelSearch();

  const {
    currentFilterState,
    setFilters,
    didFilterChange,
    resetFilterStates,
    setSelectedDemographics,
    setChainOperator,
    setChains,
    setNewHomeBuilders,
    setSiteFeaturesActive,
    setDemographicsActive,
    setAdvancedDemographicsActive,
    setChainStoresActive,
    setZoningActive,
    setNewHomeActive,
    setActivePanels,
  } = useSiteFilter();

  const goToSiteSelection = () => {
    setShowSiteSelectionPanel(true);
    setShowFilterPanel(false);
    setShowResultPanel(false);
  };

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        width: '500px',
      }}
    >
      <div
        style={{
          borderBottom: '1px solid #ccc',
          padding: '12px 24px',
          height: '81px',
        }}
      >
        <Button type="link" onClick={goToSiteSelection} style={{ padding: 0 }}>
          {'< Site Selection'}
        </Button>
        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <h3 style={{ fontSize: '16px', fontWeight: 500, margin: 0 }}>
            Filters
          </h3>
          <Segmented<string>
            value={filterPayload.type}
            options={['Parcel', 'Listing']}
            onChange={(value) => {
              setFilterPayload((prevState) => ({
                ...prevState,
                type: value as 'Parcel' | 'Listing',
              }));
            }}
          />
          <Button
            type="link"
            disabled={!didFilterChange()}
            onClick={() => resetFilterStates()}
          >
            Reset
          </Button>
        </div>
      </div>

      <div
        style={{
          // padding: '16px 32px',
          display: 'flex',
          flexDirection: 'column',
          gap: '5px',
          overflowY: 'auto',
          height: '100%',
        }}
      >
        {children}
      </div>

      <div className="flex flex-row py-2.5 px-8 border-t border-[#ccc] justify-between">
        <SavedFilters
          currentFilters={currentFilterState}
          onApplySavedFilters={(payload?: Record<string, any> | null) => {
            console.log('onApplySavedFilters: ', payload);
            if (!payload) {
              if (payload === null) {
                resetFilterStates();
              }
            } else {
              setFilters(structuredClone(payload.filters));
              setSelectedDemographics(payload.selectedDemographics);
              setChainOperator(payload.chainOperator);
              setChains(payload.chains);
              setNewHomeBuilders(payload.newHomeBuilders);
              setViewOnMapDemographics(payload.viewOnMapDemographics);
              setViewOnMapChainStores(payload.viewOnMapChainStores);
              setSiteFeaturesActive(payload.siteFeaturesActive);
              setDemographicsActive(payload.demographicsActive);
              setAdvancedDemographicsActive(payload.advancedDemographicsActive);
              setChainStoresActive(payload.chainStoresActive);
              setZoningActive(payload.zoningActive);
              setNewHomeActive(payload.newHomeActive);
              setActivePanels(payload.activePanels);
              setFilterPayload(payload.filterPayload);
            }
          }}
        />
        <Button type="primary" onClick={handleSearchSubmit} loading={isLoading}>
          Search
        </Button>
      </div>
    </div>
  );
};

const DEFAULT_SAVED_FILTER = {
  filters: LAND_DEVELOPMENT_FILTERS,
  selectedDemographics: [],
  chainOperator: 'AND',
  chains: [],
  newHomeBuilders: [],
  viewOnMapDemographics: false,
  viewOnMapChainStores: false,
  siteFeaturesActive: true,
  demographicsActive: true,
  advancedDemographicsActive: false,
  chainStoresActive: false,
  zoningActive: false,
  newHomeActive: false,
  activePanels: DEFAULT_ACTIVE_PANELS,
  filterPayload: DEFAULT_FILTER_PAYLOAD,
};

// Sorry for this shitty code. Too lazy to fix...
// - Jason

const compareSavedFilters = (a, b) => {
  const keys = Object.keys(DEFAULT_SAVED_FILTER).filter(
    (k) => !['activePanels', 'filterPayload'].includes(k),
  );
  for (const key of keys) {
    console.log('key: ', key, isEqual(a[key], b[key]));
    if (!isEqual(a[key], b[key])) {
      return false;
    }
  }
  return true;
};

interface SavedFilter {
  id: string;
  name: string;
  filters: Record<string, any>;
  last_modified: string;
}

const SavedFilters = (props: {
  currentFilters: Record<string, any>;
  onApplySavedFilters: (payload?: Record<string, any> | null) => void;
}) => {
  const [showForm, setShowForm] = React.useState(false);
  const [open, setOpen] = React.useState(false);
  // const [savedFilters, setSavedFilters] = React.useState<Array<SavedFilter>>(
  //   [],
  // );
  const [selectedFilter, setSelectedFilter] = React.useState<
    undefined | SavedFilter | null
  >(null);

  const qc = useQueryClient();
  const {
    data: savedFilters = [],
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['landSearch-saved-filters'],
    queryFn: async () => {
      const res = (await getLandSearchSavedFilters()) as any;

      const data = res.map((d) => d.filter_payload) as Array<SavedFilter>;

      return data;
    },
  });

  console.log('savedFilters: ', savedFilters);

  const postSavedFilterMutation = useMutation(postLandSearchSavedFilters, {
    onSuccess: async (params) => {
      qc.invalidateQueries(['landSearch-saved-filters']);
    },
  });

  const deleteSavedFilterMutation = useMutation(deleteLandSearchSavedFilters, {
    onSuccess: async (params) => {
      qc.invalidateQueries(['landSearch-saved-filters']);
    },
  });

  React.useEffect(() => {
    if (!open && showForm) {
      setShowForm(false);
    }
  }, [open]);

  React.useEffect(() => {
    if (
      (selectedFilter &&
        !compareSavedFilters(props.currentFilters, selectedFilter.filters)) ||
      (selectedFilter === null &&
        !compareSavedFilters(props.currentFilters, DEFAULT_SAVED_FILTER))
    ) {
      setSelectedFilter(undefined);
    }
  }, [props.currentFilters]);

  const showConfirmSelectFilter = (filter: SavedFilter | null) => {
    Modal.confirm({
      centered: true,
      title: `Replace current filters?`,
      content: `Use ${filter !== null ? filter.name : 'Default'} filter.`,
      onOk() {
        setSelectedFilter(filter);
        setOpen(true);
        props.onApplySavedFilters(filter !== null ? filter.filters : null);
      },
      onCancel() {
        setOpen(true);
      },
    });
  };

  const showDeleteFilter = (filter: SavedFilter) => {
    Modal.confirm({
      centered: true,
      title: `Delete filter?`,
      icon: <ExclamationCircleFilled />,
      content: `Delete ${filter.name} filter.`,
      onOk() {
        // setSavedFilters((prev) => prev.filter((f) => f.id !== filter.id));

        deleteSavedFilterMutation.mutate({ filter_id: filter.id });

        setOpen(true);
        if (selectedFilter?.id === filter.id) {
          setSelectedFilter(undefined);
        }
      },
      onCancel() {
        setOpen(true);
      },
    });
  };

  const addFilter = React.useCallback(
    (values: any) => {
      const newSavedFilter = {
        id: crypto.randomUUID(),
        name: values.name,
        filters: props.currentFilters,
        last_modified: new Date().toISOString(),
      };
      // setSavedFilters((prev) => [...prev, newSavedFilter]);
      postSavedFilterMutation.mutate({
        body: newSavedFilter,
      });
      setSelectedFilter(newSavedFilter);
      setShowForm(false);
    },
    [props.currentFilters],
  );

  const onFinishFailed = React.useCallback((errorInfo: any) => {
    console.log('Failed:', errorInfo);
  }, []);

  return (
    <div className="flex flex-row gap-1 items-center">
      <span>Preset:</span>
      <Popover
        open={open}
        onOpenChange={setOpen}
        trigger="click"
        content={
          <div className="flex flex-col gap-2">
            {showForm && (
              <Form
                onFinish={addFilter}
                onFinishFailed={onFinishFailed}
                className="flex flex-row w-full gap-1 items-center !p-0 justify-around"
              >
                <Form.Item
                  name="name"
                  rules={[
                    { required: true, message: 'Please input a filter name!' },
                  ]}
                >
                  <Input placeholder="Name" />
                </Form.Item>
                <Form.Item label={null}>
                  <Button type="primary" htmlType="submit">
                    Save
                  </Button>
                </Form.Item>
              </Form>
            )}

            <div className="border-[#ccc] border rounded-md flex flex-col overflow-y-auto min-h-[250px] max-h-[250px] bg-muted">
              <div
                className={cn(
                  'flex flex-row items-center justify-between gap-2 border-b border-[#ccc] p-2 cursor-pointer',
                  selectedFilter === null
                    ? 'bg-blue-100'
                    : 'bg-background  hover:bg-background/60',
                )}
                onClick={(e) => {
                  e.stopPropagation();
                  showConfirmSelectFilter(null);
                }}
              >
                <div className="w-[250px] overflow-hidden">
                  <p className="text-nowrap truncate select-none">Default</p>
                </div>
              </div>
              {savedFilters.map((filter, idx) => (
                <div
                  key={filter.id}
                  className={cn(
                    'flex flex-row items-center justify-between gap-2 border-b border-[#ccc] p-2 cursor-pointer',
                    selectedFilter && selectedFilter?.id === filter.id
                      ? 'bg-blue-100'
                      : 'bg-background  hover:bg-background/60',
                  )}
                  onClick={(e) => {
                    e.stopPropagation();
                    showConfirmSelectFilter(filter);
                  }}
                >
                  <div className="w-[250px] overflow-hidden flex flex-col">
                    <p className="text-nowrap truncate select-none">
                      {filter.name}
                    </p>
                    <span>
                      {new Date(filter.last_modified).toLocaleString()}
                    </span>
                  </div>
                  <div className="flex flex-row gap-1 items-center">
                    <Button
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        showDeleteFilter(filter);
                      }}
                    >
                      <BiTrashAlt />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        }
        title={
          <div className="flex flex-row justify-between items-center">
            <span>Saved Filters</span>
            <Button onClick={() => setShowForm((prev) => !showForm)}>
              {!showForm ? 'Add' : 'Cancel'}
            </Button>
          </div>
        }
      >
        <Button type="link" className="p-0">
          {selectedFilter
            ? selectedFilter.name
            : selectedFilter === null
            ? 'Default'
            : 'Custom'}
        </Button>
      </Popover>
    </div>
  );
};

// prettier-ignore
const maxAssessmentPerAcreOptions = () => [
    {label: `$10,000`, value: 10000},
    {label: `$15,000`, value: 15000},
    {label: `$20,000`, value: 20000},
    {label: `$25,000`, value: 25000},
    {label: `$30,000`, value: 30000},
    {label: `$35,000`, value: 35000},
    {label: `$40,000`, value: 40000},
    {label: `$45,000`, value: 45000},
    {label: `$50,000`, value: 50000},
  ];

// prettier-ignore
const nearestHighwayOrPowerlineOptions = () => [
    {label: `0.1 Miles`, value: 0.1},
    {label: `0.3 Miles`, value: 0.3},
    {label: `0.5 Miles`, value: 0.5},
    {label: `1 Miles`, value: 1},
    {label: `1.5 Miles`, value: 1.5},
    {label: `2 Miles`, value: 2},
    {label: `3 Miles`, value: 3},
    {label: `5 Miles`, value: 5},
  ]

const numFormatter = (value: any) =>
  `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
const dollarFormatter = (value: any) =>
  `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
const parser = (value: any) =>
  value ? value.replace(/\$\s?|(,*)/g, '') : value;

export const FilterForm = () => {
  const { filterPayload, setFilterPayload, siteSelectionMethod } =
    useLandParcelSearch();

  const {
    siteFeaturesActive,
    setSiteFeaturesActive,
    zoningActive,
    setZoningActive,
    demographicsActive,
    setDemographicsActive,
    advancedDemographicsActive,
    setAdvancedDemographicsActive,
    chainStoresActive,
    setChainStoresActive,
    newHomeActive,
    setNewHomeActive,
  } = useSiteFilter();

  const { filters, setFilters, activePanels, setActivePanels } =
    useSiteFilter();

  useEffect(() => {
    const parcelPayload: any = {};

    const processValue = (key: any, value: any) => {
      if (key.includes('improvementRatio')) {
        return value / 100;
      }
      return value;
    };

    for (const key in filters) {
      if (
        !siteFeaturesActive &&
        [
          'lotSize',
          'schoolScore',
          'improvementRatio',
          'assessmentValuePerAcre',
          'nearestHighway',
          'nearestPowerLine',
          'floodCoverageWithin50',
          'buildingCoverage',
          'withinOpportunityZone',
          'withinResidentialDevelopment',
          'notSoldRecently',
          'boundaryLength',
          'boundaryWidth',
          'boundaryLxWRatio',
          'mlsListing',
          'showcase',
          'listingType',
          'mean_slope',
          'mean_aspect',
        ].includes(key)
      )
        continue;

      if (
        filterPayload.type === 'Parcel' &&
        ['mlsListing', 'showcase', 'listingType'].includes(key)
      )
        continue;

      if (
        filterPayload.type === 'Listing' &&
        [
          'lotSize',
          'improvementRatio',
          'assessmentValuePerAcre',
          'floodCoverageWithin50',
          'buildingCoverage',
          'notSoldRecently',
          'boundaryLength',
          'boundaryWidth',
          'boundaryLxWRatio',
          'mean_slope',
          'mean_aspect',
        ].includes(key)
      )
        continue;

      if (
        !demographicsActive &&
        [
          'medianRent',
          'homeValue',
          'householdIncome',
          'population',
          'tax_rate',
        ].includes(key)
      )
        continue;

      const payloadSetter = (filterItem: any) => {
        let payload: any;
        if (filterItem.type === '<->') {
          payload = {};
          payload.min = processValue(key, filterItem.min);
          payload.max = processValue(key, filterItem.max);
        } else if (filterItem.type === '==') {
          payload = {};
          payload.min = processValue(key, filterItem.min);
          payload.max = processValue(key, filterItem.min); // set max to min value
        } else if (filterItem.type === '>=') {
          payload = {};
          payload.min = processValue(key, filterItem.min);
        } else if (filterItem.type === '<=') {
          payload = {};
          payload.max = processValue(key, filterItem.max);
        } else if (filterItem.type === 'boolean') {
          payload = true;
        } else if (['string', 'string[]'].includes(filterItem.type)) {
          payload = filterItem.value;
        }
        return payload;
      };

      if (filters[key].isChecked) {
        const filterItem = filters[key];
        if (!filterItem.filters) {
          parcelPayload[key] = payloadSetter(filterItem);
        } else {
          parcelPayload[key] = {};
          for (const filter in filterItem.filters) {
            if (!filterItem.filters[filter].isChecked) continue;
            parcelPayload[key][filter] = payloadSetter(
              filterItem.filters[filter],
            );
          }
        }
      }

      // turn on section if it has an active filter
    }

    setFilterPayload((prev: any) => ({
      ...prev,
      site: parcelPayload,
    }));
  }, [filters, siteFeaturesActive, demographicsActive, filterPayload.type]);

  const onChangeFilter = (type: any, value: any) => {
    const newFilters = { ...filters };
    const typeArr = type.split('.');

    if (typeArr.length === 2) {
      newFilters[typeArr[0]][typeArr[1]] = value;
    } else if (typeArr.length === 3) {
      newFilters[typeArr[0]][typeArr[1]][typeArr[2]] = value;
    } else if (typeArr.length === 4) {
      newFilters[typeArr[0]][typeArr[1]][typeArr[2]][typeArr[3]] = value;
    }

    if (
      !siteFeaturesActive &&
      [
        'lotSize',
        'schoolScore',
        'improvementRatio',
        'assessmentValuePerAcre',
        'nearestHighway',
        'nearestPowerLine',
        'floodCoverageWithin50',
        'buildingCoverage',
        'withinOpportunityZone',
        'withinResidentialDevelopment',
        'notSoldRecently',
        'landUseCategory',
        'landUseType',
        'mlsListing',
        'showcase',
        'boundaryLength',
        'boundaryWidth',
        'boundaryLxWRatio',
        'mean_slope',
        'mean_aspect',
      ].includes(typeArr[0])
    ) {
      setSiteFeaturesActive(true);
      [
        'lotSize',
        'schoolScore',
        'improvementRatio',
        'assessmentValuePerAcre',
        'nearestHighway',
        'nearestPowerLine',
        'floodCoverageWithin50',
        'buildingCoverage',
        'withinOpportunityZone',
        'withinResidentialDevelopment',
        'notSoldRecently',
        'landUseCategory',
        'landUseType',
        'mlsListing',
        'showcase',
        'boundaryLength',
        'boundaryWidth',
        'boundaryLxWRatio',
        'mean_slope',
        'mean_aspect',
      ].forEach((filter) => {
        newFilters[filter].isChecked = false;
      });

      newFilters[typeArr[0]].isChecked = true;
    }
    if (
      !demographicsActive &&
      [
        'medianRent',
        'homeValue',
        'householdIncome',
        'population',
        'tax_rate',
      ].includes(typeArr[0])
    ) {
      setDemographicsActive(true);
      [
        'medianRent',
        'homeValue',
        'householdIncome',
        'population',
        'tax_rate',
      ].forEach((filter) => {
        newFilters[filter].isChecked = false;
      });
      newFilters[typeArr[0]].isChecked = true;
    }

    setFilters(newFilters);
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column' }}>
      {filterPayload.type === 'Listing' && (
        <div className="flex flex-col gap-3">
          <Segmented<string>
            value={filters.listingType.value}
            options={[
              {
                label: (
                  <div
                    className={`h-[40px] flex justify-center items-center${
                      filters.listingType.value === 'mls'
                        ? ' font-semibold'
                        : ''
                    }`}
                  >
                    MLS
                  </div>
                ),
                value: 'mls',
              },
              {
                label: (
                  <div
                    className={`h-[40px] flex justify-center items-center${
                      filters.listingType.value === 'showcase'
                        ? ' font-semibold'
                        : ''
                    }`}
                  >
                    Showcase
                  </div>
                ),
                value: 'showcase',
              },
            ]}
            onChange={(value) => {
              onChangeFilter('listingType.value', value);
            }}
            block
          />
        </div>
      )}
      <Collapse
        expandIconPosition="end"
        activeKey={activePanels}
        onChange={(key: any) => setActivePanels(key)}
      >
        <Collapse.Panel
          header={
            <SectionHeader
              title={'Site Features'}
              sectionActive={siteFeaturesActive}
              onClickActiveBtn={(checked: boolean, e: ChangeEvent) => {
                setSiteFeaturesActive(checked);

                if (checked && !activePanels.includes('site features')) {
                  setActivePanels((prev) => [...prev, 'site features']);
                } else if (!checked && activePanels.includes('site features')) {
                  setActivePanels((prev) =>
                    prev.filter((p) => p !== 'site features'),
                  );
                }

                e.stopPropagation();
              }}
            />
          }
          key={'site features'}
        >
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '24px',
              padding: '24px 32px',
            }}
          >
            {filterPayload.type === 'Parcel' && (
              <SiteFilterParcel onChangeFilter={onChangeFilter} />
            )}
            {filterPayload.type === 'Listing' && (
              <SiteFilterListing onChangeFilter={onChangeFilter} />
            )}
            {/* {baseFilterType === 'Default' && (
              <>
                <MultiFilterInput
                  label="Lot Size"
                  filterKey="lotSize"
                  filters={filters}
                  onChangeFilter={onChangeFilter}
                  formatter={numFormatter}
                  parser={parser}
                  step={5}
                  sectionActive={siteFeaturesActive}
                />

                <BoundaryDimensionInput
                  sectionActive={siteFeaturesActive}
                  filters={filters}
                  onChangeFilter={onChangeFilter}
                  formatter={numFormatter}
                  parser={parser}
                />
              </>
            )}

            <MultiFilterInput
              label="Average School Score"
              filterKey="schoolScore"
              filters={filters}
              onChangeFilter={onChangeFilter}
              sectionActive={siteFeaturesActive}
            />

            {baseFilterType === 'Default' && (
              <MultiFilterInput
                label="Improvement Ratio"
                filterKey="improvementRatio"
                filters={filters}
                onChangeFilter={onChangeFilter}
                sectionActive={siteFeaturesActive}
              />
            )}

            <LessThanFilterSelect
              label="Max Assessment Value Per Acre"
              filterKey="assessmentValuePerAcre"
              filters={filters}
              onChangeFilter={onChangeFilter}
              selectOptions={maxAssessmentPerAcreOptions()}
              sectionActive={siteFeaturesActive}
            />

            <LessThanFilterSelect
              label="Nearest Highway"
              filterKey="nearestHighway"
              filters={filters}
              onChangeFilter={onChangeFilter}
              selectOptions={nearestHighwayOrPowerlineOptions()}
              sectionActive={siteFeaturesActive}
            />

            <GreaterThanFilterSelect
              label="Nearest Power Line"
              filterKey="nearestPowerLine"
              filters={filters}
              onChangeFilter={onChangeFilter}
              selectOptions={nearestHighwayOrPowerlineOptions()}
              sectionActive={siteFeaturesActive}
            />

            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '24px',
              }}
            >
              {baseFilterType === 'Default' && (
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    gap: '12px',
                    alignItems: 'center',
                  }}
                >
                  <Switch
                    checked={
                      filters.floodCoverageWithin50.isChecked &&
                      siteFeaturesActive
                    }
                    onChange={(checked) =>
                      onChangeFilter('floodCoverageWithin50.isChecked', checked)
                    }
                  />
                  <span
                    style={{
                      fontWeight:
                        filters.floodCoverageWithin50.isChecked &&
                        siteFeaturesActive
                          ? 600
                          : 'normal',
                    }}
                  >
                    Flood Coverage {'<'} 50%
                  </span>
                </div>
              )}
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  gap: '12px',
                  alignItems: 'center',
                }}
              >
                <Switch
                  checked={
                    filters.withinOpportunityZone.isChecked &&
                    siteFeaturesActive
                  }
                  onChange={(checked) =>
                    onChangeFilter('withinOpportunityZone.isChecked', checked)
                  }
                />
                <span
                  style={{
                    fontWeight:
                      filters.withinOpportunityZone.isChecked &&
                      siteFeaturesActive
                        ? 600
                        : 'normal',
                  }}
                >
                  Within Opportunity Zone
                </span>
              </div>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  gap: '12px',
                  alignItems: 'center',
                }}
              >
                <Switch
                  checked={
                    filters.withinResidentialDevelopment.isChecked &&
                    siteFeaturesActive
                  }
                  onChange={(checked) =>
                    onChangeFilter(
                      'withinResidentialDevelopment.isChecked',
                      checked,
                    )
                  }
                />
                <span
                  style={{
                    fontWeight:
                      filters.withinResidentialDevelopment.isChecked &&
                      siteFeaturesActive
                        ? 600
                        : 'normal',
                  }}
                >
                  Early Stage Development
                </span>
              </div>
              {baseFilterType === 'Opportunity' && (
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '12px',
                  }}
                >
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      gap: '12px',
                      alignItems: 'center',
                    }}
                  >
                    <Switch
                      checked={
                        filters.mlsListing.isChecked && siteFeaturesActive
                      }
                      onChange={(checked) =>
                        onChangeFilter('mlsListing.isChecked', checked)
                      }
                    />
                    <span
                      style={{
                        fontWeight:
                          filters.mlsListing.isChecked && siteFeaturesActive
                            ? 600
                            : 'normal',
                      }}
                    >
                      MLS Listing
                    </span>
                  </div>

                  {filters.mlsListing.isChecked && siteFeaturesActive && (
                    <div style={{ marginLeft: 25 }}>
                      <Select
                        disabled={!filters.mlsListing.isChecked}
                        value={filters.mlsListing.value}
                        onChange={(value) =>
                          onChangeFilter('mlsListing' + '.value', value)
                        }
                        options={[
                          { label: 'All', value: 'All' },
                          { label: 'Active', value: 'Active' },
                          { label: 'Pending', value: 'Pending' },
                          { label: 'Closed', value: 'Closed' },
                          { label: 'Expired', value: 'Expired' },
                          { label: 'Canceled', value: 'Canceled' },
                        ]}
                        style={{ width: '150px' }}
                      />
                      <span> status.</span>
                    </div>
                  )}
                </div>
              )}
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '12px',
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    gap: '12px',
                    alignItems: 'center',
                  }}
                >
                  <Switch
                    checked={
                      filters.notSoldRecently.isChecked && siteFeaturesActive
                    }
                    onChange={(checked) =>
                      onChangeFilter('notSoldRecently.isChecked', checked)
                    }
                  />
                  <span
                    style={{
                      fontWeight:
                        filters.notSoldRecently.isChecked && siteFeaturesActive
                          ? 600
                          : 'normal',
                    }}
                  >
                    Not Sold Recently
                  </span>
                </div>
                {filters.notSoldRecently.isChecked && siteFeaturesActive && (
                  <div style={{ marginLeft: 25 }}>
                    <span>in the past </span>
                    <Select
                      disabled={!filters.notSoldRecently.isChecked}
                      value={filters.notSoldRecently.value}
                      onChange={(value) =>
                        onChangeFilter('notSoldRecently' + '.value', value)
                      }
                      options={[
                        { label: '1 year', value: '1 year' },
                        { label: '2 years', value: '2 years' },
                        { label: '3 years', value: '3 years' },
                      ]}
                      style={{ width: '150px' }}
                    />
                  </div>
                )}
              </div>
              
            </div> */}
          </div>
        </Collapse.Panel>
        <Collapse.Panel
          header={
            <SectionHeader
              title={'Zoning'}
              sectionActive={zoningActive}
              onClickActiveBtn={(checked: boolean, e: ChangeEvent) => {
                setZoningActive(checked);
                if (checked && !activePanels.includes('zoning')) {
                  setActivePanels((prev) => [...prev, 'zoning']);
                } else if (!checked && activePanels.includes('zoning')) {
                  setActivePanels((prev) => prev.filter((p) => p !== 'zoning'));
                }
                e.stopPropagation();
              }}
            />
          }
          key={'zoning'}
        >
          <ZoningFilterForm />
        </Collapse.Panel>
        <Collapse.Panel
          header={
            <SectionHeader
              title={'Demographics'}
              sectionActive={demographicsActive}
              onClickActiveBtn={(checked: boolean, e: ChangeEvent) => {
                setDemographicsActive(checked);
                if (checked && !activePanels.includes('demographics')) {
                  setActivePanels((prev) => [...prev, 'demographics']);
                } else if (!checked && activePanels.includes('demographics')) {
                  setActivePanels((prev) =>
                    prev.filter((p) => p !== 'demographics'),
                  );
                }
                e.stopPropagation();
              }}
            />
          }
          key={'demographics'}
        >
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '24px',
              padding: '24px 32px',
            }}
          >
            <MultiFilterInput
              label="Median Rent"
              filterKey="medianRent"
              filters={filters}
              onChangeFilter={onChangeFilter}
              formatter={dollarFormatter}
              parser={parser}
              step={100}
              sectionActive={demographicsActive}
            />

            <MultiFilterInput
              label="Home Value"
              filterKey="homeValue"
              filters={filters}
              onChangeFilter={onChangeFilter}
              formatter={dollarFormatter}
              parser={parser}
              step={10000}
              sectionActive={demographicsActive}
            />

            <MultiFilterInput
              label="Household Income"
              filterKey="householdIncome"
              filters={filters}
              onChangeFilter={onChangeFilter}
              formatter={dollarFormatter}
              parser={parser}
              step={1000}
              sectionActive={demographicsActive}
            />

            <MultiFilterInput
              label="Population (within 3 miles)"
              filterKey="population"
              filters={filters}
              onChangeFilter={onChangeFilter}
              formatter={numFormatter}
              parser={parser}
              step={5000}
              sectionActive={demographicsActive}
            />
            <MultiFilterInput
              label="Tax Rate (%)"
              filterKey="tax_rate"
              filters={filters}
              onChangeFilter={onChangeFilter}
              formatter={numFormatter}
              parser={parser}
              step={0.1}
              sectionActive={demographicsActive}
            />
            <AdvancedDemographicsForm />
          </div>
        </Collapse.Panel>

        <Collapse.Panel
          header={
            <SectionHeader
              title={'Chain Stores'}
              sectionActive={chainStoresActive}
              onClickActiveBtn={(checked: boolean, e: ChangeEvent) => {
                setChainStoresActive(checked);
                if (checked && !activePanels.includes('chain stores')) {
                  setActivePanels((prev) => [...prev, 'chain stores']);
                } else if (!checked && activePanels.includes('chain stores')) {
                  setActivePanels((prev) =>
                    prev.filter((p) => p !== 'chain stores'),
                  );
                }
                e.stopPropagation();
              }}
            />
          }
          key={'chain stores'}
        >
          <ChainStoreForm sectionActive={chainStoresActive} />
        </Collapse.Panel>
        <Collapse.Panel
          key={'new homes'}
          header={
            <SectionHeader
              title={'New Homes'}
              sectionActive={newHomeActive}
              onClickActiveBtn={(checked: boolean, e: ChangeEvent) => {
                setNewHomeActive(checked);
                if (checked && !activePanels.includes('new homes')) {
                  setActivePanels((prev) => [...prev, 'new homes']);
                } else if (!checked && activePanels.includes('new homes')) {
                  setActivePanels((prev) =>
                    prev.filter((p) => p !== 'new homes'),
                  );
                }
                e.stopPropagation();
              }}
            />
          }
        >
          <NewHomesForm sectionActive={newHomeActive} />
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};

const SiteFilterParcel = ({
  onChangeFilter,
}: {
  onChangeFilter: (type: any, value: any) => void;
}) => {
  const { filters, siteFeaturesActive } = useSiteFilter();

  return (
    <>
      <MultiFilterInput
        label="Lot Size (ac)"
        filterKey="lotSize"
        filters={filters}
        onChangeFilter={onChangeFilter}
        formatter={numFormatter}
        parser={parser}
        step={5}
        sectionActive={siteFeaturesActive}
      />

      <BoundaryDimensionInput
        sectionActive={siteFeaturesActive}
        filters={filters}
        onChangeFilter={onChangeFilter}
        formatter={numFormatter}
        parser={parser}
      />

      <MultiFilterInput
        label="Average School Score"
        filterKey="schoolScore"
        filters={filters}
        onChangeFilter={onChangeFilter}
        sectionActive={siteFeaturesActive}
      />

      <MultiFilterInput
        label="Improvement Ratio (%)"
        filterKey="improvementRatio"
        filters={filters}
        onChangeFilter={onChangeFilter}
        sectionActive={siteFeaturesActive}
      />

      <LessThanFilterSelect
        label="Max Assessment Value Per Acre"
        filterKey="assessmentValuePerAcre"
        filters={filters}
        onChangeFilter={onChangeFilter}
        selectOptions={maxAssessmentPerAcreOptions()}
        sectionActive={siteFeaturesActive}
      />

      <LessThanFilterSelect
        label="Nearest Highway"
        filterKey="nearestHighway"
        filters={filters}
        onChangeFilter={onChangeFilter}
        selectOptions={nearestHighwayOrPowerlineOptions()}
        sectionActive={siteFeaturesActive}
      />

      <GreaterThanFilterSelect
        label="Nearest Power Line"
        filterKey="nearestPowerLine"
        filters={filters}
        onChangeFilter={onChangeFilter}
        selectOptions={nearestHighwayOrPowerlineOptions()}
        sectionActive={siteFeaturesActive}
      />

      <SwitchFilterInput
        label="Flood Coverage < 50%"
        filterKey="floodCoverageWithin50"
        onChangeFilter={onChangeFilter}
        sectionActive={
          filters.floodCoverageWithin50.isChecked && siteFeaturesActive
        }
      />

      <MultiFilterInput
        label="Building Coverage (%)"
        filterKey="buildingCoverage"
        filters={filters}
        onChangeFilter={onChangeFilter}
        sectionActive={siteFeaturesActive}
      />

      <SwitchFilterInput
        label="Within Opportunity Zone"
        filterKey="withinOpportunityZone"
        onChangeFilter={onChangeFilter}
        sectionActive={
          filters.withinOpportunityZone.isChecked && siteFeaturesActive
        }
      />

      <SwitchFilterInput
        label="Early Stage Development"
        filterKey="withinResidentialDevelopment"
        onChangeFilter={onChangeFilter}
        sectionActive={
          filters.withinResidentialDevelopment.isChecked && siteFeaturesActive
        }
      />

      <SwitchFilterInput
        label="Not Sold Recently"
        filterKey="notSoldRecently"
        onChangeFilter={onChangeFilter}
        sectionActive={filters.notSoldRecently.isChecked && siteFeaturesActive}
      >
        {filters.notSoldRecently.isChecked && siteFeaturesActive && (
          <div style={{ marginLeft: 25 }}>
            <span>in the past </span>
            <Select
              disabled={!filters.notSoldRecently.isChecked}
              value={filters.notSoldRecently.value}
              onChange={(value) =>
                onChangeFilter('notSoldRecently' + '.value', value)
              }
              options={[
                { label: '1 year', value: '1 year' },
                { label: '2 years', value: '2 years' },
                { label: '3 years', value: '3 years' },
              ]}
              style={{ width: '150px' }}
            />
          </div>
        )}
      </SwitchFilterInput>
      <SwitchFilterInput
        label="Land Use Category"
        filterKey="landUseCategory"
        onChangeFilter={onChangeFilter}
        sectionActive={filters.landUseCategory.isChecked && siteFeaturesActive}
      >
        <LandUseCategoryFilter
          filters={filters}
          onChangeFilter={onChangeFilter}
          sectionActive={
            filters.landUseCategory.isChecked && siteFeaturesActive
          }
        />
      </SwitchFilterInput>
      <SwitchFilterInput
        label="Land Use Type"
        filterKey="landUseType"
        onChangeFilter={onChangeFilter}
        sectionActive={filters.landUseType.isChecked && siteFeaturesActive}
      >
        <LandUseTypeFilter
          filters={filters}
          onChangeFilter={onChangeFilter}
          sectionActive={filters.landUseType.isChecked && siteFeaturesActive}
        />
      </SwitchFilterInput>
      <MultiFilterInput
        label="Mean Slope (°)"
        filterKey="mean_slope"
        filters={filters}
        onChangeFilter={onChangeFilter}
        sectionActive={siteFeaturesActive}
      />
      <SwitchFilterInput
        label="Mean Aspect (°)"
        filterKey="mean_aspect"
        onChangeFilter={onChangeFilter}
        sectionActive={filters.mean_aspect.isChecked && siteFeaturesActive}
      >
        {filters.mean_aspect.isChecked && siteFeaturesActive && (
          <div style={{ marginLeft: 25 }}>
            <Select
              disabled={!filters.mean_aspect.isChecked}
              value={filters.mean_aspect.value}
              onChange={(value) =>
                onChangeFilter('mean_aspect' + '.value', value)
              }
              options={[
                { label: 'Flat (-1)', value: '{ "min": -1, "max": -1 }' },
                { label: 'North (0-22.5)', value: '{ "min": 0, "max": 22.5 }' },
                {
                  label: 'Northeast (22.5-67.5)',
                  value: '{ "min": 22.5, "max": 67.5 }',
                },
                {
                  label: 'East (67.5-112.5)',
                  value: '{ "min": 67.5, "max": 112.5 }',
                },
                {
                  label: 'Southeast (112.5-157.5)',
                  value: '{ "min": 112.5, "max": 157.5 }',
                },
                {
                  label: 'South (157.5-202.5)',
                  value: '{ "min": 157.5, "max": 202.5 }',
                },
                {
                  label: 'Southwest (202.5-247.5)',
                  value: '{ "min": 202.5, "max": 247.5 }',
                },
                {
                  label: 'West (247.5-292.5)',
                  value: '{ "min": 247.5, "max": 292.5 }',
                },
                {
                  label: 'Northwest (292.5-337.5)',
                  value: '{ "min": 292.5, "max": 337.5 }',
                },
                {
                  label: 'North (337.5-360)',
                  value: '{ "min": 337.5, "max": 360 }',
                },
              ]}
              style={{ width: '100%' }}
            />
          </div>
        )}
      </SwitchFilterInput>
    </>
  );
};

const SiteFilterListing = ({
  onChangeFilter,
}: {
  onChangeFilter: (type: any, value: any) => void;
}) => {
  const { filters, siteFeaturesActive } = useSiteFilter();

  const subOnChangeFilter = useCallback(
    (type: any, value: any) => {
      const filter = { ...filters.mlsListing.filters };
      // console.log('filter', filter);
      // console.log('type', type);
      const keys = type.split('.');
      filter[keys[0]][keys[1]] = value;
      onChangeFilter(`mlsListing.filters`, filter);
    },
    [filters],
  );

  return (
    <>
      {filters.listingType.value === 'mls' && (
        <MLSListingFilterForm
          filters={filters}
          onChangeFilter={onChangeFilter}
          sectionActive={filters.mlsListing.isChecked && siteFeaturesActive}
        />
      )}

      {filters.listingType.value === 'showcase' && (
        <ShowcaseListingFilterForm
          filters={filters}
          onChangeFilter={onChangeFilter}
          sectionActive={filters.showcase.isChecked && siteFeaturesActive}
        />
      )}

      <MultiFilterInput
        label="Average School Score"
        filterKey="schoolScore"
        filters={filters}
        onChangeFilter={onChangeFilter}
        sectionActive={siteFeaturesActive}
      />

      <LessThanFilterSelect
        label="Nearest Highway"
        filterKey="nearestHighway"
        filters={filters}
        onChangeFilter={onChangeFilter}
        selectOptions={nearestHighwayOrPowerlineOptions()}
        sectionActive={siteFeaturesActive}
      />

      <GreaterThanFilterSelect
        label="Nearest Power Line"
        filterKey="nearestPowerLine"
        filters={filters}
        onChangeFilter={onChangeFilter}
        selectOptions={nearestHighwayOrPowerlineOptions()}
        sectionActive={siteFeaturesActive}
      />

      <SwitchFilterInput
        label="Within Opportunity Zone"
        filterKey="withinOpportunityZone"
        onChangeFilter={onChangeFilter}
        sectionActive={
          filters.withinOpportunityZone.isChecked && siteFeaturesActive
        }
      />

      <SwitchFilterInput
        label="Early Stage Development"
        filterKey="withinResidentialDevelopment"
        onChangeFilter={onChangeFilter}
        sectionActive={
          filters.withinResidentialDevelopment.isChecked && siteFeaturesActive
        }
      />
    </>
  );
};

const MLSListingFilterForm = ({
  filters,
  onChangeFilter,
  sectionActive,
}: any) => {
  const subOnChangeFilter = useCallback(
    (type: any, value: any) => {
      const filter = { ...filters.mlsListing.filters };
      // console.log('filter', filter);
      // console.log('type', type);
      const keys = type.split('.');
      filter[keys[0]][keys[1]] = value;
      onChangeFilter(`mlsListing.filters`, filter);
    },
    [filters],
  );

  return (
    // <div className="ml-[25px] flex flex-col gap-3">
    <div className="flex flex-col gap-3">
      <SwitchFilterInput
        label="Status"
        filterKey="status"
        onChangeFilter={subOnChangeFilter}
        sectionActive={filters.mlsListing.filters.status.isChecked}
      >
        {filters.mlsListing.filters.status.isChecked && (
          <div style={{ marginLeft: 25 }}>
            <Select
              disabled={
                !filters.mlsListing.filters.status.isChecked || !sectionActive
              }
              value={filters.mlsListing.filters.status.value}
              onChange={(value) =>
                onChangeFilter('mlsListing.filters', {
                  ...filters.mlsListing.filters,
                  status: {
                    ...filters.mlsListing.filters.status,
                    value: value,
                  },
                })
              }
              options={[
                { label: 'Active', value: 'Active' },
                { label: 'Pending', value: 'Pending' },
                { label: 'Closed', value: 'Closed' },
                { label: 'Expired', value: 'Expired' },
                { label: 'Canceled', value: 'Canceled' },
              ]}
              style={{ width: '150px' }}
            />
          </div>
        )}
      </SwitchFilterInput>

      <MultiFilterInput
        label="Lot Size (ac)"
        filterKey="lotSize"
        filters={filters.mlsListing.filters}
        onChangeFilter={subOnChangeFilter}
        formatter={numFormatter}
        parser={parser}
        step={5}
        sectionActive={sectionActive}
      />

      <MultiFilterInput
        label="Price"
        filterKey="price"
        filters={filters.mlsListing.filters}
        onChangeFilter={subOnChangeFilter}
        formatter={dollarFormatter}
        parser={parser}
        step={5}
        sectionActive={sectionActive}
      />
    </div>
  );
};

const ShowcaseListingFilterForm = ({
  filters,
  onChangeFilter,
  sectionActive,
}: any) => {
  const subOnChangeFilter = useCallback(
    (type: any, value: any) => {
      const filter = { ...filters.showcase.filters };
      const keys = type.split('.');
      filter[keys[0]][keys[1]] = value;
      onChangeFilter(`showcase.filters`, filter);
    },
    [filters],
  );

  return (
    <div className="flex flex-col gap-3">
      <MultiFilterInput
        label="Lot Size (ac)"
        filterKey="lotSize"
        filters={filters.showcase.filters}
        onChangeFilter={subOnChangeFilter}
        formatter={numFormatter}
        parser={parser}
        step={5}
        sectionActive={sectionActive}
      />
      <SwitchFilterInput
        label="Listing Type"
        filterKey="for_type"
        onChangeFilter={subOnChangeFilter}
        sectionActive={filters.showcase.filters.for_type.isChecked}
      >
        {filters.showcase.filters.for_type.isChecked && (
          <div style={{ marginLeft: 25 }}>
            <Select
              disabled={
                !filters.showcase.filters.for_type.isChecked || !sectionActive
              }
              value={filters.showcase.filters.for_type.value}
              onChange={(value) =>
                onChangeFilter('showcase.filters', {
                  ...filters.showcase.filters,
                  for_type: {
                    ...filters.showcase.filters.for_type,
                    value: value,
                  },
                })
              }
              options={[
                { label: 'Land For Auction', value: 'LandForAuction' },
                { label: 'Land For Sale', value: 'LandForSale' },
              ]}
              mode="multiple"
              maxTagCount="responsive"
              allowClear={true}
              placeholder="Select For Type"
              style={{ width: '100%' }}
            />
          </div>
        )}
      </SwitchFilterInput>

      <SwitchFilterInput
        label="Landuse Type"
        filterKey="sub_type"
        onChangeFilter={subOnChangeFilter}
        sectionActive={filters.showcase.filters.sub_type.isChecked}
      >
        {filters.showcase.filters.sub_type.isChecked && (
          <div style={{ marginLeft: 25 }}>
            <Select
              disabled={
                !filters.showcase.filters.sub_type.isChecked || !sectionActive
              }
              value={filters.showcase.filters.sub_type.value}
              onChange={(value) =>
                onChangeFilter('showcase.filters', {
                  ...filters.showcase.filters,
                  sub_type: {
                    ...filters.showcase.filters.sub_type,
                    value: value,
                  },
                })
              }
              options={[
                { label: 'Agricultural', value: 'Agricultural' },
                { label: 'Commercial', value: 'Commercial' },
                { label: 'Industrial', value: 'Industrial' },
                { label: 'Residential', value: 'Residential' },
              ]}
              mode="multiple"
              maxTagCount="responsive"
              allowClear={true}
              placeholder="Select Sub Type"
              style={{ width: '100%' }}
            />
          </div>
        )}
      </SwitchFilterInput>

      <MultiFilterInput
        label="Sale Price"
        filterKey="sale_price"
        filters={filters.showcase.filters}
        onChangeFilter={subOnChangeFilter}
        formatter={dollarFormatter}
        parser={parser}
        step={1000}
        sectionActive={sectionActive}
      />
    </div>
  );
};

// prettier-ignore
const LAND_USE_CATEGORIES = ['Undefined', 'AGRICULTURE / FARMING', 'COMMERCIAL', 'INDUSTRIAL', 'OTHER', 'OTHER / UNKNOWN', 'PUBLIC WORKS', 'RESIDENTIAL', 'VACANT LAND'];
// prettier-ignore
const LAND_USE_TYPES = ["Undefined", "467", "468", "470", "471", "ABANDONED SITE, CONTAMINATED SITE", "AGRICULTURAL/RURAL (GENERAL)", "AGRICULTURAL (UNIMPROVED) - VACANT LAND", "AIRPORT & RELATED", "AMUSEMENT PARK, TOURIST ATTRACTION", "APARTMENT HOUSE (100+ UNITS)", "APARTMENT HOUSE (5+ UNITS)", "APARTMENTS (GENERIC)", "APPLIANCE STORE (CIRCUIT CITY, GOODS BUYS, BEST BUY)", "ARCADES (AMUSEMENT)", "ARENA, CONVENTION CENTER", "ASSEMBLY (LIGHT INDUSTRIAL)", "AUDITORIUM", "AUTO REPAIR, GARAGE", "BAKERY", "BARBER/HAIR SALON", "BARNDOMINIUM", "BAR, TAVERN", "BED & BREAKFAST", "BOARDING/ROOMING HOUSE, APT HOTEL", "BOWLING ALLEY", "BULK STORAGE, TANKS (GASOLINE, FUEL, ETC)", "BUNGALOW (RESIDENTIAL)", "BUS TERMINAL", "CABLE TV STATION", "CAMPGROUND, RV PARK", "CANNABIS DISPENSARY", "CANNABIS GROW FACILITY", "CANNERY", "CAR WASH", "CAR WASH - AUTOMATED", "CAR WASH - SELF-SERVE", "CASINO", "CELLULAR", "CEMETERY (EXEMPT)", "CEMETERY, FUNERAL HOME, MORTUARY (COMMERCIAL)", "CENTRALLY ASSESSED", "CHARITABLE ORGANIZATION, FRATERNAL", "CHEMICAL", "CHILDREN'S HOME, ORPHANAGE", "CITY, MUNICIPAL, TOWN, VILLAGE OWNED (EXEMPT)", "CLUBS, LODGES, PROFESSIONAL ASSOCIATIONS", "CLUSTER HOME", "COLD STORAGE", "COLLEGES, UNIVERSITY - PUBLIC", "COLLEGE, UNIVERSITY, VOCATIONAL SCHOOL - PRIVATE", "COMMERCIAL AUTO TRANSPORTATION/STORAGE", "COMMERCIAL BUILDING, MAIL ORDER, SHOW ROOM (NON-AUTO), WAREHOUSE", "COMMERCIAL CONDOMINIUM (NOT OFFICES)", "COMMERCIAL (GENERAL)", "COMMERCIAL MISCELLANEOUS", "COMMERCIAL OFFICE (GENERAL)", "COMMERCIAL OFFICE/RESIDENTIAL (MIXED USE)", "COMMON AREA (COMMERCIAL, NOT SHOPPING CENTER)", "COMMON AREA (INDUSTRIAL)", "COMMON AREA (MISC)", "COMMON AREA (RESIDENTIAL)", "COMMUNICATIONS", "COMMUNITY CENTER (EXEMPT)", "COMMUNITY: SHOPPING CENTER, MINI-MALL", "CONDOMINIUM", "CONDOMINIUM BUILDING (RESIDENTIAL)", "CONDOMINIUM DEVELOPMENT (ASSOCIATION ASSESSMENT)", "CONDOMINIUM OFFICES", "CONDOMINIUMS (INDUSTRIAL)", "CONSTRUCTION/CONTRACTING SERVICES (INDUSTRIAL)", "CONVENIENCE STORE (7-11)", "CONVENIENCE STORE (W/FUEL PUMP)", "COOPERATIVE", "COOPERATIVE BUILDING (RESIDENTIAL)", "CORRECTIONAL FACILITY, JAILS, PRISONS, INSANE ASYLUM", "COUNTRY CLUB", "COUNTY OWNED (EXEMPT)", "CREMATORIUM, MORTUARY (EXEMPT)", "CULTURAL, HISTORICAL (MONUMENTS, HOMES, MUSEUMS, OTHER)", "DAIRY FARM", "DANCE HALL", "DAY CARE, PRE-SCHOOL (COMMERCIAL)", "DENTAL BUILDING", "DEPARTMENT STORE (APPAREL, HOUSEHOLD GOODS, FURNITURE)", "DEPARTMENT STORE (MULTI-STORY)", "DESERT OR BARREN LAND", "DISTILLERY, BREWERY, BOTTLING", "DISTRIBUTION WAREHOUSE (REGIONAL)", "DORMITORY, GROUP QUARTERS (RESIDENTIAL)", "DRIVE-IN THEATER", "DRIVE-THRU RESTAURANT, FAST FOOD", "DRIVING RANGE (GOLF)", "DRUG STORE, PHARMACY", "DRY CLEANER", "DUMP SITE", "DUPLEX (2 UNITS, ANY COMBINATION)", "EASEMENT (MISC)", "EMERGENCY (POLICE, FIRE, RESCUE, SHELTERS, ANIMAL SHELTER)", "EQUIPMENT / SUPPLIES", "EXEMPT (FULL OR PARTIAL)", "FACTORY (APPAREL, TEXTILE, LEATHER, MEDIUM MFG)", "FAIRGROUNDS", "FARM, CROPS", "FARM (IRRIGATED OR DRY)", "FARM SUPPLY & EQUIPMENT (COMMERCIAL)", "FEDERAL PROPERTY (EXEMPT)", "FEEDLOTS", "FINANCIAL BUILDING", "FISH CAMPS, GAME CLUB TARGET SHOOTING", "FOOD PACKING, PACKING PLANT (FRUIT, VEGETABLE, MEAT, DAIRY)", "FOOD PROCESSING", "FOREST (PARK, RESERVE, RECREATION, CONSERVATION)", "FOUNDRY, INDUSTRIAL PLANT (METAL, RUBBER, PLASTIC)", "FRATERNITY HOUSE, SORORITY HOUSE", "FREEWAYS, STATE HWYS", "GARDEN APT, COURT APT (5+ UNITS)", "GARDEN CENTER, HOME IMPROVEMENT (DO-IT-YOURSELF)", "GARDEN HOME", "GAS STATION", "GO-CARTS, MINIATURE GOLF, WATER SLIDES", "GOLF COURSE", "GOVERNMENTAL / PUBLIC USE (GENERAL)", "GOVERNMENT - VACANT LAND", "GOVT. ADMINISTRATIVE OFFICE (FEDERAL, STATE, LOCAL, COURT HOUSE)", "GRAIN ELEVATOR", "GROCERY, SUPERMARKET", "GYM, HEALTH SPA", "HARBOR & MARINE TRANSPORTATION", "HEAVY INDUSTRIAL (GENERAL)", "HEAVY MANUFACTURING", "HIGH-RISE APARTMENTS", "HISTORICAL DISTRICT", "HISTORICAL OFFICE", "HISTORICAL PARK, SITE, MISC.", "HISTORICAL - PRIVATE (GENERAL)", "HISTORICAL RECREATION, ENTERTAINMENT", "HISTORICAL RESIDENCE", "HISTORICAL RETAIL", "HISTORICAL TRANSIENT LODGING (HOTEL, MOTEL)", "HISTORICAL WAREHOUSE", "HOMES (RETIRED, HANDICAP, REST, CONVALESCENT, NURSING)", "HOMESTEAD (MISC.)", "HORTICULTURE, ORNAMENTAL (AGRICULTURAL)", "HOSPITAL - PRIVATE", "HOSPITAL - PUBLIC", "HOTEL", "HOTEL/MOTEL", "HOTEL-RESORT", "INDIAN LANDS", "INDUSTRIAL (GENERAL)", "INDUSTRIAL LOFT BUILDING, LOFT BUILDING", "INDUSTRIAL MISCELLANEOUS", "INDUSTRIAL PARK", "INDUSTRIAL - VACANT LAND", "INSTITUTIONAL (GENERAL)", "INSTITUTIONAL - VACANT LAND", "INVENTORY", "IRRIGATION, FLOOD CONTROL", "KENNEL", "LABOR CAMPS (INDUSTRIAL)", "LANDOMINIUM", "LAUNDROMAT (SELF-SERVICE)", "LEASEHOLD RIGHTS (MISC)", "LIGHT INDUSTRIAL (10% IMPROVED OFFICE SPACE; MACHINE SHOP)", "LIQUOR STORE", "LIVESTOCK, ANIMALS", "LIVESTOCK (ANIMALS, FISH, BIRDS, ETC.)", "LUMBER & WOOD PRODUCT MFG (INCLUDING FURNITURE)", "LUMBERYARD, BUILDING MATERIALS", "MANUFACTURED, MODULAR, PRE-FABRICATED HOMES", "MANUFACTURING (LIGHT)", "MARINA, BOAT SLIPS, YACHT CLUB, BOAT LANDING", "MARINE FACILITY/BOARD REPAIRS (SMALL CRAFT, SAILBOAT)", "MEDICAL BUILDING", "MEDICAL CLINIC", "MICROWAVE", "MILITARY (OFFICE, BASE, POST, PORT, RESERVE, WEAPON RANGE, TEST SITES)", "MILL (FEED, GRAIN, PAPER, LUMBER, TEXTILE, PULP", "MINING, MINERAL, QUARRIES", "MINI-WAREHOUSE, STORAGE", "MISCELLANEOUS (GENERAL)", "MISCELLANEOUS (RESIDENTIAL)", "MISCELLANEOUS STRUCTURES - RANCH, FARM FIXTURES", "MISC STRUCTURES NOT OTHERWISE CLASSED (BILLBOARDS, ETC.)", "MIXED USE (COMMERCIAL/INDUSTRIAL)", "MOBILE COMMERCIAL UNITS", "MOBILE HOME", "MOBILE HOME PARK, TRAILER PARK", "MOTEL", "MULTI-FAMILY DWELLINGS (GENERIC, ANY COMBINATION)", "MULTI-FAMILY - VACANT LAND", "MULTI-TENANT INDUSTRIAL BUILDING", "MUSEUM, LIBRARY, ART GALLERY (RECREATIONAL)", "NATURAL RESOURCES", "NEIGHBORHOOD: SHOPPING CENTER, STRIP CENTER, ENTERPRISE ZONE", "NIGHTCLUB (COCKTAIL LOUNGE)", "NURSERY, GREENHOUSE, FLORIST (RETAIL, WHOLESALE)", "OFFICE BUILDING", "OFFICE BUILDING (MULTI-STORY)", "ORCHARD (FRUIT, NUT)", "ORCHARDS, GROVES", "OTHER EXEMPT PROPERTY", "OUTDOOR RECREATION: BEACH, MOUNTAIN, DESERT", "PAPER PRODUCT MFG & RELATED PRODUCTS", "PARCELS WITH IMPROVEMENTS, USE NOT SPECIFIED", "PARKING GARAGE, PARKING STRUCTURE", "PARKING LOT", "PARK, PLAYGROUND, PICNIC AREA", "PAROCHIAL SCHOOL, PRIVATE SCHOOL", "PASTURE", "PATIO HOME", "PETROLEUM & GAS WELLS (MISC)", "PIERS, WHARF (RECREATION)", "PIPELINE OR RIGHT-OF-WAY", "PLANNED UNIT DEVELOPMENT (PUD)", "POLLUTION CONTROL", "POOL HALL, BILLIARD PARLOR", "POSSESSORY INTEREST (MISC)", "POST OFFICE", "POULTRY FARM (CHICKEN, TURKEY, FISH, BEES, RABBITS)", "PRINTER - RETAIL (PIP, QWIKCOPY, ETC)", "PRINTING * PUBLISHING (LIGHT INDUSTRIAL)", "PRIVATE PRESERVE, OPEN SPACE - VACANT LAND", "PRIVATE UTILITY (ELECTRIC, WATER, GAS, ETC.)", "PROCESSING PLANT (MINERALS, CEMENT, ROCK, GRAVEL, GLASS, CLAY)", "PROFESSIONAL BUILDING (LEGAL, INSURANCE, REAL ESTATE, BUSINESS)", "PROFESSIONAL BUILDING (MULTI-STORY)", "PUBLIC HEALTH CARE FACILITY (EXEMPT)", "PUBLIC SCHOOL (ADMINISTRATION, CAMPUS, DORMS, INSTRUCTION)", "PUBLIC SWIMMING POOL", "PUBLIC UTILITY (ELECTRIC, WATER, GAS, ETC.)", "QUADPLEX (4 UNITS, ANY COMBINATION)", "QUARRIES (SAND, GRAVEL, ROCK)", "RACE TRACK (AUTO, DOG, HORSE)", "RACQUET COURT, TENNIS COURT", "RADIO OR TV STATION", "RAIL (RIGHT-OF-WAY & TRACK)", "RAILROAD & RELATED", "RANCH", "RANGE LAND (GRAZING)", "R&D FACILITY, LABORATORY, RESEARCH FACILITY, COSMETICS, PHARMACEUTICAL", "RECREATIONAL CENTER", "RECREATIONAL/ENTERTAINMENT (GENERAL)", "RECREATIONAL NON-TAXABLE (CAMPS, BOY SCOUTS)", "RECREATIONAL - VACANT LAND", "RECYCLING (METAL, PAPER, GLASS)", "REDEVELOPMENT AGENCY OR ZONE", "REFINERY, PETROLEUM PRODUCTS", "REGIONAL: SHOPPING CENTER, MALL (W/ANCHOR)", "REGULATING DISTRICTS & ASSESSMENTS; TAX ABATEMENT", "RELIGIOUS, CHURCH, WORSHIP (SYNAGOGUE, TEMPLE, PARSONAGE)", "RESERVOIR, WATER SUPPLY", "RESIDENTIAL (GENERAL/SINGLE)", "RESIDENTIAL INCOME (GENERAL/MULTI-FAMILY)", "RESIDENTIAL PARKING GARAGE", "RESIDENTIAL STORAGE SPACE", "RESIDENTIAL - VACANT LAND", "RESTAURANT", "RETAIL STORES (PERSONAL SERVICES, PHOTOGRAPHY, TRAVEL)", "RIDING STABLE, TRAILS", "RIGHT-OF-WAY (NOT RAIL, ROAD OR UTILITY)", "ROAD (RIGHT-OF-WAY)", "ROADSIDE MARKET", "ROADS, STREETS, BRIDGES", "ROW HOUSE", "ROYALTY INTEREST", "RURAL IMPROVED / NON-RESIDENTIAL", "RURAL RESIDENCE", "SBE - SPECIAL ASSESSMENTS", "SEASONAL, CABIN, VACATION RESIDENCE", "SERVICE SHOP (TV, RADIO, ELECTRIC, PLUMBING)", "SERVICE STATION (FULL SERVICE)", "SERVICE STATION W/CONVENIENCE STORE (FOOD MART)", "SHIPYARD - BUILT OR REPAIRED (SEAGOING VESSELS)", "SHOPPING CENTER COMMON AREA (PARKING ETC)", "SINGLE FAMILY RESIDENCE", "SINGLE FAMILY RESIDENTIAL", "SKATING RINK, ICE SKATING, ROLLER SKATING", "SKYSCRAPER/HIGH-RISE (COMMERCIAL OFFICES)", "SLAUGHTER HOUSE, STOCKYARD", "SPECIAL PURPOSE", "SPORTS COMPLEX", "STADIUM", "STATE OWNED (EXEMPT)", "STORAGE YARD (JUNK, AUTO WRECKING, SALVAGE)", "STORAGE YARD, OPEN STORAGE (LIGHT EQUIPMENT, MATERIAL)", "STORE/OFFICE (MIXED USE)", "STORE, RETAIL OUTLET", "STORES & APARTMENTS", "STRUCTURES ON LEASED LAND", "SUB-SURFACE RIGHTS (MINERAL)", "SUGAR REFINERY", "SURFACE RIGHTS (GRAZING, TIMBER, COAL, ETC.)", "TAKE-OUT RESTAURANT (FOOD PREPARATION)", "TELEGRAPH, TELEPHONE", "THEATER (MOVIE)", "TIMBERLAND, FOREST, TREES", "TIMESHARE", "TINY HOUSE", "TOWNHOUSE", "TRANSPORTATION (AIR, RAIL, BUS)", "TRANSPORTATION (GENERAL)", "TRIPLEX (3 UNITS, ANY COMBINATION)", "TRUCK CROPS", "TRUCK STOP (FUEL AND DINER)", "TRUCK TERMINAL (MOTOR FREIGHT)", "UNDER CONSTRUCTION", "UNUSABLE LAND (REMNANT, STEEP, ETC.)", "UTILITIES (RIGHT-OF-WAY ONLY)", "VACANT COMMERCIAL", "VACANT LAND", "VACANT LAND - DESTROYED/UNINHABITABLE IMPROVEMENT", "VACANT LAND - EXEMPT", "VACANT LAND - UNSPECIFIED IMPROVEMENT", "VEHICLE SALES, VEHICLE RENTALS (AUTO/TRUCK/RV/BOAT/ETC)", "VETERINARY, ANIMAL HOSPITAL", "VINEYARD", "WAREHOUSE, STORAGE", "WASTE DISPOSAL, SEWAGE (PROCESSING, DISPOSAL, STORAGE, TREATMENT)", "WASTE LAND, MARSH, SWAMP, SUBMERGED - VACANT LAND", "WATER AREA (LAKES, RIVER, SHORE) - VACANT LAND", "WATER RIGHTS (MISC)", "WELFARE, SOCIAL SERVICE, LOW INCOME HOUSING (EXEMPT)", "WELL SITE (AGRICULTURAL)", "WHOLESALE OUTLET, DISCOUNT STORE (FRANCHISE)", "WILDLIFE (REFUGE)", "WINERY", "WORKING INTEREST", "ZERO LOT LINE (RESIDENTIAL)", "ZOO"];

const getLandUseTypesByCategory = (category: string[]) => {
  if (
    !category ||
    category.length === 0 ||
    (category.length === 1 && category[0] === 'Undefined')
  )
    return LAND_USE_TYPES;

  // prettier-ignore
  const types = LAND_USE_TYPES.filter((option) => {
    if (["AGRICULTURAL/RURAL (GENERAL)", "AGRICULTURAL (UNIMPROVED) - VACANT LAND", "BARNDOMINIUM", "CANNABIS GROW FACILITY", "DAIRY FARM", "FARM, CROPS", "FARM (IRRIGATED OR DRY)", "FEEDLOTS", "HORTICULTURE, ORNAMENTAL (AGRICULTURAL)", "IRRIGATION, FLOOD CONTROL", "LIVESTOCK, ANIMALS", "MISCELLANEOUS STRUCTURES - RANCH, FARM FIXTURES", "ORCHARD (FRUIT, NUT)", "ORCHARDS, GROVES", "PASTURE", "POULTRY FARM (CHICKEN, TURKEY, FISH, BEES, RABBITS)", "RANCH", "RANGE LAND (GRAZING)", "TRUCK CROPS", "VINEYARD", "WELL SITE (AGRICULTURAL)"].includes(option)) {
      return category.includes('AGRICULTURE / FARMING');
    } else if (["471", "APPLIANCE STORE (CIRCUIT CITY, GOODS BUYS, BEST BUY)", "ARCADES (AMUSEMENT)", "AUTO REPAIR, GARAGE", "BAKERY", "BARBER/HAIR SALON", "BAR, TAVERN", "BED & BREAKFAST", "BOWLING ALLEY", "CANNABIS DISPENSARY", "CAR WASH", "CAR WASH - AUTOMATED", "CAR WASH - SELF-SERVE", "CASINO", "CEMETERY, FUNERAL HOME, MORTUARY (COMMERCIAL)", "COMMERCIAL BUILDING, MAIL ORDER, SHOW ROOM (NON-AUTO), WAREHOUSE", "COMMERCIAL CONDOMINIUM (NOT OFFICES)", "COMMERCIAL (GENERAL)", "COMMERCIAL MISCELLANEOUS", "COMMERCIAL OFFICE (GENERAL)", "COMMERCIAL OFFICE/RESIDENTIAL (MIXED USE)", "COMMON AREA (COMMERCIAL, NOT SHOPPING CENTER)", "COMMUNITY: SHOPPING CENTER, MINI-MALL", "CONDOMINIUM OFFICES", "CONVENIENCE STORE (7-11)", "CONVENIENCE STORE (W/FUEL PUMP)", "DAY CARE, PRE-SCHOOL (COMMERCIAL)", "DENTAL BUILDING", "DEPARTMENT STORE (APPAREL, HOUSEHOLD GOODS, FURNITURE)", "DEPARTMENT STORE (MULTI-STORY)", "DRIVE-THRU RESTAURANT, FAST FOOD", "DRUG STORE, PHARMACY", "DRY CLEANER", "FARM SUPPLY & EQUIPMENT (COMMERCIAL)", "FINANCIAL BUILDING", "GARDEN CENTER, HOME IMPROVEMENT (DO-IT-YOURSELF)", "GAS STATION", "GO-CARTS, MINIATURE GOLF, WATER SLIDES", "GROCERY, SUPERMARKET", "GYM, HEALTH SPA", "HOMES (RETIRED, HANDICAP, REST, CONVALESCENT, NURSING)", "HOSPITAL - PRIVATE", "HOTEL", "HOTEL/MOTEL", "HOTEL-RESORT", "KENNEL", "LAUNDROMAT (SELF-SERVICE)", "LIQUOR STORE", "MEDICAL BUILDING", "MIXED USE (COMMERCIAL/INDUSTRIAL)", "MOBILE COMMERCIAL UNITS", "MOBILE HOME PARK, TRAILER PARK", "MOTEL", "NEIGHBORHOOD: SHOPPING CENTER, STRIP CENTER, ENTERPRISE ZONE", "NIGHTCLUB (COCKTAIL LOUNGE)", "NURSERY, GREENHOUSE, FLORIST (RETAIL, WHOLESALE)", "OFFICE BUILDING", "OFFICE BUILDING (MULTI-STORY)", "PARKING GARAGE, PARKING STRUCTURE", "PARKING LOT", "POOL HALL, BILLIARD PARLOR", "PRINTER - RETAIL (PIP, QWIKCOPY, ETC)", "PROFESSIONAL BUILDING (LEGAL, INSURANCE, REAL ESTATE, BUSINESS)", "PROFESSIONAL BUILDING (MULTI-STORY)", "REGIONAL: SHOPPING CENTER, MALL (W/ANCHOR)", "RESTAURANT", "RETAIL STORES (PERSONAL SERVICES, PHOTOGRAPHY, TRAVEL)", "ROADSIDE MARKET", "SERVICE SHOP (TV, RADIO, ELECTRIC, PLUMBING)", "SERVICE STATION (FULL SERVICE)", "SERVICE STATION W/CONVENIENCE STORE (FOOD MART)", "SHOPPING CENTER COMMON AREA (PARKING ETC)", "SKATING RINK, ICE SKATING, ROLLER SKATING", "SKYSCRAPER/HIGH-RISE (COMMERCIAL OFFICES)", "SPORTS COMPLEX", "STORE/OFFICE (MIXED USE)", "STORE, RETAIL OUTLET", "STORES & APARTMENTS", "TAKE-OUT RESTAURANT (FOOD PREPARATION)", "THEATER (MOVIE)", "TRUCK STOP (FUEL AND DINER)", "VACANT COMMERCIAL", "VEHICLE SALES, VEHICLE RENTALS (AUTO/TRUCK/RV/BOAT/ETC)", "VETERINARY, ANIMAL HOSPITAL", "WHOLESALE OUTLET, DISCOUNT STORE (FRANCHISE)", "AMUSEMENT PARK, TOURIST ATTRACTION", "APARTMENT HOUSE (100+ UNITS)", "APARTMENT HOUSE (5+ UNITS)", "ARENA, CONVENTION CENTER", "AUDITORIUM", "BOARDING/ROOMING HOUSE, APT HOTEL", "BUS TERMINAL", "CABLE TV STATION", "CAMPGROUND, RV PARK", "CENTRALLY ASSESSED", "CHARITABLE ORGANIZATION, FRATERNAL", "CLUBS, LODGES, PROFESSIONAL ASSOCIATIONS", "COLLEGE, UNIVERSITY, VOCATIONAL SCHOOL - PRIVATE", "COMMERCIAL AUTO TRANSPORTATION/STORAGE", "COUNTRY CLUB", "CREMATORIUM, MORTUARY (EXEMPT)", "DANCE HALL", "DRIVE-IN THEATER", "DRIVING RANGE (GOLF)", "FAIRGROUNDS", "FISH CAMPS, GAME CLUB TARGET SHOOTING", "GARDEN APT, COURT APT (5+ UNITS)", "GOLF COURSE", "HIGH-RISE APARTMENTS", "HISTORICAL OFFICE", "HISTORICAL RECREATION, ENTERTAINMENT", "HISTORICAL RETAIL", "HISTORICAL TRANSIENT LODGING (HOTEL, MOTEL)", "MEDICAL CLINIC", "RACE TRACK (AUTO, DOG, HORSE)", "RADIO OR TV STATION", "RECREATIONAL/ENTERTAINMENT (GENERAL)", "RESIDENTIAL PARKING GARAGE", "SBE - SPECIAL ASSESSMENTS", "STADIUM", "ZOO"].includes(option)) {
      return category.includes('COMMERCIAL');
    } else if (["ASSEMBLY (LIGHT INDUSTRIAL)", "BULK STORAGE, TANKS (GASOLINE, FUEL, ETC)", "CANNERY", "CHEMICAL", "COLD STORAGE", "COMMON AREA (INDUSTRIAL)", "CONDOMINIUMS (INDUSTRIAL)", "CONSTRUCTION/CONTRACTING SERVICES (INDUSTRIAL)", "DISTILLERY, BREWERY, BOTTLING", "DISTRIBUTION WAREHOUSE (REGIONAL)", "DUMP SITE", "FACTORY (APPAREL, TEXTILE, LEATHER, MEDIUM MFG)", "FOOD PACKING, PACKING PLANT (FRUIT, VEGETABLE, MEAT, DAIRY)", "FOOD PROCESSING", "FOUNDRY, INDUSTRIAL PLANT (METAL, RUBBER, PLASTIC)", "GRAIN ELEVATOR", "HEAVY INDUSTRIAL (GENERAL)", "HEAVY MANUFACTURING", "HISTORICAL WAREHOUSE", "INDUSTRIAL (GENERAL)", "INDUSTRIAL LOFT BUILDING, LOFT BUILDING", "INDUSTRIAL MISCELLANEOUS", "INDUSTRIAL PARK", "INDUSTRIAL - VACANT LAND", "LABOR CAMPS (INDUSTRIAL)", "LIGHT INDUSTRIAL (10% IMPROVED OFFICE SPACE; MACHINE SHOP)", "LUMBER & WOOD PRODUCT MFG (INCLUDING FURNITURE)", "LUMBERYARD, BUILDING MATERIALS", "MANUFACTURING (LIGHT)", "MARINE FACILITY/BOARD REPAIRS (SMALL CRAFT, SAILBOAT)", "MILL (FEED, GRAIN, PAPER, LUMBER, TEXTILE, PULP", "MINING, MINERAL, QUARRIES", "MINI-WAREHOUSE, STORAGE", "MULTI-TENANT INDUSTRIAL BUILDING", "PAPER PRODUCT MFG & RELATED PRODUCTS", "PRINTING * PUBLISHING (LIGHT INDUSTRIAL)", "PROCESSING PLANT (MINERALS, CEMENT, ROCK, GRAVEL, GLASS, CLAY)", "QUARRIES (SAND, GRAVEL, ROCK)", "R&D FACILITY, LABORATORY, RESEARCH FACILITY, COSMETICS, PHARMACEUTICAL", "RECYCLING (METAL, PAPER, GLASS)", "REFINERY, PETROLEUM PRODUCTS", "SHIPYARD - BUILT OR REPAIRED (SEAGOING VESSELS)", "SLAUGHTER HOUSE, STOCKYARD", "STORAGE YARD (JUNK, AUTO WRECKING, SALVAGE)", "STORAGE YARD, OPEN STORAGE (LIGHT EQUIPMENT, MATERIAL)", "SUGAR REFINERY", "TRUCK TERMINAL (MOTOR FREIGHT)", "WAREHOUSE, STORAGE", "WASTE DISPOSAL, SEWAGE (PROCESSING, DISPOSAL, STORAGE, TREATMENT)", "WINERY"].includes(option)) {
      return category.includes('INDUSTRIAL');
    } else if (["PARCELS WITH IMPROVEMENTS", "USE NOT SPECIFIED"].includes(option)) {
      return category.includes('OTHER');
    } else if (["CHILDREN'S HOME, ORPHANAGE", "COMMON AREA (RESIDENTIAL)", "EQUIPMENT / SUPPLIES", "EXEMPT (FULL OR PARTIAL)", "HISTORICAL - PRIVATE (GENERAL)", "INVENTORY", "LIVESTOCK (ANIMALS, FISH, BIRDS, ETC.)", "MISCELLANEOUS (GENERAL)", "MISC STRUCTURES NOT OTHERWISE CLASSED (BILLBOARDS, ETC.)", "OTHER EXEMPT PROPERTY", "PAROCHIAL SCHOOL, PRIVATE SCHOOL", "REGULATING DISTRICTS & ASSESSMENTS; TAX ABATEMENT", "RELIGIOUS, CHURCH, WORSHIP (SYNAGOGUE, TEMPLE, PARSONAGE)", "RESIDENTIAL STORAGE SPACE", "RURAL IMPROVED / NON-RESIDENTIAL", "SPECIAL PURPOSE", "STRUCTURES ON LEASED LAND", "UNDER CONSTRUCTION", "UNUSABLE LAND (REMNANT, STEEP, ETC.)", "WORKING INTEREST"].includes(option)) {
      return category.includes('OTHER / UNKNOWN');
    } else if (["AIRPORT & RELATED", "CELLULAR", "CEMETERY (EXEMPT)", "CITY, MUNICIPAL, TOWN, VILLAGE OWNED (EXEMPT)", "COLLEGES, UNIVERSITY - PUBLIC", "COMMON AREA (MISC)", "COMMUNICATIONS", "COMMUNITY CENTER (EXEMPT)", "CORRECTIONAL FACILITY, JAILS, PRISONS, INSANE ASYLUM", "COUNTY OWNED (EXEMPT)", "CULTURAL, HISTORICAL (MONUMENTS, HOMES, MUSEUMS, OTHER)", "EASEMENT (MISC)", "EMERGENCY (POLICE, FIRE, RESCUE, SHELTERS, ANIMAL SHELTER)", "FEDERAL PROPERTY (EXEMPT)", "FOREST (PARK, RESERVE, RECREATION, CONSERVATION)", "FREEWAYS, STATE HWYS", "GOVERNMENTAL / PUBLIC USE (GENERAL)", "GOVERNMENT - VACANT LAND", "GOVT. ADMINISTRATIVE OFFICE (FEDERAL, STATE, LOCAL, COURT HOUSE)", "HARBOR & MARINE TRANSPORTATION", "HISTORICAL DISTRICT", "HISTORICAL PARK, SITE, MISC.", "HOSPITAL - PUBLIC", "INDIAN LANDS", "INSTITUTIONAL (GENERAL)", "INSTITUTIONAL - VACANT LAND", "LEASEHOLD RIGHTS (MISC)", "MARINA, BOAT SLIPS, YACHT CLUB, BOAT LANDING", "MICROWAVE", "MILITARY (OFFICE, BASE, POST, PORT, RESERVE, WEAPON RANGE, TEST SITES)", "MUSEUM, LIBRARY, ART GALLERY (RECREATIONAL)", "NATURAL RESOURCES", "OUTDOOR RECREATION: BEACH, MOUNTAIN, DESERT", "PARK, PLAYGROUND, PICNIC AREA", "PETROLEUM & GAS WELLS (MISC)", "PIERS, WHARF (RECREATION)", "PIPELINE OR RIGHT-OF-WAY", "POLLUTION CONTROL", "POSSESSORY INTEREST (MISC)", "POST OFFICE", "PRIVATE UTILITY (ELECTRIC, WATER, GAS, ETC.)", "PUBLIC HEALTH CARE FACILITY (EXEMPT)", "PUBLIC SCHOOL (ADMINISTRATION, CAMPUS, DORMS, INSTRUCTION)", "PUBLIC SWIMMING POOL", "PUBLIC UTILITY (ELECTRIC, WATER, GAS, ETC.)", "RACQUET COURT, TENNIS COURT", "RAIL (RIGHT-OF-WAY & TRACK)", "RAILROAD & RELATED", "RECREATIONAL CENTER", "RECREATIONAL NON-TAXABLE (CAMPS, BOY SCOUTS)", "RECREATIONAL - VACANT LAND", "REDEVELOPMENT AGENCY OR ZONE", "RESERVOIR, WATER SUPPLY", "RIDING STABLE, TRAILS", "RIGHT-OF-WAY (NOT RAIL, ROAD OR UTILITY)", "ROAD (RIGHT-OF-WAY)", "ROADS, STREETS, BRIDGES", "ROYALTY INTEREST", "STATE OWNED (EXEMPT)", "SUB-SURFACE RIGHTS (MINERAL)", "SURFACE RIGHTS (GRAZING, TIMBER, COAL, ETC.)", "TELEGRAPH, TELEPHONE", "TRANSPORTATION (AIR, RAIL, BUS)", "TRANSPORTATION (GENERAL)", "UTILITIES (RIGHT-OF-WAY ONLY)", "WATER RIGHTS (MISC)", "WELFARE, SOCIAL SERVICE, LOW INCOME HOUSING (EXEMPT)", "WILDLIFE (REFUGE)", "WORKING INTEREST"].includes(option)) {
      return category.includes('PUBLIC WORKS');
    } else if (["467", "468", "470", "APARTMENTS (GENERIC)", "BUNGALOW (RESIDENTIAL)", "CLUSTER HOME", "CONDOMINIUM", "CONDOMINIUM BUILDING (RESIDENTIAL)", "CONDOMINIUM DEVELOPMENT (ASSOCIATION ASSESSMENT)", "COOPERATIVE", "COOPERATIVE BUILDING (RESIDENTIAL)", "DORMITORY, GROUP QUARTERS (RESIDENTIAL)", "DUPLEX (2 UNITS, ANY COMBINATION)", "FRATERNITY HOUSE, SORORITY HOUSE", "GARDEN HOME", "HOMESTEAD (MISC.)", "LANDOMINIUM", "MANUFACTURED, MODULAR, PRE-FABRICATED HOMES", "MISCELLANEOUS (RESIDENTIAL)", "MOBILE HOME", "MULTI-FAMILY DWELLINGS (GENERIC, ANY COMBINATION)", "MULTI-FAMILY - VACANT LAND", "PATIO HOME", "PLANNED UNIT DEVELOPMENT (PUD)", "QUADPLEX (4 UNITS, ANY COMBINATION)", "RESIDENTIAL (GENERAL/SINGLE)", "RESIDENTIAL INCOME (GENERAL/MULTI-FAMILY)", "RESIDENTIAL - VACANT LAND", "ROW HOUSE", "RURAL RESIDENCE", "SEASONAL, CABIN, VACATION RESIDENCE", "SINGLE FAMILY RESIDENCE", "SINGLE FAMILY RESIDENTIAL", "TIMESHARE", "TINY HOUSE", "TOWNHOUSE", "TRIPLEX (3 UNITS, ANY COMBINATION)", "ZERO LOT LINE (RESIDENTIAL)", "HISTORICAL RESIDENCE"].includes(option)) {
      return category.includes('RESIDENTIAL');
    } else if (["ABANDONED SITE, CONTAMINATED SITE", "DESERT OR BARREN LAND", "PRIVATE PRESERVE, OPEN SPACE - VACANT LAND", "TIMBERLAND, FOREST, TREES", "VACANT LAND", "VACANT LAND - DESTROYED/UNINHABITABLE IMPROVEMENT", "VACANT LAND - EXEMPT", "VACANT LAND - UNSPECIFIED IMPROVEMENT", "WASTE LAND, MARSH, SWAMP, SUBMERGED - VACANT LAND", "WATER AREA (LAKES, RIVER, SHORE) - VACANT LAND"].includes(option)) {
      return category.includes('VACANT LAND');
    }
  });

  return types;
};

const LandUseCategoryFilter = ({
  filters,
  onChangeFilter,
  sectionActive,
}: any) => {
  if (!sectionActive) return null;
  return (
    <Select
      mode="multiple"
      style={{ width: '100%' }}
      maxTagCount="responsive"
      placeholder="Select Land Use Category"
      allowClear
      value={filters.landUseCategory.value}
      onChange={(value) => onChangeFilter('landUseCategory.value', value)}
      options={LAND_USE_CATEGORIES.map((c) => ({ label: c, value: c }))}
    />
  );
};

const LandUseTypeFilter = ({ filters, onChangeFilter, sectionActive }: any) => {
  const category = filters.landUseCategory.value || [];

  if (!sectionActive) return null;
  return (
    <Select
      mode="multiple"
      style={{ width: '100%' }}
      maxTagCount="responsive"
      placeholder="Select Land Use Type"
      allowClear
      value={filters.landUseType.value}
      onChange={(value) => onChangeFilter('landUseType.value', value)}
      options={getLandUseTypesByCategory(category).map((c) => ({
        label: c,
        value: c,
      }))}
    />
  );
};

const ZoningFilterForm = () => {
  const { zoningActive } = useSiteFilter();
  const { filterPayload, setFilterPayload } = useLandParcelSearch();
  const [zoningType, setZoningType] = useState<ZoningType[]>([]);
  const [zoningSubtype, setZoningSubtype] = useState<ZoningSubtype[]>([]);

  useEffect(() => {
    setFilterPayload((prevState) => ({
      ...prevState,
      zoning: {
        type: zoningActive ? zoningType : [],
        subtype: zoningActive ? zoningSubtype : [],
      },
    }));
  }, [zoningActive, zoningType, zoningSubtype]);

  const getSubtypeOptions = useCallback(() => {
    // prettier-ignore
    const subtypeOptions = [
      { label: 'Single Family', value: 'Single Family' },
      { label: 'Two Family', value: 'Two Family' },
      { label: 'Multi Family', value: 'Multi Family' },
      { label: 'Mobile Home Park', value: 'Mobile Home Park' },
      { label: 'General Commercial', value: 'General Commercial' },
      { label: 'Core Commercial', value: 'Core Commercial' },
      { label: 'Retail Commercial', value: 'Retail Commercial' },
      { label: 'Neighborhood Commercial', value: 'Neighborhood Commercial' },
      { label: 'Office', value: 'Office' },
      { label: 'Special Commercial', value: 'Special Commercial' },
      { label: 'Mixed Use', value: 'Mixed Use' },
      { label: 'Industrial', value: 'Industrial' },
      { label: 'Light Industrial', value: 'Light Industrial' },
      { label: 'Special', value: 'Special' },
      { label: 'Planned', value: 'Planned' },
      { label: 'Overlay', value: 'Overlay' },
    ];

    if (
      zoningType.length === 0 ||
      (zoningType.length === 1 && zoningType[0] === 'Undefined')
    )
      return subtypeOptions;

    // prettier-ignore
    const filteredOptions = subtypeOptions.filter((option) => {
      if (['Core Commercial', 'General Commercial', 'Neighborhood Commercial', 'Office', 'Retail Commercial', 'Special Commercial'].includes(option.value)) {
        return zoningType.includes('Commercial');
      } else if (['Industrial', 'Light Industrial'].includes(option.value)) {
        return zoningType.includes('Industrial');
      } else if (['Mixed Use'].includes(option.value)) {
        return zoningType.includes('Mixed');
      } else if (['Overlay'].includes(option.value)) {
        return zoningType.includes('Overlay');
      } else if (['Planned'].includes(option.value)) {
        return zoningType.includes('Planned');
      } else if (['Mobile Home Park', 'Multi Family', 'Single Family', 'Two Family'].includes(option.value)) {
        return zoningType.includes('Residential');
      } else if (['Special'].includes(option.value)) {
        return zoningType.includes('Special');
      }
    });

    return filteredOptions;
  }, [zoningType]);

  return (
    <div className="flex flex-col gap-6 py-6 px-8">
      <div className="flex flex-col gap-3">
        <span className="font-semibold">Zoning Type</span>
        <Select
          mode="multiple"
          style={{ width: '100%' }}
          disabled={!zoningActive}
          maxTagCount="responsive"
          placeholder="Select Type"
          allowClear
          value={filterPayload.zoning.type}
          onChange={(value) => setZoningType(value)}
          options={[
            { label: 'Undefined', value: 'Undefined' },
            { label: 'Residential', value: 'Residential' },
            { label: 'Commercial', value: 'Commercial' },
            { label: 'Special', value: 'Special' },
            { label: 'Mixed', value: 'Mixed' },
            { label: 'Planned', value: 'Planned' },
            { label: 'Agriculture', value: 'Agriculture' },
            { label: 'Industrial', value: 'Industrial' },
            { label: 'Overlay', value: 'Overlay' },
            { label: 'Others', value: 'Others' },
          ]}
        />
      </div>
      <div className="flex flex-col gap-3">
        <span className="font-semibold">Zoning Subtype</span>
        <Select
          mode="multiple"
          style={{ width: '100%' }}
          disabled={!zoningActive}
          maxTagCount="responsive"
          placeholder="Select subtype"
          allowClear
          value={filterPayload.zoning.subtype}
          onChange={(value) => setZoningSubtype(value)}
          options={[
            { label: 'Undefined', value: 'Undefined' },
            ...getSubtypeOptions(),
          ]}
        />
      </div>
    </div>
  );
};

const BoundaryDimensionInput = ({
  sectionActive,
  filters,
  onChangeFilter,
  formatter,
  parser,
}: {
  sectionActive: boolean;
  filters: any;
  onChangeFilter: (type: any, value: any) => void;
  formatter?: any;
  parser?: any;
}) => {
  const [active, setActive] = useState(true);
  const [lengthActive, setLengthActive] = useState(
    filters.boundaryLength.isChecked,
  );
  const [widthActive, setWidthActive] = useState(
    filters.boundaryWidth.isChecked,
  );
  const [ratioActive, setRatioActive] = useState(
    filters.boundaryLxWRatio.isChecked,
  );

  useEffect(() => {
    if (!active) {
      onChangeFilter('boundaryLength.isChecked', false);
      onChangeFilter('boundaryWidth.isChecked', false);
      onChangeFilter('boundaryLxWRatio.isChecked', false);
    } else {
      onChangeFilter('boundaryLength.isChecked', lengthActive);
      onChangeFilter('boundaryWidth.isChecked', widthActive);
      onChangeFilter('boundaryLxWRatio.isChecked', ratioActive);
    }
  }, [active]);

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '12px',
      }}
    >
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          gap: '12px',
          alignItems: 'center',
        }}
      >
        <Switch checked={active} onChange={(checked) => setActive(checked)} />
        <span
          style={{
            fontWeight: active && sectionActive ? 600 : 'normal',
          }}
        >
          Boundary Dimensions
        </span>
      </div>
      {active && sectionActive && (
        <div
          style={{
            marginLeft: 25,
            display: 'flex',
            flexDirection: 'column',
            gap: '12px',
          }}
        >
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              gap: '8px',
            }}
          >
            <Checkbox
              checked={filters.boundaryLength.isChecked}
              onChange={(e) => {
                onChangeFilter('boundaryLength.isChecked', e.target.checked);
                setLengthActive(e.target.checked);
              }}
            >
              <span
                style={{
                  display: 'inline-block',
                  minWidth: '85px',
                  whiteSpace: 'nowrap',
                }}
              >
                {'Length (ft)'}
              </span>
            </Checkbox>
            <Select
              disabled={!filters.boundaryLength.isChecked}
              value={filters.boundaryLength.type}
              onChange={(value) => onChangeFilter('boundaryLength.type', value)}
              style={{ minWidth: '70px' }}
              options={[
                { label: '==', value: '==' },
                { label: '>=', value: '>=' },
                { label: '<=', value: '<=' },
                { label: '<->', value: '<->' },
              ]}
            />

            {(filters.boundaryLength.type === '<->' ||
              filters.boundaryLength.type === '>=' ||
              filters.boundaryLength.type === '==') && (
              <InputNumber
                disabled={!filters.boundaryLength.isChecked}
                className="landDev-input-number"
                value={filters.boundaryLength.min}
                onChange={(value) =>
                  onChangeFilter('boundaryLength.min', value)
                }
                formatter={formatter ? formatter : null}
                parser={parser ? parser : null}
                min={0}
                step={100}
                style={{ width: '100%' }}
              />
            )}
            {(filters.boundaryLength.type === '<->' ||
              filters.boundaryLength.type === '<=') && (
              <InputNumber
                disabled={!filters.boundaryLength.isChecked}
                className="landDev-input-number"
                value={filters.boundaryLength.max}
                onChange={(value) =>
                  onChangeFilter('boundaryLength.max', value)
                }
                formatter={formatter ? formatter : null}
                parser={parser ? parser : null}
                min={0}
                step={100}
                style={{ width: '100%' }}
              />
            )}
          </div>

          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              gap: '8px',
            }}
          >
            <Checkbox
              checked={filters.boundaryWidth.isChecked}
              onChange={(e) => {
                onChangeFilter('boundaryWidth.isChecked', e.target.checked);
                setWidthActive(e.target.checked);
              }}
            >
              <span
                style={{
                  display: 'inline-block',
                  minWidth: '85px',
                  whiteSpace: 'nowrap',
                }}
              >
                {'Width (ft)'}
              </span>
            </Checkbox>
            <Select
              disabled={!filters.boundaryWidth.isChecked}
              value={filters.boundaryWidth.type}
              onChange={(value) => onChangeFilter('boundaryWidth.type', value)}
              style={{ minWidth: '70px' }}
              options={[
                { label: '==', value: '==' },
                { label: '>=', value: '>=' },
                { label: '<=', value: '<=' },
                { label: '<->', value: '<->' },
              ]}
            />

            {(filters.boundaryWidth.type === '<->' ||
              filters.boundaryWidth.type === '>=' ||
              filters.boundaryWidth.type === '==') && (
              <InputNumber
                disabled={!filters.boundaryWidth.isChecked}
                className="landDev-input-number"
                value={filters.boundaryWidth.min}
                onChange={(value) => onChangeFilter('boundaryWidth.min', value)}
                formatter={formatter ? formatter : null}
                parser={parser ? parser : null}
                min={0}
                step={100}
                style={{ width: '100%' }}
              />
            )}
            {(filters.boundaryWidth.type === '<->' ||
              filters.boundaryWidth.type === '<=') && (
              <InputNumber
                disabled={!filters.boundaryWidth.isChecked}
                className="landDev-input-number"
                value={filters.boundaryWidth.max}
                onChange={(value) => onChangeFilter('boundaryWidth.max', value)}
                formatter={formatter ? formatter : null}
                parser={parser ? parser : null}
                min={0}
                step={100}
                style={{ width: '100%' }}
              />
            )}
          </div>

          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              gap: '8px',
            }}
          >
            <Checkbox
              checked={filters.boundaryLxWRatio.isChecked}
              onChange={(e) => {
                onChangeFilter('boundaryLxWRatio.isChecked', e.target.checked);
                setRatioActive(e.target.checked);
              }}
            >
              <span
                style={{
                  display: 'inline-flex',
                  flexDirection: 'row',
                  minWidth: '85px',
                  whiteSpace: 'nowrap',
                  position: 'relative',
                }}
              >
                {'LxW Ratio'}
                <Tooltip title="Length-to-Width Ratio: 1 = square; higher values = more elongated or rectangular boundaries.">
                  <IoInformationCircle
                    size={16}
                    style={{
                      position: 'absolute',
                      right: '-2px',
                      top: '-2px',
                    }}
                  />
                </Tooltip>
              </span>
            </Checkbox>
            <Select
              disabled={!filters.boundaryLxWRatio.isChecked}
              value={filters.boundaryLxWRatio.type}
              onChange={(value) =>
                onChangeFilter('boundaryLxWRatio.type', value)
              }
              style={{ minWidth: '70px' }}
              options={[
                { label: '==', value: '==' },
                { label: '>=', value: '>=' },
                { label: '<=', value: '<=' },
                { label: '<->', value: '<->' },
              ]}
            />

            {(filters.boundaryLxWRatio.type === '<->' ||
              filters.boundaryLxWRatio.type === '>=' ||
              filters.boundaryLxWRatio.type === '==') && (
              <InputNumber
                disabled={!filters.boundaryLxWRatio.isChecked}
                className="landDev-input-number"
                value={filters.boundaryLxWRatio.min}
                onChange={(value) =>
                  onChangeFilter('boundaryLxWRatio.min', value)
                }
                formatter={formatter ? formatter : null}
                parser={parser ? parser : null}
                min={1}
                step={0.1}
                style={{ width: '100%' }}
              />
            )}
            {(filters.boundaryLxWRatio.type === '<->' ||
              filters.boundaryLxWRatio.type === '<=') && (
              <InputNumber
                disabled={!filters.boundaryLxWRatio.isChecked}
                className="landDev-input-number"
                value={filters.boundaryLxWRatio.max}
                onChange={(value) =>
                  onChangeFilter('boundaryLxWRatio.max', value)
                }
                formatter={formatter ? formatter : null}
                parser={parser ? parser : null}
                min={1}
                step={0.1}
                style={{ width: '100%' }}
              />
            )}
          </div>

          {/* <Select
                      disabled={!filters.mlsListing.isChecked}
                      value={filters.mlsListing.value}
                      onChange={(value) =>
                        onChangeFilter('mlsListing' + '.value', value)
                      }
                      options={[
                        { label: 'All', value: 'All' },
                        { label: 'Active', value: 'Active' },
                        { label: 'Pending', value: 'Pending' },
                        { label: 'Closed', value: 'Closed' },
                        { label: 'Expired', value: 'Expired' },
                        { label: 'Canceled', value: 'Canceled' },
                      ]}
                      style={{ width: '150px' }}
                    />
                    <span> status.</span> */}
        </div>
      )}
    </div>
  );
};

const SectionHeader = ({ title, sectionActive, onClickActiveBtn }: any) => {
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '10px 24px',
        paddingRight: '42px',
      }}
    >
      <h3 style={{ margin: 0 }}>{title}</h3>
      <Switch
        size="small"
        checked={sectionActive}
        checkedChildren={'ON'}
        unCheckedChildren={'OFF'}
        onClick={onClickActiveBtn}
      />
    </div>
  );
};

const NewHomesForm = ({ sectionActive }: any) => {
  const {
    filterPayload,
    setFilterPayload,
    viewOnMapChainStores,
    setViewOnMapChainStores,
    selectedSite,
  } = useLandParcelSearch();

  const { newHomeBuilders, setNewHomeBuilders } = useSiteFilter();

  const [builders, setBuilders] = useState<any[]>([]);

  useEffect(() => {
    const fetchBuilders = async () => {
      const data = await getNewHomeBuildersData();
      const builderOptions = data.map((d: any) => ({
        value: d.builder,
        label: d.builder,
      }));

      setBuilders(builderOptions);
    };
    fetchBuilders();
  }, []);

  const builderOptionsWrapper = useCallback(
    (builderOptions) => {
      if (newHomeBuilders.length > 0) {
        if (
          newHomeBuilders.some((builder) =>
            [
              'All builders',
              'All builders excluding no names',
              'Builders beyond top 50',
              'Top 50 builders',
            ].includes(builder.name),
          )
        )
          return [];

        // Doing filter here, couldnt get Select filterOption prop working :(
        builderOptions = builderOptions.filter(
          (b: any) =>
            !newHomeBuilders.some((builder) => builder.name === b.value),
        );
      }
      const builderAddonOptions = [];

      if (
        !newHomeBuilders.some((builder) =>
          ['No builder name'].includes(builder.name),
        )
      ) {
        builderAddonOptions.unshift({
          value: 'No builder name',
          label: 'No builder name',
        });
      }

      builderAddonOptions.unshift({
        value: 'Builders beyond top 50',
        label: 'Builders beyond top 50',
      });
      builderAddonOptions.unshift({
        value: 'Top 50 builders',
        label: 'Top 50 builders',
      });

      builderOptions.unshift({
        value: 'Builders beyond top 50',
        label: 'Builders beyond top 50',
      });
      builderOptions.unshift({
        value: 'Top 50 builders',
        label: 'Top 50 builders',
      });

      if (
        builderOptions.length > 0 &&
        ['poi', 'county'].includes(selectedSite.method.type)
      ) {
        builderAddonOptions.unshift({
          value: 'All builders excluding no names',
          label: 'All builders excluding no names',
        });
        builderAddonOptions.unshift({
          value: 'All builders',
          label: 'All builders',
        });
      }

      // adding dashes on last addon options to differentiate
      builderAddonOptions[builderAddonOptions.length - 1] = {
        ...builderAddonOptions[builderAddonOptions.length - 1],
        label: (
          <div className="w-full h-full border-b-2 border-dashed border-[#ccc] pb-1">
            {builderAddonOptions[builderAddonOptions.length - 1].label}
          </div>
        ),
      };

      builderOptions.unshift(...builderAddonOptions);

      return builderOptions;
    },
    [selectedSite, newHomeBuilders],
  );

  useEffect(() => {
    if (sectionActive) {
      setFilterPayload((prev: any) => ({
        ...prev,
        newHomeBuilders: {
          filters: newHomeBuilders.map((builder) => ({
            name: builder.name,
            ...{
              [builder.distanceType === '<=' ? 'max_distance' : 'min_distance']:
                builder.distance,
            },
          })),
        },
      }));
    } else if (
      !sectionActive &&
      filterPayload.newHomeBuilders.filters.length > 0
    ) {
      setFilterPayload((prev: any) => ({
        ...prev,
        newHomeBuilders: {
          filters: [],
        },
      }));
    }
  }, [newHomeBuilders, sectionActive]);

  const onAddBuilder = useCallback(
    (builder: any) => {
      if (selectedSite.method.type === 'metro' && newHomeBuilders.length === 2)
        return;
      if (selectedSite.method.type === 'state' && newHomeBuilders.length === 1)
        return;

      if (
        [
          'All builders',
          'All builders excluding no names',
          'Top 50 builders',
          'Builders beyond top 50',
        ].includes(builder.name)
      ) {
        setNewHomeBuilders([builder]);
      } else {
        setNewHomeBuilders((prev) => [...prev, builder]);
      }
    },
    [selectedSite, newHomeBuilders],
  );

  const onChangeBuilder = (name: any, key: any, value: any) => {
    setNewHomeBuilders((prev) =>
      prev.map((builder) => {
        if (builder.name === name) {
          return { ...builder, [key]: value };
        }
        return builder;
      }),
    );
  };

  const onDeleteBuilder = (name: any) => {
    setNewHomeBuilders((prev) => prev.filter((b) => b.name !== name));
  };

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '24px',
        padding: '24px 32px',
      }}
    >
      <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
        {/* <div
          style={{
            display: 'flex',
            flexDirection: 'row-reverse',
            alignItems: 'center',
            gap: '6px',
          }}
        >
          <Switch
            size="small"
            checked={viewOnMapChainStores}
            onChange={(checked) => setViewOnMapChainStores(checked)}
            disabled={!sectionActive}
          />
          <span>View on map</span>
        </div> */}
        {/* <NewHomeSearch
        
          chains={newHomeBuilders}
          onAddChain={onAddChain}
          sectionActive={sectionActive}
        /> */}

        <div>
          <Select
            showSearch
            style={{ width: '100%' }}
            disabled={!sectionActive}
            placeholder="Search new home builders"
            value={null}
            onChange={(value) =>
              onAddBuilder({
                name: value,
                distance: 1,
                distanceType: '<=',
              })
            }
            options={builderOptionsWrapper(structuredClone(builders))}
          />
        </div>
        {['metro', 'state'].includes(selectedSite.method.type) && (
          <Alert
            message={
              selectedSite.method.type === 'metro'
                ? 'Max 2 new home builders can be added.'
                : 'Max 1 new home builder can be added.'
            }
            type="info"
            showIcon
            closable
          />
        )}
        <div
          style={{
            minHeight: '250px',
            display: 'flex',
            flexDirection: 'column',
            gap: '12px',
            justifyContent: newHomeBuilders.length === 0 ? 'center' : 'start',
          }}
        >
          {newHomeBuilders.length === 0 && (
            <div>
              <Empty description={'No new home builder added.'} />
            </div>
          )}
          {newHomeBuilders.length > 0 &&
            newHomeBuilders.map((builder, idx) => (
              <NewHomeBuilderSelectedItem
                key={idx}
                builder={builder}
                onChangeBuilder={onChangeBuilder}
                onDeleteBuilder={onDeleteBuilder}
                style={{
                  borderTop: idx === 0 ? 'none' : '1px solid #ccc',
                  paddingTop: idx === 0 ? 0 : '6px',
                }}
                sectionActive={sectionActive}
              />
            ))}
        </div>
      </div>
    </div>
  );
};

const NewHomeBuilderSelectedItem = ({
  builder,
  onChangeBuilder,
  onDeleteBuilder,
  sectionActive,
  style,
}: any) => {
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        width: '100%',
        gap: '12px',
        ...style,
      }}
    >
      <div style={{ width: '100%' }}>
        <div>
          <span style={{ fontWeight: 'bold' }}>{builder.name}</span>
        </div>
        <div style={{ display: 'flex', flexDirection: 'row', gap: '12px' }}>
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              minWidth: '160px',
            }}
          >
            <span>Distance within </span>
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: '12px',
              }}
            >
              <Select
                value={builder.distance}
                onChange={(value) =>
                  onChangeBuilder(builder.name, 'distance', value)
                }
                disabled={!sectionActive}
                style={{ width: '100%' }}
                options={[
                  { label: '1 mile', value: 1 },
                  { label: '3 miles', value: 3 },
                  { label: '5 miles', value: 5 },
                  { label: '10 miles', value: 10 },
                ]}
              />
            </div>
          </div>
        </div>
      </div>
      <div
        style={{
          width: '20px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
        }}
      >
        <Button
          type="link"
          danger
          style={{ padding: 0 }}
          onClick={() => onDeleteBuilder(builder.name)}
          disabled={!sectionActive}
        >
          <FaRegTrashAlt />
        </Button>
      </div>
    </div>
  );
};

// 1,3,5,10
const ChainStoreForm = ({ sectionActive }: any) => {
  const {
    filterPayload,
    setFilterPayload,
    viewOnMapChainStores,
    setViewOnMapChainStores,
  } = useLandParcelSearch();

  const { chainOperator, setChainOperator, chains, setChains } =
    useSiteFilter();

  const [chainDatePickerOpen, setChainDatePickerOpen] = useState<
    string | undefined
  >(undefined);

  useEffect(() => {
    if (sectionActive) {
      setFilterPayload((prev: any) => ({
        ...prev,
        chains: {
          operator: chainOperator,
          filters: chains.map((chain) => ({
            chain_id: chain.chain_id,
            opened: chain.opened,
            ...{
              [chain.distanceType === '<=' ? 'max_distance' : 'min_distance']:
                chain.distance,
            },
          })),
        },
      }));
    } else if (!sectionActive && filterPayload.chains.filters.length > 0) {
      setFilterPayload((prev: any) => ({
        ...prev,
        chains: {
          operator: 'AND',
          filters: [],
        },
      }));
    }
  }, [chainOperator, chains, sectionActive]);

  const onAddChain = (chain: any) => {
    setChains((prev) => [...prev, chain]);
  };

  const onChangeChain = (chain_id: any, key: any, value: any) => {
    setChains((prev) =>
      prev.map((chain) => {
        if (chain.chain_id === chain_id) {
          return { ...chain, [key]: value };
        }
        return chain;
      }),
    );
    if (chainDatePickerOpen && chainDatePickerOpen === chain_id)
      setChainDatePickerOpen(undefined);
  };

  const onDeleteChain = (chain_id: any) => {
    setChains((prev) => prev.filter((c) => c.chain_id !== chain_id));
  };

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '24px',
        padding: '24px 32px',
      }}
    >
      <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
        <div
          style={{
            display: 'flex',
            flexDirection: 'row-reverse',
            alignItems: 'center',
            gap: '6px',
          }}
        >
          <Switch
            size="small"
            checked={viewOnMapChainStores}
            onChange={(checked) => setViewOnMapChainStores(checked)}
            disabled={!sectionActive}
          />
          <span>View on map</span>
        </div>
        <ChainStoreSearch
          chains={chains}
          onAddChain={onAddChain}
          sectionActive={sectionActive}
        />
        <div>
          <span>Filter Operator: </span>
          <Segmented
            options={[
              { label: 'AND', value: 'AND' },
              { label: 'OR', value: 'OR' },
            ]}
            value={chainOperator}
            onChange={(value) =>
              value === 'AND' ? setChainOperator('AND') : setChainOperator('OR')
            }
            disabled={!sectionActive}
            size="small"
          />
        </div>
        <div
          style={{
            minHeight: '250px',
            display: 'flex',
            flexDirection: 'column',
            gap: '12px',
            justifyContent: chains.length === 0 ? 'center' : 'start',
          }}
        >
          {chains.length === 0 && (
            <div>
              <Empty description={'No chains stores added.'} />
            </div>
          )}
          {chains.length > 0 &&
            chains.map((chain, idx) => (
              <ChainStoreSelectedItem
                key={chain.chain_id}
                chain={chain}
                onChangeChain={onChangeChain}
                onDeleteChain={onDeleteChain}
                style={{
                  borderTop: idx === 0 ? 'none' : '1px solid #ccc',
                  paddingTop: idx === 0 ? 0 : '6px',
                }}
                sectionActive={sectionActive}
              />
            ))}
        </div>
      </div>
    </div>
  );
};

const ChainStoreSelectedItem = ({
  chain,
  onChangeChain,
  onDeleteChain,
  style,
  sectionActive,
}: any) => {
  return (
    <div
      key={chain.chain_id}
      style={{
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        width: '100%',
        gap: '12px',
        ...style,
      }}
    >
      <div style={{ width: '100%' }}>
        <div>
          <span style={{ fontWeight: 'bold' }}>{chain.chain_name}</span>
        </div>
        <div style={{ display: 'flex', flexDirection: 'row', gap: '12px' }}>
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              width: '100%',
            }}
          >
            <span>Opened since </span>
            <DatePicker
              value={chain.opened ? moment(chain.opened, dateFormat) : null}
              format={dateFormat}
              onChange={(date, dateString) =>
                onChangeChain(chain.chain_id, 'opened', dateString)
              }
              disabled={!sectionActive}
              showToday={false}
              renderExtraFooter={() => (
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    width: '100%',
                  }}
                >
                  {[3, 6, 12, 24, 36].map((month) => (
                    <Button
                      key={`${chain.chain_id}-${month}-month`}
                      type="link"
                      onClick={() =>
                        onChangeChain(
                          chain.chain_id,
                          'opened',
                          moment().subtract(month, 'months').format(dateFormat),
                        )
                      }
                    >{`${month} months ago`}</Button>
                  ))}
                </div>
              )}
            />
          </div>
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              minWidth: '160px',
            }}
          >
            <span>
              Nearest distance {chain.distanceType === '<=' ? 'under' : 'over'}
            </span>
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: '12px',
              }}
            >
              <Select
                value={chain.distanceType}
                onChange={(value) =>
                  onChangeChain(chain.chain_id, 'distanceType', value)
                }
                disabled={!sectionActive}
                style={{ width: 60 }}
                options={[
                  { label: '<=', value: '<=' },
                  { label: '>=', value: '>=' },
                ]}
              />

              <Select
                value={chain.distance}
                onChange={(value) =>
                  onChangeChain(chain.chain_id, 'distance', value)
                }
                disabled={!sectionActive}
                style={{ width: '100%' }}
                options={[
                  { label: '1 mile', value: 1 },
                  { label: '3 miles', value: 3 },
                  { label: '5 miles', value: 5 },
                  { label: '10 miles', value: 10 },
                ]}
              />
            </div>
          </div>
        </div>
      </div>
      <div
        style={{
          width: '20px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
        }}
      >
        <Button
          type="link"
          danger
          style={{ padding: 0 }}
          onClick={() => onDeleteChain(chain.chain_id)}
          disabled={!sectionActive}
        >
          <FaRegTrashAlt />
        </Button>
      </div>
    </div>
  );
};

const ChainStoreSearch = ({ chains, onAddChain, sectionActive }: any) => {
  // TODO: debounce search input
  const [searchText, setSearchText] = useState<string | undefined>(undefined);
  const debouncedSearchText = useDebounce(searchText, 300);
  const { data, isLoading } = useQuery(
    `ChainStoreSearch-${debouncedSearchText}`,
    () =>
      getPOIChainSearchData({
        search: debouncedSearchText,
        limit: 10,
        sorted: true,
      }),
    { enabled: debouncedSearchText !== undefined },
  );

  const onChange = (value: string) => {
    setSearchText(value);
  };

  const onSelect = (value: any, option: any) => {
    onAddChain({
      chain_id: option.value,
      chain_name: option.label,
      opened: null,
      distance: 1,
      distanceType: '<=',
    });

    setSearchText(undefined);
  };

  return (
    <div>
      <AutoComplete
        value={searchText}
        onChange={onChange}
        onSelect={onSelect}
        filterOption={(inputValue, option) =>
          chains.some((chain: any) => chain.chain_id === option.value)
            ? false
            : true
        }
        options={data
          ?.map((chain: any) => ({
            value: chain.chain_id,
            label: chain.chain_name,
          }))
          .sort((a: any, b: any) => a.label.localeCompare(b.label))}
        style={{ width: '100%' }}
        disabled={!sectionActive}
      >
        <Input.Search
          loading={isLoading}
          enterButton={false}
          placeholder="Search a chain"
        />
      </AutoComplete>
    </div>
  );
};

const AdvancedDemographicsForm = () => {
  const {
    selectedSite,
    filterPayload,
    setFilterPayload,
    viewOnMapDemographics,
    setViewOnMapDemographics,
  } = useLandParcelSearch();

  const {
    demographicsActive,
    selectedDemographics,
    setSelectedDemographics,
    advancedDemographicsActive,
    setAdvancedDemographicsActive,
  } = useSiteFilter();

  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    if (advancedDemographicsActive && demographicsActive) {
      setFilterPayload((prev: any) => ({
        ...prev,
        heatmap: selectedDemographics,
      }));
    } else if (
      (!advancedDemographicsActive || !demographicsActive) &&
      filterPayload.heatmap.length > 0
    ) {
      setFilterPayload((prev: any) => ({
        ...prev,
        heatmap: [],
      }));
    }
  }, [selectedDemographics, advancedDemographicsActive, demographicsActive]);

  const options = [
    // { value: 'median_hh_income', label: 'Median Household Income' },
    { value: 'five_year_pop_growth', label: 'Five Year Population Growth' },
    { value: 'bachelors_and_above', label: 'Bachelors and Above' },
    { value: 'median_age', label: 'Median Age' },
    { value: 'household_size', label: 'Household Size' },
    { value: 'population_density', label: 'Population Density' },
    { value: 'fifty_five_plus', label: '55+' },
    {
      value: 'household_growth',
      label: 'Household Growth (5-Year Projection)',
    },
    { value: 'rent_vs_own', label: 'Mtg Payment - Rent' },
    { value: 'rent_vs_owner_percentage', label: 'Rent vs Owner Percentage' },
    // { value: 'median_rent', label: 'Median Rent' },
    // { value: 'median_home_value', label: 'Median Home Value' },
    { value: 'rental_growth_5_years', label: 'Rent CAGR Trailing 5 Years' },
    { value: 'crime_score', label: 'Crime Score' },
  ];

  const getValueFormatter = (type: string) => {
    if (
      [
        'median_hh_income',
        'rent_vs_own',
        'median_rent',
        'median_home_value',
      ].includes(type)
    ) {
      const dollar = (value: any) =>
        `$ ${Math.round(value)}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      return dollar;
    } else if (
      [
        'five_year_pop_growth',
        'bachelors_and_above',
        'household_growth',
        'rent_vs_owner_percentage',
        'rental_growth_5_years',
        'fifty_five_plus',
      ].includes(type)
    ) {
      const percent = (value: any) => `${Math.round(value * 10) / 10}%`;
      return percent;
    } else if (
      ['median_age', 'household_size', 'population_density'].includes(type)
    ) {
      const number = (value: any) =>
        `${type === 'household_size' ? value : Math.round(value)}`.replace(
          /\B(?=(\d{3})+(?!\d))/g,
          ',',
        );
      return number;
    } else {
      return (value: any) => value;
    }
  };

  const getValueParser = (type: string) => {
    if (
      [
        'median_hh_income',
        'rent_vs_own',
        'median_rent',
        'median_home_value',
      ].includes(type)
    ) {
      const dollar = (value: any) =>
        value ? value.replace(/\$\s?|(,*)/g, '') : value;
      return dollar;
    } else if (
      [
        'five_year_pop_growth',
        'bachelors_and_above',
        'household_growth',
        'rent_vs_owner_percentage',
        'rental_growth_5_years',
        'fifty_five_plus',
      ].includes(type)
    ) {
      const percent = (value: any) => (value ? value.replace(/%/g, '') : value);
      return percent;
    } else if (
      ['median_age', 'household_size', 'population_density'].includes(type)
    ) {
      const number = (value: any) => (value ? value.replace(/,/g, '') : value);
      return number;
    } else {
      return (value: any) => value;
    }
  };

  const getRangeSteps = (type: string) => {
    if (type === 'household_size') {
      return 0.1;
    }
    return 1;
  };

  const handleDemographicSelector = (value: any, option: any) => {
    // setCurrentDemographic(option);
    // apply input range values
    const addDemographicToList = async () => {
      let min = 0;
      let max = 100;

      if (option.value !== 'crime_score') {
        const data = await getDemographicRangeData({
          type: option.value,
          body: selectedSite.method,
        });

        min = data && data.length > 0 && data[0].min !== null ? data[0].min : 0;
        max =
          data && data.length > 0 && data[0].max !== null ? data[0].max : 100;
      } else {
        min = 1;
        max = 10;
      }

      setSelectedDemographics((prev) => [
        ...prev,
        {
          type: option.value,
          min: min,
          max: max,
        },
      ]);

      setLoading(false);
    };

    if (option) {
      setLoading(true);
      addDemographicToList();
    }
  };

  const deleteSelectedDemographic = (selectedType: any) => {
    setSelectedDemographics((prev) =>
      prev.filter((d) => d.type !== selectedType),
    );
  };

  const updateSelectedDemographic = (newDemographic: any) => {
    const newSelectedDemographics = [...selectedDemographics];
    const idx = selectedDemographics.findIndex(
      (d) => d.type === newDemographic.type,
    );
    if (idx === -1) return;
    newSelectedDemographics[idx] = newDemographic;
    setSelectedDemographics(newSelectedDemographics);
  };

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '24px',
        // padding: '12px 32px 24px',
      }}
    >
      <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
          <div className="flex flex-row justify-between">
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: '12px',
                alignItems: 'center',
              }}
            >
              <Switch
                checked={advancedDemographicsActive && demographicsActive}
                onChange={(checked) => setAdvancedDemographicsActive(checked)}
              />
              <span
                style={{
                  fontWeight:
                    advancedDemographicsActive && demographicsActive
                      ? 600
                      : 'normal',
                }}
              >
                Advanced Demographics
              </span>
            </div>
            {advancedDemographicsActive && (
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row-reverse',
                  alignItems: 'center',
                  gap: '6px',
                }}
              >
                <Switch
                  size="small"
                  checked={viewOnMapDemographics}
                  onChange={(checked) => setViewOnMapDemographics(checked)}
                  disabled={!advancedDemographicsActive || !demographicsActive}
                />
                <span>View on map</span>
              </div>
            )}
          </div>

          {advancedDemographicsActive && (
            <Select
              value={null}
              onChange={handleDemographicSelector}
              placeholder="Select demographic"
              style={{ width: '100%' }}
              loading={loading}
              disabled={!advancedDemographicsActive || !demographicsActive}
            >
              {options.reduce((acc: any, option) => {
                if (selectedDemographics.find((d) => d.type === option.value)) {
                  return acc;
                }
                return [
                  ...acc,
                  <Select.Option value={option.value} key={option.value}>
                    {option.label}
                  </Select.Option>,
                ];
              }, [])}
            </Select>
          )}
        </div>
        {advancedDemographicsActive && (
          <div
            style={{
              minHeight: '250px',
              display: 'flex',
              flexDirection: 'column',
              justifyContent:
                selectedDemographics.length === 0 ? 'center' : 'start',
            }}
          >
            {!loading && selectedDemographics.length === 0 && (
              <div>
                <Empty description={'No demographic selected.'} />
              </div>
            )}
            {selectedDemographics.length > 0 &&
              selectedDemographics.map((demographic, idx) => (
                <SelectedDemographicConfig
                  key={demographic.type}
                  demographic={demographic}
                  updateSelectedDemographic={updateSelectedDemographic}
                  deleteSelectedDemographic={deleteSelectedDemographic}
                  formatter={getValueFormatter(demographic.type)}
                  parser={getValueParser(demographic.type)}
                  step={getRangeSteps(demographic.type)}
                  label={
                    options.find((d) => d.value === demographic.type)?.label
                  }
                  style={{
                    borderTop: idx === 0 ? 'none' : '1px solid #ccc',
                    paddingTop: idx === 0 ? 0 : '6px',
                  }}
                  sectionActive={
                    advancedDemographicsActive && demographicsActive
                  }
                />
              ))}
            {loading && <div>Loading...</div>}
          </div>
        )}
      </div>
    </div>
  );
};

const SelectedDemographicConfig = ({
  demographic,
  updateSelectedDemographic,
  deleteSelectedDemographic,
  label,
  formatter = (value: any) => value,
  parser = (value: any) => value,
  step = 1,
  style,
  sectionActive,
}: any) => {
  const [rangeMin, setRangeMin] = useState<number>(demographic.min);
  const [rangeMax, setRangeMax] = useState<number>(demographic.max);

  const handleRangeSlider = (value: any) => {
    const newDemographic = {
      ...demographic,
      min: value[0],
      max: value[1],
    };

    updateSelectedDemographic(newDemographic);
  };

  const handleRangeInputs = (minValue: any, maxValue: any) => {
    if (minValue > maxValue) {
      maxValue = minValue;
    } else if (maxValue < minValue) {
      minValue = maxValue;
    }

    const newDemographic = {
      ...demographic,
      min: minValue,
      max: maxValue,
    };

    updateSelectedDemographic(newDemographic);
  };

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'row',
        width: '100%',
        gap: '12px',
        ...style,
      }}
    >
      <div style={{ width: '100%' }}>
        <div>
          <span style={{ fontWeight: 'bold' }}>{label}</span>
        </div>
        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            gap: '12px',
            justifyContent: 'center',
            width: '100%',
          }}
        >
          <InputNumber
            className="landDev-input-number"
            value={demographic.min}
            min={rangeMin}
            max={rangeMax}
            step={step}
            onChange={(value) => handleRangeInputs(value, demographic.max)}
            formatter={formatter}
            parser={parser}
            style={{ width: '100%' }}
            disabled={!sectionActive}
          />
          <InputNumber
            className="landDev-input-number"
            value={demographic.max}
            min={rangeMin}
            max={rangeMax}
            step={step}
            onChange={(value) => handleRangeInputs(demographic.min, value)}
            formatter={formatter}
            parser={parser}
            style={{ width: '100%' }}
            disabled={!sectionActive}
          />
        </div>
        <div>
          <Slider
            range={{ draggableTrack: true }}
            value={[demographic.min, demographic.max]}
            min={rangeMin}
            max={rangeMax}
            step={step}
            onChange={(value: any) => handleRangeSlider(value)}
            tooltip={{ formatter: formatter }}
            disabled={!sectionActive}
          />
        </div>
      </div>
      <div
        style={{
          width: '20px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
        }}
      >
        <Button
          type="link"
          danger
          style={{ padding: 0 }}
          onClick={() => deleteSelectedDemographic(demographic.type)}
          disabled={!sectionActive}
        >
          <FaRegTrashAlt />
        </Button>
      </div>
    </div>
  );
};

// prettier-ignore
const LessThanFilterSelect = ({ label, filterKey, filters, onChangeFilter, selectOptions, sectionActive }:any) => {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
       <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          gap: '12px',
          alignItems: 'center',
        }}
      >
        <Switch
          checked={filters[filterKey].isChecked && sectionActive}
          onChange={(checked) =>
            onChangeFilter(filterKey + '.isChecked', checked)
          }/>
      {/* <Checkbox
        checked={filters[filterKey].isChecked}
        onChange={(e) =>
          onChangeFilter(filterKey + '.isChecked', e.target.checked)
        }
      > */}
        <span
          style={{ fontWeight: filters[filterKey].isChecked && sectionActive ? 600 : 'normal' }}
        >
          {label}
        </span>
      {/* </Checkbox> */}
      </div>
      {filters[filterKey].isChecked && sectionActive && (
        <div style={{ marginLeft: 25}}>
          {/* {filters[filterKey].isChecked && (
            <> */}
              <Select
                disabled={!filters[filterKey].isChecked}
                value={filters[filterKey].max}
                onChange={(value) => onChangeFilter(filterKey + '.max', value)}
                options={selectOptions}
                />
              <span> or less.</span>
            {/* </>
          )} */}
        </div>
      )}
    </div>
  );
};

// prettier-ignore
const GreaterThanFilterSelect = ({ label, filterKey, filters, onChangeFilter, selectOptions, sectionActive }:any) => {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
       <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          gap: '12px',
          alignItems: 'center',
        }}
      >
        <Switch
          checked={filters[filterKey].isChecked && sectionActive}
          onChange={(checked) =>
            onChangeFilter(filterKey + '.isChecked', checked)
          }/>
      {/* <Checkbox
        checked={filters[filterKey].isChecked}
        onChange={(e) =>
          onChangeFilter(filterKey + '.isChecked', e.target.checked)
        }
      > */}
        <span
          style={{ fontWeight: filters[filterKey].isChecked && sectionActive? 600 : 'normal' }}
        >
          {label}
        </span>
      {/* </Checkbox> */}
      </div>
      {filters[filterKey].isChecked && sectionActive && (
        <div style={{ marginLeft: 25}}>
          {/* {filters[filterKey].isChecked && ( */}
            {/* <> */}
              <Select
                disabled={!filters[filterKey].isChecked}
                value={filters[filterKey].min}
                onChange={(value) => onChangeFilter(filterKey + '.min', value)}
                options={selectOptions}
                />
              <span> or more.</span>
            {/* </>
          )} */}
        </div>
      )}
    </div>
  );
};

const getWidthOfString = (str: string) => {
  // This is estimation, font style and size may affect the actual width of the string
  const widthIdx: any = {
    ' ': 3,
    ',': 3,
    '.': 3,
  };
  let width = 0;
  for (let i = 0; i < str.length; i++) {
    if (widthIdx[str[i]]) {
      width += widthIdx[str[i]];
    } else {
      width += 10;
    }
  }
  return width;
};

const MultiFilterInput = ({
  label,
  filterKey,
  filters,
  onChangeFilter,
  formatter = (value: any) => value,
  parser = (value: any) => value,
  step,
  sectionActive = true,
}: any) => {
  const onChangeInput = useCallback(
    (type: any, value: any) => {
      onChangeFilter(type, value);
    },
    [onChangeFilter],
  );

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          gap: '12px',
          alignItems: 'center',
        }}
      >
        <Switch
          checked={filters[filterKey].isChecked && sectionActive}
          onChange={(checked) =>
            onChangeFilter(filterKey + '.isChecked', checked)
          }
        />
        <span
          style={{
            fontWeight:
              filters[filterKey].isChecked && sectionActive ? 600 : 'normal',
          }}
        >
          {label}
        </span>
      </div>
      {filters[filterKey].isChecked && sectionActive && (
        <div style={{ display: 'flex', flexDirection: 'row', gap: '12px' }}>
          <Select
            disabled={!filters[filterKey].isChecked}
            value={filters[filterKey].type}
            onChange={(value) => onChangeFilter(filterKey + '.type', value)}
            style={{ minWidth: '75px' }}
            options={[
              { label: '>=', value: '>=' },
              { label: '<=', value: '<=' },
              { label: '<->', value: '<->' },
            ]}
          />
          {(filters[filterKey].type === '<->' ||
            filters[filterKey].type === '>=') && (
            <InputNumber
              disabled={!filters[filterKey].isChecked}
              className="landDev-input-number"
              value={filters[filterKey].min}
              onChange={(value) => onChangeInput(filterKey + '.min', value)}
              formatter={formatter ? formatter : null}
              parser={parser ? parser : null}
              min={0}
              step={step ? step : 1}
              style={{ width: '100%' }}
            />
          )}
          {(filters[filterKey].type === '<->' ||
            filters[filterKey].type === '<=') && (
            <InputNumber
              disabled={!filters[filterKey].isChecked}
              className="landDev-input-number"
              value={filters[filterKey].max}
              onChange={(value) => onChangeInput(filterKey + '.max', value)}
              formatter={formatter ? formatter : null}
              parser={parser ? parser : null}
              min={0}
              step={step ? step : 1}
              style={{ width: '100%' }}
            />
          )}
        </div>
      )}
    </div>
  );
};

const SwitchFilterInput = ({
  label,
  filterKey,
  onChangeFilter,
  sectionActive,
  children,
}: {
  label: string;
  filterKey: string;
  onChangeFilter: (key: any, value: any) => void;
  sectionActive: boolean;
  children?: React.ReactNode;
}) => {
  return (
    <div
      style={
        children
          ? {
              display: 'flex',
              flexDirection: 'column',
              gap: '12px',
            }
          : undefined
      }
    >
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          gap: '12px',
          alignItems: 'center',
        }}
      >
        <Switch
          checked={sectionActive}
          onChange={(checked) =>
            onChangeFilter(`${filterKey}.isChecked`, checked)
          }
        />
        <span
          style={{
            fontWeight: sectionActive ? 600 : 'normal',
          }}
        >
          {label}
        </span>
      </div>
      {children}
    </div>
  );
};

const NestedSelectFilterInput = ({
  label,
  filterKey,
  filters,
  selectOptions,
  onChangeFilter,
  sectionActive = true,
  ...props
}: any) => {
  return (
    <div className="flex flex-row items-center gap-2">
      <Checkbox
        checked={filters[filterKey].isChecked}
        onChange={(e) =>
          onChangeFilter(`${filterKey}.isChecked`, e.target.checked)
        }
        disabled={!sectionActive}
      >
        <span className="inline-block min-w-[85px] whitespace-nowrap">
          {label}
        </span>
      </Checkbox>
      <Select
        disabled={!filters[filterKey].isChecked || !sectionActive}
        value={filters[filterKey].value}
        onChange={(value) => onChangeFilter(`${filterKey}.value`, value)}
        options={selectOptions}
        style={{ width: '150px' }}
        {...props}
      />
    </div>
  );
};

const NestedMultiFilterInput = ({
  label,
  filterKey,
  filters,
  onChangeFilter,
  sectionActive = true,
  min,
  max,
  step = 1,
}: any) => {
  return (
    <div className="flex flex-row items-center gap-2">
      <Checkbox
        checked={filters[filterKey].isChecked}
        onChange={(e) =>
          onChangeFilter(`${filterKey}.isChecked`, e.target.checked)
        }
        disabled={!sectionActive}
      >
        <span className="inline-block min-w-[85px] whitespace-nowrap">
          {label}
        </span>
      </Checkbox>
      <Select
        disabled={!filters[filterKey].isChecked || !sectionActive}
        value={filters[filterKey].type}
        onChange={(value) => onChangeFilter(`${filterKey}.type`, value)}
        style={{ minWidth: '70px' }}
        options={[
          { label: '==', value: '==' },
          { label: '>=', value: '>=' },
          { label: '<=', value: '<=' },
          { label: '<->', value: '<->' },
        ]}
      />

      {(filters[filterKey].type === '<->' ||
        filters[filterKey].type === '>=' ||
        filters[filterKey].type === '==') && (
        <InputNumber
          disabled={!filters[filterKey].isChecked || !sectionActive}
          className="landDev-input-number"
          value={filters[filterKey].min}
          onChange={(value) => onChangeFilter(`${filterKey}.min`, value)}
          // formatter={formatter ? formatter : null}
          // parser={parser ? parser : null}
          min={min}
          max={max}
          step={step}
          style={{ width: '100%' }}
        />
      )}
      {(filters[filterKey].type === '<->' ||
        filters[filterKey].type === '<=') && (
        <InputNumber
          disabled={!filters[filterKey].isChecked || !sectionActive}
          className="landDev-input-number"
          value={filters[filterKey].max}
          onChange={(value) => onChangeFilter(`${filterKey}.max`, value)}
          // formatter={formatter ? formatter : null}
          // parser={parser ? parser : null}
          min={min}
          max={max}
          step={step}
          style={{ width: '100%' }}
        />
      )}
    </div>
  );
};
