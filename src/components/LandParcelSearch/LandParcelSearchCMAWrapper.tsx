import { getOtherOwnersDetails } from '@/services/data';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import {
  AdminAreaMethodSelector,
  FilterForm,
  LandParcelSearch,
  Loader,
  MapLayer,
  PointsOfInterestMethod,
  ResultDisplayProvider,
  ResultFilterProvider,
  ResultFuzzySearchProvider,
  ResultPanel,
  ResultSorterProvider,
  SiteFilter,
  SiteFilterProvider,
  SiteSelection,
  SiteSelectionSubmit,
  useLandParcelSearch,
} from '.';
import OwnersOtherParcelsPanel from './OwnersOtherParcelsPanel';

type LandFeature = LandParcelSearch.LandFeature;

// NOTE: This component is stays in CMA app
const LandParcelSearchCMAWrapper = () => {
  const map = useSelector((state: any) => state.CMA.map);
  const ownersOtherParcelsTrigger = useSelector(
    (state: any) => state.CMA.ownersOtherParcelsTrigger,
  );
  const appViewMode = useSelector((state: any) => state.CMA.appViewMode);
  const dispatch = useDispatch();

  const {
    showSiteSelectionPanel,
    setShowSiteSelectionPanel,
    showFilterPanel,
    setShowFilterPanel,
    showResultPanel,
    setShowResultPanel,
    setSelectedSite,
    siteSelectionMethod,
    setSiteSelectionMethod,
    setShowOwnersOtherParcelsPanel,
    showOwnersOtherParcelsPanel,
  } = useLandParcelSearch();

  const onCloseLandSearchParcelFeature = () => {
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        cmaTabKey: '1',
        appViewMode:
          appViewMode.subMode.length === 1
            ? {
                mode: 'CMA',
                subMode: [],
              }
            : {
                mode: appViewMode.mode,
                subMode: appViewMode.subMode.filter(
                  (mode: string) => mode !== 'LandParcelSearch',
                ),
              },
        ownersOtherParcelsTrigger: null,
      },
    });
  };

  const onSaveLandSiteSelection = (sitePayload: any) => {
    setSelectedSite(sitePayload);
    setShowSiteSelectionPanel(false);
    setShowFilterPanel(true);
  };

  const onSearchSubmit = (values: any) => {};

  useEffect(() => {
    console.log(
      'land-search ownersOtherParcelsTrigger',
      ownersOtherParcelsTrigger,
    );

    if (ownersOtherParcelsTrigger) {
      setShowResultPanel(false);
      setShowSiteSelectionPanel(false);
      setShowOwnersOtherParcelsPanel(true);
      // setCurrentView('OWNERSOTHERPARCELS')
    }
  }, [ownersOtherParcelsTrigger]);
  return (
    <div id="land-parcel-search" style={{ position: 'relative' }}>
      <ResultDisplayProvider>
        {appViewMode.subMode[appViewMode.subMode.length - 1] ===
          'LandParcelSearch' && (
          <SiteFilterProvider>
            <LandParcelSearch onClose={onCloseLandSearchParcelFeature}>
              {showSiteSelectionPanel && (
                <SiteSelection
                  method={siteSelectionMethod}
                  setMethod={setSiteSelectionMethod}
                >
                  {['state', 'metro', 'county'].includes(
                    siteSelectionMethod,
                  ) && <AdminAreaMethodSelector map={map} />}

                  {siteSelectionMethod === 'poi' && (
                    <PointsOfInterestMethod
                      map={map}
                      onSave={onSaveLandSiteSelection}
                    />
                  )}
                  <SiteSelectionSubmit />
                </SiteSelection>
              )}
              {showFilterPanel && (
                <SiteFilter>
                  <FilterForm />
                </SiteFilter>
              )}
              {showOwnersOtherParcelsPanel && <OwnersOtherParcelsPanel />}
              {showResultPanel && (
                <ResultFilterProvider>
                  <ResultSorterProvider<LandFeature>>
                    <ResultFuzzySearchProvider>
                      <ResultPanel />
                    </ResultFuzzySearchProvider>
                  </ResultSorterProvider>
                </ResultFilterProvider>
              )}
            </LandParcelSearch>
            <MapLayer map={map} />
          </SiteFilterProvider>
        )}
      </ResultDisplayProvider>
      <Loader />
    </div>
  );
};

export default LandParcelSearchCMAWrapper;
