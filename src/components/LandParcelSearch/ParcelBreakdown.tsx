export function organizeParcelData(parcel: any) {
  // Predefine the keys that are organized in the new structure
  const organizedKeys = [
    'ogc_fid',
    'geoid',
    'parcelnumb',
    'address',
    'scity',
    'state2',
    'szip',
    'owner',
    'mailadd',
    'mail_city',
    'mail_state2',
    'mail_zip',
    'parvaltype',
    'improvval',
    'landval',
    'parval',
    'zoning_type',
    'zoning_subtype',
    'zoning_id',
    'zoning_code_link',
    'usecode',
    'usedesc',
    'lbcs_activity',
    'lbcs_activity_desc',
    'lbcs_function',
    'lbcs_function_desc',
    'lbcs_structure',
    'lbcs_structure_desc',
    'lbcs_site',
    'lbcs_site_desc',
    'usps_vacancy',
    'usps_vacancy_date',
    'dpv_codes',
    'dpv_notes',
    'dpv_type',
    'cass_errorno',
    'numunits',
    'structstyle',
    'll_gisacre',
    'll_gissqft',
    'lat',
    'lon',
    'qoz',
    'qoz_tract',
    'census_block',
    'census_blockgroup',
    'census_tract',
    'legaldesc',
    'subdivision',
    'zoning',
    'zoning_description',
  ];

  const newParcel: any = {
    parcelAddress: {
      objectId: parcel.ogc_fid,
      fipsCode: parcel.geoid,
      parcelNumber: parcel.parcelnumb,
      alternativeApn: parcel?.estated?.owners?.apn,
      siteAddress: parcel.address,
      siteCity: parcel.scity,
      siteState: parcel.state2,
      siteZip: parcel.szip,
    },
    ownerInformation: {
      ownerName: parcel.owner_name,
      mailingInformation: {
        mailingAddress:
          parcel?.estated?.owners?.formatted_street_address ||
          parcel.owner_street_address,
        mailingCity: parcel?.estated?.owners?.city || parcel.owner_city,
        mailingState: parcel?.estated?.owners?.state || parcel.owner_state,
        mailingZip: parcel?.estated?.owners?.zip_code || parcel.owner_zip_code,
      },
    },
    propertySalesAndValue: {
      countyProvidedValues: {
        last_sale_date: parcel?.estated?.lastsale?.assr_last_sale_date,
        last_sale_amount: parcel?.estated?.lastsale?.assr_last_sale_amount,
        parcelValueType: parcel.parvaltype,
        improvementValue:
          parcel?.estated?.market_assessment?.improvement_value ||
          parcel.improvval,
        landValue:
          parcel?.estated?.market_assessment?.land_value || parcel.landval,
        parcelValue:
          parcel?.estated?.market_assessment?.total_value || parcel.parval,
        propertyTax: parcel?.estated?.taxes?.amount,
      },
    },
    zoningLandUseVacancy: {
      zoningType: parcel.zoning_type,
      zoning: parcel.zoning,
      zoningDescription: parcel.zoning_description,
      zoningSubtype: parcel.zoning_subtype,
      zoningId: parcel.zoning_id,
      zoningCodeLink: parcel.zoning_code_link,
      parcelUseCode: parcel.usecode,
      parcelUseDescription: parcel.usedesc,
      standardizedLandUseCodes: {
        landUseCodeActivity: parcel.lbcs_activity,
        landUseCodeActivityDescription: parcel.lbcs_activity_desc,
        landUseCodeFunction: parcel.lbcs_function,
        landUseCodeFunctionDescription: parcel.lbcs_function_desc,
        landUseCodeStructure: parcel.lbcs_structure,
        landUseCodeStructureDescription: parcel.lbcs_structure_desc,
        landUseCodeSite: parcel.lbcs_site,
        landUseCodeSiteDescription: parcel.lbcs_site_desc,
      },
      residentialAndVacancyIndicators: {
        uspsVacancyIndicator: parcel.usps_vacancy,
        uspsVacancyIndicatorDate: parcel.usps_vacancy_date,
        deliveryPointValidationCodes: parcel.dpv_codes,
        deliveryPointValidationNotes: parcel.dpv_notes,
        deliveryPointMatchType: parcel.dpv_type,
        cassErrorCodes: parcel.cass_errorno,
      },
    },
    structureDetails: {
      numberOfLivingUnits: parcel.numunits,
      structureStyle: parcel.structstyle,
      regridCalculatedData: {
        regridCalculatedBuildingCount: parcel.ll_bldg_count,
        regridCalculatedBuildingFootprintSquareFeet:
          parcel.ll_bldg_footprint_sqft
            ? parcel.ll_bldg_footprint_sqft.toLocaleString('en-US')
            : 'N/A',
      },
    },
    geographicInformation: {
      centroidCoordinates: `${parcel.lat}, ${parcel.lon}`,
      opportunityZone: {
        federalQualifiedOpportunityZone: parcel.qoz,
        qualifiedOpportunityZoneTractNumber: parcel.qoz_tract,
      },
      censusGeographies: {
        censusBlock: parcel.census_block,
        censusBlockgroup: parcel.census_blockgroup,
        censusTract: parcel.census_tract,
      },
      regridCalculatedData: {
        calculatedAcres: parcel.gisacre ? parcel.gisacre : 'N/A',
        calculatedParcelSqFt: parcel.sqft
          ? parcel.sqft.toLocaleString('en-US')
          : 'N/A',
      },
      platBlockLotLegalData: {
        legalDescription: parcel.legaldesc,
        subdivision: parcel.subdivision,
      },
    },
    additionalItem: {},
  };

  // Iterate over all keys in the original parcel data
  Object.keys(parcel).forEach((key: any) => {
    // If the key is not in the list of organized keys, add it to additionalItem
    if (!organizedKeys.includes(key)) {
      if (
        key === 'original_address' ||
        key === 'cdl_raw'
        // || key === 'fema_flood_zone_raw'
      )
        return;
      else {
        let value;
        try {
          value = JSON.parse(parcel[key]);
        } catch (err) {
          value = parcel[key];
        }

        if (Array.isArray(value)) {
          console.log('Array', key, value);
          newParcel.additionalItem = {
            ...newParcel.additionalItem,
            ...value
              .map((item) => handleJSONItem(key, item))
              .reduce((a, b) => ({ ...a, ...b }), {}),
          };
        } else if (isNaN(value) && typeof value === 'object') {
          newParcel.additionalItem = {
            ...newParcel.additionalItem,
            ...handleJSONItem(key, value),
          };
        } else {
          newParcel.additionalItem[key] = value;
        }
      }
    }
  });

  // sort the additionalItem object by key
  newParcel.additionalItem = Object.keys(newParcel.additionalItem)
    .sort()
    .reduce((obj: any, key) => {
      obj[key] = newParcel.additionalItem[key];
      return obj;
    }, {});

  return newParcel;
}

const handleJSONItem = (key: string, value: Record<string, any>) => {
  const obj = {} as Record<string, any>;
  const keys = Object.keys(value);
  for (let i = 0; i < keys.length; i++) {
    obj[`${key}_${keys[i]}`] = value[keys[i]];
  }

  console.log('Object', obj);
  return obj;
};

import { Button, Collapse, Divider } from 'antd';
import React, { Children, useState } from 'react';
import { useLandBreakdown } from './land-breakdown';
const { Panel } = Collapse;
function parseCamelCaseToString(input: any) {
  if (input === 'fipsCode') return 'FIPS Code';
  if (input === 'propertySalesAndValue') return 'Property Sales & Values';
  if (input === 'zoningLandUseVacancy') return 'Zoning, Land Use & Vacancy';
  if (input === 'gisAcre') return 'GIS Acres';
  let result = input.replace(/_/g, ' ');
  result = result.replace(/([A-Z])/g, ' $1');
  result = result
    .split(' ')
    .map(
      (word: any) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase(),
    )
    .join(' ');

  return result;
}

function formatCurrency(number: any) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0, // Remove decimal points
    maximumFractionDigits: 0, // Remove decimal points
  }).format(number);
}

const ParcelBreakdown = (data: any) => {
  const { setCurrentView } = useLandBreakdown();
  console.log('test raw parcel data', data);
  const parcel = organizeParcelData(data.data);
  console.log('test parcel data', parcel);
  const renderLink = (key: any, value: any) => (
    <div
      style={{
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
      }}
    >
      <p style={{ fontWeight: 600 }}>{parseCamelCaseToString(key)}</p>
      {value ? (
        <a href={value} target="_blank" style={{ color: 'blue' }}>
          Click to Open
        </a>
      ) : (
        <p style={{ fontSize: '12px' }}>N/A</p>
      )}
    </div>
  );

  const renderValue = (key: string, value: any) => (
    <div
      style={{
        display: 'grid',
        gridTemplateColumns: '1fr 1fr',
      }}
    >
      <p style={{ fontWeight: 600 }}>{parseCamelCaseToString(key)}</p>
      <p>{value ? value : 'N/A'}</p>
    </div>
  );

  const renderObject = (key: any, value: any) => (
    <>
      {/* <p style={{ fontWeight: 600, fontSize: '16px' }}>
        {parseCamelCaseToString(key)}
      </p> */}
      <div
        style={{
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
        }}
      >
        {
          value &&
            Object.entries(value).map(([subKey, subValue]: any) => (
              <React.Fragment key={subKey}>
                {' '}
                {/* Ensure to use React.Fragment with a key here */}
                <p style={{ fontWeight: 600, fontSize: '14px' }}>
                  {parseCamelCaseToString(subKey)}
                </p>
                <p style={{ fontSize: '14px' }}>
                  {[
                    'last_sale_amount',
                    'improvementValue',
                    'landValue',
                    'parcelValue',
                    'propertyTax',
                  ].includes(subKey)
                    ? subValue > 0
                      ? formatCurrency(subValue)
                      : 'Not provided'
                    : subValue || 'N/A'}
                </p>
              </React.Fragment>
            ))
          // : (
          //   <p>Data is not available</p>
          // )
        }
      </div>
    </>
  );

  return (
    <div>
      {Object.entries(parcel).map(([key, value]: any) => (
        <>
          <Divider plain>
            <p className="font-semibold text-lg pt-2">
              {parseCamelCaseToString(key)}
            </p>
            {key === 'zoningLandUseVacancy' && (
              <div>
                <Button
                  type="default"
                  size="small"
                  onClick={() => setCurrentView('ZONEOMICS')}
                >
                  More Details
                </Button>
              </div>
            )}
            {key === 'ownerInformation' && (
              <div className="flex gap-4 justify-center">
                <Button
                  type="default"
                  size="small"
                  onClick={() => setCurrentView('CONTACT')}
                >
                  Contact Information
                </Button>
                <Button
                  type="default"
                  size="small"
                  onClick={() => setCurrentView('OWNERSOTHERPARCELS')}
                >
                  Owner's Other Parcels
                </Button>
              </div>
            )}
            {key === 'propertySalesAndValue' && (
              <div>
                <Button
                  type="default"
                  size="small"
                  onClick={() => setCurrentView('TAXPROPER')}
                >
                  More Details
                </Button>
              </div>
            )}
          </Divider>
          <Collapse ghost defaultActiveKey={[...Array(11).keys()]}>
            {Object.entries(value).map(([key, val], idx) => {
              if (typeof val !== 'object') {
                return key === 'zoningCodeLink'
                  ? renderLink(key, val)
                  : renderValue(key, val);
              } else {
                return renderObject(key, val);
              }
            })}
          </Collapse>
        </>
      ))}
    </div>
  );
};

export default ParcelBreakdown;
