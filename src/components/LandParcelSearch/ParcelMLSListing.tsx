import { Button, Collapse, Tooltip } from 'antd';
import { useCallback, useState } from 'react';
import { FaRegCopy } from 'react-icons/fa';

const { Panel } = Collapse;

const ParcelMLSListing = ({ data }: any) => {
  if (!data || data.length === 0) return <div>No MLS listings found</div>;
  if (data.length === 1) return <RenderMLSListing data={data[0]} />;
  return (
    <div id="land-parcel-search-mls-listing-collapse">
      <Collapse accordion defaultActiveKey={0}>
        {data
          .sort((a: any, b: any) => a.mls_id.localeCompare(b.mls_id))
          .map((listing: any, index: number) => (
            <Panel
              header={
                <div
                  style={{
                    fontWeight: 600,
                    padding: '10px 0',
                  }}
                >
                  <span>{`Listing ${index + 1}`}</span>
                </div>
              }
              key={index}
            >
              <RenderMLSListing data={listing} />
            </Panel>
          ))}
      </Collapse>
    </div>
  );
};

const DetailValue = ({ value }: { value: string }) => {
  const [mouseOver, setMouseOver] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);

  const onClickHandler = useCallback(() => {
    navigator.clipboard.writeText(value);
    setShowTooltip(true);
    setTimeout(() => setShowTooltip(false), 1500);
  }, [value]);

  return (
    <div
      style={{
        borderRadius: '6px',
        display: 'inline-block',
        textAlign: 'right',
      }}
      onMouseEnter={() => setMouseOver(true)}
      onMouseLeave={() => setMouseOver(false)}
    >
      <Button
        type="link"
        style={{
          padding: 0,
          visibility:
            value && value.length > 0 && mouseOver ? 'visible' : 'hidden',
        }}
        size="small"
        onClick={onClickHandler}
      >
        <FaRegCopy />
      </Button>

      <Tooltip title="Copied to clipboard" open={showTooltip}>
        <span style={{ pointerEvents: 'none' }}>{value}</span>
      </Tooltip>
    </div>
  );
};

const keyFormatter = (key: string) => {
  if (key === 'sl_uuid') return 'Spatial Laser ID';
  if (key === 'mls_id') return 'MLS ID';
  if (key === 'dom') return 'Days on Market';
  if (key === 'cdom') return 'Cumulative Days on Market';
  if (key === 'member_phone') return 'Phone';
  if (key === 'member_email') return 'Email';
  if (key === 'member_full_name') return 'Name';

  return key
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

const valueFormatter = (key: string, value: any) => {
  if (['original_price', 'current_price', 'close_price'].includes(key)) {
    return `$${value.toLocaleString()}`;
  }
  return value;
};

const RenderMLSListing = ({ data }: any) => {
  return (
    <div style={{ padding: '12px 24px' }}>
      <h3 style={{ margin: 0 }}>Property</h3>
      {Object.keys(data)
        .filter(
          (key) =>
            ![
              'modification_timestamp',
              'first_entry_timestamp',
              'agent_info',
              'geography',
            ].includes(key),
        )
        .sort()
        .map((key: string, index: number) => {
          return (
            <div
              key={index}
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: '12px',
                justifyContent: 'space-between',
                borderBottom: '1px solid #ccc',
                padding: '3px 0',
              }}
            >
              <span>{keyFormatter(key)}:</span>
              <DetailValue
                value={`${
                  data[key] !== null ? valueFormatter(key, data[key]) : '-'
                }`}
              />
            </div>
          );
        })}
      {data.agent_info && (
        <>
          <h3 style={{ margin: 0, marginTop: '10px' }}>Agent</h3>
          {Object.keys(data.agent_info)
            .filter(
              (key) =>
                ![
                  'office_key_numeric',
                  'office_phone',
                  'office_broker_mls_id',
                  'office_postal_code',
                  'office_corporate_license',
                  'office_state_or_province',
                  'office_mail_city',
                  'office_mail_address',
                  'license_number',
                  'member_mls_id',
                ].includes(key),
            )
            .sort((a, b) => {
              const order = [
                'member_full_name',
                'member_email',
                'member_phone',
              ];
              return order.indexOf(a) - order.indexOf(b);
            })
            .map((key: string, index: number) => {
              return (
                <div
                  key={index}
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    gap: '12px',
                    justifyContent: 'space-between',
                    borderBottom: '1px solid #ccc',
                    padding: '3px 0',
                  }}
                >
                  <span>{keyFormatter(key)}:</span>
                  <DetailValue
                    value={`${
                      data.agent_info[key] !== null
                        ? valueFormatter(key, data.agent_info[key])
                        : '-'
                    }`}
                  />
                </div>
              );
            })}
        </>
      )}
    </div>
  );
};

export default ParcelMLSListing;
