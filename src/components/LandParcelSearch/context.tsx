import {
  Dispatch,
  SetStateAction,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import { QueryClient, QueryClientProvider } from 'react-query';
import { serverType } from '../../services/data';
import { capitalize } from '../../utils/strings';
// import mockData from './mockData.json';
// import mockMeta from './mockMeta.json';
import { Button, Spin } from 'antd';
import * as z from 'zod';
import './global.css';

import { getUserToken, getUsername } from '@/utils/auth';
import { LandParcelSearch } from './types/types';
import {
  LandFeatureBoundarySchema,
  LandFeatureSchema,
  LandListingMLSPropertiesSchema,
  LandListingShowcasePropertiesSchema,
  LandParcelPropertiesSchema,
  landParcelSchema,
} from './types/zod-schemas';
import { landDataToFeatures } from './utils/conversions';

type LandParcel = LandParcelSearch.LandParcel;
type LandFeature = LandParcelSearch.LandFeature;

const getSocketURL = async ({
  server,
  type,
}: {
  server: 'LOCAL' | 'LIVE';
  type: 'Parcel' | 'Listing';
}) => {
  // if (type === 'Parcel') {
  //   if (server === 'LIVE' || process.env.NODE_ENV === 'production')
  //     return `wss://api.locatealpha.com/cma/${serverType}/ws/land-parcel-search`;
  //   return `ws://localhost:8080/cma/${serverType}/ws/land-parcel-search`;
  // } else {
  const username = await getUsername();
  const token = await getUserToken('access');
  if (server === 'LIVE' || process.env.NODE_ENV === 'production')
    return `wss://api.locatealpha.com/sbs/${serverType}/api/v1/land/search/listing/ws?user_id=${username}&access_token=${token}`;
  return `ws://localhost:9003/sbs/${serverType}/api/v1/land/search/listing/ws?user_id=${username}&access_token=${token}`;
  // }
};

// export type LandParcel = z.infer<typeof landParcelSchema>;

function parse(o: any): LandParcel {
  try {
    return landParcelSchema.parse(o);
  } catch (err) {
    console.log('err', err);
    console.log('o', o);
    throw err;
  }
}

function landFeatureParse(o: any) {
  try {
    return LandFeatureSchema.parse(o);
  } catch (err) {
    console.log('err', err);
    console.log('o', o);
    throw err;
  }
}

function landFeatureBoundaryParse(o: any) {
  try {
    return LandFeatureBoundarySchema.parse(o);
  } catch (err) {
    console.log('err', err);
    console.log('o', o);
    throw err;
  }
}

export const getLandParcelKeyType = (dataType: string) => {
  try {
    if (
      LandParcelPropertiesSchema.keyof().safeParse(dataType) ||
      LandListingMLSPropertiesSchema.keyof().safeParse(dataType) ||
      LandListingShowcasePropertiesSchema.keyof().safeParse(dataType)
    ) {
      let dataTypeShape = null;
      if (LandParcelPropertiesSchema.keyof().safeParse(dataType)) {
        dataTypeShape =
          LandParcelPropertiesSchema.shape[
            dataType as keyof typeof LandParcelPropertiesSchema.shape
          ];
      } else if (LandListingMLSPropertiesSchema.keyof().safeParse(dataType)) {
        dataTypeShape =
          LandListingMLSPropertiesSchema.shape[
            dataType as keyof typeof LandListingMLSPropertiesSchema.shape
          ];
      } else if (
        LandListingShowcasePropertiesSchema.keyof().safeParse(dataType)
      ) {
        dataTypeShape =
          LandListingShowcasePropertiesSchema.shape[
            dataType as keyof typeof LandListingShowcasePropertiesSchema.shape
          ];
      }

      if (dataType == 'mls_listing') {
        return 'string';
      }

      if (
        dataTypeShape instanceof z.ZodString ||
        (dataTypeShape instanceof z.ZodNullable &&
          dataTypeShape.unwrap() instanceof z.ZodString)
      ) {
        return 'string';
      } else if (
        dataTypeShape instanceof z.ZodNumber ||
        (dataTypeShape instanceof z.ZodNullable &&
          dataTypeShape.unwrap() instanceof z.ZodNumber)
      ) {
        return 'number';
      } else if (dataTypeShape instanceof z.ZodBoolean) {
        return 'boolean';
      } else if (dataTypeShape instanceof z.ZodArray) {
        return 'array';
      } else if (dataTypeShape instanceof z.ZodObject) {
        return 'object';
      } else {
        return 'string';
      }
    }
  } catch (err) {
    console.log('dataType', dataType);
    console.log('err', err);
  }
};

export const getTitleOfColumn = (column: keyof LandParcel | string) => {
  let text = '';
  if (['fips', 'apn'].includes(column)) {
    text = column.toUpperCase();
  } else if (['elem', 'middle', 'high'].includes(column)) {
    if (column === 'elem') {
      text = 'Elementary School Score';
    }
    text = `${column[0].toUpperCase() + column.slice(1)} School Score`;
  } else if (column === 'deed_owner_1') {
    text = 'Deed Owner';
  } else if (column === 'cbsa_name') {
    text = 'Metro name';
  } else if (column === 'medianhhincome') {
    text = 'Median Household Income';
  } else if (column === 'mls_listing') {
    text = 'MLS Listings';
  } else if (column === 'sl_land_source') {
    text = 'Land Source';
  } else if (column === 'sl_uuid') {
    text = 'Spatial Laser ID';
  } else if (column === 'standardized_land_use_type') {
    text = 'Standardized Landuse Type';
  } else if (column === 'for_type') {
    text = 'Listing Type';
  } else if (column === 'sub_type') {
    text = 'Landuse Type';
  } else if (
    [
      'nearest_highway_distance_miles',
      'nearest_powerline_distance_miles',
      'nearest_railway_distance_miles',
    ].includes(column)
  ) {
    text = column
      .split('_')
      .map(capitalize)
      .slice(1)
      .join(' ')
      .replace('Distance', 'Dist.')
      .replace('Miles', 'mi');
  } else {
    text = column.split('_').map(capitalize).join(' ');
  }
  return text;
};

export type ZoningType =
  | 'Undefined'
  | 'Residential'
  | 'Commercial'
  | 'Special'
  | 'Mixed'
  | 'Planned'
  | 'Agriculture'
  | 'Industrial'
  | 'Overlay'
  | 'Others';
export type ZoningSubtype =
  | 'Undefined'
  | 'Single Family'
  | 'Two Family'
  | 'Multi Family'
  | 'Mobile Home Park'
  | 'General Commercial'
  | 'Core Commercial'
  | 'Retail Commercial'
  | 'Neighborhood Commercial'
  | 'Office'
  | 'Special Commercial'
  | 'Mixed Use'
  | 'Industrial'
  | 'Light Industrial'
  | 'Special'
  | 'Planned'
  | 'Overlay';

type FilterPayloadType = {
  type: 'Parcel' | 'Listing';
  site: any;
  heatmap: any[];
  zoning: {
    type: ZoningType[];
    subtype: ZoningSubtype[];
  };
  chains: {
    operator: 'AND' | 'OR';
    filters: {
      chain_id: String;
      opened: String;
      max_distance?: number;
      min_distance?: number;
    }[];
  };
  newHomeBuilders: {
    filters: {
      name: String;
      max_distance?: number;
      min_distance?: number;
    }[];
  };
};

export const DEFAULT_FILTER_PAYLOAD: FilterPayloadType = {
  type: 'Parcel',
  site: {},
  zoning: {
    type: [],
    subtype: [],
  },
  heatmap: [],
  chains: {
    operator: 'AND',
    filters: [],
  },
  newHomeBuilders: {
    filters: [],
  },
};

export type SiteMethods = 'county' | 'state' | 'metro' | 'poi';

type LandParcelSearchContext =
  | {
      showSiteSelectionPanel: boolean;
      setShowSiteSelectionPanel: Dispatch<SetStateAction<boolean>>;
      showFilterPanel: boolean;
      setShowFilterPanel: Dispatch<SetStateAction<boolean>>;
      showResultPanel: boolean;
      setShowResultPanel: Dispatch<SetStateAction<boolean>>;
      selectedSite: any;
      setSelectedSite: Dispatch<SetStateAction<any>>;
      filterPayload: FilterPayloadType;
      setFilterPayload: Dispatch<SetStateAction<FilterPayloadType>>;
      handleSearchSubmit: () => void;
      searchResults: LandFeature[];
      setSearchResults: Dispatch<SetStateAction<LandFeature[]>>;
      isLoading: boolean;
      loadingMessage: string;
      siteSelectionMethod: SiteMethods;
      setSiteSelectionMethod: Dispatch<SetStateAction<SiteMethods>>;

      adminAreaMethod: 'map' | 'search';
      setAdminAreaMethod: Dispatch<SetStateAction<'map' | 'search'>>;
      adminAreaResult: any;
      setAdminAreaResult: Dispatch<SetStateAction<any>>;

      metroMethod: any;
      setMetroMethod: Dispatch<SetStateAction<any>>;
      metroResult: any;
      setMetroResult: Dispatch<SetStateAction<any>>;

      points: any;
      setPoints: Dispatch<SetStateAction<any>>;
      pointsRadius: any;
      setPointsRadius: Dispatch<SetStateAction<any>>;
      viewOnMapDemographics: any;
      setViewOnMapDemographics: Dispatch<SetStateAction<any>>;
      viewOnMapChainStores: any;
      setViewOnMapChainStores: Dispatch<SetStateAction<any>>;
      selectedResult: LandFeature | null;
      setSelectedResult: Dispatch<SetStateAction<LandFeature | null>>;
      filteredResults: LandFeature[];
      setFilteredResults: Dispatch<SetStateAction<LandFeature[]>>;
      activeResultTab: 'all-sites' | 'saved-sites';
      setActiveResultTab: Dispatch<SetStateAction<'all-sites' | 'saved-sites'>>;
      cancelQuery: boolean;
      setCancelQuery: Dispatch<SetStateAction<boolean>>;

      resultMeta: any;
      setResultMeta: any;

      parcelBreakDownId: any;
      setParcelBreakDownId: Dispatch<SetStateAction<any>>;

      selectedUserGroup?: string;

      showOwnersOtherParcelsPanel: boolean;
      setShowOwnersOtherParcelsPanel: Dispatch<SetStateAction<boolean>>;

      // searchResultBoundaries: { sl_uuid: string; boundary: Geometry }[];
      // setSearchResultBoundaries: Dispatch<
      //   SetStateAction<{ sl_uuid: string; boundary: Geometry }[]>
      // >;
    }
  | undefined;

const queryClient = new QueryClient();

const landParcelSearchContext =
  createContext<LandParcelSearchContext>(undefined);

let socket: WebSocket | null = null;

interface LandParcelSearchProviderProps {
  children: React.ReactNode;
  selectedUserGroup?: string;
}
export const LandParcelSearchProvider = ({
  children,
  selectedUserGroup,
}: LandParcelSearchProviderProps) => {
  // layout
  const [showSiteSelectionPanel, setShowSiteSelectionPanel] = useState(true);
  const [showFilterPanel, setShowFilterPanel] = useState(false);
  const [showResultPanel, setShowResultPanel] = useState(false);
  const [showOwnersOtherParcelsPanel, setShowOwnersOtherParcelsPanel] =
    useState(false);
  // const [showSiteSelectionPanel, setShowSiteSelectionPanel] = useState(false);
  // const [showFilterPanel, setShowFilterPanel] = useState(true);
  // const [showResultPanel, setShowResultPanel] = useState(true);

  // site selection
  const [siteSelectionMethod, setSiteSelectionMethod] =
    useState<SiteMethods>('poi');
  const [selectedSite, setSelectedSite] = useState<any>(null);

  const [adminAreaMethod, setAdminAreaMethod] = useState<'map' | 'search'>(
    'map',
  );
  const [adminAreaResult, setAdminAreaResult] = useState<any | undefined>(
    undefined,
  );

  const [metroMethod, setMetroMethod] = useState<'map' | 'search'>('map');
  const [metroResult, setMetroResult] = useState<any | undefined>(undefined);
  const [points, setPoints] = useState<any>([]);
  const [pointsRadius, setPointsRadius] = useState<any>(2);
  const [viewOnMapDemographics, setViewOnMapDemographics] =
    useState<any>(false);
  const [viewOnMapChainStores, setViewOnMapChainStores] = useState<any>(false);

  const [filterPayload, setFilterPayload] = useState<FilterPayloadType>(
    DEFAULT_FILTER_PAYLOAD,
  );

  // const [searchResults, setSearchResults] = useState<any>([]);
  // const [searchResultBoundaries, setSearchResultBoundaries] = useState<
  //   {
  //     sl_uuid: string;
  //     boundary: Geometry;
  //   }[]
  // >([]);
  const [searchResults, setSearchResults] = useState<LandFeature[]>(
    // mockData as LandParcel[],
    [],
  );
  const [selectedResult, setSelectedResult] = useState<LandFeature | null>(
    null,
  );
  const [filteredResults, setFilteredResults] = useState<LandFeature[]>([]);

  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('Loading');

  const [activeResultTab, setActiveResultTab] = useState<
    'all-sites' | 'saved-sites'
  >('all-sites');

  const [cancelQuery, setCancelQuery] = useState(false);

  const [resultMeta, setResultMeta] = useState<any>(
    // mockMeta,
    {},
  );

  const [parcelBreakDownId, setParcelBreakDownId] = useState<{
    sl_uuid?: string;
    boundary_id?: string;
  } | null>(null);

  const isLoadingRef = useRef(isLoading);
  isLoadingRef.current = isLoading;

  useEffect(() => {
    return () => {
      queryClient.clear();
      setPoints([]);
      setSearchResults([]);
      setSelectedResult(null);
      setFilteredResults([]);
    };
  }, []);

  useEffect(() => {
    if (cancelQuery) {
      if (socket) {
        console.log('cancel query closing socket');
        try {
          socket.close(1000, 'User cancelling request');
        } catch (error) {
          console.log('error closing socket', error);
        }
      }
      setIsLoading(false);
      setCancelQuery(false);
      socket = null;
    }
  }, [cancelQuery]);

  const handleSearchSubmit = async () => {
    if (socket) return;
    if (!showResultPanel) setShowResultPanel(true);
    setIsLoading(true);
    if (searchResults.length > 0) setSearchResults([]);
    if (filteredResults.length > 0) setFilteredResults([]);
    if (selectedResult) setSelectedResult(null);

    const startTimer = performance.now();
    const processTimes: any = {};
    let processTimer = performance.now();

    socket = new WebSocket(
      await getSocketURL({
        // server: 'LOCAL',
        server: 'LIVE',
        type: filterPayload.type as 'Parcel' | 'Listing',
      }),
    );

    console.log('selectedSite', selectedSite);
    console.log('filterPayload', filterPayload);

    socket.onopen = () => {
      if (!socket) return;
      processTimer = performance.now();
      console.log('socket open');
      socket.send(
        JSON.stringify({
          action: 'LAND_PARCEL_SEARCH',
          data: {
            method: selectedSite.method,
            filters: filterPayload,
          },
        }),
      );
    };

    socket.onmessage = (event) => {
      if (!socket) return;
      const response = JSON.parse(event.data);

      if (response.action.toLowerCase().includes('success')) {
        console.log('results', response.data);

        const parcels = response.data.parcels;
        const features = response.data.features;
        const boundaries = response.data.boundaries;
        const meta = response.data.meta;
        console.log('meta', meta);

        const emptyBoundaries = parcels.filter((d: any) => !d.boundary);
        console.log('emptyBoundaries', emptyBoundaries);

        if (filterPayload.type === 'Parcel') {
          // const { features } = landDataToFeatures(parcels);
          console.log('features', features);
          const landParcelFeatureResults = features.map(landFeatureParse);
          // const landParcelBoundaries = boundaries.map(landFeatureBoundaryParse);
          console.log('landParcelFeatureResults', landParcelFeatureResults);

          // const landParcelResults = parcels.map(parse);
          setSearchResults(landParcelFeatureResults);
          // setSearchResultBoundaries(landParcelBoundaries);
        } else if (filterPayload.type === 'Listing') {
          const landListingFeatureResults = features.map(landFeatureParse);
          // const landListingBoundaries = boundaries.map(
          //   landFeatureBoundaryParse,
          // );
          console.log('landListingFeatureResults', landListingFeatureResults);
          setSearchResults(landListingFeatureResults);
          // setSearchResultBoundaries(landListingBoundaries);
        }

        setResultMeta(meta);
        socket.close(1000);
      } else if (response.action.toLowerCase().includes('processing')) {
        console.log('response', response.data);
        setLoadingMessage(response.data.stage);
        const processTimeEnd = performance.now();
        processTimes[response.data.stage] = {
          ms: processTimeEnd - processTimer,
          s: (processTimeEnd - processTimer) / 1000,
        };
        processTimer = performance.now();
      } else if (response.action.toLowerCase().includes('error')) {
        console.log('error', response?.data);
        alert(response?.data?.message);
        setSearchResults([]);
        setIsLoading(false);
        socket.close();
      }
    };

    socket.onclose = (event) => {
      if (!socket) return;
      const endTimer = performance.now();
      processTimes['Total'] = {
        ms: endTimer - startTimer,
        s: (endTimer - startTimer) / 1000,
      };
      console.log('Duration:');
      console.table(processTimes);

      if (event.code != 1000) {
        console.log('socket closed - Abnormal', event);
        alert('Something went wrong. Please try again.');
      } else {
        console.log('socket closed - Normal', event);
      }
      if (isLoadingRef.current) {
        setIsLoading(false);
      }
      socket.close();
      socket = null;
    };
  };

  return (
    <QueryClientProvider client={queryClient}>
      <landParcelSearchContext.Provider
        value={{
          showSiteSelectionPanel,
          setShowSiteSelectionPanel,
          showFilterPanel,
          setShowFilterPanel,
          showResultPanel,
          setShowResultPanel,
          selectedSite,
          setSelectedSite,
          filterPayload,
          setFilterPayload,
          handleSearchSubmit,
          searchResults,
          setSearchResults,
          isLoading,
          loadingMessage,

          siteSelectionMethod,
          setSiteSelectionMethod,
          adminAreaMethod,
          setAdminAreaMethod,
          adminAreaResult,
          setAdminAreaResult,

          metroMethod,
          setMetroMethod,
          metroResult,
          setMetroResult,

          points,
          setPoints,
          pointsRadius,
          setPointsRadius,
          viewOnMapDemographics,
          setViewOnMapDemographics,
          viewOnMapChainStores,
          setViewOnMapChainStores,
          selectedResult,
          setSelectedResult,
          filteredResults,
          setFilteredResults,
          activeResultTab,
          setActiveResultTab,
          cancelQuery,
          setCancelQuery,
          resultMeta,
          setResultMeta,
          parcelBreakDownId,
          setParcelBreakDownId,

          selectedUserGroup,

          showOwnersOtherParcelsPanel,
          setShowOwnersOtherParcelsPanel,
        }}
      >
        {children}
      </landParcelSearchContext.Provider>
      {/* <ReactQueryDevtools initialIsOpen={false} /> */}
    </QueryClientProvider>
  );
};

export const useLandParcelSearch = () => {
  const context = useContext(landParcelSearchContext);
  if (context === undefined) {
    throw new Error(
      'useLandParcelSearch must be used within LandParcelSearchProvider component',
    );
  }
  return context;
};

export const Loader = () => {
  const {
    isLoading,
    loadingMessage,
    activeResultTab,
    cancelQuery,
    setCancelQuery,
  } = useLandParcelSearch();
  const [showCancel, setShowCancel] = useState(false);

  const messageParser = useCallback((message: string) => {
    if (message.toLowerCase().includes('reading filter inputs')) {
      return 'Reading filter inputs';
    } else if (message.toLowerCase().includes('fetching parcels')) {
      return 'Gathering land parcels';
    } else if (message.toLowerCase().includes('processing parcels')) {
      return 'Processing land parcels';
    } else if (
      message.includes('Applying chain store filters') ||
      message.includes('Applying advanced demographics filters')
    ) {
      return message;
    }
    return 'Loading';
  }, []);

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    if (isLoading) {
      if (showCancel) setShowCancel(false);
      timeoutId = setTimeout(() => {
        setShowCancel(true);
      }, 90000);
    }
    return () => {
      clearTimeout(timeoutId);
    };
  }, [isLoading]);

  if (!isLoading) return null;
  return (
    <>
      <div
        style={{
          position: 'absolute',
          top: '133px',
          left: 0,
          right: activeResultTab === 'all-sites' ? 0 : 'auto',
          bottom: '52px',
          width: activeResultTab === 'all-sites' ? '100%' : '500px',
          background: 'rgba(255,255,255,.75)',
        }}
        onContextMenu={(e) => e.preventDefault()}
      >
        <div
          style={{
            position: 'absolute',
            top: '30%',
            left: '50%',
            transform: 'translate(-50%,-50%)',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            gap: '24px',
            background: 'white',
            borderRadius: '8px',
            padding: '24px',
            border: '1px solid #ccc',
            width: '250px',
          }}
        >
          <Spin size="large" />
          <span style={{ fontSize: '16px' }}>
            {messageParser(loadingMessage)}
          </span>
          {showCancel && (
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: '12px',
              }}
            >
              <span>
                It's been over 1 min and 30 secs, do you want to cancel?
              </span>
              <Button onClick={() => setCancelQuery(true)}>Cancel</Button>
            </div>
          )}
        </div>
      </div>
      {activeResultTab === 'all-sites' && (
        <div
          style={{
            position: 'absolute',
            left: '502px',
            right: 0,
            bottom: 0,
            height: '60px',
            background: 'rgba(255,255,255,.75)',
          }}
          onContextMenu={(e) => e.preventDefault()}
        />
      )}
    </>
  );
};
