import { useClientAllowedCoordinates } from '@/hooks/client-access';
import { <PERSON>Complete, Button, Input, Segmented, Slider, message } from 'antd';
import {
  Dispatch,
  SetStateAction,
  useCallback,
  useEffect,
  useState,
} from 'react';
import { FaRegCopy, FaRegTrashAlt } from 'react-icons/fa';
import { useQuery } from 'react-query';
import { SiteMethods, useLandParcelSearch } from '.';
import poiMarkerImg from '../../assets/images/mapbox/marker/mapbox-marker-icon-20px-red.png';
import { geojsonTemplate } from '../../constants';
import {
  getCBSAOfPointData,
  getCBSASearchData,
  getClientAllowedAccessCBSAData,
  getClientAllowedAccessCountyData,
  getClientAllowedAccessStateData,
  getCountyOfPointData,
  getCountySearchData,
  getMapboxReverseGeocodingData,
  getStateOfPointData,
  getStateSearchData,
} from '../../services/data';
import { capitalize } from '../../utils/strings';

type SiteSelectionProps = {
  method: SiteMethods;
  setMethod: Dispatch<SetStateAction<SiteMethods>>;
  children: React.ReactNode;
};

export const SiteSelection = ({
  method,
  setMethod,
  children,
}: SiteSelectionProps) => {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', width: '500px' }}>
      <div style={{ padding: '10px 32px' }}>
        <h3 style={{ fontSize: '16px', fontWeight: 500, margin: 0 }}>
          Site Selection
        </h3>
      </div>
      <div
        style={{
          // padding: '16px 32px',
          display: 'flex',
          flexDirection: 'column',
          gap: '16px',
          height: '100%',
          minHeight: '0',
        }}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '12px',
            padding: '0 32px',
          }}
        >
          <div>
            <span style={{ fontWeight: 500 }}>Selection Method</span>
          </div>
          <div>
            <Segmented
              options={[
                { label: 'Points of Interest', value: 'poi' },
                { label: 'County', value: 'county' },
                { label: 'Metro', value: 'metro' },
                { label: 'State', value: 'state' },
              ]}
              value={method}
              onChange={(value) => setMethod(value as SiteMethods)}
              block
            />
          </div>
        </div>
        {children}
      </div>
    </div>
  );
};

export const AdminAreaMethodSelector = ({ map }: any) => {
  const {
    siteSelectionMethod,
    adminAreaMethod,
    setAdminAreaMethod,
    adminAreaResult,
    setAdminAreaResult,
  } = useLandParcelSearch();
  const [lat, setLat] = useState<number | undefined>(undefined);
  const [lng, setLng] = useState<number | undefined>(undefined);

  useEffect(() => {
    if (!map) return;
    if (adminAreaMethod !== 'map') {
      if (lat !== undefined && lng !== undefined) {
        setLat(undefined);
        setLng(undefined);
      }
      return;
    }

    const setLngLat = () => {
      const center = map.getCenter();
      setLat(center.lat);
      setLng(center.lng);
    };

    setLngLat();
    map.on('moveend', setLngLat);
    return () => {
      map.off('moveend', setLngLat);
    };
  }, [map, adminAreaMethod]);

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '16px',
        height: '100%',
        minHeight: 0,
      }}
    >
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '12px',
          padding: '0 32px',
        }}
      >
        <div>
          <span style={{ fontWeight: 500 }}>
            {capitalize(siteSelectionMethod)} Selection
          </span>
        </div>
        <div>
          <Segmented
            options={[
              { label: 'Map Center', value: 'map' },
              { label: 'Search', value: 'search' },
            ]}
            value={adminAreaMethod}
            onChange={(value) => setAdminAreaMethod(value as 'map' | 'search')}
            block
          />
        </div>
      </div>
      {adminAreaMethod === 'map' && lat && lng && (
        <AdminAreaMethodMap
          type={siteSelectionMethod}
          lat={lat}
          lng={lng}
          setData={setAdminAreaResult}
          fetchFn={
            siteSelectionMethod === 'county'
              ? getCountyOfPointData
              : siteSelectionMethod === 'metro'
              ? getCBSAOfPointData
              : getStateOfPointData
          }
        />
      )}
      {adminAreaMethod === 'search' && (
        <>
          {siteSelectionMethod === 'county' && (
            <AdminAreaMethodSearch
              type={siteSelectionMethod}
              adminResult={adminAreaResult}
              setAdminResult={setAdminAreaResult}
              fetchFn={getCountySearchData}
            />
          )}
          {siteSelectionMethod === 'metro' && (
            <AdminAreaMethodSearch
              type={siteSelectionMethod}
              adminResult={adminAreaResult}
              setAdminResult={setAdminAreaResult}
              fetchFn={getCBSASearchData}
            />
          )}
          {siteSelectionMethod === 'state' && (
            <AdminAreaMethodSearch
              type={siteSelectionMethod}
              adminResult={adminAreaResult}
              setAdminResult={setAdminAreaResult}
              fetchFn={getStateSearchData}
            />
          )}
        </>
      )}
      {/* <div>
      <Button type="primary" disabled={!metro} onClick={saveSiteHandler}>
        Save Site
      </Button>
    </div> */}
    </div>
  );
};

const SiteSelectionContainer = ({ children }: any) => {
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '12px',
        height: '100%',
        minHeight: 0,
        padding: '0 32px',
      }}
    >
      <div>
        <span style={{ fontWeight: 500 }}>Selection</span>
      </div>
      {children}
    </div>
  );
};

const SiteResultContainer = ({
  children,
  padding,
  border,
  backgroundColor,
  style,
}: any) => {
  return (
    <div
      style={{
        padding: padding ? padding : '16px 32px',
        border: border ? border : '2px solid #eee',
        backgroundColor: backgroundColor ? backgroundColor : '#f4f4f4',
        ...(style ? style : {}),
      }}
    >
      {children}
    </div>
  );
};

const AdminAreaMethodMap = ({ type, lat, lng, setData, fetchFn }: any) => {
  // const { selectedUserGroup } = useLandParcelSearch();
  // const allowed = useClientAllowedCoordinates({
  //   latitude: lat,
  //   longitude: lng,
  //   selectedUserGroup: selectedUserGroup,
  // });
  const { data, isLoading } = useQuery(
    `get${type}OfLatLng-${lat}-${lng}`,
    () => fetchFn({ lat, lng }),
    // { enabled: allowed == true },
  );

  // DISABLE RESTRICTION CHECKS until solution is decided since we have limited city codes...
  // useEffect(() => {
  //   if (allowed === false) {
  //     message.error('You do not have access to this location');
  //     return;
  //   }
  // }, [allowed]);

  useEffect(() => {
    if (!isLoading && data && (data as any[]).length > 0) {
      setData(data[0]);
      return;
    }
    setData(undefined);
  }, [data, isLoading]);

  return (
    <SiteSelectionContainer>
      <SiteResultContainer>
        <div style={{ textAlign: 'center', width: '100%', height: '100%' }}>
          {isLoading && <div>Loading...</div>}
          {data && data.length > 0 && <div>{data[0].name}</div>}
          {!isLoading && (!data || (data && data.length === 0)) && (
            <div>Please use the map to navigate to a {type}</div>
          )}
        </div>
      </SiteResultContainer>
    </SiteSelectionContainer>
  );
};

const AdminAreaMethodSearch = ({
  type,
  adminResult,
  setAdminResult,
  fetchFn,
}: any) => {
  const { selectedUserGroup } = useLandParcelSearch();
  const [search, setSearch] = useState<string | undefined>(undefined);

  const { data, isLoading } = useQuery(
    `get${type}Search-${search}`,
    () => fetchFn({ search, limit: 10, sorted: true }),
    { enabled: search !== undefined },
  );

  useEffect(() => {
    if (adminResult) setAdminResult(undefined);
  }, []);

  useEffect(() => {
    if (!adminResult) return;
    // if (
    //   selectedUserGroup &&
    //   !['Embry', 'Vertica'].includes(selectedUserGroup) &&
    //   process.env.UMI_APP_SERVER_TYPE === 'exp'
    // )
    //   return;

    const checkIfAllowed = async () => {
      if (type === 'county') {
        const allowed = await getClientAllowedAccessCountyData({
          countyKey: adminResult.key,
        });
        if (!allowed) {
          message.error('You do not have access to this location');
          setAdminResult(undefined);
        }
      } else if (type === 'metro') {
        const allowed = await getClientAllowedAccessCBSAData({
          cbsaKey: adminResult.key,
        });
        if (!allowed) {
          message.error('You do not have access to this location');
          setAdminResult(undefined);
        }
      } else if (type === 'state') {
        const allowed = await getClientAllowedAccessStateData({
          stateAbbrev: adminResult.abbrev,
        });
        if (!allowed) {
          message.error('You do not have access to this location');
          setAdminResult(undefined);
        }
      }
    };

    // DISABLE RESTRICTION CHECKS until solution is decided since we have limited city codes...
    // checkIfAllowed();
  }, [type, adminResult, selectedUserGroup]);

  return (
    <SiteSelectionContainer>
      <div>
        <AutoComplete
          value={search}
          onChange={(value) => setSearch(value)}
          onSelect={(value) => {
            setAdminResult(data.find((d: any) => d.key === value));
            setSearch(undefined);
          }}
          options={data?.map((d: any) => ({ label: d.name, value: d.key }))}
          style={{ width: '100%' }}
        >
          <Input.Search
            loading={isLoading}
            enterButton={false}
            placeholder={`Search a ${type}`}
          />
        </AutoComplete>
      </div>
      <SiteResultContainer>
        <div style={{ textAlign: 'center', width: '100%', height: '100%' }}>
          {isLoading && <div>Loading...</div>}
          {adminResult && <div>{adminResult.name}</div>}
          {!isLoading && !adminResult && <div>Please search a {type}</div>}
        </div>
      </SiteResultContainer>
      {/* <div>{metro && <div>{metro.name}</div>}</div> */}
    </SiteSelectionContainer>
  );
};

export const PointsOfInterestMethod = ({ map, onSave }: any) => {
  const { points, setPoints, pointsRadius, setPointsRadius } =
    useLandParcelSearch();

  return (
    <SiteSelectionContainer>
      <div>
        <span>
          Radius for points{' '}
          {`(${pointsRadius} ${pointsRadius === 1 ? 'mile' : 'miles'})`}
        </span>
        <Slider
          defaultValue={pointsRadius}
          min={1}
          max={10}
          onAfterChange={(value) => setPointsRadius(value)}
        />
      </div>
      <div>
        <span>Use the map to select points of interest {'(Max 10)'}</span>
      </div>
      <SiteResultContainer
        padding={points.length > 0 ? '0px' : null}
        border={points.length > 0 ? '0' : null}
        backgroundColor={points.length > 0 ? 'white' : null}
        style={
          points.length > 0
            ? { height: '100%', minHeight: 0, overflowY: 'auto' }
            : {}
        }
      >
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '6px',
            height: '100%',
            overflowY: 'auto',
          }}
        >
          {points.length === 0 && <div>No points selected</div>}
          {points.map((point) => (
            <div
              key={point.key}
              style={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'space-between',
                borderBottom: '1px solid #ccc',
              }}
            >
              <div
                style={{
                  width: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                }}
              >
                <div style={{}}>
                  <Input
                    // style={{ border: 'none' }}
                    value={point.name}
                    onChange={(e) => {
                      setPoints((prev) => {
                        const newPoints = [...prev];
                        const index = newPoints.findIndex(
                          (p) => p.key === point.key,
                        );
                        newPoints[index].name = e.target.value;
                        return newPoints;
                      });
                    }}
                    placeholder="Name"
                    style={{ textOverflow: 'ellipsis', fontSize: '16px' }}
                    className={'invisible-input'}
                  />
                </div>
                <div style={{ display: 'flex' }}>
                  <div style={{ width: '100%' }}>
                    <Input
                      // style={{ border: 'none' }}
                      value={point.address}
                      onChange={(e) => {
                        setPoints((prev) => {
                          const newPoints = [...prev];
                          const index = newPoints.findIndex(
                            (p) => p.key === point.key,
                          );
                          newPoints[index].address = e.target.value;
                          return newPoints;
                        });
                      }}
                      placeholder="Address"
                      style={{ textOverflow: 'ellipsis' }}
                      className={'invisible-input'}
                    />
                  </div>
                  <div style={{ width: '40px' }}>
                    <Button
                      type="link"
                      style={{ padding: '0px' }}
                      onClick={() =>
                        navigator.clipboard.writeText(point.address)
                      }
                    >
                      <FaRegCopy size={18} />
                    </Button>
                  </div>
                </div>
                <div style={{ display: 'flex' }}>
                  <div
                    style={{
                      width: '100%',
                      padding: '4px 11px',
                      pointerEvents: 'none',
                      userSelect: 'none',
                    }}
                  >
                    <span>
                      {parseFloat(point.coordinates.lat.toFixed(5))},{' '}
                      {parseFloat(point.coordinates.lng.toFixed(5))}
                    </span>
                  </div>
                  <div style={{ width: '40px' }}>
                    <Button
                      type="link"
                      style={{ padding: '0px' }}
                      onClick={() =>
                        navigator.clipboard.writeText(
                          `${point.coordinates.lat}, ${point.coordinates.lng}`,
                        )
                      }
                    >
                      <FaRegCopy size={18} />
                    </Button>
                  </div>
                </div>
              </div>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  width: '20px',
                }}
              >
                <Button
                  type="link"
                  danger
                  style={{ padding: '0px' }}
                  onClick={() => {
                    setPoints((prev) => {
                      const newPoints = [...prev];
                      const index = newPoints.findIndex(
                        (p) => p.key === point.key,
                      );
                      newPoints.splice(index, 1);
                      return newPoints;
                    });
                  }}
                >
                  <FaRegTrashAlt size={18} />
                </Button>
              </div>
            </div>
          ))}
        </div>
      </SiteResultContainer>
    </SiteSelectionContainer>
  );
};

export const SiteSelectionSubmit = ({ onSave }: any) => {
  const {
    siteSelectionMethod,
    adminAreaResult,
    points,
    pointsRadius,
    setSelectedSite,
    setShowSiteSelectionPanel,
    setShowFilterPanel,
  } = useLandParcelSearch();

  let disabled = false;
  if (['state', 'metro'].includes(siteSelectionMethod) && !adminAreaResult) {
    disabled = true;
  } else if (siteSelectionMethod === 'poi' && points.length === 0) {
    disabled = true;
  }

  const saveSiteHandler = () => {
    let sitePayload;
    if (siteSelectionMethod === 'state') {
      sitePayload = {
        method: {
          type: 'state',
          state_key: adminAreaResult.key,
          state_abbrev: adminAreaResult.abbrev,
        },
      };
    } else if (siteSelectionMethod === 'metro') {
      sitePayload = {
        method: {
          type: 'metro',
          metro_key: adminAreaResult.key,
          metro_name: adminAreaResult.name,
        },
      };
    } else if (siteSelectionMethod === 'county') {
      sitePayload = {
        method: {
          type: 'county',
          county_key: adminAreaResult.key,
          county_name: adminAreaResult.name,
        },
      };
    } else if (siteSelectionMethod === 'poi') {
      sitePayload = {
        method: {
          type: 'poi',
          radius: pointsRadius,
          points: points.map((point: any) => ({
            name: point.name,
            address: point.address,
            // coordinates: point.coordinates,
            lat: point.coordinates.lat,
            lng: point.coordinates.lng,
            radius: pointsRadius,
          })),
        },
      };
    }

    setSelectedSite(sitePayload);
    setShowSiteSelectionPanel(false);
    setShowFilterPanel(true);
  };

  return (
    <div
      style={{
        padding: '12px 32px',
        display: 'flex',
        flexDirection: 'row-reverse',
        borderTop: '1px solid #ccc',
      }}
    >
      <Button type="primary" disabled={disabled} onClick={saveSiteHandler}>
        Next
      </Button>
    </div>
  );
};
