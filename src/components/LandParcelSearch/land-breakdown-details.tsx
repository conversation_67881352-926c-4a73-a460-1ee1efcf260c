import { Button, Toolt<PERSON> } from 'antd';
import { useCallback, useState } from 'react';
import { FaRegCopy } from 'react-icons/fa';
import { LandParcelSearch } from './types/types';

const DetailValue = ({ value }: { value: string }) => {
  const [mouseOver, setMouseOver] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);

  const onClickHandler = useCallback(() => {
    navigator.clipboard.writeText(value);
    setShowTooltip(true);
    setTimeout(() => setShowTooltip(false), 1500);
  }, [value]);

  return (
    <div
      style={{
        borderRadius: '6px',
        display: 'inline-block',
        textAlign: 'right',
      }}
      onMouseEnter={() => setMouseOver(true)}
      onMouseLeave={() => setMouseOver(false)}
    >
      <Button
        type="link"
        style={{
          padding: 0,
          visibility:
            value && value.length > 0 && mouseOver ? 'visible' : 'hidden',
        }}
        size="small"
        onClick={onClickHandler}
      >
        <FaRegCopy />
      </Button>

      <Tooltip title="Copied to clipboard" open={showTooltip}>
        <span style={{ pointerEvents: 'none' }}>{value}</span>
      </Tooltip>
    </div>
  );
};

const keyFormatter = (key: string) => {
  if (key === 'sl_uuid') return 'Spatial Laser ID';
  if (key === 'source_url') return 'Source URL';

  return key
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

export const LandShowcaseDetails = ({ data }: { data: any }) => {
  if (!data) return null;
  return (
    <div className="p-4">
      {data.photo && (
        <div className="w-full h-[200px] border-[#ccc]">
          <img src={data.photo} className="w-full h-full object-cover" />
        </div>
      )}
      {Object.keys(data)
        .filter(
          (k) =>
            ![
              'photo',
              // 'source_url',
              // 'prices',
              // 'sizes',
              'geom_point',
              // 'space_uses',
            ].includes(k),
        )
        .sort()
        .map((key, idx) => (
          <div
            key={idx}
            className="flex flex-row justify-between border-b border-[#ccc] px-2 py-1"
          >
            <span>{keyFormatter(key)}</span>
            {['source_url', 'photo'].includes(key) ? (
              <Button
                className="px-0"
                type="link"
                href={data[key]}
                target="_blank"
              >
                Go to Link
              </Button>
            ) : (
              <DetailValue
                value={
                  typeof data[key] === 'object' && data[key] !== null
                    ? JSON.stringify(data[key])
                    : data[key]
                }
              />
            )}
          </div>
        ))}
    </div>
  );
};
