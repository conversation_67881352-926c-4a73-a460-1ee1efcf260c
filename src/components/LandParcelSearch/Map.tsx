import turf_bbox from '@turf/bbox';
import pointInPolygon from '@turf/boolean-point-in-polygon';
import turf_center from '@turf/center';
import { point } from '@turf/helpers';
import { message } from 'antd';
import isEqual from 'lodash.isequal';
import { AnyLayoutName, FilterOptions, Map } from 'mapbox-gl';
import moment, { MomentInput } from 'moment';
import { useCallback, useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import { FaExclamationTriangle } from 'react-icons/fa';
import { useLandParcelSearch, useResultDisplay, useSiteFilter } from '.';
// @ts-ignore
import landParcelMarkerImg from '../../assets/images/mapbox/marker/mapbox-marker-icon-20px-blue.png';
// @ts-ignore
import chainMarkerImg from '../../assets/images/mapbox/marker/mapbox-marker-icon-20px-orange.png';
// @ts-ignore
import poiMarkerImg from '../../assets/images/mapbox/marker/mapbox-marker-icon-20px-red.png';
import { geojsonTemplate } from '../../constants';
// @ts-ignore
import { GeometryCollection } from 'geojson';
import usePrevious from '../../hooks/usePrevious';
import {
  getAllDemographicsInSquareData,
  getClientAllowedAccessCoordinatesData,
  getMapboxReverseGeocodingData,
  getPOIChainLocationData,
} from '../../services/data';
import { getGeometryFromGeometryCollection } from './utils/conversions';

const safelySetMapLayoutProperty = (
  map: Map,
  layer: string,
  name: AnyLayoutName,
  value: any,
  options?: FilterOptions,
): Boolean => {
  if (!map) throw new Error('Map is not initialized');

  try {
    if (!map.getLayer(layer)) throw new Error(`Layer ${layer} does not exist`);

    if (map.getLayoutProperty(layer, name) === value) return true;
    map.setLayoutProperty(layer, name, value, options);
    return true;
  } catch (error) {
    console.log('Error setting map layout property: ', error);
    return false;
  }
};

// prettier-ignore
const safelySetMapZoomRange = (map:Map, layerId:string, minzoom:number, maxzoom:number) => {
  if (!map) throw new Error('Map is not initialized');
  try {
    if (!map.getLayer(layerId)) throw new Error(`Layer ${layerId} does not exist`);
    map.setLayerZoomRange(layerId, minzoom, maxzoom);
    return true;
  } catch (error) {
    console.log('Error setting map zoom range: ', error);
    return false;
  }
}

export const MapLayer = ({ map }: { map: Map }) => {
  const {
    showFilterPanel,
    siteSelectionMethod,
    viewOnMapDemographics,
    viewOnMapChainStores,
    showResultPanel,
  } = useLandParcelSearch();

  const { advancedDemographicsActive, chainStoresActive } = useSiteFilter();

  // prettier-ignore
  useEffect(() => {
    if (!map) return;

    // disable default red hovered map boundaries

    // map.setLayoutProperty('hoveredParcel', 'visibility', 'none');
    safelySetMapLayoutProperty(map, 'hoveredParcel', 'visibility', 'none');
    // disable map parcel dots/avm points
    safelySetMapLayoutProperty(map, 'parcelMapScopeLayer', 'visibility', 'none');
    safelySetMapLayoutProperty(map, 'parcelMapScopeLayerRentAVM', 'visibility', 'none');
    safelySetMapLayoutProperty(map, 'parcelMapScopeLayerRentNullAVM', 'visibility', 'none');
    safelySetMapZoomRange(map, 'parcelsStyle', 14, 21);
    safelySetMapZoomRange(map, 'parcelsFillStyle', 14, 21);
    
    if (!showResultPanel) {
      // disable default map white boundaries when result panel is not shown
      safelySetMapLayoutProperty(map, 'parcelsStyle', 'visibility', 'none');
      safelySetMapLayoutProperty(map, 'parcelsFillStyle', 'visibility', 'none');
    }

    map.resize();
    return () => {
      safelySetMapLayoutProperty(map, 'hoveredParcel', 'visibility', 'visible');
      safelySetMapLayoutProperty(map, 'parcelsStyle', 'visibility', 'visible');
      safelySetMapLayoutProperty(map, 'parcelsFillStyle', 'visibility', 'visible');
      safelySetMapLayoutProperty(map, 'parcelMapScopeLayer', 'visibility', 'visible');
      safelySetMapLayoutProperty(map, 'parcelMapScopeLayerRentAVM', 'visibility', 'visible');
      safelySetMapLayoutProperty(map, 'parcelMapScopeLayerRentNullAVM', 'visibility', 'visible');
      safelySetMapZoomRange(map, 'parcelsStyle', 15, 21);
      safelySetMapZoomRange(map, 'parcelsFillStyle', 15, 21);
    };
  }, [map, showFilterPanel, showResultPanel]);

  return (
    <>
      {/* {siteSelectionMethod === 'metro' && <MetroMap map={map} />} */}
      {['county', 'state', 'metro'].includes(siteSelectionMethod) && (
        <AdminAreaMap map={map} />
      )}
      {siteSelectionMethod === 'poi' && <POIMap map={map} />}
      {showFilterPanel &&
        advancedDemographicsActive &&
        viewOnMapDemographics && <AdvancedDemographicsMap map={map} />}
      {showFilterPanel && chainStoresActive && viewOnMapChainStores && (
        <ChainStoresMap map={map} />
      )}
      {showResultPanel && (
        <>
          <SearchResultsMap map={map} />
          <ParcelBoundarySelector map={map} />
          {createPortal(
            <ZoomProgressTooltip map={map} minZoom={14} />,
            document.getElementById('cma-map-container') as HTMLElement,
          )}
        </>
      )}
    </>
  );
};

const ZoomProgressTooltip = ({
  map,
  minZoom,
}: {
  map: Map;
  minZoom: number;
}) => {
  const [percent, setPercent] = useState<number | undefined>(undefined);

  useEffect(() => {
    if (!map) return;

    const zoomEnd = () => {
      const zoom = map.getZoom();
      if (zoom < minZoom) {
        setPercent(Math.round((zoom / minZoom) * 100));
      } else {
        setPercent(undefined);
      }
    };

    zoomEnd();
    map.on('zoom', zoomEnd);
    return () => {
      map.off('zoom', zoomEnd);
    };
  }, [map]);

  if (percent === undefined) return null;
  return (
    <div
      style={{
        position: 'absolute',
        top: '40px',
        left: '50%',
        transform: 'translateX(-50%)',
        borderRadius: '8px',
        overflow: 'hidden',
        background: 'white',
        boxShadow:
          '0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)',
      }}
    >
      <div style={{ display: 'flex', flexDirection: 'column' }}>
        <div
          style={{
            padding: '4px 8px',

            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            gap: '4px',
          }}
        >
          <FaExclamationTriangle color="#fdb100f5" size={18} />
          <span>Zoom in to see boundaries</span>
        </div>
        <div>
          <div
            style={{
              position: 'relative',
              width: '100%',
              height: '8px',
              background: '#e0e0e0',
            }}
          >
            <div
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: `${percent}%`,
                height: '100%',
                background: '#3e59fb',
                borderRadius: '4px 0 0 4px',
              }}
            ></div>
          </div>
        </div>
      </div>
    </div>
  );
};

const ParcelBoundarySelector = ({ map }: any) => {
  const [selectedGeoJSON, setSelectedGeoJSON] = useState<any>(geojsonTemplate);
  const { openDetails, setOpenDetails } = useResultDisplay();
  const {
    filteredResults,
    selectedResult,
    setSelectedResult,
    setParcelBreakDownId,
  } = useLandParcelSearch();

  const openDetailsRef = useRef(openDetails);
  openDetailsRef.current = openDetails;

  // parcelsFillStyle
  useEffect(() => {
    if (!map) return;

    const onLoad = () => {
      map.addSource('land-parcel-search-select-boundary', {
        type: 'geojson',
        data: geojsonTemplate,
      });

      map.addLayer({
        id: 'land-parcel-search-select-boundary-fill',
        type: 'fill',
        source: 'land-parcel-search-select-boundary',
        minzoom: 14,
        paint: {
          'fill-color': '#3e59fb',
          'fill-opacity': 0.25,
        },
      });

      map.addLayer({
        id: 'land-parcel-search-select-boundary-line',
        type: 'line',
        source: 'land-parcel-search-select-boundary',
        minzoom: 14,
        paint: {
          'line-color': '#3e59fb',
          'line-width': 4,
          'line-opacity': 1,
        },
      });
    };

    const onLoadSelectedBoundary = () => {
      map.addSource('land-parcel-search-selected-boundary', {
        type: 'geojson',
        data: geojsonTemplate,
      });

      map.addLayer(
        {
          id: 'land-parcel-search-selected-boundary-line',
          type: 'line',
          source: 'land-parcel-search-selected-boundary',
          minzoom: 14,
          paint: {
            'line-color': '#3e59fb',
            'line-width': 4,
            'line-opacity': 1,
          },
        },
        'land-parcel-search-marker',
      );

      map.addLayer(
        {
          id: 'land-parcel-search-selected-boundary-fill',
          type: 'fill',
          source: 'land-parcel-search-selected-boundary',
          minzoom: 14,
          paint: {
            'fill-color': '#3e59fb',
            'fill-opacity': 0.25,
          },
        },
        'land-parcel-search-marker',
      );
    };

    const mouseMove = (e: any) => {
      if (e.features && e.features.length > 0) {
        const { _x, _y, _z } = e.features[0];
        const geojson = e.features[0]._vectorTileFeature.toGeoJSON(_x, _y, _z);
        // console.log(geojson);

        const source = map.getSource('land-parcel-search-select-boundary');
        if (source) {
          source.setData(geojson);
        }
      }
    };

    const mouseLeave = () => {
      const source = map.getSource('land-parcel-search-select-boundary');
      if (source) {
        source.setData(geojsonTemplate);
      }
    };

    const mouseClick = (e: any) => {
      const feature = e.features[0];
      const { _x, _y, _z } = feature;
      const geojson = feature._vectorTileFeature.toGeoJSON(_x, _y, _z);
      console.log('map mouse click', geojson);
      setSelectedParcelAndBoundary(geojson);
    };

    const documentClick = (e: MouseEvent) => {
      if (!map) return;
      e.stopPropagation();
      const mapDimensions = map.getCanvas().getBoundingClientRect();
      const resultsListContainer = document
        .getElementById('land-parcel-result-list-container')
        ?.getBoundingClientRect();

      if (
        // !openDetails &&
        (e.clientX < mapDimensions.left ||
          e.clientX > mapDimensions.right ||
          e.clientY < mapDimensions.top ||
          e.clientY > mapDimensions.bottom) &&
        resultsListContainer &&
        (e.clientX < resultsListContainer.left ||
          e.clientX > resultsListContainer.right ||
          e.clientY < resultsListContainer.top ||
          e.clientY > resultsListContainer.bottom)
      ) {
        clearSelectedParcelAndBoundary();
      }
    };

    if (!map.getSource('land-parcel-search-select-boundary')) onLoad();
    if (!map.getSource('land-parcel-search-selected-boundary'))
      onLoadSelectedBoundary();

    map.on('mousemove', 'parcelsFillStyle', mouseMove);
    map.on('mouseleave', 'parcelsFillStyle', mouseLeave);
    map.on('click', 'parcelsFillStyle', mouseClick);
    document.addEventListener('click', documentClick);

    return () => {
      if (map.getSource('land-parcel-search-select-boundary')) {
        map.removeLayer('land-parcel-search-select-boundary-fill');
        map.removeLayer('land-parcel-search-select-boundary-line');
        map.removeSource('land-parcel-search-select-boundary');
      }
      if (map.getSource('land-parcel-search-selected-boundary')) {
        map.removeLayer('land-parcel-search-selected-boundary-line');
        map.removeLayer('land-parcel-search-selected-boundary-fill');
        map.removeSource('land-parcel-search-selected-boundary');
      }
      map.off('mousemove', 'parcelsFillStyle', mouseMove);
      map.off('mouseleave', 'parcelsFillStyle', mouseLeave);
      map.off('click', 'parcelsFillStyle', mouseClick);
    };
  }, [map, filteredResults]);

  useEffect(() => {
    if (!map || !selectedResult) return;

    // const geojson = {
    //   type: 'FeatureCollection',
    //   features: selectedResult
    //     ? [
    //         {
    //           type: 'Feature',
    //           geometry: selectedResult.boundary,
    //         },
    //       ]
    //     : [],
    // };

    // const source = map.getSource('land-parcel-search-selected-boundary');
    // if (source) {
    //   source.setData(geojson);
    // }

    map.flyTo({
      center: turf_center(selectedResult).geometry.coordinates,
      zoom: 15,
    });
  }, [map, selectedResult]);

  const setSelectedParcelAndBoundary = useCallback(
    (geojson) => {
      if (!map) return;
      const source = map.getSource('land-parcel-search-selected-boundary');
      if (source) {
        source.setData(geojson);
        // TODO: temporary solution - use ll_uuid to find the selected parcel
        let newSelectedResult = null;
        for (let i = 0; i < filteredResults.length; i++) {
          const p =
            filteredResults[i].geometry.type === 'GeometryCollection'
              ? getGeometryFromGeometryCollection({
                  type: 'Point',
                  geometryCollection: filteredResults[i]
                    .geometry as GeometryCollection,
                })
              : point(filteredResults[i].geometry.coordinates as number[]);

          if (pointInPolygon(p, geojson)) {
            newSelectedResult = filteredResults[i];
            break;
          }
        }
        setSelectedResult(newSelectedResult);
        // not found
        if (!newSelectedResult) {
          setOpenDetails(true);
          map.fitBounds(turf_bbox(geojson), { maxZoom: 15 });
          setParcelBreakDownId({
            boundary_id: geojson.properties.ll_uuid,
          });
        } else {
          // if (newSelectedResult) {
          setParcelBreakDownId({
            sl_uuid: newSelectedResult.properties.sl_uuid,
          });
        }
      }
    },
    [map, filteredResults],
  );

  const clearSelectedParcelAndBoundary = useCallback(() => {
    if (!map || openDetailsRef.current) return;
    const source = map.getSource('land-parcel-search-selected-boundary');
    if (source) {
      source.setData(geojsonTemplate);
      setSelectedResult(null);
      // setOpenDetails(false);
    }
  }, [map, openDetails]);

  return null;
};

let hoveredPolygonId: any = null;
const SearchResultsMap = ({ map }: any) => {
  const { filteredResults, selectedResult, setSelectedResult } =
    useLandParcelSearch();
  const { openDetails } = useResultDisplay();

  const selectedResultPrev = usePrevious(selectedResult);
  const filteredResultsPrev = usePrevious(filteredResults);

  const [mapClickSelectedResult, setMapClickSelectedResult] = useState<
    string | null
  >(null);

  useEffect(() => {
    if (!map) return;

    const loadMarkerImg = () => {
      const imgId = 'land-parcel-search-marker';
      map.loadImage(landParcelMarkerImg, (error: any, image: any) => {
        if (error) throw error;
        if (!map.hasImage(imgId)) {
          map.addImage(imgId, image);
        }
      });
      return imgId;
    };

    const onLoad = () => {
      map.addSource('land-parcel-search-results', {
        type: 'geojson',
        data: geojsonTemplate,
      });

      const imgId = loadMarkerImg();

      map.setLayoutProperty(
        'land-parcel-search-marker-layout',
        'icon-image',
        imgId,
      );

      map.addLayer({
        id: 'land-parcel-search-marker',
        type: 'symbol',
        source: 'land-parcel-search-results',
        layout: {
          'icon-image': imgId,
          'icon-size': 1,
          'icon-allow-overlap': true,
        },
      });
    };

    // const onLoadBoundary = () => {
    //   map.addSource('land-parcel-search-results-boundary', {
    //     type: 'geojson',
    //     data: geojsonTemplate,
    //     promoteId: 'apn',
    //   });

    //   map.addLayer(
    //     {
    //       id: 'land-parcel-search-results-boundary-line',
    //       type: 'line',
    //       source: 'land-parcel-search-results-boundary',
    //       paint: {
    //         'line-color': '#007cbf',
    //         'line-width': 4,
    //         'line-opacity': [
    //           'case',
    //           ['boolean', ['feature-state', 'hover'], false],
    //           1,
    //           0,
    //         ],
    //       },
    //     },
    //     'land-parcel-search-marker',
    //   );

    //   map.addLayer(
    //     {
    //       id: 'land-parcel-search-results-boundary-fill',
    //       type: 'fill',
    //       source: 'land-parcel-search-results-boundary',
    //       paint: {
    //         'fill-color': '#007cbf',
    //         'fill-opacity': [
    //           'case',
    //           ['boolean', ['feature-state', 'hover'], false],
    //           0.25,
    //           0,
    //         ],
    //       },
    //     },
    //     'land-parcel-search-marker',
    //   );
    // };

    const onLoadSelectedBoundary = () => {
      map.addSource('land-parcel-search-selected-results-boundary', {
        type: 'geojson',
        data: geojsonTemplate,
      });

      map.addLayer(
        {
          id: 'land-parcel-search-selected-results-boundary-line',
          type: 'line',
          source: 'land-parcel-search-selected-results-boundary',
          paint: {
            'line-color': '#3e59fb',
            'line-width': 4,
            'line-opacity': 1,
          },
        },
        'land-parcel-search-marker',
      );

      map.addLayer(
        {
          id: 'land-parcel-search-selected-results-boundary-fill',
          type: 'fill',
          source: 'land-parcel-search-selected-results-boundary',
          paint: {
            'fill-color': '#3e59fb',
            'fill-opacity': 0.25,
          },
        },
        'land-parcel-search-marker',
      );
    };

    if (!map.getSource('land-parcel-search-results')) onLoad();
    // if (!map.getSource('land-parcel-search-results-boundary')) onLoadBoundary();
    // if (!map.getSource('land-parcel-search-selected-results-boundary'))
    //   onLoadSelectedBoundary();

    const mouseMove = (e: any) => {
      if (e.features && e.features.length > 0) {
        if (hoveredPolygonId) {
          map.setFeatureState(
            {
              source: 'land-parcel-search-results-boundary',
              id: hoveredPolygonId,
            },
            { hover: false },
          );
        }
        hoveredPolygonId = e.features[0].id;
        map.setFeatureState(
          {
            source: 'land-parcel-search-results-boundary',
            id: hoveredPolygonId,
          },
          { hover: true },
        );
      }
    };

    const mouseLeave = () => {
      if (hoveredPolygonId) {
        const clearFeatureState = () => {
          map.setFeatureState(
            {
              source: 'land-parcel-search-results-boundary',
              id: hoveredPolygonId,
            },
            { hover: false },
          );
        };

        if (!map || !map.style || !map.style._loaded) {
          map.on('style.load', clearFeatureState);
        } else {
          clearFeatureState();
        }
      }
      hoveredPolygonId = null;
    };

    const mouseClick = (e: any) => {
      const feature = e.features[0];
      const { fips, apn } = feature.properties;
      setMapClickSelectedResult(feature.properties.sl_uuid);
    };

    map.on('style.load', loadMarkerImg);
    // map.on('mousemove', 'land-parcel-search-results-boundary-fill', mouseMove);
    // map.on(
    //   'mouseleave',
    //   'land-parcel-search-results-boundary-fill',
    //   mouseLeave,
    // );
    // map.on('click', 'land-parcel-search-results-boundary-fill', mouseClick);
    return () => {
      if (map.getSource('land-parcel-search-results')) {
        map.removeLayer('land-parcel-search-marker');
        map.removeSource('land-parcel-search-results');
      }
      if (map.getSource('land-parcel-search-selected-results-boundary')) {
        map.removeLayer('land-parcel-search-selected-results-boundary-line');
        map.removeLayer('land-parcel-search-selected-results-boundary-fill');
        map.removeSource('land-parcel-search-selected-results-boundary');
      }
    };
  }, [map]);

  useEffect(() => {
    if (!map || !mapClickSelectedResult) return;
    if (
      selectedResult &&
      selectedResult.properties.sl_uuid === mapClickSelectedResult
    )
      return;

    const result = filteredResults.find(
      (d: any) => d.properties.sl_uuid === mapClickSelectedResult,
    );

    if (result) {
      setSelectedResult(result);
    }
    setMapClickSelectedResult(null);
  }, [map, filteredResults, selectedResult, mapClickSelectedResult]);

  useEffect(() => {
    if (!map) return;
    if (isEqual(filteredResultsPrev, filteredResults)) return;

    // extract point from GeometryCollection
    const features = [];

    for (let i = 0; i < filteredResults.length; i++) {
      const p =
        filteredResults[i].geometry.type === 'GeometryCollection'
          ? getGeometryFromGeometryCollection({
              type: 'Point',
              geometryCollection: filteredResults[i]
                .geometry as GeometryCollection,
            })
          : filteredResults[i].geometry;

      features.push({
        type: 'Feature',
        geometry: p,
        properties: {
          ...filteredResults[i].properties,
        },
      });
    }

    const geojson = {
      type: 'FeatureCollection',
      features: features,
    };

    const source = map.getSource('land-parcel-search-results');
    if (source) {
      source.setData(geojson);
      if (filteredResults.length > 0 && selectedResult === null) {
        map.fitBounds(turf_bbox(geojson), { padding: 50, maxZoom: 12 });
      }
    }

    // const boundaryGeoJSON = {
    //   type: 'FeatureCollection',
    //   features: filteredResults.map((d: any) => ({
    //     type: 'Feature',
    //     geometry: d.boundary,
    //     properties: {
    //       fips: d.fips,
    //       apn: d.apn,
    //     },
    //   })),
    // };

    // const boundarySource = map.getSource('land-parcel-search-results-boundary');
    // if (boundarySource) {
    //   boundarySource.setData(boundaryGeoJSON);
    // }
  }, [map, filteredResults, selectedResult]);

  useEffect(() => {
    if (!map) return;

    // const geojson = {
    //   type: 'FeatureCollection',
    //   features: selectedResult
    //     ? [
    //         {
    //           type: 'Feature',
    //           geometry: selectedResult.boundary,
    //         },
    //       ]
    //     : [],
    // };

    // const source = map.getSource(
    //   'land-parcel-search-selected-results-boundary',
    // );
    // if (source) {
    //   source.setData(geojson);
    // }
  }, [map, selectedResult]);

  // useEffect(() => {
  //   if (!map || !selectedResult) return;
  //   if (isEqual(selectedResultPrev, selectedResult)) return;

  //   map
  //     .flyTo({ center: selectedResult.geog.coordinates, zoom: 14 })
  //     .once('moveend', () => {
  //       map.once('moveend', () => {
  //         if (!openDetails)
  //           setSelectedResult((prevState) =>
  //             isEqual(prevState, selectedResult) ? null : prevState,
  //           );
  //       });
  //     });
  // }, [map, selectedResult, openDetails]);

  return null;
};

const ChainStoresMap = ({ map }: any) => {
  const { filterPayload } = useLandParcelSearch();

  const [chainStores, setChainStores] = useState<any>([]);
  const [geojson, setGeojson] = useState<any>(geojsonTemplate);

  useEffect(() => {
    if (!map) return;

    const loadMarkerImg = () => {
      const imgId = 'land-parcel-search-chain-marker';
      map.loadImage(chainMarkerImg, (error: any, image: any) => {
        if (error) throw error;
        if (!map.hasImage(imgId)) {
          map.addImage(imgId, image);
        }
      });
      return imgId;
    };

    const onLoad = () => {
      map.addSource('land-parcel-chain-stores', {
        type: 'geojson',
        data: geojsonTemplate,
      });

      const imgId = loadMarkerImg();

      // map.setLayoutProperty(
      //   'land-parcel-search-chain-marker-layout',
      //   'icon-image',
      //   imgId,
      // );

      map.addLayer({
        id: 'land-parcel-search-chain-marker',
        type: 'symbol',
        source: 'land-parcel-chain-stores',
        layout: {
          'icon-image': imgId,
          'icon-size': [
            'interpolate',
            ['linear'],
            ['zoom'],
            3,
            0.1,
            7,
            0.5,
            12,
            1,
          ],
          'icon-allow-overlap': true,
        },
      });

      // map.addLayer({
      //   id: 'land-parcel-chain-stores-layer',
      //   type: 'circle',
      //   source: 'land-parcel-chain-stores',
      //   paint: {
      //     'circle-radius': 3,
      //     'circle-color': '#ff0000',
      //     'circle-opacity': 0.5,
      //   },
      // });
    };

    const moveEnd = () => {
      if (map.getZoom() < 9) return;
      if (filterPayload.chains.filters.length === 0) return;

      const getMapChainStores = async () => {
        const bounds = map.getBounds().toArray();
        const response = await getPOIChainLocationData({
          lng1: bounds[0][1],
          lat1: bounds[0][0],
          lng2: bounds[1][1],
          lat2: bounds[1][0],
          firstappeared: '1900-01-01',
          body: filterPayload.chains.filters.map((c: any) => c.chain_id),
        });

        if (response) {
          const filteredData = response.filter((d: any) => {
            const { chains } = filterPayload;

            const filter = chains.filters.find(
              (c: any) => c.chain_id === d.chain_id,
            );
            const filterDate = filter?.opened;
            if (!filterDate) return true;
            if (!d.first_appeared) return false;
            const fDater = moment(filterDate as MomentInput, 'YYYY-MM-DD');
            const cDate = moment(d.first_appeared, 'YYYY-MM-DD');

            return cDate.isAfter(fDater);
          });

          setChainStores(filteredData);
        } else {
          setChainStores([]);
        }
      };

      getMapChainStores();
    };

    if (!map.getSource('land-parcel-chain-stores')) {
      onLoad();
    }
    moveEnd();
    map.on('moveend', moveEnd);
    map.on('style.load', loadMarkerImg);

    return () => {
      if (map.getSource('land-parcel-chain-stores')) {
        map.removeLayer('land-parcel-search-chain-marker');
        map.removeSource('land-parcel-chain-stores');
      }
      map.off('moveend', moveEnd);
    };
  }, [map, filterPayload.chains]);

  useEffect(() => {
    if (!map) return;

    const geojson = generateGeojson(chainStores);

    const source = map.getSource('land-parcel-chain-stores');
    if (source) {
      source.setData(geojson);
    }
  }, [map, chainStores]);

  const generateGeojson = useCallback((data) => {
    const geojson = {
      type: 'FeatureCollection',
      features: data.map((d: any) => ({
        type: 'Feature',
        geometry: {
          type: 'Point',
          coordinates: d.geometry.coordinates,
        },
        properties: { ...d },
      })),
    };

    return geojson;
  }, []);

  return null;
};

const sourceId = 'land-parcel-advanced-heatmap';
const sourceLayer = 'BG-1nqtgi';
const sourceURL = 'mapbox://sxbxchen.2zcnf2qd';

const AdvancedDemographicsMap = ({ map }: any) => {
  const { filterPayload } = useLandParcelSearch();
  const [advancedDemographics, setAdvancedDemographics] = useState<any>([]);
  const prevPayload = usePrevious(filterPayload);

  useEffect(() => {
    if (!map) return;
    if (isEqual(prevPayload?.heatmap, filterPayload.heatmap)) return;

    const onLoad = () => {
      map.addSource(sourceId, {
        type: 'vector',
        url: sourceURL,
      });

      map.addLayer({
        id: 'land-parcel-advanced-heatmap-layer',
        type: 'fill',
        source: sourceId,
        'source-layer': sourceLayer,
        paint: {
          'fill-color': '#807dba',
          'fill-opacity': 0,
        },
      });

      map.addLayer({
        id: 'land-parcel-advanced-heatmap-layer-line',
        type: 'line',
        source: sourceId,
        'source-layer': sourceLayer,
        paint: {
          'line-color': '#807dba',
          'line-width': 2,
          'line-opacity': 0,
        },
      });
    };

    const moveEnd = () => {
      if (map.getZoom() < 9) return;
      if (filterPayload.heatmap.length === 0) return;

      const getMapDemographics = async () => {
        if (map.getZoom() < 9) return;
        const bounds = map.getBounds().toArray();
        const response = await getAllDemographicsInSquareData({
          lng1: bounds[0][0],
          lat1: bounds[0][1],
          lng2: bounds[1][0],
          lat2: bounds[1][1],
        });

        if (response) {
          setAdvancedDemographics(response);
        } else {
          setAdvancedDemographics([]);
        }
      };

      getMapDemographics();
    };

    if (!map.getSource(sourceId)) {
      onLoad();
    }
    moveEnd();
    map.on('moveend', moveEnd);
    return () => {
      if (map.getSource(sourceId)) {
        map.removeLayer('land-parcel-advanced-heatmap-layer');
        map.removeLayer('land-parcel-advanced-heatmap-layer-line');
        map.removeSource(sourceId);
      }
      map.off('moveend', moveEnd);
    };
  }, [map, filterPayload.heatmap]);

  useEffect(() => {
    if (!map) return;
    if (!filterPayload.heatmap) return;

    const filterFn = (d: any) => {
      const { heatmap } = filterPayload;

      const result = heatmap.every((h: any) => {
        const { type, min, max } = h;

        const value = d[type];

        if (min && max) {
          return value >= min && value <= max;
        } else if (min && !max) {
          return value >= min;
        } else if (!min && max) {
          return value <= max;
        }
        return false;
      });

      return result;
    };

    const expression: any = ['match', ['get', 'Name']];
    const expressionLine: any = ['match', ['get', 'Name']];

    for (let i = 0; i < advancedDemographics.length; i++) {
      const d = advancedDemographics[i];
      if (filterFn(d)) {
        expression.push(d.id, 0.25);
        expressionLine.push(d.id, 0.75);
      }
    }
    expression.push(0);
    expressionLine.push(0);

    map.setPaintProperty(
      'land-parcel-advanced-heatmap-layer',
      'fill-opacity',
      filterPayload.heatmap.length > 0 ? expression : 0.25,
    );
    map.setPaintProperty(
      'land-parcel-advanced-heatmap-layer-line',
      'line-opacity',
      filterPayload.heatmap.length > 0 ? expressionLine : 0.75,
    );
  }, [map, advancedDemographics, filterPayload.heatmap]);

  return null;
};

export const POIMap = ({ map }: any) => {
  const {
    pointsRadius,
    points,
    setPoints,
    showSiteSelectionPanel,
    selectedUserGroup,
  } = useLandParcelSearch();

  useEffect(() => {
    if (!map) return;

    const loadMarkerImg = () => {
      const imgId = 'land-parcel-search-poi-marker';
      map.loadImage(poiMarkerImg, (error: any, image: any) => {
        if (error) throw error;
        if (!map.hasImage(imgId)) {
          map.addImage(imgId, image);
        }
      });
      return imgId;
    };

    const onLoad = () => {
      map.addSource('land-parcel-search-poi', {
        type: 'geojson',
        data: generateGeojson(points), // whatever the initial data is
      });

      const imgId = loadMarkerImg();

      map.setLayoutProperty(
        'land-parcel-search-poi-marker-layout',
        'icon-image',
        imgId,
      );

      map.addLayer({
        id: 'land-parcel-search-poi-marker',
        type: 'symbol',
        source: 'land-parcel-search-poi',
        layout: {
          'icon-image': imgId,
          'icon-size': 1,
          'icon-allow-overlap': true,
        },
      });

      map.addLayer(
        {
          id: 'land-parcel-search-poi-radius',
          type: 'circle',
          source: 'land-parcel-search-poi',
          paint: {
            'circle-radius': {
              stops: [
                [0, 0],
                // metersToPixelsAtMaxZoom(
                //   pointsRadius* 1609.344,
                //   map.getCenter().lat,
                // ),
                [20, (pointsRadius * 1609.344) / 2],
              ],
              base: 2,
            },
            'circle-color': '#007cbf',
            'circle-opacity': 0.3,
            'circle-stroke-width': 1,
            'circle-stroke-color': '#007cbf',
            'circle-stroke-opacity': 0.5,
          },
        },
        'land-parcel-search-poi-marker',
      );
    };

    if (!map.getSource('land-parcel-search-poi')) {
      onLoad();
    }

    map.on('style.load', loadMarkerImg);
    return () => {
      if (map.getSource('land-parcel-search-poi')) {
        map.removeLayer('land-parcel-search-poi-marker');
        map.removeLayer('land-parcel-search-poi-radius');
        map.removeSource('land-parcel-search-poi');
      }
    };
  }, [map]);

  useEffect(() => {
    if (!map || !showSiteSelectionPanel) return;

    const click = async (e: any) => {
      if (points.length >= 10) return;

      const newPOI = {
        key: e.lngLat.toString(),
        name: null,
        address: '123 Test Street',
        coordinates: e.lngLat,
      };

      // if (
      //   selectedUserGroup &&
      //   ['Embry', 'Vertica'].includes(selectedUserGroup) &&
      //   process.env.UMI_APP_SERVER_TYPE === 'exp'
      // ) {
      const allowed = await getClientAllowedAccessCoordinatesData({
        coordinates: e.lngLat.toArray().reverse(),
      });

      if (!allowed) {
        message.error('You do not have access to this location');
        return;
      }
      // }

      const response = await getMapboxReverseGeocodingData(e.lngLat);

      const address = extractAddress(response);
      newPOI.address = address;
      setPoints((prev: any) => [...prev, newPOI]);
    };

    map.on('click', click);
    return () => {
      map.off('click', click);
    };
  }, [map, points, showSiteSelectionPanel, selectedUserGroup]);

  useEffect(() => {
    if (!map) return;

    const geojson = {
      type: 'FeatureCollection',
      features: points.map((point: any) => ({
        type: 'Feature',
        geometry: {
          type: 'Point',
          coordinates: [point.coordinates.lng, point.coordinates.lat],
        },
        properties: {
          title: point.name,
          description: point.address,
        },
      })),
    };

    const source = map.getSource('land-parcel-search-poi');
    if (source) {
      source.setData(geojson);
    }
  }, [map, points]);

  useEffect(() => {
    if (!map) return;

    const layer = map.getLayer('land-parcel-search-poi-radius');
    if (layer) {
      map.setPaintProperty('land-parcel-search-poi-radius', 'circle-radius', {
        stops: [
          [0, 0],
          // [20, (pointsRadius* 1609.344) / 2],
          [
            20,
            metersToPixelsAtMaxZoom(
              pointsRadius * 1609.344,
              map.getCenter().lat,
            ),
          ],
        ],
        base: 2,
      });
    }
  }, [map, pointsRadius]);

  const extractAddress = useCallback((data) => {
    if (!data || !data.features || !data.features.length) return null;
    const { features } = data;
    const address = features.filter((feat: any) => feat.id.includes('address'));
    if (!address || !address.length) return null;
    return address[0].place_name;
  }, []);

  const generateGeojson = useCallback((points) => {
    const geojson = {
      type: 'FeatureCollection',
      features: points.map((point: any) => ({
        type: 'Feature',
        geometry: {
          type: 'Point',
          coordinates: [point.coordinates.lng, point.coordinates.lat],
        },
        properties: {
          title: point.name,
          description: point.address,
        },
      })),
    };

    return geojson;
  }, []);

  const metersToPixelsAtMaxZoom = useCallback(
    (meters, latitude) => meters / 0.075 / Math.cos((latitude * Math.PI) / 180),
    [],
  );

  return null;
};

const AdminAreaMap = ({ map }: any) => {
  const { adminAreaResult, adminAreaMethod } = useLandParcelSearch();

  useEffect(() => {
    if (!map || !adminAreaResult) return;
    const geojson = {
      type: 'FeatureCollection',
      features: [
        {
          type: 'Feature',
          geometry: adminAreaResult.geom,
        },
      ],
    };

    const onLoad = () => {
      map.addSource('land-parcel-search-admin-area', {
        type: 'geojson',
        data: geojson,
      });

      // map.addLayer({
      //   id: 'land-parcel-search-metro-layer',
      //   type: 'fill',
      //   source: 'land-parcel-search-metro',
      //   paint: {
      //     'fill-color': '#007cbf',
      //     'fill-opacity': 0.3,
      //   },
      // });

      map.addLayer({
        id: 'land-parcel-search-admin-area-layer-line',
        type: 'line',
        source: 'land-parcel-search-admin-area',
        paint: {
          'line-color': '#de2d26',
          'line-width': 4,
          'line-opacity': 1,
        },
      });
    };

    if (!map.getSource('land-parcel-search-admin-area')) {
      onLoad();
    }

    if (adminAreaMethod === 'search') {
      map.fitBounds(turf_bbox(geojson));
    }

    return () => {
      if (map.getSource('land-parcel-search-admin-area')) {
        // map.removeLayer('land-parcel-search-admin-area-layer');
        map.removeLayer('land-parcel-search-admin-area-layer-line');
        map.removeSource('land-parcel-search-admin-area');
      }
    };
  }, [map, adminAreaResult]);

  return null;
};

const MetroMap = ({ map }: any) => {
  const { metroResult, metroMethod } = useLandParcelSearch();

  useEffect(() => {
    if (!map || !metroResult) return;
    const geojson = {
      type: 'FeatureCollection',
      features: [
        {
          type: 'Feature',
          geometry: metroResult.geom,
        },
      ],
    };

    const onLoad = () => {
      map.addSource('land-parcel-search-metro', {
        type: 'geojson',
        data: geojson,
      });

      // map.addLayer({
      //   id: 'land-parcel-search-metro-layer',
      //   type: 'fill',
      //   source: 'land-parcel-search-metro',
      //   paint: {
      //     'fill-color': '#007cbf',
      //     'fill-opacity': 0.3,
      //   },
      // });

      map.addLayer({
        id: 'land-parcel-search-metro-layer-line',
        type: 'line',
        source: 'land-parcel-search-metro',
        paint: {
          'line-color': '#de2d26',
          'line-width': 4,
          'line-opacity': 1,
        },
      });
    };

    if (!map.getSource('land-parcel-search-metro')) {
      onLoad();
    }

    if (metroMethod === 'search') {
      map.fitBounds(turf_bbox(geojson));
    }

    return () => {
      if (map.getSource('land-parcel-search-metro')) {
        // map.removeLayer('land-parcel-search-metro-layer');
        map.removeLayer('land-parcel-search-metro-layer-line');
        map.removeSource('land-parcel-search-metro');
      }
    };
  }, [map, metroResult]);

  return null;
};
