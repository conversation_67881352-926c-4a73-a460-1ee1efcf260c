import * as z from 'zod';

const geojsonPointSchema = z.object({
  type: z.literal('Point'),
  coordinates: z.number().array().length(2),
});

const geojsonMultiPointSchema = z.object({
  type: z.literal('MultiPoint'),
  coordinates: z.array(z.number().array().length(2)),
});

const geojsonLineStringSchema = z.object({
  type: z.literal('LineString'),
  coordinates: z.array(z.number().array().length(2)),
});

const geojsonMultiLineStringSchema = z.object({
  type: z.literal('MultiLineString'),
  coordinates: z.array(z.array(z.number().array().length(2))),
});

const geojsonPolygonSchema = z.object({
  type: z.literal('Polygon'),
  coordinates: z.array(z.array(z.array(z.number()))),
});

const geojsonMultiPolygonSchema = z.object({
  type: z.literal('MultiPolygon'),
  coordinates: z.array(z.array(z.array(z.array(z.number())))),
});

const geojsonGeometryCollectionSchema = z.object({
  type: z.literal('GeometryCollection'),
  geometries: z.array(
    z.union([
      geojsonPointSchema,
      geojsonMultiPointSchema,
      geojsonLineStringSchema,
      geojsonMultiLineStringSchema,
      geojsonPolygonSchema,
      geojsonMultiPolygonSchema,
    ]),
  ),
});

export const landParcelSchema = z.object({
  fips: z.string(),
  apn: z.string(),
  elem: z.number().int().nullable(),
  middle: z.number().int().nullable(),
  high: z.number().int().nullable(),
  street_number: z.string().nullable(),
  street_name: z.string().nullable(),
  street_suffix: z.string().nullable(),
  unit_type: z.string().nullable(),
  unit_number: z.string().nullable(),
  formatted_street_address: z.string().nullable(),
  city: z.string().nullable(),
  state: z.string().nullable(),
  zip_code: z.string().nullable(),
  latitude: z.number(),
  longitude: z.number(),
  geog: geojsonPointSchema,
  owner_name: z.string().nullable(),
  deed_owner_1: z.string().nullable(),
  owner_street_address: z.string().nullable(),
  owner_city: z.string().nullable(),
  owner_state: z.string().nullable(),
  owner_zip_code: z.string().nullable(),
  county_name: z.string().nullable(),
  cbsa_name: z.string().nullable(),
  improvement_value: z.number().nullable(),
  total_value: z.number().nullable(),
  improvement_ratio: z.number().nullable(),
  assessment_value_per_acre: z.number().nullable(),
  avg_school: z.number().nullable(),
  area_acres: z.number().nullable(),
  standardized_land_use_type: z.string().nullable(),
  zoning: z.string().nullable(),
  housing_value: z.number().nullable(),
  medianhhincome: z.number().nullable(),
  population: z.number().nullable(),
  block_group_id: z.string().nullable(),
  median_rent: z.number().nullable(),
  median_home_value: z.number().nullable(),
  nearest_highway_distance_miles: z.number().nullable(),
  nearest_powerline_distance_miles: z.number().nullable(),
  flood_coverage: z.number().nullable(),
  building_coverage: z.number().nullable(),
  municipality_jurisdiction: z.string().nullable(),
  zoning_type: z.string().nullable(),
  zoning_subtype: z.string().nullable(),
  mls_listing: z
    .object({
      mls_id: z.string().nullable(),
      status: z.string().nullable(),
      metro: z.string().nullable(),
    })
    .nullable(),
  boundary: z.union([
    geojsonPolygonSchema,
    geojsonMultiPolygonSchema,
    geojsonGeometryCollectionSchema,
    z.null(),
  ]),
});

export const LandParcelPropertiesSchema = z.object({
  sl_uuid: z.string(),
  fips: z.string(),
  apn: z.string(),
  sl_land_source: z.literal('parcel'),
  boundary_id: z.string(),

  elem: z.number().int().nullable(),
  middle: z.number().int().nullable(),
  high: z.number().int().nullable(),
  street_number: z.string().nullable(),
  street_name: z.string().nullable(),
  street_suffix: z.string().nullable(),
  unit_type: z.string().nullable(),
  unit_number: z.string().nullable(),
  formatted_street_address: z.string().nullable(),
  city: z.string().nullable(),
  state: z.string().nullable(),
  zip_code: z.string().nullable(),
  zip_plus_four_code: z.string().nullable(),
  census_tract: z.string().nullable(),
  carrier_code: z.string().nullable(),
  latitude: z.number(),
  longitude: z.number(),
  owner_name: z.string().nullable(),
  deed_owner_1: z.string().nullable(),
  owner_street_address: z.string().nullable(),
  owner_city: z.string().nullable(),
  owner_state: z.string().nullable(),
  owner_zip_code: z.string().nullable(),
  county_name: z.string().nullable(),
  cbsa_name: z.string().nullable(),
  improvement_value: z.number().nullable(),
  total_value: z.number().nullable(),
  improvement_ratio: z.number().nullable(),
  assessment_value_per_acre: z.number().nullable(),
  avg_school: z.number().nullable(),
  area_acres: z.number().nullable(),
  standardized_land_use_type: z.string().nullable(),
  standardized_land_use_category: z.string().nullable(),
  zoning: z.string().nullable(),
  housing_value: z.number().nullable(),
  medianhhincome: z.number().nullable(),
  population: z.number().nullable(),
  block_group_id: z.string().nullable(),
  median_rent: z.number().nullable(),
  median_home_value: z.number().nullable(),
  nearest_highway_distance_miles: z.number().nullable(),
  nearest_powerline_distance_miles: z.number().nullable(),
  flood_coverage: z.number().nullable(),
  building_coverage: z.number().nullable(),
  municipality_jurisdiction: z.string().nullable(),
  zoning_type: z.string().nullable(),
  zoning_subtype: z.string().nullable(),
  mean_slope: z.coerce.number().nullable(),
  mean_aspect: z.coerce.number().nullable(),
});

export const LandListingMLSPropertiesSchema = z.object({
  sl_uuid: z.string(),
  metro: z.string(),
  mls_id: z.any(),
  sl_land_source: z.literal('mls'),
  boundary_id: z.string().nullable(),

  avg_school: z.number().nullable(),
  cdom: z.number().nullable(),
  city: z.string().nullable(),
  close_date: z.string().nullable(),
  close_price: z.number().nullable(),
  contract_date: z.string().nullable(),
  county: z.string().nullable(),
  current_price: z.number().nullable(),
  dom: z.number().nullable(),
  first_entry_timestamp: z.string().nullable(),
  full_address: z.string().nullable(),
  housing_value: z.number().nullable(),
  latitude: z.number().nullable(),
  listing_key: z.string().nullable(),
  longitude: z.number().nullable(),
  lot_size: z.number().nullable(),
  median_home_value: z.number().nullable(),
  median_rent: z.number().nullable(),
  medianhhincome: z.number().nullable(),
  modification_timestamp: z.string().nullable(),
  nearest_highway_distance_miles: z.number().nullable(),
  nearest_powerline_distance_miles: z.number().nullable(),
  nearest_railway_distance_miles: z.number().nullable(),
  off_market_date: z.string().nullable(),
  original_price: z.number().nullable(),
  owner_name: z.string().nullable(),
  placekey: z.string().nullable(),
  population: z.number().nullable(),
  price_change: z.string().nullable(),
  price_change_history: z
    .array(
      z.object({
        price_change: z.string().nullable().optional(),
        price_change_timestamp: z.string().nullable().optional(),
        price: z.number().nullable().optional(),
      }),
    )
    .nullable(),
  price_change_timestamp: z.string().nullable(),
  property_subtype: z.string().nullable(),
  property_type: z.string().nullable(),
  special_listing_conditions: z.string().nullable(),
  state_or_province: z.string().nullable(),
  status: z.string().nullable(),
  status_change_history: z.array(z.any()).nullable(),
  status_change_timestamp: z.string().nullable(),
  subdivision: z.string().nullable(),
  water_source: z.string().nullable(),
  year_built: z.number().nullable(),
  zipcode: z.string().nullable(),
  zoning: z.string().nullable(),
  zoning_subtype: z.string().nullable(),
  zoning_type: z.string().nullable(),
});

export const LandListingShowcasePropertiesSchema = z.object({
  sl_uuid: z.string(),
  base_id: z.string(),
  sl_land_source: z.literal('showcase'),
  boundary_id: z.string().nullable(),

  address: z.string().nullable(),
  address_cc: z.string().nullable(),
  address_city: z.string().nullable(),
  address_post: z.string().nullable(),
  address_sc: z.string().nullable(),
  address_street_dir: z.string().nullable(),
  address_street_name: z.string().nullable(),
  address_street_num: z.string().nullable(),
  address_street_type: z.string().nullable(),
  available_date: z.string().nullable(),
  avg_school: z.number().nullable(),
  building_area: z.string().nullable(),
  cap_rate: z.number().nullable(),
  category: z.string().nullable(),
  first_seen: z.string().nullable(),
  for_type: z.string().nullable(),
  housing_value: z.number().nullable(),
  last_seen: z.string().nullable(),
  listing_tier: z.string().nullable(),
  matterport: z.boolean().nullable(),
  max_rent: z.number().nullable(),
  max_space: z.number().nullable(),
  median_home_value: z.number().nullable(),
  median_rent: z.number().nullable(),
  medianhhincome: z.number().nullable(),
  min_rent: z.number().nullable(),
  min_space: z.number().nullable(),
  multi_family_sub_market: z.string().nullable(),
  nearest_highway_distance_miles: z.number().nullable(),
  nearest_powerline_distance_miles: z.number().nullable(),
  nearest_railway_distance_miles: z.number().nullable(),
  owner_name: z.string().nullable(),
  parcel_id: z.number().nullable(),
  photo: z.string().nullable(),
  population: z.number().nullable(),
  prices: z
    .object({
      dailyPrice: z.string().nullable().optional(),
      dailyPriceSF: z.string().nullable().optional(),
      monthlyPrice: z.string().nullable().optional(),
      monthlyPriceSF: z.string().nullable().optional(),
      yearlyPrice: z.string().nullable().optional(),
      yearlyPriceSF: z.string().nullable().optional(),
    })
    .nullable(),
  prop_id: z.number().nullable(),
  prop_type: z.string().nullable(),
  rent_basic: z.number().nullable(),
  research_market_id: z.number().nullable(),
  sale_price: z.number().nullable(),
  sizes: z.any().nullable(),
  source_url: z.string().nullable(),
  space_uses: z.array(z.string()).nullable(),
  spaces: z.number().nullable(),
  sub_type: z.string().nullable(),
  zoning: z.string().nullable(),
  zoning_subtype: z.string().nullable(),
  zoning_type: z.string().nullable(),
});

export const LandFeatureSchema = z.object({
  type: z.literal('Feature'),
  geometry: z.union([geojsonPointSchema, geojsonGeometryCollectionSchema]),
  properties: z.discriminatedUnion('sl_land_source', [
    LandParcelPropertiesSchema,
    LandListingMLSPropertiesSchema,
    LandListingShowcasePropertiesSchema,
  ]),
});

export const LandFeatureBoundarySchema = z.object({
  sl_uuid: z.string(),
  boundary: z.union([
    geojsonPolygonSchema,
    geojsonMultiPolygonSchema,
    geojsonGeometryCollectionSchema,
  ]),
});

export interface LandFeature extends z.infer<typeof LandFeatureSchema> {}

// Type guards
export function isLandParcelProperties(
  properties: LandFeature['properties'],
): properties is z.infer<typeof LandParcelPropertiesSchema> {
  return properties.sl_land_source === 'parcel';
}

export function isLandListingMLSProperties(
  properties: LandFeature['properties'],
): properties is z.infer<typeof LandListingMLSPropertiesSchema> {
  return properties.sl_land_source === 'mls';
}

export function isLandListingShowcaseProperties(
  properties: LandFeature['properties'],
): properties is z.infer<typeof LandListingShowcasePropertiesSchema> {
  return properties.sl_land_source === 'showcase';
}

export const savedResultSchema = z.array(
  z.object({
    sl_uuid: z.string(),
  }),
);
