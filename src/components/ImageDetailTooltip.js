import { useState, useEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'umi';
import { Tooltip, Spin } from 'antd';
import isEmpty from 'lodash.isempty';
import { getMetroNameForParam } from '@/utils/geography';

const ImageDetailTooltip = ({
  mlsProperty,
  children,
  customContainerRef = null,
}) => {
  const containerRef = useRef(null);
  const [imageURL, setImageURL] = useState(null);
  const [open, setOpen] = useState(false);

  const dispatch = useDispatch();

  const lastKeyRef = useRef(null);

  const fetchImage = () => {
    if (!mlsProperty || isEmpty(mlsProperty)) return;

    let key = mlsProperty.listingkey;
    if (
      ['sanantonio', 'san antonio'].includes(mlsProperty.metro.toLowerCase()) ||
      getMetroNameForParam(mlsProperty, true) === 'sanantonio'
    ) {
      key = mlsProperty.mlsid;
    }

    setOpen(true);
    if (lastKeyRef.current === key) {
      return;
    }

    dispatch({
      type: 'CMA/getMLSPopupImages',
      payload: {
        key: key,
        city: getMetroNameForParam(mlsProperty, true), // true for using realtrac instead of nashville
      },
    }).then((imageURL) => {
      setImageURL(imageURL);
      lastKeyRef.current = key;
    });
  };

  useEffect(() => {
    if (open) {
      fetchImage();
    }
  }, [mlsProperty]);

  const onMouseEnter = () => {
    fetchImage();
  };

  if (imageURL === '') {
    return <Tooltip title="View Images and House Details">{children}</Tooltip>;
  }

  return (
    <div
      className="image-detail-tooltip-container"
      ref={containerRef}
      style={{ display: 'inline' }}
      onMouseEnter={onMouseEnter}
      onMouseLeave={() => setOpen(false)}
    >
      <Tooltip
        open={open}
        // placement={placement}
        title={imageURL ? <img src={imageURL} /> : <Spin />}
        getPopupContainer={() =>
          customContainerRef ? customContainerRef.current : containerRef.current
        }
      >
        {children}
      </Tooltip>
    </div>
  );
};

export default ImageDetailTooltip;
