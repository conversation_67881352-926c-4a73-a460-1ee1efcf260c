.popupContainer {
  max-width: 240px;
  display: flex;
  flex-direction: column;
}

.popupContent {
  display: flex;
  flex-direction: column;
  /* gap: 10px; */
  /* gap: 4px; */
  gap: 4px;
  padding: 10px;
  font-size: 11px !important;
}

.MLS_popupImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.popupStat,
.popupAddress {
  display: flex;
  flex-direction: column;
}

.popupStatOther {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  gap: 2px;
}

.popupStat p,
.popupStat span,
.popupAddress p,
.popupStatOther span {
  margin: 0;
  padding: 0;
  line-height: 14px;
}

.popupValue {
  /* font-size: 13px; */
  font-size: 12px !important;
  font-weight: 500;
}

.popupDotContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.locatePropertyButton {
  width: 26px;
  height: 26px;
  border-radius: 50%;
  border: none;
  background: transparent;
  background-color: rgb(76, 187, 23);
  cursor: pointer;
  position: relative;
}
.locatePropertyButton:hover {
  background-color: rgb(76, 187, 23, 0.8);
}
.locatePropertyButton svg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  pointer-events: none;
}
.removePropertyButton {
  width: 26px;
  height: 26px;
  border-radius: 50%;
  border: none;
  background: transparent;
  background-color: red;
  cursor: pointer;
  position: relative;
}
.removePropertyButton:hover {
  background-color: rgba(255, 0, 0, 0.8);
}
.removePropertyButton svg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  pointer-events: none;
}
