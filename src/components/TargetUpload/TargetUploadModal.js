import { DownloadOutlined, LoadingOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON><PERSON>, Spin } from 'antd';
import React, { useEffect, useState } from 'react';
import { connect } from 'umi';
import { postTargetsUploadCSV } from '../../services/data';
import CSVUploader from './CSVUploader';
import ResultPage from './ResultPage';
import TargetUploadTable from './TargetUploadTable';
import style from './targetUploadModal.css';

const TargetUploadModal = (props) => {
  const [showSuccessPage, setShowSuccessPage] = useState(false);
  const [readyToUpload, setReadyToUpload] = useState(false);
  const [requestSent, setRequestSent] = useState(false);
  const closeModal = () => {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        showTargetsUploadModal: false,
        uploadSuccess: false,
        targetsCsvFile: File,
        targetsParsedData: {},
      },
    });
  };

  function setSuccess() {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        uploadSuccess: true,
      },
    });
  }
  useEffect(() => {
    console.log(
      'test target ue to enable upload button',
      props.targetsParsedData,
    );
    if (Object.keys(props.targetsParsedData).length > 0) {
      setReadyToUpload(true);
    } else {
      setReadyToUpload(false);
    }
  }, [props.targetsCsvFile, props.targetsParsedData]);

  const upload = async () => {
    //TODO: add upload post method
    setReadyToUpload(false);
    setRequestSent(true);
    if (!props.targetsCsvFile) {
      console.log('test No CSV file to upload', props.targetsCsvFile);
      return;
    }

    const formData = new FormData();
    formData.append('file', props.targetsCsvFile);

    try {
      await postTargetsUploadCSV({ body: formData }).then((res) => {
        if (res === 'CSV file uploaded and processed successfully') {
          setSuccess();
          console.log('test upload success', props.targetsCsvFile);
          setRequestSent(false);
        }
      });
    } catch (error) {
      console.log('test upload error', error);
      setReadyToUpload(true);
      setRequestSent(true);
    }
    console.log('test upload', props.uploadSuccess);
  };
  useEffect(() => {
    if (props.uploadSuccess) {
      setShowSuccessPage(true);
      console.log('test ue to true', showSuccessPage);
    } else {
      setShowSuccessPage(false);
      console.log('test ue to false', showSuccessPage);
    }
  }, [props.uploadSuccess]);
  return (
    <Modal
      open={props.showTargetsUploadModal}
      closable={true}
      maskClosable={true}
      onCancel={closeModal}
      centered={true}
      footer={null}
      width={'calc(100% - 64px)'}
      style={{
        backgroundColor: 'rgba(253,253,253,0.92)',
        height: 'calc(100vh - 64px)',
        zIndex: 999,
      }}
      styles={{
        body: { height: 'calc(100vh - 64px)' },
      }}
    >
      {!showSuccessPage ? (
        <>
          <p className={style.modalTitle}>Targets Upload Panel</p>
          <div className={style.modalPanel}>
            {/* LEFT */}
            <div>
              <p>
                Plese choose a CSV file to start. To minimize errors, please
                make sure your CSV file follows the format of our template.
              </p>
              <Button type="primary" icon={<DownloadOutlined />}>
                Download CSV Template
              </Button>
              <div className={style.uploadWidget}>
                <CSVUploader />
              </div>

              <Button type="primary" onClick={upload} disabled={!readyToUpload}>
                Upload Data
              </Button>
              {requestSent && <p>Uploading...</p>}
            </div>
            {/* RIGHT */}
            <div className={style.tableDiv}>
              <TargetUploadTable />
            </div>
          </div>
        </>
      ) : (
        <ResultPage />
      )}
    </Modal>
  );
};

export default connect(({ CMA }) => ({
  showTargetsUploadModal: CMA.showTargetsUploadModal,
  targetsParsedData: CMA.targetsParsedData,
  uploadSuccess: CMA.uploadSuccess,
  targetsCsvFile: CMA.targetsCsvFile,
}))(TargetUploadModal);

const antIcon = (
  <LoadingOutlined
    style={{
      fontSize: 24,
    }}
    spin
  />
);
const SpinningLoading = () => <Spin indicator={antIcon} />;
