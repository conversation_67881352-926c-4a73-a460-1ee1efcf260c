import React, { useState } from 'react';
import { Progress } from 'antd';
import { InboxOutlined } from '@ant-design/icons';
import { message, Upload } from 'antd';
import { connect } from 'umi';
import <PERSON> from 'papaparse';

const { Dragger } = Upload;

const CSVUploader = (props) => {
  const setParsedData = (data) => {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        targetsParsedData: data,
      },
    });
  };
  const [uploadInfo, setUploadInfo] = useState();
  const [percent, setPercent] = useState(0);
  const parseProps = {
    name: 'file',
    multiple: false,
    accept: '.csv',
    // action: 'https://www.mocky.io/v2/5cc8019d300000980a055e76',
    onChange(info) {
      const { status } = info.file;
      setUploadInfo(info);
      setPercent(info.file.percent);
      if (status !== 'uploading') {
        console.log('test', info.file.status);
        console.log('test', info.file);
      }
      if (status === 'done') {
        message.success(
          `${info.file.name} (${(info.file.size / 1024).toFixed(
            2,
          )} KB) file uploaded successfully.`,
        );
        Papa.parse(info.file.originFileObj, {
          header: true,
          dynamicTyping: true,
          skipEmptyLines: true,
          complete: function (results) {
            console.log('test result', results);
            console.log('test Finished', results.data);
            setParsedData(results.data);
          },
        });
        props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            targetsCsvFile: info.file.originFileObj,
          },
        });
      } else if (status === 'error') {
        message.error(`${info.file.name} file upload failed.`);
      }
    },
    onDrop(e) {
      console.log('Dropped files', e.dataTransfer.files);
    },
  };
  return (
    <div>
      <Dragger {...parseProps}>
        <p className="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p className="ant-upload-text">
          Click or drag file to this area to upload
        </p>
        <p className="ant-upload-hint">Single file supported ONLY.</p>
        <p className="ant-upload-hint">Supported formats: .csv</p>
      </Dragger>
      {uploadInfo && uploadInfo.file && <Progress percent={percent} />}
    </div>
  );
};

export default connect(({ CMA }) => ({
  showTargetsUploadModal: CMA.showTargetsUploadModal,
  targetsParsedData: CMA.targetsParsedData,
  targetsCsvFile: CMA.targetsCsvFile,
}))(CSVUploader);
