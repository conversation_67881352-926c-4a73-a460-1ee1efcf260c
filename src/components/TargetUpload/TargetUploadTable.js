import { Table } from 'antd';
import React, { useState, useEffect } from 'react';
import { connect } from 'umi';

function convertObjectKeys(inputObject, idx) {
  const convertKey = (key) => {
    // Convert key to lower case and replace specific characters or patterns
    return (
      key
        .toLowerCase()
        .replace('?', '')
        .replace('# ', '')
        .replace(' ', '')
        .replace('fcflag', 'fcFlag')
        .replace('lastofferdate', 'lastOfferDate')
        .replace('offerssent', '#offersSent')
        .replace('psassigned', '#psasSigned')
        .replace('ownerocc', 'ownerOcc')
        .replace('thvalue', 'thValue')
        .replace('threntavm', 'thRentAvm')
        .replace('seedaddress', 'seedAddress')
        .replace('accountid', 'accountId')
        // Additional rules for camelCase conversion might be added here
        .replace(/([a-z0-9])([A-Z])/g, '$1$2')
        .replace(/(\s+|_)/g, '')
    );
  };

  const outputObject = { key: idx };
  for (const key in inputObject) {
    const newKey = convertKey(key);
    outputObject[newKey] = inputObject[key];
  }

  return outputObject;
}

const columns = [
  {
    title: 'fips',
    width: 100,
    dataIndex: 'fips',
    key: 'fips',
    fixed: 'left',
  },
  {
    title: 'Address',
    width: 150,
    dataIndex: 'address',
    key: 'address',
  },

  {
    title: 'City',
    width: 100,
    dataIndex: 'city',
    key: 'city',
  },
  {
    title: 'State',
    width: 100,
    dataIndex: 'state',
    key: 'state',
  },
  {
    title: 'Zip',
    width: 100,
    dataIndex: 'zip',
    key: 'zip',
  },
  {
    title: 'Target?',
    width: 80,
    dataIndex: 'target?',
    key: 'target',
  },
  {
    title: 'Zillow',
    width: 80,
    dataIndex: 'zillow',
    key: 'zillow',
  },
  {
    title: 'Google',
    width: 100,
    dataIndex: 'google',
    key: 'google',
  },
  {
    title: 'Prior Opps',
    width: 100,
    dataIndex: 'priorOpps',
    key: 'prioropps',
  },
  {
    title: 'Last Offer Date',
    width: 100,
    dataIndex: 'lastOfferDate',
    key: 'lastofferdate',
  },
  {
    title: '# Offers Sent',
    width: 100,
    dataIndex: 'offersSent',
    key: 'offerssent',
  },
  {
    title: '# PSAs Signed',
    width: 100,
    dataIndex: 'psasSigned',
    key: 'psassigned',
  },
  {
    title: 'Status',
    width: 100,
    dataIndex: 'status',
    key: 'status',
  },

  {
    title: 'Beds',
    width: 100,
    dataIndex: 'beds',
    key: 'beds',
  },
  {
    title: 'Baths',
    width: 100,
    dataIndex: 'baths',
    key: 'baths',
  },
  {
    title: 'Vintage',
    width: 100,
    dataIndex: 'vintage',
    key: 'vintage',
  },
  {
    title: 'Owner Occ',
    width: 100,
    dataIndex: 'ownerOcc',
    key: 'ownerocc',
  },
  {
    title: 'FC Flag',
    width: 100,
    dataIndex: 'fcFlag',
    key: 'fcflag',
  },
  {
    title: 'TH Value',
    width: 100,
    dataIndex: 'thValue',
    key: 'thvalue',
  },
  {
    title: 'TH Rent AVM',
    width: 100,
    dataIndex: 'thRentAvm',
    key: 'threntavm',
  },
  {
    title: 'SEED ADDRESS',
    width: 100,
    dataIndex: 'seedAddress',
    key: 'seedaddress',
  },
  {
    title: 'METHODOLOGY',
    width: 100,
    dataIndex: 'methodology',
    key: 'methodology',
  },
  {
    title: 'apn',
    width: 100,
    dataIndex: 'apn',
    key: 'apn',
  },
  {
    title: 'ACCOUNT ID',
    width: 100,
    dataIndex: 'accountId',
    key: 'accountid',
  },
];

// for (let i = 0; i < 100; i++) {
//   data.push({
//     key: i,
//     name: `Edrward ${i}`,
//     age: 32,
//     address: `London Park no. ${i}`,
//   });
// }
const TargetUploadTable = (props) => {
  const [data, setData] = useState([]);

  useEffect(() => {
    console.log('test parsed Data', props.targetsParsedData);
    let temp = [];
    if (props.targetsParsedData.length > 0) {
      props.targetsParsedData.forEach((element, idx) => {
        temp.push(convertObjectKeys(element, idx));
      });
    }
    setData(temp);
  }, [props.targetsParsedData]);

  return (
    <Table
      columns={columns}
      dataSource={data}
      scroll={{
        x: 450,
        y: 700,
      }}
    />
  );
};

export default connect(({ CMA }) => ({
  showTargetsUploadModal: CMA.showTargetsUploadModal,
  targetsParsedData: CMA.targetsParsedData,
}))(TargetUploadTable);
