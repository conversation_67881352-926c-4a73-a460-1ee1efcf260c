import { Button, Result } from 'antd';
import React from 'react';
import { connect } from 'umi';
const ResultPage = (props) => {
  const closeModalAndReset = () => {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        showTargetsUploadModal: false,
        uploadSuccess: false,
        targetsCsvFile: File,
        targetsParsedData: {},
      },
    });
  };

  return (
    <Result
      status="success"
      title="Successfully Upload the targets data"
      subTitle=""
      extra={[
        <Button type="primary" key="close" onClick={closeModalAndReset}>
          Close
        </Button>,
      ]}
    />
  );
};

export default connect(({ CMA }) => ({
  showTargetsUploadModal: CMA.showTargetsUploadModal,
  targetsParsedData: CMA.targetsParsedData,
  uploadSuccess: CMA.uploadSuccess,
  targetsCsvFile: CMA.targetsCsvFile,
}))(ResultPage);
