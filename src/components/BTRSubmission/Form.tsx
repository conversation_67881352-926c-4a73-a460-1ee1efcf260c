import { MAPBOX_TOKEN } from '@/constants';
import { AimOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Form, Input, Select, Space, message } from 'antd';
import { isEmpty } from 'lodash';
import React, { useState } from 'react';
import { useSelector } from 'umi';

const { Option } = Select;

// You should store this in an environment variable

const SubmissionForm = ({ onSubmit, loading, setFormRef }) => {
  const [form] = Form.useForm();
  React.useEffect(() => {
    if (setFormRef) {
      // Wrap the form instance to add reset handler
      const formWithReset = {
        ...form,
        resetFields: (...args) => {
          setAddressTyped(false);
          return form.resetFields(...args);
        },
      };
      setFormRef(formWithReset);
    }
    return () => setFormRef?.(null);
  }, [form, setFormRef]);

  const [addressTyped, setAddressTyped] = useState(false);
  const [geocodingLoading, setGeocodingLoading] = useState(false);

  const currentPropertyAddress = useSelector(
    (state: any) => state.CMA.currentPropertyAddress,
  );
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      await onSubmit(values);
      form.resetFields();
    } catch (error) {
      console.error('Error validating form:', error);
    }
  };

  const getCoordinatesFromAddress = async (address) => {
    try {
      setGeocodingLoading(true);
      const encodedAddress = encodeURIComponent(address);
      const response = await fetch(
        `https://api.mapbox.com/search/geocode/v6/forward?q=${encodedAddress}&access_token=${MAPBOX_TOKEN}`,
      );

      if (!response.ok) {
        throw new Error('Geocoding failed');
      }

      const data = await response.json();

      if (data.features && data.features.length > 0) {
        const [lng, lat] = data.features[0].geometry.coordinates;
        form.setFieldsValue({
          lat,
          lng,
        });
        message.success('Coordinates found successfully');
      } else {
        message.error('No coordinates found for this address');
      }
    } catch (error) {
      console.error('Error getting coordinates:', error);
      message.error('Failed to get coordinates');
    } finally {
      setGeocodingLoading(false);
    }
  };

  const handleLocationButton = async () => {
    if (addressTyped) {
      // Get coordinates from typed address
      const address = form.getFieldValue('address');
      if (address) {
        await getCoordinatesFromAddress(address);
      }
    } else {
      // Use current property address
      if (currentPropertyAddress) {
        form.setFieldsValue({
          lat: currentPropertyAddress.latitude,
          lng: currentPropertyAddress.longitude,
          address: currentPropertyAddress.fullAddress,
        });
      }
    }
  };

  const handleAddressChange = (e) => {
    setAddressTyped(e.target.value.length > 0);
  };
  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={onSubmit}
      initialValues={{
        status: 'existing',
      }}
    >
      <Form.Item
        name="status"
        label="Status"
        rules={[{ required: true, message: 'Please select a status' }]}
      >
        <Select>
          <Option value="existing">Existing</Option>
          <Option value="future">Future</Option>
        </Select>
      </Form.Item>

      <Space direction="vertical" style={{ width: '100%', marginBottom: 24 }}>
        <Button
          onClick={handleLocationButton}
          disabled={isEmpty(currentPropertyAddress) && !addressTyped}
          icon={addressTyped ? <SearchOutlined /> : <AimOutlined />}
          loading={geocodingLoading}
        >
          {addressTyped
            ? 'Get coordinate from address'
            : 'Use Selected Location'}
        </Button>

        <Form.Item
          name="address"
          label="Address"
          rules={[{ required: true, message: 'Please enter an address' }]}
          style={{ marginBottom: 0 }}
        >
          <Input placeholder="Enter address" onChange={handleAddressChange} />
        </Form.Item>

        <Space style={{ width: '100%' }}>
          <Form.Item
            name="lat"
            label="Latitude"
            rules={[{ required: true, message: 'Please enter latitude' }]}
            style={{ marginBottom: 0 }}
          >
            <Input type="number" step="any" placeholder="Enter latitude" />
          </Form.Item>

          <Form.Item
            name="lng"
            label="Longitude"
            rules={[{ required: true, message: 'Please enter longitude' }]}
            style={{ marginBottom: 0 }}
          >
            <Input type="number" step="any" placeholder="Enter longitude" />
          </Form.Item>
        </Space>
      </Space>

      <Form.Item
        name="builder"
        label="Builder"
        rules={[{ required: true, message: 'Please enter builder name' }]}
      >
        <Input placeholder="Enter builder name" />
      </Form.Item>

      <Form.Item
        name="owner"
        label="Owner"
        rules={[{ required: true, message: 'Please enter owner name' }]}
      >
        <Input placeholder="Enter owner name" />
      </Form.Item>

      <Form.Item name="note" label="Note">
        <Input.TextArea rows={4} placeholder="Enter notes" />
      </Form.Item>

      <Form.Item
        name="url"
        label="URL"
        rules={[{ type: 'url', message: 'Please enter a valid URL' }]}
      >
        <Input placeholder="Enter URL" />
      </Form.Item>
    </Form>
  );
};

export default SubmissionForm;
