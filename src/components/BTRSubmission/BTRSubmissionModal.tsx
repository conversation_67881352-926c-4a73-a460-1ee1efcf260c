import { postBTRSubmission } from '@/services/data';
import { Button, Modal, Space } from 'antd';
import React, { useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import SubmissionForm from './Form';

const BTRSubmissionModal = () => {
  const [loading, setLoading] = useState(false);
  const [form, setForm] = useState(null);
  const showBTRSubmissionPanel = useSelector(
    (state: any) => state.CMA.showBTRSubmissionPanel,
  );
  const dispatch = useDispatch();

  const onClose = () => {
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        showBTRSubmissionPanel: false,
      },
    });
  };

  const handleSubmit = async (values: any) => {
    const submission = {
      ...values,
      lat: Number(values.lat),
      lng: Number(values.lng),
    };
    console.log('handleSubmit', submission);
    try {
      setLoading(true);
      await postBTRSubmission({ body: values });
      onClose();
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    form?.resetFields();
  };

  return (
    <Modal
      title="Submit BTR Pipeline"
      open={showBTRSubmissionPanel}
      onCancel={onClose}
      width={720}
      destroyOnClose={true}
      footer={
        <Space>
          <Button onClick={handleReset}>Reset</Button>
          <Button
            type="primary"
            loading={loading}
            onClick={() => form?.submit()}
          >
            Submit
          </Button>
        </Space>
      }
    >
      <SubmissionForm
        onSubmit={handleSubmit}
        loading={loading}
        setFormRef={setForm}
      />
    </Modal>
  );
};

export default BTRSubmissionModal;
