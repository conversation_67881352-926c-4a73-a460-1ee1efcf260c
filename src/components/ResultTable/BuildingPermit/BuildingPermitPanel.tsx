import { getBuildingPermit } from '@/services/data';
import { Table, TableColumnsType } from 'antd';
import { isEmpty } from 'lodash';
import React, { useEffect, useState } from 'react';
import { connect, useDispatch } from 'umi';
import styles from '../resultTable.css';
import BPFilter from './BPFilter';
import BuildingPermitSummary, { BuildingPermit } from './Summary';

const BuildingPermitPanel = connect(({ CMA }: any) => ({
  map: CMA.map,
  currentPropertyAddress: CMA.currentPropertyAddress,
  currentRadiusMile: CMA.currentRadiusMile,
  buildingPermitData: CMA.buildingPermitData,
  cmaTabKey: CMA.cmaTabKey,
  filteredBuildingPermitData: CMA.filteredBuildingPermitData,
  buildingPermitSearchParams: CMA.buildingPermitSearchParams,
  buildingPermitLoading: CMA.buildingPermitLoading,
}))(function (props: any) {
  const [params, setParams] = useState<any>({ lat: '', lng: '', distance: '' });
  const [expandedRowKeys, setExpandedRowKeys] = useState<any[]>([]); // To control which rows are expanded
  interface DataType {
    key: React.Key;
    property_id: string;
    fulladdress: string;
  }
  const outsideColumn: TableColumnsType<DataType> = [
    // {
    //   title: 'Property ID',
    //   dataIndex: 'property_id',
    //   width: 250,
    //   key: 'property_id',
    //   render: (text: string, record: any) => {
    //     return <p>{text}</p>;
    //   },
    // },
    {
      title: 'Address',
      dataIndex: 'street',
      width: 250,
      key: 'street',
      render: (text: string, record: any) => {
        console.log('abc', record);
        return (
          <p className="text-xs">
            {record.street}, {record.city}, {record.state}, {record.zip_code}
          </p>
        );
      },
    },
  ];

  const dispatch = useDispatch();

  useEffect(() => {
    if (!isEmpty(props.currentPropertyAddress)) {
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          buildingPermitSearchParams: {
            lat: props.currentPropertyAddress.latitude,
            lng: props.currentPropertyAddress.longitude,
            distance: props.currentRadiusMile,
            days: props.buildingPermitSearchParams.days,
          },
        },
      });
    } else {
      // Building Permit
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          buildingPermitData: [],
          filteredBuildingPermitData: [],
          buildingPermitHover: null,
          buildingPermitSearchParams: {
            lat: '',
            lng: '',
            distance: '',
            days: 180,
          },
        },
      });
    }
  }, [props.currentPropertyAddress, props.currentRadiusMile]);

  useEffect(() => {
    const fetchData = async () => {
      if (
        props.buildingPermitSearchParams.lat &&
        props.buildingPermitSearchParams.lng
      ) {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            buildingPermitLoading: true,
          },
        });

        const data = await getBuildingPermit(props.buildingPermitSearchParams);
        console.log('bp csv', formatBuildingPermits(data));
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            buildingPermitData: formatBuildingPermits(data),
            filteredBuildingPermitData: formatBuildingPermits(data),
            buildingPermitLoading: false,
          },
        });
      }
    };
    fetchData();
  }, [props.buildingPermitSearchParams]);

  const handleExpand = (expanded: boolean, record: any) => {
    console.log('expand', expanded, record);
    setExpandedRowKeys(expanded ? [record.property_id] : []);
  };
  interface ExpandedDataType {
    key: React.Key;
    building_permit_id: string;
    description: string;
    type: string;
    project_type: string;
    initial_status: string;
    initial_status_date: string;
    latest_status: string;
    latest_status_date: string;
  }

  const expandedRowRender = (record: any) => {
    const columns: TableColumnsType<ExpandedDataType> = [
      // {
      //   title: 'Id',
      //   dataIndex: 'building_permit_id',
      //   width: 250,
      //   key: 'building_permit_id',
      //   render: (text: string, record: any) => {
      //     return (
      //       <p className='text-xs'>
      //         {record.building_permit_id ? record.building_permit_id : 'N/A'}
      //       </p>
      //     );
      //   },
      // },
      {
        title: 'Description',
        dataIndex: 'description',
        width: 300,
        key: 'description',
        render: (text: string, record: any) => {
          console.log('bp record', record);
          return (
            <p className="text-xs">
              {record.description ? record.description : 'N/A'}
            </p>
          );
        },
      },
      {
        title: 'Type',
        dataIndex: 'type',
        key: 'type',
        width: 150,
        render: (text: string, record: any) => {
          return <p className="text-xs">{record.type}</p>;
        },
      },
      {
        title: 'Project Type',
        dataIndex: 'project_type',
        key: 'project_type',
        width: 150,
        render: (text: string, record: any) => {
          return <p className="text-xs">{record.project_type}</p>;
        },
      },
      {
        title: 'Home Owner',
        dataIndex: 'homeowner',
        key: 'homeowner',
        width: 150,
        render: (text: string, record: any) => {
          return <p className="text-xs">{record.homeowner}</p>;
        },
      },
      {
        title: 'Business Name',
        dataIndex: 'business_name',
        key: 'business_name',
        width: 150,
        render: (text: string, record: any) => {
          return <p className="text-xs">{record.business_name}</p>;
        },
      },
      {
        title: 'Initial Status',
        dataIndex: 'initial_status',
        key: 'initial_status',
        width: 150,
        render: (text: string, record: any) => {
          return <p className="text-xs">{record.initial_status}</p>;
        },
      },
      {
        title: 'Initial Status Date',
        dataIndex: 'initial_status_date',
        key: 'initial_status_date',
        width: 150,
        render: (text: string, record: any) => {
          return <p className="text-xs">{record.initial_status_date}</p>;
        },
      },
      {
        title: 'Latest Status',
        dataIndex: 'permit_status',
        key: 'permit_status',
        width: 150,
        render: (text: string, record: any) => {
          return <p className="text-xs">{record.permit_status}</p>;
        },
      },
      {
        title: 'Latest Status Date',
        dataIndex: 'latest_status_date',
        key: 'latest_status_date',
        width: 150,
        render: (text: string, record: any) => {
          return <p className="text-xs">{record.latest_status_date}</p>;
        },
      },
    ];

    return (
      <Table
        columns={columns}
        dataSource={findBuildingPermitsByPropertyId(
          record.property_id,
          props.filteredBuildingPermitData,
        )}
        pagination={false}
        onRow={(record, rowIndex) => {
          return {
            onMouseEnter: (event: any) => {
              dispatch({
                type: 'CMA/saveCMAStates',
                payload: { buildingPermitHover: record },
              });
            },
            onMouseLeave: (event: any) => {
              dispatch({
                type: 'CMA/saveCMAStates',
                payload: { buildingPermitHover: null },
              });
            },
          };
        }}
      />
    );
  };
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  return (
    <div className={styles.cardWrapper}>
      <p className="text-lg font-semibold mb-4">Building Permits</p>

      {props.buildingPermitData && <BuildingPermitSummary />}
      <BPFilter />
      <p>
        <b>{countBP(props.filteredBuildingPermitData).totalPermits} </b>
        Building Permits found in{' '}
        <b>{countBP(props.filteredBuildingPermitData).totalProperties}</b>{' '}
        properties
      </p>
      {props.buildingPermitData && (
        <Table
          dataSource={props.filteredBuildingPermitData}
          loading={props.buildingPermitLoading}
          columns={outsideColumn}
          expandable={{
            expandedRowRender, // Render expanded rows
            expandedRowKeys: expandedRowKeys,
            onExpand: handleExpand,
          }}
          rowKey={(record) => record.property_id}
          bordered={true}
          size="small"
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            onChange: (page, pageSize) => {
              setCurrentPage(page);
              setPageSize(pageSize);
            },
          }}
          sticky={true}
          scroll={{ x: '45vw', y: 800 }}
        />
      )}
    </div>
  );
});

export default BuildingPermitPanel;

export function formatBuildingPermits(data: any) {
  const result = [];
  const propertyMap: {
    [key: string]: {
      property_id: string;
      street: string;
      city: string;
      state: string;
      zip_code: string;
      geom: any;
      building_permit: any[];
    };
  } = {};

  data?.forEach((item: any) => {
    const {
      property_id,
      street,
      city,
      state,
      zip_code,
      geom,
      ...permitDetails
    } = item;
    if (!propertyMap[property_id]) {
      propertyMap[property_id] = {
        property_id,
        street,
        city,
        state,
        zip_code,
        geom,
        building_permit: [],
      };
    }
    propertyMap[property_id].building_permit.push({
      ...permitDetails,
      street,
      city,
      state,
      zip_code,
      geom,
    });
  });

  for (const property in propertyMap) {
    result.push(propertyMap[property]);
  }
  return result;
}

function findBuildingPermitsByPropertyId(propertyId: string, data: any) {
  if (data) {
    const property = data.find((item: any) => item.property_id === propertyId);
    return property ? property.building_permit : [];
  }
  return [];
}

interface PermitCounts {
  totalProperties: number; // Total number of properties
  totalPermits: number; // Total number of building permits
  averagePermitsPerProperty: number; // Average permits per property
  permitsByType: Record<string, number>; // Count of each permit type
  permitsByStatus: Record<string, number>; // Count of permits by current status
}

export const countBP = (
  filteredBuildingPermitData: BuildingPermit[],
): PermitCounts => {
  if (!filteredBuildingPermitData?.length) {
    return {
      totalProperties: 0,
      totalPermits: 0,
      averagePermitsPerProperty: 0,
      permitsByType: {},
      permitsByStatus: {},
    };
  }

  const permitsByType: Record<string, number> = {};
  const permitsByStatus: Record<string, number> = {};
  let totalPermits = 0;

  filteredBuildingPermitData.forEach((property) => {
    property.building_permit.forEach((permit) => {
      totalPermits++;
      permitsByType[permit.type] = (permitsByType[permit.type] || 0) + 1;
      permitsByStatus[permit.latest_status] =
        (permitsByStatus[permit.latest_status] || 0) + 1;
    });
  });

  return {
    totalProperties: filteredBuildingPermitData.length,
    totalPermits,
    averagePermitsPerProperty: Number(
      (totalPermits / filteredBuildingPermitData.length).toFixed(2),
    ),
    permitsByType,
    permitsByStatus,
  };
};
