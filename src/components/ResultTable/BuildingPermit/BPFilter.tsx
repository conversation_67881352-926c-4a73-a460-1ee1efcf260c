import { postBuildingPermitCSV } from '@/services/data';
import { Button, InputNumber, InputNumberProps, Select, Space } from 'antd';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'umi';

const BPFilter = () => {
  const dispatch = useDispatch();
  const buildingPermitData = useSelector(
    (state: any) => state.CMA.buildingPermitData,
  );
  const currentPropertyAddress = useSelector(
    (state: any) => state.CMA.currentPropertyAddress,
  );
  const currentRadiusMile = useSelector(
    (state: any) => state.CMA.currentRadiusMile,
  );
  const buildingPermitSearchParams = useSelector(
    (state: any) => state.CMA.buildingPermitSearchParams,
  );
  const filteredBuildingPermitData = useSelector(
    (state: any) => state.CMA.filteredBuildingPermitData,
  );

  const [projectTypesOptions, setProjectTypesOptions] = useState<any>();
  const [initialStatusOptions, setInitialStatusOptions] = useState<any>();
  const [projectTypeSelection, setProjectTypeSelection] = useState<string[]>([
    'All',
  ]);
  const [initialStatusSelection, setInitialStatusSelection] = useState<
    string[]
  >(['All']);

  const [daysOption, setDaysOption] = useState<any>(180);
  // const handleTypeChange = (value: string) => {
  //   console.log(`selected ${value}`);
  //   setProjectTypeSelection(value);
  //   filterBuildingPermits(value);
  // };
  const handleInitialStatusChange = (value: string[]) => {
    // If 'All' is selected along with other options, remove 'All'
    if (value.includes('All') && value.length > 1) {
      value = value.filter((item) => item !== 'All');
    }

    // If no options are selected, default back to 'All'
    if (value.length === 0) {
      value = ['All'];
    }

    console.log(`selected ${value}`);
    setInitialStatusSelection(value);
    filterBuildingPermitsByInitialStatus(value);
  };

  const getDistinctProjectTypes = (data: any) => {
    const projectTypes = new Set<string>();

    data.forEach((entry: any) => {
      console.log('entry', entry);
      if (entry.building_permit.length > 0) {
        entry.building_permit.forEach((element: any) => {
          projectTypes.add(element.project_type);
        });
      }
    });

    // Convert the set to an array and add the "All" option
    const projectTypesArray = Array.from(projectTypes);
    projectTypesArray.push('All');

    // Sort the array alphabetically
    projectTypesArray.sort();

    // Map the sorted array to the desired format
    return projectTypesArray.map((type) => ({
      value: type,
      label: type,
    }));
  };

  const getDistinctProjectInitialStatus = (data: any) => {
    const projectTypes = new Set<string>();

    data.forEach((entry: any) => {
      if (entry.building_permit.length > 0) {
        entry.building_permit.forEach((element: any) => {
          projectTypes.add(element.latest_status);
        });
      }
    });

    // Convert the set to an array and add the "All" option
    const projectTypesArray = Array.from(projectTypes);
    projectTypesArray.push('All');

    // Sort the array alphabetically
    projectTypesArray.sort();

    // Map the sorted array to the desired format
    return projectTypesArray.map((type) => ({
      value: type,
      label: type,
    }));
  };

  const onChange: InputNumberProps['onChange'] = (value) => {
    console.log('changed', value);
    if (value) {
      setDaysOption(value);
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          buildingPermitSearchParams: {
            lat: currentPropertyAddress.latitude,
            lng: currentPropertyAddress.longitude,
            distance: currentRadiusMile,
            days: value,
          },
        },
      });
    }
  };

  const filterBuildingPermits = (projectTypeList: string[]) => {
    if (projectTypeList[0] === 'All') {
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          filteredBuildingPermitData: buildingPermitData,
        },
      });
    } else {
      const filteredData = buildingPermitData
        .map((entry: any) => {
          const filteredPermits = entry.building_permit.filter((permit: any) =>
            projectTypeList.includes(permit.project_type),
          );

          // Only return the property if it has matching permits
          if (filteredPermits.length > 0) {
            return {
              ...entry,
              building_permit: filteredPermits,
            };
          } else {
            return null;
          }
        })
        .filter((entry) => entry !== null);

      console.log('filterBuildingPermits', filteredData);
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          filteredBuildingPermitData: filteredData,
        },
      });
    }
  };

  const filterBuildingPermitsByInitialStatus = (initialStatus: string[]) => {
    if (initialStatus[0] === 'All') {
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          filteredBuildingPermitData: buildingPermitData,
        },
      });
    } else {
      const filteredData = buildingPermitData
        .map((entry: any) => {
          const filteredPermits = entry.building_permit.filter((permit: any) =>
            initialStatus.includes(permit.initial_status),
          );

          // Only return the property if it has matching permits
          if (filteredPermits.length > 0) {
            return {
              ...entry,
              building_permit: filteredPermits,
            };
          } else {
            return null;
          }
        })
        .filter((entry) => entry !== null);

      console.log('filterBuildingPermitsByInitialStatus', filteredData);
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          filteredBuildingPermitData: filteredData,
        },
      });
    }
  };

  useEffect(() => {
    if (buildingPermitData.length > 0) {
      const projectTypes = getDistinctProjectTypes(buildingPermitData);
      const initialStatuses =
        getDistinctProjectInitialStatus(buildingPermitData);
      console.log('projectTypes', projectTypes);
      setProjectTypesOptions(projectTypes);
      setInitialStatusOptions(initialStatuses);

      console.log('buildingPermitData', projectTypeSelection);
      filterBuildingPermits(projectTypeSelection);
    }
  }, [buildingPermitData]);

  const handleChange = (value: string[]) => {
    // If 'All' is selected along with other options, remove 'All'
    if (value.includes('All') && value.length > 1) {
      value = value.filter((item) => item !== 'All');
    }

    // If no options are selected, default back to 'All'
    if (value.length === 0) {
      value = ['All'];
    }

    console.log(`selected ${value}`);
    setProjectTypeSelection(value);
    console.log('handleChange', value);
    filterBuildingPermits(value);
  };

  const [CSVButtonLoading, setCSVButtonLoading] = useState<boolean>(false);
  const onClickCSVButton = () => {
    setCSVButtonLoading(true);
    const params: any = {
      body: {
        lat: currentPropertyAddress.latitude,
        lng: currentPropertyAddress.longitude,
        distance: currentRadiusMile,
        day: buildingPermitSearchParams.days,
        filter: {
          projectType: [],
          latestStatus: [],
        },
      },
    };
    if (!projectTypeSelection.includes('All')) {
      params.body.filter.projectType = projectTypeSelection;
    }
    if (!initialStatusSelection.includes('All')) {
      params.body.filter.latestStatus = initialStatusSelection;
    }
    const fetchData = async () => {
      try {
        const response = await postBuildingPermitCSV(params);
        if (response) {
          // Create blob directly from the response string
          const blob = new Blob([response], {
            type: 'text/csv;charset=utf-8;',
          });

          // Create download URL
          const url = window.URL.createObjectURL(blob);

          // Create and trigger download
          const a = document.createElement('a');
          a.href = url;

          // Create filename with current date
          const date = new Date().toISOString();
          const filename = `building_permits_${date}.csv`;
          a.download = filename;

          // Trigger download
          document.body.appendChild(a);
          a.click();

          // Cleanup
          window.URL.revokeObjectURL(url);
          a.remove();
        }
        setCSVButtonLoading(false);
      } catch (error) {
        console.error('Error downloading CSV:', error);
        // Handle error appropriately
      }
    };
    fetchData();
  };
  return (
    <div className="mb-4">
      <Space wrap>
        <p>Project Type:</p>

        <Select
          mode="multiple"
          allowClear
          style={{ width: '200px' }}
          placeholder="Please select Types"
          value={projectTypeSelection}
          onChange={handleChange}
          options={projectTypesOptions}
        />
        <p>Latest Status:</p>
        <Select
          mode="multiple"
          value={initialStatusSelection}
          style={{ width: 200 }}
          options={initialStatusOptions}
          onChange={handleInitialStatusChange}
        />
        <p>Days:</p>
        <InputNumber
          min={90}
          max={9999}
          // defaultValue={daysOption}
          value={buildingPermitSearchParams.days}
          onChange={onChange}
        />
        <Button
          type="default"
          loading={CSVButtonLoading}
          onClick={onClickCSVButton}
          disabled={filteredBuildingPermitData.length === 0}
        >
          Export CSV
        </Button>
      </Space>
    </div>
  );
};

export default BPFilter;
