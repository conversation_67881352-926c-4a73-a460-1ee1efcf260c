import {
  getAreaDemographicInDistance,
  getBuildingPermit,
} from '@/services/data';
import { Column } from '@ant-design/plots';
import { Card, Col, Row, Select, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import { countBP, formatBuildingPermits } from './BuildingPermitPanel';

export interface BuildingPermit {
  property_id: string;
  street: string;
  city: string;
  state: string;
  zip_code: string;
  geom: Geom;
  building_permit: BuildingPermitElement[];
}

export interface BuildingPermitElement {
  building_permit_id: string;
  description: string;
  type: string;
  subtype: string;
  initial_status: string;
  initial_status_date: Date;
  latest_status: string;
  latest_status_date: Date;
  project_type: string;
  permit_status: string;
  street: string;
  city: string;
  state: string;
  zip_code: string;
  geom: Geom;
}

export interface Geom {
  type: string;
  coordinates: number[];
}

export interface PermitTypeCount {
  type: string;
  count: number;
  percentage: number;
}

export interface QuarterlyTrend {
  quarter: string;
  value: number;
}

const BuildingPermitSummary: React.FC = () => {
  const dispatch = useDispatch();
  const buildingPermitLoading = useSelector(
    (state: any) => state.CMA.buildingPermitLoading,
  );
  const currentPropertyAddress = useSelector<any>(
    (state: any) => state.CMA.currentPropertyAddress,
  );
  const currentRadiusMile = useSelector<any>(
    (state: any) => state.CMA.currentRadiusMile,
  );
  const [selectedPermitType, setSelectedPermitType] = useState<string>('All');

  const [dataForSummary, setDataForSummary] = useState<BuildingPermit[]>([]);

  const [totalHousehold, setTotalHousehold] = useState<any>();
  useEffect(() => {
    const params = {
      lat: currentPropertyAddress.latitude,
      lng: currentPropertyAddress.longitude,
      distance: currentRadiusMile,
      days: 2190,
    };

    const fetchData = async () => {
      if (params.lat && params.lng) {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            buildingPermitLoading: true,
          },
        });

        const data = await getBuildingPermit(params);

        console.log('bp csv', formatBuildingPermits(data));
        setDataForSummary(formatBuildingPermits(data));
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            buildingPermitLoading: false,
          },
        });
        const result = await getAreaDemographicInDistance({
          lat: currentPropertyAddress.latitude,
          lng: currentPropertyAddress.longitude,
          distance: currentRadiusMile,
        });

        if (result && result.length > 0) {
          setTotalHousehold(result[0].total_households);
        }
      }
    };
    fetchData();
  }, [currentPropertyAddress, currentRadiusMile]);
  const permitTypeCounts = useMemo<PermitTypeCount[]>(() => {
    if (!dataForSummary) return [];

    const counts: Record<string, number> = {};
    let total = 0;

    dataForSummary.forEach((property) => {
      property.building_permit.forEach((permit) => {
        counts[permit.project_type] = (counts[permit.project_type] || 0) + 1;
        total += 1;
      });
    });

    return Object.entries(counts)
      .map(([type, count]) => ({
        type,
        count,
        percentage: Number(((count / total) * 100).toFixed(1)),
      }))
      .sort((a, b) => b.count - a.count);
  }, [dataForSummary]);

  const quarterlyTrends = useMemo<QuarterlyTrend[]>(() => {
    if (!dataForSummary) return [];

    const trends: Record<string, Record<string, number>> = {};
    const allTypes = new Set<string>();

    dataForSummary.forEach((property) => {
      property.building_permit.forEach((permit) => {
        const date = new Date(permit.initial_status_date);
        const quarter = `${date.getFullYear()} Q${
          Math.floor(date.getMonth() / 3) + 1
        }`;
        if (!trends[quarter]) {
          trends[quarter] = {};
        }
        allTypes.add(permit.project_type);
        trends[quarter][permit.project_type] =
          (trends[quarter][permit.project_type] || 0) + 1;
      });
    });

    // Convert to array and calculate totals
    return Object.entries(trends)
      .map(([quarter, typeCounts]) => {
        const total = Object.values(typeCounts).reduce(
          (sum, count) => sum + count,
          0,
        );
        return {
          quarter,
          value:
            selectedPermitType === 'All'
              ? total
              : typeCounts[selectedPermitType] || 0,
        };
      })
      .sort((a, b) => {
        const [yearA, qA] = a.quarter.split(' ');
        const [yearB, qB] = b.quarter.split(' ');
        return (
          yearA.localeCompare(yearB) || qA.slice(1).localeCompare(qB.slice(1))
        );
      });
  }, [dataForSummary, selectedPermitType]);
  const permitTypeOptions = useMemo(() => {
    const types = new Set<string>();
    dataForSummary?.forEach((property) => {
      property.building_permit.forEach((permit) => {
        types.add(permit.project_type);
      });
    });
    return ['All', ...Array.from(types)].map((type) => ({
      label: type,
      value: type,
    }));
  }, [dataForSummary]);
  const permitTypeColumns: ColumnsType<PermitTypeCount> = [
    {
      title: 'Permit Type',
      dataIndex: 'type',
      key: 'type',
      sorter: (a, b) => a.project_type.localeCompare(b.project_type),
    },
    {
      title: 'Count',
      dataIndex: 'count',
      key: 'count',
      sorter: (a, b) => a.count - b.count,
      defaultSortOrder: 'descend',
    },
    {
      title: 'Count / Total Household',
      dataIndex: 'count',
      key: 'count per hh',
      render: (text, record) => {
        return ((record.count / totalHousehold) * 100).toFixed(2) + '%';
      },
    },
    {
      title: 'Percentage',
      dataIndex: 'percentage',
      key: 'percentage',
      render: (value: number) => `${value}%`,
      sorter: (a, b) => a.percentage - b.percentage,
    },
  ];

  const quarterlyTrendConfig = {
    data: quarterlyTrends,
    xField: 'quarter',
    yField: 'value',
    label: {
      position: 'middle',
      style: {
        fill: '#FFFFFF',
        opacity: 0.6,
      },
    },
    xAxis: {
      label: {
        autoHide: true,
        autoRotate: false,
      },
    },
    meta: {
      quarter: {
        alias: 'Quarter',
      },
      value: {
        alias: 'Count',
      },
    },
  };

  return (
    <div className="my-4">
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card
            title={`Project Types Distribution (${
              countBP(dataForSummary).totalPermits
            })`}
          >
            <Table
              columns={permitTypeColumns}
              dataSource={permitTypeCounts}
              rowKey="type"
              scroll={{ y: '400px' }}
              size="small"
              loading={buildingPermitLoading}
              pagination={false}
            />
          </Card>
        </Col>
        <Col span={24}>
          <Card
            title="Quarterly Permit Trends since 2019 Q1"
            extra={
              <Select
                showSearch
                style={{ width: 300 }}
                value={selectedPermitType}
                onChange={setSelectedPermitType}
                options={permitTypeOptions}
                loading={buildingPermitLoading}
                placeholder="Select permit type"
              />
            }
          >
            <Column {...quarterlyTrendConfig} height={300} />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default BuildingPermitSummary;
