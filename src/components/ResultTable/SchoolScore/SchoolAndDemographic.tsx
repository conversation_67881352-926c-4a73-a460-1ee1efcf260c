import { Segmented } from 'antd';
import { useDispatch, useSelector } from 'umi';
import styles from '../resultTable.css';
import AreaDemographic from './AreaBased/AreaDemographic';
import AreaPsychographicTable from './AreaBased/AreaPsychographicTable';
import ChainStore from './AreaBased/ChainStore';
import MajorEmployer from './AreaBased/MajorEmployer';
import PopulationDensity from './AreaBased/PopulationDensity';
import DemoGraphic from './ParcelBased/DemoGraphic';
import ParcelPsychographic from './ParcelBased/ParcelPsychographic';
import SchoolScore from './ParcelBased/SchoolScore';
const SchoolAndDemographic = () => {
  const dispatch = useDispatch();
  const schoolAndDemographicTab = useSelector(
    (state: any) => state.CMA.schoolAndDemographicTab,
  );

  const switchTab = (value: string) => {
    console.log('switch tab', value);
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        schoolAndDemographicTab: value,
      },
    });
  };
  return (
    <div key="MLS and SFR wrapper" className={styles.cardWrapper}>
      <Segmented
        options={['Area Based', 'Parcel Based']}
        block
        onChange={switchTab}
      />
      {schoolAndDemographicTab === 'Parcel Based' ? (
        <div className="mt-4">
          <SchoolScore />
          <ParcelPsychographic />
          <DemoGraphic />
        </div>
      ) : (
        <div className="mt-4">
          <AreaPsychographicTable />
          <ChainStore />
          <AreaDemographic />
          <PopulationDensity />
          <MajorEmployer />
        </div>
      )}
    </div>
  );
};

export default SchoolAndDemographic;
