import {
  getPopulationDensity,
  getPopulationDensityClass,
  getSchoolScoreDemographic,
} from '@/services/data';
import { Skeleton } from 'antd';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import styles from '../../resultTable.css';
const DemoGraphic = () => {
  const demopraphicData = useSelector(
    (state: any) => state.CMA.demopraphicData,
  );
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const currentPropertyAddress = useSelector(
    (state: any) => state.CMA.currentPropertyAddress,
  );
  const currentRadiusMile = useSelector(
    (state: any) => state.CMA.currentRadiusMile,
  );
  // useEffect(() => {
  //   const fetchData = async () => {
  //     if (currentPropertyAddress.latitude && currentPropertyAddress.longitude) {
  //       setLoading(true);
  //       const schoolScoreResult = await getSchoolScoreDemographic({
  //         lat: currentPropertyAddress.latitude,
  //         lng: currentPropertyAddress.longitude,
  //       });
  //       console.log('SchoolAndDemographic', schoolScoreResult);
  //       if (schoolScoreResult) {
  //         dispatch({
  //           type: 'CMA/saveCMAStates',
  //           payload: {
  //             demopraphicData: schoolScoreResult[0],
  //           },
  //         });
  //       }
  //       setLoading(false);
  //     } else {
  //       dispatch({
  //         type: 'CMA/saveCMAStates',
  //         payload: {
  //           demopraphicData: null,
  //         },
  //       });
  //     }
  //   };
  //   fetchData();
  // }, [currentPropertyAddress, currentRadiusMile]);
  return (
    <div className={styles.cardWrapper}>
      <p className="text-lg font-semibold mb-2">Demographics (Block Group)</p>
      {loading ? (
        <Skeleton active />
      ) : demopraphicData ? (
        <>
          {/* Income */}
          <div>
            <div className="grid grid-cols-1 gap-y-2">
              <div className="grid grid-cols-2">
                <p>Median Household Income: </p>
                <p>
                  {new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                  }).format(demopraphicData.medianhhincome)}
                </p>
              </div>

              <div className="grid grid-cols-2">
                <p>Five-Year Income Growth Projection: </p>
                <p>{demopraphicData.fiveyearincomegrowth?.toFixed(1)}%</p>
              </div>
              <div className="grid grid-cols-2">
                <p>Five-Year Population Growth Projection:</p>
                <p>{demopraphicData.fiveyearpopgrowth?.toFixed(1) + '%'}</p>
              </div>
              <div className="grid grid-cols-2">
                <p>Crime Score:</p>
                <p>{demopraphicData.crime_score} out of 10</p>
              </div>
            </div>
          </div>
        </>
      ) : (
        <>
          <p>Select a location on the map to see result.</p>
        </>
      )}
    </div>
  );
};

export default DemoGraphic;
