import { getSchoolScoreDemographic } from '@/services/data';
import {
  CaretDownOutlined,
  CaretRightOutlined,
  CaretUpOutlined,
} from '@ant-design/icons';
import { Skeleton, Tooltip } from 'antd';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import styles from '../../resultTable.css';
const SchoolScore = () => {
  const demopraphicData = useSelector(
    (state: any) => state.CMA.demopraphicData,
  );
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const currentPropertyAddress = useSelector(
    (state: any) => state.CMA.currentPropertyAddress,
  );
  const currentRadiusMile = useSelector(
    (state: any) => state.CMA.currentRadiusMile,
  );
  useEffect(() => {
    const fetchData = async () => {
      if (currentPropertyAddress.latitude && currentPropertyAddress.longitude) {
        setLoading(true);
        const schoolScoreResult = await getSchoolScoreDemographic({
          lat: currentPropertyAddress.latitude,
          lng: currentPropertyAddress.longitude,
        });
        console.log('SchoolAndDemographic', schoolScoreResult);
        if (schoolScoreResult) {
          dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              demopraphicData: schoolScoreResult[0],
            },
          });
        }
        setLoading(false);
      } else {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            demopraphicData: null,
          },
        });
      }
    };
    fetchData();
  }, [currentPropertyAddress, currentRadiusMile]);

  return (
    <div className={styles.cardWrapper}>
      <p className="text-lg mb-2 font-semibold">School Scores</p>
      {loading ? (
        <Skeleton active />
      ) : demopraphicData ? (
        <div className="grid grid-cols-1 gap-y-2">
          <div className="grid grid-cols-2">
            <p>
              Elementary School:{' '}
              <a
                target="_blank"
                href={demopraphicData.school.elem.url}
                className="text-blue-500"
              >
                {demopraphicData.school?.elem.name
                  ? demopraphicData.school.elem.name
                      .toLowerCase()
                      .replace(/\b\w/g, (letter: string) =>
                        letter.toUpperCase(),
                      )
                  : '-'}
              </a>
            </p>
            <p>
              {demopraphicData.school?.elem.score_2023 || '-'}
              {demopraphicData.school?.elem.score_2022 &&
                demopraphicData.school?.elem.score_2023 &&
                scoreDifference(
                  demopraphicData.school?.elem.score_2022,
                  demopraphicData.school?.elem.score_2023,
                )}
            </p>
          </div>
          <div className="grid grid-cols-2">
            <p>
              Middle School:{' '}
              <a
                target="_blank"
                href={demopraphicData.school.middle.url}
                className="text-blue-500"
              >
                {demopraphicData.school?.middle.name
                  ? demopraphicData.school.middle.name
                      .toLowerCase()
                      .replace(/\b\w/g, (letter: string) =>
                        letter.toUpperCase(),
                      )
                  : '-'}
              </a>
            </p>
            <p>
              {demopraphicData.school?.middle.score_2023 || '-'}
              {demopraphicData.school?.middle.score_2022 &&
                demopraphicData.school?.middle.score_2023 &&
                scoreDifference(
                  demopraphicData.school?.middle.score_2022,
                  demopraphicData.school?.middle.score_2023,
                )}
            </p>
          </div>
          <div className="grid grid-cols-2">
            <p>
              High School:{' '}
              <a
                target="_blank"
                href={demopraphicData.school.high.url}
                className="text-blue-500"
              >
                {demopraphicData.school?.high.name
                  ? demopraphicData.school.high.name
                      .toLowerCase()
                      .replace(/\b\w/g, (letter: string) =>
                        letter.toUpperCase(),
                      )
                  : '-'}
              </a>
            </p>
            <p>
              {demopraphicData.school?.high.score_2023 || '-'}
              {demopraphicData.school?.high.score_2022 &&
                demopraphicData.school?.high.score_2023 &&
                scoreDifference(
                  demopraphicData.school?.high.score_2022,
                  demopraphicData.school?.high.score_2023,
                )}
            </p>
          </div>
        </div>
      ) : (
        <>
          <p>Select a location on the map to see result.</p>
        </>
      )}
    </div>
  );
};

export default SchoolScore;

const scoreDifference = (oldNum: number, newNum: number) => {
  let diff;
  let arrow;
  if (newNum > oldNum) {
    diff = newNum - oldNum;
    arrow = '+';
  } else if (newNum < oldNum) {
    diff = oldNum - newNum;
    arrow = '-';
  } else if (newNum === oldNum) {
    diff = 0;
    arrow = '=';
  }
  if (arrow === '+')
    return (
      <Tooltip title={'Last year score: ' + oldNum}>
        <span className={styles.schoolScoreDiffIncre}>
          <CaretUpOutlined />
          {diff}
        </span>
      </Tooltip>
    );
  else if (arrow === '-') {
    return (
      <Tooltip title={'Last year score: ' + oldNum}>
        <span className={styles.schoolScoreDiffDecre}>
          <CaretDownOutlined />
          {diff}
        </span>
      </Tooltip>
    );
  } else if (arrow === '=') {
    return (
      <span className={styles.schoolScore}>
        <CaretRightOutlined />
      </span>
    );
  }
};
