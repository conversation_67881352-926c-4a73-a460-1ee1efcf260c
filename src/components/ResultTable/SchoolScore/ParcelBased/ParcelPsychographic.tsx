import { getPsychographicData } from '@/services/data';
import { Skeleton } from 'antd';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import styles from '../../resultTable.css';
import downloadLink from '../downloadLink';
const ParcelPsychographic = () => {
  const psychographicData = useSelector(
    (state: any) => state.CMA.psychographicData,
  );
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const currentPropertyAddress = useSelector(
    (state: any) => state.CMA.currentPropertyAddress,
  );
  const currentRadiusMile = useSelector(
    (state: any) => state.CMA.currentRadiusMile,
  );
  useEffect(() => {
    const fetchData = async () => {
      if (currentPropertyAddress.latitude && currentPropertyAddress.longitude) {
        setLoading(true);
        const psychographicResult = await getPsychographicData({
          lat: currentPropertyAddress.latitude,
          lng: currentPropertyAddress.longitude,
          zip: currentPropertyAddress.postalCode,
        });
        console.log(
          'SchoolAndDemographic psychographicResult',
          psychographicResult,
        );

        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            psychographicData: psychographicResult,
          },
        });
        setLoading(false);
      } else {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            psychographicData: null,
          },
        });
      }
    };
    fetchData();
  }, [currentPropertyAddress, currentRadiusMile]);
  return (
    <div className={styles.cardWrapper}>
      <p className="text-lg mb-2 font-semibold">Psychographics</p>

      {loading ? (
        <Skeleton active />
      ) : psychographicData ? (
        <div className="grid grid-cols-1 gap-y-2">
          <div className="grid grid-cols-2">
            <p>Family: {psychographicData.Family}</p>
            <p>Segment: {psychographicData.Segment}</p>
          </div>
          <div className="grid grid-cols-2">
            <p>
              Match Level:{' '}
              {psychographicData['Match Level'] === 'ADDRESS'
                ? 'Household'
                : psychographicData['Match Level'] === 'NEAREST HH'
                ? 'Household'
                : psychographicData['Match Level']}
            </p>
            <p>
              Segment Overview:{' '}
              <span>
                <a
                  href={
                    downloadLink[psychographicData.Segment.split(' - ')[0]]
                      ? downloadLink[psychographicData.Segment.split(' - ')[0]]
                      : 'https://google.com'
                  }
                  target="_blank"
                  className="text-blue-500"
                >
                  Download{' '}
                </a>
              </span>
            </p>
          </div>
        </div>
      ) : (
        <>
          <p>Select a location on the map to see result.</p>
        </>
      )}
    </div>
  );
};

export default ParcelPsychographic;
