import { DragOutlined } from '@ant-design/icons';
import { Divider, Modal, Segmented, Slider, Spin, Table, Tag } from 'antd';
import React, { useRef, useState } from 'react';
import type { DraggableData, DraggableEvent } from 'react-draggable';
import Draggable from 'react-draggable';
import { getSegment } from './utils';

interface SegmentModalProps {
  visible: boolean;
  selectedSegment: string;
  segmentOverview: any;
  segmentOverviewDma: any;
  segmentTableLoading: boolean;
  marketLevel: string;
  segmentMaxValue: number;
  isLoadingRange: boolean;
  currentSliderMin: number;
  currentSliderMax: number;
  onClose: () => void;
  onMarketLevelChange: (value: string) => void;
  onSliderChange: (values: [number, number]) => void;
  onMarketClick: (marketName: string, geom: string, type: string) => void;
  onMarketHover: (geom: string, marketName: string) => void;
  onMarketLeave: () => void;
}

const SegmentModal: React.FC<SegmentModalProps> = ({
  visible,
  selectedSegment,
  segmentOverview,
  segmentOverviewDma,
  segmentTableLoading,
  marketLevel,
  segmentMaxValue,
  isLoadingRange,
  currentSliderMin,
  currentSliderMax,
  onClose,
  onMarketLevelChange,
  onSliderChange,
  onMarketClick,
  onMarketHover,
  onMarketLeave,
}) => {
  const [modalDisabled, setModalDisabled] = useState(true);
  const [modalBounds, setModalBounds] = useState({
    left: 0,
    top: 0,
    bottom: 0,
    right: 0,
  });
  const draggleRef = useRef<HTMLDivElement>(null!);

  const onModalStart = (_event: DraggableEvent, uiData: DraggableData) => {
    const { clientWidth, clientHeight } = window.document.documentElement;
    const targetRect = draggleRef.current?.getBoundingClientRect();
    if (!targetRect) return;

    setModalBounds({
      left: -targetRect.left + uiData.x,
      right: clientWidth - (targetRect.right - uiData.x),
      top: -targetRect.top + uiData.y,
      bottom: clientHeight - (targetRect.bottom - uiData.y),
    });
  };

  const getTableColumns = () => [
    {
      title: 'Market',
      dataIndex: 'marketName',
      key: 'marketName',
      width: '25%',
      render: (text: string, record: any) => {
        const displayText =
          marketLevel === 'state' ? text : text.replace(/\s*\([A-Z]{2}\)/g, '');

        return (
          <div
            className="cursor-pointer hover:text-blue-600 transition-colors"
            onMouseEnter={() => onMarketHover(record.geom, text)}
            onMouseLeave={onMarketLeave}
          >
            <p className="text-xs">{displayText}</p>
          </div>
        );
      },
    },
    {
      title: 'Segment %',
      dataIndex: 'segmentPercentage',
      key: 'segmentPercentage',
      width: '20%',
      align: 'right' as const,
      render: (value: number) => (
        <Tag color="blue" className="font-medium text-xs">
          {value}%
        </Tag>
      ),
      sorter: (a: any, b: any) => a.segmentPercentage - b.segmentPercentage,
      defaultSortOrder: 'descend' as const,
    },
    {
      title: 'Segment Count',
      dataIndex: 'segmentCount',
      key: 'segmentCount',
      width: '25%',
      align: 'right' as const,
      render: (value: number) => (
        <div className="text-right">
          <div className="font-medium text-xs">{value.toLocaleString()}</div>
        </div>
      ),
      sorter: (a: any, b: any) => a.segmentCount - b.segmentCount,
    },
    {
      title: 'Total Population',
      dataIndex: 'totalPopulation',
      key: 'totalPopulation',
      width: '30%',
      align: 'right' as const,
      render: (value: number) => (
        <div className="text-right">
          <div style={{ color: '#666' }} className="text-xs">
            {value.toLocaleString()}
          </div>
        </div>
      ),
      sorter: (a: any, b: any) => a.totalPopulation - b.totalPopulation,
    },
  ];

  const currentData =
    marketLevel === 'state'
      ? segmentOverview?.markets
      : segmentOverviewDma?.markets;

  const segment = getSegment(selectedSegment);

  return (
    <Modal
      title={
        <div
          style={{
            width: '100%',
            cursor: 'move',
            display: 'flex',
            alignItems: 'center',
            background: '#fff',
            fontWeight: 500,
            userSelect: 'none',
          }}
          onMouseOver={() => {
            if (modalDisabled) {
              setModalDisabled(false);
            }
          }}
          onFocus={() => {}}
          onBlur={() => {}}
        >
          <DragOutlined style={{ marginRight: '8px', color: '#999' }} />
          Selected Segment: {segment?.name}
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      mask={false}
      wrapClassName="pointer-events-none"
      width={600}
      style={{ top: 160, left: 300 }}
      modalRender={(modal) => (
        <Draggable
          disabled={modalDisabled}
          bounds={modalBounds}
          nodeRef={draggleRef}
          onStart={(event, uiData) => onModalStart(event, uiData)}
        >
          <div ref={draggleRef}>{modal}</div>
        </Draggable>
      )}
    >
      {/* Segment Overview Section */}
      <div className="mb-4">
        <p className="mb-3 text-sm font-semibold">Segment Overview</p>
        <div className="bg-gray-50 p-4 rounded-lg mb-3">
          <p className="text-gray-700 mb-3 leading-relaxed">
            {segment?.description}
          </p>

          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center space-x-2">
              <span className="inline-flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-full text-sm font-medium">
                👥
              </span>
              <div>
                <div className="text-xs text-gray-500 uppercase tracking-wide">
                  Head of Household Age
                </div>
                <div className="font-semibold text-gray-900">
                  {segment?.headOfHouseholdAge}
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <span className="inline-flex items-center justify-center w-8 h-8 bg-green-100 text-green-600 rounded-full text-sm font-medium">
                💰
              </span>
              <div>
                <div className="text-xs text-gray-500 uppercase tracking-wide">
                  Est. Household Income
                </div>
                <div className="font-semibold text-gray-900">
                  {segment?.EstHouseholdIncome}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Divider />

      {/* Market Performance Section */}
      {(segmentOverview || segmentOverviewDma) && (
        <div className="mb-4">
          <div className="flex items-center justify-between mb-3">
            <p className="mb-0 text-sm font-semibold">
              Top 10 Performing Markets
            </p>
            <div className="flex items-center space-x-2">
              <Segmented
                options={[
                  { label: 'Metro', value: 'metro' },
                  { label: 'State', value: 'state' },
                ]}
                value={marketLevel}
                onChange={(value) => onMarketLevelChange(value as string)}
                size="small"
              />
            </div>
          </div>

          <Table
            dataSource={currentData}
            pagination={false}
            size="small"
            scroll={{ y: 240 }}
            loading={segmentTableLoading}
            className="custom-segment-table"
            columns={getTableColumns()}
            rowKey="marketName"
            onRow={(record: any) => ({
              onClick: () => {
                onMarketClick(record.marketName, record.geom, marketLevel);
              },
              className: 'cursor-pointer hover:bg-blue-50 transition-colors',
              title: `Click to zoom to ${
                marketLevel === 'state'
                  ? record.marketName
                  : record.marketName.replace(/\s*\([A-Z]{2}\)/g, '')
              }`,
            })}
          />

          {/* Summary Stats */}
          {currentData && (
            <div className="mt-3 p-3 bg-blue-50 rounded-lg">
              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <div className="text-lg font-bold text-blue-600">
                    {Math.max(
                      ...currentData.map((m: any) => m.segmentPercentage),
                    )}
                    %
                  </div>
                  <div className="text-xs text-gray-600">
                    Highest Penetration
                  </div>
                </div>
                <div>
                  <div className="text-lg font-bold text-blue-600">
                    {currentData
                      .reduce((sum: number, m: any) => sum + m.segmentCount, 0)
                      .toLocaleString()}
                  </div>
                  <div className="text-xs text-gray-600">
                    Total Segment Pop.
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      <Divider />

      {/* Filter Controls Section */}
      <div className="mt-4">
        <div className="flex items-center justify-between mb-3">
          <strong>Filter by Segment Percentage</strong>
          <div className="flex items-center space-x-2">
            {isLoadingRange && <Spin size="small" />}
            <Tag color="cyan" className="text-xs">
              {currentSliderMin}% - {currentSliderMax}%
            </Tag>
          </div>
        </div>

        <Slider
          value={[currentSliderMin, currentSliderMax]}
          range
          min={1}
          max={segmentMaxValue}
          disabled={isLoadingRange}
          onChange={(values) => onSliderChange(values as [number, number])}
          tooltip={{
            formatter: (value) => `${value}%`,
          }}
          trackStyle={[{ backgroundColor: '#1890ff' }]}
          handleStyle={[{ borderColor: '#1890ff' }, { borderColor: '#1890ff' }]}
        />

        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>1%</span>
          <span>Max: {segmentMaxValue}%</span>
        </div>
      </div>
    </Modal>
  );
};

export default SegmentModal;
