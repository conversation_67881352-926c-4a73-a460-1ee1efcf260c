import { MAPBOX_TOKEN } from '@/constants';
import {
  getAreaDemographicInDistance,
  getAreaDemographicInDriveTime,
  postAreaDemographicCSV,
} from '@/services/data';
import {
  Button,
  InputNumber,
  Segmented,
  Select,
  Skeleton,
  Tooltip,
} from 'antd';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import styles from '../../resultTable.css';
import { formatNumber } from '../util';

const AreaDemographic = () => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState<boolean>(false);
  const [calculationMode, setCalculationMode] = useState<
    'Drive Time' | 'Distance'
  >('Drive Time');
  const [driveTime, setDriveTime] = useState<string>('10');
  const [distance, setDistance] = useState<string>('3');
  const [mapData, setMapData] = useState<any>(null);
  const currentPropertyAddress = useSelector(
    (state: any) => state.CMA.currentPropertyAddress,
  );
  const map = useSelector((state: any) => state.CMA.map);
  const currentRadiusMile = useSelector(
    (state: any) => state.CMA.currentRadiusMile,
  );
  const areaDemographicData = useSelector(
    (state: any) => state.CMA.areaDemographicData,
  );
  const currentHoverSection = useSelector(
    (state: any) => state.CMA.currentHoverSection,
  );

  const clearMapLayers = () => {
    if (map && map.getLayer('demographic-drive-time-polygon')) {
      map.removeLayer('demographic-drive-time-polygon');
      map.removeLayer('demographic-drive-time-outline');
      map.removeSource('demographic-drive-time-area');
    }
    if (map && map.getLayer('demographic-radius-circle')) {
      map.removeLayer('demographic-radius-circle');
      map.removeLayer('demographic-radius-outline');
      map.removeSource('demographic-radius-area');
    }
  };

  const showMapLayers = () => {
    if (!mapData) return;

    clearMapLayers();

    if (calculationMode === 'Distance') {
      // Add circle source
      map.addSource('demographic-radius-area', {
        type: 'geojson',
        data: mapData,
      });

      // Add circle fill layer
      map.addLayer({
        id: 'demographic-radius-circle',
        type: 'fill',
        source: 'demographic-radius-area',
        layout: {},
        paint: {
          'fill-color': '#18d5db',
          'fill-opacity': 0.2,
        },
      });

      // Add circle outline layer
      map.addLayer({
        id: 'demographic-radius-outline',
        type: 'line',
        source: 'demographic-radius-area',
        layout: {},
        paint: {
          'line-color': '#18d5db',
          'line-width': 2,
        },
      });
    } else {
      // Add drive time source and layers
      map.addSource('demographic-drive-time-area', {
        type: 'geojson',
        data: mapData,
      });

      map.addLayer({
        id: 'demographic-drive-time-polygon',
        type: 'fill',
        source: 'demographic-drive-time-area',
        layout: {},
        paint: {
          'fill-color': '#18d5db',
          'fill-opacity': 0.2,
        },
      });

      map.addLayer({
        id: 'demographic-drive-time-outline',
        type: 'line',
        source: 'demographic-drive-time-area',
        layout: {},
        paint: {
          'line-color': '#18d5db',
          'line-width': 2,
        },
      });
    }
  };

  const generateCircleData = (
    center: [number, number],
    radiusMiles: number,
  ) => {
    const points = 64;
    const radiusMeters = radiusMiles * 1609.34;
    const coords = [];

    for (let i = 0; i < points; i++) {
      const angle = (i * 360) / points;
      const lat =
        center[1] + (radiusMeters / 111320) * Math.cos((angle * Math.PI) / 180);
      const lng =
        center[0] +
        (radiusMeters / (111320 * Math.cos((center[1] * Math.PI) / 180))) *
          Math.sin((angle * Math.PI) / 180);
      coords.push([lng, lat]);
    }
    coords.push(coords[0]); // Close the circle

    return {
      type: 'Feature',
      properties: {},
      geometry: {
        type: 'Polygon',
        coordinates: [coords],
      },
    };
  };

  // Effect for handling hover state
  useEffect(() => {
    if (currentHoverSection === 'area-demographic' && mapData) {
      showMapLayers();
    } else {
      clearMapLayers();
    }
  }, [currentHoverSection, mapData]);

  // Effect for fetching data
  useEffect(() => {
    const fetchData = async () => {
      if (
        currentRadiusMile &&
        currentPropertyAddress.latitude &&
        currentPropertyAddress.longitude
      ) {
        setLoading(true);
        if (calculationMode === 'Distance') {
          const distanceInMiles = Number(distance);
          // Generate circle data
          const circleData = generateCircleData(
            [currentPropertyAddress.longitude, currentPropertyAddress.latitude],
            distanceInMiles,
          );
          setMapData(circleData);

          const result = await getAreaDemographicInDistance({
            lat: currentPropertyAddress.latitude,
            lng: currentPropertyAddress.longitude,
            distance: distanceInMiles,
          });
          dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              areaDemographicData: result[0],
            },
          });
          setLoading(false);
        } else {
          const mapboxResult = await fetch(
            `https://api.mapbox.com/isochrone/v1/mapbox/driving-traffic/${currentPropertyAddress.longitude}%2C${currentPropertyAddress.latitude}?contours_minutes=${driveTime}&polygons=true&denoise=1&access_token=${MAPBOX_TOKEN}`,
          );

          const data = await mapboxResult.json();
          setMapData({
            type: 'Feature',
            properties: {},
            geometry: {
              type: 'Polygon',
              coordinates: [data.features[0].geometry.coordinates[0]],
            },
          });

          const driveTimeResult = await getAreaDemographicInDriveTime({
            body: {
              polygon: data.features[0].geometry.coordinates[0],
              lat: currentPropertyAddress.latitude,
              lng: currentPropertyAddress.longitude,
            },
          });
          dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              areaDemographicData: driveTimeResult[0],
            },
          });
          setLoading(false);
        }
      } else {
        clearMapLayers();
        setMapData(null);
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            areaDemographicData: null,
          },
        });
      }
    };
    fetchData();
  }, [
    currentRadiusMile,
    currentPropertyAddress,
    driveTime,
    calculationMode,
    distance,
    map,
  ]);

  function handleChange(value: string): void {
    setDriveTime(value);
  }
  const handleDriveTimeChange = (value: number | null) => {
    if (value === null) return;
    // Ensure value is within bounds
    const boundedValue = Math.min(Math.max(value, 5), 45);
    setDriveTime(boundedValue.toString());
  };

  const handleDistanceChange = (value: number | null) => {
    if (value === null) return;
    // Ensure value is within bounds
    const boundedValue = Math.min(Math.max(value, 1), 15);
    setDistance(boundedValue.toString());
  };

  const [CSVButtonLoading, setCSVButtonLoading] = useState<boolean>(false);
  const onClickCSVButton = () => {
    const mode = calculationMode === 'Distance' ? 'distance' : 'drive';
    setCSVButtonLoading(true);
    const params: any = {
      body: {
        lat: currentPropertyAddress.latitude,
        lng: currentPropertyAddress.longitude,
        distance: distance,
        mode: mode,
        time: driveTime,
      },
    };
    const fetchData = async () => {
      try {
        const response = await postAreaDemographicCSV(params);
        if (response) {
          // Create blob directly from the response string
          const blob = new Blob([response], {
            type: 'text/csv;charset=utf-8;',
          });

          // Create download URL
          const url = window.URL.createObjectURL(blob);

          // Create and trigger download
          const a = document.createElement('a');
          a.href = url;

          // Create filename with current date
          const date = new Date().toISOString();
          const filename = `area_demographic_${date}.csv`;
          a.download = filename;

          // Trigger download
          document.body.appendChild(a);
          a.click();

          // Cleanup
          window.URL.revokeObjectURL(url);
          a.remove();
        }
        setCSVButtonLoading(false);
      } catch (error) {
        console.error('Error downloading CSV:', error);
        // Handle error appropriately
      }
    };
    fetchData();
  };
  return (
    <div
      className={`${styles.cardWrapper} ${
        currentHoverSection === 'area-demographic' ? ' bg-slate-50' : 'bg-white'
      }`}
      onMouseEnter={() => {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            currentHoverSection: 'area-demographic',
          },
        });
      }}
      onMouseLeave={() => {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            currentHoverSection: null,
          },
        });
      }}
    >
      <div className="flex justify-between">
        <p className="text-lg mb-2 font-semibold">Demographics</p>
        <Button
          type="default"
          loading={CSVButtonLoading}
          onClick={onClickCSVButton}
          disabled={!areaDemographicData}
        >
          {' '}
          Export CSV
        </Button>
      </div>
      <div className="flex items-center">
        <Segmented<'Drive Time' | 'Distance'>
          options={['Drive Time', 'Distance']}
          onChange={(value) => {
            setCalculationMode(value);
          }}
        />
        {calculationMode === 'Drive Time' ? (
          <div className="ml-2 flex items-center">
            <Tooltip title="Range: 5-45 minutes" placement="top">
              <InputNumber
                min={5}
                max={45}
                value={Number(driveTime)}
                onChange={handleDriveTimeChange}
                style={{ width: 110 }}
                addonAfter="mins"
                controls
                keyboard
              />
            </Tooltip>
          </div>
        ) : (
          <div className="ml-2 flex items-center">
            <Tooltip title="Range: 1-15 miles" placement="top">
              <InputNumber
                min={1}
                max={15}
                value={Number(distance)}
                onChange={handleDistanceChange}
                style={{ width: 110 }}
                addonAfter="miles"
                controls
                keyboard
              />
            </Tooltip>
          </div>
        )}
      </div>
      {loading ? (
        <div className="mt-2">
          <Skeleton active />
        </div>
      ) : areaDemographicData ? (
        <>
          <div className="mt-2">
            <div className="grid grid-cols-1 gap-y-2">
              <div className="grid grid-cols-2">
                <p>Total Population: </p>
                <p>
                  {areaDemographicData?.total_population
                    ? formatNumber(
                        areaDemographicData.total_population.toFixed(0),
                      )
                    : 'N/A'}
                </p>
              </div>
              <div className="grid grid-cols-2">
                <p>Total Household: </p>
                <p>
                  {areaDemographicData?.total_households
                    ? formatNumber(
                        areaDemographicData.total_households.toFixed(0),
                      )
                    : 'N/A'}
                </p>
              </div>
              <div className="grid grid-cols-2">
                <p>5 Years Population Growth: </p>
                <p>
                  {areaDemographicData?.five_year_pop_growth
                    ? `${formatNumber(
                        areaDemographicData.five_year_pop_growth.toFixed(2),
                      )}%`
                    : 'N/A'}
                </p>
              </div>
              <div className="grid grid-cols-2">
                <p>5 Years Household Income Growth: </p>
                <p>
                  {areaDemographicData?.five_year_hh_income_growth
                    ? `${formatNumber(
                        areaDemographicData.five_year_hh_income_growth.toFixed(
                          2,
                        ),
                      )}%`
                    : 'N/A'}
                </p>
              </div>
              <div className="grid grid-cols-2">
                <p>Median Household Income: </p>
                <p>
                  {areaDemographicData?.median_hh_income
                    ? `$${formatNumber(
                        areaDemographicData.median_hh_income.toFixed(0),
                      )}`
                    : 'N/A'}
                </p>
              </div>
    
              <div className="grid grid-cols-2">
                <p>Median Family Income: </p>
                <p>
                  {areaDemographicData?.median_family_income
                    ? `$${formatNumber(
                        areaDemographicData.median_family_income.toFixed(0),
                      )}`
                    : 'N/A'}
                </p>
              </div>
            </div>
          </div>
        </>
      ) : (
        <>
          <p>Select a location on the map to see result.</p>
        </>
      )}
    </div>
  );
};

export default AreaDemographic;
