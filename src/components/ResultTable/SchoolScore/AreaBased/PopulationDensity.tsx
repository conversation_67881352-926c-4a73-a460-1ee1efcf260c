import {
  getPopulationDensity,
  getPopulationDensityClass,
} from '@/services/data';
import { Skeleton } from 'antd';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import styles from '../../resultTable.css';
import { formatNumber } from '../util';

const PopulationDensity = () => {
  const populationDensityData = useSelector(
    (state: any) => state.CMA.populationDensityData,
  );
  const dispatch = useDispatch();
  const map = useSelector((state: any) => state.CMA.map);
  const [loading, setLoading] = useState(false);
  const currentPropertyAddress = useSelector(
    (state: any) => state.CMA.currentPropertyAddress,
  );
  const currentRadiusMile = useSelector(
    (state: any) => state.CMA.currentRadiusMile,
  );
  const currentHoverSection = useSelector(
    (state: any) => state.CMA.currentHoverSection,
  );

  const clearMapLayers = () => {
    if (map && map.getLayer('density-circle-5')) {
      map.removeLayer('density-circle-5');
      map.removeLayer('density-outline-5');
      map.removeSource('density-area-5');
    }
    if (map && map.getLayer('density-circle-10')) {
      map.removeLayer('density-circle-10');
      map.removeLayer('density-outline-10');
      map.removeSource('density-area-10');
    }
  };

  const createCircle = (center: [number, number], radiusMiles: number) => {
    const points = 64;
    const radiusMeters = radiusMiles * 1609.34;
    const coords = [];

    for (let i = 0; i < points; i++) {
      const angle = (i * 360) / points;
      const lat =
        center[1] + (radiusMeters / 111320) * Math.cos((angle * Math.PI) / 180);
      const lng =
        center[0] +
        (radiusMeters / (111320 * Math.cos((center[1] * Math.PI) / 180))) *
          Math.sin((angle * Math.PI) / 180);
      coords.push([lng, lat]);
    }
    coords.push(coords[0]); // Close the circle
    return coords;
  };

  const updateMapCircles = (center: [number, number]) => {
    clearMapLayers();

    // Check if map style is loaded before adding sources/layers
    const addMapLayers = () => {
      // Add 5-mile circle (darker purple)
      map.addSource('density-area-5', {
        type: 'geojson',
        data: {
          type: 'Feature',
          properties: {},
          geometry: {
            type: 'Polygon',
            coordinates: [createCircle(center, 5)],
          },
        },
      });

      map.addLayer({
        id: 'density-circle-5',
        type: 'fill',
        source: 'density-area-5',
        layout: {
          visibility:
            currentHoverSection === 'area-pop-density' ? 'visible' : 'none',
        },
        paint: {
          'fill-color': '#7e22ce', // Purple-700
          'fill-opacity': 0.2,
        },
      });

      map.addLayer({
        id: 'density-outline-5',
        type: 'line',
        source: 'density-area-5',
        layout: {
          visibility:
            currentHoverSection === 'area-pop-density' ? 'visible' : 'none',
        },
        paint: {
          'line-color': '#7e22ce',
          'line-width': 2,
        },
      });

      // Add 10-mile circle (lighter purple)
      map.addSource('density-area-10', {
        type: 'geojson',
        data: {
          type: 'Feature',
          properties: {},
          geometry: {
            type: 'Polygon',
            coordinates: [createCircle(center, 10)],
          },
        },
      });

      map.addLayer({
        id: 'density-circle-10',
        type: 'fill',
        source: 'density-area-10',
        layout: {
          visibility:
            currentHoverSection === 'area-pop-density' ? 'visible' : 'none',
        },
        paint: {
          'fill-color': '#a855f7', // Purple-500
          'fill-opacity': 0.1,
        },
      });

      map.addLayer({
        id: 'density-outline-10',
        type: 'line',
        source: 'density-area-10',
        layout: {
          visibility:
            currentHoverSection === 'area-pop-density' ? 'visible' : 'none',
        },
        paint: {
          'line-color': '#a855f7',
          'line-width': 2,
        },
      });
    };

    // Check if the map style is loaded
    if (map.isStyleLoaded()) {
      addMapLayers();
    } else {
      // Wait for the style to load before adding layers
      map.once('styledata', addMapLayers);
    }
  };

  // Add visibility effect
  useEffect(() => {
    if (!map) return;

    const circle5Exists = map.getLayer('density-circle-5');
    const circle10Exists = map.getLayer('density-circle-10');

    if (circle5Exists) {
      map.setLayoutProperty(
        'density-circle-5',
        'visibility',
        currentHoverSection === 'area-pop-density' ? 'visible' : 'none',
      );
      map.setLayoutProperty(
        'density-outline-5',
        'visibility',
        currentHoverSection === 'area-pop-density' ? 'visible' : 'none',
      );
    }

    if (circle10Exists) {
      map.setLayoutProperty(
        'density-circle-10',
        'visibility',
        currentHoverSection === 'area-pop-density' ? 'visible' : 'none',
      );
      map.setLayoutProperty(
        'density-outline-10',
        'visibility',
        currentHoverSection === 'area-pop-density' ? 'visible' : 'none',
      );
    }
  }, [currentHoverSection, map]);

  useEffect(() => {
    const fetchData = async () => {
      if (currentPropertyAddress.latitude && currentPropertyAddress.longitude) {
        setLoading(true);

        // Update map circles
        if (map) {
          updateMapCircles([
            currentPropertyAddress.longitude,
            currentPropertyAddress.latitude,
          ]);
        }

        const densityClassResult = await getPopulationDensityClass({
          lat: currentPropertyAddress.latitude,
          lng: currentPropertyAddress.longitude,
        });

        const populationDensityFive = await getPopulationDensity(
          {
            lat: currentPropertyAddress.latitude,
            lng: currentPropertyAddress.longitude,
          },
          5,
        );
        const populationDensityTen = await getPopulationDensity(
          {
            lat: currentPropertyAddress.latitude,
            lng: currentPropertyAddress.longitude,
          },
          10,
        );

        const result = {
          class: densityClassResult,
          densityFiveRadius: populationDensityFive,
          densityTenRadius: populationDensityTen,
        };

        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            populationDensityData: result,
          },
        });
        setLoading(false);
      } else {
        clearMapLayers();
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            populationDensityData: null,
          },
        });
      }
    };
    fetchData();
  }, [currentPropertyAddress, currentRadiusMile]);
  return (
    <div
      className={`${styles.cardWrapper}  ${
        currentHoverSection === 'area-pop-density' ? ' bg-slate-50' : 'bg-white'
      }`}
      onMouseEnter={() => {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            currentHoverSection: 'area-pop-density',
          },
        });
      }}
      onMouseLeave={() => {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            currentHoverSection: null,
          },
        });
      }}
    >
      <p className="text-lg font-semibold mb-2">Population Density</p>
      {loading ? (
        <Skeleton active />
      ) : populationDensityData ? (
        <>
          {/* Income */}
          <div>
            <div className="grid grid-cols-1 gap-y-2">
              <div className="grid grid-cols-2">
                <p>Polulation Density Class: </p>
                <p>{populationDensityData.class}</p>
              </div>

              <div className="grid grid-cols-2">
                <p>Population Density in 5 miles: </p>

                <p>
                  {populationDensityData.densityFiveRadius &&
                    formatNumber(
                      populationDensityData.densityFiveRadius?.toFixed(0),
                    )}{' '}
                  / square mile
                </p>
              </div>
              <div className="grid grid-cols-2">
                <p>Population Density in 10 miles: </p>
                <p>
                  {populationDensityData.densityTenRadius &&
                    formatNumber(
                      populationDensityData.densityTenRadius?.toFixed(0),
                    )}{' '}
                  / square mile
                </p>
              </div>
            </div>
          </div>
        </>
      ) : (
        <>
          <p>Select a location on the map to see result.</p>
        </>
      )}
    </div>
  );
};

export default PopulationDensity;
