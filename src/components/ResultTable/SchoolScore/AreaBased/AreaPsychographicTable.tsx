// AreaPsychographicTable.tsx - Updated main component
import { MAPBOX_TOKEN } from '@/constants';
import {
  getPsychographicDataDetails,
  getPsychographicDataDetailsDriveTime,
} from '@/services/data';
import {
  ArrowDownOutlined,
  ArrowUpOutlined,
  DownloadOutlined,
  MinusOutlined,
} from '@ant-design/icons';
import { InputNumber, Segmented, Switch, Table, Tooltip } from 'antd';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import styles from '../../resultTable.css';
import downloadLink from '../downloadLink';
import { useSegmentModal } from './hooks/useSegmentModal';
import SegmentModal from './SegmentModal';

const AreaPsychographicTable = () => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState<boolean>(false);
  const [calculationMode, setCalculationMode] = useState<string>('Drive Time');
  const [driveTime, setDriveTime] = useState<string>('10');
  const [distance, setDistance] = useState<string>('3');
  const [showMoreDetails, setShowMoreDetails] = useState<boolean>(false);

  // Redux selectors
  const currentPropertyAddress = useSelector(
    (state: any) => state.CMA.currentPropertyAddress,
  );
  const map = useSelector((state: any) => state.CMA.map);
  const currentRadiusMile = useSelector(
    (state: any) => state.CMA.currentRadiusMile,
  );
  const psychographicDataInDetails = useSelector(
    (state: any) => state.CMA.psychographicDataInDetails,
  );
  const currentHoverSection = useSelector(
    (state: any) => state.CMA.currentHoverSection,
  );
  const displaySegmentHeatmapPopup = useSelector(
    (state: any) => state.CMA.displaySegmentHeatmapPopup,
  );
  const selectedSegment = useSelector(
    (state: any) => state.CMA.selectedSegment,
  );

  // Use the segment modal hook
  const {
    segmentOverview,
    segmentOverviewDma,
    segmentTableLoading,
    marketLevel,
    segmentMaxValue,
    isLoadingRange,
    currentSliderMin,
    currentSliderMax,
    setMarketLevel,
    openSegmentModal,
    closeSegmentModal,
    updateSliderRange,
  } = useSegmentModal({ map });

  // Map layer management
  const clearMapLayers = () => {
    if (map && map.getLayer('drive-time-polygon')) {
      map.removeLayer('drive-time-polygon');
      map.removeLayer('drive-time-outline');
      map.removeSource('drive-time-area');
    }
    if (map && map.getLayer('radius-circle')) {
      map.removeLayer('radius-circle');
      map.removeLayer('radius-outline');
      map.removeSource('radius-area');
    }
  };

  const updateMapPolygon = (coordinates: number[][], map: any) => {
    clearMapLayers();

    // Check if map style is loaded before adding sources/layers
    const addMapLayers = () => {
      map.addSource('drive-time-area', {
        type: 'geojson',
        data: {
          type: 'Feature',
          properties: {},
          geometry: {
            type: 'Polygon',
            coordinates: [coordinates],
          },
        },
      });

      map.addLayer({
        id: 'drive-time-polygon',
        type: 'fill',
        source: 'drive-time-area',
        layout: {
          visibility:
            currentHoverSection === 'area-psychographic' ? 'visible' : 'none',
        },
        paint: {
          'fill-color': '#ff4400',
          'fill-opacity': 0.2,
        },
      });

      map.addLayer({
        id: 'drive-time-outline',
        type: 'line',
        source: 'drive-time-area',
        layout: {
          visibility:
            currentHoverSection === 'area-psychographic' ? 'visible' : 'none',
        },
        paint: {
          'line-color': '#ff4400',
          'line-width': 2,
        },
      });
    };

    // Check if the map style is loaded
    if (map.isStyleLoaded()) {
      addMapLayers();
    } else {
      // Wait for the style to load before adding layers
      map.once('styledata', addMapLayers);
    }
  };

  const updateMapCircle = (center: [number, number], radiusMiles: number) => {
    clearMapLayers();

    const points = 64;
    const radiusMeters = radiusMiles * 1609.34;
    const coords = [];

    for (let i = 0; i < points; i++) {
      const angle = (i * 360) / points;
      const lat =
        center[1] + (radiusMeters / 111320) * Math.cos((angle * Math.PI) / 180);
      const lng =
        center[0] +
        (radiusMeters / (111320 * Math.cos((center[1] * Math.PI) / 180))) *
          Math.sin((angle * Math.PI) / 180);
      coords.push([lng, lat]);
    }
    coords.push(coords[0]);

    // Check if map style is loaded before adding sources/layers
    const addMapLayers = () => {
      map.addSource('radius-area', {
        type: 'geojson',
        data: {
          type: 'Feature',
          properties: {},
          geometry: {
            type: 'Polygon',
            coordinates: [coords],
          },
        },
      });

      map.addLayer({
        id: 'radius-circle',
        type: 'fill',
        source: 'radius-area',
        layout: {
          visibility:
            currentHoverSection === 'area-psychographic' ? 'visible' : 'none',
        },
        paint: {
          'fill-color': '#ff4400',
          'fill-opacity': 0.2,
        },
      });

      map.addLayer({
        id: 'radius-outline',
        type: 'line',
        source: 'radius-area',
        layout: {
          visibility:
            currentHoverSection === 'area-psychographic' ? 'visible' : 'none',
        },
        paint: {
          'line-color': '#ff4400',
          'line-width': 2,
        },
      });
    };

    // Check if the map style is loaded
    if (map.isStyleLoaded()) {
      addMapLayers();
    } else {
      // Wait for the style to load before adding layers
      map.once('styledata', addMapLayers);
    }
  };

  // Data fetching effect
  useEffect(() => {
    const fetchData = async () => {
      if (
        currentRadiusMile &&
        currentPropertyAddress.latitude &&
        currentPropertyAddress.longitude
      ) {
        setLoading(true);
        if (calculationMode === 'Distance') {
          const distanceInMiles = Number(distance);
          updateMapCircle(
            [currentPropertyAddress.longitude, currentPropertyAddress.latitude],
            distanceInMiles,
          );

          const result = await getPsychographicDataDetails({
            lat: currentPropertyAddress.latitude,
            lng: currentPropertyAddress.longitude,
            distance: distanceInMiles * 1609.34,
          });

          const sliceSize = showMoreDetails ? 20 : 5;
          dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              psychographicDataInDetails: result.slice(0, sliceSize),
            },
          });
          setLoading(false);
        } else {
          const mapboxResult = await fetch(
            `https://api.mapbox.com/isochrone/v1/mapbox/driving-traffic/${currentPropertyAddress.longitude}%2C${currentPropertyAddress.latitude}?contours_minutes=${driveTime}&polygons=true&denoise=1&access_token=${MAPBOX_TOKEN}`,
          );

          const data = await mapboxResult.json();
          const coordinates = data.features[0].geometry.coordinates[0];

          if (map) {
            updateMapPolygon(coordinates, map);
          }

          const driveTimeResult = await getPsychographicDataDetailsDriveTime({
            body: {
              polygon: coordinates,
              lat: currentPropertyAddress.latitude,
              lng: currentPropertyAddress.longitude,
            },
          });

          const sliceSize = showMoreDetails ? 20 : 5;
          dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              psychographicDataInDetails: driveTimeResult.slice(0, sliceSize),
            },
          });
          setLoading(false);
        }
      } else {
        clearMapLayers();
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            psychographicDataInDetails: null,
          },
        });
      }
    };
    fetchData();
  }, [
    currentRadiusMile,
    currentPropertyAddress,
    driveTime,
    calculationMode,
    distance,
    map,
    showMoreDetails,
  ]);

  // Visibility handling
  useEffect(() => {
    if (!map) return;

    const driveTimeLayerExists = map.getLayer('drive-time-polygon');
    const radiusLayerExists = map.getLayer('radius-circle');

    if (driveTimeLayerExists) {
      map.setLayoutProperty(
        'drive-time-polygon',
        'visibility',
        currentHoverSection === 'area-psychographic' ? 'visible' : 'none',
      );
      map.setLayoutProperty(
        'drive-time-outline',
        'visibility',
        currentHoverSection === 'area-psychographic' ? 'visible' : 'none',
      );
    }

    if (radiusLayerExists) {
      map.setLayoutProperty(
        'radius-circle',
        'visibility',
        currentHoverSection === 'area-psychographic' ? 'visible' : 'none',
      );
      map.setLayoutProperty(
        'radius-outline',
        'visibility',
        currentHoverSection === 'area-psychographic' ? 'visible' : 'none',
      );
    }
  }, [currentHoverSection, map]);

  // Event handlers
  const handleSegmentClick = (segmentName: string) => {
    console.log('segment debug', {
      selectedSegment: segmentName.split('_')[0].toUpperCase(),
      displaySegmentHeatmapPopup,
    });

    openSegmentModal(segmentName);
  };

  const handleDriveTimeChange = (value: number | null) => {
    if (value === null) return;
    const boundedValue = Math.min(Math.max(value, 5), 45);
    setDriveTime(boundedValue.toString());
  };

  const handleDistanceChange = (value: number | null) => {
    if (value === null) return;
    const boundedValue = Math.min(Math.max(value, 1), 15);
    setDistance(boundedValue.toString());
  };

  // Map interaction handlers for modal
  const addHoverPolygon = (geom: string, marketName: string) => {
    if (!map || !geom) return;

    try {
      const wktToGeoJSON = (wkt: string) => {
        const coordsMatch = wkt.match(
          /MULTIPOLYGON\s*\(\s*\(\s*\(([^)]+)\)\s*\)\s*\)/i,
        );
        if (!coordsMatch || !coordsMatch[1]) {
          console.warn(`Invalid WKT format for ${marketName}`);
          return null;
        }

        const points = coordsMatch[1].split(',').map((pair) => {
          const [lng, lat] = pair.trim().split(/\s+/).map(Number);
          return [lng, lat];
        });

        if (
          points[0][0] !== points[points.length - 1][0] ||
          points[0][1] !== points[points.length - 1][1]
        ) {
          points.push(points[0]);
        }

        return {
          type: 'MultiPolygon',
          coordinates: [[points]],
        };
      };

      const geoJSON = wktToGeoJSON(geom);
      if (!geoJSON) return;

      removeHoverPolygon();

      map.addSource('hover-polygon-source', {
        type: 'geojson',
        data: {
          type: 'Feature',
          properties: { name: marketName },
          geometry: geoJSON,
        },
      });

      map.addLayer({
        id: 'hover-polygon-fill',
        type: 'fill',
        source: 'hover-polygon-source',
        paint: {
          'fill-color': '#1890ff',
          'fill-opacity': 0.3,
        },
      });

      map.addLayer({
        id: 'hover-polygon-outline',
        type: 'line',
        source: 'hover-polygon-source',
        paint: {
          'line-color': '#1890ff',
          'line-width': 2,
        },
      });
    } catch (error) {
      console.error(`Error adding hover polygon for ${marketName}:`, error);
    }
  };

  const removeHoverPolygon = () => {
    if (!map) return;

    if (map.getLayer('hover-polygon-fill')) {
      map.removeLayer('hover-polygon-fill');
    }
    if (map.getLayer('hover-polygon-outline')) {
      map.removeLayer('hover-polygon-outline');
    }
    if (map.getSource('hover-polygon-source')) {
      map.removeSource('hover-polygon-source');
    }
  };

  const zoomToState = (marketName: string, geom: string, type: string) => {
    const parsePoint = (geom: string) => {
      if (!geom) return null;

      try {
        const pointMatch = geom.match(
          /POINT\s*\(\s*([-.\d\s]+)\s+([-.\d\s]+)\s*\)/i,
        );
        if (!pointMatch || !pointMatch[1] || !pointMatch[2]) {
          console.warn(`Invalid WKT POINT format for ${marketName}`);
          return null;
        }

        const [lng, lat] = [
          parseFloat(pointMatch[1]),
          parseFloat(pointMatch[2]),
        ];
        if (isNaN(lng) || isNaN(lat)) {
          console.warn(`Invalid coordinates for ${marketName}`);
          return null;
        }

        return [lng, lat];
      } catch (error) {
        console.error(`Error parsing POINT for ${marketName}:`, error);
        return null;
      }
    };

    const coords = parsePoint(geom);
    if (coords && map) {
      map.flyTo({
        center: coords,
        zoom: type === 'state' ? 6 : 7,
        duration: 1000,
      });
    } else {
      console.warn(`Coordinates not found for market: ${marketName}`);
    }
  };

  // Data calculations
  const calculateTotals = () => {
    if (
      !psychographicDataInDetails ||
      psychographicDataInDetails.length === 0
    ) {
      return {
        totalPercentage: 0,
        totalPercentage2024: 0,
        totalPercentage2022: 0,
      };
    }

    return psychographicDataInDetails.reduce(
      (acc, item) => {
        return {
          totalPercentage: acc.totalPercentage + item.percentage,
          totalPercentage2024: acc.totalPercentage2024 + item.percentage2024,
          totalPercentage2022: acc.totalPercentage2022 + item.percentage2022,
        };
      },
      { totalPercentage: 0, totalPercentage2024: 0, totalPercentage2022: 0 },
    );
  };

  const calculateRunningTotals = () => {
    if (
      !psychographicDataInDetails ||
      psychographicDataInDetails.length === 0
    ) {
      return [];
    }

    let runningPercentage = 0;
    let runningPercentage2024 = 0;
    let runningPercentage2022 = 0;

    return psychographicDataInDetails.map((item) => {
      runningPercentage += item.percentage;
      runningPercentage2024 += item.percentage2024;
      runningPercentage2022 += item.percentage2022;

      return {
        ...item,
        runningPercentage,
        runningPercentage2024,
        runningPercentage2022,
      };
    });
  };

  const totals = calculateTotals();
  const dataWithRunningTotals = calculateRunningTotals();

  // Table columns
  const getBasicColumns = () => [
    {
      title: (
        <div className="">
          Segment
          <span
            className=" text-gray-500 text-xs pl-1"
            style={{ fontSize: '10px' }}
          >
            (Click segment to turn on heatmap)
          </span>
        </div>
      ),
      dataIndex: 'column_name',
      key: 'column_name',
      width: 280,
      render: (text: string, record: any) => {
        if (record.isTotal) {
          return <p className="font-bold">Total</p>;
        }
        return (
          <div
            className=" cursor-pointer"
            onClick={() => handleSegmentClick(text)}
          >
            <p>{formatSegmentName(text)}</p>
          </div>
        );
      },
    },
    {
      title: '2025',
      dataIndex: 'percentage',
      key: 'percentage',
      render: (percentage: number, record: any) => {
        if (record.isTotal) {
          return (
            <span className="font-bold">
              {totals.totalPercentage.toFixed(2)}%
            </span>
          );
        }
        return (
          <Tooltip
            title={`Running total: ${record.runningPercentage.toFixed(2)}%`}
          >
            <span>{percentage.toFixed(2)}% </span>
          </Tooltip>
        );
      },
    },
    {
      title: 'current vs 2021',
      key: 'vs2021',
      className: 'bg-gray-100',
      onCell: () => ({
        style: { backgroundColor: '#f5f5f5' },
      }),
      render: (_: string, record: any) => {
        if (record.isTotal) {
          return null;
        }

        const difference = record.percentage - record.percentage2022;
        let arrow;
        const textColor =
          difference > 0 ? '#52c41a' : difference < 0 ? '#f5222d' : '#8c8c8c';

        if (difference > 0) {
          arrow = (
            <ArrowUpOutlined
              style={{ color: '#52c41a', width: '16px', textAlign: 'center' }}
            />
          );
        } else if (difference < 0) {
          arrow = (
            <ArrowDownOutlined
              style={{ color: '#f5222d', width: '16px', textAlign: 'center' }}
            />
          );
        } else {
          arrow = (
            <MinusOutlined
              style={{ color: '#8c8c8c', width: '16px', textAlign: 'center' }}
            />
          );
        }

        return (
          <div className="flex items-center whitespace-nowrap">
            <span
              style={{
                margin: '0 4px',
                display: 'inline-flex',
                justifyContent: 'left',
                width: '12px',
              }}
            >
              {arrow}
            </span>
            <span style={{ color: textColor }}>
              {difference > 0 ? '+' : ''}
              {difference.toFixed(2)}%
            </span>
          </div>
        );
      },
    },
    {
      title: '2021',
      dataIndex: 'percentage2022',
      key: 'percentage2022',
      render: (percentage2022: number, record: any) => {
        if (record.isTotal) {
          return (
            <span className="font-bold">
              {totals.totalPercentage2022.toFixed(2)}%
            </span>
          );
        }
        return (
          <Tooltip
            title={`Running total: ${record.runningPercentage2022.toFixed(2)}%`}
          >
            <span>{percentage2022.toFixed(2)}% </span>
          </Tooltip>
        );
      },
    },
    {
      title: 'Segment Overview',
      dataIndex: 'column_name',
      key: 'segment_overview',
      render: (text: string, record: any) => {
        if (record.isTotal) {
          return null;
        }
        return (
          <a
            href={downloadLink[record.column_name.split('_')[0].toUpperCase()]}
            target="_blank"
            className="text-blue-500"
          >
            Download
          </a>
        );
      },
    },
  ];

  const getDetailedColumns = () => [
    {
      title: (
        <div className="">
          Segment
          <span
            className=" text-gray-500 text-xs pl-1 select-none"
            style={{ fontSize: '10px' }}
          >
            (Click segment to turn on heatmap)
          </span>
        </div>
      ),
      dataIndex: 'column_name',
      key: 'column_name',
      width: 260,
      render: (text: string, record: any) => {
        if (record.isTotal) {
          return <p className="font-bold">Total</p>;
        }
        return (
          <div
            className="flex justify-between items-center cursor-pointer"
            onClick={() => handleSegmentClick(text)}
          >
            <p>{formatSegmentName(text)}</p>
            <a
              href={
                downloadLink[record.column_name.split('_')[0].toUpperCase()]
              }
              target="_blank"
              className="text-blue-500"
            >
              <DownloadOutlined />
            </a>
          </div>
        );
      },
    },
    {
      title: '2025',
      dataIndex: 'percentage',
      key: 'percentage',
      render: (percentage: number, record: any) => {
        if (record.isTotal) {
          return (
            <span className="font-bold">
              {totals.totalPercentage.toFixed(2)}%
            </span>
          );
        }
        return <span>{percentage.toFixed(2)}%</span>;
      },
    },
    {
      title: 'current vs 2024',
      key: 'vs2024',
      className: 'bg-gray-100',
      render: (_: string, record: any) => {
        if (record.isTotal) {
          return null;
        }

        const difference = record.percentage - record.percentage2024;
        const textColor =
          difference > 0 ? '#52c41a' : difference < 0 ? '#f5222d' : '#8c8c8c';
        let arrow;
        if (difference > 0) {
          arrow = (
            <ArrowUpOutlined
              style={{ color: textColor, width: '16px', textAlign: 'center' }}
            />
          );
        } else if (difference < 0) {
          arrow = (
            <ArrowDownOutlined
              style={{ color: textColor, width: '16px', textAlign: 'center' }}
            />
          );
        } else {
          arrow = (
            <MinusOutlined
              style={{ color: textColor, width: '16px', textAlign: 'center' }}
            />
          );
        }

        return (
          <div className="flex items-center whitespace-nowrap">
            <span
              style={{
                margin: '0 4px',
                display: 'inline-flex',
                justifyContent: 'left',
                width: '12px',
              }}
            >
              {arrow}
            </span>
            <span style={{ color: textColor }}>
              {difference > 0 ? '+' : ''}
              {difference.toFixed(2)}%
            </span>
          </div>
        );
      },
    },
    {
      title: '2024',
      dataIndex: 'percentage2024',
      key: 'percentage2024',
      render: (percentage2024: number, record: any) => {
        if (record.isTotal) {
          return (
            <span className="font-bold">
              {totals.totalPercentage2024.toFixed(2)}%
            </span>
          );
        }
        return <span>{percentage2024.toFixed(2)}%</span>;
      },
    },
    {
      title: 'current vs 2021',
      key: 'vs2021',
      className: 'bg-gray-100',
      render: (_: string, record: any) => {
        if (record.isTotal) {
          return null;
        }

        const difference = record.percentage - record.percentage2022;
        let arrow;
        const textColor =
          difference > 0 ? '#52c41a' : difference < 0 ? '#f5222d' : '#8c8c8c';

        if (difference > 0) {
          arrow = (
            <ArrowUpOutlined
              style={{ color: textColor, width: '16px', textAlign: 'center' }}
            />
          );
        } else if (difference < 0) {
          arrow = (
            <ArrowDownOutlined
              style={{ color: textColor, width: '16px', textAlign: 'center' }}
            />
          );
        } else {
          arrow = (
            <MinusOutlined
              style={{ color: textColor, width: '16px', textAlign: 'center' }}
            />
          );
        }

        return (
          <div className="flex items-center whitespace-nowrap">
            <span
              style={{
                margin: '0 4px',
                display: 'inline-flex',
                justifyContent: 'left',
                width: '12px',
              }}
            >
              {arrow}
            </span>
            <span style={{ color: textColor }}>
              {difference > 0 ? '+' : ''}
              {difference.toFixed(2)}%
            </span>
          </div>
        );
      },
    },
    {
      title: '2021',
      dataIndex: 'percentage2022',
      key: 'percentage2022',
      render: (percentage2022: number, record: any) => {
        if (record.isTotal) {
          return (
            <span className="font-bold">
              {totals.totalPercentage2022.toFixed(2)}%
            </span>
          );
        }
        return <span>{percentage2022.toFixed(2)}%</span>;
      },
    },
  ];

  const getTableDataSource = () => {
    if (!psychographicDataInDetails) return [];
    return [...dataWithRunningTotals, { isTotal: true }];
  };

  return (
    <div
      className={`${styles.cardWrapper} ${
        currentHoverSection === 'area-psychographic'
          ? ' bg-slate-50'
          : 'bg-white'
      }`}
      onMouseEnter={() => {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            currentHoverSection: 'area-psychographic',
          },
        });
      }}
      onMouseLeave={() => {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            currentHoverSection: null,
          },
        });
      }}
    >
      <div className="flex justify-between items-center mb-2">
        <p className="text-lg font-semibold">
          {showMoreDetails ? 'Top 20 Segments' : 'Top 5 Segments'}
        </p>
        <div className="flex items-center">
          <span className="mr-2 text-sm">Show more details</span>
          <Switch
            size="small"
            checked={showMoreDetails}
            onChange={(checked) => setShowMoreDetails(checked)}
          />
        </div>
      </div>

      <div className="flex items-center">
        <Segmented<string>
          options={['Drive Time', 'Distance']}
          onChange={(value) => {
            setCalculationMode(value);
          }}
        />
        {calculationMode === 'Drive Time' ? (
          <div className="ml-2 flex items-center">
            <Tooltip title="Range: 5-45 minutes" placement="top">
              <InputNumber
                min={5}
                max={45}
                value={Number(driveTime)}
                onChange={handleDriveTimeChange}
                style={{ width: 110 }}
                addonAfter="mins"
                controls
                keyboard
              />
            </Tooltip>
          </div>
        ) : (
          <div className="ml-2 flex items-center">
            <Tooltip title="Range: 1-15 miles" placement="top">
              <InputNumber
                min={1}
                max={15}
                value={Number(distance)}
                onChange={handleDistanceChange}
                style={{ width: 110 }}
                addonAfter="miles"
                controls
                keyboard
              />
            </Tooltip>
          </div>
        )}
      </div>

      <SegmentModal
        visible={displaySegmentHeatmapPopup}
        selectedSegment={selectedSegment}
        segmentOverview={segmentOverview}
        segmentOverviewDma={segmentOverviewDma}
        segmentTableLoading={segmentTableLoading}
        marketLevel={marketLevel}
        segmentMaxValue={segmentMaxValue}
        isLoadingRange={isLoadingRange}
        currentSliderMin={currentSliderMin}
        currentSliderMax={currentSliderMax}
        onClose={closeSegmentModal}
        onMarketLevelChange={setMarketLevel}
        onSliderChange={updateSliderRange}
        onMarketClick={zoomToState}
        onMarketHover={addHoverPolygon}
        onMarketLeave={removeHoverPolygon}
      />

      <Table
        dataSource={getTableDataSource()}
        columns={showMoreDetails ? getDetailedColumns() : getBasicColumns()}
        loading={loading}
        size="small"
        className="mt-4"
        pagination={false}
        rowClassName={(record: any) => (record.isTotal ? 'bg-gray-100' : '')}
      />
    </div>
  );
};

export default AreaPsychographicTable;

export interface PsychographicDataDetails {
  column_name: string;
  percentage: number;
  percentage2024: number;
  percentage2022: number;
  old_percentage?: number;
}

function formatSegmentName(input: string): string {
  try {
    if (!input || !input.includes('_-_')) {
      throw new Error('Invalid input format');
    }

    const [prefix, ...descriptionParts] = input.split('_-_');
    const description = descriptionParts.join('_-_');

    return `${prefix.toUpperCase()} - ${description
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ')}`;
  } catch (error) {
    console.error('Error formatting segment name:', error);
    return input;
  }
}
