// hooks/useSegmentModal.ts
import {
  getSegmentOverview,
  getSegmentOverviewDma,
  getSegmentRange,
} from '@/services/data';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'umi';

interface UseSegmentModalProps {
  map: any;
}

export const useSegmentModal = ({ map }: UseSegmentModalProps) => {
  const dispatch = useDispatch();
  const [segmentOverview, setSegmentOverview] = useState<any>(null);
  const [segmentOverviewDma, setSegmentOverviewDma] = useState<any>(null);
  const [segmentTableLoading, setSegmentTableLoading] =
    useState<boolean>(false);
  const [marketLevel, setMarketLevel] = useState('metro');
  const [segmentMaxValue, setSegmentMaxValue] = useState<number>(100);
  const [isLoadingRange, setIsLoadingRange] = useState<boolean>(false);

  const selectedSegment = useSelector(
    (state: any) => state.CMA.selectedSegment,
  );
  const displaySegmentHeatmapPopup = useSelector(
    (state: any) => state.CMA.displaySegmentHeatmapPopup,
  );
  const selectedSegmentHeatmapMinValue = useSelector(
    (state: any) => state.CMA.selectedSegmentHeatmapMinValue,
  );
  const selectedSegmentHeatmapMaxValue = useSelector(
    (state: any) => state.CMA.selectedSegmentHeatmapMaxValue,
  );

  // Reset and fetch data when segment changes
  useEffect(() => {
    if (!selectedSegment) {
      resetModalState();
      return;
    }

    fetchSegmentData(selectedSegment);
  }, [selectedSegment]);

  const resetModalState = () => {
    setSegmentOverview(null);
    setSegmentOverviewDma(null);
    setSegmentMaxValue(100);
    setIsLoadingRange(false);
    setSegmentTableLoading(false);
  };

  const fetchSegmentData = async (segment: string) => {
    setSegmentTableLoading(true);
    setIsLoadingRange(true);

    try {
      // Fetch range first to get max value
      const range: any = await getSegmentRange({ segmnetKey: segment });

      if (range) {
        const newMaxValue = Math.ceil(range.maxValue * 100);
        setSegmentMaxValue(newMaxValue);

        // Update Redux state with new max value, ensuring current values don't exceed it
        const currentMin = selectedSegmentHeatmapMinValue || 1;
        const currentMax = selectedSegmentHeatmapMaxValue || newMaxValue;

        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            selectedSegmentHeatmapMinValue: Math.min(currentMin, newMaxValue),
            selectedSegmentHeatmapMaxValue: Math.min(currentMax, newMaxValue),
          },
        });
      }

      // Fetch overview data in parallel
      const [overviewResult, overviewDmaResult] = await Promise.all([
        getSegmentOverview({ segmnetKey: segment }),
        getSegmentOverviewDma({ segmnetKey: segment }),
      ]);

      setSegmentOverview(overviewResult);
      setSegmentOverviewDma(overviewDmaResult);

      // Zoom map
      if (map) {
        map.zoomTo(7, { duration: 1000 });
      }
    } catch (error) {
      console.error('Error fetching segment data:', error);
    } finally {
      setSegmentTableLoading(false);
      setIsLoadingRange(false);
    }
  };

  const openSegmentModal = (segmentName: string) => {
    const segmentKey = segmentName.split('_')[0].toUpperCase();

    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        selectedSegment: segmentKey,
        displaySelectedSegmentHeatmap: true,
        displaySegmentHeatmapPopup: true,
        // Reset to default values - will be updated when data loads
        selectedSegmentHeatmapMinValue: 1,
        selectedSegmentHeatmapMaxValue: null,
      },
    });

    if (map && map.getZoom() > 7) {
      map.zoomTo(7, { duration: 1000 });
    }
  };

  const closeSegmentModal = () => {
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        displaySegmentHeatmapPopup: false,
        displaySelectedSegmentHeatmap: false,
        selectedSegment: null,
        selectedSegmentHeatmapMinValue: 1,
        selectedSegmentHeatmapMaxValue: null,
      },
    });
    resetModalState();
  };

  const updateSliderRange = (values: [number, number]) => {
    const [min, max] = values;

    // Ensure values don't exceed the segment's max value
    const constrainedMin = Math.max(1, Math.min(min, segmentMaxValue));
    const constrainedMax = Math.max(
      constrainedMin,
      Math.min(max, segmentMaxValue),
    );

    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        selectedSegmentHeatmapMinValue: constrainedMin,
        selectedSegmentHeatmapMaxValue: constrainedMax,
      },
    });
  };

  return {
    // State
    segmentOverview,
    segmentOverviewDma,
    segmentTableLoading,
    marketLevel,
    segmentMaxValue,
    isLoadingRange,

    // Computed values
    currentSliderMin: selectedSegmentHeatmapMinValue || 1,
    currentSliderMax: selectedSegmentHeatmapMaxValue || segmentMaxValue,

    // Actions
    setMarketLevel,
    openSegmentModal,
    closeSegmentModal,
    updateSliderRange,
  };
};
