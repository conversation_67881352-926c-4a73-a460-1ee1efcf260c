import { MAPBOX_TOKEN } from '@/constants';
import {
  getMajorEmployers,
  getMajorEmployersnDriveTime,
  postMajorEmployersCSV,
} from '@/services/data';
import {
  Button,
  InputNumber,
  Segmented,
  Select,
  Skeleton,
  Tooltip,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import styles from '../../resultTable.css';
import MajorEmployersTable from './MajorEmployersTable';

const MajorEmployer = () => {
  const majorEmployersData = useSelector(
    (state: any) => state.CMA.majorEmployersData,
  );
  const map = useSelector((state: any) => state.CMA.map);
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [mapData, setMapData] = useState<any>(null);
  const currentPropertyAddress = useSelector(
    (state: any) => state.CMA.currentPropertyAddress,
  );
  const currentHoverSection = useSelector(
    (state: any) => state.CMA.currentHoverSection,
  );

  const [calculationMode, setCalculationMode] = useState<string>('Drive Time');
  const [driveTime, setDriveTime] = useState<string>('10');
  const [distance, setDistance] = useState<string>('3');

  const clearMapLayers = () => {
    // Remove circle layers
    if (map && map.getLayer('employers-circle')) {
      map.removeLayer('employers-circle');
      map.removeLayer('employers-outline');
      map.removeSource('employers-area');
    }
    // Remove drive time layers
    if (map && map.getLayer('employers-drive-time-polygon')) {
      map.removeLayer('employers-drive-time-polygon');
      map.removeLayer('employers-drive-time-outline');
      map.removeSource('employers-drive-time-area');
    }
  };

  const updateMapCircle = () => {
    if (!mapData || calculationMode !== 'Distance') return;

    clearMapLayers();

    // Check if map style is loaded before adding sources/layers
    const addMapLayers = () => {
      // Add circle source and layers
      map.addSource('employers-area', {
        type: 'geojson',
        data: mapData,
      });

      map.addLayer({
        id: 'employers-circle',
        type: 'fill',
        source: 'employers-area',
        layout: {
          visibility:
            currentHoverSection === 'area-major-employers' ? 'visible' : 'none',
        },
        paint: {
          'fill-color': '#2563eb',
          'fill-opacity': 0.2,
        },
      });

      map.addLayer({
        id: 'employers-outline',
        type: 'line',
        source: 'employers-area',
        layout: {
          visibility:
            currentHoverSection === 'area-major-employers' ? 'visible' : 'none',
        },
        paint: {
          'line-color': '#2563eb',
          'line-width': 2,
        },
      });
    };

    // Check if the map style is loaded
    if (map.isStyleLoaded()) {
      addMapLayers();
    } else {
      // Wait for the style to load before adding layers
      map.once('styledata', addMapLayers);
    }
  };

  const updateMapDriveTime = () => {
    if (!mapData || calculationMode !== 'Drive Time') return;

    clearMapLayers();

    // Check if map style is loaded before adding sources/layers
    const addMapLayers = () => {
      // Add drive time source and layers
      map.addSource('employers-drive-time-area', {
        type: 'geojson',
        data: mapData,
      });

      map.addLayer({
        id: 'employers-drive-time-polygon',
        type: 'fill',
        source: 'employers-drive-time-area',
        layout: {
          visibility:
            currentHoverSection === 'area-major-employers' ? 'visible' : 'none',
        },
        paint: {
          'fill-color': '#2563eb',
          'fill-opacity': 0.2,
        },
      });

      map.addLayer({
        id: 'employers-drive-time-outline',
        type: 'line',
        source: 'employers-drive-time-area',
        layout: {
          visibility:
            currentHoverSection === 'area-major-employers' ? 'visible' : 'none',
        },
        paint: {
          'line-color': '#2563eb',
          'line-width': 2,
        },
      });
    };

    // Check if the map style is loaded
    if (map.isStyleLoaded()) {
      addMapLayers();
    } else {
      // Wait for the style to load before adding layers
      map.once('styledata', addMapLayers);
    }
  };

  // Generate circle data
  const generateCircleData = (
    center: [number, number],
    radiusMiles: number,
  ) => {
    const points = 64;
    const radiusMeters = radiusMiles * 1609.34;
    const coords = [];

    for (let i = 0; i < points; i++) {
      const angle = (i * 360) / points;
      const lat =
        center[1] + (radiusMeters / 111320) * Math.cos((angle * Math.PI) / 180);
      const lng =
        center[0] +
        (radiusMeters / (111320 * Math.cos((center[1] * Math.PI) / 180))) *
          Math.sin((angle * Math.PI) / 180);
      coords.push([lng, lat]);
    }
    coords.push(coords[0]); // Close the circle

    return {
      type: 'Feature',
      properties: {},
      geometry: {
        type: 'Polygon',
        coordinates: [coords],
      },
    };
  };

  // Effect for handling hover state and mode changes
  useEffect(() => {
    if (!map || !mapData) return;

    clearMapLayers();

    if (currentHoverSection === 'area-major-employers') {
      if (calculationMode === 'Distance') {
        updateMapCircle();
      } else {
        updateMapDriveTime();
      }
    }
  }, [currentHoverSection, calculationMode, mapData, map]);

  useEffect(() => {
    const fetchData = async () => {
      if (currentPropertyAddress.latitude && currentPropertyAddress.longitude) {
        setLoading(true);
        if (calculationMode === 'Distance') {
          const distanceInMiles = Number(distance);
          // Generate and store circle data
          setMapData(
            generateCircleData(
              [
                currentPropertyAddress.longitude,
                currentPropertyAddress.latitude,
              ],
              distanceInMiles,
            ),
          );

          const result = await getMajorEmployers({
            lat: currentPropertyAddress.latitude,
            lng: currentPropertyAddress.longitude,
            distance: distanceInMiles,
          });
          dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              majorEmployersData: result,
            },
          });
          setLoading(false);
        } else {
          const mapboxResult = await fetch(
            `https://api.mapbox.com/isochrone/v1/mapbox/driving-traffic/${currentPropertyAddress.longitude}%2C${currentPropertyAddress.latitude}?contours_minutes=${driveTime}&polygons=true&denoise=1&access_token=${MAPBOX_TOKEN}`,
          );

          const data = await mapboxResult.json();
          // Store drive time polygon data
          setMapData({
            type: 'Feature',
            properties: {},
            geometry: {
              type: 'Polygon',
              coordinates: [data.features[0].geometry.coordinates[0]],
            },
          });

          const driveTimeResult = await getMajorEmployersnDriveTime({
            body: {
              polygon: data.features[0].geometry.coordinates[0],
              lat: currentPropertyAddress.latitude,
              lng: currentPropertyAddress.longitude,
            },
          });
          if (driveTimeResult) {
            dispatch({
              type: 'CMA/saveCMAStates',
              payload: {
                majorEmployersData: driveTimeResult,
              },
            });
          }
          setLoading(false);
        }
      } else {
        clearMapLayers();
        setMapData(null);
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            majorEmployersData: null,
          },
        });
      }
    };
    fetchData();
  }, [currentPropertyAddress, driveTime, calculationMode, distance, map]);

  function handleChange(value: string): void {
    setDriveTime(value);
  }
  const handleDriveTimeChange = (value: number | null) => {
    if (value === null) return;
    // Ensure value is within bounds
    const boundedValue = Math.min(Math.max(value, 5), 45);
    setDriveTime(boundedValue.toString());
  };

  const handleDistanceChange = (value: number | null) => {
    if (value === null) return;
    // Ensure value is within bounds
    const boundedValue = Math.min(Math.max(value, 1), 15);
    setDistance(boundedValue.toString());
  };

  const [CSVButtonLoading, setCSVButtonLoading] = useState<boolean>(false);
  const onClickCSVButton = () => {
    const mode = calculationMode === 'Distance' ? 'distance' : 'drive';
    setCSVButtonLoading(true);
    const params: any = {
      body: {
        lat: currentPropertyAddress.latitude,
        lng: currentPropertyAddress.longitude,
        distance: distance,
        mode: mode,
        time: driveTime,
      },
    };
    const fetchData = async () => {
      try {
        const response = await postMajorEmployersCSV(params);
        if (response) {
          // Create blob directly from the response string
          const blob = new Blob([response], {
            type: 'text/csv;charset=utf-8;',
          });

          // Create download URL
          const url = window.URL.createObjectURL(blob);

          // Create and trigger download
          const a = document.createElement('a');
          a.href = url;

          // Create filename with current date
          const date = new Date().toISOString();
          const filename = `major_employers_${date}.csv`;
          a.download = filename;

          // Trigger download
          document.body.appendChild(a);
          a.click();

          // Cleanup
          window.URL.revokeObjectURL(url);
          a.remove();
        }
        setCSVButtonLoading(false);
      } catch (error) {
        console.error('Error downloading CSV:', error);
        // Handle error appropriately
      }
    };
    fetchData();
  };

  return (
    <div
      className={`${styles.cardWrapper}  ${
        currentHoverSection === 'area-major-employers'
          ? ' bg-slate-50'
          : 'bg-white'
      }`}
      onMouseEnter={() => {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            currentHoverSection: 'area-major-employers',
          },
        });
      }}
      onMouseLeave={() => {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            currentHoverSection: null,
          },
        });
      }}
    >
      <div className="flex justify-between">
        <p className="text-lg font-semibold pr-2">Major Employers</p>
        <Button
          type="default"
          loading={CSVButtonLoading}
          onClick={onClickCSVButton}
          disabled={!majorEmployersData}
        >
          {' '}
          Export CSV
        </Button>
      </div>
      <div className="flex items-center">
        <Segmented<string>
          options={['Drive Time', 'Distance']}
          onChange={(value) => {
            setCalculationMode(value);
          }}
        />

        {calculationMode === 'Drive Time' ? (
          <div className="ml-2 flex items-center">
            <Tooltip title="Range: 5-45 minutes" placement="top">
              <InputNumber
                min={5}
                max={45}
                value={Number(driveTime)}
                onChange={handleDriveTimeChange}
                style={{ width: 110 }}
                addonAfter="mins"
                controls
                keyboard
              />
            </Tooltip>
          </div>
        ) : (
          <div className="ml-2 flex items-center">
            <Tooltip title="Range: 1-15 miles" placement="top">
              <InputNumber
                min={1}
                max={15}
                value={Number(distance)}
                onChange={handleDistanceChange}
                style={{ width: 110 }}
                addonAfter="miles"
                controls
                keyboard
              />
            </Tooltip>
          </div>
        )}
      </div>
      {loading ? (
        <Skeleton active />
      ) : majorEmployersData ? (
        <>
          <MajorEmployersTable />
        </>
      ) : (
        <>
          <p>Select a location on the map to see result.</p>
        </>
      )}
    </div>
  );
};

export default MajorEmployer;
