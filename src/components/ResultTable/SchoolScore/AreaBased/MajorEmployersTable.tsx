import { Table, TableColumnsType } from 'antd';
import { useSelector } from 'umi';
import { MajorEmployer } from '../panel';

const MajorEmployersTable = () => {
  const majorEmployersData = useSelector(
    (state: any) => state.CMA.majorEmployersData,
  );

  const columns: TableColumnsType<MajorEmployer> = [
    {
      title: 'Company Name',
      dataIndex: 'companyName',
      key: 'companyName',
      render: (text: string, record: MajorEmployer) => {
        return <p>{text}</p>;
      },
    },
    {
      title: 'Distance',
      dataIndex: 'distance',
      key: 'distance',
      render: (text: string, record: MajorEmployer) => {
        return <p>{(Number(text) / 1609.34).toFixed(2)} mi</p>;
      },
    },
    {
      title: 'Number of Employee',
      dataIndex: 'locationEmployeeSizeActual',
      key: 'locationEmployeeSizeActual',
      render: (text: string, record: MajorEmployer) => {
        return <p>{text}</p>;
      },
    },
    {
      title: 'Category',
      dataIndex: 'subcategory',
      key: 'subcategory',
      render: (text: string, record: MajorEmployer) => {
        return <p>{text}</p>;
      },
    },
  ];

  console.log('majorEmployersData', majorEmployersData);
  return (
    <div>
      <Table dataSource={majorEmployersData} columns={columns} size="small" />
    </div>
  );
};

export default MajorEmployersTable;
