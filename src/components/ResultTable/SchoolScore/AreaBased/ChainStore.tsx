import { useNearbyChainStore } from '@spatiallaser/map';
import { Button, Select, Skeleton, Table } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import styles from '../../resultTable.css';

const ChainStore = () => {
  const dispatch = useDispatch();
  const currentMapLayerOptions = useSelector(
    (state: any) => state.CMA.currentMapLayerOptions,
  );
  const map = useSelector((state: any) => state.CMA.map);
  const sadChainStoreDistance = useSelector(
    (state: any) => state.CMA.sadChainStoreDistance,
  );
  const currentHoverSection = useSelector(
    (state: any) => state.CMA.currentHoverSection,
  );
  const currentPropertyAddress = useSelector(
    (state: any) => state.CMA.currentPropertyAddress,
  );

  const {
    data: chainData,
    isLoading: chainLoading,
    isError: chainError,
  } = useNearbyChainStore();

  const clearMapLayers = () => {
    if (map && map.getLayer('chain-store-circle')) {
      map.removeLayer('chain-store-circle');
      map.removeLayer('chain-store-outline');
      map.removeSource('chain-store-area');
    }
  };

  const updateMapCircle = (center: [number, number], radiusMiles: number) => {
    clearMapLayers();

    // Create a circular polygon
    const points = 64;
    const radiusMeters = radiusMiles * 1609.34;
    const coords = [];

    for (let i = 0; i < points; i++) {
      const angle = (i * 360) / points;
      const lat =
        center[1] + (radiusMeters / 111320) * Math.cos((angle * Math.PI) / 180);
      const lng =
        center[0] +
        (radiusMeters / (111320 * Math.cos((center[1] * Math.PI) / 180))) *
          Math.sin((angle * Math.PI) / 180);
      coords.push([lng, lat]);
    }
    coords.push(coords[0]); // Close the circle

    // Check if map style is loaded before adding sources/layers
    const addMapLayers = () => {
      map.addSource('chain-store-area', {
        type: 'geojson',
        data: {
          type: 'Feature',
          properties: {},
          geometry: {
            type: 'Polygon',
            coordinates: [coords],
          },
        },
      });

      map.addLayer({
        id: 'chain-store-circle',
        type: 'fill',
        source: 'chain-store-area',
        layout: {
          visibility:
            currentHoverSection === 'area-chain-stores' ? 'visible' : 'none',
        },
        paint: {
          'fill-color': '#22c55e',
          'fill-opacity': 0.2,
        },
      });

      map.addLayer({
        id: 'chain-store-outline',
        type: 'line',
        source: 'chain-store-area',
        layout: {
          visibility:
            currentHoverSection === 'area-chain-stores' ? 'visible' : 'none',
        },
        paint: {
          'line-color': '#22c55e',
          'line-width': 2,
        },
      });
    };

    // Check if the map style is loaded
    if (map.isStyleLoaded()) {
      addMapLayers();
    } else {
      // Wait for the style to load before adding layers
      map.once('styledata', addMapLayers);
    }
  };

  // Add visibility effect
  useEffect(() => {
    if (!map) return;

    const circleLayerExists = map.getLayer('chain-store-circle');

    if (circleLayerExists) {
      map.setLayoutProperty(
        'chain-store-circle',
        'visibility',
        currentHoverSection === 'area-chain-stores' ? 'visible' : 'none',
      );
      map.setLayoutProperty(
        'chain-store-outline',
        'visibility',
        currentHoverSection === 'area-chain-stores' ? 'visible' : 'none',
      );
    }
  }, [currentHoverSection, map]);

  // Update circle when distance or location changes
  useEffect(() => {
    if (
      map &&
      currentPropertyAddress.latitude &&
      currentPropertyAddress.longitude
    ) {
      updateMapCircle(
        [currentPropertyAddress.longitude, currentPropertyAddress.latitude],
        Number(sadChainStoreDistance),
      );
    } else {
      clearMapLayers();
    }
  }, [currentPropertyAddress, sadChainStoreDistance, map]);

  useEffect(() => {
    if (chainData && chainData.favorable && chainData.favorable.length > 0) {
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          nearbyChainStoreData: chainData.favorable,
        },
      });
    }
  }, [chainData]);

  const openFavorableBrandSettings = useCallback(() => {
    const payload = {
      currentMapLayerOptions: [
        currentMapLayerOptions.filter((l: string) => l !== 'chain stores'),
        'chain stores',
      ],
    };

    map.fire('mapLayers.currentMapLayerOptions', {
      payload: payload,
    });
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: payload,
    });
  }, [map, currentMapLayerOptions]);

  return (
    <div
      className={`${styles.cardWrapper}  ${
        currentHoverSection === 'area-chain-stores'
          ? ' bg-slate-50'
          : 'bg-white'
      }`}
      onMouseEnter={() => {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            currentHoverSection: 'area-chain-stores',
          },
        });
      }}
      onMouseLeave={() => {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            currentHoverSection: null,
          },
        });
      }}
    >
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '1em',
        }}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            gap: '4px',
            alignItems: 'center',
          }}
        >
          <div
            style={{
              width: '10px',
              height: '10px',
              borderRadius: '50%',
              backgroundColor: 'green',
            }}
          ></div>
          <p className="text-lg font-semibold" style={{ marginBottom: 0 }}>
            Favorable Brands Nearby within {sadChainStoreDistance}{' '}
            {sadChainStoreDistance == '1' ? 'mile' : 'miles'}{' '}
            {chainData && chainData.favorable && chainData.favorable.length > 0
              ? `(${chainData.favorable.length} results)`
              : ''}
          </p>
        </div>

        <Button onClick={openFavorableBrandSettings}>Settings</Button>
      </div>
      <div className="mb-2 flex justify-start">
        <p className="pt-1 pr-2 text-base">Distance: </p>
        <Select
          value={sadChainStoreDistance}
          style={{ width: 120 }}
          onChange={(value: string) => {
            dispatch({
              type: 'CMA/saveCMAStates',
              payload: {
                sadChainStoreDistance: value,
              },
            });
          }}
          options={[
            { value: 1, label: '1 mile' },
            { value: 2, label: '2 miles' },
            { value: 3, label: '3 miles' },
            { value: 4, label: '4 miles' },
            { value: 5, label: '5 miles' },
          ]}
        />
      </div>
      {chainLoading && <Skeleton active />}
      {!chainLoading && chainError && <div>Something went wrong.</div>}
      {!chainLoading && !chainError && (
        <div>
          {!chainData && <div>Select a location on the map.</div>}
          {chainData &&
            chainData.favorable &&
            chainData.favorable.length === 0 && (
              <div>
                No favorable chains nearby. Try adjusting your radius or
                selection of brands in the settings.
              </div>
            )}
          {chainData &&
            chainData.favorable &&
            chainData.favorable.length > 0 && (
              <>
                <Table
                  id="favorable-brands-table"
                  rowKey={(record) => record.hash_id}
                  dataSource={chainData.favorable}
                  scroll={{ y: 240 }}
                  pagination={false}
                  size="small"
                  columns={[
                    {
                      title: 'Chain Name',
                      dataIndex: 'chain_name',
                      sorter: (a, b) =>
                        a.chain_name.localeCompare(b.chain_name),
                    },
                    {
                      title: 'Distance',
                      dataIndex: 'distance',
                      sorter: (a, b) => a.distance - b.distance,
                      defaultSortOrder: 'ascend',
                      render: (text) => (
                        <span>{Math.round(Number(text) * 100) / 100} mi</span>
                      ),
                    },
                  ]}
                  rowClassName={styles.cursorPointer}
                  onRow={(record) => ({
                    onClick: () => {
                      const lat = record.geometry.coordinates[1];
                      const lng = record.geometry.coordinates[0];
                      map.flyTo({ center: [lng, lat], zoom: 16 });
                    },
                  })}
                />
              </>
            )}
        </div>
      )}
    </div>
  );
};

export default ChainStore;
