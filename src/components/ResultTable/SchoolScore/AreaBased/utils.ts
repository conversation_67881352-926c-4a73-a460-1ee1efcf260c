export interface Segment {
  name: string;
  description: string;
  headOfHouseholdAge: string;
  EstHouseholdIncome: string;
}

const SegmentDescription: Segment[] = [
  {
    headOfHouseholdAge: '45-54 years',
    EstHouseholdIncome: '$200k+',
    name: 'A01 - Midas Might',
    description: 'Extremely wealthy families living in large estate homes.',
  },
  {
    headOfHouseholdAge: '35-44 years',
    EstHouseholdIncome: '$200k+',
    name: 'A02 - Sky High',
    description:
      'High income, highly educated, singles and young couples, living in the dense urban core.',
  },
  {
    headOfHouseholdAge: '45-54 years',
    EstHouseholdIncome: '$200k+',
    name: 'A03 - Exclusive Exburbs',
    description:
      'High-income families living in wealthy enclaves on the outskirts of major cities.',
  },
  {
    headOfHouseholdAge: '45-54 years',
    EstHouseholdIncome: '$200k+',
    name: 'A04 - Family Outposts',
    description:
      'Wealthy family-focused households straddling the suburban/rural line.',
  },
  {
    headOfHouseholdAge: '45-54 years',
    EstHouseholdIncome: '$200k+',
    name: 'A05 - Suburb Chic',
    description:
      'Affluent families living in higher-priced suburbs not too far from the city center.',
  },
  {
    headOfHouseholdAge: '45-54 years',
    EstHouseholdIncome: '$200k+',
    name: 'A06 - Tech Titans',
    description:
      'Tech-forward wealthy families in proximity to software and highly technical jobs.',
  },
  {
    headOfHouseholdAge: '35-44 years',
    EstHouseholdIncome: '$150k - $200k',
    name: 'B01 - Fusion Families',
    description:
      'Family-centered, suburban high-income households who still visit the mall.',
  },
  {
    headOfHouseholdAge: '45-54 years',
    EstHouseholdIncome: '$150k - $200k',
    name: 'B02 - Satellite Scions',
    description:
      'Highly educated suburbanite families living on the outskirts of tech hubs.',
  },
  {
    headOfHouseholdAge: '45-54 years',
    EstHouseholdIncome: '$100k - $125k',
    name: 'B03 - Backyard Bliss',
    description:
      'Outdoor-loving upper-income families straddling the suburban rural divide.',
  },
  {
    headOfHouseholdAge: '35-44 years',
    EstHouseholdIncome: '$100k - $125k',
    name: 'B04 - Babies Burbs & Blessings',
    description:
      'Younger, upper-middle-class suburban families that take parenting and faith seriously.',
  },
  {
    headOfHouseholdAge: '45-54 years',
    EstHouseholdIncome: '$100k - $125k',
    name: 'C01 - Good Life Citizens',
    description:
      'Upper-income families living their best life just outside cities.',
  },
  {
    headOfHouseholdAge: '35-44 years',
    EstHouseholdIncome: '$75k - $100k',
    name: 'C02 - Family & Faith',
    description:
      'Young faith-focused families with children in suburban enclaves.',
  },
  {
    headOfHouseholdAge: '35-44 years',
    EstHouseholdIncome: '$75k - $100k',
    name: 'C03 - Flourishing Fusion',
    description:
      'Families with children living in diverse white and black exurbs.',
  },
  {
    headOfHouseholdAge: '35-44 years',
    EstHouseholdIncome: '$75k - $100k',
    name: 'C04 - Frugal Fashionistas',
    description:
      'Active, educated, and diverse couples and singles in near suburbs.',
  },
  {
    headOfHouseholdAge: '45-54 years',
    EstHouseholdIncome: '$75k - $100k',
    name: 'C05 - Bootstrappers',
    description:
      'Upper-middle income families near cities, working management roles in service, blue-collar, and admin jobs.',
  },
  {
    headOfHouseholdAge: '60-64 years',
    EstHouseholdIncome: '$75k - $100k',
    name: 'D01 - Pets & Empty Nests',
    description: 'Middle-aged empty-nesters in diverse suburbs.',
  },
  {
    headOfHouseholdAge: '65-74 years',
    EstHouseholdIncome: '$75k - $100k',
    name: 'D02 - Roaring Retirees',
    description:
      'Retired or near retirement, empty nesters living active and involved lifestyles.',
  },
  {
    headOfHouseholdAge: '65-74 years',
    EstHouseholdIncome: '$60k - $75k',
    name: 'D03 - Timeless Tribe',
    description: 'Middle-class boomers slowing down in suburban neighborhoods.',
  },
  {
    headOfHouseholdAge: '45-54 years',
    EstHouseholdIncome: '$75k - $100k',
    name: 'E01 - City Sabor',
    description:
      'Diverse, Hispanic-influenced consumers stretching their earnings near major cities.',
  },
  {
    headOfHouseholdAge: '45-54 years',
    EstHouseholdIncome: '$75k - $100k',
    name: 'E02 - City Chic',
    description:
      'Upper-middle-class African American families near major urban areas.',
  },
  {
    headOfHouseholdAge: '35-44 years',
    EstHouseholdIncome: '$75k - $100k',
    name: 'E03 - Generational Go Getters',
    description:
      'Large Hispanic families working service and blue-collar jobs in dense urban areas',
  },
  {
    headOfHouseholdAge: '45-54 years',
    EstHouseholdIncome: '$60k - $75k',
    name: 'E04 - Fashion Fusion',
    description:
      'Fashionable blue-collar Hispanic families in affordable urban neighborhoods.',
  },
  {
    headOfHouseholdAge: '35-44 years',
    EstHouseholdIncome: '$60k - $75k',
    name: 'E05 - Mixed Mecca',
    description:
      'Diverse, blue-collar and service workers living in dominantly Asian and Hispanic urban neighborhoods.',
  },
  {
    headOfHouseholdAge: '35-44 years',
    EstHouseholdIncome: '$35k - $50k',
    name: 'E06 - Eastern Heritage',
    description:
      'Primarily Asian renters working local restaurant and retail jobs in dense urban areas.',
  },
  {
    headOfHouseholdAge: '35-44 years',
    EstHouseholdIncome: '$50k - $60k',
    name: 'F01 - Game Time Glory',
    description:
      'Younger singles and couples living in mixed blue and white-collar neighborhoods near cities.',
  },
  {
    headOfHouseholdAge: '35-44 years',
    EstHouseholdIncome: '$50k - $60k',
    name: 'F02 - Fast Lane Families',
    description:
      'Working-class Hispanic and Black households with low-priced homes and lots of kids.',
  },
  {
    headOfHouseholdAge: '35-44 years',
    EstHouseholdIncome: '$50k - $60k',
    name: 'F03 - Penny Wise Parents',
    description:
      'Young families living in affordable neighborhoods near cities.',
  },
  {
    headOfHouseholdAge: '25-34 years',
    EstHouseholdIncome: '$35k - $50k',
    name: 'F04 - Military',
    description: 'Families and singles living on military bases.',
  },
  {
    headOfHouseholdAge: '35-44 years',
    EstHouseholdIncome: '$35k - $50k',
    name: 'F05 - Old Town Road',
    description:
      'Black and white and families renting and owning in lower-priced suburbs.',
  },
  {
    headOfHouseholdAge: '25-34 years',
    EstHouseholdIncome: '$200k+',
    name: 'G01 - High Risers',
    description:
      'High income, highly educated, Warby Parker wearing young professionals in densely populated areas.',
  },
  {
    headOfHouseholdAge: '35-44 years',
    EstHouseholdIncome: '$150k - $200k',
    name: 'G02 - Silicon Nation',
    description:
      'Well educated and affluent engineers, doctors, businessmen in urban areas.',
  },
  {
    headOfHouseholdAge: '25-34 years',
    EstHouseholdIncome: '$100k - $125k',
    name: 'G03 - Urbanists',
    description:
      'Young, White, well-off renters living in gentrified neighborhoods of first-tier cities.',
  },
  {
    headOfHouseholdAge: '25-34 years',
    EstHouseholdIncome: '$100k - $125k',
    name: 'H01 - Young Stars',
    description:
      'Early-career, high-income young professionals renting near city centers or in nice, denser suburbs.',
  },
  {
    headOfHouseholdAge: '25-34 years',
    EstHouseholdIncome: '$75k - $100k',
    name: 'H02 - Rising Professionals',
    description:
      'Well-educated college graduates renting in the trendiest parts of town.',
  },
  {
    headOfHouseholdAge: '25-34 years',
    EstHouseholdIncome: '$50k - $60k',
    name: 'H03 - Raising The Bar',
    description:
      'Primarily young, well-educated renters raising the waterline in diverse, mixed-income areas.',
  },
  {
    headOfHouseholdAge: '15-24 years',
    EstHouseholdIncome: 'Less than $15k',
    name: 'H04 - College',
    description: 'Student renters making little to no income at school.',
  },
  {
    headOfHouseholdAge: '25-34 years',
    EstHouseholdIncome: '$35k - $50k',
    name: 'I01 - Big City Progressives',
    description:
      'Young diverse renters working service and low paying arts/entertainment jobs trying to "make it" in top tier cities.',
  },
  {
    headOfHouseholdAge: '25-34 years',
    EstHouseholdIncome: '$35k - $50k',
    name: 'I02 - Multicultural Renters',
    description:
      'White and Latino renters making low relative income in high property value areas.',
  },
  {
    headOfHouseholdAge: '25-34 years',
    EstHouseholdIncome: '$35k - $50k',
    name: 'I03 - Rust Renters',
    description:
      'Young, lower-income renters scattered across 2nd and 3rd tier cities.',
  },
  {
    headOfHouseholdAge: '25-34 years',
    EstHouseholdIncome: '$35k - $50k',
    name: 'I04 - Adulting',
    description:
      'Single, diverse renters working service jobs and transitioning to independent lives.',
  },
  {
    headOfHouseholdAge: '35-44 years',
    EstHouseholdIncome: '$25k - $35k',
    name: 'I05 - Solos',
    description: 'Heavily Hispanic, urban singles making low incomes.',
  },
  {
    headOfHouseholdAge: '65-74 years',
    EstHouseholdIncome: '$200k+',
    name: 'J01 - Picturesque Prosperity',
    description:
      'Middle aged wealthy families living in luxurious homes in scenic areas and neighborhoods.',
  },
  {
    headOfHouseholdAge: '65-74 years',
    EstHouseholdIncome: '$150k - $200k',
    name: 'J02 - Destination Retirees',
    description:
      'Older, wealthy, and often retired empty-nesters living out their golden years on the beach, or occasionally themountains.',
  },
  {
    headOfHouseholdAge: '65-74 years',
    EstHouseholdIncome: '$150k - $200k',
    name: 'J03 - Couples Coasting In',
    description: 'Empty-nesters living active healthy lives in upscale suburbs',
  },
  {
    headOfHouseholdAge: '65-74 years',
    EstHouseholdIncome: '$100k - $125k',
    name: 'J04 - Golden City Solos',
    description:
      'Retired and mostly single households renting in classy urban areas.',
  },
  {
    headOfHouseholdAge: '65-74 years',
    EstHouseholdIncome: '$75k - $100k',
    name: 'J05 - Scenic Seniors',
    description:
      'Older, upper middle class retirees living in beachfront and mountain communities.',
  },
  {
    headOfHouseholdAge: '65-74 years',
    EstHouseholdIncome: '$35k - $50k',
    name: 'K01 - Rural Retirement',
    description:
      'Retired couples living on a shoestring budget in remote communities with easy access to outdoor recreation.',
  },
  {
    headOfHouseholdAge: '65-74 years',
    EstHouseholdIncome: '$35k - $50k',
    name: 'K02 - Snowbird Set',
    description:
      'Modestly retired couples, many living in Florida, these households planning their next vacation to the beach or casino.',
  },
  {
    headOfHouseholdAge: '65-74 years',
    EstHouseholdIncome: '$35k - $50k',
    name: 'K03 - Big Box Boomers',
    description:
      'Working-class boomers renting in socially active near-urban neighborhoods.',
  },
  {
    headOfHouseholdAge: '65-74 years',
    EstHouseholdIncome: '$25k - $35k',
    name: 'K04 - Faith & Football',
    description:
      'Middle aged, diverse, and lower income households working blue collar jobs.',
  },
  {
    headOfHouseholdAge: '45-54 years',
    EstHouseholdIncome: '$100k - $125k',
    name: 'L01 - Northern Lights',
    description:
      'Middle-aged rural couples making relatively high incomes in white-collar jobs.',
  },
  {
    headOfHouseholdAge: '45-54 years',
    EstHouseholdIncome: '$75k - $100k',
    name: 'L02 - Cornfield Cabernet',
    description:
      'Farming and blue-collar couples living their best lives in the country.',
  },
  {
    headOfHouseholdAge: '45-54 years',
    EstHouseholdIncome: '$60k - $75k',
    name: 'L03 - Country Barons',
    description:
      'Middle-aged and older rural couples in blue-collar jobs living iconic country lives.',
  },
  {
    headOfHouseholdAge: '65-74 years',
    EstHouseholdIncome: '$60k - $75k',
    name: 'L04 - Pension Pastures',
    description:
      'Older couples in farm and blue-collar professions enjoying the outdoors.',
  },
  {
    headOfHouseholdAge: '45-54 years',
    EstHouseholdIncome: '$50k - $60k',
    name: 'M01 - Wide Open Spaces',
    description:
      'People in communities characterized by cornfields, farming, and open land.',
  },
  {
    headOfHouseholdAge: '35-44 years',
    EstHouseholdIncome: '$50k - $60k',
    name: 'M02 - John Deere Country',
    description:
      'Families and couples with average income working blue-collar jobs.',
  },
  {
    headOfHouseholdAge: '55-59 years',
    EstHouseholdIncome: '$35k - $50k',
    name: 'M03 - Camo & Cornfields',
    description:
      'Industrious farming and blue-collar families who love DIY and fast food.',
  },
  {
    headOfHouseholdAge: '35-44 years',
    EstHouseholdIncome: '$35k - $50k',
    name: 'M04 - Kids & Country',
    description:
      'Younger families working farming and blue-collar jobs in small towns and rural communities.',
  },
  {
    headOfHouseholdAge: '55-59 years',
    EstHouseholdIncome: '$35k - $50k',
    name: 'N01 - Dirt Road Dynasty',
    description:
      'Older, lower-income families working factory and blue-collar jobs.',
  },
  {
    headOfHouseholdAge: '60-64 years',
    EstHouseholdIncome: '$25k - $35k',
    name: 'N02 - Southern Cross',
    description:
      'Lower-income couples and single parents crossing white and black rural cultures, meeting at the intersection ofworship and sports.',
  },
  {
    headOfHouseholdAge: '35-44 years',
    EstHouseholdIncome: '$15k - $25k',
    name: 'N03 - Heritage Haven',
    description:
      'Native American households supported by farm, blue-collar, and service-related occupations.',
  },
  {
    headOfHouseholdAge: '45-54 years',
    EstHouseholdIncome: '$15k - $25k',
    name: 'N04 - Rural Rationers',
    description:
      'White semi-rural households with little education or earnings following high school and college sports closely.',
  },
  {
    headOfHouseholdAge: '55-59 years',
    EstHouseholdIncome: '$35k - $50k',
    name: 'O01 - Small Town Sattelites',
    description:
      'Older ruralites satelliting small towns in blue-collar, farm, and service professions finding time to enjoy the simplethings.',
  },
  {
    headOfHouseholdAge: '25-34 years',
    EstHouseholdIncome: '$25k - $35k',
    name: 'O02 - Main Street USA',
    description:
      'Moderate income, young, blue-collar workers living near main streets in small-town America.',
  },
  {
    headOfHouseholdAge: '25-34 years',
    EstHouseholdIncome: '$25k - $35k',
    name: 'O03 - Starting Line',
    description:
      'Blue collar renters starting out in small towns and urban clusters making meager wages.',
  },
  {
    headOfHouseholdAge: '45-54 years',
    EstHouseholdIncome: 'Less than $15k',
    name: 'O04 - Harmonic Hamlets',
    description: 'Small town African American households with little earnings.',
  },
  {
    headOfHouseholdAge: '45-54 years',
    EstHouseholdIncome: '$35k - $50k',
    name: 'P01 - Farmsted Fusion',
    description:
      'Households in Latin-influenced rural communities working farm, service, and blue-collar jobs.',
  },
  {
    headOfHouseholdAge: '35-44 years',
    EstHouseholdIncome: '$35k - $50k',
    name: 'P02 - Next Wave',
    description:
      'Hispanics in small towns working blue-collar, farm, and service jobs with 5+ person households and plenty of children.',
  },
  {
    headOfHouseholdAge: '35-44 years',
    EstHouseholdIncome: '$35k - $50k',
    name: 'P03 - In The Heights',
    description: 'Hispanic households renting in dense urban environments.',
  },
  {
    headOfHouseholdAge: '25-34 years',
    EstHouseholdIncome: '$25k - $35k',
    name: 'P04 - Heritage Cultivators',
    description:
      'Hispanic households in near-urban areas supported by farming jobs at a rate of 13x the national average.',
  },
  {
    headOfHouseholdAge: '25-34 years',
    EstHouseholdIncome: '$15k - $25k',
    name: 'P05 - Reggaeton Roads',
    description:
      'Afro-Latino renters in near-suburbs working blue-collar and service jobs making meager earnings.',
  },
  {
    headOfHouseholdAge: '45-54 years',
    EstHouseholdIncome: '$15k - $25k',
    name: 'P06 - Cultural Collection',
    description:
      'Low-income Hispanic households in lively social areas who, despite low earnings, take fashion seriously.',
  },
  {
    headOfHouseholdAge: '35-44 years',
    EstHouseholdIncome: '$25k - $35k',
    name: 'Q01 - Rhythmic Renters',
    description:
      'Afro-Latino renters in dense urban areas working blue-collar and service professions making below average income.',
  },
  {
    headOfHouseholdAge: '55-59 years',
    EstHouseholdIncome: '$25k - $35k',
    name: 'Q02 - Urban Upgrade',
    description:
      'Older and below-average income African American households often headed by single (and female) parents.',
  },
  {
    headOfHouseholdAge: '25-34 years',
    EstHouseholdIncome: '$25k - $35k',
    name: 'Q03 - City Starters',
    description:
      'Young, mostly African American singles renting low price neighborhoods surrounding city centers.',
  },
  {
    headOfHouseholdAge: '25-34 years',
    EstHouseholdIncome: '$25k - $35k',
    name: 'Q04 - Intrepid Influencers',
    description:
      'Renters in African American neighborhoods with low income and a high percentage of female householders.',
  },
  {
    headOfHouseholdAge: '45-54 years',
    EstHouseholdIncome: '$15k - $25k',
    name: 'Q05 - Diesel Blues',
    description:
      'Blue-collar mixed black and white neighborhoods with low education and income.',
  },
  {
    headOfHouseholdAge: '55-59 years',
    EstHouseholdIncome: '$15k - $25k',
    name: 'Q06 - Blue Collar Grit',
    description:
      'Single white renters stretching incomes in towns and urban areas.',
  },
  {
    headOfHouseholdAge: '55-59 years',
    EstHouseholdIncome: 'Less than $15k',
    name: 'Q07 - Historic Transitions',
    description:
      'Historically older and lower-income single households renting in dense neighborhoods with a growing youngerpopulation.',
  },
  {
    headOfHouseholdAge: '25-34 years',
    EstHouseholdIncome: 'Less than $15k',
    name: 'Q08 - Aspiring Parents',
    description:
      'Renting households characterized by extremely low income, low education levels, vacant buildings, and singlemothers',
  },
];

export function getSegment(key: string): Segment | undefined {
  return SegmentDescription.find(
    (segment) => segment.name.split(' - ')[0] === key,
  );
}
