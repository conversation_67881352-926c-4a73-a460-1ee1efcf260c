import { postSkipTrace } from '@/services/data';
import {
  <PERSON>ton,
  Card,
  Descriptions,
  Divider,
  Empty,
  Space,
  Spin,
  Tag,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'umi';

const { Title, Text } = Typography;

const OwnerInformations = () => {
  const dispatch = useDispatch();
  const persons = useSelector((state: any) => state.CMA.persons);
  const currentHouseDetails = useSelector(
    (state: any) => state.CMA.currentHouseDetails,
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isDataReady, setIsDataReady] = useState(false);

  // Check if the current house details are ready
  useEffect(() => {
    // This will run whenever currentHouseDetails changes
    if (
      currentHouseDetails &&
      currentHouseDetails.street_number &&
      currentHouseDetails.street_name &&
      currentHouseDetails.city &&
      currentHouseDetails.state_or_province &&
      currentHouseDetails.postal_code
    ) {
      setIsDataReady(true);
    } else {
      setIsDataReady(false);
    }
  }, [currentHouseDetails]);

  // Auto-fetch owner info when component becomes ready
  useEffect(() => {
    if (isDataReady && !persons && !loading) {
      getOwnersInfo();
    }
  }, [isDataReady, persons, loading]);

  const getPropertyAddress = () => {
    if (!currentHouseDetails) return null;

    const {
      street_number,
      street_name,
      city,
      state_or_province,
      postal_code,
      street_suffix,
    } = currentHouseDetails;

    // Handle cases where street_suffix might be undefined
    const streetAddress = street_suffix
      ? `${street_number || ''} ${street_name || ''} ${street_suffix}`
      : `${street_number || ''} ${street_name || ''}`;

    return {
      city: city || '',
      street: streetAddress.trim(),
      state: state_or_province || '',
      zip: postal_code || '',
    };
  };

  const getOwnersInfo = async () => {
    const propertyAddress = getPropertyAddress();

    if (!propertyAddress || !propertyAddress.street || !propertyAddress.city) {
      setError(
        'Property address information is incomplete. Please try again later.',
      );
      return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log(
        'OwnerInformations requesting with propertyAddress:',
        propertyAddress,
      );

      const response = await postSkipTrace({
        body: { requests: [{ propertyAddress }] },
      });

      console.log('OwnerInformations response', response);

      // Handle different possible response structures
      let personsData;
      if (response?.results?.persons) {
        personsData = response.results.persons;
      } else if (response?.persons) {
        personsData = response.persons;
      } else if (Array.isArray(response)) {
        personsData = response;
      } else {
        console.warn('Unexpected response structure:', response);
        personsData = [];
      }

      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          persons: personsData,
        },
      });
    } catch (err) {
      console.error('Error fetching owner information:', err);
      setError('Failed to fetch owner information. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value) => {
    if (!value && value !== 0) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0,
    }).format(value);
  };

  // Format date string to a more readable format
  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown';

    try {
      const date = new Date(dateString);
      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'Invalid date';
      }

      // Format as MM/DD/YYYY
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Date format error';
    }
  };

  const formatPhoneNumber = (phoneNumberString) => {
    if (!phoneNumberString) return 'Unknown';

    // Remove all non-numeric characters
    const cleaned = ('' + phoneNumberString).replace(/\D/g, '');

    // Check if the number has 10 digits (US format)
    if (cleaned.length === 10) {
      return `${cleaned.slice(0, 3)}-${cleaned.slice(3, 6)}-${cleaned.slice(
        6,
        10,
      )}`;
    } else if (cleaned.length === 11 && cleaned.charAt(0) === '1') {
      // Handle numbers with country code 1
      return `${cleaned.slice(1, 4)}-${cleaned.slice(4, 7)}-${cleaned.slice(
        7,
        11,
      )}`;
    }

    // Return the original if it doesn't match expected formats
    return phoneNumberString;
  };

  const renderPersonInfo = (person) => {
    if (!person) return null;

    return (
      <Card
        key={person._id || Math.random().toString(36).substr(2, 9)}
        style={{ marginBottom: 16 }}
        title={
          <Space>
            <span>
              {`${person.name?.first || ''} ${person.name?.middle || ''} ${
                person.name?.last || ''
              }`.trim() || 'Unknown'}
            </span>
            {person.property?.absenteeOwner && (
              <Tag color="orange">Absentee Owner</Tag>
            )}
            {person.property?.vacant && <Tag color="red">Vacant Property</Tag>}
          </Space>
        }
      >
        <Descriptions column={{ xs: 1, sm: 2, md: 3 }} bordered size="small">
          {/* Property Address - First check propertyAddress, then property.address */}
          <Descriptions.Item label="Property Address" span={3}>
            {person.propertyAddress
              ? `${person.propertyAddress.street || ''}, ${
                  person.propertyAddress.city || ''
                }, ${person.propertyAddress.state || ''} ${
                  person.propertyAddress.zip || ''
                }`
              : person.property?.address
              ? `${person.property.address.street || ''}, ${
                  person.property.address.city || ''
                }, ${person.property.address.state || ''} ${
                  person.property.address.zip || ''
                }`
              : 'Address not available'}
          </Descriptions.Item>

          {/* Mailing Address */}
          {person.mailingAddress && (
            <Descriptions.Item label="Mailing Address" span={3}>
              {`${person.mailingAddress.street || ''}, ${
                person.mailingAddress.city || ''
              }, ${person.mailingAddress.state || ''} ${
                person.mailingAddress.zip || ''
              }`}
            </Descriptions.Item>
          )}
        </Descriptions>

        {/* Email Addresses */}
        {person.emails && person.emails.length > 0 && (
          <>
            <Divider orientation="left">Email Addresses</Divider>
            <Descriptions column={1} bordered size="small">
              {person.emails.map((email, index) => (
                <Descriptions.Item key={index} label={`Email ${index + 1}`}>
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      flexWrap: 'wrap',
                    }}
                  >
                    <span style={{ marginRight: '8px' }}>
                      {email.email || 'Unknown'}
                    </span>
                  </div>
                </Descriptions.Item>
              ))}
            </Descriptions>
          </>
        )}

        {/* Phone Numbers */}
        {person.phoneNumbers && person.phoneNumbers.length > 0 && (
          <>
            <Divider orientation="left">Phone Numbers</Divider>
            <Descriptions column={1} bordered size="small">
              {person.phoneNumbers.map((phone, index) => (
                <Descriptions.Item key={index} label={`Phone ${index + 1}`}>
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      flexWrap: 'wrap',
                    }}
                  >
                    <span style={{ marginRight: '8px' }}>
                      {formatPhoneNumber(phone.number) || 'Unknown'}
                    </span>
                    <span
                      style={{
                        fontSize: '12px',
                        color: '#888',
                        marginRight: '8px',
                      }}
                    >
                      (last updated: {formatDate(phone.lastReportedDate || '')})
                    </span>
                    {phone.type && (
                      <Tag style={{ marginRight: '8px' }}>{phone.type}</Tag>
                    )}
                    {phone.isPrimary && <Tag color="blue">Primary</Tag>}
                  </div>
                </Descriptions.Item>
              ))}
            </Descriptions>
          </>
        )}

        {/* Additional Status Information */}
        <Divider orientation="left">Additional Information</Divider>
        <Descriptions column={{ xs: 1, sm: 2 }} bordered size="small">
          {person.death && (
            <Descriptions.Item label="Deceased Status">
              {person.death.deceased ? 'Deceased' : 'Not Deceased'}
            </Descriptions.Item>
          )}

          {person.litigator !== undefined && (
            <Descriptions.Item label="Litigator">
              {person.litigator ? 'Yes' : 'No'}
            </Descriptions.Item>
          )}

          {person.dnc && (
            <Descriptions.Item label="Do Not Call">
              {person.dnc.tcpa ? 'Yes' : 'No'}
            </Descriptions.Item>
          )}
        </Descriptions>
      </Card>
    );
  };

  // Render loading state when data is not ready
  if (!isDataReady) {
    return (
      <div className="p-4">
        <div style={{ textAlign: 'center', padding: 24 }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>Loading property details...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 16,
        }}
      >
        <Title level={4}>Contact Info</Title>
        <Button
          onClick={getOwnersInfo}
          type="primary"
          loading={loading}
          disabled={!isDataReady}
        >
          {persons ? 'Refresh Contact Info' : 'Get Contact Info'}
        </Button>
      </div>

      {loading && (
        <div style={{ textAlign: 'center', padding: 24 }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>Loading owner information...</div>
        </div>
      )}

      {error && !loading && (
        <Card style={{ backgroundColor: '#fff2f0', marginBottom: 16 }}>
          <Text type="danger">{error}</Text>
        </Card>
      )}

      {!loading && !error && persons ? (
        Array.isArray(persons) && persons.length > 0 ? (
          <div>{persons.map((person, index) => renderPersonInfo(person))}</div>
        ) : (
          <Empty description="No owner information found" />
        )
      ) : (
        !loading && (
          <Card>
            <Text type="secondary">
              Click the button above to retrieve owner information for this
              property.
            </Text>
          </Card>
        )
      )}
    </div>
  );
};

export default OwnerInformations;
