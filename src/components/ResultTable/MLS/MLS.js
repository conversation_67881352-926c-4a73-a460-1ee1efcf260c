import styles from '@/components/ResultTable/resultTable.css';
import { showSFROperatorsFullName } from '@/components/SFRBrandConvertFunction';
import { formatter } from '@/utils/money';
import { capitalize, sortString } from '@/utils/strings';
import { Col, Row, Table, Tooltip } from 'antd';
import isEqual from 'lodash.isequal';
import moment from 'moment';
import { useEffect, useRef, useState } from 'react';
import { connect } from 'umi';
import EmptyState from '../../EmptyState';
import {
  columnWidthSmall,
  columnWidthSmaller,
  dateFormat,
} from '../ResultTable';
import MLSStyles from './MLS.css';
// import MLSModal from './MLSModal';
import ImageDetailTooltip from '@/components/ImageDetailTooltip';
import { useElementSize } from '@/hooks';
import { arrayDifference } from '@/utils/arrayMethods';
import {
  getCityCodeViaMetroName,
  getMetroNameForParam,
} from '@/utils/geography';
import { default as turf_distance } from '@turf/distance';
import { point } from '@turf/helpers';
import isEmpty from 'lodash.isempty';
import { renderToNodeStream, renderToString } from 'react-dom/server';
import { sanantonioZipCodes } from '../../../constants';
import ExportToCSV from '../../ExportToCSV/ExportToCSV';

export let selectedRowKey;
export let rentAverageMLS;

let imageDetailTooltipContainer = null;

export const generateColumnsMLS = (
  leaseMode,
  leaseSort,
  saleSort,
  drawnCustomPolygons,
  eventCoordinates,
  dataSource,
) => {
  console.log('leaseSort: ', leaseSort);
  const columns = [
    // {
    //   title: 'Saved',
    //   dataIndex: 'saved',
    //   width: 50,
    //   align: 'center',
    //   fixed: 'left',
    //   render: (text, record) => {
    //     <div
    //       key='saved status wrapper'
    //       style={{
    //         display: 'flex',
    //         justifyContent: 'center',
    //         alignItems: 'center',
    //       }}
    //     >
    //       {

    //       }
    //     </div>
    //   }
    // },
    {
      title: 'Address',
      dataIndex: 'fulladdress',
      width: 300,
      align: 'left',
      fixed: 'left',
      render: (text, record) => (
        <ImageDetailTooltip
          mlsProperty={record}
          customContainerRef={imageDetailTooltipContainer}
        >
          <span>
            {capitalize(text.replace(/ /g, ' ') + ', ' + record.city) +
              ', ' +
              record.stateorprovince +
              ' ' +
              record.zipcode +
              '  '}
            {/* <FileImageTwoTone className='imageIconInAddress' twoToneColor='#1890ff' /> */}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              width="14"
              height="14"
              className={styles.imageIconForAddress}
            >
              <path d="M7.479 12.083h8.709l-2.626-3.937-2.208 3.146-1.729-2.188Zm-.646 3.5q-.895 0-1.541-.645-.646-.646-.646-1.542V3.375q0-.896.646-1.542.646-.645 1.541-.645h10.021q.917 0 1.552.645.636.646.636 1.542v10.021q0 .896-.636 1.542-.635.645-1.552.645Zm-3.687 3.688q-.917 0-1.552-.636Q.958 18 .958 17.083V4.875h2.188v12.208h12.208v2.188Z" />
            </svg>
          </span>
        </ImageDetailTooltip>
      ),
      // ellipsis: true,
    },
    {
      title: 'Dist.',
      // dataIndex: ['properties', 'address'],
      dataIndex: 'distance',
      width: 100,
      key: 'distance',
      align: 'center',
      render: (text, record) => {
        if (drawnCustomPolygons.length > 0 && eventCoordinates.length > 0) {
          const propertyPoint = point(record.geography.coordinates);
          const eventPoint = point(eventCoordinates);
          const distance = turf_distance(propertyPoint, eventPoint, 'miles');

          return distance.toFixed(1) + ' mi';
        }
        return (parseFloat(text) / 1609.34).toFixed(1) + ' mi';
      },
      sorter: (a, b) => {
        if (drawnCustomPolygons.length > 0 && eventCoordinates.length > 0) {
          const aPoint = point(a.geography.coordinates);
          const bPoint = point(b.geography.coordinates);
          const eventPoint = point(eventCoordinates);
          const adistance = turf_distance(aPoint, eventPoint, 'miles');
          const bdistance = turf_distance(bPoint, eventPoint, 'miles');

          return adistance - bdistance;
        }
        return a.distance - b.distance;
      },
      // sortDirections: ['ascend', 'descend'],
      // defaultSortOrder: 'ascend',
      // sortOrder: leaseSort.columnKey === 'distance' ? leaseSort.order : null,
      sortOrder:
        leaseMode && leaseSort.columnKey === 'distance'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'distance'
          ? saleSort.order
          : null,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      width: 100,
      key: 'status',
      align: 'center',
      sorter: (a, b) => sortString(a.status, b.status),
      // sortDirections: ['ascend', 'descend'],
      sortOrder:
        leaseMode && leaseSort.columnKey === 'status'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'status'
          ? saleSort.order
          : null,
    },
    {
      title: 'SFR Owner',
      // dataIndex: ['properties', 'brand'],
      dataIndex: 'sfr_owner',
      width: 100,
      align: 'center',
      render: (text) => (text ? showSFROperatorsFullName(text) : ''),
      filters: [
        ...dataSource.reduce((result, property, idx) => {
          const exists = result.find((r) => r.value === property.sfr_owner);

          if (idx === 0) {
            result.push({
              text: 'N/A',
              value: 'N/A',
            });
          }

          if (
            !exists &&
            property.sfr_owner &&
            property.sfr_owner !== 'Rently'
          ) {
            result.push({
              value: property.sfr_owner,
              text: property.sfr_owner
                ? showSFROperatorsFullName(property.sfr_owner) == ''
                  ? 'N/A'
                  : showSFROperatorsFullName(property.sfr_owner)
                : 'N/A',
            });
          }
          return result;
        }, []),
      ],
      onFilter: (value, record) => {
        if (
          value === 'N/A' &&
          (!record.sfr_owner ||
            record.sfr_owner === 'Rently' ||
            showSFROperatorsFullName(record.sfr_owner) == '')
        )
          return true;
        return record.sfr_owner === value;
      },
      // ellipsis: true,
    },
    {
      title: leaseMode ? 'Rent' : 'Latest Sold',
      dataIndex: 'latestPrice',
      key: 'latestPrice',
      width: 100,
      align: 'center',
      render: (text) => '$' + formatter(text),
      sorter: (a, b) => a.latestPrice - b.latestPrice,
      // sortDirections: ['ascend', 'descend'],
      sortOrder:
        leaseMode && leaseSort.columnKey === 'latestPrice'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'latestPrice'
          ? saleSort.order
          : null,
    },
    {
      title: 'Last Sale Price',
      dataIndex: 'deed_last_sale_price',
      key: 'deed_last_sale_price',
      align: 'center',
      width: 100,
      render: (text) => {
        if (text) {
          if (text == '0') {
            return 'N/A';
          }
          return '$' + formatter(text);
        } else {
          return '';
        }
      },
      sorter: (a, b) => {
        if (a.deed_last_sale_price && b.deed_last_sale_price) {
          return +a.deed_last_sale_price - +b.deed_last_sale_price;
        } else if (a.deed_last_sale_price) {
          return -1;
        } else if (b.deed_last_sale_price) {
          return 1;
        }
      },
      sortOrder:
        leaseMode && leaseSort.columnKey === 'deed_last_sale_price'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'deed_last_sale_price'
          ? saleSort.order
          : null,
    },
    {
      title: 'Last Sale Date',
      dataIndex: 'deed_last_sale_date',
      key: 'deed_last_sale_date',
      align: 'center',
      width: 100,
      render: (text) => {
        if (text) {
          return moment(text, dateFormat).format(dateFormat);
        } else {
          return '';
        }
      },
      sorter: (a, b) => {
        if (a.deed_last_sale_date && b.deed_last_sale_date) {
          return (
            +moment(a.deed_last_sale_date) - +moment(b.deed_last_sale_date)
          );
        } else if (a.deed_last_sale_date) {
          return -1;
        } else if (b.deed_last_sale_date) {
          return 1;
        }
      },
      sortOrder:
        leaseMode && leaseSort.columnKey === 'deed_last_sale_date'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'deed_last_sale_date'
          ? saleSort.order
          : null,
    },
    {
      title: 'Chg. $(%)',
      showSorterTooltip: {
        title: 'Total cumulative change since original listing',
      },
      dataIndex: 'latestPrice',
      key: 'change',
      align: 'center',
      width: 100,
      render: (text, record) => {
        const originalPrice = record.orginalprice;
        const latestPrice = record.latestPrice;
        const change = latestPrice - originalPrice;
        const changePercent = Math.round((change / originalPrice) * 100);

        if (change && change > 0 && isFinite(changePercent)) {
          return (
            <span style={{ color: '#3f8600' }}>
              +${formatter(change)}
              <br />
              (+{changePercent}%)
            </span>
          );
        } else if (change && change < 0 && isFinite(changePercent)) {
          return (
            <span style={{ color: '#cf1322' }}>
              -${formatter(Math.abs(change))}
              <br />({changePercent}%)
            </span>
          );
        } else {
          return <span>$0{'(0%)'}</span>;
        }
      },
      sorter: (a, b) => {
        const aChange = a.latestPrice - a.orginalprice;
        const bChange = b.latestPrice - b.orginalprice;
        return aChange - bChange;
      },
      sortOrder:
        leaseMode && leaseSort.columnKey === 'change'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'change'
          ? saleSort.order
          : null,
    },
    {
      title: 'Type',
      dataIndex: 'propertysubtype',
      key: 'propertysubtype',
      width: 100,
      align: 'center',
      // sorter: (a, b) => sortString(a.propertysubtype, b.propertysubtype),
      filters: [
        ...dataSource.reduce((result, property) => {
          const exists = result.find(
            (r) => r.value === property.propertysubtype,
          );

          const textFormat = (text) => {
            if (['Single Family Residence'].includes(text)) return 'SFR';
            if (['Single Family Detached'].includes(text)) return 'SFD';
            if (text === 'Townhouse') return 'TH';
            return text;
          };

          if (!exists && property.propertysubtype) {
            const text = result.push({
              text: textFormat(property.propertysubtype),
              value: property.propertysubtype,
            });
          }
          return result;
        }, []),
      ],
      onFilter: (value, record) => record.propertysubtype === value,
      render: (text) => {
        if (['Single Family Residence'].includes(text)) {
          return 'SFR';
        } else if (['Single Family Detached'].includes(text)) {
          return 'SFD';
        } else if (text === 'Townhouse') {
          return 'TH';
        } else {
          return text;
        }
      },
      // sortDirections: ['ascend', 'descend'],
      sortOrder:
        leaseMode && leaseSort.columnKey === 'propertysubtype'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'propertysubtype'
          ? saleSort.order
          : null,
      ellipsis: true,
    },
    {
      title: 'YrB',
      dataIndex: 'yearbuilt',
      key: 'yearbuilt',
      width: 100,
      align: 'center',
      sorter: (a, b) => a.yearbuilt - b.yearbuilt,
      // sortDirections: ['descend', 'ascend'],
      sortOrder:
        leaseMode && leaseSort.columnKey === 'yearbuilt'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'yearbuilt'
          ? saleSort.order
          : null,
    },
    {
      title: 'Bd',
      dataIndex: 'bed',
      key: 'bed',
      width: 100,
      align: 'center',
      sorter: (a, b) => a.bed - b.bed,
      // sortDirections: ['ascend', 'descend'],
      sortOrder:
        leaseMode && leaseSort.columnKey === 'bed'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'bed'
          ? saleSort.order
          : null,
    },
    {
      title: 'Ba',
      dataIndex: 'bath',
      key: 'bath',
      width: 100,
      align: 'center',
      sorter: (a, b) => a.bath - b.bath,
      // sortDirections: ['ascend', 'descend'],
      sortOrder:
        leaseMode && leaseSort.columnKey === 'bath'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'bath'
          ? saleSort.order
          : null,
    },
    {
      title: 'Sqft',
      dataIndex: 'size',
      key: 'size',
      width: 100,
      align: 'center',
      render: (text) => (text ? formatter(text) : ''),
      sorter: (a, b) => a.size - b.size,
      // sortDirections: ['ascend', 'descend'],
      sortOrder:
        leaseMode && leaseSort.columnKey === 'size'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'size'
          ? saleSort.order
          : null,
    },
    {
      title: 'Lot Size',
      dataIndex: 'area_acres',
      key: 'area_acres',
      align: 'center',
      width: 100,
      render: (text) => (text ? `${text.toFixed(2)} ac` : ''),
      sorter: (a, b) => a.area_acres - b.area_acres,
      sortOrder:
        leaseMode && leaseSort.columnKey === 'area_acres'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'area_acres'
          ? saleSort.order
          : null,
    },
    {
      title: 'CDOM',
      dataIndex: 'cdom',
      key: 'cdom',
      width: 100,
      align: 'center',
      sorter: (a, b) => a.cdom - b.cdom,
      // sortDirections: ['ascend', 'descend'],
      sortOrder:
        leaseMode && leaseSort.columnKey === 'cdom'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'cdom'
          ? saleSort.order
          : null,
    },
    {
      title: 'Cls.',
      // dataIndex: ['properties', 'available_date'],
      dataIndex: 'closedate',
      key: 'closedate',
      width: 100,
      align: 'center',
      render: (text) => {
        if (text) {
          return moment(text).format(dateFormat);
        } else {
          return '';
        }
      },
      sorter: (a, b) => {
        if (a.closedate && b.closedate) {
          return +moment(a.closedate) - +moment(b.closedate);
        } else {
          return a.closedate || b.closedate;
        }
      },
      sortOrder:
        leaseMode && leaseSort.columnKey === 'closedate'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'closedate'
          ? saleSort.order
          : null,
    },
    {
      title: 'PSF',
      dataIndex: 'latestPrice',
      key: 'psf',
      width: 100,
      align: 'center',
      render: (text, record) => {
        if (text && record.size) {
          return '$' + (text / record.size).toFixed(2);
        }
      },
      sorter: (a, b) => a.latestPrice / a.size - b.latestPrice / b.size,
      // sortDirections: ['ascend', 'descend'],
      sortOrder:
        leaseMode && leaseSort.columnKey === 'psf'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'psf'
          ? saleSort.order
          : null,
    },
  ];

  if (drawnCustomPolygons.length > 0 && eventCoordinates.length === 0) {
    columns.splice(1, 1); // remove distance column
  }

  return columns;
};

export const openMLSImageModal = (record, props) => {
  let key = record.listingkey;

  if (
    ['sanantonio', 'san antonio'].includes(record.metro.toLowerCase()) ||
    getMetroNameForParam(record, true) === 'sanantonio'
  ) {
    key = record.mlsid;
  }

  props.dispatch({
    type: 'CMA/getBatchMLSPropertyImages',
    payload: {
      key: key,
      city: getMetroNameForParam(record, true), // true for using realtrac instead of nashville
    },
  });

  props.dispatch({
    type: 'CMA/getHouseDetailsData',
    payload: {
      from: 'MLS Table',
      listingID: record.mlsid,
      cityCode: getCityCodeViaMetroName(record.metro),
    },
  });
  props.dispatch({
    type: 'CMA/getAPNOwnerName',
    payload: {
      from: 'MLS Table',
      placekey: record.placekey,
    },
  });

  props.dispatch({
    type: 'CMA/saveCMAStates',
    payload: {
      selectedMLProperty: record,
      openMLSImageModal: true,
    },
  });
};

const MLS = connect(({ CMA }) => ({
  subjectPropertyParcelData: CMA.subjectPropertyParcelData,
  currentMLSPropertiesFiltered: CMA.currentMLSPropertiesFiltered,
  searchingMode: CMA.searchingMode,
  mapExpandedView: CMA.mapExpandedView,
  openMLSImageModal: CMA.openMLSImageModal,
  userGroup: CMA.userGroup,
  MLSTableSortLease: CMA.MLSTableSortLease,
  MLSTableSortSale: CMA.MLSTableSortSale,
  mlsTableFilters: CMA.mlsTableFilters,
  drawnCustomPolygons: CMA.drawnCustomPolygons,
  eventCoordinates: CMA.eventCoordinates,
  mapLocateProperty: CMA.mapLocateProperty,
  cmaTabKey: CMA.cmaTabKey,
  compingMode: CMA.compingMode,
  fetchCompMLSDone: CMA.fetchCompMLSDone,
  selectedRowKeysMLSLease: CMA.selectedRowKeysMLSLease,
  selectedRowKeysMLSSale: CMA.selectedRowKeysMLSSale,
}))(function (props) {
  const mlsContainer = useRef(null);
  const mlsTooltip = useRef(null);
  imageDetailTooltipContainer = useRef(null);

  const tableSize = useElementSize(mlsContainer);

  const [scrollToSelectedRow, setScrollToSelectedRow] = useState(false);
  const [previousMLSPropertiesFiltered, setPreviousMLSPropertiesFiltered] =
    useState([]);
  const [prevSubjectPropertyParcelData, setPrevSubjectPropertyParcelData] =
    useState(props.subjectPropertyParcelData);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [currentTableData, setCurrentTableData] = useState([]); // current table data with current sorting and filtering
  const [unselectedRowKeysLease, setUnselectedRowKeysLease] = useState([]);
  const [unselectedRowKeysSales, setUnselectedRowKeysSales] = useState([]); // for keeping selection when currentMLSPropertiesFiltered or filters have changed

  // sort the table data based on MLSTableSortLease or MLSTableSortSale
  const getInitialTableData = () => {
    if (props.searchingMode === 'Lease') {
      if (props.MLSTableSortLease.order === 'ascend') {
        return props.currentMLSPropertiesFiltered.sort(
          (a, b) =>
            a[props.MLSTableSortLease.columnKey] -
            b[props.MLSTableSortLease.columnKey],
        );
      } else {
        return props.currentMLSPropertiesFiltered.sort(
          (a, b) =>
            b[props.MLSTableSortLease.columnKey] -
            a[props.MLSTableSortLease.columnKey],
        );
      }
    } else if (props.searchingMode === 'Sale') {
      if (props.MLSTableSortSale.order === 'ascend') {
        return props.currentMLSPropertiesFiltered.sort(
          (a, b) =>
            a[props.MLSTableSortSale.columnKey] -
            b[props.MLSTableSortSale.columnKey],
        );
      } else {
        return props.currentMLSPropertiesFiltered.sort(
          (a, b) =>
            b[props.MLSTableSortSale.columnKey] -
            a[props.MLSTableSortSale.columnKey],
        );
      }
    }
  };
  console.log('getInitialTableData', getInitialTableData());

  // get current table data after currentMLSPropertiesFiltered has changed
  // and keep selection
  const getCurrentTableData = () => {
    // first, sort the data based on current sort order
    const allMLSCompsDataSorted = getInitialTableData();
    console.log('allMLSCompsDataSorted', allMLSCompsDataSorted);
    console.log('props.mlsTableFilters', props.mlsTableFilters);
    // remove filters that are null, e.g. { propertysubtype: null }
    const nonNullFilters = Object.fromEntries(
      Object.entries(props.mlsTableFilters).filter(
        ([_, value]) => value !== null,
      ),
    );
    // then, filter the data based on current filters
    const currentTableData = allMLSCompsDataSorted.filter((row) => {
      return Object.keys(nonNullFilters).every((filter) => {
        return nonNullFilters[filter].includes(row[filter]);
      });
    });
    // then, remove unselected rows from all row keys
    const unselectedRowKeys =
      props.searchingMode === 'Lease'
        ? unselectedRowKeysLease
        : unselectedRowKeysSales;
    const selectedRowKeys = arrayDifference(
      currentTableData.map((row) => row.mlsid),
      unselectedRowKeys,
    );
    return { currentTableData, selectedRowKeys };
  };

  // clear unselected row keys for new subject property
  if (
    !isEqual(props.subjectPropertyParcelData, prevSubjectPropertyParcelData)
  ) {
    setPrevSubjectPropertyParcelData(props.subjectPropertyParcelData);
    setUnselectedRowKeysLease([]);
    setUnselectedRowKeysSales([]);
  }

  // after currentMLSPropertiesFiltered has changed
  // get current table data and selected row keys
  if (
    !isEqual(props.currentMLSPropertiesFiltered, previousMLSPropertiesFiltered)
  ) {
    const { currentTableData, selectedRowKeys } = getCurrentTableData();
    setCurrentTableData(currentTableData);
    props.onSelectChangeMLS(selectedRowKeys);
    setPreviousMLSPropertiesFiltered(props.currentMLSPropertiesFiltered);
  }

  selectedRowKey = props.selectedRowKeysMLS; // can be lease or sales rows
  rentAverageMLS = props.rentAverageMLS;

  useEffect(() => {
    const click = (e) => {
      if (e.target.id === 'locatePropertyButton' || !mlsContainer.current)
        return;

      const row = mlsContainer.current.querySelector(
        `.${styles.mapLocatePropertySelected}`,
      );
      if (row) {
        row.classList.remove(styles.mapLocatePropertySelected);
        props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: { mapLocateProperty: {} },
        });
      }
    };

    document.addEventListener('click', click);
    return () => {
      document.removeEventListener('click', click);
    };
  }, []);

  useEffect(() => {
    if (scrollToSelectedRow && mlsContainer.current) {
      console.log('HERE');
      const row = mlsContainer.current.querySelector(
        `.${styles.mapLocatePropertySelected}`,
      );
      console.log('row: ', row);
      if (row) {
        row.scrollIntoView({ behavior: 'smooth', block: 'center' });
        setScrollToSelectedRow(false);
      }
    }
  }, [scrollToSelectedRow]);

  useEffect(() => {
    if (
      !isEmpty(props.mapLocateProperty) &&
      props.mapLocateProperty.type === 'MLS'
    ) {
      const rowData = currentTableData.find(
        (property) => property.mlsid === props.mapLocateProperty.id,
      );
      console.log('rowData: ', rowData);
      if (rowData) {
        const rowIndex = currentTableData.indexOf(rowData);
        console.log('rowIndex: ', rowIndex);
        const rowPage = Math.floor(rowIndex / pageSize) + 1;
        console.log('rowPage: ', rowPage);
        setCurrentPage(rowPage);
        if (props.cmaTabKey !== '1') {
          props.dispatch({
            type: 'CMA/saveCMAStates',
            payload: { cmaTabKey: '1' },
          });
        }
        setTimeout(() => {
          setScrollToSelectedRow(true);
        }, 100);
      }
    }
  }, [props.mapLocateProperty]);

  const generateColumnsMLSMinified = [
    {
      title: 'Address',
      dataIndex: 'fulladdress',
      width: 175,
      align: 'left',
      render: (text, record) => (
        <span>
          {capitalize(text.replace(/ /g, ' ') + ', ' + record.city) +
            ', ' +
            record.stateorprovince +
            ' ' +
            record.zipcode}
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            width="16"
            height="16"
            className={styles.imageIconForAddress}
          >
            <path d="M7.479 12.083h8.709l-2.626-3.937-2.208 3.146-1.729-2.188Zm-.646 3.5q-.895 0-1.541-.645-.646-.646-.646-1.542V3.375q0-.896.646-1.542.646-.645 1.541-.645h10.021q.917 0 1.552.645.636.646.636 1.542v10.021q0 .896-.636 1.542-.635.645-1.552.645Zm-3.687 3.688q-.917 0-1.552-.636Q.958 18 .958 17.083V4.875h2.188v12.208h12.208v2.188Z" />
          </svg>
        </span>
      ),
      // ellipsis: true,
    },
    {
      title: props.searchingMode === 'Lease' ? 'Rent' : 'Sold',
      dataIndex: 'latestPrice',
      key: 'latestPrice',
      // width: columnWidthSmaller,
      align: 'center',
      render: (text) => '$' + formatter(text),
      sorter: (a, b) => a.latestPrice - b.latestPrice,
      // sortDirections: ['ascend', 'descend'],
      sortOrder:
        props.searchingMode === 'Lease' &&
        props.MLSTableSortLease.columnKey === 'latestPrice'
          ? props.MLSTableSortLease.order
          : props.searchingMode === 'Sale' &&
            props.MLSTableSortSale.columnKey === 'latestPrice'
          ? props.MLSTableSortSale.order
          : null,
    },
    {
      title: 'Sqft',
      dataIndex: 'size',
      // width: columnWidthSmaller,
      key: 'size',
      align: 'center',
      render: (text) => (text ? formatter(text) : ''),
      sorter: (a, b) => a.size - b.size,
      // sortDirections: ['ascend', 'descend'],
      sortOrder:
        props.searchingMode === 'Lease' &&
        props.MLSTableSortLease.columnKey === 'size'
          ? props.MLSTableSortLease.order
          : props.searchingMode === 'Sale' &&
            props.MLSTableSortSale.columnKey === 'size'
          ? props.MLSTableSortSale.order
          : null,
    },
    {
      title: 'PSF',
      dataIndex: 'latestPrice',
      // width: columnWidthSmaller,
      key: 'psf',
      align: 'center',
      render: (text, record) => {
        if (text && record.size) {
          return '$' + (text / record.size).toFixed(2);
        }
      },
      sorter: (a, b) => a.latestPrice / a.size - b.latestPrice / b.size,
      // sortDirections: ['ascend', 'descend'],
      sortOrder:
        props.searchingMode === 'Lease' &&
        props.MLSTableSortLease.columnKey === 'psf'
          ? props.MLSTableSortLease.order
          : props.searchingMode === 'Sale' &&
            props.MLSTableSortSale.columnKey === 'psf'
          ? props.MLSTableSortSale.order
          : null,
    },
  ];

  const onRowMLS = (record) => {
    return {
      onClick: (event) => {
        console.log('record', record);
        openMLSImageModal(record, props);
      },
      onMouseEnter: (event) => {
        if (
          !isEqual(
            props.currentHighlightCoordinates,
            record.geography.coordinates,
          )
        ) {
          props.dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              currentHighlightCoordinates: record.geography.coordinates,
              priceHighlightMarker: record.latestPrice,
              typeHighlightMarker: 'MLS',
            },
          });
        }

        if (!props.mapExpandedView) return;
        const { status, propertytype, yearbuilt, bed, bath, cdom, closedate } =
          record;

        const distance =
          (parseFloat(record.distance) / 1609.34).toFixed(1) + ' mi';

        const tooltipMessage = `Distance: ${distance}\nStatus: ${status}\nProperty Type: ${propertytype}\nYear Built:${yearbuilt}\nBeds: ${bed}\bBaths: ${bath}\nDOM: ${cdom}\nClose Date: ${closedate}`;

        mlsTooltip.current.innerText = tooltipMessage;
        mlsTooltip.current.style.display = 'block';

        const containerPos = mlsContainer.current.getBoundingClientRect();
        const rowPos = event.currentTarget.getBoundingClientRect();

        mlsTooltip.current.style.top = `${
          rowPos.top - containerPos.top - mlsTooltip.current.clientHeight + 185
        }px`;
        mlsTooltip.current.style.left = '50%';
        mlsTooltip.current.style.transform = 'translateX(-50%)';
      },

      onMouseLeave: (event) => {
        if (!props.mapExpandedView) return;

        mlsTooltip.current.innerText = '';
        mlsTooltip.current.style.display = 'none';
      },
    };
  };

  const tableHeader = (
    <Row
      id="mlsTableHeader"
      key="table title row MLS"
      align="bottom"
      justify="space-between"
      wrap={true}
      gutter={[0, 8]}
      style={{ marginBottom: 12 }}
    >
      <Col key="table title" className={styles.cardTitleH2}>
        MLS
      </Col>
      <Col key="MLS summary row wrapper">
        <Row key="MLS summary row" align="middle" justify="end" gutter={24}>
          <Col key="total unit">
            <span
              key="total unit text"
              className={styles.cardSubtitle}
              style={{ marginRight: 8 }}
            >
              Total Selected
            </span>
            <span
              id="mlsTable-Total"
              key="total unit number"
              className={styles.cardDataValue}
            >
              {/* {props.currentMLSPropertiesFiltered.length || '-'} */}
              {(props.searchingMode === 'Lease'
                ? props.selectedRowKeysMLSLease.length
                : props.selectedRowKeysMLSSale.length) || '-'}
            </span>
          </Col>
          <Col key="MLS avg">
            <span
              key="MLS avg text"
              className={styles.cardSubtitle}
              style={{ marginRight: 8 }}
            >
              {props.searchingMode === 'Lease' ? 'Median Rent' : 'Median Sale'}
            </span>
            <span
              id="mlsTable-Median"
              key="MLS avg number"
              className={styles.cardDataValue}
            >
              {props.rentMedianMLS ? '$' + formatter(props.rentMedianMLS) : '-'}
            </span>
          </Col>
        </Row>
      </Col>
    </Row>
  );

  return (
    <>
      <div
        ref={mlsContainer}
        key="MLS card"
        className={styles.cardWrapperMLS}
        style={{ marginTop: '16px' }}
      >
        {/* <div key="divider title" className={styles.dividerCardHeader} /> */}
        <Table
          key="mls table"
          className="table-sticky-title-header"
          rowKey={(record) => record.mlsid}
          columns={
            props.mapExpandedView
              ? generateColumnsMLSMinified
              : generateColumnsMLS(
                  props.searchingMode === 'Lease',
                  props.MLSTableSortLease,
                  props.MLSTableSortSale,
                  props.drawnCustomPolygons,
                  props.eventCoordinates,
                  props.currentMLSPropertiesFiltered,
                )
          }
          dataSource={props.currentMLSPropertiesFiltered}
          variant={'filled'}
          size="small"
          // pagination={false}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            onChange: (page, pageSize) => {
              setCurrentPage(page);
              setPageSize(pageSize);
            },
          }}
          loading={!props.fetchCompMLSDone}
          rowSelection={{
            // defaultSelectedRowKeys: props.allRowKeysMLS,
            selectedRowKeys: props.selectedRowKeysMLS,
            onChange: (selectedRowKeys) => {
              const unselectedRowKeys = arrayDifference(
                currentTableData.map((row) => row.mlsid),
                selectedRowKeys,
              );
              if (props.searchingMode === 'Lease') {
                setUnselectedRowKeysLease(unselectedRowKeys);
              } else if (props.searchingMode === 'Sale') {
                setUnselectedRowKeysSales(unselectedRowKeys);
              }
              props.onSelectChangeMLS(selectedRowKeys);
            },
            // preserveSelectedRowKeys: true,
            selections: [
              Table.SELECTION_ALL,
              Table.SELECTION_INVERT,
              Table.SELECTION_NONE,
            ],
            getCheckboxProps: (record) => ({
              disabled: props.compingMode === 'intelligentComping',
            }),
          }}
          rowClassName={(record, index) => {
            let className = styles.propertyDataTableRow;
            if (
              !isEmpty(props.mapLocateProperty) &&
              props.mapLocateProperty.type === 'MLS'
            ) {
              if (record.mlsid === props.mapLocateProperty.id) {
                className += ' ' + styles.mapLocatePropertySelected;
              }
            }
            return className;
          }}
          onRow={onRowMLS}
          sticky={true}
          components={{
            header: {
              wrapper: ({ className, children }) => {
                return (
                  <thead className={className}>
                    <div style={{ width: tableSize.width - 12 }}>
                      {tableHeader}
                    </div>
                    {children}
                  </thead>
                );
              },
            },
          }}
          // sticky={{
          //   offsetHeader: 48,
          //   // getContainer: () =>
          //   //   document.getElementsByClassName('ant-tabs-content-holder')[0],
          //   // document.getElementById('resultTableScrollWrapper'),
          // }}
          scroll={props.mapExpandedView ? null : { x: 2000 }}
          defaultFilteredValue={props.currentMLSPropertiesFiltered}
          filterResetToDefaultFilteredValue={true}
          onChange={(pagination, filters, sorter, extra) => {
            console.log('mls filters: ', filters);
            console.log('lease', sorter);
            console.log('MLS table onChange extra', extra);
            console.log('extra.currentDataSource', extra.currentDataSource);
            if (!isEqual(extra.currentDataSource, currentTableData)) {
              setCurrentTableData(extra.currentDataSource);
            }
            switch (extra.action) {
              case 'sort':
                props.dispatch({
                  type: 'CMA/saveCMAStates',
                  payload: {
                    ...(props.searchingMode === 'Lease'
                      ? { MLSTableSortLease: sorter }
                      : { MLSTableSortSale: sorter }),
                  },
                });
                break;
              case 'filter':
                // selectedRowKeys are not guaranteed to update automatically
                // so we need to manually update them
                // and use unselectedRowKeys to keep selection
                const allRowKeys = extra.currentDataSource.map(
                  (item) => item.mlsid,
                );
                const unselectedRowKeys =
                  props.searchingMode === 'Lease'
                    ? unselectedRowKeysLease
                    : unselectedRowKeysSales;
                const selectedRowKeys = arrayDifference(
                  allRowKeys,
                  unselectedRowKeys,
                );
                props.dispatch({
                  type: 'CMA/saveCMAStates',
                  payload: {
                    mlsTableFilters: filters,
                    ...(props.searchingMode === 'Lease'
                      ? { selectedRowKeysMLSLease: selectedRowKeys }
                      : { selectedRowKeysMLSSale: selectedRowKeys }),
                  },
                });
                break;
              default:
                break;
            }
          }}
          summary={(currentData) => {
            return (
              <Table.Summary fixed>
                <Table.Summary.Row>
                  <Table.Summary.Cell colSpan={props.mapExpandedView ? 2 : 5}>
                    {'Median '}
                    {props.searchingMode === 'Lease' ? 'Rent' : 'Sales'} of
                    Selected
                  </Table.Summary.Cell>
                  <Table.Summary.Cell
                    className={styles.tableSummaryCellTextAlignCenter}
                  >
                    $
                    {props.rentMedianMLS ? formatter(props.rentMedianMLS) : '-'}
                  </Table.Summary.Cell>
                </Table.Summary.Row>
              </Table.Summary>
            );
          }}
        />
        <div ref={mlsTooltip} className={styles.customToolTip}></div>
        <div
          ref={imageDetailTooltipContainer}
          className="image-detail-tooltip-container"
        ></div>
      </div>
    </>
  );
});

export default MLS;
