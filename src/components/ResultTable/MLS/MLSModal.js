import { formatter } from '@/utils/money';
import { Button, Empty, Modal, Spin, Tabs } from 'antd';
import React, { useState } from 'react';
import { IoClose } from 'react-icons/io5';
import { Carousel } from 'react-responsive-carousel';
import 'react-responsive-carousel/lib/styles/carousel.min.css'; // requires a loader
import { connect } from 'umi';
import HouseDetails from '../../HouseDetails/HouseDetails';
import styles from './MLS.css';
import OwnerInformations from './OwnerInformations';

const MLSModal = connect(({ CMA }) => ({
  currentMLSPropertyImages: CMA.currentMLSPropertyImages,
  userGroup: CMA.userGroup,
  selectedMLProperty: CMA.selectedMLProperty,
  MLSImageModalIsLoading: CMA.MLSImageModalIsLoading,
  currentHouseDetails: CMA.currentHouseDetails,
  currentAPNOwner: CMA.currentAPNOwner,
  currentSingleListingBrokerOfficeInfo:
    CMA.currentSingleListingBrokerOfficeInfo,
}))(function (props) {
  const [selectedCarouselImg, setSelectedCarouselImg] = useState(0);
  const [showCarousel, setShowCarousel] = useState(false);
  console.log('OwnerInformations props', props.userGroup);

  const closeModalHandler = (e) => {
    e.stopPropagation();
    // clear and close modal images
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        currentMLSPropertyImages: [],
        openMLSImageModal: false,
        persons: null,
      },
    });
  };

  const handleImageClick = (e) => {
    e.stopPropagation();
    setSelectedCarouselImg(Number(e.currentTarget.dataset.index));
    setShowCarousel(true);
  };

  console.log(props.currentMLSPropertyImages);

  console.log('currentAPNOwner: ', props.currentAPNOwner);
  console.log('currentHouseDtails: ', props.currentHouseDetails);
  console.log(
    'currentSingleListingBrokerOfficeInfo: ',
    props.currentSingleListingBrokerOfficeInfo,
  );

  // Check if user group includes "demo-users" or "AvenueOne"
  const canViewOwnerInfo = () => {
    if (!props.userGroup) return false;

    // If userGroup is a string
    if (typeof props.userGroup === 'string') {
      return (
        props.userGroup.includes('demo-users') ||
        props.userGroup.includes('AvenueOne') ||
        props.userGroup.includes('REMAXFine')
      );
    }

    // If userGroup is an array
    if (Array.isArray(props.userGroup)) {
      return props.userGroup.some(
        (group) =>
          group === 'demo-users' ||
          group === 'AvenueOne' ||
          group === 'REMAXFine' ||
          (typeof group === 'string' &&
            (group.includes('demo-users') || group.includes('AvenueOne'))),
      );
    }

    return false;
  };

  // Define base tabs that are always available
  const baseTabs = [
    {
      key: '1',
      label: 'Images',
      children: (
        <>
          <div className={styles.MLSModalBodyContent}>
            {props.currentMLSPropertyImages.map((imgUrl, index) => (
              <div
                key={index}
                data-index={index}
                onClick={handleImageClick}
                className={
                  index === 0
                    ? styles.MLSModalBodyContentFirstImage
                    : styles.MLSModalBodyContentImage
                }
              >
                <img src={imgUrl} alt="" />
              </div>
            ))}
          </div>
          {props.currentMLSPropertyImages.length === 0 && (
            <div className={styles.MLSFeedbackContainer}>
              {props.MLSImageModalIsLoading ? (
                <Spin tip="Loading..." size="large" />
              ) : (
                <Empty description={<span>No Images Available</span>} />
              )}
            </div>
          )}
        </>
      ),
    },
    {
      key: '2',
      label: 'House Details',
      children: (
        <HouseDetails
          currentHouseDetails={props.currentHouseDetails}
          currentAPNOwner={props.currentAPNOwner}
          currentSingleListingBrokerOfficeInfo={
            props.currentSingleListingBrokerOfficeInfo
          }
        />
      ),
    },
  ];

  // Add owner information tab conditionally
  const ownerInfoTab = {
    key: '3',
    label: 'Contact Info',
    children: (
      <OwnerInformations currentHouseDetails={props.currentHouseDetails} />
    ),
  };

  // Combine tabs based on permission
  const tabItems = canViewOwnerInfo() ? [...baseTabs, ownerInfoTab] : baseTabs;

  const tabBarOnChange = () => {
    const tabContent = document.querySelector(
      '#MLSImageModalTabs .ant-tabs-content-holder',
    );
    tabContent.scrollTop = 0;
  };

  let price = props.selectedMLProperty.latestPrice;
  if (!price) {
    const property = props.selectedMLProperty;
    price = property.closeprice ? property.closeprice : property.currentprice;
  }

  return (
    <>
      <div className={styles.MLSModalWrap}>
        <div className={styles.MLSModalContainer}>
          <div className={styles.MLSModalHeader}>
            <div>
              <div>
                <span className={styles.rentPrice}>${formatter(price)}</span>{' '}
                <span> - {props.selectedMLProperty.status}</span>
              </div>
              <div>
                <span>{props.selectedMLProperty.bed} Bds </span>
                <span>| {props.selectedMLProperty.bath} Ba </span>
                {props.selectedMLProperty.size != null && (
                  <span>
                    | {props.selectedMLProperty.size} Sqft | $
                    {(price / props.selectedMLProperty.size).toFixed(2)}{' '}
                    Price/Sqft
                  </span>
                )}
              </div>
              <div>
                <span>{props.selectedMLProperty.fulladdress}</span>
                <span>
                  , {props.selectedMLProperty.city},{' '}
                  {props.selectedMLProperty.stateorprovince}{' '}
                  {props.selectedMLProperty.zipcode}
                </span>
              </div>
              <div>
                <span>{props.selectedMLProperty.propertysubtype} </span>
                <span>built in {props.selectedMLProperty.yearbuilt}</span>
              </div>
            </div>
            <div className={styles.rightCol}>
              <div>
                <span>
                  {props.selectedMLProperty.cdom} Cumulative Days on Market
                </span>
              </div>
              <div>
                <span>Closed on {props.selectedMLProperty.closedate}</span>
              </div>
            </div>
            <button
              className={styles.MLSModalCloseBtn}
              onClick={closeModalHandler}
            >
              <IoClose />
            </button>
          </div>
          <div className={styles.MLSModalBody}>
            <Tabs
              id="MLSImageModalTabs"
              defaultActiveKey="1"
              items={tabItems}
              size={'small'}
              tabBarStyle={{ padding: '0 20px' }}
              style={{ height: 'calc(100% - 109px)' }}
              onChange={tabBarOnChange}
            />
          </div>
        </div>
      </div>
      <Modal
        getContainer={() => document.getElementById('result-container')}
        title={React.createElement(
          'strong',
          null,
          `${props.selectedMLProperty.fulladdress}, ${props.selectedMLProperty.city}, ${props.selectedMLProperty.stateorprovince} ${props.selectedMLProperty.zipcode}`,
        )}
        wrapClassName={styles.MLSImageModalWrap}
        className={styles.MLSImageModalContainer}
        style={{
          top: 0,
          left: 0,
          bottom: 0,
        }}
        styles={{
          body: {
            width: '100%',
            height: 'calc(100vh - 55px)',
          },
        }}
        width={'100vw'}
        open={showCarousel}
        onCancel={() => {
          setShowCarousel(false);
        }}
        footer={null}
      >
        <Carousel
          autoPlay={false}
          showIndicators={false}
          showArrows={true}
          infiniteLoop={true}
          selectedItem={selectedCarouselImg}
          thumbWidth={175}
        >
          {props.currentMLSPropertyImages.map((imgUrl, index) => (
            <div key={index} className={styles.MLSCarouselSlideWrapper}>
              <img
                src={imgUrl}
                alt=""
                style={{
                  objectFit: 'contain',
                  maxWidth: '100%',
                  maxHeight: '100%',
                }}
              />
            </div>
          ))}
        </Carousel>
      </Modal>
    </>
  );
});

export default MLSModal;
