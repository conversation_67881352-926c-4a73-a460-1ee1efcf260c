.MLSRowClass {
  cursor: pointer;
}

.MLSModalWrap {
  position: absolute !important;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  /* background-color: rgba(0, 0, 0, 0.25); */
}
.MLSModalWrap h2 {
  margin: 0;
  padding: 0;
}
.MLSModalWrap p {
  margin: 0;
  padding: 0;
}

.MLSModalContainer {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-52.5%, -50%);
  background-color: white;
  height: 95%;
  width: 90%;
  overflow: hidden;
  box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  display: flex;
  flex-direction: column;
  border-radius: 10px;
}

.MLSModalHeader {
  padding: 10px 20px 0px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  font-size: 14px;
  position: relative;
}

.MLSModalCloseBtn {
  position: absolute;
  right: 20px;
  top: 10px;
  outline: none;
  padding: 0;
  margin: 0;
  background-color: transparent;
  border: transparent;
  cursor: pointer;
  font-size: 24px;
}

.rentPrice {
  font-size: 24px;
  font-weight: bold;
}

.rightCol {
  display: flex;
  flex-direction: column;
  justify-content: end;
}

.MLSModalBody {
  /* overflow-y: auto; */
  height: 100%;
}

.MLSModalBodyContent {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 4px;
}

.MLSModalBodyContent img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

.MLSModalBodyContentFirstImage {
  grid-column: span 2 / span 2;
  height: 400px;
}

.MLSModalBodyContentImage {
  height: 300px;
}

.MLSImageModalContainer {
  height: 100%;
  width: 100%;
  max-width: 100vw;
}

.MLSCarouselSlideWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 265px);
  background: #fff;
}

.MLSFeedbackContainer {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.MLSImageCellContainer {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  /* width: 300px; */
}
.MLSrowImageIcon {
  display: flex;
  flex-direction: column;
  justify-content: end;
  opacity: 0.5;
}
