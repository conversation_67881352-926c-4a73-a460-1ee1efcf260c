import { getLastSoldPublicRecord } from '@/services/data';
import React, { useEffect, useState } from 'react';
import { connect, useDispatch } from 'umi';
import LastSalePublicRecordTable from './Table';
export interface TCurrentAddress {
  fullAddress?: string;
  streetAddress?: string;
  postalCode?: string;
  city?: string;
  region?: string;
  latitude?: number;
  longitude?: number;
}

type LastSalePublicRecordProps = {
  currentPropertyAddress: TCurrentAddress;
  currentRadiusMile: number;
};

type paramsProps = {
  lat?: number;
  lng?: number;
  radius?: number;
};

// Generated by https://quicktype.io

export interface APIResponse {
  address: string;
  distance: number;
  last_sale_date: string;
  city: string;
  geom: Geom;
  zip_code: string;
  baths: number;
  last_sale_price: number;
  sqft: number;
  year_built: number;
  state: string;
  beds: number;
  propertysubtype: string;
}

export interface Geom {
  type: string;
  coordinates: number[];
}

function isEmptyObject(obj: any): boolean {
  return Object.keys(obj).length === 0 && obj.constructor === Object;
}

const LastSalePublicRecord = (props: LastSalePublicRecordProps) => {
  const [publicRecordData, setPublicRecordData] = useState<any>();

  const dispatch = useDispatch();

  let params: paramsProps = {};
  useEffect(() => {
    console.log('test', props.currentPropertyAddress, props.currentRadiusMile);
    if (!isEmptyObject(props.currentPropertyAddress)) {
      console.log('test currentPropertyAddress', props.currentRadiusMile);
      params = {
        lat: props.currentPropertyAddress.latitude,
        lng: props.currentPropertyAddress.longitude,
        radius: props.currentRadiusMile * 1609.344,
      };
    }
  }, [props.currentPropertyAddress, props.currentRadiusMile]);
  //lastSalePublicRecordLoading
  useEffect(() => {
    const getData = async () => {
      console.log('test params', params);
      if (Object.keys(params).length > 0) {
        try {
          dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              lastSalePublicRecordLoading: true,
            },
          });
          const data = await getLastSoldPublicRecord(params);
          console.log('test data', data);
          if (data) {
            setPublicRecordData(data);
            dispatch({
              type: 'CMA/saveCMAStates',
              payload: {
                lastSalePublicRecordData: data,
                lastSalePublicRecordLoading: false,
              },
            });
          }
        } catch (e) {
          console.error('Error fetching data:', e);
        }
      }
    };
    getData();
  }, [params]);

  //Clear Selected Key
  useEffect(() => {
    console.log('test clear row');
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        lastSalePublicRecordSelectedRowKey: [],
      },
    });
  }, [props.currentPropertyAddress, props.currentRadiusMile]);

  return (
    <div>
      <LastSalePublicRecordTable data={publicRecordData} />
    </div>
  );
};
export default connect(({ CMA }: any) => ({
  currentPropertyAddress: CMA.currentPropertyAddress,
  currentRadiusMile: CMA.currentRadiusMile,
}))(LastSalePublicRecord);
