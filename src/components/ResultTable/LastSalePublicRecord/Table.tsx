import styles from '@/components/ResultTable/resultTable.css';
import { useElementSize } from '@/hooks';
import { arrayDifference } from '@/utils/arrayMethods';
import {
  DataType,
  calculateMedianLastSalePrice,
  calculateMedianPricePerSqFt,
  convert,
  formatCurrency,
  generateSmartFilterExpression,
  getAllSubtypes,
  simplifyPropertySubtypes,
  toTitleCase,
} from '@/utils/lastSalePublicRecord';
import { formatter } from '@/utils/money';
import {
  DownOutlined,
  EnvironmentOutlined,
  UpOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Checkbox,
  Col,
  Dropdown,
  InputNumber,
  Layout,
  MenuProps,
  Row,
  Space,
  Switch,
  Table,
  Tooltip,
} from 'antd';
import type { ColumnsType } from 'antd/es/table/interface';
import { isEqual } from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { connect, useDispatch } from 'umi';

export const NON_DISCLOSURE_STATES = [
  'AK',
  'ID',
  'KS',
  'MS',
  'LA',
  'WY',
  'UT',
  'ND',
  'NM',
  'MT',
  'TX',
];

const origin = window.location.origin;

const LastSalePublicRecordTable = (props: any) => {
  const lsprContainer = useRef(null);
  const tableSize = useElementSize(lsprContainer);
  const [dataForRender, setDataForRender] = useState<DataType[]>([]);
  const [dateRange, setDateRange] = useState<number | null>(180);
  const [loading, setLoading] = useState(true);
  const [matchMode, setMatchMode] = useState<boolean>(true);

  // Selection tracking state
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [currentTableData, setCurrentTableData] = useState<DataType[]>([]);

  // *Filter
  const [tempData, setTempData] = useState<any[]>([]);
  const [tableFilters, setTableFilters] = useState<any>({});

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // *Pop up for loading large data
  const [showTable, setShowTable] = useState<boolean>(false);
  const dispatch = useDispatch();

  // Initialize selection when data changes
  useEffect(() => {
    if (dataForRender.length > 0) {
      // Select all rows by default
      const allKeys = dataForRender.map((row) => row.key);
      setSelectedRowKeys(allKeys);

      // Update Redux state
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          lastSalePublicRecordSelectedRowKey: allKeys,
        },
      });
    }
  }, [dataForRender]);

  // Update calculations when selection changes
  useEffect(() => {
    if (selectedRowKeys.length > 0) {
      const currentData = tempData.length > 0 ? tempData : dataForRender;

      const median = calculateMedianLastSalePrice(
        selectedRowKeys,
        props.lastSalePublicRecordDataForRender || currentData,
      );
      const medianPerSqft = calculateMedianPricePerSqFt(
        selectedRowKeys,
        props.lastSalePublicRecordDataForRender || currentData,
      );

      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          lastSalePublicRecordSelectedRowKey: selectedRowKeys,
          lastSalePublicRecordMedian: median,
          lastSalePublicRecordMedianPricePerSqft: medianPerSqft,
        },
      });
    } else {
      // When no rows are selected, reset the median values
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          lastSalePublicRecordSelectedRowKey: [],
          lastSalePublicRecordMedian: null,
          lastSalePublicRecordMedianPricePerSqft: null,
        },
      });
    }
  }, [selectedRowKeys, dataForRender, tempData]);

  // Reset tempData when dataForRender changes
  useEffect(() => {
    setTempData([]);
    setCurrentTableData(dataForRender);

    // When data changes, select all rows by default
    if (dataForRender.length > 0) {
      const allKeys = dataForRender.map((row) => row.key);
      setSelectedRowKeys(allKeys);
    } else {
      setSelectedRowKeys([]);
    }
  }, [dataForRender]);

  // Handle data filtering and processing
  useEffect(() => {
    if (props.lastSalePublicRecordData != undefined) {
      setTableFilters({});
      setLoading(true);
      const filtered = convert(
        generateSmartFilterExpression({
          subjectPropertyData: props.subjectPropertyParcelData,
          dataSource: props.lastSalePublicRecordData,
        }),
        dateRange,
      );
      console.log(
        'testv5 filtered',
        generateSmartFilterExpression({
          subjectPropertyData: props.subjectPropertyParcelData,
          dataSource: props.lastSalePublicRecordData,
        }),
      );
      if (filtered.length >= 50) {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            lastSalePublicRecordShowTable: false,
            lastSalePublicRecordShowLayer: false,
          },
        });
      } else {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            lastSalePublicRecordShowTable: true,
            lastSalePublicRecordShowLayer: true,
          },
        });
      }
      if (matchMode) {
        setDataForRender(filtered);
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            lastSalePublicRecordDataForRender: filtered,
          },
        });
      } else {
        const allData = convert(props.lastSalePublicRecordData, dateRange);
        setDataForRender(allData);
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            lastSalePublicRecordDataForRender: allData,
          },
        });
      }

      setLoading(false);
    }
  }, [props.lastSalePublicRecordData, dateRange, matchMode]);

  // Handle row selection change
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  // Row selection configuration
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    selections: [
      Table.SELECTION_ALL,
      Table.SELECTION_INVERT,
      Table.SELECTION_NONE,
    ],
    preserveSelectedRowKeys: false, // Ensure keys are cleaned up when data changes
  };

  const getColumns = () => {
    let columnsWithPrice: ColumnsType<DataType> = [
      // ... columns with price definition unchanged
      {
        title: 'Address',
        dataIndex: 'address',
        key: 'address',
        width: 200,
        align: 'left',
        fixed: 'left',
        render: (text: string, record: DataType) => {
          const coordinates = record.geom.coordinates;
          return (
            <p>
              {text ? text : ''}{' '}
              <a
                href={`${origin}/?address=${text}&radius=0.5&minBeds=1&maxBeds=99&minBaths=1&maxBaths=99&minSqft=100&maxSqft=10000&minYearBuilt=1900&maxYearBuilt=2022&minCumulativeDaysOnMarket=0&maxCumulativeDaysOnMarket=999&currentStatusMLS=Closed&currentStartMLS=2024-01-06&currentEndMLS=2024-04-05&searchingMode=Sale`}
                target={
                  ['ILE'].includes(props.selectedUserGroup) ? '_self' : '_blank'
                }
              >
                {' '}
                <Tooltip
                  placement="topLeft"
                  title="Explore this property in new CMA Tab"
                  arrowPointAtCenter
                  overlayInnerStyle={{ fontSize: '12px' }}
                >
                  <EnvironmentOutlined
                    style={{ color: 'blue', fontSize: '14px' }}
                  />
                </Tooltip>
              </a>
            </p>
          );
        },
      },
      {
        title: 'Dist.',
        dataIndex: 'distance',
        key: 'distance',
        width: 55,
        align: 'center',
        render: (text: number) => <p>{(text / 1609.34).toFixed(1)} mi</p>,
        defaultSortOrder: 'ascend',
        sorter: (a, b) => a.distance - b.distance,
      },
      {
        title: 'Last Sale Date',
        dataIndex: 'last_sale_date',
        key: 'last_sale_date',
        width: 100,
        align: 'center',
        render: (text: any) => <p>{text}</p>,
      },
      {
        title: 'Last Sale Price',
        dataIndex: 'last_sale_price',
        key: 'last_sale_price',
        width: 100,
        align: 'center',
        render: (text: number) => <p>{formatCurrency(text)}</p>,

        sorter: (a, b) => a.last_sale_price - b.last_sale_price,
      },
      {
        title: 'YrB',
        dataIndex: 'year_built',
        key: 'year_built',
        width: 55,
        align: 'center',
        sorter: (a, b) => a.year_built - b.year_built,
        render: (text: number) => <p>{text}</p>,
      },

      {
        title: 'Type',
        dataIndex: 'propertysubtype',
        key: 'propertysubtype',
        width: 150,
        align: 'left',
        render: (text: string) => <p>{toTitleCase(text)}</p>,
        filters: simplifyPropertySubtypes(dataForRender),
        onFilter: (value: any, record) => {
          if (record.propertysubtype) {
            return record.propertysubtype.indexOf(value) === 0;
          }
          return false;
        },
      },
      {
        title: 'Sqft',
        dataIndex: 'sqft',
        key: 'sqft',
        width: 50,
        align: 'center',
        render: (text: number) => (
          <p>{new Intl.NumberFormat('en-US').format(text)}</p>
        ),
        sorter: (a, b) => a.sqft - b.sqft,
      },
      {
        title: 'Bd',
        dataIndex: 'bedroom',
        key: 'bedroom',
        width: 55,
        align: 'center',
        render: (text: number) => <p>{text}</p>,
      },
      {
        title: 'Ba',
        dataIndex: 'bathroom',
        key: 'bathroom',
        // width: 30,
        align: 'center',
        render: (text: number) => <p>{text}</p>,
      },
    ];

    let columns: ColumnsType<DataType> = [
      // ... columns without price definition unchanged
      {
        title: 'Address',
        dataIndex: 'address',
        key: 'address',
        width: 200,
        align: 'left',
        fixed: 'left',
        render: (text: string, record: DataType) => {
          const coordinates = record.geom.coordinates;
          return (
            <p>
              {text ? text : ''}{' '}
              <a
                href={`${origin}/?address=${text}&radius=0.5&minBeds=1&maxBeds=99&minBaths=1&maxBaths=99&minSqft=100&maxSqft=10000&minYearBuilt=1900&maxYearBuilt=2022&minCumulativeDaysOnMarket=0&maxCumulativeDaysOnMarket=999&currentStatusMLS=Closed&currentStartMLS=2024-01-06&currentEndMLS=2024-04-05&searchingMode=Sale`}
                target={
                  ['ILE'].includes(props.selectedUserGroup) ? '_self' : '_blank'
                }
              >
                {' '}
                <Tooltip
                  placement="topLeft"
                  title="Explore this property in new CMA Tab"
                  arrowPointAtCenter
                  overlayInnerStyle={{ fontSize: '12px' }}
                >
                  <EnvironmentOutlined
                    style={{ color: 'blue', fontSize: '14px' }}
                  />
                </Tooltip>
              </a>
            </p>
          );
        },
      },
      {
        title: 'Dist.',
        dataIndex: 'distance',
        key: 'distance',
        width: 55,
        align: 'center',
        render: (text: number) => <p>{(text / 1609.34).toFixed(1)} mi</p>,
        defaultSortOrder: 'ascend',
        sorter: (a, b) => a.distance - b.distance,
      },
      {
        title: 'Last Sale Date',
        dataIndex: 'last_sale_date',
        key: 'last_sale_date',
        width: 100,
        align: 'center',
        render: (text: any) => <p>{text}</p>,
      },
      {
        title: 'YrB',
        dataIndex: 'year_built',
        key: 'year_built',
        width: 55,
        align: 'center',
        sorter: (a, b) => a.year_built - b.year_built,
        render: (text: number) => <p>{text}</p>,
      },

      {
        title: 'Type',
        dataIndex: 'propertysubtype',
        key: 'propertysubtype',
        width: 150,
        align: 'left',
        render: (text: string) => <p>{toTitleCase(text)}</p>,
        filters: simplifyPropertySubtypes(dataForRender),
        onFilter: (value: any, record) => {
          return record.propertysubtype.indexOf(value) === 0;
        },
      },
      {
        title: 'Sqft',
        dataIndex: 'sqft',
        key: 'sqft',
        width: 50,
        align: 'center',
        render: (text: number) => (
          <p>{new Intl.NumberFormat('en-US').format(text)}</p>
        ),
        sorter: (a, b) => a.sqft - b.sqft,
      },
      {
        title: 'Bd',
        dataIndex: 'bedroom',
        key: 'bedroom',
        width: 55,
        align: 'center',
        render: (text: number) => <p>{text}</p>,
      },
      {
        title: 'Ba',
        dataIndex: 'bathroom',
        key: 'bathroom',
        // width: 30,
        align: 'center',
        render: (text: number) => <p>{text}</p>,
      },
    ];

    if (
      dataForRender.length > 0 &&
      !NON_DISCLOSURE_STATES.includes(dataForRender[0].state)
    ) {
      return columnsWithPrice;
    } else {
      return columns;
    }
  };

  // *Date Range
  const onChange = (value: number | null) => setDateRange(value);

  const tableHeader = (
    <>
      <Row
        id="lastSalePRTableHeader"
        key="table title row LSPR 1"
        align="bottom"
        justify="space-between"
        wrap={true}
        gutter={[0, 8]}
        style={{ marginBottom: 12 }}
      >
        {/* //! Date Range Input */}
        <Col key="table title" className={styles.cardTitleH2}>
          <strong style={{ fontSize: '16px', fontWeight: 500 }}>
            Last Sales From Public Record in{' '}
            <InputNumber
              min={1}
              max={9999}
              value={dateRange}
              bordered={false}
              size="small"
              onChange={onChange}
              style={{ color: 'blue', fontSize: '16px', width: '68px' }}
              step={30}
            />
            <span style={{ fontWeight: 500 }}>Days</span>
          </strong>
        </Col>
        <Col key="LSPR summary row wrapper">
          <Row key="LSPR summary row" align="middle" justify="end" gutter={24}>
            {/* //! Median Last Sale */}
            <Col key="median last sale">
              <span
                key="median last sale"
                className={styles.cardSubtitle}
                style={{ marginRight: 8 }}
              >
                Median Last Sale:
              </span>
              <span
                id="median last sale "
                key="lspr median last sale"
                className={styles.cardDataValue}
              >
                {props.lastSalePublicRecordMedian
                  ? //lastSalePublicRecordMedian
                    '$' + formatter(props.lastSalePublicRecordMedian)
                  : '-'}
              </span>
            </Col>
            {/* //! Total Unit */}
            <Col key="total unit">
              <span
                key="total unit text"
                className={styles.cardSubtitle}
                style={{ marginRight: 8 }}
              >
                Total:
              </span>
              <span
                id="lsprTable-Total"
                key="total unit number"
                className={styles.cardDataValue}
              >
                <span>
                  {tempData.length > 0 ? tempData.length : dataForRender.length}{' '}
                  of{' '}
                </span>
                <span>{dataForRender.length} </span>
              </span>
            </Col>
          </Row>
        </Col>
      </Row>
      <Row
        id="lastSalePRTableHeader"
        key="table title row LSPR 2"
        align="bottom"
        justify="space-between"
        wrap={true}
        gutter={[0, 8]}
        style={{ marginBottom: 12 }}
      >
        {/* //! Date Range Input */}
        <Col key="day range button" style={{ display: 'flex', gap: '8px' }}>
          <Button
            type={dateRange === 180 ? 'primary' : 'default'}
            onClick={() => setDateRange(180)}
          >
            180 days
          </Button>
          <Button
            type={dateRange === 365 ? 'primary' : 'default'}
            onClick={() => setDateRange(365)}
          >
            365 days
          </Button>
          <Tooltip placement="topLeft" title="+30 Days" arrowPointAtCenter>
            <Button
              onClick={() =>
                setDateRange((prevRange) =>
                  Math.max((prevRange || 365) + 30, 0),
                )
              }
            >
              +
            </Button>
          </Tooltip>
          <Tooltip placement="topLeft" title="-30 Days" arrowPointAtCenter>
            <Button
              onClick={() =>
                setDateRange((prevRange) =>
                  Math.max((prevRange || 365) - 30, 0),
                )
              }
            >
              -
            </Button>
          </Tooltip>
          <div style={{ paddingTop: '4px' }}>
            <Switch
              checkedChildren="Match"
              unCheckedChildren="All"
              checked={matchMode}
              onChange={(check) => setMatchMode(check)}
            />
          </div>
        </Col>
      </Row>
    </>
  );

  return (
    <div>
      <div
        id="prTableContainer"
        className={styles.cardWrapperMLS}
        style={{ marginTop: '16px' }}
        ref={lsprContainer}
      >
        {props.lastSalePublicRecordShowTable ? (
          <Table
            virtual
            className="table-sticky-title-header"
            loading={props.lastSalePublicRecordLoading}
            rowKey={(record) => record.key}
            components={{
              header: {
                wrapper: ({
                  className,
                  children,
                }: {
                  className: any;
                  children: any;
                }) => {
                  return (
                    <thead className={className}>
                      <div style={{ width: tableSize.width - 12 }}>
                        {tableHeader}
                      </div>
                      {children}
                    </thead>
                  );
                },
              },
            }}
            columns={getColumns()}
            dataSource={dataForRender}
            bordered={true}
            size="small"
            pagination={{
              current: currentPage,
              pageSize: pageSize,
              onChange: (page, pageSize) => {
                setCurrentPage(page);
                setPageSize(pageSize);
              },
            }}
            sticky={true}
            scroll={{ x: '50vw' }}
            rowSelection={rowSelection}
            onChange={(
              pagination,
              filters,
              sorter,
              { action, currentDataSource },
            ) => {
              if (!isEqual(currentDataSource, currentTableData)) {
                setCurrentTableData(currentDataSource);
              }

              console.log('testv4 filters', filters);
              setTableFilters(filters);
              setTempData(currentDataSource);

              // If filtering, update selection state for filtered data
              if (action === 'filter') {
                const newKeys = currentDataSource.map((item: any) => item.key);
                const newSelectedKeys = selectedRowKeys.filter((key) =>
                  newKeys.includes(key),
                );
                setSelectedRowKeys(newSelectedKeys);
              }
            }}
            onRow={(record, rowIndex) => {
              return {
                onMouseEnter: (event: any) => {
                  dispatch({
                    type: 'CMA/saveCMAStates',
                    payload: {
                      lastSalePublicRecordHover: record,
                    },
                  });
                },
                onMouseLeave: (event: any) => {
                  dispatch({
                    type: 'CMA/saveCMAStates',
                    payload: {
                      lastSalePublicRecordHover: null,
                    },
                  });
                },
                onClick: () => {
                  if (record.geom?.coordinates) {
                    // If map exists, fly to the location
                    const map = window.map || null;
                    if (map) {
                      map.flyTo({
                        center: record.geom.coordinates,
                        zoom: 16,
                        speed: 2,
                        curve: 1,
                        easing: (t: any) => t,
                      });
                    }
                  }
                },
              };
            }}
            rowClassName={(record, index) => {
              let className = styles.propertyDataTableRow;
              return className;
            }}
          />
        ) : (
          <Card style={{ textAlign: 'center' }}>
            <WarningOutlined
              twoToneColor="#e0e01f"
              style={{ fontSize: '36px' }}
            />
            <p style={{ fontSize: '14px' }}>
              The last sales from public record have over 50 results, which may
              slow down the application.
            </p>
            <p style={{ fontSize: '14px' }}>Click the button to continue.</p>
            <Button
              onClick={() => {
                dispatch({
                  type: 'CMA/saveCMAStates',
                  payload: {
                    lastSalePublicRecordShowTable: true,
                    lastSalePublicRecordShowLayer: true,
                  },
                });
              }}
            >
              Load the comps
            </Button>
          </Card>
        )}
      </div>
    </div>
  );
};

export default connect(({ CMA }: any) => ({
  lastSalePublicRecordData: CMA.lastSalePublicRecordData,
  lastSalePublicRecordSelectedRowKey: CMA.lastSalePublicRecordSelectedRowKey,
  lastSalePublicRecordHover: CMA.lastSalePublicRecordHover,
  lastSalePublicRecordDataForRender: CMA.lastSalePublicRecordDataForRender,
  lastSalePublicRecordMedian: CMA.lastSalePublicRecordMedian,
  lastSalePublicRecordMedianPricePerSqft:
    CMA.lastSalePublicRecordMedianPricePerSqft,
  mapExpandedView: CMA.mapExpandedView,
  subjectPropertyParcelData: CMA.subjectPropertyParcelData,
  lastSalePublicRecordShowTable: CMA.lastSalePublicRecordShowTable,
  lastSalePublicRecordLoading: CMA.lastSalePublicRecordLoading,
  selectedUserGroup: CMA.selectedUserGroup,
}))(LastSalePublicRecordTable);
