import styles from '@/components/ResultTable/resultTable.css';
import { Col, DatePicker, Row, Table, Tooltip } from 'antd';
import { isEmpty, isEqual } from 'lodash';
import moment from 'moment';
import { useEffect, useRef, useState } from 'react';
import { connect, useDispatch, useSelector } from 'umi';
import { formatter } from '../../../utils/money';
import { calculateAvgRent } from '../../../utils/padSplit';
import { capitalize } from '../../../utils/strings';
import EmptyState from '../../EmptyState';
import {
  columnWidthSmall,
  columnWidthSmaller,
  dateFormat,
} from '../ResultTable';

const PadSplit = (props) => {
  const columns = [
    {
      title: 'Property ID',
      dataIndex: 'property_id',
      width: columnWidthSmall,
    },
    {
      title: 'City',
      dataIndex: 'city',
      width: columnWidthSmall,
    },
    {
      title: 'State',
      dataIndex: 'state',
      width: 75,
    },
    {
      title: 'ZIP Code',
      dataIndex: 'zip_code',
      width: 75,
    },
    {
      title: 'Metro',
      dataIndex: 'metro_area',
      width: columnWidthSmall,
    },
    {
      title: 'Type',
      dataIndex: 'property_type',
      width: columnWidthSmall,
      render: (text, record) => capitalize(record.property_type),
    },
    {
      title: 'Beds',
      dataIndex: 'property_bedrooms',
      width: columnWidthSmaller,
    },
    {
      title: 'Baths',
      dataIndex: 'property_bathrooms',
      width: columnWidthSmaller,
    },
    {
      title: 'Sqft',
      dataIndex: 'property_sq_ft',
      render: (text, record) => formatter(record.property_sq_ft),
      width: columnWidthSmaller,
    },

    {
      title: '365 Days Trailing',
      children: [
        {
          title: 'Avg. Rent',
          dataIndex: 'active_rooms_with_private_bath',
          width: 100,
          align: 'right',
          render: (text, record) => {
            return (
              <Tooltip title="Weighted Average Rent">
                {'$' + formatter(calculateAvgRent(record))}
              </Tooltip>
            );
          },
          sorter: (a, b) => {
            const avgRent = (record) => {
              return calculateAvgRent(record);
            };
            return avgRent(a) - avgRent(b);
          },
        },
        {
          title: 'Occupancy Rate',
          dataIndex: 'occupancy_rate',
          width: 100,
          align: 'right',
          render: (text, record) => {
            return <>{(record.occupancy_rate * 100).toFixed(2) + '%'}</>;
          },
          sorter: (a, b) => {
            const avgRent = (record) => {
              return calculateAvgRent(record);
            };
            return avgRent(a) - avgRent(b);
          },
        },
        {
          title: 'Shared Bath',
          dataIndex: 'active_rooms_with_shared_bath',
          width: 200,
          render: (text, record) => {
            return (
              <span>
                <Tooltip title="Shared Bath">{text}bd</Tooltip>
                &nbsp;/&nbsp;
                <Tooltip title="Average Rent">
                  {'$' + formatter(record.rooms_with_shared_bath_price)}
                </Tooltip>
                &nbsp;/&nbsp;
                <Tooltip title="Occupancy Rate">
                  {(
                    record.mature_attainable_occupancy_rate_shared_bath * 100
                  ).toFixed(2) + '%'}
                </Tooltip>
              </span>
            );
          },
        },
        {
          title: 'Private Bath',
          dataIndex: 'active_rooms_with_private_bath',
          width: 200,
          render: (text, record) => {
            return (
              <span>
                <Tooltip title="Private Bath">{text}bd</Tooltip>
                &nbsp;/&nbsp;
                <Tooltip title="Average Rent">
                  {'$' + formatter(record.rooms_with_private_bath_price)}
                </Tooltip>
                &nbsp;/&nbsp;
                <Tooltip title="Occupancy Rate">
                  {(
                    record.mature_attainable_occupancy_rate_private_bath * 100
                  ).toFixed(2) + '%'}
                </Tooltip>
              </span>
            );
          },
        },
      ],
    },
  ];

  const currentPadSplitPropertiesFiltered = useSelector(
    (state) => state.CMA.currentPadSplitPropertiesFiltered,
  );
  const fetchCompHotPadsDone = useSelector(
    (state) => state.CMA.fetchCompHotPadsDone,
  );
  const selectedRowKeysPadSplit = useSelector(
    (state) => state.CMA.selectedRowKeysPadSplit,
  );
  const mapLocateProperty = useSelector((state) => state.CMA.mapLocateProperty);
  const mapExpandedView = useSelector((state) => state.CMA.mapExpandedView);
  const searchingMode = useSelector((state) => state.CMA.searchingMode);
  const currentHighlightCoordinates = useSelector(
    (state) => state.CMA.currentHighlightCoordinates,
  );
  const cmaTabKey = useSelector((state) => state.CMA.cmaTabKey);

  const PadSplitContainer = useRef(null);

  const [scrollToSelectedRow, setScrollToSelectedRow] = useState(false);

  const dispatch = useDispatch();

  useEffect(() => {
    console.log(
      'testv2 currentPadSplitPropertiesFiltered',
      currentPadSplitPropertiesFiltered,
    );
  }, [currentPadSplitPropertiesFiltered]);
  useEffect(() => {
    const click = (e) => {
      if (e.target.id === 'locatePropertyButton' || !PadSplitContainer.current)
        return;

      const row = PadSplitContainer.current.querySelector(
        `.${styles.mapLocatePropertySelected}`,
      );
      if (row) {
        row.classList.remove(styles.mapLocatePropertySelected);
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: { mapLocateProperty: {} },
        });
      }
    };

    document.addEventListener('click', click);
    return () => {
      document.removeEventListener('click', click);
    };
  }, []);

  useEffect(() => {
    if (scrollToSelectedRow && PadSplitContainer.current) {
      const row = PadSplitContainer.current.querySelector(
        `.${styles.mapLocatePropertySelected}`,
      );

      if (row) {
        row.scrollIntoView({ behavior: 'smooth', block: 'center' });
        setScrollToSelectedRow(false);
      }
    }
  }, [scrollToSelectedRow]);

  useEffect(() => {
    if (!isEmpty(mapLocateProperty) && mapLocateProperty.type === props.type) {
      if (cmaTabKey !== '1') {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: { cmaTabKey: '1' },
        });
      }
      setTimeout(() => {
        setScrollToSelectedRow(true);
      }, 100);
    }
  }, [mapLocateProperty]);

  // useEffect(() => {
  //   if (!PadSplitContainer.current) return;
  //   const headerTable = PadSplitContainer.current.querySelector(
  //     '.ant-table-header table',
  //   );

  //   const headerTableTitle =
  //     PadSplitContainer.current.querySelector('#sfrTableHeader');

  //   if (headerTable && !headerTableTitle) {
  //     const el = renderToString(tableHeader);
  //     headerTable.insertAdjacentHTML('beforebegin', el);
  //   }
  // }, [PadSplitContainer.current]);

  // useEffect(() => {
  //   if (!PadSplitContainer.current) return;

  //   const sfrTableTotal =
  //     PadSplitContainer.current.querySelector('#sfrTable-Total');
  //   const sfrTableMedian =
  //     PadSplitContainer.current.querySelector('#sfrTable-Median');
  //   if (sfrTableTotal) {
  //     sfrTableTotal.innerText = currentPadSplitPropertiesFiltered.length || '-';
  //   }
  //   if (sfrTableMedian) {
  //     sfrTableMedian.innerText = rentMedian ? '$' + formatter(rentMedian) : '-';
  //   }
  // }, [currentPadSplitPropertiesFiltered, rentMedian]);

  const onRowPadSplit = (record) => {
    return {
      onMouseEnter: (event) => {
        if (
          !isEqual(currentHighlightCoordinates, record.geometry.coordinates)
        ) {
          dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              currentHighlightCoordinates: record.geometry.coordinates,
              priceHighlightMarker: '$' + formatter(calculateAvgRent(record)),
              // '$' +
              // formatter(record.currentAvgRoomsPrivateBathPrice) +
              // ' / $' +
              // formatter(record.currentAvgRoomsSharedBathPrice),
              typeHighlightMarker: 'PadSplit',
            },
          });
        }

        if (!mapExpandedView) return;
      },
      onMouseLeave: (event) => {
        if (!mapExpandedView) return;
      },
    };
  };

  return (
    <div
      ref={PadSplitContainer}
      key="padsplit card"
      className={styles.cardWrapperSFR}
      style={{ display: searchingMode === 'Lease' ? 'block' : 'none' }}
    >
      <Row
        id="PadSplitTableHeader"
        key="table title row pad split"
        align="bottom"
        justify="space-between"
        style={{ marginBottom: 12 }}
      >
        <div key="table title" className={styles.cardTitleH2}>
          Room Rental
        </div>
        <Row
          key="PadSplit summary row"
          align="middle"
          justify="end"
          gutter={24}
        >
          <Col key="total unit">
            <span
              key="total unit text"
              className={styles.cardSubtitle}
              style={{ marginRight: 8 }}
            >
              Total
            </span>
            <span
              id="PadSplitTable-Total"
              key="total unit number"
              className={styles.cardDataValue}
            >
              {currentPadSplitPropertiesFiltered.length || '-'}
            </span>
          </Col>
          <Col key="PadSplit avg">
            <span
              key="PadSplit avg text"
              className={styles.cardSubtitle}
              style={{ marginRight: 8 }}
            >
              {/* Average Rent (Private/Shared Bath) */}
              Average Rent
            </span>
            <span
              id="PadSplitTable-Median"
              key="PadSplit avg number"
              className={styles.cardDataValue}
            >
              {props.rentAveragePadSplit
                ? '$' + formatter(props.rentAveragePadSplit)
                : '-'}
              {/* {props.rentAveragePadSplitPrivateBath
                ? '$' + formatter(props.rentAveragePadSplitPrivateBath)
                : '-'}
              &nbsp;/&nbsp;
              {props.rentAveragePadSplitSharedBath
                ? '$' + formatter(props.rentAveragePadSplitSharedBath)
                : '-'} */}
            </span>
          </Col>
        </Row>
      </Row>
      <Table
        key="PadSplit table"
        rowKey={(record) => record.property_id}
        columns={columns}
        dataSource={currentPadSplitPropertiesFiltered}
        size="small"
        bordered
        pagination={false}
        loading={!fetchCompHotPadsDone}
        rowSelection={{
          selectedRowKeys: selectedRowKeysPadSplit,
          onChange: (selectedRowKeys, selectedRows) => {
            dispatch({
              type: 'CMA/saveCMAStates',
              payload: {
                selectedRowKeysPadSplit: selectedRowKeys,
              },
            });
          },
        }}
        onRow={onRowPadSplit}
        rowClassName={(record, index) => {
          let className = styles.propertyDataTableRow;
          if (!isEmpty(mapLocateProperty)) {
            if (record.property_id === mapLocateProperty.id) {
              className += ' ' + styles.mapLocatePropertySelected;
            }
          }
          return className;
        }}
        sticky
        scroll={mapExpandedView ? null : { x: 1200 }}
      />
    </div>
  );
};

export default PadSplit;
