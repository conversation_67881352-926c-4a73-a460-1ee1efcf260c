@import '../globalVars.css';
@import '../MapCMA/mapboxStyle.css';

.colWrapper {
  width: 100%;
  height: 100%;
  /* height: calc(
    100vh - (var(--map-padding) * 3) - var(--logo-height) -
      var(--header-margin-bottom)
  ); */
  /* padding: 0 var(--map-padding) var(--map-padding) 0; */
  padding-right: var(--map-padding);
  display: flex;
  flex-direction: column;
  /* align-items: stretch; */
  justify-content: flex-start;
  overflow: auto;
  min-height: 760px;
}

.scrollWrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex: 1;
  overflow: inherit;
}

.singleTabWrapperDoublePanels {
  display: flex;
  flex-direction: row;
  gap: 8px;
  width: 100%;
  height: 100%;
}

.singleTabWrapperSinglePanel {
  height: 100%;
  overflow: auto;
}


.cardWrapper {
  position: relative;
  width: 100%;
  /* max-height: 50vh; */
  background-color: #fff;
  padding: 24px 32px;
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 0 4px rgba(19, 16, 204, 0.1);
  /* overflow-x: auto; */
}

.cardWrapperInner {
  position: relative;
  width: 100%;
  background-color: #fff;
  /* padding: 24px 32px; */
  margin: 32px 0;
  /* overflow-x: auto; */
}

.cardWrapperFilterRow {
  composes: cardWrapperInner;
  /* background-color: rgba(0, 0, 0, 0.02); */
  margin-top: 0;
  display: flex;
  flex-direction: column;
  gap: 14px;
  margin-bottom: 16px;
}

.cardWrapperSFR {
  composes: cardWrapperInner;
}

.cardWrapperMLS {
  composes: cardWrapperInner;
  margin-bottom: 0;
}

.cardWrapperSticky {
  composes: cardWrapper;
  position: sticky;
  position: -webkit-sticky;
  top: 0;
  z-index: 99;
  background: #f0f8ff;
  border: none;
  box-shadow: none;
  padding: 0px 24px;
}

.cardWrapperCMAFilters {
  composes: cardWrapper;
  background-color: #fff3f3;
}

.cardTitleRow {
  padding: 8px;
}

.cardTitleH1 {
  font-size: 16px;
  font-weight: 500;
  color: #222;
  /* margin-left: 4px; */
  /* padding: 4px; */
}

.shareButton,
.csvButton,
.comparisonButton {
  font-size: 14px;
  font-weight: 500;
  color: var(--antd-active-blue);
  border: none;
  border-radius: 4px;
  margin-left: 8px;
  cursor: pointer;
}

.csvButtonSmall {
  composes: csvButton;
  font-size: 12px;
  line-height: 1.25;
  text-align: center;
}

.showSavedCompsButton {
  /* composes: csvButton; */
  background-color: rgb(23, 156, 204);
  color: #fff;
  font-size: 12px;
  line-height: 1.25;
  text-align: center;
  padding: 0 4px;
  border-radius: 4px;
}

.cardSubtitle {
  font-size: 14px;
  font-weight: 400;
}

.cardDataValue {
  font-size: 14px;
  font-weight: 500;
}

.cardSubtitleLighter {
  font-size: 11px;
  font-weight: 500;
  color: #666;
}

.cardDataValueLighter {
  font-size: 14px;
  font-weight: 500;
}

.summaryCardAvgSum {
  composes: cardDataValue;
  /* width: 50px;
	display: inline-block; */
}

.summaryCardAvgSqftSum {
  font-size: 12px;
}

.summaryCardAvgWrapper {
  display: flex;
  flex-direction: column;
  /* justify-content: space-between; */
  justify-content: center;
  align-items: center;
}

.summaryCardDetail {
  composes: cardDataValue;
  margin-right: 4px;
}

.summaryCardDetailYearBuilt {
  composes: cardDataValue;
  margin-left: 4px;
}

.summaryCardDetailLight400 {
  composes: cardDataValue;
  font-weight: 400;
  margin-right: 4px;
}

.cardTitleH2 {
  font-size: 16px;
  font-weight: 500;
}

.dividerCardHeader {
  width: 100%;
  height: 1px;
  background-color: rgba(0, 0, 0, 0.1);
  margin-top: 12px;
}

.controlRow {
  margin: 16px 0;
}

.controlSelect {
  color: var(--antd-active-blue);
  /* font-weight: 500; */
  /* font-size: 16px; */
  text-align: center;
  padding: 5.5px 0 4.5px;
  border: none;
  /* border-bottom: 1px solid var(--antd-active-blue); */
  cursor: pointer;
  margin: 0 8px;
}

.controlSelect:disabled {
  color: #999;
  cursor: not-allowed;
}

.controlDatePicker {
  /* color: var(--antd-active-blue); */
  border: none;
  border-bottom: 1px solid var(--antd-active-blue);
}

.filterSetButton {
  color: var(--antd-active-blue);
  background-color: rgba(0, 0, 0, 0);
  border: none;
  /* border: 1px solid var(--antd-active-blue);
	border-radius: 999px; */
}

.text_nationalOperators_AH4R {
  color: var(--color-AH4R);
}

.text_nationalOperators_Invitatio {
  color: var(--color-Invitatio);
}

.text_nationalOperators_HRG {
  color: var(--color-HRG);
}

.text_nationalOperators_PR {
  color: var(--color-PR);
}

.text_nationalOperators_CPM {
  color: var(--color-CPM);
}

.text_nationalOperators_TR {
  color: var(--color-TR);
}

.text_nationalOperators_MYND {
  color: var(--color-MYND);
}

.text_nationalOperators_KP {
  color: var(--color-KP);
}

.text_nationalOperators_RW {
  color: var(--color-RW);
}

.text_nationalOperators_VH {
  color: var(--color-VH);
}

.text_nationalOperators_Amhrest {
  color: var(--color-Amhrest);
}

.text_nationalOperators_ARG {
  color: var(--color-ARG);
}
.text_nationalOperators_Brandywine {
  color: var(--color-Brandywine);
}
.text_nationalOperators_BridgeHome {
  color: var(--color-BridgeHome);
}
.text_nationalOperators_Camillo {
  color: var(--color-Camillo);
}
.text_nationalOperators_Copperbay {
  color: var(--color-Copperbay);
}
.text_nationalOperators_Divvy {
  color: var(--color-Divvy);
}
.text_nationalOperators_FirstKey {
  color: var(--color-FirstKey);
}
.text_nationalOperators_Hudson {
  color: var(--color-Hudson);
}
.text_nationalOperators_Imagine {
  color: var(--color-Imagine);
}
.text_nationalOperators_Imagine {
  color: var(--color-Imagine);
}
.text_nationalOperators_KairosLiving {
  color: var(--color-KairosLiving);
}
.text_nationalOperators_KrchRealty {
  color: var(--color-KrchRealty);
}
.text_nationalOperators_LiveReszi {
  color: var(--color-LiveReszi);
}
.text_nationalOperators_OpenHouse {
  color: var(--color-OpenHouse);
}
.text_nationalOperators_Pathway {
  color: var(--color-Pathway);
}
.text_nationalOperators_Peak {
  color: var(--color-Peak);
}
.text_nationalOperators_PPMG {
  color: var(--color-PPMG);
}
.text_nationalOperators_Propify {
  color: var(--color-Propify);
}
.text_nationalOperators_RENU {
  color: var(--color-RENU);
}
.text_nationalOperators_ResiHome {
  color: var(--color-ResiHome);
}
.text_nationalOperators_SPA {
  color: var(--color-SPA);
}
.text_nationalOperators_Streetlane {
  color: var(--color-Streetlane);
}
.text_nationalOperators_SYLV {
  color: var(--color-SYLV);
}

.customToolTip {
  display: none;
  background-color: rgba(0, 0, 0, 0.75);
  padding: 10px;
  color: white;
  position: absolute;
  z-index: 10;
}

.customToolTip::after {
  content: '';
  border: 15px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.75);
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
}

.emptyTable {
  width: 100%;
  height: 48px;
  background: rgba(0, 0, 0, 0.1);
}

.propertyImageBtn {
  all: unset;
  transform: translateY(-3px);
  cursor: pointer;
}

.imageIconForAddress {
  display: inline-block;
  fill: var(--antd-active-blue);
  margin-left: 4px;
  vertical-align: text-top;
}

.propertyDataTableRow {
  font-size: 12px;
  font-weight: 450;
  color: #333;
  cursor: pointer;
}

.propertyDataTableRowSelected {
  composes: propertyDataTableRow;
  background-color: rgba(144, 208, 234, 0.8) !important;
  border-color: rgba(144, 208, 234, 0.8) !important;
}

.mapLocatePropertySelected > td {
  background-color: rgba(144, 208, 234, 0.8) !important;
  border-color: rgba(144, 208, 234, 0.8) !important;
}

.iconSummaryRow {
  fill: var(--antd-active-blue);
}

.shareIconSummaryRow {
  composes: iconSummaryRow;
  vertical-align: -2px;
}

.printIconSummaryRow {
  composes: iconSummaryRow;
  vertical-align: -3px;
}

.buttonWrapperSummaryRow {
  background-color: rgba(0, 0, 0, 0);
  border: none;
  cursor: pointer;
}

.buttonWrapperSummaryRowText {
  composes: buttonWrapperSummaryRow;
  padding: 0;
}

.buttonWrapperSummaryRowText > button:disabled {
  color: rgba(0, 0, 0, 0.25);
  background-color: rgba(0, 0, 0, 0);
  border: none;
  padding: 0;
}

.tableSummaryCellTextAlignCenter {
  text-align: center;
  padding: 0 !important;
}

.modeButtonWrapper {
  font-weight: 500;
  /* line-height: 1.5; */
  /* color: var(--color-BT-blue); */
  border: 2px solid var(--color-BT-blue);
}

.modeButton {
  padding: 6px 12px;
}

.tabContainer {
  height: 100% !important;
  overflow: hidden !important;
  display: flex;
  align-items: stretch;
  justify-content: space-between;
  gap: 8px;
  padding: 0;
  padding-left: 24px;
}

.marketConditionGrayRow {
  background-color: rgba(245, 245, 245, 1);
}

.modeSelectorWrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.sfrLink {
  color: inherit;
}
.sfrLink:hover {
  text-decoration: underline;
  color: var(--antd-active-blue);
}

.headerBarButton {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.5;
  color: var(--color-BT-blue);
}

/* School Score Card */
.schoolScoreDiffIncre {
  color: #10b981;
  letter-spacing: 0px;
  line-height: 1.6;
  font-size: 14px;
}
.schoolScoreDiffDecre {
  color: #ef4444;
  letter-spacing: 0px;
  line-height: 1.6;
  font-size: 14px;
}
.schoolScore {
  color: #999da3;
  /* color: yellow; */
  letter-spacing: 0px;
  line-height: 1.6;
  font-size: 14px;
}

.SSDPanel {
  display: grid;
  grid-template-columns: 1fr 1fr;
  column-gap: 20px;
}

.cardTitleH3 {
  font-size: 14px;
  font-weight: 500;
}

.cardDivBorder {
  border: 0.5px solid black;
}

.cursorPointer:hover {
  cursor: pointer;
}

.tabLabelText {
  font-size: 12px;
  font-weight: 500;
  line-height: 1.25;
  text-align: center;
}