import { Spin } from 'antd';
import { GoTriangleDown, GoTriangleUp } from 'react-icons/go';

const formatValue = (value: any, record: any) => {
  if (value === null || value === undefined) return '-';
  if (value === 'loading') return <Spin />;
  if (['active', 'closed', 'median_dom'].includes(record.type)) {
    return Math.round(value).toLocaleString();
  } else if (
    record.type === 'active_median_psf' ||
    record.type === 'closed_median_psf'
  ) {
    return `$${(Math.round(value * 100) / 100).toLocaleString()}`;
  } else {
    return (Math.round(value * 100) / 100).toLocaleString();
  }
};

const round = (value: any) => {
  return Math.round(value * 100) / 100;
};

// prettier-ignore
const columnCellRender = (
  value: any,
  record: any,
  index: number,
  compareData: any[] = [],
  valueKey: string = '',
) => {
  const formattedValue = formatValue(value, record);

  if (compareData.length && valueKey.length) {
    const compareValue = compareData[index][valueKey as keyof typeof record];
    const isUp = (isNaN(value) || isNaN(compareValue)) || value === compareValue ? null : value > compareValue;
    const percentChange =
      Math.round((round(value) / round(compareValue) - 1) * 100 * 100) / 100;
    const isActiveOrClosed = !(['median_dom', 'months_of_inventory'].includes(record.type));

    return (
      <div className={`min-h-[50px] max-h-[50px] flex flex-col justify-center items-center${record.type.includes('psf') ? ' border-b-2 border-[#ddd]': '' }`}>
        <div className="flex flex-row">
          <span>{formattedValue}</span>
          {isUp === true && <GoTriangleUp style={{ color: isActiveOrClosed? 'limegreen' : 'red' }} />}
          {isUp === false && <GoTriangleDown style={{ color: isActiveOrClosed ? 'red' : 'limegreen' }} />}
        </div>
        {['active_median_psf', 'closed_median_psf'].includes(record.type) && !isNaN(percentChange) && (
          <span>{percentChange}%</span>
        )}
      </div>
    );
  }

  return (
    <div className={`min-h-[50px] max-h-[50px] flex flex-col justify-center items-center${record.type.includes('psf') ? ' border-b-2 border-[#ddd]': '' }`}>
      <span>{formattedValue}</span>
    </div>
  );
};

const typeValueFormatter = (value: string) => {
  switch (value) {
    case 'active':
      return 'Active';
    case 'closed':
      return 'Closed';
    case 'active_median_psf':
    case 'closed_median_psf':
      return 'Median PSF';
    case 'median_dom':
      return 'Median DOM';
    case 'months_of_inventory':
      return 'Months of Inventory';
    default:
      return value;
  }
};

export const getMarketConditionColumns = ({
  showTypeCol,
  compareData,
}: {
  showTypeCol: boolean;
  compareData?: any[];
}) => {
  const columns = [
    {
      title: '',
      dataIndex: 'type',
      width: 100,
      render: (value: string, record: any) => {
        const formattedValue = typeValueFormatter(value);
        // prettier-ignore
        return (
          <div className={`min-h-[50px] max-h-[50px] flex flex-col justify-center items-center${record.type.includes('psf') ? ' border-b-2 border-[#ddd]': '' }`}>
            <span>{formattedValue}</span>
          </div>
        )
      },
    },
    {
      title: 'AOI',
      dataIndex: 'aoi',
      render: (value: any, record: any, index: number) => {
        return columnCellRender(value, record, index, compareData, 'aoi');
      },
    },
    {
      title: 'ZIP Code',
      dataIndex: 'zipcode',
      render: (value: any, record: any, index: number) => {
        return columnCellRender(value, record, index, compareData, 'zipcode');
      },
    },
    {
      title: 'School District',
      dataIndex: 'schoolDistrict',
      render: (value: any, record: any, index: number) => {
        return columnCellRender(
          value,
          record,
          index,
          compareData,
          'schoolDistrict',
        );
      },
    },
    {
      title: 'County',
      dataIndex: 'county',
      render: (value: any, record: any, index: number) => {
        return columnCellRender(value, record, index, compareData, 'county');
      },
    },
    {
      title: 'Metro',
      dataIndex: 'metro',
      render: (value: any, record: any, index: number) => {
        return columnCellRender(value, record, index, compareData, 'metro');
      },
    },
  ];

  if (!showTypeCol) {
    columns.shift();
  }
  return columns;
};
