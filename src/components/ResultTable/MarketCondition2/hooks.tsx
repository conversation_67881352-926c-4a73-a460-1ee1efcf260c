import {
  getMLSListingSummaryWithinCountyData,
  getMLSListingSummaryWithinDistrictData,
  getMLSListingSummaryWithinMetroData,
  getMLSListingSummaryWithinSphereData,
  getMLSListingSummaryWithinZIPCodeData,
} from '@/services/data';
import React from 'react';
import { useQuery } from 'react-query';
import z from 'zod';

interface MarketConditionMonthYearProps {
  numberOfMonths: number;
  type: 'current' | 'prior';
}
const useMarketConditionMonthYear = (props: MarketConditionMonthYearProps) => {
  const date =
    props.type === 'current'
      ? new Date()
      : new Date(
          new Date().setMonth(new Date().getMonth() - props.numberOfMonths),
        );
  const month = date.getMonth();
  const year = date.getFullYear();
  return { month, year };
};

const MarketConditionAPIResponseSchema = z.object({
  active: z.number().nullable(),
  closed_average_cdom: z.number().nullable(),
  closed_median_cdom: z.number().nullable(),
  closed_average_close_price_and_size_ratio: z.number().nullable(),
  closed_median_close_price_and_size_ratio: z.number().nullable(),
  closed: z.number().nullable(),
  closed_dom_median: z.number().nullable(),
  active_average_current_price_and_size_ratio: z.number().nullable(),
  active_median_current_price_and_size_ratio: z.number().nullable(),
  ratio: z.number().nullable(),
  closed_median_close_price: z.number().nullable(),
  active_median_current_price: z.number().nullable(),
});

type MarketConditionAPIResponse = z.infer<
  typeof MarketConditionAPIResponseSchema
>;

const convertToMarketCondition = (data: MarketConditionAPIResponse) => {
  return {
    active: data.active,
    active_median_psf: data.active_median_current_price_and_size_ratio,
    closed: data.closed,
    closed_median_psf: data.closed_median_close_price_and_size_ratio,
    median_dom: data.closed_dom_median,
    months_of_inventory: data.ratio,
  };
};

interface MarketConditionSchoolAPIProps {
  propertyType: 'Residential' | 'Residential Lease';
  latitude: number;
  longitude: number;
  radius: number;
  numberOfMonths: number;
  type: 'current' | 'prior';
  enabled?: boolean;
}

// prettier-ignore
export const useMarketConditionAOI = ({enabled = true, ...props}: MarketConditionSchoolAPIProps) => {
  const { month, year } = useMarketConditionMonthYear({
    numberOfMonths: props.numberOfMonths,
    type: props.type,
  });

  const { data, isLoading } = useQuery(
    `market-condition-aoi-${props.propertyType}-${props.latitude}-${props.longitude}-${props.radius}-${props.numberOfMonths}-${month}-${year}`,
    async () => {
      try {
        const resp = await getMLSListingSummaryWithinSphereData({
          propertyType: props.propertyType,
          lat: props.latitude,
          lng: props.longitude,
          distance: props.radius,
          numberOfMonths: props.numberOfMonths,
          month,
          year,
        });

        const parsed = MarketConditionAPIResponseSchema.parse(resp);
        return convertToMarketCondition(parsed);
      } catch (error) {
        console.log('useMarketConditionAOI error:', error);
        return undefined
      }
    },
    {
      retry(failureCount, error: unknown) {
        if ((error as { status?: number }).status === 404) return false;
        return failureCount < 3;
      },
      enabled: enabled && props.latitude && props.longitude && props.radius ? true : false,
    },
  );

  return { data, isLoading };
};

// prettier-ignore
export const useMarketConditionZIPCode = ({enabled = true, ...props}:  Omit<MarketConditionSchoolAPIProps,'radius'>) => {
  const { month, year } = useMarketConditionMonthYear({
    numberOfMonths: props.numberOfMonths,
    type: props.type,
  });

  const { data, isLoading } = useQuery(
    `market-condition-zipcode-${props.propertyType}-${props.latitude}-${props.longitude}-${props.numberOfMonths}-${month}-${year}`,
    async () => {
      try {
        const resp = await getMLSListingSummaryWithinZIPCodeData({
          propertyType: props.propertyType,
          lat: props.latitude,
          lng: props.longitude,
          numberOfMonths: props.numberOfMonths,
          month,
          year,
        });

        const parsed = MarketConditionAPIResponseSchema.parse(resp);
        return convertToMarketCondition(parsed);
      } catch (error) {
        console.log('useMarketConditionZIPCode error:', error);
        return undefined
      }
    },
    {
      retry(failureCount, error: unknown) {
        if ((error as { status?: number }).status === 404) return false;
        return failureCount < 3;
      },
      enabled: enabled && props.latitude && props.longitude && props.numberOfMonths ? true : false,
    },
  );

  return { data, isLoading };
};

// prettier-ignore
export const useMarketConditionSchoolDistrict = ({enabled = true, ...props}:  Omit<MarketConditionSchoolAPIProps,'radius'>) => {
  const { month, year } = useMarketConditionMonthYear({
    numberOfMonths: props.numberOfMonths,
    type: props.type,
  });

  const { data, isLoading } = useQuery(
    `market-condition-school-district-${props.propertyType}-${props.latitude}-${props.longitude}-${props.numberOfMonths}-${month}-${year}`,
    async () => {
      try {
        const resp = await getMLSListingSummaryWithinDistrictData({
          propertyType: props.propertyType,
          lat: props.latitude,
          lng: props.longitude,
          numberOfMonths: props.numberOfMonths,
          month,
          year,
        });

        const parsed = MarketConditionAPIResponseSchema.parse(resp);
        return convertToMarketCondition(parsed);
      } catch (error) {
        console.log('useMarketConditionSchoolDistrict error:', error);
        return undefined
      }
    },
    {
      retry(failureCount, error: unknown) {
        if ((error as { status?: number }).status === 404) return false;
        return failureCount < 3;
      },
      enabled: enabled && props.latitude && props.longitude && props.numberOfMonths ? true : false,
    },
  );

  return { data, isLoading };
};

// prettier-ignore
export const useMarketConditionCounty = ({enabled = true, ...props}: Omit<MarketConditionSchoolAPIProps,'radius'>) => {
  const { month, year } = useMarketConditionMonthYear({
    numberOfMonths: props.numberOfMonths,
    type: props.type,
  });

  const { data, isLoading } = useQuery(
    `market-condition-county-${props.propertyType}-${props.latitude}-${props.longitude}-${props.numberOfMonths}-${month}-${year}`,
    async () => {
      try {
        const resp = await getMLSListingSummaryWithinCountyData({
          propertyType: props.propertyType,
          lat: props.latitude,
          lng: props.longitude,
          numberOfMonths: props.numberOfMonths,
          month,
          year,
        });

        const parsed = MarketConditionAPIResponseSchema.parse(resp);
        return convertToMarketCondition(parsed);
      } catch (error) {
        console.log('useMarketConditionCounty error:', error);
        return undefined
      }
    },
    {
      retry(failureCount, error: unknown) {
        if ((error as { status?: number }).status === 404) return false;
        return failureCount < 3;
      },
      enabled: enabled && props.latitude && props.longitude && props.numberOfMonths ? true : false,
    },
  );

  return { data, isLoading };
};

// prettier-ignore
export const useMarketConditionMetro = ({enabled = true, ...props}: Omit<MarketConditionSchoolAPIProps,'radius'>) => {
  const { month, year } = useMarketConditionMonthYear({
    numberOfMonths: props.numberOfMonths,
    type: props.type,
  });

  const { data, isLoading } = useQuery(
    `market-condition-metro-${props.propertyType}-${props.latitude}-${props.longitude}-${props.numberOfMonths}-${month}-${year}`,
    async () => {
      try {
        const resp = await getMLSListingSummaryWithinMetroData({
          propertyType: props.propertyType,
          lat: props.latitude,
          lng: props.longitude,
          numberOfMonths: props.numberOfMonths,
          month,
          year,
        });

        const parsed = MarketConditionAPIResponseSchema.parse(resp);
        return convertToMarketCondition(parsed);
      } catch (error) {
        console.log('useMarketConditionMetro error:', error);
        return undefined
      }
    },
    {
      retry(failureCount, error: unknown) {
        if ((error as { status?: number }).status === 404) return false;
        return failureCount < 3;
      },
      enabled: enabled && props.latitude && props.longitude && props.numberOfMonths ? true : false,
    },
  );

  return { data, isLoading };
};

export interface MarketConditionData {
  type: string;
  aoi: number | null | undefined;
  zipcode: number | null | undefined;
  schoolDistrict: number | null | undefined;
  county: number | null | undefined;
  metro: number | null | undefined;
}

interface CurrentMarketConditionProps {
  searchingMode: string;
  latitude: number;
  longitude: number;
  radius: number;
  numberOfMonths: number;
  timePeriod: 'current' | 'prior';
  enabled?: boolean;
}
export const useMarketConditionData = ({
  enabled = true,
  ...props
}: CurrentMarketConditionProps) => {
  const { data: aoiCurrentData, isLoading: aoiCurrentLoading } =
    useMarketConditionAOI({
      propertyType:
        props.searchingMode === 'Sale' ? 'Residential' : 'Residential Lease',
      latitude: props.latitude,
      longitude: props.longitude,
      radius: props.radius,
      numberOfMonths: props.numberOfMonths,
      type: props.timePeriod,
      enabled: enabled,
    });
  const { data: zipCodeCurrentData, isLoading: zipCodeCurrentLoading } =
    useMarketConditionZIPCode({
      propertyType:
        props.searchingMode === 'Sale' ? 'Residential' : 'Residential Lease',
      latitude: props.latitude,
      longitude: props.longitude,
      numberOfMonths: props.numberOfMonths,
      type: props.timePeriod,
      enabled: enabled,
    });
  const {
    data: schoolDistrictCurrentData,
    isLoading: schoolDistrictCurrentLoading,
  } = useMarketConditionSchoolDistrict({
    propertyType:
      props.searchingMode === 'Sale' ? 'Residential' : 'Residential Lease',
    latitude: props.latitude,
    longitude: props.longitude,
    numberOfMonths: props.numberOfMonths,
    type: props.timePeriod,
    enabled: enabled,
  });
  const { data: countyCurrentData, isLoading: countyCurrentLoading } =
    useMarketConditionCounty({
      propertyType:
        props.searchingMode === 'Sale' ? 'Residential' : 'Residential Lease',
      latitude: props.latitude,
      longitude: props.longitude,
      numberOfMonths: props.numberOfMonths,
      type: props.timePeriod,
      enabled: enabled,
    });
  const { data: metroCurrentData, isLoading: metroCurrentLoading } =
    useMarketConditionMetro({
      propertyType:
        props.searchingMode === 'Sale' ? 'Residential' : 'Residential Lease',
      latitude: props.latitude,
      longitude: props.longitude,
      numberOfMonths: props.numberOfMonths,
      type: props.timePeriod,
      enabled: enabled,
    });

  // prettier-ignore
  const data = React.useMemo(() => {
    if (!enabled) return undefined;
    const markers = ['active', 'active_median_psf', 'closed', 'closed_median_psf', 'median_dom', 'months_of_inventory'];
    const areaDataSet = [
      { key: 'aoi', data: aoiCurrentData }, 
      { key: 'zipcode', data: zipCodeCurrentData }, 
      { key: 'schoolDistrict', data:schoolDistrictCurrentData },
      { key: 'county', data: countyCurrentData }, 
      { key: 'metro', data: metroCurrentData }
    ];
    
    const result = [];

    const isAreaDataLoading = (areaKey: string) => {
      switch (areaKey) {
        case 'aoi':
          return aoiCurrentLoading;
        case 'zipcode':
          return zipCodeCurrentLoading;
        case 'schoolDistrict':
          return schoolDistrictCurrentLoading;
        case 'county':
          return countyCurrentLoading;
        case 'metro':
          return metroCurrentLoading;
        default:
          return false;
      }
    }
    
    for (const marker of markers) {
      const d = {} as MarketConditionData;
        d['type'] = marker;
      for (const areaData of areaDataSet) {
        // @ts-ignore
        d[areaData.key] = isAreaDataLoading(areaData.key) ? 'loading' : areaData.data ? areaData.data[marker as keyof typeof areaData.data] : undefined;
      }
      result.push(d);
    }

    return result;
  }, [
    aoiCurrentData,
    aoiCurrentLoading,
    zipCodeCurrentData,
    zipCodeCurrentLoading,
    schoolDistrictCurrentData,
    schoolDistrictCurrentLoading,
    countyCurrentData,
    countyCurrentLoading,
    metroCurrentData,
    metroCurrentLoading,
  ]);

  // if (!enabled || !props.latitude || !props.longitude || !props.radius)
  //   return undefined;
  return data;
};
