import { <PERSON>ton, Card, Carousel, InputNumber, Table, Tooltip } from 'antd';
import { CarouselRef } from 'antd/es/carousel';
import React from 'react';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import { IoInformationCircle } from 'react-icons/io5';
import { useResizeObserver } from 'usehooks-ts';
import { MarketConditionData, useMarketConditionData } from './hooks';
import './styles.css';
import { getMarketConditionColumns } from './utils';

interface ContextType {
  months: number;
  setMonths: React.Dispatch<React.SetStateAction<number>>;
  showPrior: boolean;
  setShowPrior: React.Dispatch<React.SetStateAction<boolean>>;
  activated: boolean;
}
const Context = React.createContext<ContextType | undefined>(undefined);

interface MarketConditionProps {
  searchingMode: string;
  latitude: number;
  longitude: number;
  radius: number;
  showPrior?: boolean;
  responsive?: boolean;
  showContainerHeader?: boolean;
}
export const MarketCondition = ({
  responsive = true,
  showContainerHeader = true,
  ...props
}: MarketConditionProps) => {
  const ref = React.useRef<HTMLDivElement>(null);
  const { width = 0 } = useResizeObserver({
    ref,
    box: 'border-box',
  });

  const carouselRef = React.useRef<CarouselRef>(null);
  const [carouselIndex, setCarouselIndex] = React.useState(0);

  const [months, setMonths] = React.useState(3);
  const [showPrior, setShowPrior] = React.useState(props.showPrior || false);
  const currentMarketCondition = useMarketConditionData({
    searchingMode: props.searchingMode,
    latitude: props.latitude,
    longitude: props.longitude,
    radius: props.radius,
    numberOfMonths: months,
    timePeriod: 'current',
  });
  const priorMarketCondition = useMarketConditionData({
    searchingMode: props.searchingMode,
    latitude: props.latitude,
    longitude: props.longitude,
    radius: props.radius,
    numberOfMonths: months,
    timePeriod: 'prior',
    enabled: showPrior,
  });

  const activated =
    props.latitude && props.longitude && props.radius ? true : false;

  const context = React.useMemo(
    () => ({ months, setMonths, showPrior, setShowPrior, activated }),
    [months, setMonths, showPrior, setShowPrior, activated],
  );

  return (
    <Context.Provider value={context}>
      <MarketConditionContainer showContainerHeader={showContainerHeader}>
        <div ref={ref}>
          {activated && (
            <>
              {(!responsive || width >= 900 || !showPrior) && (
                <div className="flex flex-row gap-4 w-full">
                  {currentMarketCondition && (
                    <MarketConditionCard
                      title={<MarketConditionCurrentHeader />}
                    >
                      <MarketConditionTable
                        data={currentMarketCondition}
                        compareData={
                          showPrior ? priorMarketCondition : undefined
                        }
                      />
                    </MarketConditionCard>
                  )}
                  {priorMarketCondition && showPrior && (
                    <MarketConditionCard title={<MarketConditionPriorHeader />}>
                      <MarketConditionTable data={priorMarketCondition} />
                    </MarketConditionCard>
                  )}
                </div>
              )}
              {responsive && width < 900 && showPrior && (
                <div className="max-w-[900px] flex flex-col gap-4">
                  <Carousel
                    ref={carouselRef}
                    initialSlide={carouselIndex}
                    infinite
                    afterChange={(currentSlide) =>
                      setCarouselIndex(currentSlide)
                    }
                  >
                    {currentMarketCondition && (
                      <MarketConditionCard
                        title={<MarketConditionCurrentHeader />}
                      >
                        <MarketConditionTable
                          data={currentMarketCondition}
                          compareData={
                            showPrior ? priorMarketCondition : undefined
                          }
                        />
                      </MarketConditionCard>
                    )}
                    {priorMarketCondition && showPrior && (
                      <MarketConditionCard
                        title={<MarketConditionPriorHeader />}
                      >
                        <MarketConditionTable data={priorMarketCondition} />
                      </MarketConditionCard>
                    )}
                  </Carousel>
                  {currentMarketCondition && priorMarketCondition && (
                    <div className="flex flex-row justify-center items-center gap-2">
                      <Button
                        shape="circle"
                        onClick={() => carouselRef.current?.prev()}
                      >
                        <FaChevronLeft />
                      </Button>
                      <div className="w-[125px] text-center select-none">
                        {carouselIndex === 0 ? (
                          <span>Previous months</span>
                        ) : (
                          <span>Prior months</span>
                        )}
                      </div>
                      <Button
                        shape="circle"
                        onClick={() => carouselRef.current?.next()}
                      >
                        <FaChevronRight />
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </MarketConditionContainer>
    </Context.Provider>
  );
};

const useMarketCondition = () => {
  const context = React.useContext(Context);
  if (!context) {
    throw new Error('useMarketCondition must be used within MarketCondition');
  }
  return context;
};

const MarketConditionContainer = (props: {
  showContainerHeader: boolean;
  children: React.ReactNode;
}) => {
  const { showPrior, setShowPrior, activated } = useMarketCondition();
  return (
    <div className="w-full bg-white py-6 px-8 mb-4 rounded-lg shadow-md flex flex-col gap-4">
      {props.showContainerHeader && (
        <>
          <div className="flex flex-row justify-between">
            <h3 className="font-medium text-base">Market Conditions</h3>
            {activated && (
              <Button onClick={() => setShowPrior(!showPrior)} size="small">
                {showPrior ? 'Remove Comparison' : 'Add Comparison'}
              </Button>
            )}
          </div>
          {activated && <hr />}
        </>
      )}
      {props.children}
    </div>
  );
};

const MarketConditionCurrentHeader = () => {
  const { months, setMonths } = useMarketCondition();

  return (
    <div className="flex flex-row gap-1 justify-center items-center">
      <span className="text-sm select-none">
        Previous completed {months == 1 ? 'month' : 'months'}:{' '}
      </span>
      <InputNumber
        value={months}
        onChange={(value) => setMonths(value || 0)}
        min={1}
        precision={0} // no decimal
        step={1}
        addonAfter={
          <span className="select-none">Month{months > 1 ? 's' : ''}</span>
        }
        style={{ width: 120, textAlign: 'right' }}
      />
    </div>
  );
};

const MarketConditionPriorHeader = () => {
  const { months } = useMarketCondition();
  // beginning of the month for startDate and end of month for endDate, prior to current month number.
  const endDate = new Date(
    new Date().setMonth(new Date().getMonth() - months, 0),
  );
  const startDate = new Date(
    new Date(
      new Date().setMonth(new Date(endDate).getMonth() - months + 1),
    ).setDate(1),
  );

  const renderDate = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  return (
    <div className="flex flex-row gap-1 justify-center items-center">
      <div className="relative">
        <span className="text-sm select-none">
          {`Previous prior completed months ${months}`}
        </span>
        <Tooltip
          title={`${renderDate(startDate)} to ${renderDate(endDate)}`}
          className="absolute top-0 -right-3"
        >
          <IoInformationCircle />
        </Tooltip>
      </div>
    </div>
  );
};

const MarketConditionCard = (props: {
  title?: React.ReactNode;
  children: React.ReactNode;
}) => {
  return (
    <Card
      className="shadow-md w-full"
      title={props.title}
      styles={{
        header: { padding: 0, fontSize: '12px' },
        body: { padding: '10px' },
      }}
    >
      {props.children}
    </Card>
  );
};

const MarketConditionTable = (props: {
  data: MarketConditionData[];
  compareData?: MarketConditionData[];
  showTypeCol?: boolean;
}) => {
  const { data, compareData, showTypeCol = true } = props;
  return (
    <Table
      className="market-condition-table"
      rowKey={(record) => record.type}
      columns={getMarketConditionColumns({ showTypeCol, compareData })}
      dataSource={data || []}
      pagination={false}
      rowClassName={(record, index) => {
        if (record.type === 'closed' || record.type === 'closed_median_psf') {
          return 'bg-[#f5f5f5]';
        }
        return '';
      }}
    />
  );
};
