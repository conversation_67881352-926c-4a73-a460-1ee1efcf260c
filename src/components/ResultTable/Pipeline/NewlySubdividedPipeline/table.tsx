import {
  getNewlySubdividedPipeLine,
  postNewlySubdivdedLotsCSV,
} from '@/services/data';
import { Button, Table, TableColumnsType } from 'antd';
import { isEmpty } from 'lodash';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import styles from '../../resultTable.css';

export interface ProjectData {
  cluster_id: number;
  locationinfo: string;
  stage: string;
  distance: number;
  latitude: number;
  acreage: number;
  builder: string;
  county: string;
  projecttype: string;
  unitscount: string;
  state: string;
  longitude: number;
}

const NewlySubdividedPipeLineTable = () => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState<boolean>(false);
  const map = useSelector((state: any) => state.CMA.map);
  const NewlySubdividedPipeLineData = useSelector(
    (state: any) => state.CMA.NewlySubdividedPipeLineData,
  );
  const currentPropertyAddress = useSelector(
    (state: any) => state.CMA.currentPropertyAddress,
  );
  const currentRadiusMile = useSelector(
    (state: any) => state.CMA.currentRadiusMile,
  );
  const NewlySubdividedPipeLineSelectedRowKey = useSelector(
    (state: any) => state.CMA.NewlySubdividedPipeLineSelectedRowKey,
  );
  const columns: TableColumnsType<ProjectData> = [
    {
      title: 'Address',
      dataIndex: 'locationinfo',
      width: 150,
      key: 'locationinfo',
      render: (text: string, record: ProjectData) => {
        return (
          <p className="text-xs">
            {record.locationinfo}, {record.county}, {record.state}
          </p>
        );
      },
      fixed: 'left',
    },

    {
      title: 'Distance',
      dataIndex: 'distance',
      width: 75,
      key: 'distance',
      render: (text: string, record: ProjectData) => {
        return <p className="text-xs">{record.distance.toFixed(2) + ' mi'}</p>;
      },
    },
    {
      title: '# of Units',
      dataIndex: 'unitscount',
      width: 75,
      key: 'unitscount',
      render: (text: string, record: ProjectData) => {
        return <p className="text-xs">{record.unitscount}</p>;
      },
    },
    {
      title: 'Acreage',
      dataIndex: 'acreage',
      width: 50,
      key: 'acreage',
      render: (text: string, record: ProjectData) => {
        return <p className="text-xs">{record.acreage}</p>;
      },
    },
    {
      title: 'Builder',
      dataIndex: 'builder',
      width: 150,
      key: 'builder',
      render: (text: string, record: ProjectData) => {
        return <p className="text-xs">{record.builder}</p>;
      },
    },
    // {
    //   title: 'For Sale',
    //   dataIndex: 'producttype',
    //   width: 100,
    //   key: 'producttype',
    //   render: (text: string, record: ProjectData) => {
    //     return <p className="text-xs">{record.projecttype}</p>;
    //   },
    // },
    {
      title: 'Stage',
      dataIndex: 'stage',
      width: 100,
      key: 'stage',
      render: (text: string, record: ProjectData) => {
        return <p className="text-xs">{record.stage}</p>;
      },
    },
  ];
  useEffect(() => {
    const fetchData = async () => {
      if (currentPropertyAddress.latitude && currentPropertyAddress.longitude) {
        setLoading(true);
        const params = {
          lat: currentPropertyAddress.latitude,
          lng: currentPropertyAddress.longitude,
          distance: currentRadiusMile,
        };

        const data: any = await getNewlySubdividedPipeLine(params);
        if (data) {
          const allKeys = data.map((record: ProjectData) => record.cluster_id);
          dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              NewlySubdividedPipeLineData: data,
              NewlySubdividedPipeLineSelectedRowKey: allKeys,
            },
          });
        }
        setLoading(false);
      } else {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            NewlySubdividedPipeLineData: [],
            NewlySubdividedPipeLineHover: null,
            NewlySubdividedPipeLineSelectedRowKey: [],
          },
        });
      }
    };

    fetchData();
  }, [currentPropertyAddress, currentRadiusMile, dispatch]);

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        NewlySubdividedPipeLineSelectedRowKey: newSelectedRowKeys,
      },
    });
  };
  const rowSelection = {
    selectedRowKeys: NewlySubdividedPipeLineSelectedRowKey,
    onChange: onSelectChange,
  };
  useEffect(() => {
    console.log(
      'NewlySubdividedPipeLineSelectedRowKey',
      NewlySubdividedPipeLineSelectedRowKey,
    );
  }, [NewlySubdividedPipeLineData, NewlySubdividedPipeLineSelectedRowKey]);

  const [CSVButtonLoading, setCSVButtonLoading] = useState<boolean>(false);
  const onClickCSVButton = () => {
    setCSVButtonLoading(true);
    const params: any = {
      body: {
        lat: currentPropertyAddress.latitude,
        lng: currentPropertyAddress.longitude,
        distance: currentRadiusMile,
        selected: NewlySubdividedPipeLineSelectedRowKey,
      },
    };

    const fetchData = async () => {
      try {
        const response = await postNewlySubdivdedLotsCSV(params);
        if (response) {
          // Create blob directly from the response string
          const blob = new Blob([response], {
            type: 'text/csv;charset=utf-8;',
          });

          // Create download URL
          const url = window.URL.createObjectURL(blob);

          // Create and trigger download
          const a = document.createElement('a');
          a.href = url;

          // Create filename with current date
          const date = new Date().toISOString();
          const filename = `newly-subdivided-lots-${date}.csv`;
          a.download = filename;

          // Trigger download
          document.body.appendChild(a);
          a.click();

          // Cleanup
          window.URL.revokeObjectURL(url);
          a.remove();
        }
        setCSVButtonLoading(false);
      } catch (error) {
        console.error('Error downloading CSV:', error);
        // Handle error appropriately
      }
    };
    fetchData();
  };
  return (
    <div className={styles.cardWrapper}>
      <div className="flex justify-between">
        <p className="text-lg font-semibold my-2">Newly Subdivided Lots</p>
        <Button
          type="default"
          loading={CSVButtonLoading}
          onClick={onClickCSVButton}
          disabled={NewlySubdividedPipeLineData.length === 0}
          className="mb-2"
        >
          Export CSV
        </Button>
      </div>
      {NewlySubdividedPipeLineData && (
        <>
          <Table
            dataSource={NewlySubdividedPipeLineData}
            loading={loading}
            columns={columns}
            rowKey={(record) => record.cluster_id}
            bordered={true}
            size="small"
            pagination={false}
            scroll={{ x: '70vw', y: '300px' }}
            rowSelection={rowSelection}
            onRow={(record, rowIndex) => {
              return {
                onClick: (event: any) => {
                  map.flyTo({
                    center: [record.longitude, record.latitude],
                    zoom: 16,
                    essential: true,
                    duration: 1500,
                    padding: { top: 50, bottom: 50, left: 50, right: 50 },
                  });
                },
                onMouseEnter: (event: any) => {
                  dispatch({
                    type: 'CMA/saveCMAStates',
                    payload: {
                      NewlySubdividedPipeLineHover: record,
                    },
                  });
                },
                onMouseLeave: (event: any) => {
                  dispatch({
                    type: 'CMA/saveCMAStates',
                    payload: {
                      NewlySubdividedPipeLineHover: null,
                    },
                  });
                },
              };
            }}
          />
        </>
      )}
    </div>
  );
};

export default NewlySubdividedPipeLineTable;
