import { getBTRPipeLine, postBTRPipelineCSV } from '@/services/data';
import { Button, Table, TableColumnsType } from 'antd';
import { isEmpty } from 'lodash';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import { FilterOption } from '../../LandCrexi/utils/type';
import styles from '../../resultTable.css';
import NewlySubdividedPipeLineTable from '../NewlySubdividedPipeline/table';
import BTRPipeLineModal from './Modal';
import { ProjectData, transformProjectsList } from './util';

const BTRPipelinePanel = () => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState<boolean>(false);
  const [filteredData, setFilteredData] = useState<ProjectData[]>([]);
  const map = useSelector((state: any) => state.CMA.map);
  const BTRPipeLineData = useSelector(
    (state: any) => state.CMA.BTRPipeLineData,
  );
  const currentPropertyAddress = useSelector(
    (state: any) => state.CMA.currentPropertyAddress,
  );
  const currentRadiusMile = useSelector(
    (state: any) => state.CMA.currentRadiusMile,
  );
  const BTRPipeLineSelectedRowKey = useSelector(
    (state: any) => state.CMA.BTRPipeLineSelectedRowKey,
  );
  const onClickDetails = (record: ProjectData) => {
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        BTRPipeLineModalOpen: true,
        BTRPipeLineModalDetails: record,
      },
    });
  };
  const [stageOptions, setStageOptions] = useState<FilterOption[]>([]);
  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    let filtered = [...BTRPipeLineData];
    if (filters.stage && filters.stage.length > 0) {
      filtered = filtered.filter((item) => filters.stage.includes(item.stage));
    }

    setFilteredData(filtered);
  };
  const columns: TableColumnsType<ProjectData> = [
    {
      title: 'Address',
      dataIndex: 'locationinfo',
      width: 150,
      key: 'locationinfo',
      render: (text: string, record: ProjectData) => {
        return (
          <p className="text-xs">
            {record.locationinfo}, {record.city}, {record.state},{' '}
            {record.postalcode}
          </p>
        );
      },
      fixed: 'left',
    },
    {
      title: 'Stage',
      dataIndex: 'stage',
      width: 100,
      key: 'stage',
      render: (text: string, record: ProjectData) => {
        return (
          <p className="text-xs">
            {record.stage} - {record.projectschedule}
          </p>
        );
      },
      filters: stageOptions,
      filterMode: 'menu',
      filterSearch: true,
    },
    {
      title: 'Value (M)',
      dataIndex: 'projectvalue',
      width: 75,
      key: 'projectvalue',
      render: (text: string, record: ProjectData) => {
        console.log('BTR PL record', record);
        return <p className="text-xs">{record.projectvalue}</p>;
      },
    },
    {
      title: '# of Units',
      dataIndex: 'unitscount',
      width: 75,
      key: 'unitscount',
      render: (text: string, record: ProjectData) => {
        return <p className="text-xs">{record.unitscount}</p>;
      },
    },
    {
      title: 'Acreage',
      dataIndex: 'acreage',
      width: 50,
      key: 'acreage',
      render: (text: string, record: ProjectData) => {
        return <p className="text-xs">{record.acreage}</p>;
      },
    },
    {
      title: 'Development Type',
      dataIndex: 'producttype',
      width: 100,
      key: 'producttype',
      render: (text: string, record: ProjectData) => {
        return <p className="text-xs">{record.producttype}</p>;
      },
    },
    {
      title: 'Developer',
      dataIndex: 'developers',
      width: 150,
      key: 'developers',
      render: (text: string, record: ProjectData) => {
        return <p className="text-xs">{record.developers[0]?.companyname}</p>;
      },
    },
    {
      title: 'Created',
      dataIndex: 'submitteddate',
      width: 100,
      key: 'submitteddate',
      render: (text: string, record: ProjectData) => {
        return (
          <p className="text-xs">
            {record.submitteddate} -{' '}
            <span className="text-red-500">
              {record.updateddate} (Last Updated)
            </span>
          </p>
        );
      },
    },
    {
      title: 'Action',
      dataIndex: 'action',
      width: 100,
      key: 'action',
      render: (text: string, record: ProjectData) => {
        return (
          <Button
            type="primary"
            size="small"
            className="text-xs"
            onClick={() => {
              onClickDetails(record);
            }}
          >
            Details
          </Button>
        );
      },
    },
  ];
  useEffect(() => {
    const fetchData = async () => {
      if (currentPropertyAddress.latitude && currentPropertyAddress.longitude) {
        setLoading(true);
        const params = {
          lat: currentPropertyAddress.latitude,
          lng: currentPropertyAddress.longitude,
          distance: currentRadiusMile,
        };

        const data: any = await getBTRPipeLine(params);
        if (data) {
          const transformedData = transformProjectsList(data);
          const allKeys = transformedData.map(
            (record: any) => record.projectid,
          );

          // Create unique stage options
          const uniqueStages = Array.from(
            new Set(transformedData.map((record) => record.stage)),
          ).map((stage) => ({
            text: stage,
            value: stage,
          }));
          setStageOptions(uniqueStages);

          setFilteredData(transformedData); // Initialize filtered data
          dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              BTRPipeLineData: transformedData,
              BTRPipeLineSelectedRowKey: allKeys,
            },
          });
        }
        setLoading(false);
      } else {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            BTRPipeLineData: [],
            BTRPipeLineHover: null,
            BTRPipeLineModalOpen: false,
            BTRPipeLineModalDetails: null,
            BTRPipeLineSelectedRowKey: [],
          },
        });
        setFilteredData([]); // Clear filtered data
      }
    };

    fetchData();
  }, [currentPropertyAddress, currentRadiusMile, dispatch]);
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        BTRPipeLineSelectedRowKey: newSelectedRowKeys,
      },
    });
  };
  const rowSelection = {
    selectedRowKeys: BTRPipeLineSelectedRowKey,
    onChange: onSelectChange,
  };
  useEffect(() => {
    console.log('BTRPipeLineSelectedRowKey', BTRPipeLineSelectedRowKey);
  }, [BTRPipeLineData, BTRPipeLineSelectedRowKey]);

  const [CSVButtonLoading, setCSVButtonLoading] = useState<boolean>(false);
  const onClickCSVButton = () => {
    setCSVButtonLoading(true);
    const params: any = {
      body: {
        lat: currentPropertyAddress.latitude,
        lng: currentPropertyAddress.longitude,
        distance: currentRadiusMile,
        selected: BTRPipeLineSelectedRowKey,
      },
    };

    const fetchData = async () => {
      try {
        const response = await postBTRPipelineCSV(params);
        if (response) {
          // Create blob directly from the response string
          const blob = new Blob([response], {
            type: 'text/csv;charset=utf-8;',
          });

          // Create download URL
          const url = window.URL.createObjectURL(blob);

          // Create and trigger download
          const a = document.createElement('a');
          a.href = url;

          // Create filename with current date
          const date = new Date().toISOString();
          const filename = `btr-pipeline_${date}.csv`;
          a.download = filename;

          // Trigger download
          document.body.appendChild(a);
          a.click();

          // Cleanup
          window.URL.revokeObjectURL(url);
          a.remove();
        }
        setCSVButtonLoading(false);
      } catch (error) {
        console.error('Error downloading CSV:', error);
        // Handle error appropriately
      }
    };
    fetchData();
  };

  return (
    <div className={styles.cardWrapper}>
      <div className="flex justify-between">
        <p className="text-lg font-semibold mb-4">BTR Pipeline</p>
        <Button
          type="default"
          loading={CSVButtonLoading}
          onClick={onClickCSVButton}
          disabled={BTRPipeLineData.length === 0}
          className="mb-2"
        >
          Export CSV
        </Button>
      </div>

      {BTRPipeLineData && (
        <>
          <Table
            dataSource={filteredData}
            loading={loading}
            columns={columns}
            rowKey={(record) => record.projectid}
            bordered={true}
            size="small"
            pagination={false}
            sticky={true}
            scroll={{ x: '70vw', y: '400px' }}
            rowSelection={rowSelection}
            onChange={handleTableChange}
            onRow={(record, rowIndex) => {
              return {
                onClick: (event: any) => {
                  map.flyTo({
                    center: [record.longitude, record.latitude],
                    zoom: 16,
                    essential: true,
                    duration: 1500,
                    padding: { top: 50, bottom: 50, left: 50, right: 50 },
                  });
                },
                onMouseEnter: (event: any) => {
                  dispatch({
                    type: 'CMA/saveCMAStates',
                    payload: {
                      BTRPipeLineHover: record,
                    },
                  });
                },
                onMouseLeave: (event: any) => {
                  dispatch({
                    type: 'CMA/saveCMAStates',
                    payload: {
                      BTRPipeLineHover: null,
                    },
                  });
                },
              };
            }}
          />
        </>
      )}
      <BTRPipeLineModal />
    </div>
  );
};

export default BTRPipelinePanel;
