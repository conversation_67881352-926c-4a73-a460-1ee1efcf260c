import {
  Button,
  Col,
  Descriptions,
  Divider,
  List,
  Modal,
  Row,
  Tag,
  Timeline,
  Typography,
} from 'antd';
import React, { useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import { formatNote, ProjectData } from './util';

const { Title, Text } = Typography;

const BTRPipeLineModal = () => {
  const dispatch = useDispatch();
  const [showAllNotes, setShowAllNotes] = useState(false);
  const BTRPipeLineModalDetails: ProjectData = useSelector(
    (state: any) => state.CMA.BTRPipeLineModalDetails,
  );
  const BTRPipeLineModalOpen = useSelector(
    (state: any) => state.CMA.BTRPipeLineModalOpen,
  );

  const onClose = () => {
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        BTRPipeLineModalOpen: false,
        BTRPipeLineModalDetails: null,
      },
    });
  };

  const renderContactInfo = (entity: any) => (
    <>
      {entity.contactname && <Text strong>{entity.contactname}</Text>}
      {entity.contacttitle && <div>{entity.contacttitle}</div>}
      {entity.contactphone && <div>Phone: {entity.contactphone}</div>}
      {entity.contactemail && <div>Email: {entity.contactemail}</div>}
      <div>{entity.locationaddress1}</div>
      {entity.locationaddress2 && <div>{entity.locationaddress2}</div>}
      <div>{`${entity.locationcity}, ${entity.locationstate} ${entity.locationpostalcode}`}</div>
      {entity.locationphone && <div>Office: {entity.locationphone}</div>}
    </>
  );

  const renderEntitySection = (title: string, entities: any[]) => {
    if (!entities || entities.length === 0) return null;
    return (
      <>
        <Title level={5}>{title}</Title>
        <List
          dataSource={entities}
          renderItem={(entity) => (
            <List.Item>
              <List.Item.Meta
                title={entity.companyname}
                description={renderContactInfo(entity)}
              />
            </List.Item>
          )}
        />
        <div className="pr-8">
          <Divider />
        </div>
      </>
    );
  };

  const renderEntitySections = () => {
    const sections = [
      ['Developers', BTRPipeLineModalDetails.developers],
      ['Architects', BTRPipeLineModalDetails.architects],
      ['Engineers', BTRPipeLineModalDetails.engineers],
      ['Consultants', BTRPipeLineModalDetails.consultants],
      ['Contractors', BTRPipeLineModalDetails.contractors],
      ['Property Managers', BTRPipeLineModalDetails.propertymanagers],
      ['Builders', BTRPipeLineModalDetails.builders],
      ['Tenants', BTRPipeLineModalDetails.tenants],
    ].filter(([_, entities]) => entities && entities.length > 0);

    const midPoint = Math.ceil(sections.length / 2);

    return (
      <Row gutter={24}>
        <Col span={12}>
          {sections
            .slice(0, midPoint)
            .map(([title, entities]) =>
              renderEntitySection(title as string, entities as any[]),
            )}
        </Col>
        <Col span={12}>
          {sections
            .slice(midPoint)
            .map(([title, entities]) =>
              renderEntitySection(title as string, entities as any[]),
            )}
        </Col>
      </Row>
    );
  };

  const renderProjectNotes = () => {
    if (!BTRPipeLineModalDetails.projectnotes) return null;

    const formattedNotes = formatNote(BTRPipeLineModalDetails.projectnotes);
    const displayNotes = showAllNotes
      ? formattedNotes
      : formattedNotes.slice(0, 5);
    const hasMoreNotes = formattedNotes.length > 5;

    return (
      <>
        <Divider />
        <div className="flex justify-between items-center">
          <Title level={5}>Project Notes</Title>
          {hasMoreNotes && (
            <Button
              type="link"
              onClick={() => setShowAllNotes(!showAllNotes)}
              className="mb-4"
            >
              {showAllNotes
                ? 'Show Less'
                : `Show All (${formattedNotes.length})`}
            </Button>
          )}
        </div>
        <Timeline
          items={displayNotes.map((note) => ({
            children: (
              <>
                <Text strong>{note.date.format('MM/DD/YYYY')}</Text>
                <br />
                <Text>{note.content}</Text>
              </>
            ),
            color: note.content.toLowerCase().includes('hold') ? 'red' : 'blue',
          }))}
        />
      </>
    );
  };

  if (BTRPipeLineModalDetails) {
    return (
      <Modal
        title={`${BTRPipeLineModalDetails.title}`}
        open={BTRPipeLineModalOpen}
        onOk={onClose}
        onCancel={onClose}
        width="1200px"
        bodyStyle={{
          height: '1000px',
          overflowY: 'auto',
          padding: '24px',
        }}
      >
        <Descriptions column={2}>
          <Descriptions.Item label="Location">
            {BTRPipeLineModalDetails.locationinfo},{' '}
            {BTRPipeLineModalDetails.city}, {BTRPipeLineModalDetails.state}{' '}
            {BTRPipeLineModalDetails.postalcode}
          </Descriptions.Item>
          <Descriptions.Item label="Status">
            <Tag
              color={
                BTRPipeLineModalDetails.stage === 'Planning' ? 'blue' : 'green'
              }
            >
              {BTRPipeLineModalDetails.stage}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="Project Value (M)">
            {BTRPipeLineModalDetails.projectvalue}
          </Descriptions.Item>
          <Descriptions.Item label="Units">
            {BTRPipeLineModalDetails.unitscount}
          </Descriptions.Item>
          <Descriptions.Item label="Acreage">
            {BTRPipeLineModalDetails.acreage}
          </Descriptions.Item>
          <Descriptions.Item label="Project Type">
            {BTRPipeLineModalDetails.projecttype}
          </Descriptions.Item>
          <Descriptions.Item label="Development Type">
            {BTRPipeLineModalDetails.producttype}
          </Descriptions.Item>
          <Descriptions.Item label="Construction Type">
            {BTRPipeLineModalDetails.constructiontype}
          </Descriptions.Item>
        </Descriptions>

        <Divider />

        <Title level={5}>Project Details</Title>
        <Text>{BTRPipeLineModalDetails.projectdetails}</Text>

        {renderProjectNotes()}

        <Divider />

        {renderEntitySections()}
        <Text type="primary">
          Last Updated: {BTRPipeLineModalDetails.updateddate}
        </Text>
      </Modal>
    );
  }
  return null;
};

export default BTRPipeLineModal;
