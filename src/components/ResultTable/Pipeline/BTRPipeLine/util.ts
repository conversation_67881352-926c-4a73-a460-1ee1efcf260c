import dayjs from 'dayjs';

interface GeoPoint {
  type: string;
  coordinates: [number, number];
}

export interface ProjectData {
  projectid: number;
  title: string;
  stage: string;
  sector: string;
  projectvalue: string | null;
  unitscount: string;
  projecttype: string;
  producttype: string;
  constructiontype: string;
  constructiondate: string | null;
  projectschedule: string;
  acreage: string;
  price: string | null;
  amenity: string | null;
  projectdetails: string;
  projectnotes: string | null;
  submitteddate: string;
  updateddate: string;

  // Location data
  locationinfo: string;
  postalcode: number | null;
  city: string;
  state: string;
  county: string;
  countyfips: number;
  latitude: number;
  longitude: number;
  geom: GeoPoint;

  // Arrays of entities
  architects: Entity[];
  contractors: Entity[];
  engineers: Entity[];
  developers: Entity[];
  owners: Entity[];
  propertymanagers: Entity[];
  consultants: Entity[];
  builders: Entity[];
  tenants: Entity[];
}

interface Entity {
  companyname: string | null;
  companyurl: string | null;
  contactname: string | null;
  contacttitle: string | null;
  contactphone: string | null;
  contactemail: string | null;
  contactfax: string | null;
  locationaddress1: string | null;
  locationaddress2: string | null;
  locationcity: string | null;
  locationstate: string | null;
  locationpostalcode: string | null;
  locationcountry: string | null;
  locationphone: string | null;
  locationfax: string | null;
  locationtollfree: string | null;
}

export function transformProjectsList(projects: any[]): ProjectData[] {
  return projects.map((data) => {
    const createEntityArray = (prefix: string): Entity[] => {
      const result: Entity[] = [];
      for (let i = 1; i <= 3; i++) {
        const prefixNum = `${prefix}0${i}`;
        const entity = {
          companyname: data[`${prefixNum}companyname`] || null,
          companyurl: data[`${prefixNum}companyurl`] || null,
          contactname: data[`${prefixNum}contactname`] || null,
          contacttitle: data[`${prefixNum}contacttitle`] || null,
          contactphone: data[`${prefixNum}contactphone`] || null,
          contactemail: data[`${prefixNum}contactemail`] || null,
          contactfax: data[`${prefixNum}contactfax`] || null,
          locationaddress1: data[`${prefixNum}locationaddress1`] || null,
          locationaddress2: data[`${prefixNum}locationaddress2`] || null,
          locationcity: data[`${prefixNum}locationcity`] || null,
          locationstate: data[`${prefixNum}locationstate`] || null,
          locationpostalcode: data[`${prefixNum}locationpostalcode`] || null,
          locationcountry: data[`${prefixNum}locationcountry`] || null,
          locationphone: data[`${prefixNum}locationphone`] || null,
          locationfax: data[`${prefixNum}locationfax`] || null,
          locationtollfree: data[`${prefixNum}locationtollfree`] || null,
        };

        if (Object.values(entity).some((value) => value !== null)) {
          result.push(entity);
        }
      }
      return result;
    };

    return {
      projectid: data.projectid,
      title: data.title,
      stage: data.stage,
      sector: data.sector,
      projectvalue: data.projectvalue,
      unitscount: data.unitscount,
      projecttype: data.projecttype,
      producttype: data.producttype,
      constructiontype: data.constructiontype,
      constructiondate: data.constructiondate,
      projectschedule: data.projectschedule,
      acreage: data.acreage,
      price: data.price,
      amenity: data.amenity,
      projectdetails: data.projectdetails,
      projectnotes: data.projectnotes,
      submitteddate: data.submitteddate,
      updateddate: data.updateddate,

      locationinfo: data.locationinfo,
      postalcode: data.postalcode,
      city: data.city,
      state: data.state,
      county: data.county,
      countyfips: data.countyfips,
      latitude: data.latitude,
      longitude: data.longitude,
      geom: data.geom,

      architects: createEntityArray('architect'),
      contractors: createEntityArray('contractor'),
      engineers: createEntityArray('engineer'),
      developers: createEntityArray('developer'),
      owners: createEntityArray('owner'),
      propertymanagers: createEntityArray('propertymanager'),
      consultants: createEntityArray('consultant'),
      builders: createEntityArray('builder'),
      tenants: createEntityArray('tenant'),
    };
  });
}

export const formatNote = (noteText: string) => {
  // Split by multiple newlines to handle different formats
  const notes = noteText.split(/\s*\r\n\s*/);

  const noteEntries = notes.flatMap((note) => {
    // Match date and content, handling various date formats
    const dateMatch = note.match(/(\d{1,2}\/\d{1,2}\/\d{4}):\s*(.*)/);
    if (dateMatch) {
      return [
        {
          date: dayjs(dateMatch[1], 'M/D/YYYY'),
          content: dateMatch[2].trim(),
        },
      ];
    }
    return [];
  });

  return noteEntries.sort((a, b) => b.date.unix() - a.date.unix());
};
