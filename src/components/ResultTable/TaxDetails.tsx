import { getParcelAVMData, getTaxProperDetailsData } from '@/services/data';
import { useQuery } from '@tanstack/react-query';
import { Button, Modal, Segmented, Spin } from 'antd';
import { isEmpty } from 'lodash';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'umi';

const TaxDetailsModal = ({ visible, onClose }) => {
  const dispatch = useDispatch();
  const [activeTab, setActiveTab] = useState('general');

  // Get data from Redux store
  const currentPropertyAddress = useSelector(
    (state) => state.CMA.currentPropertyAddress,
  );
  const taxDetails = useSelector((state) => state.CMA.taxDetails);

  // We'll use sessionStorage for caching instead of React Query

  // Fetch AVM data with React Query
  // State to store AVM data
  const [avm, setAvm] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  // Fetch AVM data when modal becomes visible
  useEffect(() => {
    if (!visible) return;

    const getAvm = async () => {
      if (!currentPropertyAddress || isEmpty(currentPropertyAddress)) return;

      setIsLoading(true);
      try {
        // Check if we have cached data
        const cacheKey = `avm-${currentPropertyAddress.latitude}-${currentPropertyAddress.longitude}`;
        const cachedData = sessionStorage.getItem(cacheKey);

        if (cachedData) {
          console.log('Using cached AVM data');
          setAvm(JSON.parse(cachedData));
        } else {
          console.log('Fetching AVM data for:', currentPropertyAddress);
          const result = await getParcelAVMData({
            lat: currentPropertyAddress.latitude,
            lng:
              currentPropertyAddress.longitude ||
              currentPropertyAddress.longgitude,
          });
          console.log('AVM data received:', result);
          setAvm(result);

          // Cache the result
          sessionStorage.setItem(cacheKey, JSON.stringify(result));
        }
      } catch (error) {
        console.error('Error fetching AVM data:', error);
      }
    };

    getAvm();
  }, [visible, currentPropertyAddress]);

  // Fetch tax details when AVM data is available
  useEffect(() => {
    if (!visible || isEmpty(avm)) return;

    const getTaxDetails = async () => {
      if (!currentPropertyAddress || isEmpty(currentPropertyAddress)) return;

      try {
        // Check if we have cached data
        const cacheKey = `taxDetails-${
          currentPropertyAddress.address || currentPropertyAddress.fullAddress
        }`;
        const cachedData = sessionStorage.getItem(cacheKey);

        if (cachedData) {
          console.log('Using cached tax details');
          const parsedData = JSON.parse(cachedData);
          dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              taxDetails: parsedData,
            },
          });
        } else {
          console.log('Fetching tax details with AVM:', avm);
          const result = await getTaxProperDetailsData({
            body: {
              address: `${
                currentPropertyAddress.address
                  ? currentPropertyAddress.address
                  : currentPropertyAddress.fullAddress
              }`,
              latitude: currentPropertyAddress.latitude,
              longitude:
                currentPropertyAddress.longitude ||
                currentPropertyAddress.longgitude,
              purchase_price: avm?.salesavm || 500000,
            },
          });

          console.log('Tax details received:', result);
          dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              taxDetails: result,
            },
          });

          // Cache the result
          sessionStorage.setItem(cacheKey, JSON.stringify(result));
        }
      } catch (error) {
        console.error('Error fetching tax details:', error);
      } finally {
        setIsLoading(false);
      }
    };

    getTaxDetails();
  }, [visible, avm, dispatch, currentPropertyAddress]);

  // Log when taxDetails changes in Redux
  useEffect(() => {
    console.log('Current taxDetails from Redux:', taxDetails);
  }, [taxDetails]);

  // Use refetch functions directly from the query hooks for manual refetching

  const parseSnakeCaseToString = (input) => {
    return input
      .replace(/_/g, ' ')
      .split(' ')
      .map((s) => s.charAt(0).toUpperCase() + s.substring(1))
      .join(' ');
  };

  const renderValue = (key, value) => (
    <div key={key} style={{ display: 'grid', gridTemplateColumns: '1fr 1fr' }}>
      <p style={{ fontWeight: 'bold' }}>{parseSnakeCaseToString(key)}</p>
      <p>{value !== null && value !== undefined ? value : '-'}</p>
    </div>
  );

  const segmentedOptions = React.useMemo(() => {
    const options = [
      { label: 'General', value: 'general' },
      { label: 'Projections', value: 'projections' },
      { label: 'Rates', value: 'rate_breakdown' },
    ];

    if (taxDetails?.source_forecasts) {
      options.push({
        label: 'Nearby',
        value: 'source_forecasts',
      });
    }
    return options;
  }, [taxDetails]);

  // isLoading is now managed in the state

  return (
    <Modal
      title="Tax Details"
      open={visible}
      onCancel={() => {
        setActiveTab('general');
        onClose();
      }}
      footer={null}
      width={800}
      bodyStyle={{ padding: 0, maxHeight: '80vh', overflow: 'auto' }}
    >
      <div
        className="sticky top-0 z-10 px-[24px] pb-[24px] bg-white"
        style={{ borderBottom: '1px solid #ddd' }}
      >
        <Segmented
          value={activeTab}
          onChange={(value) => setActiveTab(value)}
          block
          options={segmentedOptions}
        />
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <Spin size="large" />
        </div>
      ) : (
        <div
          className="flex flex-col gap-8 pt-4 px-[24px] pb-[24px]"
          style={{ color: '#333' }}
        >
          {activeTab === 'general' && (
            <>
              <div>
                {renderValue('base_rate', taxDetails?.base_rate?.toFixed(6))}
                {renderValue('tax_cap', taxDetails?.tax_cap?.toFixed(2))}
                {/* assessment_cap is a json object, try phoennix area */}
                {/* {renderValue("assessment_cap", taxDetails.assessment_cap?.toFixed(2))} */}
              </div>
              <div>
                {taxDetails?.forecast_metadata &&
                  Object.entries(taxDetails.forecast_metadata)
                    .filter(
                      ([k, v]) =>
                        ![
                          'messages',
                          'tax_districts',
                          'request_id',
                          'purchase_price',
                        ].includes(k),
                    )
                    .map(([key, value]) => {
                      if (
                        ['rates_last_check', 'rates_next_check'].includes(key)
                      ) {
                        return (
                          <div key={key}>
                            {renderValue(key, value && value.split('T')[0])}
                          </div>
                        );
                      }
                      if (
                        [
                          'total_millage_rate',
                          'total_flat_charge',
                          'median_millage_rate',
                          'median_flat_charge',
                          'mean_millage_rate',
                          'median_flat_charge',
                        ].includes(key)
                      ) {
                        return (
                          <div key={key}>
                            {renderValue(key, value && value?.toFixed(6))}
                          </div>
                        );
                      }
                      if (
                        ['purchase_price', 'stabilized_tax_amount'].includes(
                          key,
                        )
                      ) {
                        // filtered out, maybe temp
                        return (
                          <div key={key}>
                            {renderValue(
                              key,
                              value &&
                                `$ ${Math.round(value).toLocaleString()}`,
                            )}
                          </div>
                        );
                      }
                      return <div key={key}>{renderValue(key, value)}</div>;
                    })}
              </div>
            </>
          )}
          {activeTab === 'projections' && (
            <>
              {taxDetails?.tax_projections &&
                taxDetails.tax_projections.map((projection, idx) => (
                  <div key={idx}>
                    <h3 className="font-bold text-base">
                      Year {projection.year}
                    </h3>

                    {projection?.market_value &&
                      renderValue(
                        'market_value',
                        projection?.market_value !== null
                          ? `$ ${Math.round(
                              projection?.market_value,
                            ).toLocaleString()}`
                          : '-',
                      )}
                    {projection?.tax_amount &&
                      renderValue(
                        'tax_amount',
                        `$ ${Math.round(
                          projection?.tax_amount,
                        ).toLocaleString()}`,
                      )}
                    {projection?.tax_amount &&
                      renderValue('tax_cap', projection?.tax_cap)}

                    {projection?.assessment_method &&
                      renderValue(
                        'assessment_method',
                        `${projection?.assessment_method.split('_').join(' ')}`,
                      )}

                    {renderValue(
                      'is_assessment_capped',
                      `${projection.is_assessment_capped ? 'Yes' : 'No'}`,
                    )}

                    {projection?.assessment &&
                      renderValue(
                        'assessment',
                        `$ ${Math.round(
                          projection?.assessment,
                        ).toLocaleString()}`,
                      )}
                    {projection?.gross_assessment &&
                      renderValue(
                        'gross_assessment',
                        `$ ${Math.round(
                          projection?.gross_assessment,
                        ).toLocaleString()}`,
                      )}
                    {projection?.net_assessment &&
                      renderValue(
                        'net_assessment',
                        `$ ${Math.round(
                          projection?.net_assessment,
                        ).toLocaleString()}`,
                      )}
                  </div>
                ))}
            </>
          )}
          {activeTab === 'rate_breakdown' && (
            <>
              {taxDetails?.rate_breakdown &&
                taxDetails.rate_breakdown.map((rate, idx) => (
                  <div key={idx}>
                    <h3 className="font-bold text-base">
                      {rate.taxing_authority}
                    </h3>
                    {renderValue('rate', rate.rate?.toFixed(6))}
                    {renderValue(
                      'amount',
                      rate.amount &&
                        `$ ${Math.round(rate.amount).toLocaleString()}`,
                    )}
                    {renderValue(
                      'flat_charge',
                      rate.flat_charge !== null
                        ? `$ ${Math.round(rate.flat_charge).toLocaleString()}`
                        : '-',
                    )}
                  </div>
                ))}
            </>
          )}
          {taxDetails?.source_forecasts && activeTab === 'source_forecasts' && (
            <>
              {taxDetails.source_forecasts.map((source, idx) => (
                <div key={idx}>
                  {source?.location && (
                    <>
                      <h3 className="font-bold text-base">
                        {source?.location?.resolved_address || '-'}
                      </h3>
                      <div
                        className="mb-2"
                        style={{
                          display: 'grid',
                          gridTemplateColumns: '1fr 1fr',
                        }}
                      >
                        <span className="font-bold">Coordinates</span>
                        <span>
                          {source?.location?.latitude || '-'},{' '}
                          {source?.location?.longitude || '-'}
                        </span>
                      </div>
                    </>
                  )}
                  <div className="flex flex-col gap-2">
                    {source.tax_projections.map((estimate, estimateIdx) => (
                      <div key={estimateIdx}>
                        <h3 className="font-bold text-base">
                          Year {estimate.year}
                        </h3>
                        {renderValue(
                          'market_value',
                          estimate.market_value &&
                            `$ ${Math.round(
                              estimate.market_value,
                            ).toLocaleString()}`,
                        )}
                        {renderValue(
                          'gross_assessment',
                          estimate.gross_assessment &&
                            `$ ${Math.round(
                              estimate.gross_assessment,
                            ).toLocaleString()}`,
                        )}
                        {renderValue(
                          'net_assessment',
                          estimate.net_assessment &&
                            `$ ${Math.round(
                              estimate.net_assessment,
                            ).toLocaleString()}`,
                        )}
                        {renderValue(
                          'gross_taxes',
                          estimate.gross_taxes &&
                            `$ ${Math.round(
                              estimate.gross_taxes,
                            ).toLocaleString()}`,
                        )}
                        {renderValue(
                          'net_taxes',
                          estimate.net_taxes &&
                            `$ ${Math.round(
                              estimate.net_taxes,
                            ).toLocaleString()}`,
                        )}
                        {renderValue(
                          'tax_amount',
                          estimate.tax_amount &&
                            `$ ${Math.round(
                              estimate.tax_amount,
                            ).toLocaleString()}`,
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </>
          )}
        </div>
      )}
    </Modal>
  );
};

export default TaxDetailsModal;
