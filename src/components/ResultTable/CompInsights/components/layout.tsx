export const CompScatterContainer = (props: { children: React.ReactNode }) => {
  return (
    <div className="w-full bg-white py-6 px-8 mb-4 rounded-lg shadow-[0_0_4px_rgba(19,16,204,0.1)]">
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
        }}
      >
        <div className="font-medium text-base">Comp Scattergram</div>
        {props.children}
      </div>
    </div>
  );
};
