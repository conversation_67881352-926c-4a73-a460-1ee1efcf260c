import * as d3regression from 'd3-regression';
// import { getMinValue, getMaxValue } from './util';
import jStat from 'jstat';

const REGRESSION_MAP = {
  // exp: d3regression.regressionExp,
  linear: d3regression.regressionLinear,
  // loess: d3regression.regressionLoess,
  log: d3regression.regressionLog,
  // poly: d3regression.regressionPoly,
  pow: d3regression.regressionPow,
  // quad: d3regression.regressionQuad,
};

const getRegressionLine = (data, type, algo, xTicks, yTicks) => {
  let regressionLine = [];
  const minX = xTicks[0];
  const maxX = xTicks[xTicks.length - 1];
  const minY = yTicks[0];
  const maxY = yTicks[yTicks.length - 1];

  const yAlias = data[0].yAlias;

  let inc = 1;
  for (let i = minX; i <= maxX; i += inc) {
    const regY = algo.predict(i);
    // if (regY >= minY && regY <= maxY && i >= minX && i <= maxX) {
    if (inc === 1) inc = 50;
    regressionLine.push({
      x: i,
      regY: algo.predict(i),
      type: `${type} Regression`,
      yAlias,
    });
    // }
    if (i + inc > maxX) {
      inc = 1;
    }
  }

  return regressionLine;
};

const calculateSTEYX = (data, algo, degreesOfFreedom) => {
  // standard error of the predicted y-values / regression line
  const predictedValues = data.map((d) => algo.predict(d.x));
  const residuals = data.map((d, i) => d.y - predictedValues[i]);
  const squaredErrors = residuals.map((d) => Math.pow(d, 2));
  const sumSquaredErrors = squaredErrors.reduce((a, b) => a + b, 0);
  const standardError = Math.sqrt(sumSquaredErrors / degreesOfFreedom);
  return standardError;
};

const calculateDEVSQ = (data) => {
  const mean = data.reduce((sum, value) => sum + value, 0) / data.length;

  const sumOfSquares = data.reduce(
    (sum, value) => sum + Math.pow(value - mean, 2),
    0,
  );

  return sumOfSquares;
};

const getConfidenceInterval = (
  data,
  type,
  algo,
  regressionLine,
  confidenceLevel,
) => {
  const n = data.length; // number of data points
  const degreesOfFreedom = n - 2;
  const syx = calculateSTEYX(data, algo, degreesOfFreedom);
  const xavg = data.reduce((a, b) => a + b.x, 0) / n; // average of x
  const ssx = calculateDEVSQ(data.map((d) => d.x)); // standard deviation of x
  const t = jStat.studentt.inv(
    1 - (1 - confidenceLevel / 100) / 2,
    degreesOfFreedom,
  );

  const confidenceInvervalData = regressionLine.map((d, i) => {
    const x = d.x;
    const y = algo.predict(x);
    const lowerBound =
      y - t * syx * Math.sqrt(1 / n + Math.pow(x - xavg, 2) / ssx);
    const upperBound =
      y + t * syx * Math.sqrt(1 / n + Math.pow(x - xavg, 2) / ssx);

    return {
      x: d.x,
      bounds: [lowerBound, upperBound],
      type: `${type} Confidence Interval`,
      yAlias: d.yAlias,
    };
  });

  return confidenceInvervalData;
};

self.onmessage = function (event) {
  if (event.data.status === 'start') {
    const {
      data,
      regressionType,
      regressionStatus,
      combineRegression,
      confidenceInterval,
      xTicks,
      yTicks,
    } = event.data;

    if (data && data.length <= 1) return;

    const formula = REGRESSION_MAP[regressionType]()
      .x((d) => d.x)
      .y((d) => d.y)
      .domain([xTicks[0], xTicks[xTicks.length - 1]]);

    const regressionLines = [];
    const confidenceIntervals = [];
    const rSquaredData = [];
    const coefficients = [];
    let mlsData = [];
    let sfrData = [];
    let thirdPartyData = [];
    let resaleData = [];
    let newConstructionData = [];
    let LastSalePublicRecordData = [];

    for (let i = 0; i < data.length; i++) {
      if (data[i].type && data[i].type.includes('MLS')) {
        mlsData.push(data[i]);
      } else if (data[i].type === 'SFR Operators') {
        sfrData.push(data[i]);
      } else if (data[i].type === '3rd Party') {
        thirdPartyData.push(data[i]);
      } else if (data[i].type === 'Resale') {
        resaleData.push(data[i]);
      } else if (data[i].type === 'New Construction') {
        newConstructionData.push(data[i]);
      } else if (data[i].type === 'Last Sale Public Record') {
        LastSalePublicRecordData.push(data[i]);
      }
    }
    if (combineRegression) {
      let combinedData = [];
      if (
        mlsData.length > 0 &&
        regressionStatus['MLS'] &&
        regressionStatus['MLS'].active
      ) {
        combinedData.push(...mlsData);
      }
      if (
        sfrData.length > 0 &&
        regressionStatus['SFR Operators'] &&
        regressionStatus['SFR Operators'].active
      ) {
        combinedData.push(...sfrData);
      }
      if (
        thirdPartyData.length > 0 &&
        regressionStatus['3rd Party'] &&
        regressionStatus['3rd Party'].active
      ) {
        combinedData.push(...thirdPartyData);
      }
      if (
        resaleData.length > 0 &&
        regressionStatus['Resale'] &&
        regressionStatus['Resale'].active
      ) {
        combinedData.push(...resaleData);
      }
      if (
        newConstructionData.length > 0 &&
        regressionStatus['New Construction'] &&
        regressionStatus['New Construction'].active
      ) {
        combinedData.push(...newConstructionData);
      }
      if (
        LastSalePublicRecordData.length > 0 &&
        regressionStatus['Last Sale Public Record'] &&
        regressionStatus['Last Sale Public Record'].active
      ) {
        combinedData.push(...LastSalePublicRecordData);
      }
      const algo = formula(combinedData);
      coefficients.push({ type: 'Combined', a: algo.a, b: algo.b });

      const rSquared = algo.rSquared.toFixed(2);
      rSquaredData.push({ type: 'Combined', rSquared });

      const regressionLine = getRegressionLine(
        combinedData,
        'Combined',
        algo,
        xTicks,
        yTicks,
      );
      regressionLines.push(...regressionLine);

      if (confidenceInterval.active) {
        const confidenceData = getConfidenceInterval(
          combinedData,
          'Combined',
          algo,
          regressionLine,
          confidenceInterval.level,
        );
        confidenceIntervals.push(...confidenceData);
      }
    } else {
      if (
        mlsData.length > 0 &&
        regressionStatus['MLS'] &&
        regressionStatus['MLS'].active
      ) {
        const algo = formula(mlsData);
        coefficients.push({ type: 'MLS', a: algo.a, b: algo.b });

        const rSquared = algo.rSquared.toFixed(2);
        rSquaredData.push({ type: 'MLS', rSquared });

        const regressionLine = getRegressionLine(
          mlsData,
          'MLS',
          algo,
          xTicks,
          yTicks,
        );
        regressionLines.push(...regressionLine);

        if (confidenceInterval.active) {
          const confidenceData = getConfidenceInterval(
            mlsData,
            'MLS',
            algo,
            regressionLine,
            confidenceInterval.level,
          );
          confidenceIntervals.push(...confidenceData);
        }
      }
      if (
        resaleData.length > 0 &&
        regressionStatus['Resale'] &&
        regressionStatus['Resale'].active
      ) {
        const algo = formula(resaleData);
        coefficients.push({ type: 'Resale', a: algo.a, b: algo.b });

        const rSquared = algo.rSquared.toFixed(2);
        rSquaredData.push({ type: 'Resale', rSquared });

        const regressionLine = getRegressionLine(
          resaleData,
          'Resale',
          algo,
          xTicks,
          yTicks,
        );
        regressionLines.push(...regressionLine);

        if (confidenceInterval.active) {
          const confidenceData = getConfidenceInterval(
            resaleData,
            'Resale',
            algo,
            regressionLine,
            confidenceInterval.level,
          );
          confidenceIntervals.push(...confidenceData);
        }
      }
      if (
        newConstructionData.length > 0 &&
        regressionStatus['New Construction'] &&
        regressionStatus['New Construction'].active
      ) {
        const algo = formula(newConstructionData);
        coefficients.push({ type: 'New Construction', a: algo.a, b: algo.b });

        const rSquared = algo.rSquared.toFixed(2);
        rSquaredData.push({ type: 'New Construction', rSquared });

        const regressionLine = getRegressionLine(
          newConstructionData,
          'New Construction',
          algo,
          xTicks,
          yTicks,
        );
        regressionLines.push(...regressionLine);

        if (confidenceInterval.active) {
          const confidenceData = getConfidenceInterval(
            newConstructionData,
            'New Construction',
            algo,
            regressionLine,
            confidenceInterval.level,
          );
          confidenceIntervals.push(...confidenceData);
        }
      }
      if (
        LastSalePublicRecordData.length > 0 &&
        regressionStatus['Last Sale Public Record'] &&
        regressionStatus['Last Sale Public Record'].active
      ) {
        const algo = formula(LastSalePublicRecordData);
        coefficients.push({
          type: 'Last Sale Public Record',
          a: algo.a,
          b: algo.b,
        });

        const rSquared = algo.rSquared.toFixed(2);
        rSquaredData.push({ type: 'Last Sale Public Record', rSquared });

        const regressionLine = getRegressionLine(
          LastSalePublicRecordData,
          'Last Sale Public Record',
          algo,
          xTicks,
          yTicks,
        );
        regressionLines.push(...regressionLine);

        if (confidenceInterval.active) {
          const confidenceData = getConfidenceInterval(
            LastSalePublicRecordData,
            'Last Sale Public Record',
            algo,
            regressionLine,
            confidenceInterval.level,
          );
          confidenceIntervals.push(...confidenceData);
        }
      }
      if (
        sfrData.length > 0 &&
        regressionStatus['SFR Operators'] &&
        regressionStatus['SFR Operators'].active
      ) {
        const algo = formula(sfrData);
        coefficients.push({ type: 'SFR Operators', a: algo.a, b: algo.b });

        const rSquared = algo.rSquared.toFixed(2);
        rSquaredData.push({ type: 'SFR Operators', rSquared });

        const regressionLine = getRegressionLine(
          sfrData,
          'SFR Operators',
          algo,
          xTicks,
          yTicks,
        );
        regressionLines.push(...regressionLine);

        if (confidenceInterval.active) {
          const confidenceData = getConfidenceInterval(
            sfrData,
            'SFR Operators',
            algo,
            regressionLine,
            confidenceInterval.level,
          );
          confidenceIntervals.push(...confidenceData);
        }
      }
      if (
        thirdPartyData.length > 0 &&
        regressionStatus['3rd Party'] &&
        regressionStatus['3rd Party'].active
      ) {
        const algo = formula(thirdPartyData);
        coefficients.push({ type: '3rd Party', a: algo.a, b: algo.b });

        const rSquared = algo.rSquared.toFixed(2);
        rSquaredData.push({ type: '3rd Party', rSquared });

        const regressionLine = getRegressionLine(
          thirdPartyData,
          '3rd Party',
          algo,
          xTicks,
          yTicks,
        );
        regressionLines.push(...regressionLine);
        if (confidenceInterval.active) {
          const confidenceData = getConfidenceInterval(
            thirdPartyData,
            '3rd Party',
            algo,
            regressionLine,
            confidenceInterval.level,
          );
          confidenceIntervals.push(...confidenceData);
        }
      }
    }

    self.postMessage({
      status: 'done',
      regressionLines: JSON.parse(JSON.stringify(regressionLines)),
      confidenceIntervals: JSON.parse(JSON.stringify(confidenceIntervals)),
      rSquaredData: JSON.parse(JSON.stringify(rSquaredData)),
      coefficients: JSON.parse(JSON.stringify(coefficients)),
    });
  }
};
