import {
  Button,
  Checkbox,
  Popover,
  Radio,
  Select,
  Slider,
  Switch,
  Tag,
  Tooltip,
} from 'antd';
import React from 'react';
import { BsFilter } from 'react-icons/bs';
import { FaMinus, FaSearch } from 'react-icons/fa';
import { useDispatch, useSelector } from 'umi';
// @ts-ignore
import { locatePropertyHandler } from '../../../MapCMA/MapCMA';
import { useFilterRange } from '../hooks/filter';
import { color, DATA_SOURCE_LIST, propertyFieldMap } from '../lib/constants';
import {
  ChartData,
  HotpadsProperty,
  MLSProperty,
  PropertyData,
  SFRProperty,
} from '../lib/types';
import {
  basePropertyFilterFn,
  getPropertyAddress,
  validateDataSource,
} from '../lib/utils';
import { DataSource, useCompScatter } from './provider';

const tagRender = (props: {
  label: React.ReactNode;
  value: any;
  disabled: boolean;
  onClose: (event?: React.MouseEvent<HTMLElement, MouseEvent>) => void;
  closable: boolean;
  isMaxTag: boolean;
}) => {
  const { value, closable, onClose } = props;

  const onPreventMouseDown: React.MouseEventHandler<HTMLSpanElement> = (
    event,
  ) => {
    event.preventDefault();
    event.stopPropagation();
  };

  return (
    <Tag
      color={color[value as keyof typeof color]}
      onMouseDown={onPreventMouseDown}
      closable={closable}
      onClose={onClose}
      style={{
        marginRight: 3,
      }}
    >
      {value}
    </Tag>
  );
};

const DataSourceSelector = () => {
  const { sourceOptions, setSourceOptions, searchingMode } = useCompScatter();

  return (
    <Select
      mode="multiple"
      tagRender={tagRender}
      value={sourceOptions}
      size="small"
      style={{
        width: '100%',
      }}
      options={validateDataSource(
        DATA_SOURCE_LIST as DataSource[],
        searchingMode,
      ).map((s) => ({ label: s, value: s }))}
      onChange={(options) => {
        setSourceOptions(options);
      }}
    />
  );
};

const RegressionTypeSelector = () => {
  const {
    regressionLine: { type },
    setRegressionLine,
  } = useCompScatter();
  return (
    <Select
      style={{ width: '120px' }}
      value={type}
      size="small"
      onChange={(value) =>
        setRegressionLine((prev) => ({ ...prev, type: value }))
      }
      options={[
        { value: 'linear', label: 'Linear' },
        { value: 'logarithmic', label: 'Logarithmic' },
        { value: 'power', label: 'Power' },
      ]}
    />
  );
};

const PriceTypeSelector = () => {
  const { priceType, setPriceType, searchingMode } = useCompScatter();
  return (
    <Radio.Group
      value={priceType}
      size="small"
      onChange={(e) => {
        setPriceType(e.target.value);
      }}
    >
      <Radio.Button value="full">
        {searchingMode === 'Lease' ? 'Rent' : 'Price'}
      </Radio.Button>
      <Radio.Button value="psf">
        {searchingMode === 'Lease' ? 'RSF' : 'PSF'}
      </Radio.Button>
    </Radio.Group>
  );
};

const labelMap = {
  bed: 'Bed',
  bath: 'Bath',
  sqft: 'Sqft',
  yearBuilt: 'Year Built',
};

const PropertyAttributeFilter = () => {
  const { setPropertyFilter } = useCompScatter();
  const filterRange = useFilterRange();

  // Store the initial filter range when component mounts, with safeguards
  const [initialFilterRange] = React.useState(() => {
    // Make a safe copy of filterRange with default values if needed
    if (!filterRange) {
      const currentYear = new Date().getFullYear();
      return {
        bed: [0, 5],
        bath: [0, 5],
        sqft: [0, 5000],
        yearBuilt: [0, currentYear],
      };
    }

    // Ensure yearBuilt always starts with 0 as minimum
    const modifiedRange = { ...filterRange };
    if (modifiedRange.yearBuilt) {
      modifiedRange.yearBuilt = [
        0,
        modifiedRange.yearBuilt[1] || new Date().getFullYear(),
      ];
    }

    return modifiedRange;
  });

  // Use initialFilterRange for UI display and slider bounds
  const [filter, setFilter] = React.useState(() => {
    // Ensure filter starts with valid values
    const currentYear = new Date().getFullYear();
    const defaultFilter = {
      bed: [0, 5],
      bath: [0, 5],
      sqft: [0, 5000],
      yearBuilt: [0, currentYear],
    };

    return initialFilterRange || defaultFilter;
  });

  const [yearInputs, setYearInputs] = React.useState(['', '']);

  // Synchronize filter with propertyFilter
  React.useEffect(() => {
    if (!filter) return;
    setPropertyFilter((prev) => {
      return {
        ...prev,
        shouldFilter: true,
        filterFn: (source, data) =>
          data !== undefined && basePropertyFilterFn(filter, source, data),
      };
    });
  }, [filter]);

  // Initialize yearInputs and synchronize with filter.yearBuilt
  React.useEffect(() => {
    const maxYear =
      initialFilterRange?.yearBuilt?.[1] ?? new Date().getFullYear();
    setYearInputs(['0', maxYear.toString()]);
    setFilter((prev) => ({
      ...prev,
      yearBuilt: [0, maxYear],
    }));
  }, []);

  // Synchronize yearInputs with filter.yearBuilt when filter changes
  React.useEffect(() => {
    if (filter?.yearBuilt) {
      setYearInputs([
        filter.yearBuilt[0].toString(),
        filter.yearBuilt[1].toString(),
      ]);
    }
  }, [filter.yearBuilt]);

  const handleYearInputChange = (index: number, value: string) => {
    // Update temporary input state
    const newInputs = [...yearInputs];
    newInputs[index] = value;
    setYearInputs(newInputs);

    // Only update filter if the value is not empty
    if (value !== '') {
      const numValue = parseInt(value);
      // Ensure we have default values in case yearBuilt is undefined
      const minRange = initialFilterRange?.yearBuilt?.[0] ?? 0;
      const maxRange = initialFilterRange?.yearBuilt?.[1] ?? 0;

      setFilter((prev) => {
        // Ensure prev.yearBuilt exists with default values if needed
        const prevYearBuilt = prev?.yearBuilt ?? [0, 0];
        const newValues = [...prevYearBuilt];

        newValues[index] = Math.max(minRange, Math.min(maxRange, numValue));

        if (index === 0 && newValues[0] > (newValues[1] ?? 0)) {
          newValues[1] = newValues[0];
        }
        if (index === 1 && newValues[1] < (newValues[0] ?? 0)) {
          newValues[0] = newValues[1];
        }

        return { ...prev, yearBuilt: newValues };
      });
    }
  };

  const handleYearInputBlur = (index: number) => {
    // When losing focus, convert empty to current value or default
    if (yearInputs[index] === '') {
      const newInputs = [...yearInputs];
      // Safely get the current value with fallback to 0
      const currentValue = filter?.yearBuilt?.[index] ?? 0;
      newInputs[index] = currentValue.toString();
      setYearInputs(newInputs);
    }
  };

  return (
    <div>
      {filter &&
        Object.keys(filter).map((key) => {
          // Always use initialFilterRange for min/max values with safe fallbacks
          const defaultRange = [0, 0];
          const rangeValues =
            initialFilterRange?.[key as keyof typeof initialFilterRange] ??
            defaultRange;
          const minRange = rangeValues[0] ?? 0;
          const maxRange = rangeValues[1] ?? 0;

          // Safely get current filter values
          const currentFilterValues =
            filter[key as keyof typeof filter] ?? defaultRange;
          const yearBuiltValues = filter?.yearBuilt ?? defaultRange;

          return (
            <div key={key}>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'end',
                  gap: '10px',
                }}
              >
                <span style={{ fontSize: '12px' }}>
                  {labelMap[key as keyof typeof labelMap] ?? key}
                </span>
                {key === 'yearBuilt' ? (
                  <div
                    style={{ display: 'flex', gap: '5px', alignItems: 'end' }}
                  >
                    <input
                      type="number"
                      value={yearInputs[0] ?? '0'}
                      onChange={(e) => handleYearInputChange(0, e.target.value)}
                      onBlur={() => handleYearInputBlur(0)}
                      min={0}
                      max={yearBuiltValues[1] ?? maxRange}
                      style={{
                        width: '60px',
                        height: '24px',
                        fontSize: '12px',
                        fontWeight: 'bold',
                      }}
                    />
                    <span>-</span>
                    <input
                      type="number"
                      value={yearInputs[1] ?? maxRange.toString()}
                      onChange={(e) => handleYearInputChange(1, e.target.value)}
                      min={yearBuiltValues[0] ?? 0}
                      max={maxRange}
                      style={{
                        width: '60px',
                        height: '24px',
                        fontSize: '12px',
                        fontWeight: 'bold',
                      }}
                    />
                  </div>
                ) : (
                  <span>
                    <strong>{currentFilterValues[0] ?? 0}</strong> -{' '}
                    <strong>{currentFilterValues[1] ?? 0}</strong>
                  </span>
                )}
              </div>
              <Slider
                style={{ marginTop: '5px' }}
                min={minRange}
                max={maxRange}
                step={key === 'bath' ? 0.5 : 1}
                value={currentFilterValues}
                defaultValue={currentFilterValues}
                onChange={(value: number[]) =>
                  setFilter((prev) => ({ ...prev, [key]: value }))
                }
                range={{ draggableTrack: true }}
              />
            </div>
          );
        })}
    </div>
  );
};

export const DataModifiers = (props: { active: boolean }) => {
  const { syncComp } = useCompScatter();
  const [filterOpen, setFilterOpen] = React.useState(false);

  return (
    <div className="flex flex-row justify-between items-center flex-wrap gap-1 py-0.5">
      <div className="flex flex-row gap-2 items-center">
        <DataSourceSelector />
      </div>
      <div className="flex flex-row gap-2 items-center">
        <RegressionTypeSelector />
        <PriceTypeSelector />
        {props.active && (
          <Popover
            title="Filter"
            trigger="click"
            open={filterOpen}
            onOpenChange={(newOpen) => {
              setFilterOpen(newOpen);
            }}
            placement="bottomRight"
            content={<PropertyAttributeFilter />}
          >
            <Tooltip title={syncComp ? 'Disabled in sync mode' : null}>
              <Button size="small" disabled={syncComp}>
                <BsFilter size={20} />
              </Button>
            </Tooltip>
          </Popover>
        )}
      </div>
    </div>
  );
};

export const InfoModifiers = () => {
  const { syncComp, setSyncComp, regressionLine, setRegressionLine } =
    useCompScatter();

  return (
    <div className="flex flex-row justify-between items-center flex-wrap py-0.5">
      <div className="flex flex-row gap-2 items-center">
        <Switch
          size="small"
          checkedChildren="ON"
          unCheckedChildren="OFF"
          checked={syncComp}
          onChange={(checked) => {
            setSyncComp(checked);
          }}
        />
        <span className="text-nowrap">Sync with comps</span>
      </div>
      <div className="flex flex-row gap-2 items-center flex-wrap">
        <Checkbox
          checked={regressionLine.combine}
          onChange={(e) =>
            setRegressionLine((prev) => ({
              ...prev,
              combine: e.target.checked,
            }))
          }
        >
          <span className="text-nowrap">Combine Trendlines</span>
        </Checkbox>
        <Checkbox
          checked={regressionLine.equation.show}
          onChange={(e) =>
            setRegressionLine((prev) => ({
              ...prev,
              equation: { ...prev.equation, show: e.target.checked },
            }))
          }
        >
          <span className="text-nowrap">Show Equation</span>
        </Checkbox>
        <Checkbox
          checked={regressionLine.confidence.enabled}
          onChange={(e) =>
            setRegressionLine((prev) => ({
              ...prev,
              confidence: { ...prev.confidence, enabled: e.target.checked },
            }))
          }
        >
          <span className="text-nowrap">Confidence Interval</span>
        </Checkbox>
        {regressionLine.confidence.enabled && (
          <Select
            value={regressionLine.confidence.value}
            size="small"
            onChange={(value) =>
              setRegressionLine((prev) => ({
                ...prev,
                confidence: { ...prev.confidence, value },
              }))
            }
            options={[
              { value: 99, label: '99%' },
              { value: 95, label: '95%' },
              { value: 90, label: '90%' },
            ]}
          />
        )}
      </div>
    </div>
  );
};

const useLocateProperty = (property?: ChartData) => {
  const {
    currentNationalOperatorsPropertiesFiltered,
    currentHotPadsPropertiesFiltered,
    currentMLSPropertiesFiltered,
  } = useSelector((state: any) => state.CMA);

  const locateProperty = React.useCallback(() => {
    if (!property) return;

    if (['MLS', 'Resale', 'New Construction'].includes(property.type)) {
      locatePropertyHandler('MLS', (property.property as MLSProperty).mlsid);
    } else if (property.type === 'SFR Operators') {
      locatePropertyHandler('SFR', (property.property as SFRProperty).base_id);
    } else if (property.type === '3rd Party') {
      locatePropertyHandler(
        'HotPads',
        (property.property as HotpadsProperty).base_id,
      );
    }
  }, [property]);

  const exists = React.useMemo(() => {
    if (!property) return false;

    if (['MLS', 'Resale', 'New Construction'].includes(property.type)) {
      return currentMLSPropertiesFiltered.some(
        (p: MLSProperty) =>
          p.mlsid === (property.property as MLSProperty).mlsid,
      );
    } else if (property.type === 'SFR Operators') {
      return currentNationalOperatorsPropertiesFiltered.some(
        (p: SFRProperty) =>
          p.base_id === (property.property as SFRProperty).base_id,
      );
    } else if (property.type === '3rd Party') {
      return currentHotPadsPropertiesFiltered.some(
        (p: HotpadsProperty) =>
          p.base_id === (property.property as HotpadsProperty).base_id,
      );
    }

    return false;
  }, [
    property,
    currentNationalOperatorsPropertiesFiltered,
    currentHotPadsPropertiesFiltered,
    currentMLSPropertiesFiltered,
  ]);

  if (!exists) return null;
  return locateProperty;
};

export const PropertyModifier = () => {
  const {
    selectedChartData,
    setSelectedChartData,
    setRemovedPropertyIds,
    searchingMode,
  } = useCompScatter();
  const locateProperty = useLocateProperty(selectedChartData);

  const {
    selectedRowKeysNationalOperators,
    selectedRowKeysHotPads,
    selectedRowKeysMLSLease,
    selectedRowKeysMLSSale,
  } = useSelector((state: any) => state.CMA);
  const dispatch = useDispatch();

  const onRemoveProperty = React.useCallback(
    (property: ChartData) => {
      setRemovedPropertyIds((prev) => ({
        ...prev,
        [property.type]: [
          ...prev[property.type as keyof typeof prev],
          property.property[
            propertyFieldMap[property.type as keyof typeof propertyFieldMap]
              .id as keyof PropertyData
          ],
        ],
      }));

      let payload = {};

      if (property.type === 'SFR Operators') {
        payload = {
          selectedRowKeysNationalOperators:
            selectedRowKeysNationalOperators.filter(
              (key: string) =>
                key !==
                property.property[
                  propertyFieldMap[
                    property.type as keyof typeof propertyFieldMap
                  ].id as keyof PropertyData
                ],
            ),
        };
      } else if (property.type === '3rd Party') {
        payload = {
          selectedRowKeysHotPads: selectedRowKeysHotPads.filter(
            (key: string) =>
              key !==
              property.property[
                propertyFieldMap[property.type as keyof typeof propertyFieldMap]
                  .id as keyof PropertyData
              ],
          ),
        };
      } else if (
        ['MLS', 'Resale', 'New Construction'].includes(property.type)
      ) {
        if (searchingMode === 'Lease') {
          payload = {
            selectedRowKeysMLSLease: selectedRowKeysMLSLease.filter(
              (key: string) =>
                key !==
                property.property[
                  propertyFieldMap[
                    property.type as keyof typeof propertyFieldMap
                  ].id as keyof PropertyData
                ],
            ),
          };
        } else {
          payload = {
            selectedRowKeysMLSSale: selectedRowKeysMLSSale.filter(
              (key: string) =>
                key !==
                property.property[
                  propertyFieldMap[
                    property.type as keyof typeof propertyFieldMap
                  ].id as keyof PropertyData
                ],
            ),
          };
        }
      }

      if (Object.keys(payload).length > 0) {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: payload,
        });
      }

      setSelectedChartData(undefined);
    },
    [
      searchingMode,
      selectedRowKeysNationalOperators,
      selectedRowKeysHotPads,
      selectedRowKeysMLSLease,
      selectedRowKeysMLSSale,
    ],
  );

  if (!selectedChartData) return null;
  return (
    <div className="flex flex-row justify-center gap-3 mt-2">
      <div>
        <span>
          Address: <strong>{getPropertyAddress(selectedChartData)}</strong>
        </span>
      </div>
      {selectedChartData.type !== 'Subject Property' && (
        <div className="flex flex-row gap-2 items-center">
          {locateProperty && (
            <button
              className="w-6 h-6 rounded-full bg-green-400 cursor hover:bg-green-500 flex justify-center items-center"
              onClick={(e) => {
                e.stopPropagation();
                locateProperty();
              }}
            >
              <FaSearch className="text-white hover:text-[#ccc]" />
            </button>
          )}
          <button
            className="w-6 h-6 rounded-full bg-red-400 cursor hover:bg-red-500 flex justify-center items-center"
            onClick={(e) => {
              e.stopPropagation();
              onRemoveProperty(selectedChartData);
            }}
          >
            <FaMinus className="text-white hover:text-[#ccc]" />
          </button>
        </div>
      )}
    </div>
  );
};
