import isEqual from 'lodash.isequal';
import React from 'react';
import {
  DATA_SOURCE_LIST,
  DEFAULT_PRICE_TYPE,
  DEFAULT_PROPERTY_FILTER,
  DEFAULT_REGRESSION_LINE,
  DEFAULT_REMOVED_PROPERTY_IDS,
  DEFAULT_SYNC_COMP,
} from '../lib/constants';
import {
  ChartData,
  HotpadsProperty,
  LastSalePublicRecordProperty,
  MLSProperty,
  PropertyData,
  SFRProperty,
  SubjectProperty,
} from '../lib/types';
import { validateDataSource } from '../lib/utils';

export type SearchingMode = 'Lease' | 'Sale' | 'Land';
export type RegressionLine = typeof DEFAULT_REGRESSION_LINE;
export type PriceType = typeof DEFAULT_PRICE_TYPE;
export type DataSource =
  | 'Subject Property'
  | 'SFR Operators'
  | '3rd Party'
  | 'Last Sale Public Record'
  | 'MLS'
  | 'Resale'
  | 'New Construction';

type PropertyFilter = {
  shouldFilter: boolean;
  filterFn: (source: DataSource, data?: PropertyData) => boolean;
};

interface CompScatterContextProps {
  searchingMode: SearchingMode;
  regressionLine: RegressionLine;
  setRegressionLine: React.Dispatch<React.SetStateAction<RegressionLine>>;
  priceType: PriceType;
  setPriceType: React.Dispatch<React.SetStateAction<PriceType>>;
  syncComp: boolean;
  setSyncComp: React.Dispatch<React.SetStateAction<boolean>>;
  sourceOptions: DataSource[];
  setSourceOptions: React.Dispatch<React.SetStateAction<DataSource[]>>;
  propertyFilter: PropertyFilter;
  setPropertyFilter: React.Dispatch<React.SetStateAction<PropertyFilter>>;
  removedPropertyIds: {
    [K in Exclude<DataSource, 'Subject Property'>]: string[];
  };
  setRemovedPropertyIds: React.Dispatch<
    React.SetStateAction<{
      [K in Exclude<DataSource, 'Subject Property'>]: string[];
    }>
  >;
  selectedChartData: ChartData | undefined;
  setSelectedChartData: React.Dispatch<
    React.SetStateAction<ChartData | undefined>
  >;
}
const CompScatterContext = React.createContext<
  undefined | CompScatterContextProps
>(undefined);

interface CompScatterProviderProps {
  searchingMode: SearchingMode;
  children: React.ReactNode;
}
export const CompScatterProvider = (props: CompScatterProviderProps) => {
  const [sourceOptions, setSourceOptions] = React.useState<DataSource[]>(
    validateDataSource(DATA_SOURCE_LIST as DataSource[], props.searchingMode),
  );
  const [regressionLine, setRegressionLine] = React.useState<RegressionLine>(
    DEFAULT_REGRESSION_LINE,
  );
  const [priceType, setPriceType] =
    React.useState<PriceType>(DEFAULT_PRICE_TYPE);
  const [syncComp, setSyncComp] = React.useState<boolean>(DEFAULT_SYNC_COMP);
  const [propertyFilter, setPropertyFilter] = React.useState<PropertyFilter>(
    DEFAULT_PROPERTY_FILTER,
  );
  const [removedPropertyIds, setRemovedPropertyIds] = React.useState<{
    [K in Exclude<DataSource, 'Subject Property'>]: string[];
  }>(DEFAULT_REMOVED_PROPERTY_IDS);
  const [selectedChartData, setSelectedChartData] = React.useState<ChartData>();

  React.useEffect(() => {
    setSourceOptions((prev) => {
      const valid = validateDataSource(prev, props.searchingMode);
      if (isEqual(valid, prev)) return prev;
      return valid;
    });
  }, [props.searchingMode]);

  const ctx = React.useMemo(
    () => ({
      searchingMode: props.searchingMode,
      regressionLine,
      setRegressionLine,
      priceType,
      setPriceType,
      syncComp,
      setSyncComp,
      sourceOptions,
      setSourceOptions,
      propertyFilter,
      setPropertyFilter,
      removedPropertyIds,
      setRemovedPropertyIds,
      selectedChartData,
      setSelectedChartData,
    }),
    [
      props.searchingMode,
      regressionLine,
      priceType,
      syncComp,
      sourceOptions,
      propertyFilter,
      removedPropertyIds,
      selectedChartData,
    ],
  );

  return (
    <CompScatterContext.Provider value={ctx}>
      {props.children}
    </CompScatterContext.Provider>
  );
};

export const useCompScatter = () => {
  const ctx = React.useContext(CompScatterContext);
  if (ctx === undefined) {
    throw new Error('useCompScatter must be used within a CompScatterProvider');
  }
  return ctx;
};
