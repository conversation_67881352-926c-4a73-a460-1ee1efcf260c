import { Mix, type MixConfig } from '@ant-design/charts';
import { Linear, wilkinsonExtended } from '@antv/scale';
import { Button } from 'antd';
import { isEqual } from 'lodash';
import React from 'react';
import { BiLineChart } from 'react-icons/bi';
import {
  BsCircleFill,
  BsHexagonFill,
  BsSquareFill,
  BsTriangleFill,
} from 'react-icons/bs';
import { color, DEFAULT_REMOVED_PROPERTY_IDS } from '../lib/constants';
import { ChartData } from '../lib/types';
import { showSFROperatorsFullNameOnly } from '../lib/utils';
import { PriceType, RegressionLine, useCompScatter } from './provider';

interface CompScatterChartProps {
  data: any[];
}

const yAxisFormat = (num: number) => {
  if (num < 10) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(num);
  } else if (num > 1000) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0,
      minimumFractionDigits: 0,
    }).format(Math.round(num / 10) * 10);
  } else {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0,
      minimumFractionDigits: 0,
    }).format(num);
  }
};

const xAxisFormat = (num: number) => {
  if (!num) return num;
  return num.toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  });
};

const getAxisTicks = (
  domain: number[],
  range = [0, 1],
  tickCount = 10,
  nice = true,
) => {
  return new Linear({
    domain: domain,
    range: range,
    tickCount: tickCount,
    nice: nice,
    tickMethod: wilkinsonExtended,
  }).getTicks();
};

const RenderEquation = (opts: {
  coefficients: any[];
  regressionType: 'linear' | 'logarithmic' | 'power';
}) => {
  const { coefficients, regressionType } = opts;
  let equation = [];

  for (let i = 0; i < coefficients.length; i++) {
    const { type } = coefficients[i];
    const a = Math.round(coefficients[i].a * 100) / 100;
    const b = Math.round(coefficients[i].b * 100) / 100;

    if (regressionType === 'linear') {
      equation.push(
        <div key={`linear-${i}`}>
          <span>{type}: </span>
          <span>
            <strong>y</strong> = {a}
            <strong>x</strong> + {b}
          </span>
        </div>,
      );
    } else if (regressionType === 'logarithmic') {
      equation.push(
        <div key={`log-${i}`}>
          <span>{type}: </span>
          <span>
            <strong>y</strong> = {a} * <strong>{`ln(x)`}</strong> + {b}
          </span>
        </div>,
      );
    } else if (regressionType === 'power') {
      equation.push(
        <div key={`pow-${i}`}>
          <span>{type}: </span>
          <span>
            <strong>y</strong> = {a} * <strong>x</strong>^{b}
          </span>
        </div>,
      );
    }
  }

  return <>{equation}</>;
};

const useAxisHandler = (props: { data: any[]; type: 'x' | 'y' }) => {
  const { data, type } = props;
  const [min, setMin] = React.useState<number>(0);
  const [max, setMax] = React.useState<number>(0);
  const [ticks, setTicks] = React.useState<Array<number>>([]);

  React.useEffect(() => {
    if (data.length > 0) {
      const values = data.map((d) => d[type]);
      let min = Math.min(...values);
      min = type === 'y' && min < 0 ? 0 : min;
      let max = Math.max(...values);
      const diff = max - min;
      min = min - diff * 0.1;
      max = max + diff * 0.1;
      const ticks = getAxisTicks([min, ...values, max]);
      setMin(ticks[0] as number);
      setMax(ticks[ticks.length - 1] as number);
      setTicks(ticks as number[]);
    }
  }, [data]);

  return { min, max, ticks };
};

const useChartConfig = (props: {
  data: any[];
  ciData: any[];
  minX: number;
  minY: number;
  maxX: number;
  maxY: number;
}): MixConfig | undefined => {
  const { data, ciData, minX, minY, maxX, maxY } = props;

  const config = React.useMemo(() => {
    return {
      data: data,
      syncViewPadding: true,
      slider: {
        formatter: (value: any) => '',
      },
      legend: false,
      tooltip: {
        shared: false,
        showCrosshairs: true,
        crosshairs: {
          type: 'xy',
          follow: true,
        },
        marker: false,
        title: 'type',
        customContent: (title: string, items: any[]) => {
          if (items.length === 0) return null;
          if (title.includes('Confidence')) return null;
          if (title.includes('Regression')) {
            const type = title.split(' Regression')[0];
            const unitType = items[0].data.yAlias.toLowerCase();
            const size = xAxisFormat(items[0].data.x);
            const value = yAxisFormat(items[0].data.regY);

            let message = `The ideal ${unitType} based on ${type} comps for ${size} sqft is ${value}`;

            if (ciData && ciData.length > 0) {
              const x = typeof size === 'string' ? parseFloat(size) : size;
              const CI = ciData.find((d) => d.x === x);
              if (CI) {
                const lowerBound = yAxisFormat(CI.bounds[0]);
                const upperBound = yAxisFormat(CI.bounds[1]);
                message = `${message}, with a range between ${lowerBound} and ${upperBound}`;
              }
            }

            return (
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  padding: '10px 0',
                  gap: '10px',
                  width: '150px',
                }}
              >
                <span style={{ fontWeight: 'bold', lineHeight: '20px' }}>
                  {message}
                </span>
              </div>
            );
          }

          const newItems = [];
          for (let i = 0; i < items.length; i++) {
            if (items[i].name == 'type' || items[i].name == 'shape') continue;
            const itemName =
              items[i].name === 'y' ? items[i].data.yAlias : items[i].name;
            newItems.push({
              name: itemName,
              value: items[i].value,
            });
          }

          const type = items[0].data.type;
          const property = items[0].data.property;

          if (['MLS', 'Resale', 'New Construction'].includes(type)) {
            newItems.push({
              name: 'Address',
              value: property?.fulladdress,
            });
          } else if (type.includes('Subject')) {
            newItems.push({
              name: 'Address',
              value: property?.streetAddress,
            });
          } else {
            newItems.push({
              name: 'Address',
              value: property?.address?.split(',')[0],
            });
            if (type.includes('SFR Operators')) {
              newItems.push({
                name: 'Brand',
                value: showSFROperatorsFullNameOnly(property.brand),
              });
            }
          }

          return (
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                padding: '10px 0',
                gap: '10px',
              }}
            >
              <div>
                <span style={{ fontWeight: 'bold' }}>{title}</span>
              </div>
              {newItems.map((item, index) => (
                <div
                  key={index}
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    gap: '20px',
                  }}
                >
                  <span>{item.name}:</span>
                  <span style={{ fontWeight: 'bold' }}>{item.value}</span>
                </div>
              ))}
            </div>
          );
        },
      },

      plots: [
        {
          type: 'line',
          top: true,
          options: {
            limitInPlot: true,
            xField: 'x',
            yField: 'regY',
            yAxis: {
              position: 'left',
              // tickCount: yTickCount,
            },
            xAxis: {
              tickCount: 10,
              nice: false,
            },
            meta: {
              regY: {
                minLimit: minY < 0 ? 0 : minY,
                maxLimit: maxY,
                formatter: (value: number) => (value ? yAxisFormat(value) : ''),
              },
            },
            size: 2,
            seriesField: 'type',
            color: ({ type }: any) => {
              const source = Object.keys(color).find(
                (key) => type && type.includes(key),
              );
              return color[source as keyof typeof color] || 'red';
            },
          },
        },
        {
          type: 'area',
          top: true,
          options: {
            limitInPlot: true,
            xField: 'x',
            yField: 'bounds',
            isStack: false,
            yAxis: {
              position: 'left',
              // tickCount: yTickCount,
            },
            xAxis: {
              tickCount: 10,
              nice: false,
            },
            meta: {
              bounds: {
                minLimit: minY,
                maxLimit: maxY,
                formatter: () => '',
              },
            },
            seriesField: 'type',
            color: ({ type }: any) =>
              color[type as keyof typeof color] || '#000',
            areaStyle: () => {
              return {
                fillOpacity: 0.1,
              };
            },
          },
        },
        {
          type: 'scatter',
          top: true,
          options: {
            xField: 'x',
            yField: 'y',
            yAxis: {
              position: 'left',
              // tickCount: yTickCount,
            },
            xAxis: {
              title: {
                text: 'Square Feet',
              },
              tickCount: 10,
              nice: false,
              min: minX - 100 >= 0 ? minX - 100 : 0,
              // max: maxX + 100,
              grid: {
                line: {
                  style: {
                    stroke: '#eee',
                  },
                },
              },
            },
            size: 5,
            colorField: 'type',
            color: ({ type }: any) =>
              color[type as keyof typeof color] || '#000',
            shapeField: 'shape',
            shape: ({ shape }: any) => shape,
            shapeLegend: false,
            sizeField: 'type',
            // @ts-ignore
            size: ({ type }: any) => {
              if (type === 'Subject Property') return 8;
              return 6;
            },
            pointStyle: {
              lineWidth: 0,
            },
            meta: {
              x: {
                minLimit: 0,
                alias: 'Sqft',
                formatter: (value: number) => xAxisFormat(value),
              },
              y: {
                minLimit: minY < 0 ? 0 : minY,
                maxLimit: maxY,
                formatter: (value: number) => yAxisFormat(value),
              },
            },
          },
        },
      ],
    };
  }, [data, minX, minY, maxX, maxY]);

  return config as unknown as MixConfig | undefined;
};

const regressionTypeMap = {
  linear: 'linear',
  logarithmic: 'log',
  power: 'pow',
};

const useRegressionLine = (props: {
  data: any[];
  xData: any[];
  yData: any[];
  opts?: RegressionLine;
  enabled?: boolean;
}) => {
  const { data, xData, yData, opts, enabled = true } = props;
  const [regressionData, setRegressionData] = React.useState<{
    confidenceIntervals: any[];
    regressionLines: any[];
    rSquaredData: any[];
    coefficients: any[];
  }>();

  React.useEffect(() => {
    if (!enabled) return;

    const worker = new Worker(new URL('./worker.js', import.meta.url));

    worker.postMessage({
      status: 'start',
      data,
      regressionType:
        regressionTypeMap[opts?.type as keyof typeof regressionTypeMap],
      regressionStatus: Object.entries(opts?.visible || {}).reduce(
        (acc, [key, value]) => {
          if (key === 'Subject Property' || !value) return acc;
          acc[key] = { active: value };
          return acc;
        },
        {} as Record<string, { active: boolean }>,
      ),
      combineRegression: opts?.combine,
      confidenceInterval: {
        active: opts?.confidence.enabled,
        level: opts?.confidence.value,
      },
      xTicks: xData,
      yTicks: yData,
    });

    worker.addEventListener('message', (event) => {
      if (event.data.status === 'done') {
        const {
          regressionLines,
          confidenceIntervals,
          rSquaredData,
          coefficients,
        } = event.data;

        setRegressionData({
          confidenceIntervals,
          regressionLines,
          rSquaredData,
          coefficients,
        });
      }

      worker.terminate();
    });

    return () => {
      if (worker) worker.terminate();
    };
  }, [data, xData, yData, JSON.stringify(opts), enabled]);

  return regressionData;
};

export const ScatterChart = React.memo(
  (props: {
    data?: ChartData[];
    loading: boolean;
    priceType: PriceType;
    onReady?: MixConfig['onReady'];
    regressionOpts?: RegressionLine;
  }) => {
    const { loading, onReady, regressionOpts } = props;

    const {
      min: minY,
      max: maxY,
      ticks: yTicks,
    } = useAxisHandler({ data: props.data ? props.data : [], type: 'y' });
    const {
      min: minX,
      max: maxX,
      ticks: xTicks,
    } = useAxisHandler({ data: props.data ? props.data : [], type: 'x' });

    const data = React.useMemo(() => {
      if (props?.data && props?.data?.length > 0) {
        return [
          { x: minX - 100 },
          ...(props.data ? props.data : []),
          { x: maxX + 100 },
        ];
      }
      return [];
    }, [props.data, minX, maxX]);

    const regressionData = useRegressionLine({
      data,
      xData: xTicks,
      yData: yTicks,
      opts: regressionOpts,
    });
    const confidenceIntervals = regressionData?.confidenceIntervals || [];
    const regressionLines = regressionData?.regressionLines || [];
    const rSquaredData = regressionData?.rSquaredData || [];
    const coefficients = regressionData?.coefficients || [];

    const config = useChartConfig({
      data,
      ciData: confidenceIntervals,
      minX,
      minY,
      maxX,
      maxY,
    });

    return (
      <div id="cma-comp-scatter">
        {config && (
          <Mix
            {...config}
            {...{
              ...config,
              data: [
                ...(config.data ? config.data : []),
                ...regressionLines,
                ...confidenceIntervals,
              ],
            }}
            loading={loading}
            onReady={onReady}
          />
        )}
        <div
          className={`absolute ${
            props.priceType === 'full' ? 'bottom-28' : 'top-10'
          } right-2 flex flex-col gap-2 p-1 border border-gray-300 bg-white`}
        >
          {regressionOpts?.equation.show && (
            <div className="flex flex-col gap-0.5">
              <RenderEquation
                coefficients={coefficients}
                regressionType={regressionOpts?.type}
              />
            </div>
          )}
          <span>
            <strong>
              R<sup>2</sup>:{' '}
            </strong>
            {rSquaredData.map((item, index) => (
              <span key={index}>
                {item.type}(<strong>{item.rSquared}</strong>){' '}
              </span>
            ))}
          </span>
        </div>
      </div>
    );
  },
  (prev, next) => {
    return isEqual(prev, next);
  },
);

export const ScatterLegend = (opts: { stats: Record<string, number> }) => {
  const {
    sourceOptions,
    regressionLine,
    setRegressionLine,
    removedPropertyIds,
    setRemovedPropertyIds,
  } = useCompScatter();

  const propertyRemoved = React.useMemo(() => {
    return Object.entries(removedPropertyIds).find(
      ([key, value]) => value.length > 0,
    )
      ? true
      : false;
  }, [removedPropertyIds]);

  const onReset = React.useCallback(() => {
    setRemovedPropertyIds(DEFAULT_REMOVED_PROPERTY_IDS);
  }, []);

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'row',
        gap: '10px',
        justifyContent: 'center',
      }}
    >
      {sourceOptions.reduce((acc, option) => {
        const count = opts.stats[option];
        if (count === undefined) return acc;
        acc.push(
          <span
            key={option}
            style={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              gap: '3px',
              fontSize: '12px',
              color: '#333',
            }}
          >
            {option === 'Subject Property' && (
              <BsHexagonFill fill={color[option]} />
            )}
            {[
              'MLS',
              'Resale',
              'New Construction',
              'Last Sale Public Record',
            ].includes(option) && <BsCircleFill fill={color[option]} />}
            {option === 'SFR Operators' && (
              <BsTriangleFill fill={color[option]} />
            )}
            {option === '3rd Party' && <BsSquareFill fill={color[option]} />}

            <span>
              {option} <strong>({count})</strong>
            </span>
            <span style={{ fontSize: '20px', cursor: 'pointer' }}>
              {option !== 'Subject Property' && (
                <BiLineChart
                  fill={regressionLine.visible[option] ? color[option] : '#ccc'}
                  onClick={() => {
                    setRegressionLine((prev) => ({
                      ...prev,
                      visible: {
                        ...prev.visible,
                        [option]: !prev.visible[option],
                      },
                    }));
                  }}
                />
              )}
            </span>
          </span>,
        );
        return acc;
      }, [] as React.ReactElement[])}

      {propertyRemoved && (
        <Button size="small" type="link" onClick={() => onReset()}>
          Reset
        </Button>
      )}
    </div>
  );
};
