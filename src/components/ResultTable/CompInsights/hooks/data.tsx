import {
  getMLSPropertiesWithinPolygonsData,
  getMLSPropertiesWithinRadiusData,
} from '@/services/data';
// @ts-ignore
import { processMLSProperties } from '@/utils/processAPIResponses';
import moment from 'moment';
import React from 'react';
import { useQuery } from 'react-query';
import { useSelector } from 'umi';
import { useCompScatter } from '../components/provider';
import {
  HotpadsProperty,
  LastSalePublicRecordProperty,
  MLSProperty,
  SFRProperty,
  SubjectProperty,
} from '../lib/types';
import { convertToChartData, filterData } from '../lib/utils';

const useCompIsLoading = () => {
  const {
    searchingMode,
    fetchCompMLSDone,
    fetchCompSFRDone,
    fetchCompHotPadsDone,
  } = useSelector(({ CMA }: any) => ({
    searchingMode: CMA.searchingMode,
    fetchCompMLSDone: CMA.fetchCompMLSDone,
    fetchCompSFRDone: CMA.fetchCompSFRDone,
    fetchCompHotPadsDone: CMA.fetchCompHotPadsDone,
  }));

  return searchingMode === 'Lease'
    ? !fetchCompMLSDone || !fetchCompSFRDone || !fetchCompHotPadsDone
    : !fetchCompMLSDone;
};

const useSubjectPropertyData = (props: {
  enabled?: boolean;
}): SubjectProperty | undefined => {
  const { currentPropertyAddress, subjectPropertyParcelData, marketRentValue } =
    useSelector((state: any) => state.CMA);

  if (!props.enabled) return;
  return {
    ...currentPropertyAddress,
    ...subjectPropertyParcelData,
    rent: marketRentValue,
    sales: marketRentValue,
  };
};

const useSFRData = (props: {
  syncWithComps: boolean;
  enabled?: boolean;
}): Array<SFRProperty> | undefined => {
  const { syncWithComps } = props;
  const compIsLoading = useCompIsLoading();
  const {
    compingMode,
    searchingMode,
    selectedRowKeysNationalOperators,
    currentNationalOperatorsProperties,
    currentNationalOperatorsPropertiesFiltered,
  } = useSelector((state: any) => state.CMA);

  if (!props.enabled) return;
  if (compIsLoading || searchingMode === 'Sale') return [];
  if (compingMode === 'intelligentComping')
    return currentNationalOperatorsPropertiesFiltered;
  if (syncWithComps) {
    return currentNationalOperatorsPropertiesFiltered.filter((d: any) =>
      selectedRowKeysNationalOperators.includes(d.base_id),
    );
  }

  const sfrData = filterData(currentNationalOperatorsProperties, 'SFR');
  const unselectedKeys = currentNationalOperatorsPropertiesFiltered
    .map((d: any) => d.base_id)
    .filter((id: string) => !selectedRowKeysNationalOperators.includes(id));
  return sfrData.filter(
    (property) => !unselectedKeys.includes(property.base_id),
  );
};

const useHotPadsData = (props: {
  syncWithComps: boolean;
  enabled?: boolean;
}): Array<HotpadsProperty> | undefined => {
  const { syncWithComps } = props;
  const compIsLoading = useCompIsLoading();
  const {
    compingMode,
    searchingMode,
    currentHotPadsProperties,
    currentHotPadsPropertiesFiltered,
    selectedRowKeysHotPads,
  } = useSelector((state: any) => state.CMA);

  if (!props.enabled) return;
  if (compIsLoading || searchingMode === 'Sale') return [];
  if (compingMode === 'intelligentComping')
    return currentHotPadsPropertiesFiltered;
  if (syncWithComps) {
    return currentHotPadsPropertiesFiltered.filter((d: any) =>
      selectedRowKeysHotPads.includes(d.base_id),
    );
  }

  const hotpadsData = filterData(currentHotPadsProperties, 'SFR');
  const unselectedKeys = currentHotPadsPropertiesFiltered
    .map((d: any) => d.base_id)
    .filter((id: string) => !selectedRowKeysHotPads.includes(id));
  return hotpadsData.filter(
    (property) => !unselectedKeys.includes(property.base_id),
  );
};

const useMLSData = (props: {
  syncWithComps: boolean;
  enabled?: boolean;
}): Array<MLSProperty> | undefined => {
  const { syncWithComps } = props;
  const compIsLoading = useCompIsLoading();
  const {
    compingMode,
    searchingMode,
    currentMLSProperties,
    currentMLSPropertiesFiltered,
    selectedRowKeysMLSLease,
    selectedRowKeysMLSSale,
  } = useSelector((state: any) => state.CMA);

  if (!props.enabled) return;
  if (compIsLoading) return [];
  if (compingMode === 'intelligentComping') return currentMLSPropertiesFiltered;
  if (syncWithComps) {
    return currentMLSPropertiesFiltered.filter((d: any) =>
      searchingMode === 'Lease'
        ? selectedRowKeysMLSLease.includes(d.mlsid)
        : selectedRowKeysMLSSale.includes(d.mlsid),
    );
  }

  const mlsData = filterData(currentMLSProperties, 'MLS');
  return mlsData;
};

const useNewOrExistingMLSData = (props: {
  syncWithComps: boolean;
  constructionType: 'NEW' | 'EXISTING';
  enabled?: boolean;
}): Array<MLSProperty> | undefined => {
  const { syncWithComps } = props;
  const {
    searchingMode,
    eventCoordinates,
    currentRadiusMile,
    currentStatusMLS,
    currentStartMLS,
    currentEndMLS,
    drawnCustomPolygons,
    compingMode,
    selectedRowKeysMLSLease,
    selectedRowKeysMLSSale,
    selectedUserGroup,
  } = useSelector((state: any) => state.CMA);
  const { data: currentMLSProperties, isFetching } = useQuery(
    `CompGraphMLS-${JSON.stringify([
      searchingMode,
      currentStatusMLS,
      currentStartMLS,
      currentEndMLS,
      eventCoordinates,
      currentRadiusMile,
      props.constructionType,
      props.enabled,
    ])}`,
    async () => {
      let response = null;

      if (eventCoordinates.length === 2 && currentRadiusMile) {
        response = await getMLSPropertiesWithinRadiusData({
          status: currentStatusMLS,
          propertyType:
            searchingMode === 'Lease' ? 'Residential Lease' : 'Residential',
          startDate: moment(currentStartMLS).format('YYYY-MM-DD'),
          endDate: moment(currentEndMLS).format('YYYY-MM-DD'),
          lng: eventCoordinates[0],
          lat: eventCoordinates[1],
          distance: currentRadiusMile * 1609.34,
          constructionType: props.constructionType,
        });
      } else if (drawnCustomPolygons.length > 0) {
        response = await getMLSPropertiesWithinPolygonsData({
          status: currentStatusMLS,
          propertyType:
            searchingMode === 'Lease' ? 'Residential Lease' : 'Residential',
          startDate: moment(currentStartMLS).format('YYYY-MM-DD'),
          endDate: moment(currentEndMLS).format('YYYY-MM-DD'),
          body: drawnCustomPolygons,
          distance: currentRadiusMile * 1609.34,
          constructionType: props.constructionType,
        });
      }

      return processMLSProperties({ response, selectedUserGroup });
    },
    {
      enabled:
        props.enabled &&
        ((eventCoordinates.length === 2 && currentRadiusMile) ||
          drawnCustomPolygons.length > 0)
          ? true
          : false,
    },
  );

  if (!props.enabled) return;
  if (isFetching || compingMode === 'intelligentComping') return [];

  const mlsData = filterData(currentMLSProperties, 'MLS');
  if (syncWithComps) {
    return mlsData.filter((d: any) =>
      searchingMode === 'Lease'
        ? selectedRowKeysMLSLease.includes(d.mlsid)
        : selectedRowKeysMLSSale.includes(d.mlsid),
    );
  }
  return mlsData;
};

const useLastSalePublicRecordData = (props: {
  enabled?: boolean;
}): Array<LastSalePublicRecordProperty> | undefined => {
  const { lastSalePublicRecordDataForRender } = useSelector(
    (state: any) => state.CMA,
  );

  if (!props.enabled) return;
  return lastSalePublicRecordDataForRender;
};

export const useCompScatterData = (props: { enabled?: boolean }) => {
  const {
    syncComp,
    searchingMode,
    sourceOptions,
    propertyFilter,
    priceType,
    removedPropertyIds,
  } = useCompScatter();
  const compIsLoading = useCompIsLoading();
  const subjectPropertyData = useSubjectPropertyData({
    enabled: props.enabled && sourceOptions.includes('Subject Property'),
  });
  const sfrData = useSFRData({
    syncWithComps: syncComp,
    enabled:
      props.enabled &&
      searchingMode === 'Lease' &&
      sourceOptions.includes('SFR Operators'),
  });
  const hotPadsData = useHotPadsData({
    syncWithComps: syncComp,
    enabled:
      props.enabled &&
      searchingMode === 'Lease' &&
      sourceOptions.includes('3rd Party'),
  });
  const mlsData = useMLSData({
    syncWithComps: syncComp,
    enabled: props.enabled && sourceOptions.includes('MLS'),
  });
  const resaleData = useNewOrExistingMLSData({
    syncWithComps: syncComp,
    constructionType: 'EXISTING',
    enabled:
      props.enabled &&
      searchingMode === 'Sale' &&
      sourceOptions.includes('Resale'),
  });
  const newConstructionData = useNewOrExistingMLSData({
    syncWithComps: syncComp,
    constructionType: 'NEW',
    enabled:
      props.enabled &&
      searchingMode === 'Sale' &&
      sourceOptions.includes('New Construction'),
  });
  const lastSalePublicRecordData = useLastSalePublicRecordData({
    enabled:
      props.enabled &&
      searchingMode === 'Sale' &&
      sourceOptions.includes('Last Sale Public Record'),
  });

  console.log('CompScatter subjectPropertyData', subjectPropertyData);
  console.log('CompScatter sfrData', sfrData);
  console.log('CompScatter hotPadsData', hotPadsData);
  console.log('CompScatter mlsData', mlsData);
  console.log('CompScatter resaleData', resaleData);
  console.log('CompScatter newConstructionData', newConstructionData);
  console.log('CompScatter lastSalePublicRecordData', lastSalePublicRecordData);

  const data = React.useMemo(() => {
    const res = [];
    const stats = {} as Record<string, number>;
    const { shouldFilter, filterFn } = propertyFilter;

    if (
      (subjectPropertyData &&
        shouldFilter &&
        filterFn('Subject Property', subjectPropertyData)) ||
      subjectPropertyData
    ) {
      const data = convertToChartData('Subject Property', subjectPropertyData, {
        searchingMode,
        priceType,
      });
      if (data) res.push(data);
      stats['Subject Property'] = 1;
    }

    if (sfrData) {
      const data = shouldFilter
        ? sfrData.filter((d) => filterFn('SFR Operators', d))
        : sfrData;
      const chartData = data
        .map((d) =>
          convertToChartData('SFR Operators', d, {
            searchingMode,
            priceType,
          }),
        )
        .filter(
          (d) =>
            d &&
            !removedPropertyIds['SFR Operators'].includes(
              (d.property as SFRProperty).base_id,
            ),
        );
      res.push(...chartData);
      stats['SFR Operators'] = chartData.length;
    }

    if (hotPadsData) {
      const data = shouldFilter
        ? hotPadsData.filter((d) => filterFn('3rd Party', d))
        : hotPadsData;
      const chartData = data
        .map((d) =>
          convertToChartData('3rd Party', d, { searchingMode, priceType }),
        )
        .filter(
          (d) =>
            d &&
            !removedPropertyIds['3rd Party'].includes(
              (d.property as HotpadsProperty).base_id,
            ),
        );
      res.push(...chartData);
      stats['3rd Party'] = chartData.length;
    }

    if (mlsData) {
      const data = shouldFilter
        ? mlsData.filter((d) => filterFn('MLS', d))
        : mlsData;
      const chartData = data
        .map((d) => convertToChartData('MLS', d, { searchingMode, priceType }))
        .filter(
          (d) =>
            d &&
            !removedPropertyIds['MLS'].includes(
              (d.property as MLSProperty).mlsid,
            ),
        );
      res.push(...chartData);
      stats['MLS'] = chartData.length;
    }

    if (resaleData) {
      const data = shouldFilter
        ? resaleData.filter((d) => filterFn('Resale', d))
        : resaleData;
      const chartData = data
        .map((d) =>
          convertToChartData('Resale', d, { searchingMode, priceType }),
        )
        .filter(
          (d) =>
            d &&
            !removedPropertyIds['Resale'].includes(
              (d.property as MLSProperty).mlsid,
            ),
        );
      res.push(...chartData);
      stats['Resale'] = chartData.length;
    }

    if (newConstructionData) {
      const data = shouldFilter
        ? newConstructionData.filter((d) => filterFn('New Construction', d))
        : newConstructionData;
      const chartData = data
        .map((d) =>
          convertToChartData('New Construction', d, {
            searchingMode,
            priceType,
          }),
        )
        .filter(
          (d) =>
            d &&
            !removedPropertyIds['New Construction'].includes(
              (d.property as MLSProperty).mlsid,
            ),
        );
      res.push(...chartData);
      stats['New Construction'] = chartData.length;
    }

    if (lastSalePublicRecordData) {
      const data = shouldFilter
        ? lastSalePublicRecordData.filter((d) =>
            filterFn('Last Sale Public Record', d),
          )
        : lastSalePublicRecordData;
      const chartData = data
        .map((d) =>
          convertToChartData('Last Sale Public Record', d, {
            searchingMode,
            priceType,
          }),
        )
        .filter(
          (d) =>
            d &&
            !removedPropertyIds['Last Sale Public Record'].includes(
              (d.property as LastSalePublicRecordProperty).key,
            ),
        );
      res.push(...chartData);
      stats['Last Sale Public Record'] = chartData.length;
    }

    return { data: { chartData: res, stats: stats }, isLoading: compIsLoading };
  }, [
    subjectPropertyData,
    sfrData,
    hotPadsData,
    mlsData,
    resaleData,
    newConstructionData,
    lastSalePublicRecordData,
    propertyFilter,
    searchingMode,
    priceType,
  ]);

  return data;
};
