import React from 'react';
import { useSelector } from 'umi';
import { propertyFieldMap } from '../lib/constants';
import {
  HotpadsProperty,
  LastSalePublicRecordProperty,
  MLSProperty,
  SFRProperty,
  SubjectProperty,
} from '../lib/types';

const INIT_FILTER_RANGE = {
  bed: [0, 0],
  bath: [0, 0],
  sqft: [0, 0],
  yearBuilt: [0, 0],
};

const currentYear = new Date().getFullYear();

export const useFilterRange = () => {
  const {
    currentPropertyAddress,
    subjectPropertyParcelData,
    currentNationalOperatorsProperties,
    currentHotPadsProperties,
    currentMLSProperties,
    lastSalePublicRecordDataForRender,
  } = useSelector((state: any) => state.CMA);
  const [filterRange, setFilterRange] = React.useState(INIT_FILTER_RANGE);

  React.useEffect(() => {
    const data = {
      'Subject Property': {
        ...subjectPropertyParcelData,
        ...currentPropertyAddress,
      } as SubjectProperty,
      'SFR Operators': currentNationalOperatorsProperties as Array<SFRProperty>,
      '3rd Party': currentHotPadsProperties as Array<HotpadsProperty>,
      MLS: currentMLSProperties as Array<MLSProperty>,
      lspr: lastSalePublicRecordDataForRender as Array<LastSalePublicRecordProperty>,
    };

    const newFilterRange = Object.entries(data).reduce((acc, [key, value]) => {
      if (!value) return acc;

      const map = propertyFieldMap[key as keyof typeof propertyFieldMap];
      if (!map) return acc;

      let range = INIT_FILTER_RANGE;

      if (Array.isArray(value)) {
        range = value.reduce((acc, item) => {
          if (!item) return acc;

          const bed = item[`${map.bed}` as keyof typeof item] as number;
          const bath = item[`${map.bath}` as keyof typeof item] as number;
          const sqft = item[`${map.sqft}` as keyof typeof item] as number;
          const yearBuilt = item[
            `${map.yearBuilt}` as keyof typeof item
          ] as number;

          if (bed && bed > acc.bed[1]) acc.bed[1] = bed;
          if (bath && bath > acc.bath[1]) acc.bath[1] = bath;
          if (sqft && sqft > acc.sqft[1]) acc.sqft[1] = sqft;
          if (
            acc.yearBuilt[0] === 0 ||
            (yearBuilt && yearBuilt < acc.yearBuilt[0])
          )
            acc.yearBuilt[0] = yearBuilt;
          if (
            yearBuilt &&
            yearBuilt > acc.yearBuilt[1] &&
            yearBuilt <= currentYear
          )
            acc.yearBuilt[1] = yearBuilt;

          return acc;
        }, INIT_FILTER_RANGE);
      } else {
        const bed = value[`${map.bed}` as keyof typeof value] as number;
        const bath = value[`${map.bath}` as keyof typeof value] as number;
        const sqft = value[`${map.sqft}` as keyof typeof value] as number;
        const yearBuilt = value[
          `${map.yearBuilt}` as keyof typeof value
        ] as number;

        if (bed && bed > range.bed[1]) range.bed[1] = bed;
        if (bath && bath > range.bath[1]) range.bath[1] = bath;
        if (sqft && sqft > range.sqft[1]) range.sqft[1] = sqft;
        if (
          range.yearBuilt[0] === 0 ||
          (yearBuilt && yearBuilt < range.yearBuilt[0])
        )
          range.yearBuilt[0] = yearBuilt;
        if (
          yearBuilt &&
          yearBuilt > range.yearBuilt[1] &&
          yearBuilt <= currentYear
        )
          range.yearBuilt[1] = yearBuilt;
      }

      return { ...acc, ...range };
    }, INIT_FILTER_RANGE);

    setFilterRange(newFilterRange);
  }, [
    currentPropertyAddress,
    subjectPropertyParcelData,
    currentNationalOperatorsProperties,
    currentHotPadsProperties,
    currentMLSProperties,
    lastSalePublicRecordDataForRender,
  ]);

  return filterRange;
};
