export const generateManualFilterValues = (dataSourceType, state) => {
  const baseFilters = [
    {
      key: dataSourceType === 'MLS' ? 'bed' : 'bed_rooms',
      // name: 'Beds',
      min: state.minBeds,
      max: state.maxBeds,
      relation: state.relationBeds,
    },
    {
      key: dataSourceType === 'MLS' ? 'bath' : 'bath_rooms',
      // name: 'Baths',
      min: state.minBaths,
      max: state.maxBaths,
      relation: state.relationBaths,
    },
    {
      key: dataSourceType === 'MLS' ? 'size' : 'square_feet',
      // name: 'Sqft',
      min: state.minSqft,
      max: state.maxSqft,
      relation: state.relationSqft,
    },
    {
      key: 'area_acres',
      // name: 'Lot Size',
      min: state.minLotSize,
      max: state.maxLotSize,
      relation: state.relationLotSize,
    },
    {
      key: 'yearbuilt',
      // name: 'Year Built',
      min: state.minYearBuilt,
      max: state.maxYearBuilt,
      relation: state.relationYearBuilt,
    },
    {
      key: 'cdom',
      // name: 'Days on Market',
      min: state.minCumulativeDaysOnMarket,
      max: state.maxCumulativeDaysOnMarket,
      relation: state.relationCumulativeDaysOnMarket,
    },
    {
      key: 'garage',
      min: state.minCoveredParking,
      max: state.maxCoveredParking,
      relation: state.relationCoveredParking,
    },
    {
      key: 'pool',
      selected: state.selectedPoolAllowed,
    },
  ];

  // if (dataSourceType === 'MLS') {
  //   baseFilters.push({
  //     key: 'propertysubtype',
  //     checked: state.checkedPropertySubTypes,
  //   });
  // }
  return baseFilters;
};

export const showSFROperatorsFullNameOnly = (brand) => {
  brand = brand.replace(/ /g, ''); // remove whitespace

  switch (brand) {
    case 'AH4R':
      return 'American Homes 4 Rent';
    case 'Invitatio':
      return 'Invitation Homes';
    case 'HRG':
      return 'HomeRiver Group';
    case 'PR':
      return 'Progress Residential';
    case 'CPM':
      return 'Conrex Property Management';
    case 'TR':
      return 'Tricon Residential';
    case 'MYND':
      return 'MYND';
    case 'KP':
      return 'Kinloch Partners';
    case 'RW':
      return 'Renters Warehouse';
    case 'VH':
      return 'Vinebrook Homes';
    case 'Amhrest':
      return 'Amherst';
    case 'ARG':
      return 'ARG';
    case 'Brandywine':
      return 'Brandywine';
    case 'BridgeHome':
      return 'Bridge Home';
    case 'Camillo':
      return 'Camillo';
    case 'Copperbay':
      return 'Copperbay';
    case 'Divvy':
      return 'Divvy';
    case 'FirstKey':
      return 'FirstKey';
    case 'Hudson':
      return 'Hudson';
    case 'Imagine':
      return 'Imagine';
    case 'KairosLiving':
      return 'Kairos';
    case 'KrchRealty':
      return 'Krch Realty';
    case 'LiveReszi':
      return 'Live Reszi';
    case 'OpenHouse':
      return 'Open House';
    case 'Pathway':
      return 'Pathway Homes';
    case 'Peak':
      return 'Peak';
    case 'PPMG':
      return 'PPMG';
    case 'Propify':
      return 'National Home Rental';
    case 'RENU':
      return 'RENU';
    case 'ResiHome':
      return 'ResiHome';
    case 'SPA':
      return 'Sparrow';
    case 'Streetlane':
      return 'Streetlane';
    case 'SYLV':
      return 'Sylvan';
  }
};
