import React from 'react';
import { useSelector } from 'umi';
import { Scatter<PERSON><PERSON>, ScatterLegend } from './components/chart';
import { CompScatterContainer } from './components/layout';
import {
  DataModifiers,
  InfoModifiers,
  PropertyModifier,
} from './components/modifiers';
import { CompScatterProvider, useCompScatter } from './components/provider';
import { useCompScatterData } from './hooks/data';
import { ChartData } from './lib/types';

export const Component = () => {
  const { eventCoordinates, drawnCustomPolygons } = useSelector(
    ({ CMA }: any) => ({
      eventCoordinates: CMA.eventCoordinates,
      drawnCustomPolygons: CMA.drawnCustomPolygons,
    }),
  );
  const active = eventCoordinates.length > 0 || drawnCustomPolygons.length > 0;

  const {
    data: { chartData, stats },
    isLoading,
  } = useCompScatterData({ enabled: active });
  const { regressionLine, setSelectedChartData, priceType } = useCompScatter();

  React.useEffect(() => {
    const removeSelectedChartData = (e: Event) => {
      if (
        document.getElementById('cma-comp-scatter')?.contains(e.target as Node)
      )
        return;

      setSelectedChartData((prev) => {
        if (prev) return undefined;
        return prev;
      });
    };

    window.addEventListener('click', removeSelectedChartData);
    return () => {
      window.removeEventListener('click', removeSelectedChartData);
    };
  }, []);

  const onReady = React.useCallback((plot: any) => {
    const click = (e: any) => {
      if (e.data && e.data.data) {
        setSelectedChartData(e.data.data);
      }
    };

    plot.on('plot:click', click);
  }, []);

  return (
    <CompScatterContainer>
      <DataModifiers active={active} />
      {active && (
        <div className="relative">
          <InfoModifiers />
          <ScatterChart
            data={chartData as ChartData[]}
            loading={isLoading}
            priceType={priceType}
            onReady={onReady}
            regressionOpts={regressionLine}
          />
          <ScatterLegend stats={stats} />
          <PropertyModifier />
        </div>
      )}
    </CompScatterContainer>
  );
};

export const CompInsights = () => {
  const searchingMode = useSelector((state: any) => state.CMA.searchingMode);
  return (
    <CompScatterProvider
      searchingMode={searchingMode as 'Lease' | 'Sale' | 'Land'}
    >
      <Component />
    </CompScatterProvider>
  );
};
