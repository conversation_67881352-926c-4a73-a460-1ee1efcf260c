import { DataSource } from '../components/provider';
import { PropertyData } from './types';

export const DEFAULT_PRICE_TYPE = 'full' as 'full' | 'psf';
export const DEFAULT_REGRESSION_LINE = {
  type: 'logarithmic' as 'linear' | 'logarithmic' | 'power',
  combine: true,
  equation: {
    show: false,
    value: null as string | null,
  },
  confidence: {
    enabled: false,
    value: 95 as 90 | 95 | 99,
  },
  visible: {
    'SFR Operators': true,
    '3rd Party': true,
    MLS: true,
    'Last Sale Public Record': true,
    Resale: true,
    'New Construction': true,
  },
};
export const DEFAULT_SYNC_COMP = false as boolean;
export const DATA_SOURCE_LIST = [
  'Subject Property',
  'SFR Operators',
  '3rd Party',
  'MLS',
  'Last Sale Public Record',
  'Resale',
  'New Construction',
];
export const DEFAULT_PROPERTY_FILTER = {
  shouldFilter: false,
  filterFn: (source: DataSource, data?: PropertyData) => true,
};

export const DEFAULT_REMOVED_PROPERTY_IDS = {
  'SFR Operators': [],
  '3rd Party': [],
  MLS: [],
  'Last Sale Public Record': [],
  Resale: [],
  'New Construction': [],
};

const mlsMap = {
  id: 'mlsid',
  bed: 'bed',
  bath: 'bath',
  sqft: 'size',
  yearBuilt: 'yearbuilt',
};

export const propertyFieldMap = {
  'Subject Property': {
    id: 'apn',
    bed: 'beds_count',
    bath: 'baths',
    sqft: 'total_area_sq_ft',
    yearBuilt: 'year_built',
  },
  MLS: mlsMap,
  Resale: mlsMap,
  'New Construction': mlsMap,
  'SFR Operators': {
    id: 'base_id',
    bed: 'bed_rooms',
    bath: 'bath_rooms',
    sqft: 'square_feet',
    yearBuilt: 'yearbuilt',
  },
  '3rd Party': {
    id: 'base_id',
    bed: 'bed_rooms',
    bath: 'bath_rooms',
    sqft: 'square_feet',
    yearBuilt: 'yearbuilt',
  },
  'Last Sale Public Record': {
    id: 'key',
    bed: 'bedroom',
    bath: 'bathroom',
    sqft: 'sqft',
    yearBuilt: 'year_built',
  },
};

export const color = {
  'Subject Property': '#e200ff',
  MLS: '#000000',
  'SFR Operators': '#2394d2',
  '3rd Party': '#ed6041',
  'Last Sale Public Record': '#39a39c',
  Resale: '#f7c244',
  'New Construction': '#5be043',
};
export const shape = {
  'Subject Property': 'hexagon',
  MLS: 'circle',
  'SFR Operators': 'triangle',
  '3rd Party': 'square',
  'Last Sale Public Record': 'circle',
  Resale: 'circle',
  'New Construction': 'circle',
};
