import { DataSource, PriceType, SearchingMode } from '../components/provider';
//  prettier-ignore
import {
filterTableDataSource,generateManualFilterExpression
//@ts-ignore
} from '@/components/Filters/filterFunctions.js';
import filterValuesDefault from '@/components/Filters/filterValuesDefault.json';
import { color, propertyFieldMap, shape } from './constants';
import {
  ChartData,
  HotpadsProperty,
  LastSalePublicRecordProperty,
  MLSProperty,
  PropertyData,
  PropertyFilterValue,
  SFRProperty,
  SubjectProperty,
} from './types';

export const generateManualFilterValues = (
  dataSourceType: 'MLS' | 'SFR',
  state: Record<string, any>,
) => {
  const baseFilters = [
    {
      key: dataSourceType === 'MLS' ? 'bed' : 'bed_rooms',
      // name: 'Beds',
      min: state.minBeds,
      max: state.maxBeds,
      relation: state.relationBeds,
    },
    {
      key: dataSourceType === 'MLS' ? 'bath' : 'bath_rooms',
      // name: 'Baths',
      min: state.minBaths,
      max: state.maxBaths,
      relation: state.relationBaths,
    },
    {
      key: dataSourceType === 'MLS' ? 'size' : 'square_feet',
      // name: 'Sqft',
      min: state.minSqft,
      max: state.maxSqft,
      relation: state.relationSqft,
    },
    {
      key: 'yearbuilt',
      // name: 'Year Built',
      min: state.minYearBuilt,
      max: state.maxYearBuilt,
      relation: state.relationYearBuilt,
    },
    {
      key: 'cdom',
      // name: 'Days on Market',
      min: state.minCumulativeDaysOnMarket,
      max: state.maxCumulativeDaysOnMarket,
      relation: state.relationCumulativeDaysOnMarket,
    },
    {
      key: 'garage',
      min: state.minCoveredParking,
      max: state.maxCoveredParking,
      relation: state.relationCoveredParking,
    },
    {
      key: 'pool',
      selected: state.selectedPoolAllowed,
    },
  ];

  return baseFilters;
};

export const showSFROperatorsFullNameOnly = (brand: string) => {
  brand = brand.replace(/ /g, ''); // remove whitespace

  switch (brand) {
    case 'AH4R':
      return 'American Homes 4 Rent';
    case 'Invitatio':
      return 'Invitation Homes';
    case 'HRG':
      return 'HomeRiver Group';
    case 'PR':
      return 'Progress Residential';
    case 'CPM':
      return 'Conrex Property Management';
    case 'TR':
      return 'Tricon Residential';
    case 'MYND':
      return 'MYND';
    case 'KP':
      return 'Kinloch Partners';
    case 'RW':
      return 'Renters Warehouse';
    case 'VH':
      return 'Vinebrook Homes';
    case 'Amhrest':
      return 'Amherst';
    case 'ARG':
      return 'ARG';
    case 'Brandywine':
      return 'Brandywine';
    case 'BridgeHome':
      return 'Bridge Home';
    case 'Camillo':
      return 'Camillo';
    case 'Copperbay':
      return 'Copperbay';
    case 'Divvy':
      return 'Divvy';
    case 'FirstKey':
      return 'FirstKey';
    case 'Hudson':
      return 'Hudson';
    case 'Imagine':
      return 'Imagine';
    case 'KairosLiving':
      return 'Kairos';
    case 'KrchRealty':
      return 'Krch Realty';
    case 'LiveReszi':
      return 'Live Reszi';
    case 'OpenHouse':
      return 'Open House';
    case 'Pathway':
      return 'Pathway Homes';
    case 'Peak':
      return 'Peak';
    case 'PPMG':
      return 'PPMG';
    case 'Propify':
      return 'National Home Rental';
    case 'RENU':
      return 'RENU';
    case 'ResiHome':
      return 'ResiHome';
    case 'SPA':
      return 'Sparrow';
    case 'Streetlane':
      return 'Streetlane';
    case 'SYLV':
      return 'Sylvan';
  }
};

export function validateDataSource(
  sourceOptions: DataSource[],
  searchingMode: SearchingMode,
) {
  if (searchingMode === 'Lease') {
    return sourceOptions
      .filter((option) =>
        ['Subject Property', 'SFR Operators', '3rd Party', 'MLS'].includes(
          option,
        ),
      )
      .sort((a, b) => {
        if (a === 'Subject Property') return -1;
        return a.length - b.length;
      });
    // } else if (searchingMode === 'Sale') {
  } else {
    return sourceOptions.filter((option) =>
      [
        'Subject Property',
        'Last Sale Public Record',
        'MLS',
        'Resale',
        'New Construction',
      ].includes(option),
    );
  }
}

export const filterData = (data: any[], type: 'MLS' | 'SFR') => {
  let filteredData = [];
  if (type === 'MLS') {
    const mlsFilterExpression = generateManualFilterExpression(
      generateManualFilterValues('MLS', filterValuesDefault),
    );
    filteredData = filterTableDataSource(mlsFilterExpression, data);
  } else if (type === 'SFR') {
    const sfrFilterExpression = generateManualFilterExpression(
      generateManualFilterValues('SFR', filterValuesDefault),
    );
    filteredData = filterTableDataSource(sfrFilterExpression, data);
  }
  return filteredData as any[];
};

export const basePropertyFilterFn = (
  filter: PropertyFilterValue,
  source: DataSource,
  data: PropertyData,
) => {
  const filterMap = propertyFieldMap[source as keyof typeof propertyFieldMap];
  if (!filterMap) return false;

  const bed = data[`${filterMap.bed}` as keyof typeof data] as number;
  const bath = data[`${filterMap.bath}` as keyof typeof data] as number;
  const sqft = data[`${filterMap.sqft}` as keyof typeof data] as number;
  const yearBuilt = data[
    `${filterMap.yearBuilt}` as keyof typeof data
  ] as number;

  if (!bed || bed < filter.bed[0] || bed > filter.bed[1]) return false;
  if (!bath || bath < filter.bath[0] || bath > filter.bath[1]) return false;
  if (!sqft || sqft < filter.sqft[0] || sqft > filter.sqft[1]) return false;
  if (
    !yearBuilt ||
    yearBuilt < filter.yearBuilt[0] ||
    yearBuilt > filter.yearBuilt[1]
  )
    return false;

  return true;
};

export const convertToChartData = (
  source: DataSource,
  data: PropertyData,
  opts: { searchingMode: SearchingMode; priceType: PriceType },
) => {
  const { searchingMode, priceType } = opts;
  let yAlias;
  if (priceType === 'full') {
    yAlias = searchingMode === 'Lease' ? 'Rent' : 'Price';
  } else {
    yAlias = searchingMode === 'Lease' ? 'Rent/Sqft' : 'Price/Sqft';
  }

  if (source === 'Subject Property') {
    data = data as SubjectProperty;
    if (
      ((searchingMode === 'Lease' && data.rent) ||
        (searchingMode === 'Sale' && data.sales)) &&
      data.total_area_sq_ft
    ) {
      const x = data.total_area_sq_ft;
      let y = null;
      if (searchingMode === 'Lease') {
        y = priceType === 'full' ? data.rent : data.rent ? data.rent / x : null;
      } else {
        y =
          priceType === 'full'
            ? data.sales
            : data.sales
            ? data.sales / x
            : null;
      }

      return {
        x: x,
        y: y,
        yAlias: yAlias,
        type: 'Subject Property',
        property: data,
        shape: shape[source],
        color: color['Subject Property'],
      };
    }
  } else if (['MLS', 'Resale', 'New Construction'].includes(source)) {
    data = data as MLSProperty;
    const x = data.size;
    const y =
      priceType === 'full'
        ? data.latestPrice
        : data.latestPrice && x
        ? data.latestPrice / x
        : null;

    if (x && y) {
      return {
        x: x,
        y: y,
        yAlias: yAlias,
        type: source,
        property: data,
        shape: shape[source],
        color: color[source],
      };
    }
  } else if (source === 'Last Sale Public Record') {
    data = data as LastSalePublicRecordProperty;
    const x = data.sqft;
    const y =
      priceType === 'full'
        ? data.last_sale_price
        : data.last_sale_price && x
        ? data.last_sale_price / x
        : null;

    if (x && y) {
      return {
        x: x,
        y: y,
        yAlias: yAlias,
        type: `Last Sale Public Record`,
        property: data,
        shape: shape[source],
        color: color['Last Sale Public Record'],
      };
    }
  } else if (['SFR Operators', '3rd Party'].includes(source)) {
    data = data as SFRProperty | HotpadsProperty;
    const x = data.square_feet;
    const y =
      priceType === 'full' ? data.rent : data.rent && x ? data.rent / x : null;

    if (x && y) {
      return {
        x: x,
        y: y,
        yAlias: yAlias,
        type: source,
        property: data,
        shape: shape[source],
        color: color[source],
      };
    }
  }
};

export const getMaxValue = (data: any[], key: string) => {
  if (!data || data.length === 0) return 0;
  const values = data.reduce((res, item) => {
    if (item.data && item.data.length > 0) {
      res.push(item.ranges[key].max);
    }
    return res;
  }, []);
  return values.length > 0 ? Math.max(...values) : 0;
};

export const getMinValue = (data: any[], key: string) => {
  if (!data || data.length === 0) return 0;
  const values = data.reduce((res, item) => {
    if (item.data && item.data.length > 0) {
      res.push(item.ranges[key].min);
    }
    return res;
  }, []);
  return values.length > 0 ? Math.min(...values) : 0;
};

export const getPropertyAddress = (property: ChartData) => {
  if (property.type === 'Subject Property') {
    return (property.property as SubjectProperty).fullAddress;
  } else if (['MLS', 'Resale', 'New Construction'].includes(property.type)) {
    return (property.property as MLSProperty).fulladdress;
  } else if (['SFR Operators', '3rd Party'].includes(property.type)) {
    return (property.property as SFRProperty | HotpadsProperty).address;
  } else if (property.type === 'Last Sale Public Record') {
    return (property.property as LastSalePublicRecordProperty).address;
  }
};
