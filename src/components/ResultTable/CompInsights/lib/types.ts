import { DataSource } from '../components/provider';

export interface SubjectProperty {
  apn: string;
  baths: number;
  beds_count: number;
  deed_last_sale_data: string | null;
  deed_last_sale_date: string | null;
  deed_last_sale_price: number | null;
  elementary: number | null;
  fips: string;
  fiveyeargrowth: number | null;
  fld_zone: string | null;
  fullAddress: string;
  high: number | null;
  high_ratio: number | null;
  highschool: number | null;
  hoa_fees: number | null;
  income: number | null;
  incomegrowth: number | null;
  institution: string | null;
  latitude: number | null;
  legal_description: string | null;
  longitude: number | null;
  low: number | null;
  low_ratio: number | null;
  marker: string | null;
  middle: number | null;
  owner_occupied_sl: 'No' | 'Yes' | null;
  partial_baths_count: number | null;
  pbaplus: number | null;
  placekey: string | null;
  postalCode: string | null;
  rate: number | null;
  region: string | null;
  rent: number | null;
  sales: number | null;
  score_crime: number | null;
  standardized_land_use_type: string | null;
  stories: number | null;
  streetAddress: string | null;
  subdivision: string | null;
  tax_amount: number | null;
  tax_rate_percent: number | null;
  total_area_sq_ft: number | null;
  total_value: number | null;
  value: number | null;
  year_built: number | null;
}

export interface SFRProperty {
  address: string;
  available_date: string | null;
  base_id: string;
  bath_rooms: number | null;
  bed_rooms: number | null;
  brand: string;
  building_permit_id: number | string | null;
  cdom: number | null;
  close_date: string;
  deed_last_sale_date: string;
  deed_last_sale_price: string;
  description: string | null;
  distance: number | null;
  exists: boolean;
  geom: { type: 'Point'; coordinates: [number, number] };
  permit_number: number | null;
  placekey: string;
  pmt_type: number | null;
  postal_code: string | null;
  property_id: string;
  propertysubtype: string;
  rent: number | null;
  square_feet: number | null;
  standard_city: string;
  standard_state: string;
  status: string;
  yearbuilt: number | null;
}
export type HotpadsProperty = SFRProperty;

export interface MLSProperty {
  bath: number | null;
  bed: number | null;
  building_permit_id: number | null;
  cdom: number | null;
  city: string | null;
  closedate: string | null;
  closeprice: number | null;
  contractdate: string | null;
  county: string | null;
  currentprice: number | null;
  deed_last_sale_date: string | null;
  deed_last_sale_price: string | null;
  description: string | null;
  distance: number | null;
  dom: number | null;
  first_entry_timestamp: string | null;
  fulladdress: string | null;
  garage: number | null;
  geography: { type: 'Point'; coordinates: [number, number] };
  latestPrice: number | null;
  latitude: number | null;
  levels: string | null;
  listingkey: string | null;
  longitude: number | null;
  lot_size: number | null;
  metro: string | null;
  mlsid: string;
  modificationtimestamp: string | null;
  offmarketdate: string | null;
  orginalprice: number | null;
  permit_number: string | null;
  placekey: string | null;
  pmt_type: string | null;
  pool: boolean;
  propertysubtype: string | null;
  propertytype: string | null;
  sfr_owner: string | null;
  size: number | null;
  stateorprovince: string | null;
  status: string | null;
  subdivision: string | null;
  watersource: string | null;
  yearbuilt: number | null;
  zipcode: string | null;
}

export interface LastSalePublicRecordProperty {
  address: string | null;
  bathroom: number | null;
  bedroom: number | null;
  distance: number | null;
  geom: { type: 'Point'; coordinates: [number, number] };
  key: string;
  last_sale_date: string | null;
  last_sale_price: number | null;
  propertysubtype: string | null;
  sqft: number | null;
  state: string | null;
  year_built: number | null;
}

export type PropertyData =
  | SubjectProperty
  | SFRProperty
  | HotpadsProperty
  | MLSProperty
  | LastSalePublicRecordProperty;

export type PropertyFilterValue = {
  bed: number[];
  bath: number[];
  sqft: number[];
  yearBuilt: number[];
};

export type ChartData = {
  x: number | null;
  y: number | null;
  yAlias: string;
  type: DataSource;
  property: PropertyData;
  shape: string;
  color: string;
};
