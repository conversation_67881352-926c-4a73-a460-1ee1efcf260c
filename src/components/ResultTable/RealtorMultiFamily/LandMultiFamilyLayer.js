import { Layer, Source } from '@spatiallaser/map';
import isEmpty from 'lodash.isempty';
import mapboxgl from 'mapbox-gl';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import { renderToString } from 'react-dom/server';
import { useSelector } from 'umi';
import { geojsonTemplate } from '../../../constants';
import { formatCurrency } from '../../../utils/lastSalePublicRecord';
import {
  removePriceMarkers,
  setPriceMarkers,
} from '../../MapCMA/MapUtility/general';
import { formatPricePerSqftArce, testLog } from '../LandComp/utils/functions';

const sourceId = 'land-mf';
let priceMarkerLMF = {};
const zoomLevelToShowParcelAVM = 17;
const zoomLevelToShowPriceMarkers = 12;

const styles = {
  circle: {
    id: `${sourceId}LayerCircle`,
    type: 'circle',
    filter: ['!', ['has', 'point_count']],
    paint: {
      'circle-radius': 8,
      'circle-color': '#41a3e8',
      'circle-stroke-color': '#000',
      'circle-stroke-width': 1,
    },
  },
  symbol: {
    id: `${sourceId}LayerSymbol`,
    type: 'symbol',
    filter: ['!', ['has', 'point_count']],
    layout: {
      'text-field': ['concat', '$', ['get', 'currentprice']],
      'text-variable-anchor': ['center'],
      'text-justify': 'center',
      'text-radial-offset': 1,
      'text-font': ['Source Sans Pro Bold', 'Open Sans Bold'],
      'text-size': [
        'interpolate',
        ['linear'],
        ['zoom'],
        zoomLevelToShowParcelAVM,
        15,
        22,
        20,
      ],
      'icon-allow-overlap': true,
      'text-allow-overlap': true,
    },
    paint: {
      'text-color': '#fff',
      'text-halo-color': '#000',
      'text-halo-width': 20,
      'text-opacity': 0,
      'icon-opacity': 0,
    },
  },
  cluster: {
    id: `${sourceId}LayerClusters`,
    type: 'circle',
    filter: ['has', 'point_count'],
    paint: {
      'circle-color': '#A9BBC0',
      'circle-radius': [
        'interpolate',
        ['linear'],
        ['zoom'],
        0,
        ['max', ['*', ['^', ['get', 'point_count'], 0.5], 0.1], 12],
      ],
      'circle-opacity': 0.75,
      'circle-stroke-width': 1,
      'circle-stroke-color': 'rgba(255,255,255,1)',
    },
  },
  clusterSymbol: {
    id: `${sourceId}LayerClustersPointCount`,
    type: 'symbol',
    filter: ['has', 'point_count'],
    layout: {
      'text-font': ['Open Sans Bold'],
      'text-field': '{point_count}',
      'text-size': 14,
      'text-justify': 'auto',
    },
    paint: {
      'text-color': 'rgba(0,0,0,1)',
    },
  },
};

export const removePRPriceMarkers = () => {
  if (!isEmpty(priceMarkerLMF)) {
    priceMarkerLMF = removePriceMarkers(priceMarkerLMF);
  }
};

export const generatePRPriceMarkers = (map, GeoJSON) => {
  if (GeoJSON?.features?.length > 0) {
    if (!isEmpty(priceMarkerLMF)) {
      priceMarkerLMF = removePriceMarkers(priceMarkerLMF);
    }
    priceMarkerLMF = setPriceMarkers(
      map,
      GeoJSON.features,
      'id',
      'currentprice',
      sourceId,
    );
  }
};

function convertToGeoJSONFormat(listOfObjects, keys) {
  if (!listOfObjects) return [];

  const filteredData = listOfObjects
    .filter((item) => keys.includes(item.id))
    .map((property) => {
      const geom = {
        type: 'Point',
        coordinates: [property.longitude, property.latitude],
      };
      return { geom, ...property };
    });

  return filteredData.map((item) => {
    const { geom, ...properties } = item;
    return {
      type: 'Feature',
      geometry: geom,
      properties,
    };
  });
}

function PublicRecordTooltip({ feature }) {
  const properties = feature.properties;
  const pps = Number(properties.currentprice) / Number(properties.square_feet);

  return (
    <div className="bg-white p-4 text-sm z-50">
      <div>
        <strong>Address:</strong> {properties.address}, {properties.city},{' '}
        {properties.state}, {properties.postal_code}
      </div>
      <div>
        <strong>Size:</strong>{' '}
        {!properties.square_feet
          ? 'N/A'
          : new Intl.NumberFormat('en-US').format(
              Number(properties.square_feet),
            ) + ' sqft'}
      </div>
      <div>
        <strong>Bd:</strong> {properties.beds} | <strong>Baths:</strong>{' '}
        {properties.baths}
      </div>
      <div>
        <strong>Price:</strong> {properties.currentprice}
      </div>
      <div>
        <strong>Price per Sqft:</strong> {formatPricePerSqftArce(pps)}
      </div>
      <div>
        <strong>Status:</strong> {properties.status}
      </div>
      <div>
        <strong>Property Type:</strong>{' '}
        {properties.propertytype ? properties.propertytype : 'N/A'}
      </div>
    </div>
  );
}

function LandMultiFamilyLayer() {
  const [geoJson, setGeoJson] = useState(geojsonTemplate);
  const map = useSelector((state) => state.CMA.map);
  const showPriceMarkers = useSelector((state) => state.CMA.showPriceMarkers);
  const currentPropertyAddress = useSelector(
    (state) => state.CMA.currentPropertyAddress,
  );
  const realtorMultiFamilyDataForRender = useSelector(
    (state) => state.CMA.realtorMultiFamilyDataForRender,
  );
  const cmaTabKey = useSelector((state) => state.CMA.cmaTabKey);
  const searchingMode = useSelector((state) => state.CMA.searchingMode);
  const realtorMultiFamilySelectedRowKey = useSelector(
    (state) => state.CMA.realtorMultiFamilySelectedRowKey,
  );
  const realtorMultiFamilyHover = useSelector(
    (state) => state.CMA.realtorMultiFamilyHover,
  );

  const showPriceMarkersRef = useRef(showPriceMarkers);
  const geoJsonRef = useRef(geoJson);
  const cmaTabKeyRef = useRef(cmaTabKey);
  const searchingModeRef = useRef(searchingMode);
  const tooltipRef = useRef(new mapboxgl.Popup({ offset: 15 }));
  const currentPropertyAddressRef = useRef(currentPropertyAddress);

  // Update refs
  showPriceMarkersRef.current = showPriceMarkers;
  geoJsonRef.current = geoJson;
  cmaTabKeyRef.current = cmaTabKey;
  searchingModeRef.current = searchingMode;
  currentPropertyAddressRef.current = currentPropertyAddress;

  const showHidePriceMarkers = useCallback(() => {
    if (
      map &&
      cmaTabKeyRef.current === '7' &&
      searchingModeRef.current === 'Lease' &&
      showPriceMarkersRef.current &&
      geoJsonRef.current?.features?.length > 0 &&
      !isEmpty(currentPropertyAddressRef.current)
    ) {
      const currentZoom = map.getZoom();
      if (currentZoom >= zoomLevelToShowPriceMarkers) {
        if (!isEmpty(priceMarkerLMF)) {
          priceMarkerLMF = removePriceMarkers(priceMarkerLMF);
        }
        priceMarkerLMF = setPriceMarkers(
          map,
          geoJsonRef.current.features,
          'id',
          'currentprice',
          sourceId,
        );
      } else {
        priceMarkerLMF = removePriceMarkers(priceMarkerLMF);
      }
    } else {
      priceMarkerLMF = removePriceMarkers(priceMarkerLMF);
    }
  }, [map]);

  // Effect to handle marker visibility and cleanup
  useEffect(() => {
    if (!map) return;

    const zoomEnd = () => {
      showHidePriceMarkers();
    };

    map.on('zoomend', zoomEnd);

    return () => {
      map.off('zoomend', zoomEnd);
      priceMarkerLMF = removePriceMarkers(priceMarkerLMF);
      tooltipRef.current.remove();
    };
  }, [map, showHidePriceMarkers]);

  useEffect(() => {
    showHidePriceMarkers();
  }, [
    showPriceMarkers,
    geoJson,
    cmaTabKey,
    searchingMode,
    currentPropertyAddress,
  ]);

  // Effect to update GeoJSON when data changes
  useEffect(() => {
    const newGeoJson = {
      ...geojsonTemplate,
      features: convertToGeoJSONFormat(
        realtorMultiFamilyDataForRender,
        realtorMultiFamilySelectedRowKey,
      ),
    };
    setGeoJson(newGeoJson);
  }, [realtorMultiFamilyDataForRender, realtorMultiFamilySelectedRowKey]);

  const showTooltip = useCallback(
    (e) => {
      const feature = e.features[0];
      const coordinates = feature.geometry.coordinates.slice();
      const placeholder = document.createElement('div');
      ReactDOM.render(<PublicRecordTooltip feature={feature} />, placeholder);
      tooltipRef.current
        .setLngLat(coordinates)
        .setDOMContent(placeholder.firstChild)
        .addTo(map);
    },
    [map],
  );

  const hideTooltip = useCallback(() => {
    tooltipRef.current.remove();
  }, []);

  // Effect to handle tooltip events
  useEffect(() => {
    if (!map) return;

    map.on('mouseenter', `${sourceId}LayerCircle`, showTooltip);
    map.on('mouseleave', `${sourceId}LayerCircle`, hideTooltip);

    return () => {
      map?.off('mouseenter', `${sourceId}LayerCircle`, showTooltip);
      map?.off('mouseleave', `${sourceId}LayerCircle`, hideTooltip);
      tooltipRef.current.remove();
    };
  }, [map, showTooltip, hideTooltip]);

  useEffect(() => {
    if (!map || !realtorMultiFamilyHover) {
      tooltipRef.current.remove();
      return;
    }

    const coordinates = [
      realtorMultiFamilyHover.longitude,
      realtorMultiFamilyHover.latitude,
    ];
    const feature = {
      properties: realtorMultiFamilyHover,
      geometry: realtorMultiFamilyHover.geom,
    };
    const htmlString = renderToString(
      <PublicRecordTooltip feature={feature} />,
    );
    const placeholder = document.createElement('div');
    placeholder.innerHTML = htmlString;
    tooltipRef.current
      .setLngLat(coordinates)
      .setDOMContent(placeholder)
      .addTo(map);
  }, [realtorMultiFamilyHover, map]);

  useEffect(() => {
    return () => {
      priceMarkerLMF = removePriceMarkers(priceMarkerLMF);
      tooltipRef.current.remove();
    };
  }, []);

  const shouldRenderLayer =
    cmaTabKey === '7' &&
    searchingMode === 'Lease' &&
    !isEmpty(currentPropertyAddress);

  return shouldRenderLayer ? (
    <Source id={sourceId} type="geojson" data={geoJson}>
      <Layer {...styles.circle} />
      <Layer {...styles.symbol} />
      <Layer {...styles.cluster} />
      <Layer {...styles.clusterSymbol} />
    </Source>
  ) : null;
}

export default LandMultiFamilyLayer;
