export interface MultiFamilyCompData {
  id: string;
  status: string;
  propertytype: string;
  address: string;
  city: string;
  state: string;
  postal_code: string;
  beds: string;
  baths: string;
  square_feet: string;
  latitude: string;
  longitude: string;
  url: string;
  listedDate: any;
  listedPrice: any;
  removeddate: Date;
  reductions: string;
  currentprice: string;
  avm1source: any;
  avm1estimate: any;
  avm1low: any;
  avm1high: any;
  avm1date: any;
  avm2source: any;
  avm2estimate: any;
  avm2low: any;
  avm2high: any;
  avm2date: any;
  avm3source: any;
  avm3estimate: any;
  avm3low: any;
  avm3high: any;
  avm3date: any;
  listed: any;
  list_price: any;
  placekey: any;
  year_built: any;
  distance: number;
}
