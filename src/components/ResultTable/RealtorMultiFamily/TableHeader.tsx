import styles from '@/components/ResultTable/resultTable.css';
import { Col, Row } from 'antd';
import React from 'react';
const TableHeader = ({ selectedRowKeys }) => {
  return (
    <>
      <Row
        id="RMFTableHeader"
        key="table title row rmf 1"
        align="bottom"
        justify="space-between"
        wrap={true}
        gutter={[0, 12]}
        style={{ marginBottom: 12 }}
      >
        <Col span={12} key="table title" className={styles.cardTitleH2}>
          <p className="w-48">Multi Family</p>
        </Col>
        <Col>Total Selected: {selectedRowKeys.length}</Col>
      </Row>
    </>
  );
};

export default TableHeader;
