import { useElementSize } from '@/hooks';
import { getRealtorMultifamily } from '@/services/data';
import { arrayDifference } from '@/utils/arrayMethods';
import { formatCurrency } from '@/utils/lastSalePublicRecord';
import { Table } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { isEmpty, isEqual } from 'lodash';
import { MutableRefObject, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import TableHeader from './TableHeader';
import {
  calculateMedian,
  calculateMedianPricePerSqFt,
  formatPricePerSqftArce,
  sqftToAcre,
  testLog,
} from './utils/functions';
import { MultiFamilyCompData } from './utils/type';

const RealtorMFTable = () => {
  const dispatch = useDispatch();
  const map = useSelector((state: any) => state.CMA.map);
  const realtorMultiFamilyData = useSelector(
    (state: any) => state.CMA.realtorMultiFamilyData,
  );
  const realtorMultiFamilyDataForRender = useSelector(
    (state: any) => state.CMA.realtorMultiFamilyDataForRender,
  );
  const realtorMultiFamilySelectedRowKey = useSelector(
    (state: any) => state.CMA.realtorMultiFamilySelectedRowKey,
  );
  const currentPropertyAddress = useSelector(
    (state: any) => state.CMA.currentPropertyAddress,
  );
  const currentRadiusMile = useSelector(
    (state: any) => state.CMA.currentRadiusMile,
  );
  const currentStartMLS = useSelector(
    (state: any) => state.CMA.currentStartMLS,
  );
  const currentEndMLS = useSelector((state: any) => state.CMA.currentEndMLS);
  const selectedCompTables = useSelector(
    (state: any) => state.CMA.selectedCompTables,
  );

  const [statusOptions, setStatusOptions] = useState<any>([]);
  const [propertySubtypeOptions, setPropertySubtypeOptions] = useState<any>();
  const [loading, setLoading] = useState(false);
  const RMFContainer = useRef(null);
  const tableSize = useElementSize(RMFContainer);
  const containerRef = useRef(null);

  // Track current table data
  const [currentTableData, setCurrentTableData] = useState<
    MultiFamilyCompData[]
  >([]);

  // Track unselected row keys instead of selected ones
  const [unselectedRowKeys, setUnselectedRowKeys] = useState<React.Key[]>([]);

  // Track previous property address for reset
  const [prevPropertyAddress, setPrevPropertyAddress] = useState<string>(
    currentPropertyAddress,
  );

  // Store table filters
  const [tableFilters, setTableFilters] = useState<any>({
    propertytype: ['Multi Family', 'Apartment'],
  });

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const columns: ColumnsType<MultiFamilyCompData> = [
    {
      title: 'Address',
      dataIndex: 'address',
      key: 'address',
      width: 180,
      fixed: 'left',
      align: 'left',
      render: (text: string, record: MultiFamilyCompData) => (
        <div ref={containerRef} className="image-detail-tooltip-container">
          <a
            href={record.url}
            className="text-blue-500 text-xs"
            target="_blank"
          >
            {record.address}, {record.city}, {record.state},{' '}
            {record.postal_code}{' '}
          </a>
        </div>
      ),
    },

    {
      title: 'Dist. ',
      dataIndex: 'distance',
      key: 'distance',
      width: 75,
      align: 'left',
      defaultSortOrder: 'ascend',
      sorter: (a: MultiFamilyCompData, b: MultiFamilyCompData) =>
        a.distance - b.distance,
      render: (text: any, record: MultiFamilyCompData) => (
        <p className="text-xs">{(record.distance / 1609.34).toFixed(2)} mi</p>
      ),
    },

    {
      title: 'Property Type',
      dataIndex: 'propertytype',
      key: 'propertytype',
      width: 150,
      align: 'left',
      render: (text: any, record: MultiFamilyCompData) => (
        <p className="text-xs">{record.propertytype}</p>
      ),
      filters: propertySubtypeOptions,
      defaultFilteredValue: ['Multi Family', 'Apartment'],
    },
    {
      title: 'Beds',
      dataIndex: 'beds',
      key: 'beds',
      width: 50,
      align: 'left',
      render: (text: any, record: MultiFamilyCompData) => (
        <p className="text-xs">{record.beds}</p>
      ),
    },
    {
      title: 'Baths',
      dataIndex: 'baths',
      key: 'baths',
      width: 50,
      align: 'left',
      render: (text: any, record: MultiFamilyCompData) => (
        <p className="text-xs">{record.baths}</p>
      ),
    },
    {
      title: 'Price',
      dataIndex: 'currentprice',
      key: 'currentprice',
      width: 100,
      align: 'left',
      render: (text: any, record: MultiFamilyCompData) => {
        return <p className="text-xs">{formatCurrency(text)}</p>;
      },
      sorter: (a: MultiFamilyCompData, b: MultiFamilyCompData) =>
        Number(a.currentprice) - Number(b.currentprice),
    },
    {
      title: 'List Date',
      dataIndex: 'listedDate',
      key: 'listedDate',
      width: 100,
      align: 'left',
      render: (text: any, record: MultiFamilyCompData) => (
        <p className="text-xs">{record.listedDate ? record.listedDate : ''}</p>
      ),
      sorter: (a: MultiFamilyCompData, b: MultiFamilyCompData) =>
        a.listedDate - b.listedDate,
    },
    {
      title: 'Price per Sqft',
      dataIndex: 'currentprice',
      key: 'currentprice',
      width: 100,
      align: 'left',
      render: (text: any, record: MultiFamilyCompData) => {
        const price = Number(record.currentprice);
        const pps =
          Number(record.square_feet) > 0
            ? price / Number(record.square_feet)
            : 0;
        return (
          <p className="text-xs">{pps ? formatPricePerSqftArce(pps) : 'N/A'}</p>
        );
      },
      sorter: (a: MultiFamilyCompData, b: MultiFamilyCompData) => {
        // Determine the appropriate price based on status
        const priceA = Number(a.currentprice);
        const priceB = Number(b.currentprice);

        // Calculate PPS for each record
        const ppsA = priceA / Number(a.square_feet);
        const ppsB = priceB / Number(b.square_feet);

        return ppsA - ppsB; // Compare the PPS values
      },
    },
    {
      title: 'Size',
      dataIndex: 'square_feet',
      key: 'square_feet',
      width: 100,
      align: 'left',
      render: (text: any, record: MultiFamilyCompData) => (
        <p className="text-xs">{text}</p>
      ),
      sorter: (a: MultiFamilyCompData, b: MultiFamilyCompData) =>
        Number(a.square_feet) - Number(b.square_feet),
    },
    {
      title: 'Year Built',
      dataIndex: 'year_built',
      key: 'year_built',
      width: 100,
      align: 'left',
      render: (text: any, record: MultiFamilyCompData) => (
        <p className="text-xs">{text}</p>
      ),
      sorter: (a: MultiFamilyCompData, b: MultiFamilyCompData) =>
        a.year_built - b.year_built,
    },
  ];

  // Clear unselected row keys for new subject property
  if (!isEqual(currentPropertyAddress, prevPropertyAddress)) {
    setPrevPropertyAddress(currentPropertyAddress);
    setUnselectedRowKeys([]);
  }

  useEffect(() => {
    const fetchLandComp = async () => {
      testLog('fetchLandComp');
      testLog(selectedCompTables);

      if (
        !isEmpty(currentPropertyAddress) &&
        currentRadiusMile &&
        currentStartMLS &&
        currentEndMLS
      ) {
        setLoading(true);
        const result = await getRealtorMultifamily({
          lat: currentPropertyAddress.latitude,
          lng: currentPropertyAddress.longitude,
          distance: currentRadiusMile * 1609.344,
          startDate: currentStartMLS,
        });
        testLog(result);
        const initialFilters = {
          propertytype: ['Multi Family', 'Apartment'],
        };
        if (result) {
          dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              realtorMultiFamilyData: result,
              realtorMultiFamilyDataForRender: result.filter((house) =>
                initialFilters.propertytype.includes(house.propertytype),
              ),
              selectedCompTables: [...selectedCompTables, 'Multi-family-2'],
              showAVM: false,
            },
          });
        }

        setLoading(false);
      } else {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            realtorMultiFamilyData: [],
            realtorMultiFamilyDataForRender: [],
            selectedCompTables: [...selectedCompTables, 'Multi-family-2'],
            showAVM: false,
          },
        });
      }
    };

    fetchLandComp();
  }, [
    currentPropertyAddress,
    currentRadiusMile,
    currentStartMLS,
    currentEndMLS,
  ]);

  // Get current table data and calculate selected rows from unselected ones
  const getCurrentTableData = () => {
    // remove filters that are null, e.g. { propertysubtype: null }
    const nonNullFilters = Object.fromEntries(
      Object.entries(tableFilters).filter(([_, value]) => value !== null),
    );

    // filter the data based on current filters
    const currentTableData = realtorMultiFamilyDataForRender.filter((row) => {
      return Object.keys(nonNullFilters).every((filter) => {
        if (filter === 'propertytype' && nonNullFilters[filter].length > 0) {
          return nonNullFilters[filter].includes(row[filter]);
        }
        return true;
      });
    });

    // calculate selected row keys as difference between all rows and unselected rows
    const selectedRowKeys = arrayDifference(
      currentTableData.map((row) => row.id),
      unselectedRowKeys,
    );

    return { currentTableData, selectedRowKeys };
  };

  // Update selection state when table data changes
  useEffect(() => {
    if (
      realtorMultiFamilyDataForRender &&
      realtorMultiFamilyDataForRender.length > 0
    ) {
      const { currentTableData, selectedRowKeys } = getCurrentTableData();
      setCurrentTableData(currentTableData);

      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          realtorMultiFamilySelectedRowKey: selectedRowKeys,
        },
      });
    }
  }, [realtorMultiFamilyDataForRender]);

  // Update calculated median values when selection changes
  useEffect(() => {
    if (
      realtorMultiFamilyDataForRender &&
      realtorMultiFamilyDataForRender.length > 0 &&
      realtorMultiFamilySelectedRowKey
    ) {
      const median = calculateMedian(
        realtorMultiFamilySelectedRowKey,
        realtorMultiFamilyDataForRender,
      );
      const medianPerSqft = calculateMedianPricePerSqFt(
        realtorMultiFamilySelectedRowKey,
        realtorMultiFamilyDataForRender,
      );

      testLog(median);
      testLog(medianPerSqft);

      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          realtorMultiFamilyMedian: median,
          realtorMultiFamilyMedianPricePerSqft: medianPerSqft,
        },
      });
    }
  }, [realtorMultiFamilySelectedRowKey, realtorMultiFamilyDataForRender]);

  // Update dropdown filter options
  useEffect(() => {
    if (realtorMultiFamilyData && realtorMultiFamilyData.length > 0) {
      setStatusOptions([
        ...[
          ...new Set(
            realtorMultiFamilyData.map(
              (house: MultiFamilyCompData) => house.status,
            ),
          ),
        ].map((status) => ({ value: status, text: status })),
      ]);
      setPropertySubtypeOptions([
        ...[
          ...new Set(
            realtorMultiFamilyData.map(
              (house: MultiFamilyCompData) => house.propertytype,
            ),
          ),
        ].map((status) => ({ value: status, text: status })),
      ]);
    }
  }, [realtorMultiFamilyData]);

  // Handle row selection change
  const onSelectChange = (selectedRowKeys: React.Key[]) => {
    testLog(selectedRowKeys);

    // Calculate unselected row keys based on currently visible data
    const unselectedRowKeys = arrayDifference(
      currentTableData.map((row) => row.id),
      selectedRowKeys,
    );

    setUnselectedRowKeys(unselectedRowKeys);

    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        realtorMultiFamilySelectedRowKey: selectedRowKeys,
      },
    });
  };

  // Row selection configuration
  const rowSelection = {
    selectedRowKeys: realtorMultiFamilySelectedRowKey || [],
    onChange: onSelectChange,
    selections: [
      Table.SELECTION_ALL,
      Table.SELECTION_INVERT,
      Table.SELECTION_NONE,
    ],
  };

  return (
    <div className="mb-4" ref={RMFContainer}>
      <Table
        dataSource={realtorMultiFamilyDataForRender}
        columns={columns}
        key="lc table"
        title={() => (
          <TableHeader
            selectedRowKeys={realtorMultiFamilySelectedRowKey || []}
          />
        )}
        rowKey={(record) => record.id}
        loading={loading}
        size="small"
        rowSelection={rowSelection}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          onChange: (page, pageSize) => {
            setCurrentPage(page);
            setPageSize(pageSize);
          },
        }}
        onRow={(record, rowIndex) => {
          return {
            onMouseEnter: (event: any) => {
              dispatch({
                type: 'CMA/saveCMAStates',
                payload: {
                  realtorMultiFamilyHover: record,
                },
              });
            },
            onMouseLeave: (event: any) => {
              dispatch({
                type: 'CMA/saveCMAStates',
                payload: {
                  realtorMultiFamilyHover: null,
                },
              });
            },
            onClick: (event: any) => {
              // Use flyTo function with latitude and longitude from the record
              map.flyTo({
                center: [record.longitude, record.latitude],
                zoom: 16,
                speed: 2,
                curve: 1,
                easing: (t: any) => t,
              });
            },
          };
        }}
        scroll={{
          x: 1000,
          y: '400px',
        }}
        onChange={(
          pagination,
          filters,
          sorter,
          { action, currentDataSource },
        ) => {
          if (!isEqual(currentDataSource, currentTableData)) {
            setCurrentTableData(currentDataSource);
          }

          if (action === 'filter') {
            setTableFilters(filters);

            let filteredData = [...realtorMultiFamilyData];

            if (filters.propertytype && filters.propertytype.length > 0) {
              filteredData = filteredData.filter((house: MultiFamilyCompData) =>
                filters.propertytype.includes(house.propertytype),
              );
            }

            // Calculate and update selected row keys for the new filtered data
            const allRowKeys = filteredData.map((item) => item.id);
            const selectedRowKeys = arrayDifference(
              allRowKeys,
              unselectedRowKeys,
            );

            dispatch({
              type: 'CMA/saveCMAStates',
              payload: {
                realtorMultiFamilyDataForRender: filteredData,
                realtorMultiFamilySelectedRowKey: selectedRowKeys,
              },
            });
          }
        }}
        sticky={true}
      />
    </div>
  );
};

export default RealtorMFTable;
