import styles from '@/components/ResultTable/resultTable.css';
import { Col, DatePicker, Modal, Row, Switch } from 'antd';
import isEqual from 'lodash.isequal';
import moment from 'moment';
import { useRef, useState } from 'react';
import { connect } from 'umi';
import ExportAllCSV from '../../ExportToCSV/ExportAllCSV';
import { dateFormat } from '../ResultTable';
import CountyFilter from './CountyFilter';
import DatepickerShortcuts from './DatePickerShortcuts';
import DistrictFilter from './DistrictFilter';
import ZipCodeFilter from './ZipCodeFilter';

const FilterRowMLSAndSFR = (props) => {
  return (
    <div className={styles.cardWrapperFilterRow}>
      <Row
        key="control wrapper"
        align="middle"
        justify="start"
        // className={styles.cardWrapperFilterRow}
        // gutter={[16, 0]}
        gutter={[16, 16]}
      >
        <Col key="select">
          <label
            key="mls status label"
            htmlFor="mls status select"
            // style={{ marginRight: 8 }}
          >
            Status:
          </label>
          <select
            key="mls status select"
            name="mls status select"
            value={props.currentStatusMLS}
            className={styles.controlSelect}
            disabled={props.compingMode === 'intelligentComping'}
            onChange={(e) => {
              // console.log('value', e.target.value);
              const currentValue = e.target.value;
              props.dispatch({
                type: 'CMA/saveCMAStates',
                payload: {
                  currentStatusMLS: currentValue,
                },
              });
              if (props.drawnCustomPolygons.length === 0) {
                if (props.eventCoordinates.length === 2) {
                  // fetch all data
                  props.dispatch({
                    type: 'CMA/getAllPropertyData',
                    payload: {
                      mode: 'change MLS & SFR status',
                      lng: props.eventCoordinates[0],
                      lat: props.eventCoordinates[1],
                      // geofence: getGeofenceParamForGeocodingAPI(props.userGroup),
                      status: currentValue,
                      propertyType:
                        props.searchingMode === 'Lease'
                          ? 'Residential Lease'
                          : 'Residential',
                      startDate: moment(props.currentStartMLS).format(
                        dateFormat,
                      ),
                      endDate: moment(props.currentEndMLS).format(dateFormat),
                      distance: props.currentRadiusMile * 1609.34,
                      exists: currentValue,
                      expDateFilterOn: props.expDateFilterOn ? 'yes' : 'no',
                    },
                  });
                }
              } else {
                props.dispatch({
                  type: 'CMA/getAllPropertyData',
                  payload: {
                    mode: 'change MLS & SFR status',
                    status: currentValue,
                  },
                });
              }
            }}
          >
            <option key="Closed" value="Closed">
              Closed
            </option>
            <option key="Pending" value="Pending">
              Pending
            </option>
            <option key="Active" value="Active">
              Active
            </option>
            <option key="All" value="status">
              All
            </option>
          </select>
        </Col>
        <Col key="date picker">
          <DatepickerShortcuts />
        </Col>
        <Col key="filter button">
          <Row
            key="filter control row"
            align="middle"
            justify="center"
            gutter={12}
          >
            <div style={{ display: 'flex', flexDirection: 'row', gap: '10px' }}>
              <span style={{ fontWeight: 600 }}>Within same: </span>
              <DistrictFilter />
              <CountyFilter />
              <ZipCodeFilter />
            </div>
          </Row>
        </Col>
      </Row>
      {/* <Row>
        <ExportAllCSV />
      </Row> */}
    </div>
  );
};

export default connect(({ CMA }) => ({
  currentStatusMLS: CMA.currentStatusMLS,
  currentStartMLS: CMA.currentStartMLS,
  currentEndMLS: CMA.currentEndMLS,
  currentRadiusMile: CMA.currentRadiusMile,
  eventCoordinates: CMA.eventCoordinates,
  compingMode: CMA.compingMode,

  searchingMode: CMA.searchingMode,
  drawnCustomPolygons: CMA.drawnCustomPolygons,
}))(FilterRowMLSAndSFR);
