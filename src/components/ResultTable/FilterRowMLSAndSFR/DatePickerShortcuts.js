import styles from '@/components/ResultTable/resultTable.css';
import {
  Col,
  DatePicker,
  InputNumber,
  Modal,
  Radio,
  Row,
  Select,
  Switch,
} from 'antd';
import isEqual from 'lodash.isequal';
import moment from 'moment';
import { useRef, useState } from 'react';
import { connect } from 'umi';
import { dateFormat } from '../ResultTable';

const DatePickerShortcuts = (props) => {
  // const [shortcutValue, setShortcutValue] = useState(90);
  const [closedDateWithinOrBetween, setClosedDateWithinOrBetween] = useState(
    moment().isSame(props.currentEndMLS, 'day') ? 'within' : 'between',
  );

  const [prevCurrentEndMLS, setPrevCurrentEndMLS] = useState(
    props.currentEndMLS,
  );
  if (prevCurrentEndMLS !== props.currentEndMLS && props.currentEndMLS) {
    setPrevCurrentEndMLS(props.currentEndMLS);
    if (!moment().isSame(props.currentEndMLS, 'day')) {
      setClosedDateWithinOrBetween('between');
    }
  }

  const getWithinDaysValue = (dateRange) => {
    // end date must be today for within
    if (moment().isSame(dateRange[1], 'day')) {
      const numberOfDays = moment(dateRange[1]).diff(dateRange[0], 'days');
      return numberOfDays;
    } else {
      return null;
    }
    // if (moment().isSame(dateRange[1], 'day') && [30, 60, 90, 120, 160].includes(numberOfDays)) {
    //   return numberOfDays;
    // } else {
    //   return 'Custom';
    // }
  };

  const optionsShortcuts = [
    {
      label: '30',
      value: 30,
    },
    {
      label: '60',
      value: 60,
    },
    {
      label: '90',
      value: 90,
    },
    {
      label: '120',
      value: 120,
    },
    {
      label: '160',
      value: 160,
    },
    {
      label: 'Custom',
      value: 'Custom',
    },
  ];

  const onChangeRadioGroup = (e) => {
    if (e.target.value && e.target.value !== 'Custom') {
      const dateRange = [moment().subtract(e.target.value, 'days'), moment()];
      getAllPropertyDataAndSaveDateRange(dateRange);
    } else if (e.target.value === 'Custom') {
    }
  };

  const onChangeWithin = (value) => {
    if (value && typeof value === 'number') {
      const dateRange = [moment().subtract(value, 'days'), moment()];
      getAllPropertyDataAndSaveDateRange(dateRange);
    }
  };

  const getAllPropertyDataAndSaveDateRange = (dateRange) => {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        currentStartMLS: dateRange[0].format(dateFormat),
        currentEndMLS: dateRange[1].format(dateFormat),
      },
    });
    if (props.drawnCustomPolygons.length === 0) {
      if (props.eventCoordinates.length === 2) {
        // fetch all data
        props.dispatch({
          type: 'CMA/getAllPropertyData',
          payload: {
            mode: 'change MLS & SFR date range',
            lng: props.eventCoordinates[0],
            lat: props.eventCoordinates[1],
            status: props.currentStatusMLS,
            propertyType:
              props.searchingMode === 'Lease'
                ? 'Residential Lease'
                : 'Residential',
            startDate: dateRange[0].format(dateFormat),
            endDate: dateRange[1].format(dateFormat),
            distance: props.currentRadiusMile * 1609.34,
            exists: props.currentStatusMLS,
            expDateFilterOn: props.expDateFilterOn ? 'yes' : 'no',
          },
        });
      }
    } else {
      props.dispatch({
        type: 'CMA/getAllPropertyData',
        payload: {
          mode: 'change MLS & SFR date range',
          status: props.currentStatusMLS,
        },
      });
    }
  };

  const generateDatePickerLabel = (currentStatus, withinOrBetween) => {
    switch (true) {
      case currentStatus === 'Closed':
        if (withinOrBetween === 'within') {
          return 'Closed within';
        } else {
          return 'Closed between';
        }
      case ['Active', 'Pending'].includes(currentStatus):
        if (withinOrBetween === 'within') {
          return 'Listed within';
        } else {
          return 'Listed between';
        }
      case currentStatus === 'status':
        if (withinOrBetween === 'within') {
          return 'Within';
        } else {
          return 'Between';
        }
    }
  };

  return (
    <div key="date shortcut wrapper">
      <select
        key="within or between select"
        name="within or between select"
        value={closedDateWithinOrBetween}
        className={styles.controlSelect}
        onChange={(e) => setClosedDateWithinOrBetween(e.target.value)}
        disabled={props.compingMode === 'intelligentComping'}
      >
        <option key="within" value="within">
          {generateDatePickerLabel(props.currentStatusMLS, 'within')}
        </option>
        <option key="between" value="between">
          {generateDatePickerLabel(props.currentStatusMLS, 'between')}
        </option>
      </select>
      {closedDateWithinOrBetween === 'within' ? (
        <InputNumber
          key="date range input number"
          min={0}
          max={999}
          value={getWithinDaysValue([
            props.currentStartMLS,
            props.currentEndMLS,
          ])}
          onChange={onChangeWithin}
          precision={0} // no decimal
          step={30}
          addonAfter="Days"
          style={{ width: 120, textAlign: 'right' }}
          disabled={props.compingMode === 'intelligentComping'}
        />
      ) : (
        <DatePicker.RangePicker
          key="date range picker"
          // defaultValue={[moment().subtract(3, 'days'), moment()]}
          value={[moment(props.currentStartMLS), moment(props.currentEndMLS)]}
          format={dateFormat}
          allowClear={false}
          allowEmpty={[false, false]}
          className={styles.controlDatePicker}
          onChange={getAllPropertyDataAndSaveDateRange}
          disabled={props.compingMode === 'intelligentComping'}
        />
      )}
    </div>
  );
};

export default connect(({ CMA }) => ({
  currentStatusMLS: CMA.currentStatusMLS,
  currentStartMLS: CMA.currentStartMLS,
  currentEndMLS: CMA.currentEndMLS,
  currentRadiusMile: CMA.currentRadiusMile,
  eventCoordinates: CMA.eventCoordinates,

  searchingMode: CMA.searchingMode,
  expDateFilterOn: CMA.expDateFilterOn,
  drawnCustomPolygons: CMA.drawnCustomPolygons,
  compingMode: CMA.compingMode,
}))(DatePickerShortcuts);
