// utils/columnConfig.js
import styles from '@/components/ResultTable/resultTable.css';
import { showSFROperatorsFullName } from '@/components/SFRBrandConvertFunction';
import { formatter } from '@/utils/money';
import { default as turf_distance } from '@turf/distance';
import { point } from '@turf/helpers';
import moment from 'moment';
import { dateFormat } from '../../../../constants';
import { capitalize } from '../../../../utils/strings';

export const getTableColumns = (props, columnType, dataSource, type) => {
  const tableSorter =
    type === 'SFR' ? props.NSFRTableSort : props.HotPadTableSort;

  const permitTypeOptions = dataSource
    ? [
        ...new Set(
          dataSource
            .filter((property) => property.pmt_type) // Filter out null/undefined values
            .map((property) => property.pmt_type),
        ),
      ].map((type) => ({
        text: type,
        value: type,
      }))
    : [];

  if (columnType === 'original') {
    const columnsNationalOperators = [
      {
        title: 'Address',
        dataIndex: 'address',
        key: 'address',
        width: 300,
        align: 'left',
        fixed: 'left',
        render: (text, record) => {
          const address = capitalize(
            text.includes(',')
              ? text.replace(/,(?! )/g, ', ')
              : text +
                  ', ' +
                  record.standard_city +
                  ', ' +
                  record.standard_state,
          );

          const googleLink = `https://www.google.com/search?q=${address
            .trim()
            .replace(/,/g, '%2C')
            .replace(/ /g, '+')}`;

          const showImgIcon = props.currentMLSPropertiesFiltered.some(
            (mls) => mls.placekey === record.placekey,
          );

          return (
            <span>
              <a href={googleLink} className={styles.sfrLink} target="_blank">
                {address}
              </a>
              {showImgIcon && (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  width="16"
                  height="16"
                  className={styles.imageIconForAddress}
                >
                  <path d="M7.479 12.083h8.709l-2.626-3.937-2.208 3.146-1.729-2.188Zm-.646 3.5q-.895 0-1.541-.645-.646-.646-.646-1.542V3.375q0-.896.646-1.542.646-.645 1.541-.645h10.021q.917 0 1.552.645.636.646.636 1.542v10.021q0 .896-.636 1.542-.635.645-1.552.645Zm-3.687 3.688q-.917 0-1.552-.636Q.958 18 .958 17.083V4.875h2.188v12.208h12.208v2.188Z" />
                </svg>
              )}
            </span>
          );
        },
      },
      {
        title: 'Dist.',
        dataIndex: 'distance',
        key: 'distance',
        width: 100,
        align: 'center',
        render: (text, record) => {
          if (
            props.drawnCustomPolygons.length > 0 &&
            props.eventCoordinates.length > 0
          ) {
            const propertyPoint = point(record.geom.coordinates);
            const eventPoint = point(props.eventCoordinates);
            const distance = turf_distance(propertyPoint, eventPoint, 'miles');

            return distance.toFixed(1) + ' mi';
          }
          return (parseFloat(text) / 1609.34).toFixed(1) + ' mi';
        },
        sorter: (a, b) => {
          if (
            props.drawnCustomPolygons.length > 0 &&
            props.eventCoordinates.length > 0
          ) {
            const aPoint = point(a.geom.coordinates);
            const bPoint = point(b.geom.coordinates);
            const eventPoint = point(props.eventCoordinates);
            const adistance = turf_distance(aPoint, eventPoint, 'miles');
            const bdistance = turf_distance(bPoint, eventPoint, 'miles');

            return adistance - bdistance;
          }
          return a.distance - b.distance;
        },
        sortOrder:
          tableSorter.columnKey === 'distance' ? tableSorter.order : null,
      },
      {
        title: 'Cls.',
        dataIndex: 'close_date',
        key: 'close_date',
        width: 100,
        align: 'center',
        render: (text) => {
          if (text) {
            return moment(text).format(dateFormat);
          } else {
            return '';
          }
        },
        sorter: (a, b) => {
          if (a.close_date && b.close_date) {
            return +moment(a.close_date) - +moment(b.close_date);
          } else {
            return a.close_date || b.close_date;
          }
        },
        sortOrder:
          tableSorter.columnKey === 'close_date' ? tableSorter.order : null,
      },
      {
        title: 'Status',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        align: 'left',
      },
      {
        title: 'Rent',
        dataIndex: 'rent',
        key: 'rent',
        width: 100,
        align: 'center',
        render: (text) => '$' + formatter(parseFloat(text).toFixed()),
        sorter: (a, b) => a.rent - b.rent,
        sortOrder: tableSorter.columnKey === 'rent' ? tableSorter.order : null,
      },
      {
        title: 'Last Sale Price',
        dataIndex: 'deed_last_sale_price',
        key: 'deed_last_sale_price',
        align: 'center',
        width: 100,
        render: (text) => {
          if (text) {
            if (text == '0') {
              return 'N/A';
            }
            return '$' + formatter(text);
          } else {
            return '';
          }
        },
        sorter: (a, b) => {
          if (a.deed_last_sale_price && b.deed_last_sale_price) {
            return +a.deed_last_sale_price - +b.deed_last_sale_price;
          } else if (a.deed_last_sale_price) {
            return -1;
          } else if (b.deed_last_sale_price) {
            return 1;
          }
        },
        sortOrder:
          tableSorter.columnKey === 'deed_last_sale_price'
            ? tableSorter.order
            : null,
      },
      {
        title: 'Last Sale Date',
        dataIndex: 'deed_last_sale_date',
        key: 'deed_last_sale_date',
        align: 'center',
        width: 100,
        render: (text) => {
          if (text) {
            return moment(text, dateFormat).format(dateFormat);
          } else {
            return '';
          }
        },
        sorter: (a, b) => {
          if (a.deed_last_sale_date && b.deed_last_sale_date) {
            return (
              +moment(a.deed_last_sale_date) - +moment(b.deed_last_sale_date)
            );
          } else if (a.deed_last_sale_date) {
            return -1;
          } else if (b.deed_last_sale_date) {
            return 1;
          }
        },
        sortOrder:
          tableSorter.columnKey === 'deed_last_sale_date'
            ? tableSorter.order
            : null,
      },
      {
        title: 'Type',
        dataIndex: 'propertysubtype',
        key: 'propertysubtype',
        width: 100,
        align: 'center',
        filters: [
          ...dataSource.reduce((result, property) => {
            const alreadyExists = result.some(
              (r) => r.value === property.propertysubtype,
            );

            const textFormat = (text) => {
              if (['single family residential'].includes(text.toLowerCase()))
                return 'Single Family';
              if (['single family residence'].includes(text.toLowerCase()))
                return 'Single Family';
              if (['single family detached'].includes(text.toLowerCase()))
                return 'Single Family (Detached)';
              if (text === 'Townhouse') return 'TH';
              return text;
            };

            if (!alreadyExists && property.propertysubtype) {
              result.push({
                text: textFormat(property.propertysubtype),
                value: property.propertysubtype,
              });
            }
            return result;
          }, []),
        ],
        onFilter: (value, record) => record.propertysubtype === value,
        render: (text) => {
          if (text === null) {
            text = 'N/A';
          } else {
            if (
              text &&
              ['single family residential', 'single family residence'].includes(
                text.toLowerCase(),
              )
            ) {
              return 'Single Family';
            } else if (
              text &&
              ['single family detached'].includes(text.toLowerCase())
            ) {
              return 'Single Family (Detached)';
            } else if (text === 'Townhouse') {
              return 'TH';
            } else {
              return text;
            }
          }
        },
        ellipsis: true,
      },
      {
        title: 'Permit Type',
        dataIndex: 'pmt_type',
        key: 'pmt_type',
        width: 100,
        render: (text) => {
          return text;
        },
        filters: permitTypeOptions,
        onFilter: (value, record) => {
          return record.pmt_type ? record.pmt_type.indexOf(value) === 0 : false;
        },
      },
      {
        title: 'YrB',
        dataIndex: 'yearbuilt',
        key: 'yearbuilt',
        width: 100,
        align: 'center',
        sorter: (a, b) => a.yearbuilt - b.yearbuilt,
        sortOrder:
          tableSorter.columnKey === 'yearbuilt' ? tableSorter.order : null,
      },
      {
        title: 'Bd',
        dataIndex: 'bed_rooms',
        key: 'bed_rooms',
        width: 100,
        align: 'center',
        sorter: (a, b) => a.bed_rooms - b.bed_rooms,
        sortOrder:
          tableSorter.columnKey === 'bed_rooms' ? tableSorter.order : null,
      },
      {
        title: 'Ba',
        dataIndex: 'bath_rooms',
        key: 'bath_rooms',
        width: 100,
        align: 'center',
        sorter: (a, b) => a.bath_rooms - b.bath_rooms,
        sortOrder:
          tableSorter.columnKey === 'bath_rooms' ? tableSorter.order : null,
      },
      {
        title: 'Sqft',
        dataIndex: 'square_feet',
        key: 'square_feet',
        width: 100,
        align: 'center',
        render: (text) => {
          if (text === null) {
            return '';
          } else {
            return formatter(text);
          }
        },
        sorter: (a, b) => a.square_feet - b.square_feet,
        sortOrder:
          tableSorter.columnKey === 'square_feet' ? tableSorter.order : null,
      },
      {
        title: 'Lot Size',
        dataIndex: 'area_acres',
        key: 'area_acres',
        align: 'center',
        width: 100,
        render: (text) => (text ? `${text.toFixed(2)} ac` : ''),
        sorter: (a, b) => a.area_acres - b.area_acres,
        sortOrder:
          tableSorter.columnKey === 'area_acres' ? tableSorter.order : null,
      },
      {
        title: 'CDOM',
        dataIndex: 'cdom',
        key: 'cdom',
        width: 100,
        align: 'center',
        sorter: (a, b) => a.cdom - b.cdom,
        sortOrder: tableSorter.columnKey === 'cdom' ? tableSorter.order : null,
      },
      {
        title: 'PSF',
        dataIndex: 'rent',
        key: 'psf',
        width: 100,
        align: 'center',
        render: (text, record) => {
          if (text && record.square_feet) {
            return '$' + (text / record.square_feet).toFixed(2);
          }
        },
        sorter: (a, b) => a.rent / a.square_feet - b.rent / b.square_feet,
        sortOrder: tableSorter.columnKey === 'psf' ? tableSorter.order : null,
      },
    ];

    // Add owner column to SFR table but not HotPads table
    if (type === 'SFR') {
      const ownerColumn = {
        title: 'Owner',
        dataIndex: 'brand',
        key: 'brand',
        width: 100,
        align: 'center',
        render: (text) => showSFROperatorsFullName(text),
        filters: [
          ...dataSource.reduce((result, property, idx) => {
            const alreadyExists = result.some(
              (r) => r.value === property.brand,
            );

            if (idx === 0) {
              result.push({
                text: 'N/A',
                value: 'N/A',
              });
            }

            if (
              !alreadyExists &&
              property.brand &&
              property.brand !== 'Rently'
            ) {
              result.push({
                value: property.brand,
                text: property.brand
                  ? showSFROperatorsFullName(property.brand) == ''
                    ? 'N/A'
                    : showSFROperatorsFullName(property.brand)
                  : 'N/A',
              });
            }
            return result;
          }, []),
        ],
        onFilter: (value, record) => {
          if (
            value === 'N/A' &&
            (!record.brand ||
              record.brand === 'Rently' ||
              showSFROperatorsFullName(record.brand) == '')
          )
            return true;
          return record.brand === value;
        },
      };
      columnsNationalOperators.splice(3, 0, ownerColumn);
    }

    // Remove distance column if no custom polygons
    if (
      props.drawnCustomPolygons.length > 0 &&
      props.eventCoordinates.length === 0
    ) {
      columnsNationalOperators.splice(1, 1);
    }

    return columnsNationalOperators;
  } else if (columnType === 'minified') {
    // Minified version for map expanded view
    const columnsNationalOperatorsMinified = [
      {
        title: 'Address',
        dataIndex: 'address',
        key: 'address',
        width: 200,
        align: 'left',
        render: (text, record) => {
          const address = capitalize(
            text.includes(',')
              ? text.replace(/,(?! )/g, ', ')
              : text +
                  ', ' +
                  record.standard_city +
                  ', ' +
                  record.standard_state,
          );

          const googleLink = `https://www.google.com/search?q=${address
            .trim()
            .replace(/,/g, '%2C')
            .replace(/ /g, '+')}`;

          return (
            <a href={googleLink} className={styles.sfrLink} target="_blank">
              {address}
            </a>
          );
        },
      },
      {
        title: 'Rent',
        dataIndex: 'rent',
        key: 'rent',
        width: 100,
        align: 'center',
        render: (text) => '$' + formatter(parseFloat(text).toFixed()),
        sorter: (a, b) => a.rent - b.rent,
        sortOrder: tableSorter.columnKey === 'rent' ? tableSorter.order : null,
      },
      {
        title: 'Sqft',
        dataIndex: 'square_feet',
        key: 'square_feet',
        width: 100,
        align: 'center',
        render: (text) => {
          if (text === null) {
            return '';
          } else {
            return formatter(text);
          }
        },
        sorter: (a, b) => a.square_feet - b.square_feet,
        sortOrder:
          tableSorter.columnKey === 'square_feet' ? tableSorter.order : null,
      },
      {
        title: 'PSF',
        dataIndex: 'rent',
        key: 'psf',
        width: 100,
        align: 'center',
        render: (text, record) => {
          if (text && record.square_feet) {
            return '$' + (text / record.square_feet).toFixed(2);
          }
        },
        sorter: (a, b) => a.rent / a.square_feet - b.rent / b.square_feet,
        sortOrder: tableSorter.columnKey === 'psf' ? tableSorter.order : null,
      },
    ];
    return columnsNationalOperatorsMinified;
  }
};
