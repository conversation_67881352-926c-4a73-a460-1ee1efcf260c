// NationalSFROperators.jsx
import styles from '@/components/ResultTable/resultTable.css';
import { useElementSize } from '@/hooks';
import {
  getUserTableColumnSettings,
  postSaveUserTableColumnSettings,
} from '@/services/data';
import { arrayDifference } from '@/utils/arrayMethods';
import { formatter } from '@/utils/money';
import { useAuthenticator } from '@aws-amplify/ui-react';
import { Table } from 'antd';
import isEmpty from 'lodash.isempty';
import isEqual from 'lodash.isequal';
import moment from 'moment';
import { useEffect, useRef, useState } from 'react';
import { connect } from 'umi';
import { openMLSImageModal } from '../MLS/MLS';
import { dateFormat } from '../ResultTable';
import TableHeader from './TableHeader';
import { getTableColumns } from './utils/columnConfig';

export let selectedRowKey;
export let rentAverageNationalOperators;

const PortalListingTable = connect(({ CMA }) => ({
  subjectPropertyParcelData: CMA.subjectPropertyParcelData,
  currentStartMLS: CMA.currentStartMLS,
  currentEndMLS: CMA.currentEndMLS,
  currentStatusMLS: CMA.currentStatusMLS,
  currentNationalOperatorsPropertiesFiltered:
    CMA.currentNationalOperatorsPropertiesFiltered,
  currentHotPadsPropertiesFiltered: CMA.currentHotPadsPropertiesFiltered,
  currentMLSPropertiesFiltered: CMA.currentMLSPropertiesFiltered,
  currentRadiusMile: CMA.currentRadiusMile,
  eventCoordinates: CMA.eventCoordinates,
  searchingMode: CMA.searchingMode,
  mapExpandedView: CMA.mapExpandedView,
  userGroup: CMA.userGroup,
  NSFRTableSort: CMA.NSFRTableSort,
  HotPadTableSort: CMA.HotPadTableSort,
  sfrTableFilters: CMA.sfrTableFilters,
  hotpadsTableFilters: CMA.hotpadsTableFilters,
  drawnCustomPolygons: CMA.drawnCustomPolygons,
  mapLocateProperty: CMA.mapLocateProperty,
  cmaTabKey: CMA.cmaTabKey,
  compingMode: CMA.compingMode,
  fetchCompSFRDone: CMA.fetchCompSFRDone,
  fetchCompHotPadsDone: CMA.fetchCompHotPadsDone,
  selectedRowKeysNationalOperators: CMA.selectedRowKeysNationalOperators,
  selectedRowKeysHotPads: CMA.selectedRowKeysHotPads,
  sftColumnSettings: CMA.sftColumnSettings,
  portalColumnSettings: CMA.portalColumnSettings,
}))(function (props) {
  const NationalContainer = useRef(null);
  const NationalTooltip = useRef(null);
  const { user } = useAuthenticator();
  const [scrollToSelectedRow, setScrollToSelectedRow] = useState(false);
  const [
    previousNationalOperatorsPropertiesFiltered,
    setPreviousNationalOperatorsPropertiesFiltered,
  ] = useState([]);
  const [
    previousHotPadsPropertiesFiltered,
    setPreviousHotPadsPropertiesFiltered,
  ] = useState([]);
  const [prevSubjectPropertyParcelData, setPrevSubjectPropertyParcelData] =
    useState(props.subjectPropertyParcelData);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [currentTableData, setCurrentTableData] = useState([]); // current table data with current sorting and filtering
  const [
    unselectedRowKeysNationalOperators,
    setUnselectedRowKeysNationalOperators,
  ] = useState([]);
  const [unselectedRowKeysHotPads, setUnselectedRowKeysHotPads] = useState([]);
  const [scrollX, setScrollX] = useState(2000);

  // Define default columns for both table types
  const DEFAULT_SFR_COLUMNS = [
    { title: 'Address', key: 'address', visible: true },
    { title: 'Dist.', key: 'distance', visible: true },
    { title: 'Cls.', key: 'close_date', visible: true },
    { title: 'Owner', key: 'brand', visible: true },
    { title: 'Status', key: 'status', visible: true },
    { title: 'Rent', key: 'rent', visible: true },
    { title: 'Last Sale Price', key: 'deed_last_sale_price', visible: true },
    { title: 'Last Sale Date', key: 'deed_last_sale_date', visible: true },
    { title: 'Type', key: 'propertysubtype', visible: true },
    { title: 'Permit Type', key: 'pmt_type', visible: true },
    { title: 'YrB', key: 'yearbuilt', visible: true },
    { title: 'Bd', key: 'bed_rooms', visible: true },
    { title: 'Ba', key: 'bath_rooms', visible: true },
    { title: 'Sqft', key: 'square_feet', visible: true },
    { title: 'Lot Size', key: 'area_acres', visible: true },
    { title: 'CDOM', key: 'cdom', visible: true },
    { title: 'PSF', key: 'psf', visible: true },
  ];

  const DEFAULT_HOTPADS_COLUMNS = [
    { title: 'Address', key: 'address', visible: true },
    { title: 'Dist.', key: 'distance', visible: true },
    { title: 'Cls.', key: 'close_date', visible: true },
    { title: 'Status', key: 'status', visible: true },
    { title: 'Rent', key: 'rent', visible: true },
    { title: 'Last Sale Price', key: 'deed_last_sale_price', visible: true },
    { title: 'Last Sale Date', key: 'deed_last_sale_date', visible: true },
    { title: 'Type', key: 'propertysubtype', visible: true },
    { title: 'Permit Type', key: 'pmt_type', visible: true },
    { title: 'YrB', key: 'yearbuilt', visible: true },
    { title: 'Bd', key: 'bed_rooms', visible: true },
    { title: 'Ba', key: 'bath_rooms', visible: true },
    { title: 'Sqft', key: 'square_feet', visible: true },
    { title: 'Lot Size', key: 'area_acres', visible: true },
    { title: 'CDOM', key: 'cdom', visible: true },
    { title: 'PSF', key: 'psf', visible: true },
  ];

  // State for column configurations
  const [columnConfigs, setColumnConfigs] = useState(
    props.type === 'SFR'
      ? props.sftColumnSettings || DEFAULT_SFR_COLUMNS
      : props.portalColumnSettings || DEFAULT_HOTPADS_COLUMNS,
  );

  // Fetch user table column settings
  const fetchUserTableColumnSettings = async () => {
    try {
      const data = await getUserTableColumnSettings({
        username: user?.username,
      });

      if (props.type === 'SFR' && data.sfr) {
        props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            sftColumnSettings: Array.isArray(data.sfr)
              ? data.sfr
              : JSON.parse(data.sfr),
          },
        });
      } else if (props.type === 'HotPads' && data.portal) {
        props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            portalColumnSettings: Array.isArray(data.portal)
              ? data.portal
              : JSON.parse(data.portal),
          },
        });
      }
    } catch (err) {
      console.error('Error loading column settings:', err);
    }
  };

  // Update columnConfigs when settings change
  useEffect(() => {
    if (props.type === 'SFR' && props.sftColumnSettings) {
      setColumnConfigs(
        Array.isArray(props.sftColumnSettings)
          ? props.sftColumnSettings
          : JSON.parse(props.sftColumnSettings),
      );
    } else if (props.type === 'HotPads' && props.portalColumnSettings) {
      setColumnConfigs(
        Array.isArray(props.portalColumnSettings)
          ? props.portalColumnSettings
          : JSON.parse(props.portalColumnSettings),
      );
    }
  }, [props.sftColumnSettings, props.portalColumnSettings, props.type]);

  // Fetch settings on mount or when username changes
  useEffect(() => {
    if (user?.username) {
      fetchUserTableColumnSettings();
    }
  }, [user?.username]);

  // Save user settings
  const saveUserSettings = async (updatedColumns) => {
    try {
      const tableName = props.type === 'SFR' ? 'sfr' : 'portal';
      const response = await postSaveUserTableColumnSettings({
        body: {
          username: user.username,
          tableName: tableName,
          settings: JSON.stringify(updatedColumns),
        },
      });

      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          ...(props.type === 'SFR'
            ? { sftColumnSettings: updatedColumns }
            : { portalColumnSettings: updatedColumns }),
        },
      });
    } catch (error) {
      console.error('Error saving column settings:', error);
    }
  };

  // Update scrollX based on visible columns
  useEffect(() => {
    const columns = getGeneratedColumns();
    const totalWidth = columns.reduce(
      (sum, col) => sum + (col.width || 100),
      0,
    );
    setScrollX(Math.max(totalWidth, 1600));
  }, [columnConfigs, props.mapExpandedView]);

  // sort the table data based on NSFRTableSort or HotPadTableSort
  const getInitialTableData = () => {
    if (props.type === 'SFR') {
      return props.currentNationalOperatorsPropertiesFiltered.sort(
        (a, b) =>
          a[props.NSFRTableSort.columnKey] - b[props.NSFRTableSort.columnKey],
      );
    } else if (props.type === 'HotPads') {
      return props.currentHotPadsPropertiesFiltered.sort(
        (a, b) =>
          a[props.HotPadTableSort.columnKey] -
          b[props.HotPadTableSort.columnKey],
      );
    }
  };

  // get current table data after currentNationalOperatorsPropertiesFiltered or currentHotPadsPropertiesFiltered has changed
  // and keep selection
  const getCurrentTableData = () => {
    // first, sort the data based on current sort order
    const allCompsDataSorted = getInitialTableData();
    // remove filters that are null, e.g. { propertysubtype: null }
    const tableFilters =
      props.type === 'SFR' ? props.sfrTableFilters : props.hotpadsTableFilters;
    const nonNullFilters = Object.fromEntries(
      Object.entries(tableFilters).filter(([_, value]) => value !== null),
    );
    // then, filter the data based on current filters
    const currentTableData = allCompsDataSorted.filter((row) => {
      return Object.keys(nonNullFilters).every((filter) => {
        return nonNullFilters[filter].includes(row[filter]);
      });
    });
    // then, remove unselected rows from all row keys
    const unselectedRowKeys =
      props.type === 'SFR'
        ? unselectedRowKeysNationalOperators
        : unselectedRowKeysHotPads;
    const selectedRowKeys = arrayDifference(
      currentTableData.map((row) => row.base_id),
      unselectedRowKeys,
    );
    return { currentTableData, selectedRowKeys };
  };

  // clear unselected row keys for new subject property
  if (
    !isEqual(props.subjectPropertyParcelData, prevSubjectPropertyParcelData)
  ) {
    setPrevSubjectPropertyParcelData(props.subjectPropertyParcelData);
    setUnselectedRowKeysNationalOperators([]);
    setUnselectedRowKeysHotPads([]);
  }

  // after currentNationalOperatorsPropertiesFiltered or currentHotPadsPropertiesFiltered has changed
  // get current table data and selected row keys
  if (
    (props.type === 'SFR' &&
      !isEqual(
        props.currentNationalOperatorsPropertiesFiltered,
        previousNationalOperatorsPropertiesFiltered,
      )) ||
    (props.type === 'HotPads' &&
      !isEqual(
        props.currentHotPadsPropertiesFiltered,
        previousHotPadsPropertiesFiltered,
      ))
  ) {
    const { currentTableData, selectedRowKeys } = getCurrentTableData();
    setCurrentTableData(currentTableData);
    if (props.type === 'SFR') {
      props.onSelectChangeNationalOperators(selectedRowKeys);
      setPreviousNationalOperatorsPropertiesFiltered(
        props.currentNationalOperatorsPropertiesFiltered,
      );
    } else if (props.type === 'HotPads') {
      props.onSelectChangeHotPads(selectedRowKeys);
      setPreviousHotPadsPropertiesFiltered(
        props.currentHotPadsPropertiesFiltered,
      );
    }
  }

  const tableSize = useElementSize(NationalContainer);

  selectedRowKey = props.selectedRowKeysNationalOperators;
  rentAverageNationalOperators = props.rentAverageNationalOperators;

  const dataSource =
    props.type === 'SFR'
      ? props.currentNationalOperatorsPropertiesFiltered
      : props.currentHotPadsPropertiesFiltered;
  const selectedRowKeys =
    props.type === 'SFR'
      ? props.selectedRowKeysNationalOperators
      : props.selectedRowKeysHotPads;
  const onSelectChange =
    props.type === 'SFR'
      ? props.onSelectChangeNationalOperators
      : props.onSelectChangeHotPads;
  const rentMedian =
    props.type === 'SFR' ? props.rentMedianSFR : props.rentMedianHotPads;

  // Generate columns based on column configuration
  const getGeneratedColumns = () => {
    const allColumns = getTableColumns(
      props,
      props.mapExpandedView ? 'minified' : 'original',
      dataSource,
      props.type,
    );

    return allColumns
      .filter((col) => {
        const config = columnConfigs.find((c) => c.key === col.key);
        return config?.visible;
      })
      .sort((a, b) => {
        const aIndex = columnConfigs.findIndex((c) => c.key === a.key);
        const bIndex = columnConfigs.findIndex((c) => c.key === b.key);
        return aIndex - bIndex;
      });
  };

  useEffect(() => {
    const click = (e) => {
      if (e.target.id === 'locatePropertyButton' || !NationalContainer.current)
        return;

      const row = NationalContainer.current.querySelector(
        `.${styles.mapLocatePropertySelected}`,
      );
      if (row) {
        row.classList.remove(styles.mapLocatePropertySelected);
        props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: { mapLocateProperty: {} },
        });
      }
    };

    document.addEventListener('click', click);
    return () => {
      document.removeEventListener('click', click);
    };
  }, []);

  useEffect(() => {
    if (scrollToSelectedRow && NationalContainer.current) {
      const row = NationalContainer.current.querySelector(
        `.${styles.mapLocatePropertySelected}`,
      );

      if (row) {
        row.scrollIntoView({ behavior: 'smooth', block: 'center' });
        setScrollToSelectedRow(false);
      }
    }
  }, [scrollToSelectedRow]);

  useEffect(() => {
    if (
      !isEmpty(props.mapLocateProperty) &&
      props.mapLocateProperty.type === props.type
    ) {
      const rowData = currentTableData.find(
        (property) => property.base_id === props.mapLocateProperty.id,
      );
      if (rowData) {
        const rowIndex = currentTableData.indexOf(rowData);
        const rowPage = Math.floor(rowIndex / pageSize) + 1;
        setCurrentPage(rowPage);
        if (props.cmaTabKey !== '1') {
          props.dispatch({
            type: 'CMA/saveCMAStates',
            payload: { cmaTabKey: '1' },
          });
        }
        setTimeout(() => {
          setScrollToSelectedRow(true);
        }, 100);
      }
    }
  }, [props.mapLocateProperty]);

  // can use on multi family also
  const onRowNationalOperator = (record) => {
    return {
      onClick: (event) => {
        if (
          event.target.localName === 'path' ||
          event.target.localName === 'svg'
        ) {
          // console.log('record', record);
          const mlsRecord = props.currentMLSPropertiesFiltered.find(
            (mls) => mls.placekey === record.placekey,
          );
          openMLSImageModal(mlsRecord, props);
        }
      },
      onMouseEnter: (event) => {
        if (
          !isEqual(props.currentHighlightCoordinates, record.geom.coordinates)
        ) {
          props.dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              currentHighlightCoordinates: record.geom.coordinates,
              priceHighlightMarker: record.rent,
              typeHighlightMarker:
                record.brand === 'Hotpads' ? 'HotPads' : record.brand,
            },
          });
        }

        if (!props.mapExpandedView) return;

        const { brand, bed_rooms, bath_rooms, status } = record;
        // const exists = record.exists ? 'Available' : 'Closed';
        const distance =
          (parseFloat(record.distance) / 1609.34).toFixed(1) + ' mi';
        const available_date = moment(record.available_date).format(dateFormat);
        const close_date = moment(record.close_date).format(dateFormat);
        const tooltipMessage = `Distance: ${distance}\nStatus: ${status}\nOwner: ${brand}\nBeds: ${bed_rooms}\nBaths: ${bath_rooms}\nAvail. Date: ${available_date}\nClose Date: ${close_date}`;

        NationalTooltip.current.innerText = tooltipMessage;
        NationalTooltip.current.style.display = 'block';

        const containerPos = NationalContainer.current.getBoundingClientRect();
        const rowPos = event.currentTarget.getBoundingClientRect();

        NationalTooltip.current.style.top = `${
          rowPos.top -
          containerPos.top -
          NationalTooltip.current.clientHeight -
          15
        }px`;
        NationalTooltip.current.style.left = '50%';
        NationalTooltip.current.style.transform = 'translateX(-50%)';
      },
      onMouseLeave: (event) => {
        if (!props.mapExpandedView) return;
        NationalTooltip.current.innerText = '';
        NationalTooltip.current.style.display = 'none';
      },
    };
  };

  const isBridgeTower =
    props.userGroup && props.userGroup.includes('BridgeTower');

  return (
    <>
      {props.searchingMode === 'Lease' && (
        <div
          ref={NationalContainer}
          key="national operators card"
          className={styles.cardWrapperSFR}
          style={{
            display: props.searchingMode === 'Lease' ? 'block' : 'none',
          }}
        >
          <Table
            key="national providers table"
            className="table-sticky-title-header"
            rowKey={(record) => record.base_id}
            columns={getGeneratedColumns()}
            dataSource={dataSource}
            size="small"
            variant={'filled'}
            pagination={{
              current: currentPage,
              pageSize: pageSize,
              onChange: (page, pageSize) => {
                setCurrentPage(page);
                setPageSize(pageSize);
              },
            }}
            loading={
              props.type === 'SFR'
                ? !props.fetchCompSFRDone
                : !props.fetchCompHotPadsDone
            }
            rowSelection={{
              selectedRowKeys: selectedRowKeys,
              onChange: (selectedRowKeys) => {
                const unselectedRowKeys = arrayDifference(
                  dataSource.map((row) => row.base_id),
                  selectedRowKeys,
                );
                if (props.type === 'SFR') {
                  setUnselectedRowKeysNationalOperators(unselectedRowKeys);
                } else if (props.type === 'HotPads') {
                  setUnselectedRowKeysHotPads(unselectedRowKeys);
                }
                onSelectChange(selectedRowKeys);
              },
              selections: [
                Table.SELECTION_ALL,
                Table.SELECTION_INVERT,
                Table.SELECTION_NONE,
              ],
              getCheckboxProps: (record) => ({
                disabled: props.compingMode === 'intelligentComping',
              }),
            }}
            rowClassName={(record, index) => {
              let className = styles.propertyDataTableRow;
              if (
                !isEmpty(props.mapLocateProperty) &&
                props.mapLocateProperty.type === props.type
              ) {
                if (record.base_id === props.mapLocateProperty.id) {
                  className += ' ' + styles.mapLocatePropertySelected;
                }
              }
              return className;
            }}
            onRow={onRowNationalOperator}
            sticky={true}
            components={{
              header: {
                wrapper: ({ className, children }) => {
                  return (
                    <thead className={className}>
                      <div style={{ width: tableSize.width - 12 }}>
                        <TableHeader
                          type={props.type}
                          selectedRowKeys={selectedRowKeys}
                          columns={columnConfigs}
                          onColumnsChange={saveUserSettings}
                          defaultColumns={
                            props.type === 'SFR'
                              ? DEFAULT_SFR_COLUMNS
                              : DEFAULT_HOTPADS_COLUMNS
                          }
                          median={rentMedian}
                        />
                      </div>
                      {children}
                    </thead>
                  );
                },
              },
            }}
            scroll={props.mapExpandedView ? null : { x: scrollX }}
            defaultFilteredValue={dataSource}
            filterResetToDefaultFilteredValue={true}
            onChange={(pagination, filters, sorter, extra) => {
              if (!isEqual(extra.currentDataSource, currentTableData)) {
                setCurrentTableData(extra.currentDataSource);
              }
              switch (extra.action) {
                case 'sort':
                  props.dispatch({
                    type: 'CMA/saveCMAStates',
                    payload: {
                      ...(props.type === 'SFR'
                        ? { NSFRTableSort: sorter }
                        : { HotPadTableSort: sorter }),
                    },
                  });
                  break;
                case 'filter':
                  // selectedRowKeys are not guaranteed to update automatically
                  // so we need to manually update them
                  // and use unselectedRowKeys to keep selection
                  const allRowKeys = extra.currentDataSource.map(
                    (item) => item.base_id,
                  );
                  const unselectedRowKeys =
                    props.type === 'SFR'
                      ? unselectedRowKeysNationalOperators
                      : unselectedRowKeysHotPads;
                  const selectedRowKeys = arrayDifference(
                    allRowKeys,
                    unselectedRowKeys,
                  );
                  props.dispatch({
                    type: 'CMA/saveCMAStates',
                    payload: {
                      ...(props.type === 'SFR'
                        ? { sfrTableFilters: filters }
                        : { hotpadsTableFilters: filters }),

                      ...(props.type === 'SFR'
                        ? { selectedRowKeysNationalOperators: selectedRowKeys }
                        : { selectedRowKeysHotPads: selectedRowKeys }),
                    },
                  });
                  break;
                default:
                  break;
              }
            }}
            summary={(currentData) => {
              return (
                <Table.Summary fixed>
                  <Table.Summary.Row>
                    <Table.Summary.Cell
                      colSpan={
                        props.mapExpandedView ? 2 : props.type === 'SFR' ? 6 : 5
                      }
                    >
                      Median Rent of Selected
                    </Table.Summary.Cell>
                    <Table.Summary.Cell
                      className={styles.tableSummaryCellTextAlignCenter}
                    >
                      ${rentMedian ? formatter(rentMedian) : '-'}
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                </Table.Summary>
              );
            }}
          />
          <div ref={NationalTooltip} className={styles.customToolTip}></div>
        </div>
      )}
    </>
  );
});

export default PortalListingTable;
