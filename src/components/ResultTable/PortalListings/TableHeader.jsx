// TableHeader.jsx
import styles from '@/components/ResultTable/resultTable.css';
import { formatCurrency } from '@/utils/lastSalePublicRecord';
import { SettingOutlined } from '@ant-design/icons';
import { useAuthenticator } from '@aws-amplify/ui-react';
import { Button, Col, Row, Tooltip } from 'antd';
import React, { useState } from 'react';
import ColumnManagerModal from '../ColumnManagerModal';

const TableHeader = ({
  type,
  selectedRowKeys,
  median,
  columns,
  defaultColumns,
  onColumnsChange,
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const { user } = useAuthenticator();

  const handleOk = (updatedColumns) => {
    onColumnsChange(updatedColumns);
    setIsModalVisible(false);
  };

  const getTitle = () => {
    return type === 'SFR'
      ? 'National SFR Operators Listings'
      : 'Portal Listings';
  };

  return (
    <>
      <Row
        id={`sfrTableHeader-${type}`}
        key={`table title row ${type}`}
        align="bottom"
        justify="space-between"
        wrap={true}
        gutter={[0, 8]}
        style={{ marginBottom: 12 }}
      >
        <Col key="table title" className={styles.cardTitleH2}>
          {getTitle()}
        </Col>
        <Col key={`${type} summary row wrapper`}>
          <Row
            key={`${type} summary row`}
            align="middle"
            justify="end"
            gutter={24}
          >
            <Col key="total unit">
              <span
                key="total unit text"
                className={styles.cardSubtitle}
                style={{ marginRight: 8 }}
              >
                Total Selected
              </span>
              <span
                id={`sfrTable-Total-${type}`}
                key="total unit number"
                className={styles.cardDataValue}
              >
                {selectedRowKeys.length || '-'}
              </span>
            </Col>
            <Col key={`${type} avg`}>
              <span
                key={`${type} avg text`}
                className={styles.cardSubtitle}
                style={{ marginRight: 8 }}
              >
                Median Rent
              </span>
              <span
                id={`sfrTable-Median-${type}`}
                key={`${type} avg number`}
                className={styles.cardDataValue}
              >
                {median ? formatCurrency(median) : '-'}
              </span>
            </Col>
            <Col>
              <Tooltip title="Column Customization">
                <Button
                  icon={<SettingOutlined />}
                  onClick={() => setIsModalVisible(true)}
                ></Button>
              </Tooltip>
            </Col>
          </Row>
        </Col>
      </Row>
      <ColumnManagerModal
        visible={isModalVisible}
        columns={columns}
        defaultColumns={defaultColumns}
        onOk={handleOk}
        onCancel={() => setIsModalVisible(false)}
      />
    </>
  );
};

export default TableHeader;
