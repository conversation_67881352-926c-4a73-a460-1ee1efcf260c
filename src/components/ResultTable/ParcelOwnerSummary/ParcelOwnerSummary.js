import styles from '@/components/ResultTable/resultTable.css';
import { Col, Popover, Progress, Row, Tooltip, message } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { connect, useDispatch } from 'umi';
import { OWNER_COLOR } from '../../../constants';
import { getOtherOwnersDetails } from '../../../services/data';

export let totalParcels, occupied, renter;
export let institutions;

export const getStrokeColor = (institutionName) => {
  if (institutionName.includes('AH4R')) {
    return OWNER_COLOR.AH4RColor;
  } else if (institutionName.includes('Amherst')) {
    return OWNER_COLOR.amherstColor;
  } else if (institutionName.includes('Cerberus')) {
    return OWNER_COLOR.cerberusColor;
  } else if (institutionName.includes('Invitation')) {
    return OWNER_COLOR.invitationHomes;
  } else if (institutionName.includes('Progress')) {
    return OWNER_COLOR.progressResColor;
  } else if (institutionName.includes('Tricon')) {
    return OWNER_COLOR.triconColor;
  } else if (institutionName.includes('Mom & Pop')) {
    return OWNER_COLOR.momAndPopColor;
  } else if (institutionName.includes('Other')) {
    return OWNER_COLOR.othersColor;
  } else {
    return OWNER_COLOR.othersColor;
  }
};

const ParcelOwnerSummary = connect(({ CMA }) => ({
  currentParcelOwnerSummary: CMA.currentParcelOwnerSummary,
  otherFundsDetails: CMA.otherFundsDetails,
  eventCoordinates: CMA.eventCoordinates,
  currentRadiusMile: CMA.currentRadiusMile,
}))(function (props) {
  const institutionContainer = useRef(null);
  const [institutionContainerWidth, setInstitutionContainerWidth] = useState(0);

  const dispatch = useDispatch();
  totalParcels = 0;
  occupied = 0;
  renter = 0;
  institutions = [];

  // Process fetched parcel summary information
  if (props.currentParcelOwnerSummary) {
    const summary = props.currentParcelOwnerSummary;

    totalParcels = summary.total_parcels || 0;

    if (summary.owner_count) {
      summary.owner_count.forEach((owner) => {
        if (owner['owner_occupied_sl'] === 'Yes') {
          occupied = owner.count;
        } else {
          renter = owner.count;
        }
      });
    }

    // Extract instutitions from fetched data
    if (summary.institution_count) {
      let tempOtherInstitution;
      let tempMomAndPopInstitution;

      for (let i = 0; i < summary.institution_count.length; i++) {
        if (
          summary.institution_count[i].institution != 'None' &&
          summary.institution_count[i].institution != 'Mom & Pop'
        ) {
          if (summary.institution_count[i].institution != 'Other') {
            institutions.push(summary.institution_count[i]);
          } else {
            tempOtherInstitution = {
              institution: 'Other Funds',
              count: summary.institution_count[i].count,
            };
          }
        }
        if (summary.institution_count[i].institution === 'Mom & Pop') {
          tempMomAndPopInstitution = summary.institution_count[i];
        }
      }

      if (tempOtherInstitution) {
        institutions.push(tempOtherInstitution);
      }
      // Move mom and pop to end of institution list
      if (tempMomAndPopInstitution) {
        institutions.push(tempMomAndPopInstitution);
      }
    }
  }

  const resizeObserver = new ResizeObserver((entries) => {
    for (const entry of entries) {
      if (entry.contentRect) {
        setInstitutionContainerWidth(entry.contentRect.width);
      }
    }
  });

  useEffect(() => {
    if (institutionContainer != null && institutionContainer.current != null) {
      resizeObserver.observe(institutionContainer.current);
    }
  });

  // useEffect(() => {
  //   console.log(
  //     'institutions ue',
  //     props.eventCoordinates,
  //     props.currentRadiusMile,
  //   );
  //   const fetchOthersDetails = async () => {
  //     if (props.eventCoordinates.length > 0) {
  //       const result = await getOtherOwnersDetails(
  //         {
  //           lat: props.eventCoordinates[1],
  //           lng: props.eventCoordinates[0],
  //         },
  //         props.currentRadiusMile * 1609.34,
  //       );
  //       if (result) {
  //         console.log('fetchOthersDetails', result);
  //         dispatch({
  //           type: 'CMA/saveCMAStates',
  //           payload: {
  //             otherFundsDetails: result,
  //           },
  //         });
  //       }
  //     }
  //   };
  //   fetchOthersDetails();
  // }, [props.eventCoordinates]);
  const [messageApi, contextHolder] = message.useMessage();
  const [copiedText, setCopiedText] = useState(null);

  useEffect(() => {
    if (copiedText) {
      messageApi.open({
        type: 'success',
        content: 'Copied text to clipboard',
      });
      // Clear the copiedText state to allow for future messages
      setCopiedText(null);
    }
  }, [copiedText, messageApi]);

  const handleCopy = (text) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        setCopiedText(text);
      })
      .catch((err) => {
        console.error('Failed to copy text: ', err);
      });
  };

  return (
    <>
      {contextHolder}
      <div key="owner summary wrapper" className={styles.cardWrapper}>
        <Row
          key="owner summary row"
          align="bottom"
          justify="space-between"
          wrap={true}
          gutter={[0, 8]}
        >
          <Col key="title col" className={styles.cardTitleH2}>
            Parcel Owner Summary
          </Col>
          <Col key="data col">
            <Row
              key="data col row wrapper"
              align="middle"
              justify="end"
              gutter={24}
            >
              <Col key="total col">
                <span
                  key="total col label"
                  className={styles.cardSubtitle}
                  style={{ marginRight: 8 }}
                >
                  Total
                </span>
                <span key="total col content" className={styles.cardDataValue}>
                  {totalParcels || '-'}
                </span>
              </Col>
              <Col key="ratio col">
                <span
                  key="ratio col label"
                  className={styles.cardSubtitle}
                  style={{ marginRight: 8 }}
                >
                  Renter/Owner Occupied
                </span>
                <span key="ratio col content" className={styles.cardDataValue}>
                  {occupied && renter
                    ? `${renter}(${Math.round(
                        (renter / totalParcels) * 100,
                      )}%) / ${occupied}(${Math.round(
                        (occupied / totalParcels) * 100,
                      )}%)`
                    : '-'}
                </span>
              </Col>
            </Row>
          </Col>
        </Row>
        {institutions && institutions.length > 0 && (
          <>
            <div key="divider 1" className={styles.dividerCardHeader} />
            <p key="ratio label" style={{ margin: 0, marginTop: '10px' }}>
              % Based on renter occupied
            </p>
            <div key="wrapper" style={{ padding: '0 10px' }}>
              <div
                key="inner wrapper"
                ref={institutionContainer}
                id="test-int"
                style={{
                  display: 'grid',
                  gridTemplateColumns:
                    institutionContainerWidth > 660
                      ? 'repeat(8, minmax(0, 1fr))'
                      : 'repeat(4, minmax(0, 1fr))',
                }}
              >
                {institutions.map((institution) => {
                  console.log('institution', institution);
                  if (institution.institution === 'Other Funds') {
                    return (
                      <Popover
                        key={institution.institution}
                        content={
                          <div
                            style={{
                              maxHeight: '300px',
                              overflowY: 'auto',
                              width: '400px',
                            }}
                          >
                            {props.otherFundsDetails.map((el, index) => {
                              let name = '';
                              let address = '';

                              try {
                                // Remove curly braces and split by comma, then take the first element
                                const namesArray = el.name
                                  .replace(/^{|}$/g, '')
                                  .split(',');
                                name = namesArray[0]
                                  .replace(/^"|"$/g, '')
                                  .trim();
                              } catch (error) {
                                console.error('Error parsing name:', error);
                                name = el.name
                                  .replace(/^{|}$/g, '')
                                  .replace(/^"|"$/g, '')
                                  .trim();
                              }

                              try {
                                const addressesArray = JSON.parse(
                                  el.address.replace(/^{|}$/g, ''),
                                );
                                address = Array.isArray(addressesArray)
                                  ? addressesArray[0]
                                  : addressesArray;
                              } catch (error) {
                                console.error('Error parsing address:', error);
                                address = el.address
                                  .replace(/^{|}$/g, '')
                                  .replace(/^"|"$/g, '')
                                  .trim();
                              }

                              const stringToCopy = `${name} - ${address}`;

                              return (
                                <Tooltip
                                  key={index}
                                  title={
                                    <>
                                      <p>{address}</p>
                                    </>
                                  }
                                >
                                  <p
                                    style={{
                                      margin: '5px 0',
                                      cursor: 'pointer',
                                    }}
                                    onClick={() => handleCopy(stringToCopy)}
                                  >
                                    {name}: {el.count}
                                  </p>
                                </Tooltip>
                              );
                            })}
                          </div>
                        }
                        title="Other Funds Details (click the name to copy)"
                        trigger="click"
                      >
                        <div
                          style={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            paddingTop: '20px',
                            cursor: 'pointer',
                          }}
                        >
                          <Progress
                            id={`${
                              institution.institution.split(' ')[0]
                            }_progress_donut`}
                            type="circle"
                            percent={(institution.count / renter) * 100}
                            width={80}
                            format={() => `${institution.count}`}
                            strokeColor={getStrokeColor(
                              institution.institution,
                            )}
                          />
                          <p
                            style={{
                              width: '80px',
                              textAlign: 'center',
                              margin: 0,
                            }}
                          >
                            {`Other Funds (${(
                              (institution.count / renter) *
                              100
                            ).toFixed(1)}%)`}
                          </p>
                        </div>
                      </Popover>
                    );
                  }
                  return (
                    <div
                      key={institution.institution}
                      style={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        paddingTop: '20px',
                      }}
                    >
                      <Progress
                        id={`${
                          institution.institution.split(' ')[0]
                        }_progress_donut`}
                        type="circle"
                        percent={(institution.count / renter) * 100}
                        width={80}
                        format={() => `${institution.count}`}
                        strokeColor={getStrokeColor(institution.institution)}
                      />
                      <p
                        style={{
                          width: '80px',
                          textAlign: 'center',
                          margin: 0,
                        }}
                      >
                        {`${
                          institution.institution.includes('AH4R')
                            ? 'AMH'
                            : institution.institution === 'Cerberus'
                            ? 'Firstkey'
                            : institution.institution
                        } (${((institution.count / renter) * 100).toFixed(
                          1,
                        )}%)`}
                      </p>
                    </div>
                  );
                })}
              </div>
            </div>
          </>
        )}
      </div>
    </>
  );
});

export default ParcelOwnerSummary;
