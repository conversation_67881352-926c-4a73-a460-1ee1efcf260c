import { getMLSListingSummaryPointWithinLayerData } from '@/services/data';
import { Checkbox, Input, Space, Table } from 'antd';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import { formatter } from '../../../utils/money';
import styles from '../resultTable.css';

// TODO: BAD IMPLEMENTATION
// Need to refactor API to receive lat/lng instead of zipcode
// So it is just 1 API call instead of 2
function FairMarketRent() {
  const map = useSelector((state) => state.CMA.map);
  const eventCoordinates = useSelector((state) => state.CMA.eventCoordinates);
  const marketRentCompare = useSelector((state) => state.CMA.marketRentCompare);
  const affordableHousingBedroomsRent = useSelector(
    (state) => state.CMA.affordableHousingBedroomsRent,
  );
  const subjectPropertyParcelData = useSelector(
    (state) => state.CMA.subjectPropertyParcelData,
  );
  const dispatch = useDispatch();

  const [loading, setLoading] = useState(false);
  const [lastZIPCODE, setLastZIPCODE] = useState(null);
  const [showPercentage, setShowPercentage] = useState(false);
  const [percentageValue, setPercentageValue] = useState(80);
  useEffect(() => {
    if (!eventCoordinates || !eventCoordinates.length) return;

    const getMarketRentCompare = async () => {
      const data = await getMLSListingSummaryPointWithinLayerData({
        lng: eventCoordinates[0],
        lat: eventCoordinates[1],
      });

      if (data && data.length > 0) {
        const zipCodeData = data.find(
          (ctx) => ctx.type.indexOf('zipcode') > -1,
        );
        const zipCode = zipCodeData ? zipCodeData.key : null;

        if (
          zipCode &&
          (!lastZIPCODE || (lastZIPCODE && lastZIPCODE != zipCode))
        ) {
          setLoading(true);
          setLastZIPCODE(zipCode);

          dispatch({
            type: 'CMA/getMarketRentCompare',
            payload: {
              zipcode: zipCode,
            },
          }).then(() => {
            setLoading(false);
          });
        }
      }
    };

    getMarketRentCompare();
  }, [eventCoordinates]);

  useEffect(() => {
    if (!map) return;

    const clear = () => {
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          marketRentCompare: [],
        },
      });
      setLastZIPCODE(null);
    };

    map.on('selectRadius.clear', () => {
      clear();
    });

    map.on('mapDraw.clear', () => {
      clear();
    });
  }, [map]);

  const columns = [
    {
      title: 'Number of Bedrooms',
      dataIndex: 'bedrooms',
      key: 'bedrooms',
      width: 100,
    },
    {
      title: () => (
        <Space direction="vertical" size="small">
          <div>Fair Market Rent By Zip Code</div>
          <Space>
            <Checkbox
              checked={showPercentage}
              onChange={(e) => setShowPercentage(e.target.checked)}
              className="text-xs"
            >
              Show Percentage
            </Checkbox>
            {showPercentage && (
              <Input
                type="number"
                value={percentageValue}
                onChange={(e) => setPercentageValue(Number(e.target.value))}
                style={{ width: 85 }}
                suffix="%"
                min={0}
                max={100}
              />
            )}
          </Space>
        </Space>
      ),
      dataIndex: 'fairMarketRent',
      key: 'fairMarketRent',
      render: (value) => {
        if (!value) return '-';
        if (!showPercentage) return value;

        const numericValue = parseFloat(value.replace(/[$,]/g, ''));
        const percentageAmount = (numericValue * percentageValue) / 100;
        return `${value} ($${percentageAmount.toFixed(0).toLocaleString()})`;
      },
    },
    {
      title: 'Affordable Housing Rent',
      dataIndex: '_',
      key: '_',
      render: (text, record) => {
        if (!affordableHousingBedroomsRent) return;
        else
          return affordableHousingBedroomsRent[record.bedrooms]
            ? affordableHousingBedroomsRent[record.bedrooms]
            : '-';
      },
    },
    {
      title: 'MLS Close Price',
      dataIndex: 'avgClosePrice',
      key: 'avgClosePrice',
    },

    {
      title: 'MLS Close Count',
      dataIndex: 'closeCount',
      key: 'closeCount',
    },
    {
      title: 'Ratio (MLS / FMR)',
      dataIndex: 'ratio',
      key: 'ratio',
    },
  ];
  useEffect(() => {
    console.log('marketRentCompare', marketRentCompare);
  }, [marketRentCompare]);
  return (
    <div className={styles.cardWrapper}>
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}
      >
        <div className={styles.cardTitleH2}>HUD Fair Market Comparison</div>
      </div>
      {(marketRentCompare.length > 0 || loading) && (
        <div>
          <div
            key="divider 1"
            className={styles.dividerCardHeader}
            style={{ marginBottom: '10px' }}
          />
          <div>
            <Table
              id="fairmarketrent-table"
              rowKey={(record) => record.key}
              columns={columns}
              pagination={false}
              loading={loading}
              rowClassName={(record, index) => {
                if (
                  subjectPropertyParcelData &&
                  subjectPropertyParcelData.beds_count
                ) {
                  if (
                    record.bedrooms === subjectPropertyParcelData.beds_count
                  ) {
                    return styles.marketConditionGrayRow;
                  }
                }
              }}
              dataSource={
                marketRentCompare && marketRentCompare.length > 0
                  ? marketRentCompare
                  : []
              }
            />
          </div>
        </div>
      )}
    </div>
  );
}

export default FairMarketRent;
