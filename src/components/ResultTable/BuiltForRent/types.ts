// types.ts
export interface DataType {
  key: React.Key;
  fips: string;
  apn: string;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  latitude: string;
  longitude: string;
  owner_name: string;
  yearbuilt: number;
  bed: number;
  bath: number;
  size: number;
  watersource: any;
  first_entry_timestamp: any;
  lot_size: any;
  distance: number;
  closedate: any;
  closeprice: any;
  contractdate: any;
  currentprice: any;
  dom: any;
  county: any;
  garage: any;
  levels: any;
  offmarketdate: any;
  originalprice: any;
  pool: any;
  propertysubtype: string;
  propertytype: any;
  status: string;
  subdivision: any;
  metro: any;
  modificationtimestamp: any;
  placekey: string;
  listingkey: any;
  cdom: number;
  sfr_owner: any;
  deed_last_sale_date: Date;
  deed_last_sale_price: number;
  building_type: string;
}

export interface TableFilters {
  status?: string;
  minBeds?: number;
  maxBeds?: number;
  minBaths?: number;
  maxBaths?: number;
  minSqft?: number;
  maxSqft?: number;
}
