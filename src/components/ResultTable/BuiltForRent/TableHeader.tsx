// TableHeader.tsx (updated)
import styles from '@/components/ResultTable/resultTable.css';
import { formatCurrency } from '@/utils/lastSalePublicRecord';
import { SettingOutlined } from '@ant-design/icons';
import { Button, Col, Row, Tooltip } from 'antd';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'umi';
import ColumnManagerModal, { ColumnConfig } from '../ColumnManagerModal';

interface TableHeaderProps {
  selectedRowKeys: React.Key[];
  columns?: ColumnConfig[];
  defaultColumns?: ColumnConfig[];
  onColumnsChange?: (columns: ColumnConfig[]) => void;
}

export const TableHeader: React.FC<TableHeaderProps> = ({
  selectedRowKeys,
  columns = [],
  defaultColumns = [],
  onColumnsChange = () => {},
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);

  const handleOk = (updatedColumns: ColumnConfig[]) => {
    onColumnsChange(updatedColumns);
    setIsModalVisible(false);
  };

  const { builtForRentSelectedRowKeys, builtForRentMedianPrice } = useSelector(
    (state: any) => state.CMA,
  );

  return (
    <>
      {/* <div className="flex justify-between items-end mb-3">
        <strong className="text-base font-medium">Built For Rent</strong>
        <div className="flex items-center gap-6">
          
          <div className="text-sm">
            <div className="flex gap-2">
              <span className="font-normal">Total Selected:</span>
              <span className="font-medium">
                {builtForRentSelectedRowKeys.length || '-'}
              </span>
            </div>
          </div>
          <Tooltip title="Column Customization">
            <Button
              icon={<SettingOutlined />}
              onClick={() => setIsModalVisible(true)}
            />
          </Tooltip>
        </div>
      </div> */}
      <Row
        id="BFRTableHeader"
        key="table title row BFR"
        align="bottom"
        justify="space-between"
        wrap={true}
        gutter={[0, 8]}
        style={{ marginBottom: 12 }}
      >
        <Col key="table title" className={styles.cardTitleH2}>
          Built For Rent
        </Col>
        <Col key="bfr  summary row wrapper">
          <Row key="bfr  summary row" align="middle" justify="end" gutter={24}>
            <Col key="total unit">
              <span
                key="total unit text"
                className={styles.cardSubtitle}
                style={{ marginRight: 8 }}
              >
                Total Selected
              </span>
              <span
                id="bfr Table-Total"
                key="total unit number"
                className={styles.cardDataValue}
              >
                {builtForRentSelectedRowKeys.length || '-'}
              </span>
            </Col>
            <Col key="bfr  avg">
              <span
                key="bfr  avg text"
                className={styles.cardSubtitle}
                style={{ marginRight: 8 }}
              >
                Median Rent
              </span>
              <span
                id="bfr Table-Median"
                key="bfr  avg number"
                className={styles.cardDataValue}
              >
                {builtForRentMedianPrice
                  ? formatCurrency(builtForRentMedianPrice)
                  : '-'}
              </span>
            </Col>
            <Col>
              <Tooltip title="Column Customization">
                <Button
                  icon={<SettingOutlined />}
                  onClick={() => setIsModalVisible(true)}
                ></Button>
              </Tooltip>
            </Col>
          </Row>
        </Col>
      </Row>
      <ColumnManagerModal
        visible={isModalVisible}
        columns={columns}
        defaultColumns={defaultColumns}
        onOk={handleOk}
        onCancel={() => setIsModalVisible(false)}
      />
    </>
  );
};
