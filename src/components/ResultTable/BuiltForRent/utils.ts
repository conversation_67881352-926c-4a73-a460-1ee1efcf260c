// utils.ts
export const formatCurrency = (value: number): string => {
  if (!value) return '';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
};

export const formatter = (num: number): string => {
  return Math.abs(num).toLocaleString();
};

export const toTitleCase = (str: string | null): string => {
  if (!str) return '';
  return str
    .split(' ')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

export const simplifyPropertySubtypes = (
  data: any[],
): { text: string; value: string }[] => {
  const uniqueSubtypes = [...new Set(data.map((item) => item.propertysubtype))];
  return uniqueSubtypes
    .filter((subtype) => subtype)
    .map((subtype) => ({
      text: toTitleCase(subtype),
      value: subtype,
    }));
};
