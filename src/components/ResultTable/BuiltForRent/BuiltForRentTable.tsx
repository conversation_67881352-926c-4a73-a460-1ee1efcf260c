import { arrayDifference } from '@/utils/arrayMethods';
import { Table } from 'antd';
import { isEqual } from 'lodash';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import { ColumnConfig } from '../ColumnManagerModal';
import { NON_DISCLOSURE_STATES } from '../LastSalePublicRecord/Table';
import styles from '../resultTable.css';
import { useTableColumns } from './hooks/useTableColumns';
import { useTableData } from './hooks/useTableData';
import { TableHeader } from './TableHeader';
import { DataType } from './types';

const BuiltForRentTable: React.FC = () => {
  const dispatch = useDispatch();
  const {
    builtForRentData,
    currentStatusMLS,
    minBeds,
    maxBeds,
    minBaths,
    maxBaths,
    minSqft,
    maxSqft,
    builtForRentSelectedRowKeys,
    currentPropertyAddress,
    builtForRentMedianPrice,
  } = useSelector((state: any) => state.CMA);

  // Track current table data
  const [currentTableData, setCurrentTableData] = useState<DataType[]>([]);

  // Track unselected row keys instead of selected ones
  const [unselectedRowKeys, setUnselectedRowKeys] = useState<React.Key[]>([]);

  // Track previous property address for reset
  const [prevPropertyAddress, setPrevPropertyAddress] = useState<string>(
    currentPropertyAddress,
  );

  // Store table filters
  const [tableFilters, setTableFilters] = useState<any>({});

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const prevStatusRef = useRef(currentStatusMLS);
  const isInitialMount = useRef(true);

  const showPrices = builtForRentData[0]?.state
    ? !NON_DISCLOSURE_STATES.includes(builtForRentData[0].state)
    : false;

  // Clear unselected row keys for new subject property
  if (!isEqual(currentPropertyAddress, prevPropertyAddress)) {
    setPrevPropertyAddress(currentPropertyAddress);
    setUnselectedRowKeys([]);
  }

  // Memoize the filters object to prevent new references on every render
  const filters = useMemo(
    () => ({
      status: currentStatusMLS,
      minBeds,
      maxBeds,
      minBaths,
      maxBaths,
      minSqft,
      maxSqft,
    }),
    [currentStatusMLS, minBeds, maxBeds, minBaths, maxBaths, minSqft, maxSqft],
  );

  const allColumns = useTableColumns(builtForRentData, showPrices);
  const { filteredData, loading } = useTableData(builtForRentData, filters);

  // Get current table data and calculate selected rows from unselected ones
  const getCurrentTableData = () => {
    // Apply any additional filters if needed
    const currentData = filteredData;

    // Calculate selected row keys as difference between all rows and unselected rows
    const selectedRowKeys = arrayDifference(
      currentData.map((row) => row.apn),
      unselectedRowKeys,
    );

    return { currentTableData: currentData, selectedRowKeys };
  };

  // Handle row selection change
  const handleRowSelection = (selectedRowKeys: React.Key[]) => {
    // Calculate unselected row keys based on currently visible data
    const unselectedRowKeys = arrayDifference(
      currentTableData.map((row) => row.apn),
      selectedRowKeys,
    );

    setUnselectedRowKeys(unselectedRowKeys);

    dispatch({
      type: 'CMA/saveCMAStates',
      payload: { builtForRentSelectedRowKeys: selectedRowKeys },
    });
  };

  useEffect(() => {
    console.log('builtForRentSelectedRowKeys', builtForRentSelectedRowKeys);
  }, [builtForRentSelectedRowKeys]);

  // Update current table data and selection when filtered data changes
  useEffect(() => {
    if (filteredData.length > 0) {
      // Update current table data
      setCurrentTableData(filteredData);

      // Skip the first render to avoid selection reset
      if (isInitialMount.current) {
        isInitialMount.current = false;

        // Select all rows by default on first render
        const allRowKeys = filteredData.map((row) => row.apn);
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: { builtForRentSelectedRowKeys: allRowKeys },
        });
        return;
      }

      // Get the selected keys based on current data and unselected keys
      const { selectedRowKeys } = getCurrentTableData();

      // If current status changed, consider adjusting the selection
      const filterChanged = currentStatusMLS !== prevStatusRef.current;

      // Only update if necessary to avoid loops
      const shouldUpdateSelection =
        filterChanged ||
        builtForRentSelectedRowKeys.length === 0 ||
        !isEqual(selectedRowKeys.sort(), builtForRentSelectedRowKeys.sort());

      if (shouldUpdateSelection) {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: { builtForRentSelectedRowKeys: selectedRowKeys },
        });
      }

      // Store current status for next comparison
      prevStatusRef.current = currentStatusMLS;
    }
  }, [filteredData, currentStatusMLS]);

  useEffect(() => {
    const selectedRows = filteredData.filter((row) =>
      builtForRentSelectedRowKeys.includes(row.apn),
    );

    const rentPrices = selectedRows
      .map((row) => row.currentprice)
      .filter((price) => price != null && !isNaN(price))
      .sort((a, b) => a - b);

    let medianPrice = 0;
    if (rentPrices.length > 0) {
      const mid = Math.floor(rentPrices.length / 2);
      medianPrice =
        rentPrices.length % 2 === 0
          ? (rentPrices[mid - 1] + rentPrices[mid]) / 2
          : rentPrices[mid];
    }

    // Dispatch the calculated median price
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        builtForRentMedianPrice: medianPrice,
      },
    });
  }, [builtForRentSelectedRowKeys, filteredData, dispatch]);

  // Prepare default columns based on whether to show prices
  const DEFAULT_COLUMNS: ColumnConfig[] = [
    { title: 'Address', key: 'address', visible: true },
    { title: 'Dist.', key: 'distance', visible: true },
    { title: 'Status', key: 'status', visible: true },
    { title: 'Rent', key: 'currentprice', visible: true },
    { title: 'Owner', key: 'owner_name', visible: true },
    { title: 'Closed Date', key: 'closedate', visible: true },
    { title: 'Type', key: 'propertysubtype', visible: true },
    { title: 'Building Type', key: 'building_type', visible: true },
    { title: 'Bd', key: 'bed', visible: true },
    { title: 'Ba', key: 'bath', visible: true },
    { title: 'Sqft', key: 'size', visible: true },
    { title: 'CDOM', key: 'cdom', visible: true },
  ];

  const PRICE_COLUMNS: ColumnConfig[] = [
    { title: 'Last Sale Price', key: 'deed_last_sale_price', visible: true },
    { title: 'Chg. $(%)', key: 'price_change', visible: true },
  ];

  const defaultColumns = showPrices
    ? [...DEFAULT_COLUMNS, ...PRICE_COLUMNS]
    : DEFAULT_COLUMNS;

  // State for managing visible columns
  const [visibleColumns, setVisibleColumns] =
    useState<ColumnConfig[]>(defaultColumns);

  // Update visible columns when default columns change (e.g., when showPrices changes)
  useEffect(() => {
    setVisibleColumns(defaultColumns);
  }, [JSON.stringify(defaultColumns)]);

  // Handle column changes from the modal
  const handleColumnsChange = (updatedColumns: ColumnConfig[]) => {
    setVisibleColumns(updatedColumns);
  };

  // Filter and reorder table columns based on visibility settings
  const filteredColumns = visibleColumns
    .filter((config) => config.visible)
    .map((config) =>
      allColumns.find((column) => {
        const dataIndex = 'dataIndex' in column ? column.dataIndex : null;
        return dataIndex === config.key;
      }),
    )
    .filter((column) => column !== undefined);

  return (
    <div className={styles.cardWrapper}>
      <TableHeader
        selectedRowKeys={builtForRentSelectedRowKeys}
        columns={visibleColumns}
        defaultColumns={defaultColumns}
        onColumnsChange={handleColumnsChange}
      />
      <Table
        className="table-sticky-title-header"
        loading={loading}
        rowKey={(record) => record.apn}
        columns={filteredColumns}
        dataSource={filteredData}
        bordered
        size="small"
        onChange={(
          pagination,
          filters,
          sorter,
          { action, currentDataSource },
        ) => {
          if (!isEqual(currentDataSource, currentTableData)) {
            setCurrentTableData(currentDataSource);
          }

          setTableFilters(filters);
        }}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          onChange: (page, pageSize) => {
            setCurrentPage(page);
            setPageSize(pageSize);
          },
        }}
        sticky
        scroll={{ x: '50vw', y: '600px' }}
        rowSelection={{
          selectedRowKeys: builtForRentSelectedRowKeys,
          onChange: handleRowSelection,
          selections: [
            Table.SELECTION_ALL,
            Table.SELECTION_INVERT,
            Table.SELECTION_NONE,
          ],
        }}
        onRow={(record) => ({
          onMouseEnter: () => {
            dispatch({
              type: 'CMA/saveCMAStates',
              payload: {
                builtForRentTableRowHover: `${record.fips} ${record.apn}`,
              },
            });
          },
          onMouseLeave: () => {
            dispatch({
              type: 'CMA/saveCMAStates',
              payload: {
                builtForRentTableRowHover: null,
              },
            });
          },
          onClick: () => {
            if (record.longitude && record.latitude) {
              // If map exists, fly to the location
              const map = window.map || null;
              if (map) {
                map.flyTo({
                  center: [record.longitude, record.latitude],
                  zoom: 16,
                  speed: 2,
                  curve: 1,
                  easing: (t: any) => t,
                });
              }
            }
          },
        })}
        rowClassName={() => styles.propertyDataTableRow}
      />
    </div>
  );
};

export default BuiltForRentTable;
