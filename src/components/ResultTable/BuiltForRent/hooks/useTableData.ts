import { useEffect, useMemo, useState } from 'react';
import { DataType, TableFilters } from '../types';

export const useTableData = (rawData: DataType[], filters: TableFilters) => {
  // Use useMemo to compute filtered data efficiently
  const filteredData = useMemo(() => {
    let newData = [...rawData];

    if (filters.status && filters.status !== 'status') {
      newData = newData.filter((item) => filters.status!.includes(item.status));
    }

    if (filters.minBeds !== undefined) {
      newData = newData.filter((item) => item.bed >= filters.minBeds!);
    }

    if (filters.maxBeds !== undefined) {
      newData = newData.filter((item) => item.bed <= filters.maxBeds!);
    }

    if (filters.minBaths !== undefined) {
      newData = newData.filter((item) => item.bath >= filters.minBaths!);
    }

    if (filters.maxBaths !== undefined) {
      newData = newData.filter((item) => item.bath <= filters.maxBaths!);
    }

    if (filters.minSqft !== undefined) {
      newData = newData.filter((item) => item.size >= filters.minSqft!);
    }

    if (filters.maxSqft !== undefined) {
      newData = newData.filter((item) => item.size <= filters.maxSqft!);
    }

    return newData;
  }, [rawData, filters]);

  // Only include loading state if there's actual async work (e.g., fetching data)
  const [loading, setLoading] = useState(false);

  // If you need to simulate async behavior, keep this useEffect
  useEffect(() => {
    setLoading(true);
    // Simulate async work if necessary
    const timer = setTimeout(() => setLoading(false), 0); // Adjust delay if needed
    return () => clearTimeout(timer);
  }, [filteredData]);

  return { filteredData, loading };
};
