// hooks/useTableColumns.ts
import { ColumnsType } from 'antd/es/table';
import { DataType } from '../types';
import {
  formatCurrency,
  formatter,
  simplifyPropertySubtypes,
  toTitleCase,
} from '../utils';

export const useTableColumns = (
  data: DataType[],
  showPrices: boolean,
): ColumnsType<DataType> => {
  const baseColumns: ColumnsType<DataType> = [
    {
      title: 'Address',
      dataIndex: 'address',
      width: 200,
      fixed: 'left',
      render: (text, record) => (
        <p>{`${toTitleCase(text)}, ${toTitleCase(record.city)}, ${
          record.state
        }`}</p>
      ),
    },
    {
      title: 'Dist.',
      dataIndex: 'distance',
      width: 55,
      align: 'center',
      render: (text) => <p>{((text * 100) / 1609).toFixed(1)} mi</p>,
      defaultSortOrder: 'ascend',
      sorter: (a, b) => a.distance - b.distance,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      width: 75,
      align: 'center',
      render: (text: string) => <p>{text}</p>,
    },
    {
      title: 'Rent',
      dataIndex: 'currentprice',
      width: 75,
      align: 'left',
      render: (text: number) => <p>{text ? formatCurrency(text) : ''}</p>,
      sorter: (a, b) => {
        if (!a.currentprice && !b.currentprice) return 0;
        if (!a.currentprice) return -1;
        if (!b.currentprice) return 1;
        return a.currentprice - b.currentprice;
      },
    },
    {
      title: 'Owner',
      dataIndex: 'owner_name',
      width: 100,
      align: 'center',
      render: (text: string) => <p>{text}</p>,
    },
    {
      title: 'Closed Date',
      dataIndex: 'closedate',
      width: 100,
      align: 'center',
      sorter: (a, b) => Date.parse(a.closedate) - Date.parse(b.closedate),
      render: (text: any) => <p>{text}</p>,
    },
    {
      title: 'Type',
      dataIndex: 'propertysubtype',
      width: 150,
      align: 'left',
      render: (text: string) => <p>{toTitleCase(text)}</p>,
      filters: simplifyPropertySubtypes(data),
      onFilter: (value: string, record) =>
        record.propertysubtype.indexOf(value) === 0,
    },
    {
      title: 'Building Type',
      dataIndex: 'building_type',
      width: 100,
      align: 'center',
      render: (text: string) => <p>{text}</p>,
    },
    {
      title: 'Bd',
      dataIndex: 'bed',
      width: 70,
      align: 'center',
      render: (text: number) => <p>{text}</p>,
    },
    {
      title: 'Ba',
      dataIndex: 'bath',
      width: 70,
      align: 'center',
      render: (text: number) => <p>{text}</p>,
    },
    {
      title: 'Sqft',
      dataIndex: 'size',
      width: 55,
      align: 'center',
      render: (text: number) => (
        <p>{new Intl.NumberFormat('en-US').format(text)}</p>
      ),
      sorter: (a, b) => a.size - b.size,
    },
    {
      title: 'CDOM',
      dataIndex: 'cdom',
      width: 70,
      align: 'center',
      render: (text: number) => <p>{text}</p>,
    },
  ];

  const priceColumns: ColumnsType<DataType> = [
    {
      title: 'Last Sale Price',
      dataIndex: 'deed_last_sale_price',
      width: 100,
      align: 'center',
      render: (text: number) => <p>{formatCurrency(text)}</p>,
      sorter: (a, b) => a.deed_last_sale_price - b.deed_last_sale_price,
    },
    {
      title: 'Chg. $(%)',
      showSorterTooltip: {
        title: 'Total cumulative change since original listing',
      },
      dataIndex: 'latestPrice',
      width: 95,
      align: 'center',
      render: (_, record) => {
        const change = record.currentprice - record.originalprice;
        const changePercent = Math.round((change / record.originalprice) * 100);

        if (record.currentprice === null || record.originalprice === null) {
          return <span></span>;
        }

        if (change > 0 && isFinite(changePercent)) {
          return (
            <span style={{ color: '#3f8600' }}>
              +${formatter(change)}
              <br />
              (+{changePercent}%)
            </span>
          );
        }

        if (change < 0 && isFinite(changePercent)) {
          return (
            <span style={{ color: '#cf1322' }}>
              -${formatter(Math.abs(change))}
              <br />({changePercent}%)
            </span>
          );
        }

        return <span>$0(0%)</span>;
      },
      sorter: (a, b) => {
        const aChange = a.currentprice - a.originalprice;
        const bChange = b.currentprice - b.originalprice;
        return aChange - bChange;
      },
    },
  ];

  return showPrices ? [...baseColumns, ...priceColumns] : baseColumns;
};
