import styles from '@/components/ResultTable/resultTable.css';
import { showSFROperatorsFullName } from '@/components/SFRBrandConvertFunction';
import {
  getCityCodeViaMetroName,
  getMetroNameForParam,
} from '@/utils/geography';
import { formatter } from '@/utils/money';
import { capitalize, sortString } from '@/utils/strings';
import { Col, Row, Table } from 'antd';
import isEmpty from 'lodash.isempty';
import isEqual from 'lodash.isequal';
import moment from 'moment';
import { useEffect, useRef, useState } from 'react';
import { connect } from 'umi';
import { dateFormat } from '../../../constants';
import { calculateMedian } from '../../../utils/calculations';
import EmptyState from '../../EmptyState';

const mapMLS = (item) => {
  const address =
    capitalize(item.fulladdress.replace(/ /g, ' ') + ', ' + item.city) +
    ', ' +
    item.stateorprovince +
    ' ' +
    item.zipcode;
  return {
    id: item.mlsid,
    listingkey: item.listingkey,
    metro: item.metro,
    address: address,
    distance: item.distance,
    status: item.status,
    brand: '1:MLS',
    price: item.latestPrice,
    type: item.propertysubtype,
    yearbuilt: item.yearbuilt,
    beds: item.bed,
    baths: item.bath,
    sqft: item.size,
    cdom: item.cdom,
    closedate: item.closedate,
    geography: item.geography,
    placekey: item.placekey,
    sfr_owner: item.sfr_owner,
  };
};

const mapSFR = (item) => {
  const address = capitalize(
    item.address.includes(',')
      ? item.address.replace(/,(?! )/g, ', ') // find all comma without a following space, replace with comma and a spce
      : item.address + ', ' + item.standard_city + ', ' + item.standard_state,
  );

  let brand = item.brand;
  if (brand === 'Hotpads') {
    brand = 'z:' + brand;
  }
  return {
    id: item.base_id,
    address: address,
    distance: item.distance,
    status: item.status,
    brand: brand,
    price: item.rent,
    type: item.propertysubtype,
    yearbuilt: item.yearbuilt,
    beds: item.bed_rooms,
    baths: item.bath_rooms,
    sqft: item.square_feet,
    cdom: item.cdom,
    closedate: item.close_date,
    geography: item.geom,
    placekey: item.placekey,
  };
};

const generateColumns = (mapExpandedView) => {
  if (!mapExpandedView) {
    const columns = [
      {
        title: 'Address',
        dataIndex: 'address',
        width: 200,
        align: 'left',
        fixed: 'left',
        render: (text, record) => {
          if (record.brand.includes('MLS')) {
            return (
              <span>
                {text}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  width="16"
                  height="16"
                  className={styles.imageIconForAddress}
                >
                  <path d="M7.479 12.083h8.709l-2.626-3.937-2.208 3.146-1.729-2.188Zm-.646 3.5q-.895 0-1.541-.645-.646-.646-.646-1.542V3.375q0-.896.646-1.542.646-.645 1.541-.645h10.021q.917 0 1.552.645.636.646.636 1.542v10.021q0 .896-.636 1.542-.635.645-1.552.645Zm-3.687 3.688q-.917 0-1.552-.636Q.958 18 .958 17.083V4.875h2.188v12.208h12.208v2.188Z" />
                </svg>
              </span>
            );
          } else {
            const googleLink = `https://www.google.com/search?q=${text
              .trim()
              .replace(/,/g, '%2C')
              .replace(/ /g, '+')}`;
            return (
              <a href={googleLink} className={styles.sfrLink} target="_blank">
                {text}
              </a>
            );
          }
        },
        // ellipsis: true,
      },
      {
        title: 'Dist.',
        // dataIndex: ['properties', 'address'],
        dataIndex: 'distance',
        // width: columnWidthSmaller,
        key: 'distance',
        align: 'center',
        render: (text, record) => {
          //   if (drawnCustomPolygons.length > 0 && eventCoordinates.length > 0) {
          //     const propertyPoint = point(record.geography.coordinates);
          //     const eventPoint = point(eventCoordinates);
          //     const distance = turf_distance(propertyPoint, eventPoint, 'miles');

          //     return distance.toFixed(1) + ' mi';
          //   }
          return (parseFloat(text) / 1609.34).toFixed(1) + ' mi';
        },
        sorter: {
          // if (drawnCustomPolygons.length > 0 && eventCoordinates.length > 0) {
          //   const aPoint = point(a.geography.coordinates);
          //   const bPoint = point(b.geography.coordinates);
          //   const eventPoint = point(eventCoordinates);
          //   const adistance = turf_distance(aPoint, eventPoint, 'miles');
          //   const bdistance = turf_distance(bPoint, eventPoint, 'miles');

          //   return adistance - bdistance;
          // }
          compare: (a, b) => a.distance - b.distance,
          multiple: 1,
        },
        // sortDirections: ['ascend', 'descend'],
        defaultSortOrder: 'ascend',
        // sortOrder: leaseSort.columnKey === 'distance' ? leaseSort.order : null,
        // sortOrder:
        //   leaseMode && leaseSort.columnKey === 'distance'
        //     ? leaseSort.order
        //     : !leaseMode && saleSort.columnKey === 'distance'
        //     ? saleSort.order
        //     : null,
      },
      {
        title: 'Status',
        dataIndex: 'status',
        // width: columnWidthSmall,
        align: 'center',
        sorter: (a, b) => sortString(a.status, b.status),
        // sortDirections: ['ascend', 'descend'],
        // sortOrder:
        //   leaseMode && leaseSort.columnKey === 'status'
        //     ? leaseSort.order
        //     : !leaseMode && saleSort.columnKey === 'status'
        //     ? saleSort.order
        //     : null,
      },
      {
        title: 'Owner',
        // dataIndex: ['properties', 'brand'],
        dataIndex: 'brand',
        width: 100,
        align: 'left',
        render: (text, record) => {
          if (text === '1:MLS') {
            if (record.sfr_owner)
              return showSFROperatorsFullName(record.sfr_owner);
            return 'MLS';
          }
          if (text === 'z:Hotpads') return '3rd Party';
          return showSFROperatorsFullName(text);
        },
        // ellipsis: true,
        sorter: {
          compare: (a, b) => {
            if (a.brand && b.brand) {
              return sortString(a.brand, b.brand);
            }
            return a.brand ? false : true;
          },
          multiple: 2,
        },
      },
      {
        title: 'Rent',
        dataIndex: 'price',
        // width: leaseMode ? null : 100,
        align: 'center',
        render: (text) => '$' + formatter(text),
        sorter: {
          compare: (a, b) => a.price - b.price,
          multiple: 2,
        },
        // sortDirections: ['ascend', 'descend'],
        // sortOrder:
        //   leaseMode && leaseSort.columnKey === 'latestPrice'
        //     ? leaseSort.order
        //     : !leaseMode && saleSort.columnKey === 'latestPrice'
        //     ? saleSort.order
        //     : null,
      },
      {
        title: 'Type',
        dataIndex: 'type',
        // width: 175,
        align: 'center',
        sorter: (a, b) => sortString(a.type, b.type),
        render: (text) => {
          if (
            ['Single Family Residence', 'SINGLE FAMILY RESIDENTIAL'].includes(
              text,
            )
          ) {
            return 'SFR';
          } else if (['Single Family Detached'].includes(text)) {
            return 'SFD';
          } else if (text === 'Townhouse') {
            return 'TH';
          } else {
            return text;
          }
        },
        // sortDirections: ['ascend', 'descend'],
        // sortOrder:
        //   leaseMode && leaseSort.columnKey === 'propertysubtype'
        //     ? leaseSort.order
        //     : !leaseMode && saleSort.columnKey === 'propertysubtype'
        //     ? saleSort.order
        //     : null,
        ellipsis: true,
      },
      {
        title: 'YrB',
        dataIndex: 'yearbuilt',
        key: 'yearbuilt',
        // width: columnWidthSmaller,
        align: 'center',
        sorter: (a, b) => a.yearbuilt - b.yearbuilt,
        // sortDirections: ['descend', 'ascend'],
        // sortOrder:
        //   leaseMode && leaseSort.columnKey === 'yearbuilt'
        //     ? leaseSort.order
        //     : !leaseMode && saleSort.columnKey === 'yearbuilt'
        //     ? saleSort.order
        //     : null,
      },
      {
        title: 'Bd',
        dataIndex: 'beds',
        // width: columnWidthSmaller,
        align: 'center',
        sorter: (a, b) => a.beds - b.beds,
        // sortDirections: ['ascend', 'descend'],
        // sortOrder:
        //   leaseMode && leaseSort.columnKey === 'bed'
        //     ? leaseSort.order
        //     : !leaseMode && saleSort.columnKey === 'bed'
        //     ? saleSort.order
        //     : null,
      },
      {
        title: 'Ba',
        dataIndex: 'baths',
        // width: columnWidthSmaller,
        align: 'center',
        sorter: (a, b) => a.baths - b.baths,
        // sortDirections: ['ascend', 'descend'],
        // sortOrder:
        //   leaseMode && leaseSort.columnKey === 'bath'
        //     ? leaseSort.order
        //     : !leaseMode && saleSort.columnKey === 'bath'
        //     ? saleSort.order
        //     : null,
      },
      {
        title: 'Sqft',
        dataIndex: 'sqft',
        // width: columnWidthSmaller,
        align: 'center',
        render: (text) => (text ? formatter(text) : ''),
        sorter: (a, b) => a.sqft - b.sqft,
        // sortDirections: ['ascend', 'descend'],
        // sortOrder:
        //   leaseMode && leaseSort.columnKey === 'size'
        //     ? leaseSort.order
        //     : !leaseMode && saleSort.columnKey === 'size'
        //     ? saleSort.order
        //     : null,
      },
      {
        title: 'CDOM',
        dataIndex: 'cdom',
        key: 'cdom',
        // width: columnWidthSmaller,
        align: 'center',
        sorter: (a, b) => a.cdom - b.cdom,
        // sortDirections: ['ascend', 'descend'],
        // sortOrder:
        //   leaseMode && leaseSort.columnKey === 'cdom'
        //     ? leaseSort.order
        //     : !leaseMode && saleSort.columnKey === 'cdom'
        //     ? saleSort.order
        //     : null,
      },
      {
        title: 'Cls.',
        // dataIndex: ['properties', 'available_date'],
        dataIndex: 'closedate',
        // key: 'closedate',
        // width: columnWidthSmall,
        align: 'center',
        render: (text) => {
          if (text) {
            return moment(text).format(dateFormat);
          } else {
            return '';
          }
        },
        sorter: (a, b) => {
          if (a.closedate && b.closedate) {
            return +moment(a.closedate) - +moment(b.closedate);
          } else {
            return a.closedate || b.closedate;
          }
        },
        // sortOrder:
        //   leaseMode && leaseSort.columnKey === 'closedate'
        //     ? leaseSort.order
        //     : !leaseMode && saleSort.columnKey === 'closedate'
        //     ? saleSort.order
        //     : null,
      },
      {
        title: 'PSF',
        dataIndex: 'price',
        // width: columnWidthSmaller,
        align: 'center',
        render: (text, record) => {
          if (text && record.sqft) {
            return '$' + (text / record.sqft).toFixed(2);
          }
        },
        sorter: (a, b) => a.latestPrice / a.sqft - b.latestPrice / b.sqft,
        // sortDirections: ['ascend', 'descend'],
        // sortOrder:
        //   leaseMode && leaseSort.columnKey === 'psf'
        //     ? leaseSort.order
        //     : !leaseMode && saleSort.columnKey === 'psf'
        //     ? saleSort.order
        //     : null,
      },
    ];

    return columns;
  } else {
    const columns = [
      {
        title: 'Address',
        dataIndex: 'address',
        width: 175,
        align: 'left',
        render: (text, record) => {
          if (record.brand === 'MLS') {
            return (
              <span>
                {text}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  width="16"
                  height="16"
                  className={styles.imageIconForAddress}
                >
                  <path d="M7.479 12.083h8.709l-2.626-3.937-2.208 3.146-1.729-2.188Zm-.646 3.5q-.895 0-1.541-.645-.646-.646-.646-1.542V3.375q0-.896.646-1.542.646-.645 1.541-.645h10.021q.917 0 1.552.645.636.646.636 1.542v10.021q0 .896-.636 1.542-.635.645-1.552.645Zm-3.687 3.688q-.917 0-1.552-.636Q.958 18 .958 17.083V4.875h2.188v12.208h12.208v2.188Z" />
                </svg>
              </span>
            );
          } else {
            const googleLink = `https://www.google.com/search?q=${text
              .trim()
              .replace(/,/g, '%2C')
              .replace(/ /g, '+')}`;
            return (
              <a href={googleLink} className={styles.sfrLink} target="_blank">
                {text}
              </a>
            );
          }
        },
        // ellipsis: true,
      },
      {
        title: 'Rent',
        // dataIndex: ['properties', 'rent'],
        dataIndex: 'price',
        // width: columnWidthSmaller,
        align: 'center',
        render: (text) => '$' + formatter(parseFloat(text).toFixed()),
        sorter: (a, b) => a.price - b.price,
        // sortOrder: tableSorter.columnKey === 'rent' ? tableSorter.order : null,
      },
      {
        title: 'Sqft',
        // dataIndex: ['properties', 'square_feet'],
        dataIndex: 'sqft',
        // width: columnWidthSmaller,
        align: 'center',
        render: (text) => {
          if (text === null) {
            return '';
          } else {
            return formatter(text);
          }
        },
        sorter: (a, b) => a.sqft - b.sqft,
        // sortOrder:
        //   tableSorter.columnKey === 'square_feet' ? tableSorter.order : null,
      },
      {
        title: 'PSF',
        dataIndex: 'price',
        key: 'psf',
        // width: columnWidthSmaller,
        align: 'center',
        render: (text, record) => {
          if (text && record.sqft) {
            return '$' + (text / record.sqft).toFixed(2);
          }
        },
        sorter: (a, b) => a.rent / a.sqft - b.rent / b.sqft,
        // sortOrder: tableSorter.columnKey === 'psf' ? tableSorter.order : null,
      },
    ];
    return columns;
  }
};

function CombinedTable(props) {
  const [dataSource, setDataSource] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [medianPrice, setMedianPrice] = useState(0);
  const [scrollToSelectedRow, setScrollToSelectedRow] = useState(false);

  const combineTableRef = useRef(null);

  console.log('combined: ', dataSource);

  useEffect(() => {
    if (scrollToSelectedRow && combineTableRef.current) {
      const row = combineTableRef.current.querySelector(
        `.${styles.mapLocatePropertySelected}`,
      );

      if (row) {
        row.scrollIntoView({ behavior: 'smooth', block: 'center' });
        setScrollToSelectedRow(false);
      }
    }
  }, [scrollToSelectedRow]);

  useEffect(() => {
    if (!isEmpty(props.mapLocateProperty)) {
      if (props.cmaTabKey !== '1') {
        props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: { cmaTabKey: '1' },
        });
      }
      setTimeout(() => {
        setScrollToSelectedRow(true);
      }, 100);
    }
  }, [props.mapLocateProperty]);

  useEffect(() => {
    const combinedComps = [
      ...props.currentMLSPropertiesFiltered.map((item) => mapMLS(item)),
      ...props.currentNationalOperatorsPropertiesFiltered.map((item) =>
        mapSFR(item),
      ),
      ...props.currentHotPadsPropertiesFiltered.map((item) => mapSFR(item)),
    ];

    // Removing Duplicates
    // use placekey to check, remove MLS if with SFR duplicate
    const filtered = combinedComps.filter((comp) => {
      if (comp.brand.includes('MLS')) {
        const duplicateSFR = combinedComps.find(
          (item) =>
            !item.brand.includes('MLS') && item.placekey === comp.placekey,
        );
        if (duplicateSFR) {
          return false;
        }
      }
      return true;
    });

    setDataSource(filtered);
  }, [
    props.currentMLSPropertiesFiltered,
    props.currentNationalOperatorsPropertiesFiltered,
    props.currentHotPadsPropertiesFiltered,
    props.searchingMode,
  ]);

  // init/unselect from map/original table
  useEffect(() => {
    if (dataSource.length > 0) {
      const newSelectedKeys = [
        ...props.selectedRowKeysHotPads,
        ...props.selectedRowKeysNationalOperators,
        ...props.selectedRowKeysMLSLease,
      ];

      const newSelectedRows = dataSource.filter(({ id }) =>
        newSelectedKeys.includes(id),
      );

      changeSelectedRow(newSelectedKeys, newSelectedRows);
    }
  }, [
    dataSource,
    props.selectedRowKeysHotPads,
    props.selectedRowKeysNationalOperators,
    props.selectedRowKeysMLSLease,
  ]);

  // change from combine table
  const onChangeSelectedRows = (selectedRowKeys, selectedRows) => {
    const selectedMLS = selectedRows.reduce((result, { brand, id }) => {
      if (brand.includes('MLS')) result.push(id);
      return result;
    }, []);
    const selectedHotPads = selectedRows.reduce((result, { brand, id }) => {
      if (brand.includes('Hotpads')) result.push(id);
      return result;
    }, []);
    const selectedSFR = selectedRows.reduce((result, { brand, id }) => {
      if (!brand.includes('MLS') && !brand.includes('Hotpads')) result.push(id);
      return result;
    }, []);

    // change original selected first which then reflects combine table in useEffect
    // since combine table is based on original selected
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        selectedRowKeysHotPads: selectedHotPads,
        selectedRowKeysNationalOperators: selectedSFR,
        selectedRowKeysMLSLease: selectedMLS,
      },
    });
  };

  const changeSelectedRow = (selectedRowKeys, selectedRows) => {
    setSelectedRowKeys(selectedRowKeys);
    const prices = selectedRows
      .filter((item) => selectedRowKeys.includes(item.id))
      .map((item) => item.price);

    const median = calculateMedian(prices);
    setMedianPrice(median);
  };

  const onRow = (record) => {
    return {
      onClick: () => {
        if (record.brand.includes('MLS')) {
          let key = record.listingkey;

          if (
            ['sanantonio', 'san antonio'].includes(
              record.metro.toLowerCase(),
            ) ||
            getMetroNameForParam(record, true) === 'sanantonio'
          ) {
            key = record.id;
          }

          props.dispatch({
            type: 'CMA/getBatchMLSPropertyImages',
            payload: {
              key: key,
              city: getMetroNameForParam(record, true), // true for using realtrac instead of nashville
            },
          });

          props.dispatch({
            type: 'CMA/getHouseDetailsData',
            payload: {
              from: 'MLS Table',
              listingID: record.id,
              cityCode: getCityCodeViaMetroName(record.metro),
            },
          });
          props.dispatch({
            type: 'CMA/getAPNOwnerName',
            payload: {
              from: 'MLS Table',
              placekey: record.placekey,
            },
          });

          const selectedMLS = props.currentMLSPropertiesFiltered.filter(
            (item) => item.mlsid === record.id,
          );

          props.dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              selectedMLProperty: selectedMLS[0],
              openMLSImageModal: true,
            },
          });
        }
      },
      onMouseEnter: () => {
        if (
          !isEqual(
            props.currentHighlightCoordinates,
            record.geography.coordinates,
          )
        ) {
          let brand = record.brand;

          // remove prefixes 1:MLS and z:Hotpads
          if (brand.includes('MLS')) {
            brand = 'MLS';
          } else if (brand.includes('Hotpads')) {
            brand = 'HotPads';
          }
          props.dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              currentHighlightCoordinates: record.geography.coordinates,
              priceHighlightMarker: record.price,
              typeHighlightMarker: brand,
            },
          });
        }
      },
    };
  };

  return (
    <div className={styles.cardWrapperMLS} style={{ marginTop: '16px' }}>
      <Row
        key="table title row MLS"
        align="bottom"
        justify="space-between"
        wrap={true}
        gutter={[0, 8]}
      >
        <Col key="table title" className={styles.cardTitleH2}>
          Combined View
        </Col>
        <Col key="MLS summary row wrapper">
          <Row key="MLS summary row" align="middle" justify="end" gutter={24}>
            <Col key="total unit">
              <span
                key="total unit text"
                className={styles.cardSubtitle}
                style={{ marginRight: 8 }}
              >
                Total
              </span>
              <span key="total unit number" className={styles.cardDataValue}>
                {dataSource.length || '-'}
              </span>
            </Col>
            <Col key="MLS avg">
              <span
                key="MLS avg text"
                className={styles.cardSubtitle}
                style={{ marginRight: 8 }}
              >
                {props.searchingMode === 'Lease'
                  ? 'Median Rent'
                  : 'Median Sale'}
              </span>
              <span key="MLS avg number" className={styles.cardDataValue}>
                {medianPrice ? '$' + formatter(medianPrice) : '-'}
              </span>
            </Col>
          </Row>
        </Col>
      </Row>
      <div key="divider title" className={styles.dividerCardHeader} />
      <Table
        ref={combineTableRef}
        rowKey={(record) => record.id}
        columns={generateColumns(props.mapExpandedView)}
        rowSelection={{
          selectedRowKeys: selectedRowKeys,
          onChange: onChangeSelectedRows,
        }}
        dataSource={dataSource}
        loading={
          !props.fetchCompMLSDone &&
          !props.fetchCompSFRDone &&
          !props.fetchCompHotPadsDone
        } // only show spinner when all 3 are still fetching
        variant={'filled'}
        size="small"
        pagination={false}
        scroll={props.mapExpandedView ? null : { x: 1000 }}
        sticky={{
          getContainer: () =>
            document.getElementById('resultTableScrollWrapper'),
        }}
        // rowClassName={styles.propertyDataTableRow}
        rowClassName={(record, index) => {
          let className = styles.propertyDataTableRow;
          if (!isEmpty(props.mapLocateProperty)) {
            if (record.id === props.mapLocateProperty.id) {
              className += ' ' + styles.mapLocatePropertySelected;
            }
          }
          return className;
        }}
        onRow={onRow}
      />
    </div>
  );
}

export default connect(({ CMA }) => ({
  currentMLSPropertiesFiltered: CMA.currentMLSPropertiesFiltered,
  currentNationalOperatorsPropertiesFiltered:
    CMA.currentNationalOperatorsPropertiesFiltered,
  currentHotPadsPropertiesFiltered: CMA.currentHotPadsPropertiesFiltered,
  searchingMode: CMA.searchingMode,
  mapExpandedView: CMA.mapExpandedView,
  mapLocateProperty: CMA.mapLocateProperty,
  selectedRowKeysHotPads: CMA.selectedRowKeysHotPads,
  selectedRowKeysNationalOperators: CMA.selectedRowKeysNationalOperators,
  selectedRowKeysMLSLease: CMA.selectedRowKeysMLSLease,
  cmaTabKey: CMA.cmaTabKey,
  fetchCompMLSDone: CMA.fetchCompMLSDone,
  fetchCompSFRDone: CMA.fetchCompSFRDone,
  fetchCompHotPadsDone: CMA.fetchCompHotPadsDone,
}))(CombinedTable);
