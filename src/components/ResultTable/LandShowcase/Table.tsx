import { useElementSize } from '@/hooks';
import { getLandShowcase } from '@/services/data';
import { arrayDifference } from '@/utils/arrayMethods';
import { formatCurrency } from '@/utils/lastSalePublicRecord';
import { Table, Tooltip } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { isEmpty, isEqual } from 'lodash';
import { MutableRefObject, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import TableHeader from './TableHeader';
import {
  calculateMedian,
  calculateMedianPricePerAcre,
  calculateMedianPricePerSqFt,
  formatPricePerSqftArce,
  sqftToAcre,
  testLog,
} from './utils/functions';
import { LandShowcaseData } from './utils/type';

const LandShowcaseTable = () => {
  const dispatch = useDispatch();
  const map = useSelector((state: any) => state.CMA.map);
  const landShowcaseData = useSelector(
    (state: any) => state.CMA.landShowcaseData,
  );
  const landShowcaseDataForRender = useSelector(
    (state: any) => state.CMA.landShowcaseDataForRender,
  );
  const landShowcaseSelectedRowKey = useSelector(
    (state: any) => state.CMA.landShowcaseSelectedRowKey,
  );
  const currentPropertyAddress = useSelector(
    (state: any) => state.CMA.currentPropertyAddress,
  );
  const currentRadiusMile = useSelector(
    (state: any) => state.CMA.currentRadiusMile,
  );
  const currentStartMLS = useSelector(
    (state: any) => state.CMA.currentStartMLS,
  );
  const currentEndMLS = useSelector((state: any) => state.CMA.currentEndMLS);
  const compingMode = useSelector((state: any) => state.CMA.compingMode);
  const selectedCompTables = useSelector(
    (state: any) => state.CMA.selectedCompTables,
  );
  const currentStatusMLS = useSelector(
    (state: any) => state.CMA.currentStatusMLS,
  );

  const [statusOptions, setStatusOptions] = useState<any>([]);
  const [propertySubtypeOptions, setPropertySubtypeOptions] = useState<any>();
  const [propertyTypeOptions, setPropertyTypeOptions] = useState<any>();
  const [loading, setLoading] = useState(false);
  const landCompContainer = useRef(null);
  const tableSize = useElementSize(landCompContainer);

  // Track currently displayed table data
  const [currentTableData, setCurrentTableData] = useState<LandShowcaseData[]>(
    [],
  );

  // Track unselected row keys instead of selected ones
  const [unselectedRowKeys, setUnselectedRowKeys] = useState<React.Key[]>([]);

  // Track previous property address for reset
  const [prevPropertyAddress, setPrevPropertyAddress] = useState<string>(
    currentPropertyAddress,
  );

  // Store table filters
  const [tableFilters, setTableFilters] = useState<any>({});

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const columns: ColumnsType<LandShowcaseData> = [
    {
      title: 'Address',
      dataIndex: 'fulladdress',
      key: 'fulladdress',
      width: 180,
      fixed: 'left',
      align: 'left',
      render: (text: string, record: LandShowcaseData) => {
        const tooltipContent = record.photo ? (
          <div>
            <img
              src={record.photo}
              alt={record.fulladdress}
              style={{
                maxWidth: '200px',
                maxHeight: '150px',
                objectFit: 'cover',
              }}
            />
          </div>
        ) : null;

        return (
          <Tooltip
            title={tooltipContent}
            placement="right"
            overlayStyle={{
              maxWidth: 'none',
              padding: 0, // Remove padding from tooltip
              background: 'none', // Remove background
              boxShadow: 'none', // Remove shadow
            }}
            mouseEnterDelay={0.3}
          >
            <p className="text-xs">
              {record.fulladdress}, {record.city}, {record.stateorprovince},{' '}
              {record.zipcode}
            </p>
          </Tooltip>
        );
      },
    },
    {
      title: 'Dist. ',
      dataIndex: 'distance',
      key: 'distance',
      width: 75,
      align: 'left',
      defaultSortOrder: 'ascend',
      sorter: (a: LandShowcaseData, b: LandShowcaseData) =>
        a.distance - b.distance, // Added sorter
      render: (text: any, record: LandShowcaseData) => (
        <p className="text-xs">{(record.distance / 1609.34).toFixed(2)} mi</p>
      ),
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      width: 125,
      align: 'left',
      render: (text: any, record: LandShowcaseData) => (
        <p className="text-xs">
          {record.category === 'for_rent' ? 'For Rent' : 'For Sale'}
        </p>
      ),
      filters: statusOptions,
    },
    {
      title: 'Available Date',
      dataIndex: 'available_date',
      key: 'available_date',
      width: 125,
      align: 'left',
      render: (text: any, record: LandShowcaseData) => (
        <p className="text-xs">{record.available_date}</p>
      ),
      sorter: (a: LandShowcaseData, b: LandShowcaseData) =>
        a.available_date - b.available_date, // Added sorte
    },
    {
      title: 'First Seen',
      dataIndex: 'first_seen',
      key: 'first_seen',
      width: 125,
      align: 'left',
      render: (text: any, record: LandShowcaseData) => (
        <p className="text-xs">{record.first_seen}</p>
      ),
      sorter: (a: LandShowcaseData, b: LandShowcaseData) => {
        const dateA = new Date(a.first_seen);
        const dateB = new Date(b.first_seen);
        return dateA.getTime() - dateB.getTime();
      },
    },
    {
      title: 'Property Type',
      dataIndex: 'propertytype',
      key: 'propertytype',
      width: 100,
      align: 'left',
      render: (text: any, record: LandShowcaseData) => (
        <p className="text-xs">
          {record.propertytype ? record.propertytype : 'N/A'}
        </p>
      ),
      filters: propertyTypeOptions,
    },
    {
      title: 'Property Subtype',
      dataIndex: 'propertysubtype',
      key: 'propertysubtype',
      width: 100,
      align: 'left',
      render: (text: any, record: LandShowcaseData) => (
        <p className="text-xs">
          {record.propertysubtype ? record.propertysubtype : 'N/A'}
        </p>
      ),
      filters: propertySubtypeOptions,
    },
    // {
    //   title: 'Spaces',
    //   dataIndex: 'spaces',
    //   key: 'spaces',
    //   width: 150,
    //   align: 'left',
    //   render: (text: any, record: LandShowcaseData) => {
    //     return (
    //       <p className="text-xs">
    //         {record.min_space} - {record.max_space}
    //       </p>
    //     );
    //   },
    //   sorter: (a: LandShowcaseData, b: LandShowcaseData) =>
    //     a.saleprice - b.saleprice, // Added sorter
    // },
    // {
    //   title: 'Rent',
    //   dataIndex: 'rent',
    //   key: 'rent',
    //   width: 150,
    //   align: 'left',
    //   render: (text: any, record: LandShowcaseData) => {
    //     return (
    //       <p className="text-xs">
    //         {record.min_rent} - {record.max_rent}
    //       </p>
    //     );
    //   },
    // },
    {
      title: 'Price',
      dataIndex: 'saleprice',
      key: 'saleprice',
      width: 100,
      align: 'left',
      render: (text: any, record: LandShowcaseData) => {
        return (
          <p className="text-xs">
            {record.saleprice ? formatCurrency(text) : '-'}
          </p>
        );
      },
      sorter: (a: LandShowcaseData, b: LandShowcaseData) =>
        a.saleprice - b.saleprice, // Added sorter
    },
    {
      title: 'Cap Rate',
      dataIndex: 'cap_rate',
      key: 'cap_rate',
      width: 100,
      align: 'left',
      render: (text: any, record: LandShowcaseData) => {
        return <p className="text-xs">{(text * 100).toFixed(2)}%</p>;
      },
      sorter: (a: LandShowcaseData, b: LandShowcaseData) =>
        a.cap_rate - b.cap_rate, // Added sorter
    },
    {
      title: 'Arces',
      dataIndex: 'sizes',
      key: 'sizes',
      width: 100,
      align: 'left',
      render: (text: any, record: LandShowcaseData) => {
        const sizes = JSON.parse(text);
        return <p className="text-xs">{sizes.AC}</p>;
      },
    },
    {
      title: 'Sqft',
      dataIndex: 'sizes',
      key: 'sizes',
      width: 100,
      align: 'left',
      render: (text: any, record: LandShowcaseData) => {
        const sizes = JSON.parse(text);
        return <p className="text-xs">{sizes.SF}</p>;
      },
    },
  ];

  // Clear unselected row keys for new subject property
  if (!isEqual(currentPropertyAddress, prevPropertyAddress)) {
    setPrevPropertyAddress(currentPropertyAddress);
    setUnselectedRowKeys([]);
  }

  useEffect(() => {
    testLog({
      currentPropertyAddress,
      currentRadiusMile,
      currentStartMLS,
      currentEndMLS,
    });
    const fetchLandShowcase = async () => {
      testLog('fetchLandShowcase');
      testLog(selectedCompTables);

      if (
        !isEmpty(currentPropertyAddress) &&
        currentRadiusMile &&
        currentStartMLS &&
        currentEndMLS
      ) {
        setLoading(true);
        console.log('statusFiltered', {
          lat: currentPropertyAddress.latitude,
          lng: currentPropertyAddress.longitude,
          distance: currentRadiusMile * 1609.344,
        });

        if (currentStatusMLS !== 'status') {
          const result = await getLandShowcase({
            lat: currentPropertyAddress.latitude,
            lng: currentPropertyAddress.longitude,
            distance: currentRadiusMile * 1609.344,
          });
          console.log('statusFiltered', currentStatusMLS);
          console.log('statusFiltered compingMode', compingMode);
          console.log('statusFiltered result', result);
          const startDate = new Date(currentStartMLS); // "2024-10-07"
          const endDate = new Date(currentEndMLS); // "2025-01-07"

          // Set hours to ensure we capture the full days
          startDate.setHours(0, 0, 0, 0);
          endDate.setHours(23, 59, 59, 999);

          const dateFiltered = result?.filter((land: LandShowcaseData) => {
            const firstSeen = new Date(land.first_seen);
            return firstSeen >= startDate && firstSeen <= endDate;
          });
          // console.log('statusFiltered', statusFiltered);
          dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              landShowcaseData: result,
              landShowcaseDataForRender: dateFiltered,
              showAVM: false,
            },
          });
        } else {
          const result = await getLandShowcase({
            lat: currentPropertyAddress.latitude,
            lng: currentPropertyAddress.longitude,
            distance: currentRadiusMile * 1609.344,
          });
          dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              landShowcaseData: result,
              landShowcaseDataForRender: result,
              showAVM: false,
            },
          });
        }

        setLoading(false);
      }
    };

    fetchLandShowcase();
  }, [
    currentPropertyAddress,
    currentRadiusMile,
    currentStartMLS,
    currentEndMLS,
  ]);

  // Get current table data and calculate selected rows from unselected ones
  const getCurrentTableData = () => {
    // remove filters that are null, e.g. { propertysubtype: null }
    const nonNullFilters = Object.fromEntries(
      Object.entries(tableFilters).filter(([_, value]) => value !== null),
    );

    // filter the data based on current filters
    const currentTableData = landShowcaseDataForRender.filter((row) => {
      return Object.keys(nonNullFilters).every((filter) => {
        return nonNullFilters[filter].includes(row[filter]);
      });
    });

    // calculate selected row keys as difference between all rows and unselected rows
    const selectedRowKeys = arrayDifference(
      currentTableData.map((row) => row.base_id),
      unselectedRowKeys,
    );

    return { currentTableData, selectedRowKeys };
  };

  // Update selection state when table data changes
  useEffect(() => {
    if (landShowcaseDataForRender && landShowcaseDataForRender.length > 0) {
      const { currentTableData, selectedRowKeys } = getCurrentTableData();
      setCurrentTableData(currentTableData);

      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          landShowcaseSelectedRowKey: selectedRowKeys,
        },
      });
    }
  }, [landShowcaseDataForRender]);

  // Update calculated median values when selection changes
  useEffect(() => {
    if (
      landShowcaseDataForRender &&
      landShowcaseDataForRender.length > 0 &&
      landShowcaseSelectedRowKey
    ) {
      const median = calculateMedian(
        landShowcaseSelectedRowKey,
        landShowcaseDataForRender,
      );
      const medianPerSqft = calculateMedianPricePerSqFt(
        landShowcaseSelectedRowKey,
        landShowcaseDataForRender,
      );

      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          landShowcaseMedian: median,
          landShowcaseMedianPricePerSqft: medianPerSqft,
        },
      });
    }
  }, [landShowcaseSelectedRowKey, landShowcaseDataForRender]);

  // Update dropdown filter options
  useEffect(() => {
    if (landShowcaseData && landShowcaseData.length > 0) {
      setStatusOptions([
        ...[
          ...new Set(
            landShowcaseData.map((house: LandShowcaseData) => house.category),
          ),
        ].map((category) => ({
          value: category,
          text: category === 'for_rent' ? 'For Rent' : 'For Sale',
        })),
      ]);
      setPropertyTypeOptions([
        ...[
          ...new Set(
            landShowcaseData.map(
              (house: LandShowcaseData) => house.propertytype,
            ),
          ),
        ].map((status) => ({ value: status, text: status })),
      ]);
      setPropertySubtypeOptions([
        ...[
          ...new Set(
            landShowcaseData.map(
              (house: LandShowcaseData) => house.propertysubtype,
            ),
          ),
        ].map((status) => ({ value: status, text: status })),
      ]);
    }
  }, [landShowcaseData]);

  // Handle row selection change
  const onSelectChange = (selectedRowKeys: React.Key[]) => {
    testLog(selectedRowKeys);
    const unselectedRowKeys = arrayDifference(
      currentTableData.map((row) => row.base_id),
      selectedRowKeys,
    );
    setUnselectedRowKeys(unselectedRowKeys);

    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        landShowcaseSelectedRowKey: selectedRowKeys,
      },
    });
  };

  // Row selection configuration
  const rowSelection = {
    selectedRowKeys: landShowcaseSelectedRowKey || [],
    onChange: onSelectChange,
    selections: [
      Table.SELECTION_ALL,
      Table.SELECTION_INVERT,
      Table.SELECTION_NONE,
    ],
  };

  return (
    <div className="mb-4" ref={landCompContainer}>
      <Table
        dataSource={landShowcaseDataForRender}
        columns={columns}
        key="lc table"
        title={() => (
          <TableHeader selectedRowKeys={landShowcaseSelectedRowKey || []} />
        )}
        rowKey={(record) => record.base_id}
        loading={loading}
        size="small"
        rowSelection={rowSelection}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          onChange: (page, pageSize) => {
            setCurrentPage(page);
            setPageSize(pageSize);
          },
        }}
        onRow={(record, rowIndex) => {
          return {
            onMouseEnter: (event: any) => {
              dispatch({
                type: 'CMA/saveCMAStates',
                payload: {
                  landShowcaseHover: record,
                },
              });
            },
            onMouseLeave: (event: any) => {
              dispatch({
                type: 'CMA/saveCMAStates',
                payload: {
                  landShowcaseHover: null,
                },
              });
            },
            onClick: (event: any) => {
              // Use flyTo function with latitude and longitude from the record
              map.flyTo({
                center: [record.longitude, record.latitude],
                zoom: 16,
                speed: 2,
                curve: 1,
                easing: (t: any) => t,
              });
            },
          };
        }}
        scroll={{
          x: 1200,
          y: '400px',
        }}
        onChange={(
          pagination,
          filters,
          sorter,
          { action, currentDataSource },
        ) => {
          if (!isEqual(currentDataSource, currentTableData)) {
            setCurrentTableData(currentDataSource);
          }

          testLog(filters);
          testLog(currentDataSource);

          switch (action) {
            case 'filter':
              // Update unselected row keys when filtering
              const allRowKeys = currentDataSource.map(
                (item: any) => item.base_id,
              );
              const selectedRowKeys = arrayDifference(
                allRowKeys,
                unselectedRowKeys,
              );

              dispatch({
                type: 'CMA/saveCMAStates',
                payload: {
                  landShowcaseSelectedRowKey: selectedRowKeys,
                },
              });

              setTableFilters(filters);

              if (filters.propertysubtype) {
                const newDFR = landShowcaseData.filter(
                  (house: LandShowcaseData) => {
                    return filters.propertysubtype?.includes(
                      house.propertysubtype,
                    );
                  },
                );
                dispatch({
                  type: 'CMA/saveCMAStates',
                  payload: {
                    landShowcaseDataForRender: newDFR,
                  },
                });
              } else if (filters.propertytype) {
                const newDFR = landShowcaseData.filter(
                  (house: LandShowcaseData) => {
                    return filters.propertytype?.includes(house.propertytype);
                  },
                );
                dispatch({
                  type: 'CMA/saveCMAStates',
                  payload: {
                    landShowcaseDataForRender: newDFR,
                  },
                });
              } else if (filters.category) {
                const newDFR = landShowcaseData.filter(
                  (house: LandShowcaseData) => {
                    return filters.category?.includes(house.category);
                  },
                );
                dispatch({
                  type: 'CMA/saveCMAStates',
                  payload: {
                    landShowcaseDataForRender: newDFR,
                  },
                });
              } else {
                dispatch({
                  type: 'CMA/saveCMAStates',
                  payload: {
                    landShowcaseDataForRender: landShowcaseData,
                  },
                });
              }
              break;
          }
        }}
        sticky={true}
      />
    </div>
  );
};

export default LandShowcaseTable;
