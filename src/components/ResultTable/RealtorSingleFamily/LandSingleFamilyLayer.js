import { Layer, Source } from '@spatiallaser/map';
import isEmpty from 'lodash.isempty';
import mapboxgl from 'mapbox-gl';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import { renderToString } from 'react-dom/server';
import { useSelector } from 'umi';
import { geojsonTemplate } from '../../../constants';
import { formatCurrency } from '../../../utils/lastSalePublicRecord';
import { sourceId as sourceIdHotPadsLayer } from '../../MapCMA/MapLayers/HotPadsLayer';
import {
  removePriceMarkers,
  setPriceMarkers,
} from '../../MapCMA/MapUtility/general';
import { formatPricePerSqftArce, testLog } from '../LandComp/utils/functions';

const sourceId = 'land-sf';
let priceMarker = {};
const zoomLevelToShowParcelAVM = 17;
const zoomLevelToShowPriceMarkers = 12;

export const removePRPriceMarkers = () => {
  if (!isEmpty(priceMarker)) {
    priceMarker = removePriceMarkers(priceMarker);
  }
};

export const generatePRPriceMarkers = (map, GeoJSON) => {
  if (GeoJSON && GeoJSON.features.length > 0) {
    if (!isEmpty(priceMarker)) {
      priceMarker = removePriceMarkers(priceMarker);
    }
    priceMarker = setPriceMarkers(
      map,
      GeoJSON.features,
      'id',
      'currentprice',
      sourceId,
    );
  }
};

function LandSingleFamilyLayer() {
  const map = useSelector((state) => state.CMA.map);
  const showPriceMarkers = useSelector((state) => state.CMA.showPriceMarkers);
  const currentRealtorDotComGeoJSON = useSelector(
    (state) => state.CMA.currentRealtorDotComGeoJSON,
  );
  const cmaTabKey = useSelector((state) => state.CMA.cmaTabKey);
  const searchingMode = useSelector((state) => state.CMA.searchingMode);
  const realtorSingleFamilyHover = useSelector(
    (state) => state.CMA.realtorSingleFamilyHover,
  );

  function Tooltip({ feature }) {
    const properties = feature.properties;
    const pps =
      Number(properties.currentprice) / Number(properties.square_feet);

    return (
      <div
        style={{
          backgroundColor: 'white',
          padding: '10px',
          fontSize: '14px',
          zIndex: '999',
        }}
      >
        <div>
          <strong>Address:</strong> {properties.address}, {properties.city},{' '}
          {properties.state}, {properties.postal_code}
        </div>
        <div>
          <strong>Size:</strong>{' '}
          {!properties.square_feet
            ? 'N/A'
            : new Intl.NumberFormat('en-US').format(
                Number(properties.square_feet),
              ) + ' sqft'}
        </div>
        <div>
          <strong>Bd:</strong> {properties.beds} | <strong>Baths:</strong>{' '}
          {properties.baths}
        </div>
        <div>
          <strong>Price:</strong> {properties.currentprice}
        </div>
        <div>
          <strong>Price per Sqft:</strong> {formatPricePerSqftArce(pps)}
        </div>
        <div>
          <strong>Status:</strong> {properties.status}
        </div>
        <div>
          <strong>Property Type:</strong>{' '}
          {properties.propertytype ? properties.propertytype : 'N/A'}
        </div>
      </div>
    );
  }

  function convertToGeoJSONFormat(listOfObjects, keys) {
    if (!listOfObjects) return [];

    return listOfObjects
      .filter((item) => keys.includes(item.id))
      .map((property) => ({
        type: 'Feature',
        geometry: {
          type: 'Point',
          coordinates: [property.longitude, property.latitude],
        },
        properties: property,
      }));
  }

  // Create refs for values that need to be accessed in callbacks/effects
  const showPriceMarkersRef = useRef(showPriceMarkers);
  const searchingModeRef = useRef(searchingMode);
  const geoJsonRef = useRef(currentRealtorDotComGeoJSON);
  const cmaTabKeyRef = useRef(cmaTabKey);
  const tooltipRef = useRef(new mapboxgl.Popup({ offset: 15 }));

  // Update refs when values change
  showPriceMarkersRef.current = showPriceMarkers;
  searchingModeRef.current = searchingMode;
  geoJsonRef.current = currentRealtorDotComGeoJSON;
  cmaTabKeyRef.current = cmaTabKey;

  const showHidePriceMarkers = useCallback(() => {
    if (
      map &&
      cmaTabKeyRef.current === '1' &&
      searchingModeRef.current === 'Lease' &&
      showPriceMarkersRef.current &&
      geoJsonRef.current &&
      geoJsonRef.current.features.length > 0
    ) {
      const currentZoom = map.getZoom();
      if (currentZoom >= zoomLevelToShowPriceMarkers) {
        if (!isEmpty(priceMarker)) {
          priceMarker = removePriceMarkers(priceMarker);
        }
        priceMarker = setPriceMarkers(
          map,
          geoJsonRef.current.features,
          'id',
          'currentprice',
          sourceId,
        );
      } else {
        priceMarker = removePriceMarkers(priceMarker);
      }
    } else {
      priceMarker = removePriceMarkers(priceMarker);
    }
  }, [map]);

  useEffect(() => {
    if (!map) return;

    const zoomEnd = () => {
      showHidePriceMarkers();
    };

    map.on('zoomend', zoomEnd);

    return () => {
      map.off('zoomend', zoomEnd);
      // Cleanup markers when component unmounts
      priceMarker = removePriceMarkers(priceMarker);
    };
  }, [map, showHidePriceMarkers]);

  // Effect to update markers when relevant state changes
  useEffect(() => {
    showHidePriceMarkers();
  }, [showPriceMarkers, currentRealtorDotComGeoJSON, cmaTabKey, searchingMode]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      priceMarker = removePriceMarkers(priceMarker);
      tooltipRef.current.remove();
    };
  }, []);

  const showTooltip = useCallback(
    (e) => {
      const feature = e.features[0];
      const coordinates = feature.geometry.coordinates.slice();
      const placeholder = document.createElement('div');
      ReactDOM.render(<Tooltip feature={feature} />, placeholder);
      tooltipRef.current
        .setLngLat(coordinates)
        .setDOMContent(placeholder.firstChild)
        .addTo(map);
    },
    [map],
  );

  const hideTooltip = useCallback(() => {
    tooltipRef.current.remove();
  }, []);

  useEffect(() => {
    if (!map) return;

    map.on('mouseenter', `${sourceId}LayerCircle`, showTooltip);
    map.on('mouseleave', `${sourceId}LayerCircle`, hideTooltip);

    return () => {
      map?.off('mouseenter', `${sourceId}LayerCircle`, showTooltip);
      map?.off('mouseleave', `${sourceId}LayerCircle`, hideTooltip);
      tooltipRef.current.remove();
    };
  }, [map, showTooltip, hideTooltip]);

  useEffect(() => {
    if (!map || !realtorSingleFamilyHover) {
      tooltipRef.current.remove();
      return;
    }

    const coordinates = [
      realtorSingleFamilyHover.longitude,
      realtorSingleFamilyHover.latitude,
    ];
    const feature = {
      properties: realtorSingleFamilyHover,
      geometry: realtorSingleFamilyHover.geom,
    };
    const htmlString = renderToString(<Tooltip feature={feature} />);
    const placeholder = document.createElement('div');
    placeholder.innerHTML = htmlString;
    tooltipRef.current
      .setLngLat(coordinates)
      .setDOMContent(placeholder)
      .addTo(map);
  }, [realtorSingleFamilyHover, map]);

  const circleStyle = {
    id: `${sourceId}LayerCircle`,
    type: 'circle',
    filter: ['!', ['has', 'point_count']],
    paint: {
      'circle-radius': 8,
      'circle-color': '#a1f542',
      'circle-stroke-color': '#FFF',
      'circle-stroke-width': 1,
    },
  };

  return cmaTabKey === '1' && searchingMode === 'Lease' ? (
    <Source id={sourceId} type="geojson" data={currentRealtorDotComGeoJSON}>
      <Layer {...circleStyle} beforeId={`${sourceIdHotPadsLayer}Layer`} />
    </Source>
  ) : null;
}

export default LandSingleFamilyLayer;
