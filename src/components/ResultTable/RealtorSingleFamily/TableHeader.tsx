// TableHeader.tsx (updated)
import styles from '@/components/ResultTable/resultTable.css';
import {
  getUserTableColumnSettings,
  postSaveUserTableColumnSettings,
} from '@/services/data';
import { formatCurrency } from '@/utils/lastSalePublicRecord';
import { SettingOutlined } from '@ant-design/icons';
import { useAuthenticator } from '@aws-amplify/ui-react';
import { Button, Col, Row, Tooltip } from 'antd';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import ColumnManagerModal from '../ColumnManagerModal';

const TableHeader = ({
  selectedRowKeys,
  median,
  columns,
  defaultColumns,
  onColumnsChange,
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const dispatch = useDispatch();
  const { user } = useAuthenticator();

  const handleOk = (updatedColumns: any) => {
    onColumnsChange(updatedColumns);
    setIsModalVisible(false);
    console.log('updatedColumns', user.username, updatedColumns);
  };

  return (
    <>
      <Row
        id="realtorDotComTableHeader"
        key="table title row realtorDotCom"
        align="bottom"
        justify="space-between"
        wrap={true}
        gutter={[0, 8]}
        style={{ marginBottom: 12 }}
      >
        <Col key="table title" className={styles.cardTitleH2}>
          Secondary Portal Listings
        </Col>
        <Col key="realtorDotCom summary row wrapper">
          <Row
            key="realtorDotCom summary row"
            align="middle"
            justify="end"
            gutter={24}
          >
            <Col key="total unit">
              <span
                key="total unit text"
                className={styles.cardSubtitle}
                style={{ marginRight: 8 }}
              >
                Total Selected
              </span>
              <span
                id="realtorDotComTable-Total"
                key="total unit number"
                className={styles.cardDataValue}
              >
                {selectedRowKeys.length || '-'}
              </span>
            </Col>
            <Col key="realtorDotCom avg">
              <span
                key="realtorDotCom avg text"
                className={styles.cardSubtitle}
                style={{ marginRight: 8 }}
              >
                Median Rent
              </span>
              <span
                id="realtorDotComTable-Median"
                key="realtorDotCom avg number"
                className={styles.cardDataValue}
              >
                {selectedRowKeys.length === 0
                  ? '-'
                  : median
                  ? formatCurrency(median)
                  : '-'}
              </span>
            </Col>
            <Col>
              <Tooltip title="Column Customization">
                <Button
                  icon={<SettingOutlined />}
                  onClick={() => setIsModalVisible(true)}
                ></Button>
              </Tooltip>
            </Col>
          </Row>
        </Col>
      </Row>
      <ColumnManagerModal
        visible={isModalVisible}
        columns={columns}
        defaultColumns={defaultColumns} // Pass default columns
        onOk={handleOk}
        onCancel={() => setIsModalVisible(false)}
      />
    </>
  );
};

export default TableHeader;
