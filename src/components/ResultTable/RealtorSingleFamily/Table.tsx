import styles from '@/components/ResultTable/resultTable.css';
import { useElementSize } from '@/hooks';
import { useRealtorDotComComps } from '@/libs/filter-comps/hooks/useRealtorDotComComps';
import {
  getUserTableColumnSettings,
  postSaveUserTableColumnSettings,
} from '@/services/data';
import { arrayDifference } from '@/utils/arrayMethods';
import { formatCurrency } from '@/utils/lastSalePublicRecord';
import { formatter } from '@/utils/money';
import { ColumnConfig } from '@ant-design/charts';
import { useAuthenticator } from '@aws-amplify/ui-react';
import { default as turf_distance } from '@turf/distance';
import { point } from '@turf/helpers';
import { Table } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { isEmpty, isEqual } from 'lodash';
import { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import TableHeader from './TableHeader';
import { formatPricePerSqftArce, testLog } from './utils/functions';
import { MultiFamilyCompData } from './utils/type';

const RealtorSFTable = () => {
  const dispatch = useDispatch();
  const map = useSelector((state: any) => state.CMA.map);
  const realtorSingleFamilyDataForRender = useSelector(
    (state: any) => state.CMA.realtorSingleFamilyDataForRender,
  );
  const selectedCompTables = useSelector(
    (state: any) => state.CMA.selectedCompTables,
  );
  const selectedRowKeysRealtorDotCom = useSelector(
    (state: any) => state.CMA.selectedRowKeysRealtorDotCom,
  );
  const realtorSingleFamilyTableSorter = useSelector(
    (state: any) => state.CMA.realtorSingleFamilyTableSorter,
  );
  const mapExpandedView = useSelector(
    (state: any) => state.CMA.mapExpandedView,
  );
  const currentPropertyAddress = useSelector(
    (state: any) => state.CMA.currentPropertyAddress,
  );
  const drawnCustomPolygons = useSelector(
    (state: any) => state.CMA.drawnCustomPolygons,
  );
  const eventCoordinates = useSelector(
    (state: any) => state.CMA.eventCoordinates,
  );
  const secondaryPortalListingsColumnSettings = useSelector(
    (state: any) => state.CMA.secondaryPortalListingsColumnSettings,
  );

  const { user } = useAuthenticator();

  const [statusOptions, setStatusOptions] = useState<any>([]);
  const [propertySubtypeOptions, setPropertySubtypeOptions] = useState<any>();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [currentTableData, setCurrentTableData] = useState<
    MultiFamilyCompData[]
  >([]);
  const [unselectedRowKeys, setUnselectedRowKeys] = useState<React.Key[]>([]);
  const [tableFilters, setTableFilters] = useState<any>({});
  const [prevPropertyAddress, setPrevPropertyAddress] = useState<string>(
    currentPropertyAddress,
  );

  const RMFContainer = useRef(null);
  const tableSize = useElementSize(RMFContainer);
  const containerRef = useRef(null);

  const DEFAULT_COLUMNS: ColumnConfig[] = [
    { title: 'Address', key: 'address', visible: true },
    { title: 'Dist.', key: 'distance', visible: true },
    { title: 'Status', key: 'status', visible: true },
    { title: 'Rent', key: 'currentprice', visible: true },
    { title: 'Property Type', key: 'propertytype', visible: true },
    { title: 'Beds', key: 'beds', visible: true },
    { title: 'Baths', key: 'baths', visible: true },
    { title: 'List Date', key: 'listeddate', visible: true },
    { title: 'Price per Sqft', key: 'currentprice_pps', visible: true },
    { title: 'Size', key: 'square_feet', visible: true },
    { title: 'Year Built', key: 'year_built', visible: true },
  ];

  const [columnConfigs, setColumnConfigs] = useState<ColumnConfig[]>(
    secondaryPortalListingsColumnSettings
      ? Array.isArray(secondaryPortalListingsColumnSettings)
        ? secondaryPortalListingsColumnSettings
        : JSON.parse(secondaryPortalListingsColumnSettings)
      : DEFAULT_COLUMNS,
  );

  // Fetch user table column settings
  const fetchUserTableColumnSettings = async () => {
    try {
      console.log('fetch columns', user.username);
      const data = await getUserTableColumnSettings({
        username: user.username,
      });
      console.log('fetch columns', data);
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          secondaryPortalListingsColumnSettings: data.secPortal,
        },
      });
    } catch (err) {
      console.error('Error loading column settings:', err);
    }
  };

  // Fetch settings on mount or when username changes
  useEffect(() => {
    if (user?.username) {
      fetchUserTableColumnSettings();
    }
  }, [user?.username]);

  // Update columnConfigs when secondaryPortalListingsColumnSettings changes
  useEffect(() => {
    if (secondaryPortalListingsColumnSettings) {
      setColumnConfigs(
        Array.isArray(secondaryPortalListingsColumnSettings)
          ? secondaryPortalListingsColumnSettings
          : JSON.parse(secondaryPortalListingsColumnSettings),
      );
    }
  }, [secondaryPortalListingsColumnSettings]);

  // Save user settings using postSaveUserTableColumnSettings
  const saveUserSettings = async (updatedColumns: ColumnConfig[]) => {
    try {
      const response = await postSaveUserTableColumnSettings({
        body: {
          username: user.username,
          tableName: 'sec_portal',
          settings: JSON.stringify(updatedColumns),
        },
      });
      console.log('Save response:', response);
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          secondaryPortalListingsColumnSettings: updatedColumns, // Store as array
        },
      });
    } catch (error) {
      console.error('Error saving column settings:', error);
    }
  };

  const { loading, filteredComps, calculationResults, geoJSON } =
    useRealtorDotComComps();

  const getInitialTableData = () => {
    if (realtorSingleFamilyTableSorter.order === 'ascend') {
      return realtorSingleFamilyDataForRender.sort(
        (a: MultiFamilyCompData, b: MultiFamilyCompData) =>
          a[realtorSingleFamilyTableSorter.columnKey] -
          b[realtorSingleFamilyTableSorter.columnKey],
      );
    } else {
      return realtorSingleFamilyDataForRender.sort(
        (a: MultiFamilyCompData, b: MultiFamilyCompData) =>
          b[realtorSingleFamilyTableSorter.columnKey] -
          a[realtorSingleFamilyTableSorter.columnKey],
      );
    }
  };

  const getCurrentTableData = () => {
    const allCompsDataSorted = getInitialTableData();
    const nonNullFilters = Object.fromEntries(
      Object.entries(tableFilters).filter(([_, value]) => value !== null),
    );
    const currentTableData = allCompsDataSorted.filter((row) =>
      Object.keys(nonNullFilters).every((filter) =>
        nonNullFilters[filter].includes(row[filter]),
      ),
    );
    const selectedRowKeys = arrayDifference(
      currentTableData.map((row) => row.id),
      unselectedRowKeys,
    );
    return { currentTableData, selectedRowKeys };
  };

  if (!isEqual(currentPropertyAddress, prevPropertyAddress)) {
    setPrevPropertyAddress(currentPropertyAddress);
    setUnselectedRowKeys([]);
  }
  const [scrollX, setScrollX] = useState(1600);

  useEffect(() => {
    const visibleColumns = generateColumnsRealtorDotCom();

    const totalWidth = visibleColumns.reduce(
      (sum, col) => sum + (col.width || 100),
      0,
    );

    // Update scrollX state with the calculated width
    setScrollX(totalWidth);

    console.log('Updated scrollX to:', totalWidth);
  }, [columnConfigs]); // Re-run when column configurations change

  const generateColumnsRealtorDotCom = () => {
    const allColumns: ColumnsType<MultiFamilyCompData> = [
      {
        title: 'Address',
        dataIndex: 'address',
        key: 'address',
        width: 300,
        fixed: 'left',
        align: 'left',
        render: (text: string, record: MultiFamilyCompData) => (
          <div ref={containerRef} className="image-detail-tooltip-container">
            <a
              href={record.url}
              className="text-blue-500 text-xs"
              target="_blank"
            >
              {record.address}, {record.city}, {record.state},{' '}
              {record.postal_code}
            </a>
          </div>
        ),
      },
      {
        title: 'Dist. ',
        dataIndex: 'distance',
        key: 'distance',
        width: 100,
        align: 'center',
        render: (text: any, record: MultiFamilyCompData) => {
          if (drawnCustomPolygons.length > 0 && eventCoordinates.length > 0) {
            const propertyPoint = point([+record.longitude, +record.latitude]);
            const eventPoint = point(eventCoordinates);
            const distance = turf_distance(propertyPoint, eventPoint, {
              units: 'miles',
            });
            return <p className="text-xs">{distance.toFixed(1)} mi</p>;
          }
          return (
            <p className="text-xs">
              {(record.distance / 1609.34).toFixed(1)} mi
            </p>
          );
        },
        sorter: (a: MultiFamilyCompData, b: MultiFamilyCompData) => {
          if (drawnCustomPolygons.length > 0 && eventCoordinates.length > 0) {
            const aPoint = point([+a.longitude, +a.latitude]);
            const bPoint = point([+b.longitude, +b.latitude]);
            const eventPoint = point(eventCoordinates);
            const aDistance = turf_distance(aPoint, eventPoint, {
              units: 'miles',
            });
            const bDistance = turf_distance(bPoint, eventPoint, {
              units: 'miles',
            });
            return aDistance - bDistance;
          }
          return +a.distance - +b.distance;
        },
        sortOrder:
          realtorSingleFamilyTableSorter.columnKey === 'distance'
            ? realtorSingleFamilyTableSorter.order
            : null,
      },
      {
        title: 'Status',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        align: 'left',
        render: (text: any, record: MultiFamilyCompData) => (
          <p className="text-xs">Closed</p>
        ),
        onFilter: (value: string, record: MultiFamilyCompData) => {
          const recordStatus = record.listeddate ? 'Closed' : 'Active';
          return recordStatus === value;
        },
        filters: statusOptions,
      },
      {
        title: 'Rent',
        dataIndex: 'currentprice',
        key: 'currentprice',
        width: 100,
        align: 'center',
        render: (text: any, record: MultiFamilyCompData) => (
          <p className="text-xs">{formatCurrency(text)}</p>
        ),
        sorter: (a: MultiFamilyCompData, b: MultiFamilyCompData) =>
          Number(a.currentprice) - Number(b.currentprice),
      },
      {
        title: 'Property Type',
        dataIndex: 'propertytype',
        key: 'propertytype',
        width: 100,
        align: 'center',
        render: (text: any, record: MultiFamilyCompData) => (
          <p className="text-xs">{record.propertytype}</p>
        ),
        filters: propertySubtypeOptions,
      },
      {
        title: 'Beds',
        dataIndex: 'beds',
        key: 'beds',
        width: 100,
        align: 'center',
        render: (text: any, record: MultiFamilyCompData) => (
          <p className="text-xs">{record.beds}</p>
        ),
      },
      {
        title: 'Baths',
        dataIndex: 'baths',
        key: 'baths',
        width: 100,
        align: 'center',
        render: (text: any, record: MultiFamilyCompData) => (
          <p className="text-xs">{record.baths}</p>
        ),
      },
      {
        title: 'List Date',
        dataIndex: 'listeddate',
        key: 'listeddate',
        width: 100,
        align: 'center',
        render: (text: any, record: MultiFamilyCompData) => (
          <p className="text-xs">
            {record.listeddate ? record.listeddate.split(' ')[0] : ''}
          </p>
        ),
        sorter: (a: MultiFamilyCompData, b: MultiFamilyCompData) =>
          a.listeddate - b.listeddate,
      },
      {
        title: 'Price per Sqft',
        dataIndex: 'currentprice',
        key: 'currentprice_pps',
        width: 100,
        align: 'center',
        render: (text: any, record: MultiFamilyCompData) => {
          const price = Number(record.currentprice);
          const pps =
            Number(record.square_feet) > 0
              ? price / Number(record.square_feet)
              : 0;
          return (
            <p className="text-xs">
              {pps ? formatPricePerSqftArce(pps) : 'N/A'}
            </p>
          );
        },
        sorter: (a: MultiFamilyCompData, b: MultiFamilyCompData) => {
          const priceA = Number(a.currentprice);
          const priceB = Number(b.currentprice);
          const ppsA = priceA / Number(a.square_feet);
          const ppsB = priceB / Number(b.square_feet);
          return ppsA - ppsB;
        },
      },
      {
        title: 'Size',
        dataIndex: 'square_feet',
        key: 'square_feet',
        width: 100,
        align: 'center',
        render: (text: any, record: MultiFamilyCompData) => (
          <p className="text-xs">{formatter(text)}</p>
        ),
        sorter: (a: MultiFamilyCompData, b: MultiFamilyCompData) =>
          Number(a.square_feet) - Number(b.square_feet),
      },
      {
        title: 'Year Built',
        dataIndex: 'year_built',
        key: 'year_built',
        width: 100,
        align: 'center',
        render: (text: any, record: MultiFamilyCompData) => (
          <p className="text-xs">{text}</p>
        ),
        sorter: (a: MultiFamilyCompData, b: MultiFamilyCompData) =>
          a.year_built - b.year_built,
      },
    ];

    const currentColumn = allColumns
      .filter((col) => {
        const config = columnConfigs.find((c) => c.key === col.key);
        return config?.visible;
      })
      .sort((a, b) => {
        const aIndex = columnConfigs.findIndex((c) => c.key === a.key);
        const bIndex = columnConfigs.findIndex((c) => c.key === b.key);
        return aIndex - bIndex;
      });

    console.log('currentColumn', currentColumn);
    return currentColumn;
  };

  useEffect(() => {
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        realtorSingleFamilyDataForRender: filteredComps,
        selectedCompTables: [...selectedCompTables, 'Single-Family'],
      },
    });
  }, [filteredComps]);

  useEffect(() => {
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        realtorSingleFamilyMedian: calculationResults?.rentMedianRealtorDotCom,
        realtorSingleFamilyMedianPricePerSqft:
          calculationResults?.rentPerSqftMedianRealtorDotCom,
      },
    });
  }, [calculationResults]);

  useEffect(() => {
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        currentRealtorDotComGeoJSON: geoJSON,
      },
    });
  }, [geoJSON]);

  useEffect(() => {
    if (
      realtorSingleFamilyDataForRender &&
      realtorSingleFamilyDataForRender.length > 0
    ) {
      const statusOptions = [
        { text: 'Active', value: 'Active' },
        { text: 'Closed', value: 'Closed' },
      ];
      setStatusOptions(statusOptions);
      setPropertySubtypeOptions([
        ...[
          ...new Set(
            realtorSingleFamilyDataForRender.map(
              (house: MultiFamilyCompData) => house.propertytype,
            ),
          ),
        ].map((status) => ({ value: status, text: status })),
      ]);
    }
  }, [selectedRowKeysRealtorDotCom, realtorSingleFamilyDataForRender]);

  useEffect(() => {
    if (realtorSingleFamilyDataForRender) {
      const { currentTableData, selectedRowKeys } = getCurrentTableData();
      console.log('currentTableData', currentTableData);
      setCurrentTableData(currentTableData);
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          selectedRowKeysRealtorDotCom: selectedRowKeys,
        },
      });
    }
  }, [realtorSingleFamilyDataForRender]);

  const onSelectChangeRealtorDotCom = (newSelectedRowKeys: React.Key[]) => {
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        selectedRowKeysRealtorDotCom: newSelectedRowKeys,
      },
    });
  };

  const rowSelection = {
    selectedRowKeys: selectedRowKeysRealtorDotCom,
    onChange: (selectedRowKeys: React.Key[]) => {
      const unselectedRowKeys = arrayDifference(
        currentTableData.map((row) => row.id),
        selectedRowKeys,
      );
      setUnselectedRowKeys(unselectedRowKeys);
      onSelectChangeRealtorDotCom(selectedRowKeys);
    },
    selections: [
      Table.SELECTION_ALL,
      Table.SELECTION_INVERT,
      Table.SELECTION_NONE,
    ],
  };

  useEffect(() => {
    console.log(
      'realtorSingleFamilyDataForRender',
      realtorSingleFamilyDataForRender,
    );
  }, [realtorSingleFamilyDataForRender]);

  return (
    <div
      ref={RMFContainer}
      key="RealtorDotCom card"
      className={styles.cardWrapperMLS}
      style={{ marginTop: '16px' }}
    >
      <Table
        key="lc table"
        className="table-sticky-title-header"
        dataSource={realtorSingleFamilyDataForRender}
        columns={generateColumnsRealtorDotCom()}
        rowKey={(record) => record.id}
        loading={loading}
        size="small"
        rowSelection={rowSelection}
        rowClassName={styles.propertyDataTableRow}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          onChange: (page, pageSize) => {
            setCurrentPage(page);
            setPageSize(pageSize);
          },
        }}
        onRow={(record, rowIndex) => ({
          onMouseEnter: (event: any) => {
            dispatch({
              type: 'CMA/saveCMAStates',
              payload: {
                realtorSingleFamilyHover: record,
              },
            });
          },
          onMouseLeave: (event: any) => {
            dispatch({
              type: 'CMA/saveCMAStates',
              payload: {
                realtorSingleFamilyHover: null,
              },
            });
          },
          onClick: (event: any) => {
            map.flyTo({
              center: [record.longitude, record.latitude],
              zoom: 16,
              speed: 2,
              curve: 1,
              easing: (t: any) => t,
            });
          },
        })}
        scroll={{ x: scrollX }}
        onChange={(
          pagination,
          filters,
          sorter,
          { action, currentDataSource },
        ) => {
          if (!isEqual(currentDataSource, currentTableData)) {
            setCurrentTableData(currentDataSource);
          }
          switch (action) {
            case 'sort':
              dispatch({
                type: 'CMA/saveCMAStates',
                payload: {
                  realtorSingleFamilyTableSorter: sorter,
                },
              });
              break;
            case 'filter':
              const allRowKeys = currentDataSource.map((item) => item.id);
              const selectedRowKeys = arrayDifference(
                allRowKeys,
                unselectedRowKeys,
              );
              onSelectChangeRealtorDotCom(selectedRowKeys);
              setTableFilters(filters);
              if (filters.propertytype) {
                const newDFR = realtorSingleFamilyDataForRender.filter(
                  (house: MultiFamilyCompData) =>
                    filters.propertytype?.includes(house.propertytype),
                );
                dispatch({
                  type: 'CMA/saveCMAStates',
                  payload: {
                    realtorSingleFamilyDataForRender: newDFR,
                  },
                });
              } else if (filters.status) {
                const newDFR = realtorSingleFamilyDataForRender.filter(
                  (house: MultiFamilyCompData) => {
                    const houseStatus = house.listeddate ? 'Closed' : 'Active';
                    return filters.status?.includes(houseStatus);
                  },
                );
                dispatch({
                  type: 'CMA/saveCMAStates',
                  payload: {
                    realtorSingleFamilyDataForRender: newDFR,
                  },
                });
              }
              break;
          }
        }}
        sticky={true}
        components={{
          header: {
            wrapper: ({ className, children }) => (
              <thead className={className}>
                <div style={{ width: tableSize.width - 12 }}>
                  <TableHeader
                    selectedRowKeys={selectedRowKeysRealtorDotCom}
                    columns={columnConfigs}
                    onColumnsChange={saveUserSettings}
                    defaultColumns={DEFAULT_COLUMNS}
                    median={calculationResults?.rentMedianRealtorDotCom}
                  />
                </div>
                {children}
              </thead>
            ),
          },
        }}
        summary={(currentData) => (
          <Table.Summary fixed>
            <Table.Summary.Row>
              <Table.Summary.Cell colSpan={mapExpandedView ? 2 : 4}>
                Median Rent of Selected
              </Table.Summary.Cell>
              <Table.Summary.Cell
                className={styles.tableSummaryCellTextAlignCenter}
              >
                $
                {calculationResults?.rentMedianRealtorDotCom
                  ? formatter(calculationResults?.rentMedianRealtorDotCom)
                  : '-'}
              </Table.Summary.Cell>
            </Table.Summary.Row>
          </Table.Summary>
        )}
      />
    </div>
  );
};

export default RealtorSFTable;
