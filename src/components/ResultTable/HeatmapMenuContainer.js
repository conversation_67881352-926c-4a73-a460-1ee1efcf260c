import { useSelector, useDispatch } from 'umi';
import { Button } from 'antd';
import { HeatmapMenu } from '@spatiallaser/map';
import { useState, useEffect } from 'react';

const HeatmapMenuContainer = ({ viewMode }) => {
  const map = useSelector((state) => state.CMA.map);

  const heatmapType = useSelector((state) => state.CMA.heatmapType);

  const dispatch = useDispatch();

  // useEffect(() => {
  //   if (!map) return;

  //   const onHeatmapUpdateType = ({ payload }) => {
  //     dispatch({
  //       type: 'CMA/saveCMAStates',
  //       payload: {
  //         heatmapType: payload.heatmapType,
  //       },
  //     });
  //   };

  //   map.on('heatmap.updateType', onHeatmapUpdateType);
  // }, [map]);

  const onClose = () => {
    map.fire('heatmap.updateType', { payload: { heatmapType: null } });
  };

  console.log('heatmap: ', viewMode, heatmapType);
  if (!heatmapType) return null;
  if (viewMode === 'none' || !viewMode) return null;

  console.log('HERE');

  return (
    <div
      style={{
        // position: 'absolute',
        // left: 20,
        // right: 16,
        // zIndex: 100,
        height: '100%',
        backgroundColor: '#fff',
        padding: '24px 32px',
        // borderRadius: 8,
        // boxShadow: '0 0 4px rgba(19, 16, 204, 0.1)',
      }}
    >
      {/* <Button
        type="link"
        onClick={onClose}
        style={{
          position: 'absolute',
          top: '10px',
          right: '10px',
        }}
      >
        Close
      </Button> */}
      <HeatmapMenu map={map} />
    </div>
  );
};

export default HeatmapMenuContainer;
