import React, { memo } from 'react';
import isEqual from 'lodash.isequal';
import { Line } from '@ant-design/charts';
import { sliderDataRef } from './MarketConditionGraph';

const formatter = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
});

const getMaxValue = (data, key) => {
  if (!data || data.length === 0) return 0;
  return Math.max(...data.map((item) => item[key]));
};

const getMinValue = (data, key) => {
  if (!data || data.length === 0) return 0;
  return Math.min(
    ...data.reduce((acc, item) => {
      if (item[key] && item[key] > 0) acc.push(item[key]);
      return acc;
    }, []),
  );
};

const PriceChart = memo(
  ({ data, loading, onReady }) => {
    const min = getMinValue(data, 'value');
    const max = getMaxValue(data, 'value');
    const config = {
      data: data && data.length > 0 ? data : [],
      xField: 'date',
      yField: 'value',
      seriesField: 'type',
      slider: {
        start: sliderDataRef.current ? sliderDataRef.current.start : 0,
        end: sliderDataRef.current ? sliderDataRef.current.end : 0,
      },
      meta: {
        value: {
          min: min - min * 0.1,
          minLimit: min - min * 0.1,
          max: max + max * 0.1,
          maxLimit: max + max * 0.1,
          formatter: (value) => formatter.format(value),
        },
      },
      point: {
        shape: (datum) => {
          return 'circle';
        },
        style: (datum) => ({ r: 3.5 }),
      },
      smooth: true,
    };

    return <Line {...config} loading={loading} onReady={onReady} />;
  },
  (pre, next) => {
    return isEqual(pre, next);
  },
);

export default PriceChart;
