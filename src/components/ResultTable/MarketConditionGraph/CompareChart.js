import React, { memo } from 'react';
import isEqual from 'lodash.isequal';
import { Line } from '@ant-design/charts';
import { sliderDataRef } from './MarketConditionGraph';

const getMaxValue = (data, key) => {
  if (!data || data.length === 0) return 0;
  return Math.max(
    ...data.map((item) => item[key]).filter((value) => value !== null),
  );
};

const getMinValue = (data, key) => {
  if (!data || data.length === 0) return 0;
  return Math.min(
    ...data.map((item) => item[key]).filter((value) => value !== null),
  );
};

const formatter = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
});

const CompareChart = memo(
  ({ data, loading, onReady, formatting }) => {
    const legendFormatter = (value) => {
      if (formatting === 'dollar') {
        return formatter.format(value);
      } else if (formatting === 'percent') {
        return `${Math.round(value)}%`;
      } else {
        if (value % 1 !== 0) {
          return value.toFixed(2);
        }
        return value;
      }
    };

    const config = {
      data: data && data.length > 0 ? data : [],
      xField: 'date',
      yField: 'value',
      seriesField: 'type',
      slider: {
        start: sliderDataRef.current ? sliderDataRef.current.start : 0,
        end: sliderDataRef.current ? sliderDataRef.current.end : 1,
      },
      meta: {
        value: {
          min: getMinValue(data, 'value') - 0.05,
          max: getMaxValue(data, 'value') + 0.05,
          formatter: (value) => {
            if (formatting === 'dollar') {
              return formatter.format(value);
            } else if (formatting === 'percent') {
              return `${value}%`;
            } else {
              return value;
            }
          },
        },
      },
      legend: {
        // position: 'left-top',
        title: {
          text: 'Comparison',
          spacing: 8,
        },
      },
      tooltip: {
        customItems: (originalItems) => {
          const modifiedItems = originalItems.map((item) => ({
            ...item,
            name: item.data.type,
            // value: `${Math.round(item.data.value)}%`,
            value: legendFormatter(item.data.value),
          }));
          return modifiedItems;
        },
      },
      point: {
        shape: 'circle',
        size: 3.5,
      },
      smooth: true,
    };

    return <Line {...config} loading={loading} onReady={onReady} />;
  },
  (pre, next) => {
    return isEqual(pre, next);
  },
);

export default CompareChart;
