import React, { memo } from 'react';
import isEqual from 'lodash.isequal';
import { Line } from '@ant-design/charts';
import { sliderDataRef } from './MarketConditionGraph';

const DOMChart = memo(
  ({ data, loading, onReady }) => {
    const domConfig = {
      data: data || [],
      xField: 'date',
      yField: 'value',
      seriesField: 'type',
      meta: {
        value: {
          alias: 'Median DOM',
          formatter: (value) =>
            value !== null && !isNaN(value) ? value.toFixed(0) : value,
        },
      },
      slider: {
        start: sliderDataRef.current ? sliderDataRef.current.start : 0,
        end: sliderDataRef.current ? sliderDataRef.current.end : 0,
      },
      point: {
        shape: 'circle',
        size: 3,
      },
      smooth: true,
    };

    return <Line {...domConfig} loading={loading} onReady={onReady} />;
  },
  (pre, next) => {
    return isEqual(pre, next);
  },
);

export default DOM<PERSON>hart;
