import { default as turf_centerOfMass } from '@turf/center-of-mass';
import { multiPolygon } from '@turf/helpers';
import { Button, Dropdown, message, Radio, Select } from 'antd';
import json2csv from 'csvjson-json2csv/json2csv';
import moment from 'moment';
import { useEffect, useRef, useState } from 'react';
import { IoMenu } from 'react-icons/io5';
import { useDispatch, useSelector } from 'umi';
import styles from '../resultTable.css';
import CompareChart from './CompareChart';
import DOMChart from './DOMChart';
import ListClosedChart from './ListClosedChart';
import PriceChart from './PriceChart';
import YoYChart from './YoYChart';

export let sliderDataRef;

function MarketConditionGraph() {
  const eventCoordinates = useSelector((state) => state.CMA.eventCoordinates);
  const currentRadiusMile = useSelector((state) => state.CMA.currentRadiusMile);
  const drawnCustomPolygons = useSelector(
    (state) => state.CMA.drawnCustomPolygons,
  );
  const searchingMode = useSelector((state) => state.CMA.searchingMode);
  // const { priceChartData, listClosedChartData, domChartData } = useSelector(
  //   (state) => state.CMA.currentMLSListingChartData,
  // );
  const chartArea = useSelector((state) => state.CMA.chartArea);
  const currentMLSListingChartData = useSelector(
    (state) => state.CMA.currentMLSListingChartData,
  );

  let priceChartData;
  let listClosedChartData;
  let domChartData;
  if (currentMLSListingChartData && currentMLSListingChartData.length === 1) {
    priceChartData = currentMLSListingChartData[0].data.priceChartData;
    listClosedChartData =
      currentMLSListingChartData[0].data.listClosedChartData;
    domChartData = currentMLSListingChartData[0].data.domChartData;
  }

  // const priceChartData = currentMLSListingChartData.priceChartData;
  // const listClosedChartData = currentMLSListingChartData.listClosedChartData;
  // const domChartData = currentMLSListingChartData.domChartData;

  const priceDataFull = (priceChartData && priceChartData.data) || [];
  const priceDataPSF = (priceChartData && priceChartData.psfData) || [];
  const priceDataFullYoY = (priceChartData && priceChartData.yoy) || [];
  const priceDataPSFYoY = (priceChartData && priceChartData.psfYoY) || [];
  const listClosedData =
    (listClosedChartData && listClosedChartData.data) || [];
  const listClosedDataYoY =
    (listClosedChartData && listClosedChartData.yoy) || [];
  const domData = (domChartData && domChartData.data) || [];
  const domDataYoY = (domChartData && domChartData.yoy) || [];

  const dispatch = useDispatch();

  const [chartView, setChartView] = useState('price');
  const [priceViewType, setPriceViewType] = useState('price');
  const [defaultOrYoY, setDefaultOrYoY] = useState('default');
  const [chartLoading, setChartLoading] = useState(false);
  const [sliderData, setSliderData] = useState({
    minText: '',
    maxText: '',
    start: 0,
    end: 1,
  });

  sliderDataRef = useRef();
  const isMountedRef = useRef(true);

  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  useEffect(() => {
    if (currentMLSListingChartData && currentMLSListingChartData.length > 0) {
      if (
        currentMLSListingChartData[0].data &&
        currentMLSListingChartData[0].data.priceChartData
      ) {
        const data = currentMLSListingChartData[0].data.priceChartData.data;
        sliderDataRef.current = {
          minText: data.length > 0 ? data[0].date : '',
          maxText: data.length > 0 ? data[data.length - 1].date : '',
          start: 0,
          end: 1,
        };
      }
    }
    return () => {
      if (sliderDataRef.current) {
        sliderDataRef.current = null;
      }
    };
  }, [currentMLSListingChartData]);

  useEffect(() => {
    if (eventCoordinates.length > 0 && drawnCustomPolygons.length === 0) {
      setChartLoading(true);
      const payload = {
        type: chartArea,
        propertyType:
          searchingMode === 'Lease' ? 'Residential Lease' : 'Residential',
        lng: eventCoordinates[0],
        lat: eventCoordinates[1],
        distance: currentRadiusMile * 1609.34,
      };
      // if (chartArea === 'aoi') {
      //   payload.distance = currentRadiusMile * 1609.34;
      // }

      dispatch({
        type: 'CMA/getMLSListingSummaryChart',
        payload: payload,
      }).then(() => {
        if (!isMountedRef.current) return;
        setChartLoading(false);
      });
    } else if (drawnCustomPolygons.length > 0) {
      setChartLoading(true);
      const newChartArea = chartArea.map((x) =>
        x.replace(/aoi/g, 'multipolygon'),
      );
      const centerOfMass = turf_centerOfMass(
        multiPolygon([drawnCustomPolygons]),
      );

      dispatch({
        type: 'CMA/getMLSListingSummaryChart',
        payload: {
          type: newChartArea,
          propertyType:
            searchingMode === 'Lease' ? 'Residential Lease' : 'Residential',
          body: drawnCustomPolygons,
          lng: centerOfMass.geometry.coordinates[0],
          lat: centerOfMass.geometry.coordinates[1],
        },
      }).then(() => {
        if (!isMountedRef.current) return;
        setChartLoading(false);
      });
    }
  }, [eventCoordinates, chartArea, searchingMode, drawnCustomPolygons]);

  useEffect(() => {
    // only for aoi which uses radius
    if (chartArea != 'aoi') return;

    if (eventCoordinates.length > 0 && drawnCustomPolygons.length === 0) {
      setChartLoading(true);
      const payload = {
        type: chartArea,
        propertyType:
          searchingMode === 'Lease' ? 'Residential Lease' : 'Residential',
        lng: eventCoordinates[0],
        lat: eventCoordinates[1],
        distance: currentRadiusMile * 1609.34,
      };

      dispatch({
        type: 'CMA/getMLSListingSummaryChart',
        payload: payload,
      }).then(() => {
        if (!isMountedRef.current) return;
        setChartLoading(false);
      });
    }
  }, [currentRadiusMile]);

  const onReady = (plot) => {
    let shouldBeActive = false;
    let sliderComponent = null;
    let dateViewUI = null;

    const mousemove = (e) => {
      if (!dateViewUI) return;
      shouldBeActive = true;
      const { minText, maxText } = sliderComponent;
      dateViewUI.innerHTML = `${minText} to ${maxText}`;
    };

    const mousedown = (e) => {
      if (!dateViewUI) return;
      plot.on('slider:mousemove', mousemove);
      shouldBeActive = true;
      sliderComponent = e.gEvent.delegateObject.slider.cfg;
      dateViewUI = document.querySelector(
        '#marketConditionsHistory-dateViewed span',
      );
    };

    const mouseup = (e) => {
      if (!dateViewUI) return;
      if (shouldBeActive) {
        setTimeout(() => {
          // setTimeout because the slider component is not updated yet
          if (!sliderComponent) return;

          const { minText, maxText, start, end } = sliderComponent;

          sliderDataRef.current = {
            minText: minText,
            maxText: maxText,
            start: start,
            end: end,
          };

          dateViewUI.innerHTML = `${minText} to ${maxText}`;

          plot.off('slider:mousemove', mousemove);
          shouldBeActive = false;
          sliderComponent = null;
          dateViewUI = null;
        }, 100);
      }
    };

    plot.on('slider:mousedown', mousedown);
    plot.on('mouseup', mouseup);
    plot.on('mouseleave', mouseup);
  };

  const [dataType, setDataType] = useState(null);
  const [dataTypeOptions, setDataTypeOptions] = useState([]);
  const [compareData, setCompareData] = useState([]);
  const [compareChartFormatting, setCompareChartFormatting] =
    useState('dollar');

  useEffect(() => {
    if (chartArea.length > 1) {
      if (chartView === 'price') {
        setDataType('active_median_rent');
        setDataTypeOptions([
          {
            value: 'active_median_rent',
            label: `Active Median ${
              searchingMode === 'Lease' ? 'Rent' : 'Price'
            }`,
          },
          {
            value: 'closed_median_rent',
            label: `Closed Median ${
              searchingMode === 'Lease' ? 'Rent' : 'Price'
            }`,
          },
        ]);
      } else if (chartView === 'listclose') {
        setDataType('active');
        setDataTypeOptions([
          { value: 'active', label: 'Active' },
          { value: 'closed', label: 'Closed' },
          { value: 'months_of_inventory', label: 'Months of Inventory' },
        ]);
      } else {
        setDataType(null);
        setDataTypeOptions([]);
      }
    } else {
      if (dataType != null || dataTypeOptions.length > 0) {
        setDataType(null);
        setDataTypeOptions([]);
      }
    }
  }, [chartArea, chartView, searchingMode]);

  useEffect(() => {
    // console.log('dataType,', dataType);
    // console.log('dataType', currentMLSListingChartData);
    // console.log('defaultOrYoY', defaultOrYoY);
    // console.log('priceViewType', priceViewType);
    if (Array.isArray(currentMLSListingChartData) === false) return;
    if (chartArea.length > 1) {
      const getChartAreaLabel = (area) => {
        if (area === 'aoi') return 'AOI';
        if (area === 'zipcode') return 'ZIP Code';
        if (area === 'district') return 'School District';
        if (area === 'county') return 'County';
        if (area === 'metro') return 'Metro';
      };

      const data = [];
      if (chartView === 'price') {
        const getDataType = (type, defaultOrYoY, priceViewType) => {
          const getUnit = (viewType, searchingMode) => {
            if (viewType === 'price' && searchingMode === 'Lease')
              return ' Rent';
            if (viewType === 'price' && searchingMode !== 'Lease')
              return ' Price';
            if (viewType === 'psf' && searchingMode === 'Lease') return ' RSF';
            if (viewType === 'psf' && searchingMode !== 'Lease') return ' PSF';
          };

          const getYOY = (defaultOrYoY) => {
            if (defaultOrYoY === 'default') return '';
            if (defaultOrYoY === 'yoy') return ' YoY';
          };

          if (type === 'active_median_rent') {
            let result = 'Active Median';
            result += `${getUnit(
              priceViewType,
              searchingMode === 'Lease',
            )}${getYOY(defaultOrYoY)}`;
            return result;
          } else if (type === 'closed_median_rent') {
            let result = 'Closed Median';
            result += `${getUnit(
              priceViewType,
              searchingMode === 'Lease',
            )}${getYOY(defaultOrYoY)}`;
            return result;
          }
          return '';
        };

        currentMLSListingChartData.forEach((d) => {
          const { priceChartData } = d.data;
          const area = d.type;
          let chartData;
          if (!priceChartData) return;
          if (defaultOrYoY === 'default' && priceViewType === 'price') {
            chartData = priceChartData.data;
          } else if (defaultOrYoY === 'default' && priceViewType === 'psf') {
            chartData = priceChartData.psfData;
          } else if (defaultOrYoY === 'yoy' && priceViewType === 'price') {
            chartData = priceChartData.yoy;
          } else if (defaultOrYoY === 'yoy' && priceViewType === 'psf') {
            chartData = priceChartData.psfYoY;
          }
          console.log('chartData: ', chartData);

          for (let i = 0; i < chartData.length; i++) {
            if (
              chartData[i].type ===
              getDataType(dataType, defaultOrYoY, priceViewType)
            ) {
              data.push({
                date: chartData[i].date,
                // type: `${area} ${chartData[i].type}`,
                type: getChartAreaLabel(area),
                value: chartData[i].value,
              });
            }
          }
        });
      } else if (chartView === 'listclose') {
        const getDataType = (type, defaultOrYoY) => {
          if (defaultOrYoY === 'default') {
            if (type === 'active') return 'Active';
            if (type === 'closed') return 'Closed';
            if (type === 'months_of_inventory') return 'Months of Inventory';
          } else if (defaultOrYoY === 'yoy') {
            if (type === 'active') return 'Active YoY';
            if (type === 'closed') return 'Closed YoY';
            if (type === 'months_of_inventory')
              return 'Months of Inventory YoY';
          }
        };

        currentMLSListingChartData.forEach((d) => {
          const { listClosedChartData } = d.data;
          const area = d.type;
          let chartData;
          if (defaultOrYoY === 'default') {
            chartData = [
              ...listClosedChartData.data[0],
              ...listClosedChartData.data[1],
            ];
          } else if (defaultOrYoY === 'yoy') {
            chartData = listClosedChartData.yoy;
          }
          console.log('chartData: ', chartData);

          for (let i = 0; i < chartData.length; i++) {
            if (chartData[i].type === getDataType(dataType, defaultOrYoY)) {
              data.push({
                date: chartData[i].date,
                // type: `${area} ${chartData[i].type}`,
                type: getChartAreaLabel(area),
                value: chartData[i].value,
              });
            }
          }
        });
      } else if (chartView === 'dom') {
        const getDataType = (defaultOrYoY) => {
          if (defaultOrYoY === 'default') {
            return 'Median DOM';
          } else if (defaultOrYoY === 'yoy') {
            return 'Median DOM YoY';
          }
        };

        currentMLSListingChartData.forEach((d) => {
          const { domChartData } = d.data;
          const area = d.type;
          let chartData;
          if (defaultOrYoY === 'default') {
            chartData = domChartData.data;
          } else if (defaultOrYoY === 'yoy') {
            chartData = domChartData.yoy;
          }
          console.log('chartData: ', chartData);

          for (let i = 0; i < chartData.length; i++) {
            if (chartData[i].type === getDataType(defaultOrYoY)) {
              data.push({
                date: chartData[i].date,
                // type: `${area} ${chartData[i].type}`,
                type: getChartAreaLabel(area),
                value: chartData[i].value,
              });
            }
          }
        });
      }

      let format;
      if (defaultOrYoY === 'default') {
        if (chartView === 'price') {
          format = 'dollar';
        } else if (chartView === 'listclose' || chartView === 'dom') {
          format = 'number';
        }
      } else if (defaultOrYoY === 'yoy') {
        format = 'percent';
      }

      setCompareChartFormatting(format);
      setCompareData(data);
    }
  }, [
    currentMLSListingChartData,
    chartView,
    dataType,
    defaultOrYoY,
    priceViewType,
  ]);

  const exportToCSV = () => {
    if (currentMLSListingChartData && currentMLSListingChartData.length > 0) {
      let priceData = currentMLSListingChartData[0].data.priceChartData.data;

      // Need to refactor this
      if (priceData.length === 0) {
        for (let i = 1; i < currentMLSListingChartData.length; i++) {
          priceData = currentMLSListingChartData[i].data.priceChartData.data;
          if (priceData.length > 0) {
            break;
          }
        }
        if (priceData.length === 0) {
          console.log('No data to export', currentMLSListingChartData);
          message.error('No data to export');
          return;
        }
      }

      const startData = moment(priceData[0].date, 'MMM YYYY');
      const endData = moment(priceData[priceData.length - 1].date, 'MMM YYYY');

      const rowKeys = ['type'];
      while (startData.isSameOrBefore(endData)) {
        rowKeys.push(startData.format('MMM YYYY'));
        startData.add(1, 'month');
      }

      const dataTypes = [
        `Active Median ${searchingMode === 'Lease' ? 'Rent' : 'Price'}`,
        `Closed Median ${searchingMode === 'Lease' ? 'Rent' : 'Price'}`,
        `Active Median ${searchingMode === 'Lease' ? 'RSF' : 'PSF'}`,
        `Closed Median ${searchingMode === 'Lease' ? 'RSF' : 'PSF'}`,
        `Active Median ${searchingMode === 'Lease' ? 'RSF' : 'PSF'} YoY`,
        `Closed Median ${searchingMode === 'Lease' ? 'RSF' : 'PSF'} YoY`,
        `Active Median ${searchingMode === 'Lease' ? 'Rent' : 'Price'} YoY`,
        `Closed Median ${searchingMode === 'Lease' ? 'Rent' : 'Price'} YoY`,
        `Active`,
        `Closed`,
        `Months of Inventory`,
        `Active YoY`,
        `Closed YoY`,
        `Months of Inventory YoY`,
        `Median DOM`,
        `Median DOM YoY`,
      ];

      const formatArea = (area) => {
        if (area === 'aoi') return 'AOI';
        if (area === 'zipcode') return 'ZIP Code';
        if (area === 'district') return 'School District';
        if (area === 'county') return 'County';
        if (area === 'metro') return 'Metro';
      };

      const data = [];
      currentMLSListingChartData.forEach((d) => {
        const { priceChartData, listClosedChartData, domChartData } = d.data;
        const area = formatArea(d.type);
        const mergedData = [
          ...priceChartData.data,
          ...priceChartData.psfData,
          ...priceChartData.yoy,
          ...priceChartData.psfYoY,
          ...listClosedChartData.data[0],
          ...listClosedChartData.data[1],
          ...listClosedChartData.yoy,
          ...domChartData.data,
          ...domChartData.yoy,
        ];

        for (let i = 0; i < dataTypes.length; i++) {
          const dataType = dataTypes[i];
          const dataRow = {};
          for (let j = 0; j < rowKeys.length; j++) {
            if (rowKeys[j] === 'type') {
              dataRow[rowKeys[j]] = `${area} ${dataType}`;
            } else {
              const data = mergedData.find(
                (d) => d.type === dataType && d.date === rowKeys[j],
              );

              let value = null;

              if (data) {
                if (data.type === 'Median DOM') {
                  value = Math.round(data.value);
                } else if (data.value % 1 !== 0) {
                  value = data.value.toFixed(2);
                } else {
                  value = data.value;
                }
              }

              dataRow[rowKeys[j]] = value || '';
            }
          }
          data.push(dataRow);
        }
      });

      // console.log(moment(startData, 'MMM YYYY').format('YYYY-MM-DD'));
      // console.log(moment(endData, 'MMM YYYY').format('YYYY-MM-DD'));

      // const data = currentMLSListingChartData[0].data.priceChartData.data;

      // Transpose data - eg. switch rows and columns
      function transpose(inputData) {
        const types = inputData.map((item) => item.type);
        const dates = Object.keys(inputData[0]).filter((key) => key !== 'type');
        const transposedData = [];

        for (const date of dates) {
          const row = { date };
          for (let i = 0; i < types.length; i++) {
            const type = types[i];
            // Set the type as the key and the corresponding value for the current date
            row[type] = inputData[i][date];
          }
          transposedData.push(row);
        }
        return transposedData;
      }
      const transposedData = transpose(data);

      const csvData = json2csv(transposedData);
      const blob = new Blob([csvData], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute(
        'download',
        `market-condition-history-${new Date().toISOString()}.csv`,
      );
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
      console.log('No data to export');
      message.error('No data to export');
    }
  };

  const areaOnChange = (value) => {
    if (!isMountedRef.current) return;
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: { chartArea: value },
    });
  };

  return (
    <div className={styles.cardWrapper}>
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
        }}
      >
        <div className={styles.cardTitleH2}>Market Conditions History</div>
        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
          }}
        >
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              gap: '10px',
              flexWrap: 'wrap',
            }}
          >
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: '5px',
                alignItems: 'center',
                width: '100%',
              }}
            >
              <label>Area</label>
              <Select
                // defaultValue={'zipcode'}
                mode="multiple"
                maxTagCount={'responsive'}
                allowClear={true}
                value={chartArea}
                style={{ width: '100%' }}
                onChange={(value) => areaOnChange(value)}
                size="small"
                options={[
                  { value: 'aoi', label: 'AOI' },
                  { value: 'zipcode', label: 'ZIP Code' },
                  { value: 'district', label: 'School District' },
                  { value: 'county', label: 'County' },
                  { value: 'metro', label: 'Metro' },
                ]}
              />
            </div>
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: '5px',
                width: '100%',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  gap: '5px',
                  alignItems: 'center',
                }}
              >
                <label>Activity</label>
                <Select
                  defaultValue={'price'}
                  value={chartView}
                  style={{ width: '160px' }}
                  onChange={(value) => {
                    if (!isMountedRef.current) return;
                    setChartView(value);
                  }}
                  size="small"
                  options={[
                    {
                      value: 'price',
                      label: searchingMode === 'Lease' ? 'Rent' : 'Price',
                    },
                    {
                      value: 'listclose',
                      // label: 'Active / Closed',
                      label: 'Inventory',
                    },
                    { value: 'dom', label: 'DOM' },
                  ]}
                />
              </div>
              {dataType &&
                dataTypeOptions.length > 0 &&
                (chartView === 'price' || chartView === 'listclose') && (
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      gap: '5px',
                      alignItems: 'center',
                    }}
                  >
                    <label>Data Type</label>
                    <Select
                      value={dataType}
                      onChange={(value) => {
                        if (!isMountedRef.current) return;
                        setDataType(value);
                      }}
                      style={{ width: '170px' }}
                      size="small"
                      options={dataTypeOptions}
                    />
                  </div>
                )}
            </div>
          </div>
          <div style={{ display: 'flex', flexDirection: 'row', gap: '10px' }}>
            <div
              id="marketConditionsHistory-dateViewed"
              style={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
              }}
            >
              {/* {sliderDataRef.current &&
              sliderDataRef.current.minText &&
              sliderDataRef.current.minText.length > 0 ? (
                <span>{`${sliderDataRef.current.minText} to ${sliderDataRef.current.maxText}`}</span>
              ) : (
                <span></span>
              )} */}
              {/* {sliderData.minText && sliderData.minText.length > 0 ? (
              <span>{`${sliderData.minText} to ${sliderData.maxText}`}</span>
            ) : priceDataPSF && priceDataPSF.length > 0 ? (
              <span>{`${priceDataPSF[0].date} to ${
                priceDataPSF[priceDataPSF.length - 1].date
              }`}</span>
            ) : (
              <span></span>
            )} */}
            </div>
            {!chartLoading && currentMLSListingChartData.length > 0 && (
              <div>
                <Dropdown
                  menu={{
                    items: [
                      {
                        label: 'Export to CSV',
                        key: 'export-to-csv',
                      },
                    ],
                    onClick: ({ key }) => {
                      if (key === 'export-to-csv') {
                        exportToCSV();
                      }
                    },
                  }}
                  placement="bottomRight"
                >
                  <Button type="text">
                    <IoMenu />
                  </Button>
                </Dropdown>
              </div>
            )}
          </div>
        </div>
      </div>
      {(eventCoordinates.length > 0 || drawnCustomPolygons.length > 0) && (
        <div
          style={{
            position: 'relative',
            minHeight: '441px',
            maxHeight: '441px',
          }}
        >
          <div
            className={styles.dividerCardHeader}
            style={{ marginBottom: '10px' }}
          />

          {chartArea.length > 1 && (
            <>
              <div
                style={{
                  position: 'absolute',
                  top: '12px',
                  right: '12px',
                  zIndex: 10,
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    gap: '10px',
                  }}
                >
                  {chartView === 'price' && (
                    <Radio.Group
                      defaultValue="price"
                      value={priceViewType}
                      size="small"
                      onChange={(e) => {
                        setPriceViewType(e.target.value);
                      }}
                    >
                      <Radio.Button value="price">
                        {searchingMode === 'Lease' ? 'Rent' : 'Price'}
                      </Radio.Button>
                      <Radio.Button value="psf">
                        {searchingMode === 'Lease' ? 'RSF' : 'PSF'}
                      </Radio.Button>
                    </Radio.Group>
                  )}
                  <Button
                    size="small"
                    onClick={() => {
                      setDefaultOrYoY(
                        defaultOrYoY === 'default' ? 'yoy' : 'default',
                      );
                    }}
                  >
                    {defaultOrYoY === 'default' ? 'YoY' : 'Default'}
                  </Button>
                </div>
              </div>
              <CompareChart
                data={compareData}
                loading={chartLoading}
                formatting={compareChartFormatting}
                onReady={onReady}
              />
            </>
          )}

          {chartArea.length === 1 && (
            <>
              {chartView === 'price' && (
                <>
                  <div
                    style={{
                      position: 'absolute',
                      top: '12px',
                      right: '12px',
                      zIndex: 10,
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        gap: '10px',
                      }}
                    >
                      <Radio.Group
                        defaultValue="price"
                        value={priceViewType}
                        size="small"
                        onChange={(e) => {
                          setPriceViewType(e.target.value);
                        }}
                      >
                        <Radio.Button value="price">
                          {searchingMode === 'Lease' ? 'Rent' : 'Price'}
                        </Radio.Button>
                        <Radio.Button value="psf">
                          {searchingMode === 'Lease' ? 'RSF' : 'PSF'}
                        </Radio.Button>
                      </Radio.Group>

                      <Button
                        size="small"
                        onClick={() => {
                          setDefaultOrYoY(
                            defaultOrYoY === 'default' ? 'yoy' : 'default',
                          );
                        }}
                      >
                        {defaultOrYoY === 'default' ? 'YoY' : 'Default'}
                      </Button>
                    </div>
                  </div>
                  {defaultOrYoY === 'default' ? (
                    <PriceChart
                      data={
                        priceViewType === 'price' ? priceDataFull : priceDataPSF
                      }
                      loading={chartLoading}
                      onReady={onReady}
                    />
                  ) : (
                    <YoYChart
                      data={
                        priceViewType === 'price'
                          ? priceDataFullYoY
                          : priceDataPSFYoY
                      }
                      loading={chartLoading}
                      onReady={onReady}
                    />
                  )}
                </>
              )}

              {chartView === 'listclose' && (
                <>
                  <div
                    style={{
                      position: 'absolute',
                      top: '12px',
                      right: '12px',
                      zIndex: 10,
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        gap: '10px',
                      }}
                    >
                      <Button
                        size="small"
                        onClick={() => {
                          setDefaultOrYoY(
                            defaultOrYoY === 'default' ? 'yoy' : 'default',
                          );
                        }}
                      >
                        {defaultOrYoY === 'default' ? 'YoY' : 'Default'}
                      </Button>
                    </div>
                  </div>
                  {defaultOrYoY === 'default' ? (
                    <ListClosedChart
                      data={listClosedData}
                      loading={chartLoading}
                      onReady={onReady}
                    />
                  ) : (
                    <YoYChart
                      data={listClosedDataYoY}
                      loading={chartLoading}
                      onReady={onReady}
                    />
                  )}
                </>
              )}

              {chartView === 'dom' && (
                <>
                  <div
                    style={{
                      position: 'absolute',
                      top: '12px',
                      right: '12px',
                      zIndex: 10,
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        gap: '10px',
                      }}
                    >
                      <Button
                        size="small"
                        onClick={() => {
                          setDefaultOrYoY(
                            defaultOrYoY === 'default' ? 'yoy' : 'default',
                          );
                        }}
                      >
                        {defaultOrYoY === 'default' ? 'YoY' : 'Default'}
                      </Button>
                    </div>
                  </div>
                  {defaultOrYoY === 'default' ? (
                    <DOMChart
                      data={domData}
                      loading={chartLoading}
                      onReady={onReady}
                    />
                  ) : (
                    <YoYChart
                      data={domDataYoY}
                      loading={chartLoading}
                      onReady={onReady}
                    />
                  )}
                </>
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
}

export default MarketConditionGraph;
