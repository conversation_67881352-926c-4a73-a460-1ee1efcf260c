import styles from '@/components/ResultTable/resultTable.css';
import CloseOutlined from '@ant-design/icons/CloseOutlined';
import { default as turf_distance } from '@turf/distance';
import { point } from '@turf/helpers';
import {
  Button,
  Col,
  DatePicker,
  Form,
  Modal,
  Row,
  Select,
  Switch,
  Table,
} from 'antd';
import dayjs from 'dayjs';
import isEmpty from 'lodash.isempty';
import isEqual from 'lodash.isequal';
import moment from 'moment';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { connect } from 'umi';
import { dateFormat } from '../../../constants';
import { getNewHomeBuilders } from '../../../services/data';
import { calculateMedian } from '../../../utils/calculations';
import { formatter } from '../../../utils/money';
import EmptyState from '../../EmptyState';
import FilterForm from './FilterForm';
const mockData = [
  {
    base_id: '70e32350-056a-5c5f-9f90-4d413f98ffb1',
    property_id: '2283795',
    status: 'Now Building',
    name: null,
    address: '13060 Yellowstone Way. Providence Village, TX 76227',
    builder: 'M/I Homes',
    community: 'Catalina at Delta Shores',
    price: 434990,
    bed_rooms: 4,
    bath_rooms: 2.0,
    garage: 2,
    square_feet: 2367,
    standard_city: 'Providence Village',
    standard_state: 'TX',
    first_seen: '2023-02-19T06:00:00.000+00:00',
    postal_code: '76227',
    geom: {
      type: 'Point',
      coordinates: [-96.771301, 33.28442],
    },
  },
  {
    base_id: '737566a3-4d16-568e-994c-a45e49d90699',
    property_id: '2283793',
    status: 'Now Building',
    name: null,
    address: '13053 Mizell Lane. Providence Village, TX 76227',
    builder: 'M/I Homes',
    community: 'Catalina at Delta Shores',
    price: 396354,
    bed_rooms: 4,
    bath_rooms: 3.0,
    garage: 2,
    square_feet: 2143,
    standard_city: 'Providence Village',
    standard_state: 'TX',
    first_seen: '2023-02-19T06:00:00.000+00:00',
    postal_code: '76227',
    geom: {
      type: 'Point',
      coordinates: [-96.940434, 33.24816],
    },
  },
  {
    base_id: 'ad104b27-186c-51cd-80b1-d2daa3be0c00',
    property_id: '1960172',
    status: 'Now Building',
    name: null,
    address: '1401 Snapdragon Court. Prosper, TX 75078',
    builder: 'M/I Homes',
    community: 'Seasons at Sierra Vista',
    price: 895150,
    bed_rooms: 3,
    bath_rooms: 2.0,
    garage: 2,
    square_feet: 1870,
    standard_city: 'Prosper',
    standard_state: 'TX',
    first_seen: '2023-02-19T06:00:00.000+00:00',
    postal_code: '75078',
    geom: {
      type: 'Point',
      coordinates: [-96.761654, 33.26295],
    },
  },
];

export const initialFormValues = {
  minPrice: 0,
  maxPrice: 60000000,
  minBed: 0,
  maxBed: 6,
  minBath: 0,
  maxBath: 6,
  minSqft: 0,
  maxSqft: -1, // no limit
  firstSeenDate: [
    new Date(new Date().setMonth(new Date().getMonth() - 6)).toLocaleDateString(
      'en-CA',
      { year: 'numeric', month: '2-digit', day: '2-digit' },
    ),
    new Date().toLocaleDateString('en-CA', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    }),
  ],
};

const NewBuilds = connect(({ CMA }) => ({
  map: CMA.map,
  searchingMode: CMA.searchingMode,
  eventCoordinates: CMA.eventCoordinates,
  currentRadiusMile: CMA.currentRadiusMile,
  selectedRowKeysNewBuilds: CMA.selectedRowKeysNewBuilds,
  currentNewBuildsProperties: CMA.currentNewBuildsProperties,
  mapExpandedView: CMA.mapExpandedView,
  drawnCustomPolygons: CMA.drawnCustomPolygons,
  mapLocateProperty: CMA.mapLocateProperty,
  cmaTabKey: CMA.cmaTabKey,
  newHomeTabChecked: CMA.newHomeTabChecked,
  currentHighlightCoordinates: CMA.currentHighlightCoordinates,
  locateNewBuildId: CMA.locateNewBuildId,
}))(function (props) {
  const tblRef = useRef(null);
  const [buildSource, setBuildSource] = useState('NewHomeSource');
  const [buildStatus, setBuildStatus] = useState('All');
  const [builderName, setBuilderName] = useState('All');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [form] = Form.useForm();
  const newConstructionHomeRef = useRef(null);
  const [scrollToSelectedRow, setScrollToSelectedRow] = useState(false);
  //Builder Filter List
  const [builders, setBuilders] = useState([]);
  const [highlightBaseIdRow, setHighlightBaseIdRow] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    getNewHomeBuilders().then((data) => {
      if (data) {
        const transformedData = data.map((item) => ({
          value: item.builder,
          label: item.builder,
        }));
        // Set the state with the 'All' option as the first element
        setBuilders([{ value: 'All', label: 'All' }, ...transformedData]);
      }
    });
  }, []);

  useEffect(() => {
    if (!highlightBaseIdRow) return;
    const click = (e) => {
      if (e.target.id === 'locatePropertyButton') return;
      setHighlightBaseIdRow((prevState) =>
        prevState != null ? null : prevState,
      );
    };
    document.addEventListener('click', click);
    return () => {
      document.removeEventListener('click', click);
    };
  }, [highlightBaseIdRow]);

  useEffect(() => {
    if (!props.map) return;

    updateMapPOIFilter();
  }, [props.map, buildStatus, builderName, buildSource]);

  useEffect(() => {
    if (props.locateNewBuildId == null) return;

    tblRef.current.scrollTo({
      key: props.locateNewBuildId,
    });

    setHighlightBaseIdRow(props.locateNewBuildId);
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: { locateNewBuildId: null },
    });
  }, [props.locateNewBuildId]);

  useEffect(() => {
    if (
      props.eventCoordinates.length > 0 ||
      props.drawnCustomPolygons.length > 0
    ) {
      if (
        (props.cmaTabKey !== '3' && props.newHomeTabChecked) ||
        props.cmaTabKey === '3'
      ) {
        fetchNewBuildListings();
        return;
      }
    }
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: { currentNewBuildsProperties: [], selectedRowKeysNewBuilds: [] },
    });
  }, [props.eventCoordinates, props.currentRadiusMile, props.drawnCustomPolygons, buildStatus, builderName, buildSource, props.cmaTabKey, props.newHomeTabChecked]);

  const filterBtnHandler = () => {
    setIsModalOpen(true);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const applyFormHandler = (values) => {
    setIsModalOpen(false);
    fetchNewBuildListings();
    updateMapPOIFilter();
  };

  const getColumns = useCallback(() => {
    const columns = [
      {
        title: 'Builder',
        dataIndex: 'builder',
        key: 'builder',
        width: 100,
        align: 'left',
        fixed: 'left',
      },
      {
        title: 'Community',
        dataIndex: 'community',
        key: 'community',
        width: 100,
        align: 'left',
        fixed: 'left',
      },
      {
        title: 'Address',
        dataIndex: 'address',
        width: 200,
        align: 'left',
        fixed: 'left',
      },
      {
        title: 'Price',
        dataIndex: 'price',
        align: 'center',
        render: (text) => '$' + formatter(text),
        sorter: (a, b) => a.price - b.price,
      },
      {
        title: 'Bd',
        dataIndex: 'bed_rooms',
        align: 'center',
        width: 50,
        sorter: (a, b) => a.bed_rooms - b.bed_rooms,
      },
      {
        title: 'Ba',
        dataIndex: 'bath_rooms',
        align: 'center',
        width: 50,
        sorter: (a, b) => a.bath_rooms - b.bath_rooms,
      },
      {
        title: 'Sqft',
        dataIndex: 'square_feet',
        align: 'center',
        width: 60,
        render: (text) => (text ? formatter(text) : ''),
        sorter: (a, b) => a.square_feet - b.square_feet,
      },
      {
        title: 'Garage',
        dataIndex: 'garage',
        align: 'center',
        width: 65,
        sorter: (a, b) => a.garage - b.garage,
      },
      {
        title: 'First Seen',
        dataIndex: 'first_seen',
        align: 'center',
        render: (text) => {
          if (text) {
            return moment(text).format(dateFormat);
          } else {
            return '';
          }
        },
        sorter: (a, b) => {
          if (a.first_seen && b.first_seen) {
            return +moment(a.first_seen) - +moment(b.first_seen);
          } else {
            return a.first_seen || b.first_seen;
          }
        },
      },
      {
        title: 'Closed Date',
        dataIndex: 'close_date',
        align: 'center',
        render: (text) => {
          if (text) {
            return moment(text).format(dateFormat);
          } else {
            return '';
          }
        },
        sorter: (a, b) => {
          if (a.closed_date && b.closed_date) {
            return +moment(a.closed_date) - +moment(b.closed_date);
          } else {
            return a.closed_date || b.closed_date;
          }
        },
      },
      {
        title: 'PSF',
        dataIndex: 'psf',
        align: 'center',
        render: (text, record) => {
          if (record.psf) return '$' + record.psf ? record.psf.toFixed(2) : 0;
          return '';
        },
      },
    ];

    if (props.eventCoordinates.length > 0) {
      const distanceColumn = {
        title: 'Dist.',
        dataIndex: 'distance',
        key: 'distance',
        align: 'center',
        width: 60,
        render: (text, record) => {
          if (props.eventCoordinates.length > 0) {
            const propertyPoint = point(record.geom.coordinates);
            const eventPoint = point(props.eventCoordinates);
            const distance = turf_distance(propertyPoint, eventPoint, {
              units: 'miles',
            });

            return Math.floor(distance * 10) / 10 + ' mi';
          }
          // return (parseFloat(text) / 1609.34).toFixed(1) + ' mi';
        },
        defaultSortOrder: 'ascend',
        sorter: (a, b) => {
          const eventPoint = point(props.eventCoordinates);
          const propertyPointA = point(a.geom.coordinates);
          const distanceA = turf_distance(propertyPointA, eventPoint, {
            units: 'miles',
          });
          const propertyPointB = point(b.geom.coordinates);
          const distanceB = turf_distance(propertyPointB, eventPoint, {
            units: 'miles',
          });
          return distanceA - distanceB;
        },
      };
      columns.splice(2, 0, distanceColumn);
    }

    if (props.mapExpandedView) {
      columns.splice(1, 1); // distance
      columns.splice(2, 1); // bed
      columns.splice(2, 1); // bath
      columns.splice(3, 1); // garage
      columns.splice(3, 1); // firstseen
    }

    return columns;
  }, [props.eventCoordinates, props.mapExpandedView]);

  const onRowSelect = (selectedRowKeys) => {
    if (!isEqual(props.selectedRowKeysNewBuilds, selectedRowKeys)) {
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          selectedRowKeysNewBuilds: selectedRowKeys,
        },
      });
    }
  };

  const medianPriceResult = useMemo(() => {
    const medianPrice = calculateMedian(
      props.selectedRowKeysNewBuilds.map((base_id) => {
        const build = props.currentNewBuildsProperties.find(
          (build) => build.base_id === base_id,
        );
        return build.price;
      }),
    );
    if (medianPrice) {
      return medianPrice;
    } else {
      return '-';
    }
  }, [props.selectedRowKeysNewBuilds, props.currentNewBuildsProperties]);

  const medianPSFResult = useMemo(() => {
    const medianPSF = calculateMedian(
      props.selectedRowKeysNewBuilds.map((base_id) => {
        const build = props.currentNewBuildsProperties.find(
          (build) => build.base_id === base_id,
        );
        return build.psf;
      }),
    );
    if (medianPSF) {
      return medianPSF.toFixed(2);
    } else {
      return '-';
    }
  }, [props.selectedRowKeysNewBuilds, props.currentNewBuildsProperties]);

  const selectChangeHandler = (value, type) => {
    if (type === 'source') setBuildSource(value);
    if (type === 'status') setBuildStatus(value);
    if (type === 'builder') setBuilderName(value);
    // fetchNewBuildListings();
  };

  const fetchNewBuildListings = () => {
    let formValues = form.getFieldsValue();
    if (isEmpty(formValues)) {
      formValues = initialFormValues;
    }

    const params = { ...formValues };
    params['source'] = buildSource;
    params['status'] = buildStatus;
    params['builder'] = builderName;

    let location = {};
    if (props.drawnCustomPolygons.length > 0) {
      location = {
        type: 'Polygon',
        body: props.drawnCustomPolygons,
      };
    } else {
      location = {
        type: 'Default',
        lng: props.eventCoordinates[0],
        lat: props.eventCoordinates[1],
        distance: props.currentRadiusMile * 1609.34,
      };
    }

    if (params['status'] != null && params['builder'] !== null) {
      if (
        props.eventCoordinates.length > 0 ||
        props.drawnCustomPolygons.length > 0
      ) {
        setIsLoading(true);
        props.dispatch({
          type: 'CMA/getNewBuildsProperties',
          payload: {
            ...params,
            minFirstSeenDate: moment(params.firstSeenDate[0]).format(
              dateFormat,
            ),
            maxFirstSeenDate: moment(params.firstSeenDate[1]).format(
              dateFormat,
            ),
            ...location,
            setIsLoading: setIsLoading,
          },
        });
      }
    }
  };

  const updateMapPOIFilter = () => {
    if (!props.map) return;

    let formValues = form.getFieldsValue();
    if (isEmpty(formValues)) {
      formValues = initialFormValues;
    }

    const params = { ...formValues };

    const minDate = params.firstSeenDate[0];
    const maxDate = params.firstSeenDate[1];
    console.log('minDate: ', minDate);
    console.log('maxDate: ', maxDate);
    params.minFirstSeenDate = moment(params.firstSeenDate[0]).format(
      dateFormat,
    );
    params.maxFirstSeenDate = moment(params.firstSeenDate[1]).format(
      dateFormat,
    );

    const filter = {
      source: buildSource,
      status: buildStatus,
      builder: builderName,
      minPrice: params.minPrice,
      maxPrice: params.maxPrice,
      minBed: params.minBed,
      maxBed: params.maxBed,
      minBath: params.minBath,
      maxBath: params.maxBath,
      minSqft: params.minSqft,
      maxSqft: params.maxSqft,
      minFirstSeenDate: params.minFirstSeenDate,
      maxFirstSeenDate: params.maxFirstSeenDate,
    };

    props.map.fire('NewHomesLayer.updateNewHomeFilter', {
      payload: {
        filter: filter,
      },
    });

    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        newConstructionFilters: filter,
      },
    });
  };

  const onRow = (record) => {
    return {
      onMouseEnter: (event) => {
        // if (
        //   !isEqual(props.currentHighlightCoordinates, record.geom.coordinates)
        // ) {
        props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            currentHighlightCoordinates: record.geom.coordinates,
            priceHighlightMarker: record.price,
            typeHighlightMarker: 'newBuild',
          },
        });
        // }
      },
    };
  };

  return (
    <>
      <div ref={newConstructionHomeRef} className={styles.cardWrapper}>
        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            marginBottom: '10px',
            flexWrap: 'wrap',
            gap: '10px',
          }}
        >
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              // justifyContent: props.mapExpandedView ? 'auto' : 'left',
              gap: '5px',
              // marginBottom: '20px',
            }}
          >
            <div className={styles.cardTitleH2}>New Construction Homes</div>
            <div style={{ display: 'flex', flexDirection: 'row', gap: '10px' }}>
              <div>
                <Select
                  style={{ width: '175px' }}
                  placeholder="Select Source"
                  value={buildSource}
                  onChange={(value) => {
                    selectChangeHandler(value, 'source');
                  }}
                  // size="small"
                  options={[
                    { value: 'NewHomeSource', label: 'New Home Source' },
                    {
                      value: 'Livabl',
                      label: 'Livabl',
                    },
                  ]}
                />
              </div>
              <div>
                <Select
                  style={{ width: '150px' }}
                  placeholder="Select Status"
                  value={buildStatus}
                  onChange={(value) => {
                    selectChangeHandler(value, 'status');
                  }}
                  // size="small"
                  options={[
                    { value: 'All', label: 'All' },
                    {
                      value: 'Available',
                      label: 'Available',
                    },
                    {
                      value: 'Ready to Build',
                      label: 'Ready to Build',
                    },
                    {
                      value: 'Now Building',
                      label: 'Now Building',
                    },
                    {
                      value: 'Model Home',
                      label: 'Model Home',
                    },
                    {
                      value: 'Not Specified',
                      label: 'Not Specified',
                    },
                  ]}
                ></Select>
              </div>

              <div>
                <Select
                  showSearch
                  style={{ width: '200px' }}
                  placeholder="Select Builder"
                  allowClear={{ clearIcon: <CloseOutlined /> }}
                  value={builderName}
                  onChange={(value) => {
                    selectChangeHandler(value, 'builder');
                  }}
                  // size="small"
                  //TODO: New Home Tab - builder dropdown - make it possible to search any homebuilder
                  // options={[
                  //   { value: 'All', label: 'All' },
                  //   {
                  //     value: 'D.R. Horton',
                  //     label: 'D.R. Horton',
                  //   },
                  //   {
                  //     value: 'Lennar',
                  //     label: 'Lennar',
                  //   },
                  //   {
                  //     value: 'Pulte Homes',
                  //     label: 'Pulte Homes',
                  //   },
                  //   {
                  //     value: 'Allen Edwin Homes',
                  //     label: 'Allen Edwin Homes',
                  //   },
                  //   {
                  //     value: 'Toll Brothers',
                  //     label: 'Toll Brothers',
                  //   },
                  //   {
                  //     value: 'Meritage Homes',
                  //     label: 'Meritage Homes',
                  //   },
                  //   {
                  //     value: 'Not Specified',
                  //     label: 'Not Specified',
                  //   },
                  // ]}
                  options={builders}
                ></Select>
              </div>
              <div>
                {/* <Button onClick={filterBtnHandler} size="small"> */}
                <Button onClick={filterBtnHandler}>Settings</Button>
              </div>
            </div>
          </div>
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'end',
            }}
          >
            <div>
              {props.currentNewBuildsProperties.length > 0 && (
                <div
                  style={{
                    // display: 'flex',
                    // flexDirection: 'row',
                    // gap: '10px',
                    textAlign: 'right',
                  }}
                >
                  <span style={{ marginRight: '10px' }}>
                    Total:{' '}
                    <b>
                      {props.currentNewBuildsProperties.length.toLocaleString()}
                    </b>
                  </span>
                  <span style={{ marginRight: '10px' }}>
                    Median Price: <b>${formatter(medianPriceResult || '-')}</b>
                  </span>
                  <span style={{ marginRight: '10px' }}>
                    Median PSF: <b>${formatter(medianPSFResult || '-')}</b>
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
        <Row>
          {(props.currentNewBuildsProperties.length != 0 && !isLoading) ||
          isLoading ? (
            <Table
              ref={tblRef}
              virtual
              rowKey={(record) => record.base_id}
              columns={getColumns()}
              // dataSource={props.currentMLSPropertiesFiltered}
              dataSource={props.currentNewBuildsProperties}
              variant={'filled'}
              size="small"
              loading={isLoading}
              pagination={false}
              rowSelection={{
                selectedRowKeys: props.selectedRowKeysNewBuilds,
                onChange: onRowSelect,
              }}
              rowClassName={(record, index) => {
                let className = styles.propertyDataTableRow;
                if (highlightBaseIdRow != null) {
                  if (record.base_id == highlightBaseIdRow) {
                    className += ' ' + styles.mapLocatePropertySelected;
                  }
                }
                return className;
              }}
              onRow={onRow}
              sticky={{
                getContainer: () =>
                  document.getElementById('resultTableScrollWrapper'),
              }}
              tableLayout="auto"
              scroll={{ x: 1000, y: 500 }}
              summary={() => {
                return (
                  <Table.Summary fixed>
                    <Table.Summary.Row>
                      <Table.Summary.Cell
                        colSpan={props.mapExpandedView ? 2 : 3}
                      >
                        Median Price of Selected
                      </Table.Summary.Cell>
                      <Table.Summary.Cell
                        className={styles.tableSummaryCellTextAlignCenter}
                      >
                        {formatter(medianPriceResult || '-')}
                      </Table.Summary.Cell>
                    </Table.Summary.Row>
                  </Table.Summary>
                );
              }}
            />
          ) : buildStatus === null || builderName === null ? (
            <EmptyState description="Please select build status and name" />
          ) : (
            <EmptyState description="No New Homes Properties Found" />
          )}
        </Row>
      </div>
      <Modal open={isModalOpen} onCancel={handleCancel} footer={null}>
        <h3 style={{ fontWeight: 'bold' }}>New Construction Homes Settings</h3>
        <FilterForm
          form={form}
          handleCancel={handleCancel}
          applyFormHandler={applyFormHandler}
          initialFormValues={initialFormValues}
        />
      </Modal>
    </>
  );
  // } else {
  //   return null;
  // }
});

export default NewBuilds;
