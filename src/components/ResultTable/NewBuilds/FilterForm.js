import {
  <PERSON><PERSON>,
  Col,
  DatePicker,
  Form,
  InputNumber,
  Row,
  Select,
  Tooltip,
} from 'antd';
import dayjs from 'dayjs';
import React from 'react';
import { dateFormat } from '../../../constants';
import styles from './newBuilds.css';
import {
  optionsBaths,
  optionsBeds,
  optionsSalesPrice,
  optionsSqft,
} from './utils';

const { RangePicker } = DatePicker;

const formItemDefaultStyle = {
  labelCol: {
    span: 24,
  },
  wrapperCol: {
    span: 24,
  },
};

function FilterForm({
  form,
  handleCancel,
  applyFormHandler,
  initialFormValues,
}) {
  return (
    <Form
      form={form}
      className={styles.allForms}
      style={{ flexWrap: 'wrap' }}
      labelCol={{
        span: 16,
        offset: 4,
      }}
      wrapperCol={{
        span: 16,
        offset: 4,
      }}
      layout="vertical"
      initialValues={initialFormValues}
      onFinish={applyFormHandler}
    >
      <Row justify="space-between" gutter={32}>
        <Col span={12}>
          <Form.Item
            label="Min Price"
            name="minPrice"
            {...formItemDefaultStyle}
          >
            <Select options={optionsSalesPrice} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="Max Price"
            name="maxPrice"
            {...formItemDefaultStyle}
          >
            <Select options={optionsSalesPrice} />
          </Form.Item>
        </Col>
      </Row>

      <Row justify="space-between" gutter={32}>
        <Col span={12}>
          <Form.Item label="Min Beds" name="minBed" {...formItemDefaultStyle}>
            <Select options={optionsBeds} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Max Beds" name="maxBed" {...formItemDefaultStyle}>
            <Select options={optionsBeds} />
          </Form.Item>
        </Col>
      </Row>

      <Row justify="space-between" gutter={32}>
        <Col span={12}>
          <Form.Item label="Min Baths" name="minBath" {...formItemDefaultStyle}>
            <Select options={optionsBaths} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Max Baths" name="maxBath" {...formItemDefaultStyle}>
            <Select options={optionsBaths} />
          </Form.Item>
        </Col>
      </Row>

      <Row justify="space-between" gutter={32}>
        <Col span={12}>
          <Form.Item label="Min Sqft" name="minSqft" {...formItemDefaultStyle}>
            <Select options={optionsSqft} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Max Sqft" name="maxSqft" {...formItemDefaultStyle}>
            <Select options={optionsSqft} />
          </Form.Item>
        </Col>
      </Row>

      <Row>
        <Col>
          <Form.Item
            label="Min First Seen Date"
            name="firstSeenDate"
            rules={[{ required: true, message: 'Please select dates!' }]}
            {...formItemDefaultStyle}
          >
            <CustomRangePicker />
            {/* <RangePicker
              width="100%"
              onChange={(date, dateString) => {
                console.log(date, dateString);
              }}
            /> */}
          </Form.Item>
        </Col>
      </Row>

      <Row justify={'end'} gutter={[10]}>
        <Col>
          <Button onClick={handleCancel}>Cancel</Button>
        </Col>
        <Col>
          <Button type="primary" htmlType="submit">
            Apply
          </Button>
        </Col>
      </Row>
    </Form>
  );
}

const CustomRangePicker = ({ value, onChange }) => {
  const { status, errors } = Form.Item.useStatus();

  return (
    <RangePicker
      width="100%"
      value={value.map((date) => dayjs(date, 'YYYY-MM-DD'))}
      status={status}
      errors={errors}
      onChange={(date, dateString) => {
        onChange(dateString);
      }}
    />
  );
};

export default FilterForm;
