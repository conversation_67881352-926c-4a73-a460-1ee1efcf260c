import { formatterKM } from '../../../utils/money';

const generateSelectOptionsSalesPrice = () => {
  let options = [];
  for (let price = 0; price <= 500 * 1000; price = price + 5 * 1000) {
    options.push({
      label: formatterKM(price),
      value: price,
    });
  }
  for (
    let price = 525 * 1000;
    price <= 1000 * 1000;
    price = price + 25 * 1000
  ) {
    options.push({
      label: formatterKM(price),
      value: price,
    });
  }
  for (
    let price = 1250 * 1000;
    price <= 5000 * 1000;
    price = price + 250 * 1000
  ) {
    options.push({
      label: formatterKM(price),
      value: price,
    });
  }
  for (
    let price = 5500 * 1000;
    price <= 10 * 1000 * 1000;
    price = price + 500 * 1000
  ) {
    options.push({
      label: formatterKM(price),
      value: price,
    });
  }
  for (
    let price = 12 * 1000 * 1000;
    price <= 20 * 1000 * 1000;
    price = price + 2 * 1000 * 1000
  ) {
    options.push({
      label: formatterKM(price),
      value: price,
    });
  }
  for (
    let price = 30 * 1000 * 1000;
    price <= 60 * 1000 * 1000;
    price = price + 10 * 1000 * 1000
  ) {
    options.push({
      label: formatterKM(price),
      value: price,
    });
  }
  return options;
};

export const optionsSalesPrice = generateSelectOptionsSalesPrice();

const generateSelectOpitonsContinuous = (min, max, additionalLabelText) => {
  let options = [];
  for (let i = min; i <= max; i++) {
    const singleOption = {
      label: i + '' + additionalLabelText, // convert to string
      value: i,
    };
    options.push(singleOption);
  }
  return options;
};

export const optionsBeds = generateSelectOpitonsContinuous(1, 6, '');
export const optionsBaths = generateSelectOpitonsContinuous(1, 6, '');

const generateSelectOpitonsSqft = () => {
  let options = [];
  for (let sqft = 0; sqft <= 5000; sqft = sqft + 100) {
    options.push({
      label: sqft,
      value: sqft,
    });
  }
  options.push({ label: 'No Limit', value: -1 });
  return options;
};

export const optionsSqft = generateSelectOpitonsSqft();
