import { BTRCommunityInfoData } from './type';

export function testLog(stuff: any) {
  console.log('test6', { stuff });
}

export function sqftToAcre(sqft: number) {
  const sqftPerAcre = 43560;
  return sqft / sqftPerAcre;
}

interface MedianResult {
  minMedian: number;
  maxMedian: number;
}

export function formatPricePerSqftArce(number: any) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(number);
}
export function calculateMedian(
  selectedRowKeys: React.Key[],
  btrData: BTRCommunityInfoData[],
): MedianResult {
  const selectedData = btrData.filter((record) =>
    selectedRowKeys.includes(record.id),
  );

  const minPrices = selectedData.map((record) => record.minPrice);
  const maxPrices = selectedData.map((record) => record.maxPrice);

  minPrices.sort((a, b) => a - b);
  maxPrices.sort((a, b) => a - b);

  const mid = Math.floor(minPrices.length / 2);

  return {
    minMedian:
      minPrices.length % 2 === 0
        ? (minPrices[mid - 1] + minPrices[mid]) / 2
        : minPrices[mid],
    maxMedian:
      maxPrices.length % 2 === 0
        ? (maxPrices[mid - 1] + maxPrices[mid]) / 2
        : maxPrices[mid],
  };
}

export function calculateMedianPricePerSqFt(
  selectedRowKeys: React.Key[],
  btrData: BTRCommunityInfoData[],
): MedianResult {
  const selectedData = btrData.filter((record) =>
    selectedRowKeys.includes(record.id),
  );

  const minPricesPerSqFt = selectedData
    .map((record) => (record.size > 0 ? record.minPrice / record.size : null))
    .filter((price): price is number => price !== null);

  const maxPricesPerSqFt = selectedData
    .map((record) => (record.size > 0 ? record.maxPrice / record.size : null))
    .filter((price): price is number => price !== null);

  if (!minPricesPerSqFt.length || !maxPricesPerSqFt.length) {
    return { minMedian: 0, maxMedian: 0 };
  }

  minPricesPerSqFt.sort((a, b) => a - b);
  maxPricesPerSqFt.sort((a, b) => a - b);

  const midMin = Math.floor(minPricesPerSqFt.length / 2);
  const midMax = Math.floor(maxPricesPerSqFt.length / 2);

  return {
    minMedian:
      minPricesPerSqFt.length % 2 === 0
        ? (minPricesPerSqFt[midMin - 1] + minPricesPerSqFt[midMin]) / 2
        : minPricesPerSqFt[midMin],
    maxMedian:
      maxPricesPerSqFt.length % 2 === 0
        ? (maxPricesPerSqFt[midMax - 1] + maxPricesPerSqFt[midMax]) / 2
        : maxPricesPerSqFt[midMax],
  };
}

// export function calculateMedianPricePerAcre(
//   selectedRowKeys: import('react').Key[],
//   landCompsDataForRender: LandCompData[],
// ): number {
//   // Filter the data to only include selected rows
//   const selectedData = landCompsDataForRender.filter((record) =>
//     selectedRowKeys.includes(record.mlsid),
//   );

//   // Calculate the price per square foot for each selected entry
//   const pricesPerAcre = selectedData
//     .map((record) => {
//       const price =
//         record.status === 'Closed' ? record.closeprice : record.currentprice;
//       if (record.lot_size > 0) {
//         // Ensure lot_size is positive to avoid division by zero
//         return price / sqftToAcre(record.lot_size);
//       }
//       return null; // or handle zero lot_size in a specific way if needed
//     })
//     .filter((price) => price !== null); // Filter out null values if lot_size was zero

//   // Sort the prices per square foot to prepare for median calculation
//   pricesPerAcre.sort((a, b) => a - b);

//   // Calculate the median price per square foot
//   const mid = Math.floor(pricesPerAcre.length / 2);
//   let medianPricePerAcre;
//   if (pricesPerAcre.length % 2 === 0) {
//     // If even, average the two middle values
//     medianPricePerAcre = (pricesPerAcre[mid - 1] + pricesPerAcre[mid]) / 2;
//   } else {
//     // If odd, take the middle value
//     medianPricePerAcre = pricesPerAcre[mid];
//   }

//   return medianPricePerAcre;
// }
