import { arrayDifference } from '@/utils/arrayMethods';
import { formatCurrency } from '@/utils/lastSalePublicRecord';
import { Table, Tooltip } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { isEmpty, isEqual } from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import styles from '../resultTable.css';
import { useBTRCommunityData } from './hooks/useBTRCommunityData';
import { useTableFilters } from './hooks/useTableFilters';
import TableHeader from './TableHeader';
import {
  calculateMedian,
  calculateMedianPricePerSqFt,
} from './utils/functions';
import { BTRCommunityInfoData } from './utils/type';

const createColumns = (
  onFlyTo: (lng: number, lat: number) => void,
): ColumnsType<BTRCommunityInfoData> => [
  {
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
    width: 180,
    fixed: 'left',
    align: 'left',
    render: (text: string, record: BTRCommunityInfoData) => (
      <p
        className="text-xs cursor-pointer"
        onClick={() =>
          onFlyTo(record.geom.coordinates[0], record.geom.coordinates[1])
        }
      >
        {record.name}
      </p>
    ),
  },
  {
    title: 'Dist.',
    dataIndex: 'distance',
    key: 'distance',
    width: 75,
    align: 'left',
    defaultSortOrder: 'ascend',
    sorter: (a, b) => a.distance - b.distance,
    render: (text: any, record: BTRCommunityInfoData) => (
      <p className="text-xs">{(record.distance / 1609.34).toFixed(2)} mi</p>
    ),
  },
  {
    title: 'Size',
    dataIndex: 'size',
    key: 'size',
    width: 100,
    align: 'left',
    render: (text: string, record: BTRCommunityInfoData) => (
      <p className="text-xs">{record.size} sqft</p>
    ),
    sorter: (a, b) => a.size - b.size,
  },
  {
    title: 'Price',
    dataIndex: 'minPrice',
    key: 'minPrice',
    width: 100,
    align: 'left',
    sorter: (a, b) => a.minPrice - b.minPrice,
    render: (value: number, record: BTRCommunityInfoData) => (
      <p className="text-xs">
        {record.minPrice === null && record.maxPrice === null
          ? 'N/A'
          : record.maxPrice === null || record.maxPrice === 0
          ? formatCurrency(record.minPrice)
          : record.minPrice === record.maxPrice
          ? formatCurrency(record.minPrice)
          : `${formatCurrency(record.minPrice)} - ${formatCurrency(
              record.maxPrice,
            )}`}
      </p>
    ),
  },
  {
    title: 'Bed',
    dataIndex: 'bed',
    key: 'bed',
    width: 60,
    align: 'left',
    render: (text: string, record: BTRCommunityInfoData) => (
      <p className="text-xs">{record.bed}</p>
    ),
    sorter: (a, b) => a.bed - b.bed,
  },
  {
    title: 'Bath',
    dataIndex: 'bath',
    key: 'bath',
    width: 60,
    align: 'left',
    render: (text: string, record: BTRCommunityInfoData) => (
      <p className="text-xs">{record.bath}</p>
    ),
    sorter: (a, b) => a.bath - b.bath,
  },
  {
    title: 'Status',
    dataIndex: 'incomeRestricted',
    key: 'incomeRestricted',
    width: 100,
    align: 'left',
    render: (restricted: boolean) => (
      <p className="text-xs">
        {restricted ? 'Income Restricted' : 'Market Rate'}
      </p>
    ),
    filters: [
      { text: 'Market Rate', value: false },
      { text: 'Income Restricted', value: true },
    ],
  },
  {
    title: 'Actions',
    key: 'actions',
    width: 100,
    align: 'left',
    render: (text: string, record: BTRCommunityInfoData) => (
      <a
        href={record.url}
        target="_blank"
        rel="noopener noreferrer"
        className="text-xs text-blue-600 hover:text-blue-800"
      >
        View Website
      </a>
    ),
  },
];

const BTRCommunityInfoTable: React.FC = () => {
  const dispatch = useDispatch();
  const map = useSelector((state: any) => state.CMA.map);
  const btrCommunityInfoData = useSelector(
    (state: any) => state.CMA.btrCommunityInfoData,
  );
  const btrCommunityInfoDataForRender = useSelector(
    (state: any) => state.CMA.btrCommunityInfoDataForRender,
  );
  const btrCommunityInfoSelectedRowKey = useSelector(
    (state: any) => state.CMA.btrCommunityInfoSelectedRowKey,
  );
  const btrCommunityInfoMedian = useSelector(
    (state: any) => state.CMA.btrCommunityInfoMedian,
  );
  const currentPropertyAddress = useSelector(
    (state: any) => state.CMA.currentPropertyAddress,
  );
  const currentRadiusMile = useSelector(
    (state: any) => state.CMA.currentRadiusMile,
  );
  const currentStartMLS = useSelector(
    (state: any) => state.CMA.currentStartMLS,
  );
  const currentEndMLS = useSelector((state: any) => state.CMA.currentEndMLS);
  const currentStatusMLS = useSelector(
    (state: any) => state.CMA.currentStatusMLS,
  );

  // Track current table data
  const [currentTableData, setCurrentTableData] = useState<
    BTRCommunityInfoData[]
  >([]);

  // Track unselected row keys instead of selected ones
  const [unselectedRowKeys, setUnselectedRowKeys] = useState<React.Key[]>([]);

  // Track previous property address for reset
  const [prevPropertyAddress, setPrevPropertyAddress] = useState<string>(
    currentPropertyAddress,
  );

  // Store table filters
  const [tableFilters, setTableFilters] = useState<any>({});

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const landCompContainer = useRef(null);

  // Clear unselected row keys for new subject property
  if (!isEqual(currentPropertyAddress, prevPropertyAddress)) {
    setPrevPropertyAddress(currentPropertyAddress);
    setUnselectedRowKeys([]);
  }

  const { loading, error } = useBTRCommunityData(
    currentPropertyAddress,
    currentRadiusMile,
    currentStartMLS,
    currentEndMLS,
    currentStatusMLS,
    dispatch,
  );

  const { sizeOptions, bedOptions, bathOptions, priceRangeOptions } =
    useTableFilters(btrCommunityInfoData, btrCommunityInfoDataForRender);

  // Get current table data and calculate selected rows from unselected ones
  const getCurrentTableData = React.useCallback(() => {
    if (!btrCommunityInfoDataForRender)
      return { currentTableData: [], selectedRowKeys: [] };

    // Apply any additional filters based on tableFilters if needed
    const filteredData = btrCommunityInfoDataForRender;

    // Calculate selected row keys as difference between all rows and unselected rows
    const selectedRowKeys = arrayDifference(
      filteredData.map((row) => row.id),
      unselectedRowKeys,
    );

    return { currentTableData: filteredData, selectedRowKeys };
  }, [btrCommunityInfoDataForRender, unselectedRowKeys]);

  const handleFlyTo = React.useCallback(
    (longitude: number, latitude: number) => {
      map?.flyTo({
        center: [longitude, latitude],
        zoom: 16,
        speed: 2,
        curve: 1,
        easing: (t: number) => t,
      });
    },
    [map],
  );

  const columns = React.useMemo(() => {
    const baseColumns = createColumns(handleFlyTo);
    return baseColumns.map((column) => {
      switch (column.key) {
        case 'size':
          return { ...column, filters: sizeOptions };
        case 'bed':
          return { ...column, filters: bedOptions };
        case 'bath':
          return { ...column, filters: bathOptions };
        case 'minPrice':
          return { ...column, filters: priceRangeOptions };
        default:
          return column;
      }
    });
  }, [sizeOptions, bedOptions, bathOptions, priceRangeOptions, handleFlyTo]);

  const handleTableChange = React.useCallback(
    (
      pagination: any,
      filters: any,
      sorter: any,
      { action, currentDataSource }: any,
    ) => {
      if (!btrCommunityInfoData) return;

      if (!isEqual(currentDataSource, currentTableData)) {
        setCurrentTableData(currentDataSource);
      }

      if (action === 'filter') {
        setTableFilters(filters);

        const filterData = (data: BTRCommunityInfoData[]) => {
          return data.filter((item) => {
            const matchesSize =
              !filters.size?.length || filters.size.includes(item.size);
            const matchesBed =
              !filters.bed?.length || filters.bed.includes(item.bed);
            const matchesBath =
              !filters.bath?.length || filters.bath.includes(item.bath);
            const matchesStatus =
              !filters.incomeRestricted?.length ||
              filters.incomeRestricted.includes(item.incomeRestricted);

            return matchesSize && matchesBed && matchesBath && matchesStatus;
          });
        };

        const filteredData = filterData(btrCommunityInfoData);

        // Calculate and update selected row keys for the new filtered data
        const allRowKeys = filteredData.map((item) => item.id);
        const selectedRowKeys = arrayDifference(allRowKeys, unselectedRowKeys);

        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            btrCommunityInfoDataForRender: filteredData,
            btrCommunityInfoSelectedRowKey: selectedRowKeys,
            btrCommunityInfoMedian: calculateMedian(
              selectedRowKeys,
              filteredData,
            ),
            btrCommunityInfoMedianPricePerSqft: calculateMedianPricePerSqFt(
              selectedRowKeys,
              filteredData,
            ),
          },
        });
      }
    },
    [btrCommunityInfoData, currentTableData, unselectedRowKeys, dispatch],
  );

  const handleRowSelection = React.useCallback(
    (selectedRowKeys: React.Key[]) => {
      // Calculate unselected row keys based on currently visible data
      const unselectedRowKeys = arrayDifference(
        currentTableData.map((row) => row.id),
        selectedRowKeys,
      );

      setUnselectedRowKeys(unselectedRowKeys);

      if (btrCommunityInfoDataForRender?.length) {
        const median = calculateMedian(
          selectedRowKeys,
          btrCommunityInfoDataForRender,
        );
        const medianPerSqft = calculateMedianPricePerSqFt(
          selectedRowKeys,
          btrCommunityInfoDataForRender,
        );

        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            btrCommunityInfoSelectedRowKey: selectedRowKeys,
            btrCommunityInfoMedian: median,
            btrCommunityInfoMedianPricePerSqft: medianPerSqft,
          },
        });
      }
    },
    [btrCommunityInfoDataForRender, currentTableData, dispatch],
  );

  const handleRowEvents = React.useCallback(
    (record: BTRCommunityInfoData) => ({
      onMouseEnter: () => {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: { btrCommunityInfoHover: record },
        });
      },
      onMouseLeave: () => {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: { btrCommunityInfoHover: null },
        });
      },
    }),
    [dispatch],
  );

  // Update current table data and selection when data changes
  useEffect(() => {
    if (btrCommunityInfoDataForRender?.length) {
      const { currentTableData, selectedRowKeys } = getCurrentTableData();
      setCurrentTableData(currentTableData);

      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          btrCommunityInfoSelectedRowKey: selectedRowKeys,
          btrCommunityInfoMedian: calculateMedian(
            selectedRowKeys,
            btrCommunityInfoDataForRender,
          ),
          btrCommunityInfoMedianPricePerSqft: calculateMedianPricePerSqFt(
            selectedRowKeys,
            btrCommunityInfoDataForRender,
          ),
        },
      });
    }
  }, [btrCommunityInfoDataForRender, getCurrentTableData, dispatch]);

  if (error) {
    return <div className="text-red-500">Error: {error}</div>;
  }

  return (
    <div className={styles.cardWrapper} ref={landCompContainer}>
      <Table
        dataSource={btrCommunityInfoDataForRender}
        columns={columns}
        key="lc crexi table"
        title={() => (
          <TableHeader
            selectedRowKeys={btrCommunityInfoSelectedRowKey}
            medianPerSqft={btrCommunityInfoMedian}
          />
        )}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          onChange: (page, pageSize) => {
            setCurrentPage(page);
            setPageSize(pageSize);
          },
        }}
        expandable={{
          expandedRowRender: (record) => (
            <div className="px-4 py-2">
              <p className="font-medium mb-2">All Amenities:</p>
              <ul className="list-disc pl-4">
                {record.extraAmenities.map((amenity, index) => (
                  <li key={index} className="text-xs mb-1">
                    {amenity}
                  </li>
                ))}
              </ul>
            </div>
          ),
          rowExpandable: (record) => record?.extraAmenities?.length > 0,
        }}
        rowKey={(record) => record.id}
        loading={loading}
        size="small"
        rowSelection={{
          selectedRowKeys: btrCommunityInfoSelectedRowKey,
          onChange: handleRowSelection,
          selections: [
            Table.SELECTION_ALL,
            Table.SELECTION_INVERT,
            Table.SELECTION_NONE,
          ],
        }}
        onRow={handleRowEvents}
        scroll={{
          x: 1200,
          y: '400px',
        }}
        onChange={handleTableChange}
        sticky={true}
      />
    </div>
  );
};

export default BTRCommunityInfoTable;
