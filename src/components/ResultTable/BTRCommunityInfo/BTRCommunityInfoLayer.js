import { Layer, Source } from '@spatiallaser/map';
import isEmpty from 'lodash.isempty';
import mapboxgl from 'mapbox-gl';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import { renderToString } from 'react-dom/server';
import { useSelector } from 'umi';
import { geojsonTemplate } from '../../../constants';
import { formatCurrency } from '../../../utils/lastSalePublicRecord';

const sourceId = 'btr-community-info';
let priceMarkersLS = {};

function BTRCommunityInfoTooltip({ feature, onTooltipClose }) {
  const properties = feature.properties;

  // Handle website click
  const handleWebsiteClick = (e) => {
    e.stopPropagation();
    // We don't need this handler for direct links with target="_blank"
    // It's kept to prevent event bubbling
  };

  return (
    <div
      style={{
        backgroundColor: 'white',
        padding: '10px',
        fontSize: '14px',
        zIndex: '999',
        borderRadius: '4px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        maxWidth: '300px',
      }}
    >
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          marginBottom: '5px',
        }}
      >
        <h4 style={{ margin: '0 0 5px 0' }}>{properties.name}</h4>
        <button
          onClick={onTooltipClose}
          style={{
            background: 'none',
            border: 'none',
            cursor: 'pointer',
            fontSize: '16px',
            padding: '0',
            color: '#666',
            marginLeft: '5px',
          }}
        >
          ×
        </button>
      </div>
      <div style={{ marginBottom: '4px' }}>
        <strong>Size:</strong> {properties.size} sqft
      </div>
      <div style={{ marginBottom: '4px' }}>
        <strong>Price:</strong>{' '}
        {properties.minPrice === properties.maxPrice
          ? formatCurrency(properties.minPrice)
          : `${formatCurrency(properties.minPrice)} - ${formatCurrency(
              properties.maxPrice,
            )}`}
      </div>
      <div style={{ marginBottom: '4px' }}>
        <strong>Bed/Bath:</strong> {properties.bed}/{properties.bath}
      </div>
      <div style={{ marginBottom: '4px' }}>
        <strong>Status:</strong>{' '}
        {properties.incomeRestricted ? 'Income Restricted' : 'Market Rate'}
      </div>
      {Array.isArray(properties.extraAmenities) &&
        properties.extraAmenities.length > 0 && (
          <div style={{ marginBottom: '4px' }}>
            <strong>Amenities:</strong> {properties.extraAmenities.join(', ')}
          </div>
        )}
      {properties.url && (
        <div style={{ marginTop: '4px' }}>
          <a
            href={properties.url}
            target="_blank"
            rel="noopener noreferrer"
            onClick={handleWebsiteClick}
            style={{
              color: '#1890ff',
              textDecoration: 'none',
              display: 'block',
              padding: '5px 0',
            }}
          >
            Visit Website
          </a>
        </div>
      )}
    </div>
  );
}

function convertToGeoJSONFormat(data, selectedIds) {
  if (!data) return [];

  return data
    .filter((item) => selectedIds.includes(item.id))
    .map((item) => ({
      type: 'Feature',
      geometry: item.geom,
      properties: item,
    }));
}

function BTRCommunityInfoLayer() {
  const [geoJson, setGeoJson] = useState(geojsonTemplate);
  const [activePopup, setActivePopup] = useState(null);
  const [hoveredFeatureId, setHoveredFeatureId] = useState(null);
  const map = useSelector((state) => state.CMA.map);
  const btrCommunityInfoDataForRender = useSelector(
    (state) => state.CMA.btrCommunityInfoDataForRender,
  );
  const btrCommunityInfoSelectedRowKey = useSelector(
    (state) => state.CMA.btrCommunityInfoSelectedRowKey,
  );
  const btrCommunityInfoHover = useSelector(
    (state) => state.CMA.btrCommunityInfoHover,
  );
  const tooltipRef = useRef(
    new mapboxgl.Popup({
      offset: 15,
      closeOnClick: false, // Don't close the popup when clicking elsewhere on the map
      closeButton: false, // We'll provide our own close button
    }),
  );

  useEffect(() => {
    const newGeoJson = {
      ...geojsonTemplate,
      features: convertToGeoJSONFormat(
        btrCommunityInfoDataForRender,
        btrCommunityInfoSelectedRowKey,
      ),
    };
    setGeoJson(newGeoJson);
  }, [btrCommunityInfoDataForRender, btrCommunityInfoSelectedRowKey]);

  // Close any active popup when component unmounts
  useEffect(() => {
    return () => {
      if (tooltipRef.current) {
        tooltipRef.current.remove();
      }
    };
  }, []);

  const showTooltip = (e) => {
    const feature = e.features[0];
    const coordinates = feature.geometry.coordinates.slice();

    // Remember which feature we're showing
    setActivePopup(feature);

    // Create the popup content
    const placeholder = document.createElement('div');

    // The modern way to render React components
    const root = ReactDOM.createRoot(placeholder);
    root.render(
      <BTRCommunityInfoTooltip
        feature={feature}
        onTooltipClose={() => {
          tooltipRef.current.remove();
          setActivePopup(null);
        }}
      />,
    );

    // Add popup to map
    tooltipRef.current
      .setLngLat(coordinates)
      .setDOMContent(placeholder)
      .addTo(map);
  };

  // Effect for handling mouse events
  useEffect(() => {
    if (!map) return;

    // Use click for persistent tooltips
    map.on('click', `${sourceId}LayerCircle`, showTooltip);

    // Show tooltip on hover
    const handleMouseEnter = (e) => {
      // Only show hover tooltip if no active popup is displayed
      if (activePopup) return;

      map.getCanvas().style.cursor = 'pointer';
      showTooltip(e);

      // Remember which feature is being hovered
      if (e.features[0].id) {
        setHoveredFeatureId(e.features[0].id);
      }
    };

    const handleMouseLeave = (e) => {
      map.getCanvas().style.cursor = '';

      // Only remove the popup if it's from hovering (not from clicking)
      if (
        !activePopup ||
        (hoveredFeatureId && activePopup.id === hoveredFeatureId)
      ) {
        tooltipRef.current.remove();
        setActivePopup(null);
      }

      setHoveredFeatureId(null);
    };

    map.on('mouseenter', `${sourceId}LayerCircle`, handleMouseEnter);
    map.on('mouseleave', `${sourceId}LayerCircle`, handleMouseLeave);

    // Close popup when clicking elsewhere on the map (optional)
    map.on('click', (e) => {
      // Check if the click is on a marker - if not, close any popup
      const features = map.queryRenderedFeatures(e.point, {
        layers: [`${sourceId}LayerCircle`],
      });

      if (features.length === 0 && tooltipRef.current) {
        tooltipRef.current.remove();
        setActivePopup(null);
      }
    });

    return () => {
      map?.off('click', `${sourceId}LayerCircle`, showTooltip);
      map?.off('mouseenter', `${sourceId}LayerCircle`, handleMouseEnter);
      map?.off('mouseleave', `${sourceId}LayerCircle`, handleMouseLeave);
    };
  }, [map, geoJson, activePopup, hoveredFeatureId]);

  useEffect(() => {
    if (!map || !btrCommunityInfoHover || activePopup) return;

    const coordinates = btrCommunityInfoHover.geom.coordinates;
    const feature = {
      properties: btrCommunityInfoHover,
      geometry: btrCommunityInfoHover.geom,
    };

    // Create the popup content
    const placeholder = document.createElement('div');

    // The modern way to render React components
    const root = ReactDOM.createRoot(placeholder);
    root.render(
      <BTRCommunityInfoTooltip
        feature={feature}
        onTooltipClose={() => {
          tooltipRef.current.remove();
          setActivePopup(null);
        }}
      />,
    );

    tooltipRef.current
      .setLngLat(coordinates)
      .setDOMContent(placeholder)
      .addTo(map);

    setActivePopup(feature);

    return () => {};
  }, [btrCommunityInfoHover, map, activePopup]);

  const circleStyle = {
    id: `${sourceId}LayerCircle`,
    type: 'circle',
    filter: ['!', ['has', 'point_count']],
    paint: {
      'circle-radius': 8,
      'circle-color': '#7bde43',
      'circle-stroke-color': '#fff',
      'circle-stroke-width': 2,
    },
  };

  const clusterStyle = {
    id: `${sourceId}LayerClusters`,
    type: 'circle',
    filter: ['has', 'point_count'],
    paint: {
      'circle-color': '#A9BBC0',
      'circle-radius': [
        'interpolate',
        ['linear'],
        ['zoom'],
        0,
        ['max', ['*', ['^', ['get', 'point_count'], 0.5], 0.1], 12],
      ],
      'circle-opacity': 0.75,
      'circle-stroke-width': 1,
      'circle-stroke-color': 'rgba(255,255,255,1)',
    },
  };

  const clusterSymbolStyle = {
    id: `${sourceId}LayerClustersPointCount`,
    type: 'symbol',
    filter: ['has', 'point_count'],
    layout: {
      'text-font': ['Open Sans Bold'],
      'text-field': '{point_count}',
      'text-size': 14,
      'text-justify': 'auto',
    },
    paint: {
      'text-color': 'rgba(0,0,0,1)',
    },
  };

  return (
    <Source id={sourceId} type="geojson" data={geoJson}>
      <Layer {...circleStyle} />
      <Layer {...clusterStyle} />
      <Layer {...clusterSymbolStyle} />
    </Source>
  );
}

export default BTRCommunityInfoLayer;
