import styles from '@/components/ResultTable/resultTable.css';
import { Col, Row } from 'antd';
import React from 'react';
import { formatPricePerSqftArce } from './utils/functions';
const TableHeader = ({ selectedRowKeys, medianPerSqft }) => {
  return (
    <>
      <Row
        id="BTRCommunityInfoTableHeader"
        key="table title row lc 1"
        align="bottom"
        justify="space-between"
        wrap={true}
        gutter={[0, 12]}
        style={{ marginBottom: 12 }}
      >
        <Col span={12} key="table title" className={styles.cardTitleH2}>
          <p className="w-60">BFR Community Information</p>
        </Col>
        <Col>Total Selected: {selectedRowKeys.length}</Col>
        {/* {medianPerSqft && (
          <Col>
            Median Rent: {formatPricePerSqftArce(medianPerSqft.minMedian)} - {formatPricePerSqftArce(medianPerSqft.maxMedian)}
          </Col>
        )} */}
      </Row>
    </>
  );
};

export default TableHeader;
