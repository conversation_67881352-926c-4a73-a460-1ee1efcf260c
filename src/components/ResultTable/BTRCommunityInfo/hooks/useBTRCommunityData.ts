import { getBTRCommunityInfo } from '@/services/data';
import { isEmpty } from 'lodash';
import { useEffect, useState } from 'react';

export const useBTRCommunityData = (
  currentPropertyAddress: any,
  currentRadiusMile: number,
  currentStartDate: string,
  currentEndDate: string,
  currentStatus: string,
  dispatch: any,
) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!currentPropertyAddress || !currentRadiusMile) return;

      setLoading(true);
      setError(null);

      try {
        const result = await getBTRCommunityInfo({
          lat: currentPropertyAddress.latitude,
          lng: currentPropertyAddress.longitude,
          distance: currentRadiusMile * 1609.344,
        });

        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            btrCommunityInfoData: result,
            btrCommunityInfoDataForRender: result,
          },
        });
        return;

        // const startDate = new Date(currentStartDate);
        // const endDate = new Date(currentEndDate);
        // startDate.setHours(0, 0, 0, 0);
        // endDate.setHours(23, 59, 59, 999);

        // const dateFiltered = result?.filter((btr: BTRCommunityInfo) => {
        //   const firstSeen = new Date(btr.firstSeen);
        //   return firstSeen >= startDate && firstSeen <= endDate;
        // });

        // dispatch({
        //   type: 'CMA/saveCMAStates',
        //   payload: {
        //     btrData: result,
        //     btrDataForRender: dateFiltered,
        //   },
        // });
      } catch (error) {
        setError(
          error instanceof Error ? error.message : 'Failed to fetch BTR data',
        );
        console.error('Error fetching BTR data:', error);
      } finally {
        setLoading(false);
      }
    };

    if (!isEmpty(currentPropertyAddress)) {
      fetchData();
    }
  }, [
    currentPropertyAddress,
    currentRadiusMile,
    currentStartDate,
    currentEndDate,
    currentStatus,
    dispatch,
  ]);

  return { loading, error };
};
