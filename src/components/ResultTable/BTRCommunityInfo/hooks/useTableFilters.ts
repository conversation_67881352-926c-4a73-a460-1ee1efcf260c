import { useEffect, useState } from 'react';
import { BTRCommunityInfoData } from '../utils/type';
interface FilterOption {
  value: string;
  text: string;
}
export const useTableFilters = (
  btrData: BTRCommunityInfoData[],
  btrDataForRender: BTRCommunityInfoData[],
) => {
  const [sizeOptions, setSizeOptions] = useState<FilterOption[]>([]);
  const [bedOptions, setBedOptions] = useState<FilterOption[]>([]);
  const [bathOptions, setBathOptions] = useState<FilterOption[]>([]);
  const [priceRangeOptions, setPriceRangeOptions] = useState<FilterOption[]>(
    [],
  );

  useEffect(() => {
    if (btrData?.length) {
      // Helper function to create unique numeric options
      const createNumericOptions = (
        extractFn: (item: BTRCommunityInfoData) => number | null,
      ) => {
        const uniqueValues = new Set(btrData.map(extractFn));

        return Array.from(uniqueValues)
          .sort((a, b) => (a ?? 0) - (b ?? 0))
          .map((value) => ({
            value: value?.toString() || '-',
            text: value?.toString() || '-',
          }));
      };

      // Create price range options
      const createPriceRangeOptions = () => {
        const priceRanges = [
          { min: 0, max: 1000 },
          { min: 1001, max: 1500 },
          { min: 1501, max: 2000 },
          { min: 2001, max: 2500 },
          { min: 2501, max: 3000 },
          { min: 3001, max: 4000 },
          { min: 4001, max: Infinity },
        ];

        return priceRanges.map((range) => ({
          value: `${range.min}-${range.max}`,
          text:
            range.max === Infinity
              ? `$${range.min}+`
              : `$${range.min} - $${range.max}`,
        }));
      };

      setBedOptions(createNumericOptions((item) => item.bed));
      setBathOptions(createNumericOptions((item) => item.bath));
      setSizeOptions(createNumericOptions((item) => item.size));
      setPriceRangeOptions(createPriceRangeOptions());
    }
  }, [btrData]);

  return {
    sizeOptions,
    bedOptions,
    bathOptions,
    priceRangeOptions,
  };
};
