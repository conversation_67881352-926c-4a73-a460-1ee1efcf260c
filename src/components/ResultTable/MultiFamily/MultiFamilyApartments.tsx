import styles from '@/components/ResultTable/resultTable.css';
import { getMultiFamilyPropertiesInBuffer } from '@/services/data';
// @ts-ignore
import ExportMultiFamilyCSV from '@/components/ExportToCSV/ExportMultiFamilyCSV';
import ExportPropertyDetailsCSV from '@/components/ExportToCSV/ExportPropertyDetailsCSV';
import { Layer, Source } from '@spatiallaser/map';
import { Button, Card, Col, InputNumber, Row, Space, Table, Tabs } from 'antd';
import { ColumnsType } from 'antd/es/table/interface';
import { PopupOptions, default as mapboxgl } from 'mapbox-gl';
import React from 'react';
import { createPortal } from 'react-dom';
import { BsDot } from 'react-icons/bs';
import { useQuery } from 'react-query';
import { useSelector } from 'umi';
import z from 'zod';
import './styles.css';

const UnitSchema = z.object({
  base_id: z.string(),
  first_seen: z.string().nullable(),
  last_seen: z.string().nullable(),
  unit_name: z.string().nullable(),
  unit_number: z.string().nullable(),
  unit_count: z.number().nullable(),
  rental_key: z.string().nullable(),
  rental_type: z.string().nullable(),
  model_key: z.string().nullable(),
  model_id: z.string().nullable(),
  beds: z.number().nullable(),
  baths: z.number().nullable(),
  unit_rent: z.number().nullable(),
  unit_max_rent: z.number().nullable(),
  unit_deposit: z.number().nullable(),
  sqft: z.number().nullable(),
  max_sqft: z.number().nullable(),
  available_date: z.string().nullable(),
  apartment_id: z.string(),
  apartmentUnitForRent: z.record(z.any()).optional(),
});

const FeaturesSchema = z.object({
  type: z.literal('Feature'),
  geometry: z.object({
    type: z.literal('Point'),
    coordinates: z.array(z.number()).length(2),
  }),
  properties: z.object({
    distance_mi: z.number(),
    base_id: z.string(),
    first_seen: z.string().nullable(),
    last_seen: z.string().nullable(),
    listing_id: z.string().nullable(),
    listing_type_id: z.string().nullable(),
    name: z.string().nullable(),
    phone: z.string().nullable(),
    url: z.string().nullable(),
    city: z.string().nullable(),
    county: z.string().nullable(),
    state: z.string().nullable(),
    country: z.string().nullable(),
    zipcode: z.string().nullable(),
    address: z.string().nullable(),
    listing_dma: z.string().nullable(),
    min_rent: z.number().nullable(),
    max_rent: z.number().nullable(),
    min_bedrooms: z.number().nullable(),
    property_type: z.string(),
    latitude: z.coerce.number().nullable(),
    longitude: z.coerce.number().nullable(),
    company_id: z.string().nullable(),
    unit_availabilities: z.number().nullable(),
    url_external: z.string().nullable(),
    rent_info: z.record(z.any()).nullable(),
    description: z.string().nullable(),
    year_built: z.number().nullable(),
    total_property_units: z.number().nullable(),
    property_unit_type: z.string().nullable(),
    amenities: z.array(z.string()).nullable(),
    features: z.array(z.string()).nullable(),
    fees_policies: z.record(z.any()).nullable(),
    important_details: z.record(z.any()).nullable(),
    units: z.array(UnitSchema),
    computed: z.any(),
  }),
});

const ResponseSchema = z.object({
  type: z.literal('FeatureCollection'),
  features: z.array(FeaturesSchema),
});

type MultiFamilyUnitType = z.infer<typeof UnitSchema>;
type MultiFamilyFeatureType = z.infer<typeof FeaturesSchema>;

// prettier-ignore
const COLUMNS = [
  { dataIndex: ['properties','distance_mi'], title: 'Distance', width: 80, render: (distance_mi: number) => `${distance_mi?.toFixed(2)} mi`, defaultSortOrder: 'ascend',
    sorter: { compare: (a: MultiFamilyFeatureType, b: MultiFamilyFeatureType) => a.properties.distance_mi - b.properties.distance_mi }
  },
  { dataIndex: ['properties', 'name'], title: 'Name', width: 160,
    render: (value: string, record: MultiFamilyFeatureType) => {
      if (record.properties.url_external) {
        return (<Button type="link" className="h-auto p-0"><a href={record.properties.url_external} target="_blank" rel="noreferrer">{typeof value === 'string' && value.trim().length > 0? value : record.properties.address}</a></Button> );
      }
      return value;
    }
  },
  {
      dataIndex: ['properties', 'address'],
      title: 'Address',
      width: 160, 
  }, 
  {
      title: 'Year Built',
      dataIndex: ['properties', 'year_built'],
      width: 100,
      sorter: {
        compare: (a: MultiFamilyFeatureType, b: MultiFamilyFeatureType) =>
          (a.properties[`year_built`] || 0) - (b.properties[`year_built`] || 0),
      },
      render: (value?: number) => (typeof value === 'number' ? `${value}` : ''),
  },
  {
      title: '# of Units',
      dataIndex: ['properties', 'total_property_units'],
      width: 50,
      sorter: {
        compare: (a: MultiFamilyFeatureType, b: MultiFamilyFeatureType) =>
          (a.properties[`total_property_units`] || 0 ) - (b.properties[`total_property_units`] || 0),
      },
      render: (value?: number) => (typeof value === 'number' ? `${value}` : ''),
  },
  {
      title: 'Unit Type',
      dataIndex: ['properties', 'property_unit_type'],
      width: 65,
      // sorter: {
      //   compare: (a: MultiFamilyFeatureType, b: MultiFamilyFeatureType) =>
      //     (a.properties[`property_unit_type`] || '' ).localeCompare(b.properties[`property_unit_type`] || ''),
      // },
      render: (value?: number) => (typeof value === 'string' ? value : ''),
  },
  {
    title: 'Vacancy(%)',
    dataIndex: ['properties', 'computed', 'vacant_pct'],
    width: 100,
    sorter: {
      compare: (a: MultiFamilyFeatureType, b: MultiFamilyFeatureType) =>
        a.properties.computed[`vacant_pct`] - b.properties.computed[`vacant_pct`],
    },
    render: (value?: number, record?: MultiFamilyFeatureType) => {
      if (record?.properties?.units?.length === 0) return '-'
      return typeof value === 'number' ? `${value}%` : '';
    },
  },
  {
    title: 'Absorption(%)',
    dataIndex: ['properties', 'computed', 'absorption_pct'],
    width: 100,
    sorter: {
      compare: (a: MultiFamilyFeatureType, b: MultiFamilyFeatureType) =>
        a.properties.computed[`absorption_pct`] - b.properties.computed[`absorption_pct`],
    },
    render: (value?: number, record?: MultiFamilyFeatureType) => {
      if (record?.properties?.units?.length === 0) return '-'
      return typeof value === 'number' ? `${value}%` : '';
    },
  }
]

const getUnitColumns = (
  selectedProperty?: MultiFamilyFeatureType['properties'],
  latestLastSeenDate?: string | null,
) => {
  const UNIT_COLUMNS: ColumnsType<MultiFamilyUnitType> = [
    {
      dataIndex: 'last_seen',
      title: 'Status',
      width: 60,
      render: (value) => {
        return value === latestLastSeenDate ? 'Active' : 'Closed';
      },
      filters: [
        { text: 'Active', value: 'Active' },
        { text: 'Closed', value: 'Closed' },
      ],
      filterMode: 'menu',
      filterSearch: true,
      onFilter: (value, record) => {
        return (value === 'Active' &&
          record.last_seen === latestLastSeenDate) ||
          (value === 'Closed' && record.last_seen !== latestLastSeenDate)
          ? true
          : false;
      },
    },
    {
      dataIndex: 'unit_name',
      title: 'Name',
      width: 120,
      filters: [
        ...[...new Set(selectedProperty?.units?.map((u) => u.unit_name))].map(
          (unit_name) => ({
            text: unit_name,
            value: unit_name || '',
          }),
        ),
      ],
      filterSearch: true,
      onFilter: (value, record) =>
        record.unit_name && record.unit_name.startsWith(value as string)
          ? true
          : false,
    },
    {
      dataIndex: 'unit_number',
      title: 'Unit #',
      width: 80,
      sorter: {
        compare: (a: MultiFamilyUnitType, b: MultiFamilyUnitType) =>
          a.unit_number?.localeCompare(b.unit_number || '') || 0,
      },
    },
    {
      dataIndex: 'unit_count',
      title: '# of Units',
      width: 60,
      sorter: {
        compare: (a: MultiFamilyUnitType, b: MultiFamilyUnitType) =>
          (a.unit_count || 0) - (b.unit_count || 0),
      },
    },
    {
      dataIndex: 'beds',
      title: 'Beds',
      width: 50,
      render: (value) => (value === 0 ? 'Studio' : value),
      sorter: {
        compare: (a: MultiFamilyUnitType, b: MultiFamilyUnitType) =>
          (a.beds || 0) - (b.beds || 0),
      },
      filters: selectedProperty?.units
        ? [...new Set(selectedProperty.units.map((u) => u.beds))]
            .filter((beds): beds is number => typeof beds === 'number')
            .sort((a, b) => a - b)
            .map((beds) => ({
              text: beds === 0 ? 'Studio' : `${beds}`,
              value: beds,
            }))
        : [],
      onFilter: (value, record) => record.beds === value,
    },
    {
      dataIndex: 'baths',
      title: 'Baths',
      width: 50,
      sorter: {
        compare: (a: MultiFamilyUnitType, b: MultiFamilyUnitType) =>
          (a.baths || 0) - (b.baths || 0),
      },
      filters: selectedProperty?.units
        ? [...new Set(selectedProperty.units.map((u) => u.baths))]
            .filter((baths): baths is number => typeof baths === 'number')
            .sort((a, b) => a - b)
            .map((baths) => ({
              text: `${baths}`,
              value: baths,
            }))
        : [],
      onFilter: (value, record) => record.baths === value,
    },
    {
      dataIndex: 'sqft',
      title: 'Sqft',
      width: 50,
      sorter: {
        compare: (a: MultiFamilyUnitType, b: MultiFamilyUnitType) =>
          (a.sqft || 0) - (b.sqft || 0),
      },
    },
    {
      dataIndex: 'unit_rent',
      title: 'Rent',
      width: 80,
      sorter: {
        compare: (a: MultiFamilyUnitType, b: MultiFamilyUnitType) =>
          (a.unit_rent || 0) - (b.unit_rent || 0),
      },
      render: (v: number) => (v ? `$${v?.toLocaleString()}` : ''),
    },
  ];
  return UNIT_COLUMNS;
};

export function average(values: number[]): number | null {
  if (values.length === 0) return null;

  const sum = values.reduce((acc, val) => acc + val, 0);
  return sum / values.length;
}

const Popup = (
  props: PopupOptions & {
    longitude: number;
    latitude: number;

    className?: string;
    onOpen?: (e: Event) => void;
    onClose?: (e: Event) => void;
    children?: React.ReactNode;
  },
) => {
  const { map } = useSelector((state: any) => state.CMA);

  const container = React.useMemo(() => {
    return document.createElement('div');
  }, []);

  React.useEffect(() => {
    if (!props.className) return;
    container.className = props.className;
  }, [container, props.className]);

  const popup = React.useMemo(() => {
    const options = { ...props };
    const pp = new mapboxgl.Popup(options);
    pp.setLngLat([props.longitude, props.latitude]);
    pp.once('open', (e: any) => {
      props.onOpen?.(e);
    });
    return pp;
  }, [props.longitude, props.latitude]);

  React.useEffect(() => {
    if (!map) return;

    const onClose = (e: any) => {
      props.onClose?.(e);
    };
    popup.on('close', onClose);
    popup.setDOMContent(container).addTo(map);

    return () => {
      popup.off('close', onClose);
      if (popup.isOpen()) {
        popup.remove();
      }
    };
  }, [map, popup]);

  return createPortal(props.children, container);
};

const currentYear = new Date().getFullYear();

export const MultiFamilyApartments = () => {
  const {
    map,
    currentRadiusMile,
    eventCoordinates: [longitude, latitude],
    cmaTabKey,
    currentPropertyAddress,
  } = useSelector((state: any) => state.CMA);

  const [clickedRowKey, setClickedRowKey] = React.useState<React.Key>();
  const [selectedRowKeys, setSelectedRowKeys] = React.useState<React.Key[]>([]);
  const [container, setContainer] = React.useState<Element | null>(null);
  const [viewType, setViewType] = React.useState('table');

  const [sorter, setSorter] = React.useState({ column: COLUMNS[0] });
  const tblRef: Parameters<typeof Table>[0]['ref'] = React.useRef(null);

  const [propertyUnitsTableFilter, setPropertyUnitsTableFilter] =
    React.useState<Record<string, any>>({});

  const [yearBuiltFilter, setYearBuiltFilter] = React.useState<{
    min: number | null;
    max: number | null;
  }>({ min: null, max: null });

  const [mapClickedFeature, setMapClickedFeature] = React.useState<
    | MultiFamilyFeatureType
    | { properties: MultiFamilyFeatureType['properties'] }
  >();

  const { data, isFetching } = useQuery(
    `MultiFamilyApartments-${JSON.stringify({
      currentRadiusMile,
      longitude,
      latitude,
    })}`,
    async () => {
      try {
        const data = await getMultiFamilyPropertiesInBuffer({
          longitude,
          latitude,
          distance: currentRadiusMile * 1609.34,
        });

        const parsed = ResponseSchema.safeParse(data);
        if (!parsed.success) {
          console.log('Parsing Error: ', parsed.error);
          throw new Error(JSON.stringify(parsed.error));
        }
        return parsed.data;
      } catch (e) {
        console.error(e);
      }
    },
    {
      keepPreviousData: false,
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      enabled: longitude && latitude && currentRadiusMile ? true : false,
    },
  );

  React.useEffect(() => {
    setContainer(document.getElementById('multi-family-layers-container'));

    return () => {
      setContainer(null);
    };
  }, [cmaTabKey]);

  React.useEffect(() => {
    if (!map) return;

    const onClick = (e: any) => {
      if (e.features.length > 0) {
        setMapClickedFeature(e.features[0]);
      }
    };

    map.on('click', ['multifamily-apartments-geojson-layer'], onClick);
    return () => {
      map.off('click', ['multifamily-apartments-geojson-layer'], onClick);
    };
  }, [map]);

  const roomKeys = React.useMemo(() => {
    let rooms = [] as number[];
    if (!data) return [];

    for (const f of data.features) {
      for (const unit of f.properties.units) {
        if (typeof unit.beds === 'number') {
          rooms.push(unit.beds);
        }
      }
    }

    rooms = [...new Set(rooms)].sort((a, b) => a - b);

    return rooms;
  }, [data]);

  React.useEffect(() => {
    if (viewType === 'table' && selectedRowKeys?.length > 0) {
      const sortedData = sorter?.column
        ? data?.features?.sort(
            (a, b) =>
              (sorter.column.sorter && sorter.column.sorter.compare(a, b)) || 0,
          )
        : data?.features;
      const idx =
        sortedData?.findIndex((i) =>
          selectedRowKeys.includes(i.properties.base_id),
        ) || -1;
      if (idx !== -1) {
        tblRef.current?.scrollTo({ index: idx });
      }
    }
  }, [viewType, data, selectedRowKeys, sorter]);

  const selectedProperty = React.useMemo(() => {
    if (!clickedRowKey) return;
    return data?.features.find((f) => clickedRowKey === f.properties.base_id)
      ?.properties;
  }, [data, clickedRowKey]);

  const latestLastSeenDate = React.useMemo(() => {
    if (!selectedProperty || selectedProperty?.units.length == 0) return;
    const sorted = selectedProperty?.units.sort((a, b) =>
      a.last_seen && b.last_seen
        ? new Date(a.last_seen).getTime() - new Date(b.last_seen).getTime()
        : 0,
    );

    return sorted[sorted.length - 1].last_seen;
  }, [selectedProperty?.units]);

  const selectedPropertyUnitMix = React.useMemo(() => {
    const d = data?.features.find(
      (d) => d.properties.base_id === selectedProperty?.base_id,
    );

    let units = d?.properties?.units || [];

    const allUnitCount = units.reduce(
      (acc, unit) => acc + (unit.unit_count || 0),
      0,
    );

    if (
      Object.entries(propertyUnitsTableFilter).some(
        ([key, value]) => value !== null && value !== undefined,
      )
    ) {
      units = units.filter((unit) => {
        return Object.entries(propertyUnitsTableFilter).every(
          ([filterKey, filterValue]) => {
            if (filterValue === null || filterValue === undefined) return true;

            if (filterKey === 'last_seen') {
              const unitStatus =
                unit.last_seen === latestLastSeenDate ? 'Active' : 'Closed';
              return filterValue.includes(unitStatus);
            }

            return filterValue.includes(
              unit[filterKey as keyof typeof unit] as string,
            );
          },
        );
      });
    }

    const groupByBeds = units.reduce((acc, unit) => {
      const key = `${unit.beds}`;
      if (!acc[key]) acc[key] = [];
      acc[key].push(unit);
      return acc;
    }, {} as Record<string, MultiFamilyUnitType[]>);

    let computed = {} as Record<string, number | null>;
    const roomKeys = Object.keys(groupByBeds);

    for (const key of roomKeys) {
      const units = groupByBeds[key];
      const unitCount = units.length;
      const avgRent = average(units.map((u) => u.unit_rent || 0)) || null;
      const avgPSF =
        average(
          units.map((u) => (u.unit_rent && u.sqft ? u.unit_rent / u.sqft : 0)),
        ) || null;
      const unitPct = Math.round((unitCount / (allUnitCount || 0)) * 100);
      const estimatedTotalUnitCount =
        (selectedProperty?.total_property_units || 0) * (unitPct / 100);

      computed = {
        ...computed,
        [`unit_count_${key}bd`]: estimatedTotalUnitCount
          ? Math.round(estimatedTotalUnitCount)
          : null,
        [`avg_rent_${key}bd`]: avgRent ? Math.round(avgRent) : null,
        [`avg_psf_${key}bd`]: avgPSF ? Math.round(avgPSF * 100) / 100 : null,
        [`unit_pct_${key}bd`]: unitPct,
      };
    }

    if (d) {
      const res = [];
      for (const key of roomKeys.map(Number)) {
        if (
          computed?.[`unit_count_${key}bd`] ||
          computed?.[`avg_rent_${key}bd`]
        ) {
          res.push({
            base_id: d?.properties?.base_id,
            name: d?.properties?.name,
            unit_type: key === 0 ? 'Studio' : `${key}BD`,
            avg_rent: computed?.[`avg_rent_${key}bd`],
            avg_psf: computed?.[`avg_psf_${key}bd`],
            unit_count: computed?.[`unit_count_${key}bd`],
            unit_pct: computed?.[`unit_pct_${key}bd`],
          });
        }
      }

      return res;
    }
  }, [
    selectedProperty,
    data?.features,
    // roomKeys,
    propertyUnitsTableFilter,
    latestLastSeenDate,
  ]);

  React.useEffect(() => {
    if (!selectedProperty) return;
    setTimeout(() => {
      document
        .getElementById('mls-selected-property-details')
        ?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  }, [selectedProperty]);

  const tableValues = React.useMemo(() => {
    if (!roomKeys || !data) return { columns: COLUMNS };
    const columns = ([...COLUMNS] as any).map((c: any) => {
      if (c.title === 'Name') {
        return {
          ...c,
          filterSearch: true,
          filters: data.features.map((f) => ({
            text: f.properties.name,
            value: f.properties.name,
          })),
          onFilter: (value: string, record: MultiFamilyFeatureType) =>
            record?.properties?.name?.indexOf(value as string) === 0,
        };
      } else if (c.title === 'Address') {
        return {
          ...c,
          filterSearch: true,
          filters: data.features.map((f) => ({
            text: f.properties.address,
            value: f.properties.address,
          })),
          onFilter: (value: string, record: MultiFamilyFeatureType) =>
            record?.properties?.address?.indexOf(value as string) === 0,
        };
      } else if (c.title === 'Unit Type') {
        return {
          ...c,
          filterSearch: true,
          filters: [
            ...new Set(
              data.features.map((f) => f.properties.property_unit_type),
            ),
          ].map((unitType) => ({
            text: unitType,
            value: unitType,
          })),
          onFilter: (value: string, record: MultiFamilyFeatureType) =>
            record?.properties?.property_unit_type?.indexOf(value as string) ===
            0,
        };
      } else if (c.title === 'Year Built') {
        return {
          ...c,
          filterSearch: true,
          filters: [
            ...new Set(data.features.map((f) => f.properties.year_built)),
          ]
            .sort((a, b) => b - a)
            .map((year) => ({
              text: year,
              value: year,
            })),
          onFilter: (value: string, record: MultiFamilyFeatureType) =>
            record?.properties?.year_built
              ?.toString()
              .indexOf(value as string) === 0,
        };
      }

      return c;
    });

    const keys = roomKeys.sort((a, b) => {
      if (a === 0 && b !== 0) return 1; // Studio last
      if (b === 0 && a !== 0) return -1;
      return a - b;
    });

    for (let key of keys) {
      columns.push({
        title: key === 0 ? 'Studio' : `${key}BD`,
        children: [
          {
            title: 'Count',
            dataIndex: [`properties`, `computed`, `unit_count_${key}bd`],
            width: 60,
            sorter: {
              compare: (a: MultiFamilyFeatureType, b: MultiFamilyFeatureType) =>
                (a.properties?.computed?.[`unit_count_${key}bd`] || 0) -
                (b.properties?.computed?.[`unit_count_${key}bd`] || 0),
            },
            render: (value?: string) =>
              value ? value.toLocaleString() : value,
          },
          {
            title: 'Rent',
            dataIndex: [`properties`, `computed`, `avg_rent_${key}bd`],
            width: 80,
            sorter: {
              compare: (a: MultiFamilyFeatureType, b: MultiFamilyFeatureType) =>
                (a.properties?.computed?.[`avg_rent_${key}bd`] || 0) -
                (b.properties?.computed?.[`avg_rent_${key}bd`] || 0),
            },
            render: (value?: string) =>
              value ? `$${value.toLocaleString()}` : value,
          },
        ],
      });
    }

    return { columns };
  }, [roomKeys, data?.features]);

  // Filter data based on year built range
  const filteredData = React.useMemo(() => {
    if (!data?.features) return null;

    let filtered = data.features;

    // Apply year built filter
    if (yearBuiltFilter.min !== null || yearBuiltFilter.max !== null) {
      filtered = filtered.filter((feature) => {
        const yearBuilt = feature.properties.year_built;
        if (!yearBuilt) return false;

        if (yearBuiltFilter.min !== null && yearBuiltFilter.max !== null) {
          return (
            yearBuilt >= yearBuiltFilter.min && yearBuilt <= yearBuiltFilter.max
          );
        } else if (yearBuiltFilter.min !== null) {
          return yearBuilt >= yearBuiltFilter.min;
        } else if (yearBuiltFilter.max !== null) {
          return yearBuilt <= yearBuiltFilter.max;
        }
        return true;
      });
    }

    return { ...data, features: filtered };
  }, [data, yearBuiltFilter]);

  React.useEffect(() => {
    const features = filteredData?.features || data?.features || [];
    setSelectedRowKeys(features.map((d) => d.properties.base_id));
  }, [filteredData, data?.features]);

  const [avgVacancy, avgAbsorption, selectedUnitWeightedAverage] =
    React.useMemo(() => {
      if (!roomKeys || !data) return [undefined, undefined];

      const selected = data.features.filter((f) =>
        selectedRowKeys.includes(f.properties.base_id),
      );

      let vacancyWeightedSum = 0;
      let vacancyTotalUnits = 0;
      let absorptionWeightedSum = 0;
      let absorptionTotalUnits = 0;
      if (selected) {
        for (let row of selected) {
          if (typeof row.properties?.computed?.vacant_pct === 'number') {
            vacancyWeightedSum +=
              row.properties?.computed?.vacant_pct *
              (row.properties?.[`total_property_units`] || 0);
            vacancyTotalUnits += row.properties?.[`total_property_units`] || 0;
          }
          if (
            typeof row.properties?.computed?.[`absorption_pct`] === 'number'
          ) {
            absorptionWeightedSum +=
              row.properties?.computed?.[`absorption_pct`] *
              (row.properties?.[`total_property_units`] || 0);
            absorptionTotalUnits +=
              row.properties?.[`total_property_units`] || 0;
          }
        }
      }

      const avgVacancy =
        vacancyTotalUnits > 0
          ? Math.round((vacancyWeightedSum / vacancyTotalUnits) * 100) / 100
          : 0;
      const avgAbsorption =
        absorptionTotalUnits > 0
          ? Math.round((absorptionWeightedSum / absorptionTotalUnits) * 100) /
            100
          : 0;

      const unitData = [];
      for (let key of roomKeys) {
        const d = {
          type: key === 0 ? 'Studio' : `${key}BD`,
          units: [] as any[],
        };
        for (let row of selected) {
          if (
            !row.properties?.computed?.[`unit_count_${key}bd`] ||
            !row.properties?.computed?.[`avg_rent_${key}bd`]
          )
            continue;
          d.units.push({
            count: row.properties?.computed?.[`unit_count_${key}bd`],
            rent: row.properties?.computed?.[`avg_rent_${key}bd`],
          });
        }
        unitData.push(d);
      }

      const unitStats = [
        { type: 'Rent', total: 0 },
        { type: 'Units', total: 0 },
      ];
      let totalUnits = 0;

      for (const d of unitData) {
        const units = d.units;

        const weightedAvg =
          Math.round(
            units.reduce((acc, d) => {
              acc += d.rent * d.count;
              return acc;
            }, 0) / units.reduce((acc, d) => acc + d.count, 0),
          ) || null;
        const totalCount = units.reduce((acc, d) => acc + d.count, 0);

        unitStats[0] = { ...unitStats[0], [d.type]: weightedAvg };
        unitStats[1] = { ...unitStats[1], [d.type]: totalCount };
        totalUnits += totalCount || 0;
      }

      const weightedRent =
        totalUnits > 0
          ? Math.round(
              roomKeys.reduce((acc, curr) => {
                const key = curr === 0 ? 'Studio' : `${curr}BD`;
                const rent = unitStats[0][key as keyof (typeof unitStats)[0]];
                const count =
                  unitStats[1][key as keyof (typeof unitStats)[1]] || 0;
                if (typeof rent === 'number' && typeof count === 'number') {
                  acc += rent * count;
                }
                return acc;
              }, 0) / totalUnits,
            )
          : 0;

      unitStats[0] = { ...unitStats[0], total: weightedRent };
      unitStats[1] = { ...unitStats[1], total: totalUnits };

      return [avgVacancy, avgAbsorption, unitStats];
    }, [data?.features, selectedRowKeys, roomKeys]);

  const groupByStatus = React.useMemo(() => {
    const years = {
      // [`${currentYear + 1}`]: 0,
      [`future`]: 0,
      [`${currentYear}`]: 0,
      [`${currentYear - 1}`]: 0,
      [`rest`]: 0,
    };
    for (const feat of data?.features || []) {
      if (
        typeof feat.properties.year_built === 'number' &&
        typeof feat.properties.year_built !== null &&
        feat.properties.year_built >= currentYear - 1
      ) {
        if (feat.properties.year_built > currentYear) {
          years[`future`] += 1;
        } else {
          years[`${feat.properties.year_built}`] += 1;
        }
      } else {
        years[`rest`] += 1;
      }
    }

    // prettier-ignore
    return [
      { name: `>= ${currentYear + 1}`, color: '#e7298a', count: years[`future`] },
      { name: currentYear, color: '#7570b3', count: years[`${currentYear}`] },
      { name: currentYear - 1, color: '#d95f02', count: years[`${currentYear - 1}`] },
      { name: `<= ${currentYear - 2}`, color: '#1b9e77', count: years[`rest`] },
    ];
  }, [data?.features]);

  if (!container) return null;
  return (
    <React.Fragment>
      {container &&
        data &&
        createPortal(
          <React.Fragment>
            <Source
              id="multifamily-apartments-geojson"
              type="geojson"
              data={{
                type: 'FeatureCollection',
                features:
                  filteredData?.features.filter((f) =>
                    selectedRowKeys?.includes(f.properties.base_id),
                  ) || [],
              }}
            >
              <Layer
                id="multifamily-apartments-geojson-layer"
                type="circle"
                source="multifamily-apartments-geojson"
                paint={{
                  'circle-stroke-width': 2,
                  'circle-stroke-color': 'white',
                  // 'circle-color': '#4c7a2e',
                  'circle-color': [
                    'case',
                    ['>', ['get', 'year_built'], currentYear],
                    '#e7298a',
                    groupByStatus
                      ? [
                          'match',
                          ['get', 'year_built'],
                          ...groupByStatus
                            .filter(
                              (item) =>
                                item.name !== `<= ${currentYear - 2}` &&
                                item.name !== `>= ${currentYear + 1}`,
                            )
                            .flatMap((item) => [item.name, item.color]),
                          '#1b9e77', // default if no match
                        ]
                      : '#1b9e77',
                  ],
                  'circle-radius': 10,
                }}
              />
            </Source>
            {mapClickedFeature && (
              <Popup
                latitude={mapClickedFeature.properties.latitude as number}
                longitude={mapClickedFeature.properties.longitude as number}
                className="px-2 py-1"
                onClose={() => {
                  setMapClickedFeature(undefined);
                }}
              >
                <h3 className="text-base font-medium">
                  {mapClickedFeature.properties.name}
                </h3>
                <div>
                  <p>
                    {mapClickedFeature?.properties?.address},{' '}
                    {mapClickedFeature?.properties?.city},{' '}
                    {mapClickedFeature?.properties?.state},{' '}
                    {mapClickedFeature?.properties?.zipcode}
                  </p>
                </div>
                {(!selectedProperty ||
                  (selectedProperty &&
                    selectedProperty.base_id !=
                      mapClickedFeature?.properties?.base_id)) && (
                  <div className="flex flex-row justify-center">
                    <Button
                      type="link"
                      onClick={() => {
                        const base_id = mapClickedFeature?.properties?.base_id;
                        setClickedRowKey(base_id);
                        setSelectedRowKeys((prev) =>
                          !prev.includes(base_id) ? [base_id] : prev,
                        );
                      }}
                    >
                      View
                    </Button>
                  </div>
                )}
              </Popup>
            )}
            {groupByStatus && (
              <div className="absolute p-2 bottom-20 right-20 bg-background shadow-md">
                <div className="mb-1">
                  <span className="font-semibold text-base">
                    Multi-Family Community
                  </span>
                </div>
                <div className="flex flex-col gap-1">
                  {groupByStatus
                    .filter((item) => item.count !== 0)
                    .map((item) => (
                      <div className="flex flex-row gap-2 items-center">
                        <div
                          className="w-6 h-6"
                          style={{ background: item.color }}
                        />
                        <div>
                          {item.name} {`(${item.count})`}
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            )}
          </React.Fragment>,
          container as Element,
        )}

      {selectedUnitWeightedAverage && (
        <React.Fragment>
          <div className="flex flex-row justify-around items-center py-2">
            <div className="flex flex-col items-center">
              <span className="text-lg">Last Month</span>
              <div className="flex flex-row gap-4 items-center py-2">
                <div className="flex flex-col items-center">
                  <span className="text-sm">Vacancy</span>
                  <div className="flex flex-row gap-1 text-lg font-medium">
                    <span>
                      {typeof avgVacancy === 'number' ? `${avgVacancy}%` : '-'}
                    </span>
                  </div>
                </div>
                <div className="flex flex-col items-center">
                  <span className="text-sm">Absorption</span>
                  <div className="flex flex-row gap-1 text-lg font-medium">
                    <span>
                      {typeof avgAbsorption === 'number'
                        ? `${avgAbsorption}%`
                        : '-'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <Table
              dataSource={selectedUnitWeightedAverage.sort((a, b) =>
                a.type.localeCompare(b.type),
              )}
              columns={[
                {
                  dataIndex: 'type',
                  title: '',
                  width: 50,
                },
                ...roomKeys.map((key) => ({
                  dataIndex: key === 0 ? 'Studio' : `${key}BD`,
                  title: key === 0 ? 'Studio' : `${key}BD`,
                  width: 90,
                  render: (value: string, row: any) =>
                    row.type === 'Rent' && value
                      ? `$${value?.toLocaleString()}`
                      : row.type === 'Units' && value
                      ? `${value?.toLocaleString()}`
                      : '-',
                })),
                {
                  dataIndex: 'total',
                  title: 'Avg/Total',
                  width: 90,
                  render: (value, row) =>
                    value
                      ? `${
                          row.type === 'Rent' ? '$' : ''
                        }${value?.toLocaleString()}`
                      : '-',
                },
              ]}
              rowKey={(record) => `${record.type}`}
              size="small"
              pagination={false}
            />
          </div>
          <div className="w-full h-[1px] bg-[rgba(0,0,0,0.1)] my-3" />
        </React.Fragment>
      )}
      <div className="mb-0">
        <Row
          align="bottom"
          justify="space-between"
          wrap={true}
          gutter={[0, 16]}
          className="mb-4"
        >
          <Col>
            <p className={`w-48 ${styles.cardTitleH2} text-nowrap`}>
              Multi-Family Community
            </p>
            <span className="text-sm">
              Select a record from the table or chart to view more details such
              as amenities and units.
            </span>
          </Col>
          <Col>
            <ExportMultiFamilyCSV
              selectedRowKeys={selectedRowKeys}
              data={filteredData?.features || []}
              currentPropertyAddress={currentPropertyAddress}
              currentRadiusMile={currentRadiusMile}
            />
          </Col>
        </Row>
        {viewType === 'table' && (
          <Table
            id="multi-family-data-table"
            tableLayout="auto"
            ref={tblRef}
            rowSelection={{
              type: 'checkbox',
              columnWidth: 40,
              selectedRowKeys: selectedRowKeys,
              onChange: (keys) => setSelectedRowKeys(keys),
            }}
            dataSource={filteredData?.features || []}
            columns={tableValues?.columns || COLUMNS}
            rowKey={(record) => record.properties.base_id}
            loading={isFetching}
            size="small"
            virtual
            pagination={false}
            onChange={(pagination, filters, sorter) => {
              setSorter(sorter as any);
            }}
            rowClassName={(record) =>
              clickedRowKey === record.properties.base_id
                ? 'selectedRowHighlight'
                : ``
            }
            onRow={(record) => ({
              onMouseOver: () => {
                setMapClickedFeature({ properties: record.properties });
              },
              onClick: () => {
                setClickedRowKey(record.properties.base_id);
                map.flyTo({
                  center: record.geometry.coordinates,
                  zoom: 16,
                  speed: 2,
                  curve: 1,
                  easing: (t: any) => t,
                });
                setMapClickedFeature({ properties: record.properties });
                setSelectedRowKeys((prev) =>
                  !prev.includes(record.properties.base_id)
                    ? [record.properties.base_id]
                    : prev,
                );
              },
            })}
            scroll={{
              x: 775, //735,
              y: 440,
            }}
            sticky={true}
          />
        )}
      </div>

      {selectedProperty && (
        <React.Fragment>
          <div className="w-full h-[1px] bg-[rgba(0,0,0,0.1)] my-3" />
          <div id="mls-selected-property-details" className="my-4">
            <div className="mb-4">
              <div className="flex flex-row items-center justify-between pb-4">
                <div>
                  <div className="flex flex-row gap-2 items-center">
                    <h3 className="text-xl font-medium">
                      {selectedProperty?.name || selectedProperty?.address}
                    </h3>
                    {selectedProperty.url_external && (
                      <Button variant="link">
                        <a
                          href={selectedProperty.url_external}
                          rel="noreferrer"
                          target="_blank"
                        >
                          Link
                        </a>
                      </Button>
                    )}
                  </div>
                  {selectedProperty && (
                    <div>
                      <p>
                        {selectedProperty?.address}, {selectedProperty?.city},{' '}
                        {selectedProperty?.state}, {selectedProperty?.zipcode}
                      </p>
                      {selectedProperty?.phone && (
                        <div className="flex gap-1">
                          {selectedProperty?.phone}
                        </div>
                      )}
                    </div>
                  )}
                </div>
                <div>
                  <ExportPropertyDetailsCSV
                    selectedProperty={selectedProperty}
                    selectedPropertyUnitMix={selectedPropertyUnitMix}
                    currentPropertyAddress={currentPropertyAddress}
                    currentRadiusMile={currentRadiusMile}
                    unitsTableFilters={propertyUnitsTableFilter}
                  />
                </div>
              </div>
              <div className="flex flex-col gap-4">
                <PriceRanges selectedProperty={selectedProperty} />
                {selectedPropertyUnitMix && (
                  <div className="px-12">
                    <Table
                      dataSource={selectedPropertyUnitMix}
                      columns={[
                        {
                          dataIndex: 'unit_type',
                          title: 'Unit Type',
                          width: 90,
                          render: (value) =>
                            value ? value.toLocaleString() : '-',
                        },
                        {
                          dataIndex: 'unit_count',
                          title: 'Unit Count',
                          width: 90,
                          render: (value, row) =>
                            value
                              ? `${value?.toLocaleString()} (${row.unit_pct}%)`
                              : '-',
                        },
                        {
                          dataIndex: 'avg_rent',
                          title: 'Avg. Rent',
                          width: 90,
                          render: (value) =>
                            value ? `$${value.toLocaleString()}` : '-',
                        },
                        {
                          dataIndex: 'avg_psf',
                          title: 'Avg. PSF',
                          width: 90,
                          render: (value) =>
                            value ? `$${value.toLocaleString()}` : '-',
                        },
                      ]}
                      rowKey={(record) =>
                        `${record.base_id}-${record.unit_type}`
                      }
                      size="small"
                      pagination={false}
                    />
                  </div>
                )}
              </div>
            </div>
            <Tabs
              defaultActiveKey="units"
              items={[
                {
                  key: 'units',
                  label: 'Units',
                  children: (
                    <UnitsTab
                      selectedProperty={selectedProperty}
                      isFetching={isFetching}
                      onFilterChange={(filters) => {
                        setPropertyUnitsTableFilter(filters);
                      }}
                    />
                  ),
                },
                {
                  key: 'about',
                  label: 'About',
                  children: <AboutTab selectedProperty={selectedProperty} />,
                },
                {
                  key: 'amenities',
                  label: 'Amenities',
                  children: (
                    <AmenitiesTab selectedProperty={selectedProperty} />
                  ),
                },
              ]}
            />
          </div>
        </React.Fragment>
      )}
    </React.Fragment>
  );
};

function PriceRanges(props: {
  selectedProperty: MultiFamilyFeatureType['properties'];
}) {
  const { selectedProperty } = props;

  const unitRanges = React.useMemo(() => {
    const count = selectedProperty?.units.length || 0;
    if (count === 0) return;
    const rentData = [];
    const bedsData = [];
    const bathsData = [];
    const sqftData = [];
    for (let i = 0; i < count; i++) {
      const unit = selectedProperty?.units[i];
      typeof unit?.unit_rent === 'number' && rentData.push(unit.unit_rent);
      typeof unit?.beds === 'number' && bedsData.push(unit.beds);
      typeof unit?.baths === 'number' && bathsData.push(unit.baths);
      typeof unit?.sqft === 'number' && sqftData.push(unit.sqft);
    }
    const rent = [...new Set(rentData)].sort((a, b) => a - b);
    const beds = [...new Set(bedsData)].sort((a, b) => a - b);
    const baths = [...new Set(bathsData)].sort((a, b) => a - b);
    const sqft = [...new Set(sqftData)].sort((a, b) => a - b);

    return { rent, beds, baths, sqft };
  }, [selectedProperty]);

  return (
    <div className="px-12">
      {unitRanges && (
        <div className="flex flex-row border p-3 min-h-fit">
          <div className="flex w-full flex-col items-center justify-center gap-1 border-r px-4 py-2">
            <span>Monthly Rent</span>
            {unitRanges.rent.length > 0 ? (
              <span className="text-lg font-medium">
                ${unitRanges.rent[0]?.toLocaleString()}
                {unitRanges.rent.length > 1
                  ? ` - $ ${unitRanges.rent[
                      unitRanges.rent.length - 1
                    ]?.toLocaleString()}`
                  : ``}
              </span>
            ) : (
              <span className="text-lg font-medium">-</span>
            )}
          </div>
          <div className="flex w-full flex-col items-center justify-center gap-1 border-r px-4 py-2">
            <span>Bedrooms</span>
            {unitRanges.beds.length > 0 ? (
              <span className="text-lg font-medium">
                {unitRanges.beds[0] === 0
                  ? 'Studio'
                  : unitRanges.beds[0]?.toLocaleString()}
                {unitRanges.beds.length > 1
                  ? ` - ${unitRanges.beds[
                      unitRanges.beds.length - 1
                    ]?.toLocaleString()} bd`
                  : ` bd`}
              </span>
            ) : (
              <span className="text-lg font-medium">-</span>
            )}
          </div>
          <div className="flex w-full flex-col items-center justify-center gap-1 border-r px-4 py-2">
            <span>Bathrooms</span>
            {unitRanges.baths.length > 0 ? (
              <span className="text-lg font-medium">
                {unitRanges.baths[0]?.toLocaleString()}
                {unitRanges.baths.length > 1
                  ? ` - ${unitRanges.baths[
                      unitRanges.baths.length - 1
                    ]?.toLocaleString()} ba`
                  : ` ba`}
              </span>
            ) : (
              <span className="text-lg font-medium">-</span>
            )}
          </div>
          <div className="flex w-full flex-col items-center justify-center gap-1 px-4 py-2">
            <span>Square Feet</span>
            {unitRanges.sqft.length > 0 ? (
              <span className="text-lg font-medium">
                {unitRanges.sqft[0]?.toLocaleString()}
                {unitRanges.sqft.length > 1
                  ? ` - ${unitRanges.sqft[
                      unitRanges.sqft.length - 1
                    ]?.toLocaleString()}`
                  : ``}
              </span>
            ) : (
              <span className="text-lg font-medium">-</span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

function UnitsTab(props: {
  selectedProperty: MultiFamilyFeatureType['properties'];
  isFetching: boolean;
  onFilterChange?: (filters: Record<string, any>) => void;
}) {
  const { selectedProperty } = props;

  const latestLastSeenDate = React.useMemo(() => {
    if (!selectedProperty || selectedProperty?.units.length == 0) return;
    const sorted = selectedProperty?.units.sort((a, b) =>
      a.last_seen && b.last_seen
        ? new Date(a.last_seen).getTime() - new Date(b.last_seen).getTime()
        : 0,
    );

    return sorted[sorted.length - 1].last_seen;
  }, [selectedProperty?.units]);

  return (
    <Table
      dataSource={selectedProperty?.units || []}
      columns={getUnitColumns(selectedProperty, latestLastSeenDate)}
      rowKey={(record) => record.base_id}
      loading={props.isFetching}
      size="small"
      virtual
      onChange={(pagination, filters, sorter) => {
        if (props.onFilterChange) {
          props.onFilterChange(filters);
        }
      }}
      pagination={false}
      scroll={{
        x: 1000,
        y: 400,
      }}
      sticky={true}
    />
  );
}

function AboutTab(props: {
  selectedProperty: MultiFamilyFeatureType['properties'];
}) {
  const { selectedProperty } = props;
  return (
    <React.Fragment>
      <div className="flex flex-col gap-4">
        <Card title="Description">
          <p>{selectedProperty.description}</p>
        </Card>

        <div className="flex flex-row flex-wrap gap-4">
          <Card title="Important Details" className="w-fit">
            <div className="flex flex-col">
              <div className="flex flex-col gap-1">
                <span>Lease Options</span>
                {`${selectedProperty?.important_details?.lease_options?.join(
                  ', ',
                )}`}
              </div>
              <div className="flex flex-col gap-1">
                <span>Property Info</span>
                {`${selectedProperty?.important_details?.property_information?.join(
                  ', ',
                )}`}
              </div>
            </div>
          </Card>
          {selectedProperty?.fees_policies && (
            <Card title="Fee Policies" className="w-fit">
              <div className="flex flex-row items-start gap-4">
                <div className="flex flex-col gap-1">
                  <span className="font-bold">Parking Info</span>
                  <div className="flex flex-col">
                    {selectedProperty?.fees_policies?.parking_info
                      ?.filter((i: string[]) => i.length > 2)
                      .map((i: string) => (
                        <span key={i}>{i}</span>
                      ))}
                  </div>
                </div>
                {selectedProperty?.fees_policies?.pets_info && (
                  <div className="flex flex-col gap-1">
                    <span className="font-bold">Pets Info</span>
                    <div className="flex flex-row gap-4">
                      {Object.entries(
                        selectedProperty?.fees_policies?.pets_info || {},
                      ).map(([key, value]) => (
                        <div key={key}>
                          <span className="font-bold">{key}</span>
                          <div>
                            {Object.entries(value || {}).map(([k, v]) => (
                              <div
                                key={k}
                                className="flex flex-row items-center gap-2"
                              >
                                <span>{k}</span>
                                <span>{v}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </Card>
          )}
          {(selectedProperty?.features?.length || 0) > 0 && (
            <Card
              title={`Features ${`(${selectedProperty?.features?.length})`}`}
              className="w-fit"
            >
              <div className="grid grid-cols-2">
                {selectedProperty?.features?.map((f, idx) => (
                  <div
                    key={idx}
                    className="flex flex-row items-center gap-2 whitespace-nowrap"
                  >
                    <BsDot />
                    <span>{f}</span>
                  </div>
                ))}
              </div>
            </Card>
          )}
        </div>
      </div>
    </React.Fragment>
  );
}

function AmenitiesTab(props: {
  selectedProperty: MultiFamilyFeatureType['properties'];
}) {
  const { selectedProperty } = props;
  return (
    <React.Fragment>
      <div className="flex flex-col gap-4">
        <Card title={`Amenities ${`(${selectedProperty?.amenities?.length})`}`}>
          <div className="grid grid-cols-2">
            {selectedProperty?.amenities?.map((f, idx) => (
              <div
                key={idx}
                className="flex flex-row items-center gap-2 whitespace-nowrap"
              >
                <BsDot />
                <span>{f}</span>
              </div>
            ))}
          </div>
        </Card>
      </div>
    </React.Fragment>
  );
}
