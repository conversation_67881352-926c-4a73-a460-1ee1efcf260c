import styles from '@/components/ResultTable/resultTable.css';
import { formatter } from '@/utils/money';
import { sortString } from '@/utils/strings';
import { Col, Row, Switch, Table } from 'antd';
import isEmpty from 'lodash.isempty';
import isEqual from 'lodash.isequal';
import { useEffect, useRef, useState } from 'react';
import { ImMap } from 'react-icons/im';
import { connect } from 'umi';
import EmptyState from '../../EmptyState';
import ExportToCSV from '../../ExportToCSV/ExportToCSV';
import { columnWidthSmaller } from '../ResultTable';

const csvColumnDataIndex = [
  {
    columnName: 'Property Name',
    dataIndex: 'property_name',
  },
  { columnName: '2BR: Units', dataIndex: 'two_br_units' },
  {
    columnName: '2BR: Avail. Units',
    dataIndex: 'two_br_avail',
  },
  {
    columnName: '2BR: Rent',
    dataIndex: 'two_br_rent',
  },
  {
    columnName: '2BR: Per Sqft',
    dataIndex: 'two_br_rent_sf',
  },
  { columnName: '3BR: Units', dataIndex: 'three_br_units' },
  {
    columnName: '3BR: Avail. Units',
    dataIndex: 'three_br_avail',
  },
  {
    columnName: '3BR: Rent',
    dataIndex: 'three_br_rent',
  },
  {
    columnName: '3BR: Per Sqft',
    dataIndex: 'three_br_rent_sf',
  },
  {
    columnName: 'Total Relevant Units',
    dataIndex: 'total_relevant_units',
  },
];

export let multiFamilyTotalRelevantUnit;
export let twoBRAvail;
export let rentAverageTwoBR;
export let threeBRAvail;
export let rentAverageThreeBR;

const MultiFamily = connect(({ CMA }) => ({
  searchingMode: CMA.searchingMode,
  currentMultiFamilyProperties: CMA.currentMultiFamilyProperties,
  mapExpandedView: CMA.mapExpandedView,
  MultiFamilyTableSort: CMA.MultiFamilyTableSort,
  MultiFamilyDisplayedOnMap: CMA.MultiFamilyDisplayedOnMap,
  mapLocateProperty: CMA.mapLocateProperty,
  cmaTabKey: CMA.cmaTabKey,
}))(function (props) {
  const multiFamilyContainer = useRef(null);
  const multiFamilyTooltip = useRef(null);

  const [scrollToSelectedRow, setScrollToSelectedRow] = useState(false);

  useEffect(() => {
    const click = (e) => {
      if (
        e.target.id === 'locatePropertyButton' ||
        !multiFamilyContainer.current
      )
        return;

      const row = multiFamilyContainer.current.querySelector(
        `.${styles.mapLocatePropertySelected}`,
      );
      if (row) {
        row.classList.remove(styles.mapLocatePropertySelected);
        props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: { mapLocateProperty: {} },
        });
      }
    };

    document.addEventListener('click', click);
    return () => {
      document.removeEventListener('click', click);
    };
  }, []);

  useEffect(() => {
    if (scrollToSelectedRow && multiFamilyContainer.current) {
      const row = multiFamilyContainer.current.querySelector(
        `.${styles.mapLocatePropertySelected}`,
      );

      if (row) {
        row.scrollIntoView({ behavior: 'smooth', block: 'center' });
        setScrollToSelectedRow(false);
      }
    }
  }, [scrollToSelectedRow]);

  useEffect(() => {
    if (
      !isEmpty(props.mapLocateProperty) &&
      props.mapLocateProperty.type === 'multiFamily'
    ) {
      if (props.cmaTabKey !== '1') {
        props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: { cmaTabKey: '1' },
        });
      }
      setTimeout(() => {
        setScrollToSelectedRow(true);
      }, 100);
    }
  }, [props.mapLocateProperty]);

  multiFamilyTotalRelevantUnit = props.multiFamilyTotalRelevantUnit;
  twoBRAvail = props.twoBRAvail;
  rentAverageTwoBR = props.rentAverageTwoBR;
  threeBRAvail = props.threeBRAvail;
  rentAverageThreeBR = props.rentAverageThreeBR;

  const columnsMultiFamily = [
    {
      title: 'Property Name',
      dataIndex: 'property_name',
      key: 'property_name',
      // width: 250,
      align: 'left',
      sorter: (a, b) => sortString(a.property_name, b.property_name),
      sortOrder:
        props.MultiFamilyTableSort.columnKey === 'property_name'
          ? props.MultiFamilyTableSort.order
          : null,
    },
    {
      title: '2BR',
      children: [
        {
          title: 'Units',
          dataIndex: 'two_br_units',
          key: 'two_br_units',
          // width: columnWidthSmaller,
          align: 'center',
          render: (text) => formatter(text),
          sorter: (a, b) => a.two_br_units - b.two_br_units,
          sortOrder:
            props.MultiFamilyTableSort.columnKey === 'two_br_units'
              ? props.MultiFamilyTableSort.order
              : null,
        },
        {
          title: 'Avail. Units',
          dataIndex: 'two_br_avail',
          key: 'two_br_avail',
          // width: columnWidthSmaller,
          align: 'center',
          render: (text) => formatter(text),
          sorter: (a, b) => a.two_br_avail - b.two_br_avail,
          sortOrder:
            props.MultiFamilyTableSort.columnKey === 'two_br_avail'
              ? props.MultiFamilyTableSort.order
              : null,
        },
        {
          title: 'Rent',
          dataIndex: 'two_br_rent',
          key: 'two_br_rent',
          // width: columnWidthSmaller,
          align: 'center',
          render: (text) => (text ? '$' + formatter(text) : ''),
          sorter: (a, b) => a.two_br_rent - b.two_br_rent,
          sortOrder:
            props.MultiFamilyTableSort.columnKey === 'two_br_rent'
              ? props.MultiFamilyTableSort.order
              : null,
        },
        {
          title: 'Per Sqft',
          dataIndex: 'two_br_rent_sf',
          key: 'two_br_rent_sf',
          // width: columnWidthSmaller,
          align: 'center',
          render: (text) => (text ? '$' + text : ''),
          sorter: (a, b) => a.two_br_rent_sf - b.two_br_rent_sf,
          sortOrder:
            props.MultiFamilyTableSort.columnKey === 'two_br_rent_sf'
              ? props.MultiFamilyTableSort.order
              : null,
        },
      ],
    },
    {
      title: '3BR',
      children: [
        {
          title: 'Units',
          dataIndex: 'three_br_units',
          key: 'three_br_units',
          // width: columnWidthSmaller,
          align: 'center',
          render: (text) => formatter(text),
          sorter: (a, b) => a.three_br_units - b.three_br_units,
          sortOrder:
            props.MultiFamilyTableSort.columnKey === 'three_br_units'
              ? props.MultiFamilyTableSort.order
              : null,
        },
        {
          title: 'Avail. Units',
          dataIndex: 'three_br_avail',
          key: 'three_br_avail',
          // width: columnWidthSmaller,
          align: 'center',
          render: (text) => formatter(text),
          sorter: (a, b) => a.three_br_avail - b.three_br_avail,
          sortOrder:
            props.MultiFamilyTableSort.columnKey === 'three_br_avail'
              ? props.MultiFamilyTableSort.order
              : null,
        },
        {
          title: 'Rent',
          dataIndex: 'three_br_rent',
          key: 'three_br_rent',
          // width: columnWidthSmaller,
          align: 'center',
          render: (text) => (text ? '$' + formatter(text) : ''),
          sorter: (a, b) => a.three_br_rent - b.three_br_rent,
          sortOrder:
            props.MultiFamilyTableSort.columnKey === 'three_br_rent'
              ? props.MultiFamilyTableSort.order
              : null,
        },
        {
          title: 'Per Sqft',
          dataIndex: 'three_br_rent_sf',
          key: 'three_br_rent_sf',
          // width: columnWidthSmaller,
          align: 'center',
          render: (text) => (text ? '$' + text : ''),
          sorter: (a, b) => a.three_br_rent_sf - b.three_br_rent_sf,
          sortOrder:
            props.MultiFamilyTableSort.columnKey === 'three_br_rent_sf'
              ? props.MultiFamilyTableSort.order
              : null,
        },
      ],
    },
    {
      title: 'Total Relevant Units',
      dataIndex: 'total_relevant_units',
      key: 'total_relevant_units',
      // width: columnWidthSmaller,
      align: 'center',
      render: (text) => formatter(text),
      sorter: (a, b) => a.total_relevant_units - b.total_relevant_units,
      // sorter: (a, b) =>
      //   a.total_relevant_units -
      //   a.four_br_units -
      //   (b.total_relevant_units - b.four_br_units),
      sortOrder:
        props.MultiFamilyTableSort.columnKey === 'total_relevant_units'
          ? props.MultiFamilyTableSort.order
          : null,
    },
  ];

  const columnsMultiFamilyMinified = [
    {
      title: 'Property Name',
      dataIndex: 'property_name',
      key: 'property_name',
      width: 250,
      align: 'left',
      sorter: (a, b) => sortString(a.property_name, b.property_name),
      sortOrder:
        props.MultiFamilyTableSort.columnKey === 'property_name'
          ? props.MultiFamilyTableSort.order
          : null,
    },
    {
      title: '2BR',
      children: [
        {
          title: 'Rent',
          dataIndex: 'two_br_rent',
          key: 'two_br_rent',
          width: columnWidthSmaller,
          align: 'center',
          render: (text) => (text ? '$' + formatter(text) : ''),
          sorter: (a, b) => a.two_br_rent - b.two_br_rent,
          sortOrder:
            props.MultiFamilyTableSort.columnKey === 'two_br_rent'
              ? props.MultiFamilyTableSort.order
              : null,
        },
        {
          title: 'Per Sqft',
          dataIndex: 'two_br_rent_sf',
          key: 'two_br_rent_sf',
          width: columnWidthSmaller,
          align: 'center',
          render: (text) => (text ? '$' + text : ''),
          sorter: (a, b) => a.two_br_rent_sf - b.two_br_rent_sf,
          sortOrder:
            props.MultiFamilyTableSort.columnKey === 'two_br_rent_sf'
              ? props.MultiFamilyTableSort.order
              : null,
        },
      ],
    },
    {
      title: '3BR',
      children: [
        {
          title: 'Rent',
          dataIndex: 'three_br_rent',
          key: 'three_br_rent',
          width: columnWidthSmaller,
          align: 'center',
          render: (text) => (text ? '$' + formatter(text) : ''),
          sorter: (a, b) => a.three_br_rent - b.three_br_rent,
          sortOrder:
            props.MultiFamilyTableSort.columnKey === 'three_br_rent'
              ? props.MultiFamilyTableSort.order
              : null,
        },
        {
          title: 'Per Sqft',
          dataIndex: 'three_br_rent_sf',
          key: 'three_br_rent_sf',
          width: columnWidthSmaller,
          align: 'center',
          render: (text) => (text ? '$' + text : ''),
          sorter: (a, b) => a.three_br_rent_sf - b.three_br_rent_sf,
          sortOrder:
            props.MultiFamilyTableSort.columnKey === 'three_br_rent_sf'
              ? props.MultiFamilyTableSort.order
              : null,
        },
      ],
    },
    {
      title: 'Total Relevant Units',
      dataIndex: 'total_relevant_units',
      key: 'total_relevant_units',
      width: columnWidthSmaller,
      align: 'center',
      render: (text) => formatter(text),
      sorter: (a, b) => a.total_relevant_units - b.total_relevant_units,
      sortOrder:
        props.MultiFamilyTableSort.columnKey === 'total_relevant_units'
          ? props.MultiFamilyTableSort.order
          : null,
    },
  ];

  const onRowMultiFamily = (record) => {
    return {
      onClick: (event) => {
        // user clicked on a row that is not currently highlighted
        if (
          !isEqual(
            props.currentHighlightCoordinates,
            record.geom.coordinates,
          ) &&
          props.MultiFamilyDisplayedOnMap
        ) {
          props.dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              currentHighlightCoordinates: record.geom.coordinates,
              priceHighlightMarker: record.total_relevant_units,
              typeHighlightMarker: 'multiFamily',
            },
          });
        } else {
          // user clicked on the current highlighted row to de-highlight it
          props.dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              currentHighlightCoordinates: [],
            },
          });
        }
      },
      onMouseEnter: (event) => {
        if (
          !isEqual(
            props.currentHighlightCoordinates,
            record.geom.coordinates,
          ) &&
          props.MultiFamilyDisplayedOnMap
        ) {
          props.dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              currentHighlightCoordinates: record.geom.coordinates,
              priceHighlightMarker: record.total_relevant_units,
              typeHighlightMarker: 'multiFamily',
            },
          });
        }

        if (!props.mapExpandedView) return;
        const { two_br_units, two_br_avail, three_br_units, three_br_avail } =
          record;

        const tooltipMessage = `2BR Units: ${two_br_units}\n2BR Avail. Units: ${two_br_avail}\n3BR Units: ${three_br_units}\n3BR Avail. Units: ${three_br_avail}`;

        multiFamilyTooltip.current.innerText = tooltipMessage;
        multiFamilyTooltip.current.style.display = 'block';

        const containerPos =
          multiFamilyContainer.current.getBoundingClientRect();
        const rowPos = event.currentTarget.getBoundingClientRect();

        multiFamilyTooltip.current.style.top = `${
          rowPos.top -
          containerPos.top -
          multiFamilyTooltip.current.clientHeight -
          15
        }px`;
        multiFamilyTooltip.current.style.left = '50%';
        multiFamilyTooltip.current.style.transform = 'translateX(-50%)';
      },

      onMouseLeave: (event) => {
        if (!props.mapExpandedView) return;
        multiFamilyTooltip.current.innerText = '';
        multiFamilyTooltip.current.style.display = 'none';
      },
    };
  };

  return (
    <>
      {props.searchingMode === 'Lease' && (
        <div
          ref={multiFamilyContainer}
          key="multi family card"
          className={styles.cardWrapper}
        >
          <Row
            key="table title row multi family"
            align="middle"
            // justify="space-between"
            justify="space-between"
            wrap={true}
            gutter={[0, 8]}
          >
            <Col key="table title" className={styles.cardTitleH2}>
              Multi-Family
              {props.currentMultiFamilyProperties.length > 0 && (
                <>
                  {/* <button
                    className={styles.csvButton}
                    onClick={() => {
                      props.dispatch({
                        type: 'CMA/saveCMAStates',
                        payload: {
                          MultiFamilyDisplayedOnMap:
                            !props.MultiFamilyDisplayedOnMap,
                        },
                      });
                    }}
                  >
                    <ImMap />
                  </button> */}
                  <ExportToCSV
                    data={props.currentMultiFamilyProperties}
                    tableName={'Multi-Family'}
                    columnDataIndex={csvColumnDataIndex}
                  />
                </>
              )}
            </Col>
            <Col key="SFR summary row wrapper col">
              <Row
                key="SFR summary row"
                align="middle"
                justify={props.mapExpandedView ? 'start' : 'end'}
                gutter={24}
              >
                <Col key="total unit">
                  <span
                    key="total unit text"
                    className={styles.cardSubtitle}
                    style={{ marginRight: 8 }}
                  >
                    Total Relevant Units
                  </span>
                  <span
                    key="total unit number"
                    className={styles.cardDataValue}
                  >
                    {formatter(props.multiFamilyTotalRelevantUnit) || '-'}
                  </span>
                </Col>
                <Col key="2br avail">
                  <span
                    key="2br avail text"
                    className={styles.cardSubtitle}
                    style={{ marginRight: 8 }}
                  >
                    2BR Avail.
                  </span>
                  <span key="2br avail number" className={styles.cardDataValue}>
                    {!isEmpty(props.currentMultiFamilyProperties)
                      ? formatter(props.twoBRAvail)
                      : '-'}
                  </span>
                </Col>
                <Col key="2br avg">
                  <span
                    key="2br avg text"
                    className={styles.cardSubtitle}
                    style={{ marginRight: 8 }}
                  >
                    2BR Avg.
                  </span>
                  <span key="2br avg number" className={styles.cardDataValue}>
                    {!isEmpty(props.currentMultiFamilyProperties) &&
                    props.rentAverageTwoBR
                      ? '$' + formatter(props.rentAverageTwoBR.toFixed())
                      : '-'}
                  </span>
                </Col>
                <Col key="3br avail">
                  <span
                    key="3br avail text"
                    className={styles.cardSubtitle}
                    style={{ marginRight: 8 }}
                  >
                    3BR Avail.
                  </span>
                  <span key="3br avail number" className={styles.cardDataValue}>
                    {!isEmpty(props.currentMultiFamilyProperties)
                      ? formatter(props.threeBRAvail)
                      : '-'}
                  </span>
                </Col>
                <Col key="3br avg">
                  <span
                    key="3br avg text"
                    className={styles.cardSubtitle}
                    style={{ marginRight: 8 }}
                  >
                    3BR Avg.
                  </span>
                  <span key="3br avg number" className={styles.cardDataValue}>
                    {!isEmpty(props.currentMultiFamilyProperties) &&
                    props.rentAverageThreeBR
                      ? '$' + formatter(props.rentAverageThreeBR.toFixed())
                      : '-'}
                  </span>
                </Col>
              </Row>
            </Col>
          </Row>

          <div key="divider title" className={styles.dividerCardHeader} />

          {props.currentMultiFamilyProperties.length > 0 ? (
            <>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  gap: '10px',
                  alignItems: 'center',
                  marginTop: '16px',
                }}
              >
                <Switch
                  size="small"
                  defaultChecked={props.MultiFamilyDisplayedOnMap}
                  onChange={(checked) => {
                    props.dispatch({
                      type: 'CMA/saveCMAStates',
                      payload: { MultiFamilyDisplayedOnMap: checked },
                    });
                  }}
                />
                <span>Display properties on map</span>
              </div>
              <Table
                key="multi family table"
                rowKey={(record) => record.uid}
                columns={
                  props.mapExpandedView
                    ? columnsMultiFamilyMinified
                    : columnsMultiFamily
                }
                dataSource={props.currentMultiFamilyProperties}
                size="small"
                variant={'filled'}
                pagination={false}
                rowClassName={(record, index) => {
                  let className = styles.propertyDataTableRow;
                  if (
                    !isEmpty(props.mapLocateProperty) &&
                    props.mapLocateProperty.type === 'multiFamily'
                  ) {
                    if (record.uid == props.mapLocateProperty.id) {
                      className += ' ' + styles.mapLocatePropertySelected;
                    }
                  }
                  return className;
                }}
                onRow={onRowMultiFamily}
                style={{ marginTop: 16 }}
                onChange={(pagination, filters, sorter) => {
                  props.dispatch({
                    type: 'CMA/saveCMAStates',
                    payload: {
                      MultiFamilyTableSort: sorter,
                    },
                  });
                }}
              />
              <div
                ref={multiFamilyTooltip}
                className={styles.customToolTip}
              ></div>
            </>
          ) : (
            <EmptyState />
          )}
        </div>
      )}
    </>
  );
});

export default MultiFamily;
