import styles from '@/components/ResultTable/resultTable.css';
import { formatter } from '@/utils/money';
import { Col, Row, Tooltip } from 'antd';
import isEmpty from 'lodash.isempty';
import isEqual from 'lodash.isequal';
import { useEffect, useRef, useState } from 'react';
import { connect } from 'umi';
const ResultRow = connect(({ CMA }) => ({
  searchingMode: CMA.searchingMode,
  isLandMode: CMA.isLandMode,
  subjectPropertyParcelData: CMA.subjectPropertyParcelData,
  userGroup: CMA.userGroup,
  compingMode: CMA.compingMode,
  adjustedRentFormula: CMA.adjustedRentFormula,
  adjustedSalesFormula: CMA.adjustedSalesFormula,
  marketRentPreference: CMA.marketRentPreference,
  lastSalePublicRecordMedian: CMA.lastSalePublicRecordMedian,
  landCompsMedian: CMA.landCompsMedian,
  landCompMedianPricePerSqft: CMA.landCompMedianPricePerSqft,
  landCompMedianPricePerAcre: CMA.landCompMedianPricePerAcre,
  cmaTabKey: CMA.cmaTabKey,
  lastSalePublicRecordMedianPricePerSqft:
    CMA.lastSalePublicRecordMedianPricePerSqft,
  realtorMultiFamilyMedian: CMA.realtorMultiFamilyMedian,
  realtorMultiFamilyMedianPricePerSqft:
    CMA.realtorMultiFamilyMedianPricePerSqft,
  realtorSingleFamilyMedian: CMA.realtorSingleFamilyMedian,
  realtorSingleFamilyMedianPricePerSqft:
    CMA.realtorSingleFamilyMedianPricePerSqft,
  landShowcaseMedianPricePerSqft: CMA.landShowcaseMedianPricePerSqft,
  landCrexiMedianPricePerSqft: CMA.landCrexiMedianPricePerSqft,
  showAVM: CMA.showAVM,
}))(function (props) {
  const getMarketRentAndValueDiffToolTip = () => {
    let preference = '';
    switch (props.marketRentPreference) {
      case 'mls':
        if (props.rentMedianMLS) {
          preference = 'MLS listings';
        } else if (props.rentMedianSFR) {
          preference = 'national SFR operators listings';
        } else if (props.rentMedianHotpads) {
          preference = '3rd party listings';
        }
        break;
      case 'sfr':
        if (props.rentMedianSFR) {
          preference = 'national SFR operators listings';
        } else if (props.rentMedianMLS) {
          preference = 'MLS listings';
        } else if (props.rentMedianHotpads) {
          preference = '3rd party listings';
        }
        break;
      case 'hotpads':
        if (props.rentMedianHotPads) {
          preference = '3rd party listings';
        } else if (props.rentMedianMLS) {
          preference = 'MLS listings';
        } else if (props.rentMedianSFR) {
          preference = 'national SFR operators listings';
        }
        break;
      default:
        break;
    }
    if (
      props.rentAdjustedBT &&
      preference &&
      Math.abs(props.MLSMedianToAVMRatio) < 0.2
    ) {
      return (
        'Difference between ' +
        (props.searchingMode === 'Lease' ? 'rent' : 'sales') +
        ' AVM and median ' +
        (props.searchingMode === 'Lease' ? 'rent' : 'sales') +
        ' of ' +
        preference
      );
    } else {
      return '';
    }
  };

  useEffect(() => {
    if (props.rentAdjustedBT) {
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          marketRentValue: props.rentAdjustedBT,
        },
      });
    } else {
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          marketRentValue: null,
        },
      });
    }
  }, [props.rentAdjustedBT]);

  // if avm / (mls || sfr || hotpads) > 1.2 || < 0.8, show '-'
  const shouldShowAVM = () => {
    const avm =
      props.searchingMode === 'Lease'
        ? props.subjectPropertyParcelData?.rent
        : props.subjectPropertyParcelData?.sales;
    const median =
      props.rentMedianMLS || props.rentMedianSFR || props.rentMedianHotPads;
    if (!median) {
      return true;
    } else {
      return avm / median < 1.2 && avm / median > 0.8;
    }
  };

  const alignItems = !props.isLandMode ? 'flex-start' : 'flex-center';
  return (
    <Row
      key="result row"
      justify="space-between"
      align="top"
      className={styles.cardWrapperInner}
      style={{ margin: 0 }}
    >
      <Col
        key="summary average rows wrapper"
        style={{
          textAlign: 'center',
          display: 'flex',
          justifyContent:
            props.cmaTabKey === '1' && props.searchingMode === 'Land'
              ? 'flex-start'
              : 'space-between',
          alignItems: alignItems,
          gap: 16,
        }}
        span={24}
      >
        {props.userGroup.includes('CommonGroundCapital') && (
          <div key="crime score row" className={styles.summaryCardAvgWrapper}>
            <div key="crime score label" className={styles.cardSubtitle}>
              Crime
              <br />
              Score
            </div>
            <div key="crime score" className={styles.summaryCardAvgSum}>
              {!isEmpty(props.subjectPropertyParcelData)
                ? props.subjectPropertyParcelData.score_crime
                : '-'}
            </div>
          </div>
        )}

        {props.showAVM && (
          <div key="rent avm row" className={styles.summaryCardAvgWrapper}>
            <div key="rent avm label" className={styles.cardSubtitle}>
              Subject Property
              <br />
              {props.searchingMode === 'Lease' ? 'Rent AVM' : 'Sales AVM'}
            </div>
            <div key="rent avm amount" className={styles.summaryCardAvgSum}>
              {!isEmpty(props.subjectPropertyParcelData) &&
              (props.searchingMode === 'Lease'
                ? props.subjectPropertyParcelData.rent
                : props.subjectPropertyParcelData.sales) &&
              shouldShowAVM()
                ? '$' +
                  formatter(
                    props.searchingMode === 'Lease'
                      ? props.subjectPropertyParcelData.rent
                      : props.subjectPropertyParcelData.sales,
                  )
                : '-'}
            </div>
            <div
              key="rent per Sqft avm amount"
              className={styles.summaryCardAvgSqftSum}
            >
              {!isEmpty(props.subjectPropertyParcelData) &&
              (props.searchingMode === 'Lease'
                ? props.subjectPropertyParcelData.rent > 0
                : props.subjectPropertyParcelData.sales > 0) &&
              props.subjectPropertyParcelData.total_area_sq_ft > 0 &&
              shouldShowAVM()
                ? '$' +
                  (
                    (props.searchingMode === 'Lease'
                      ? props.subjectPropertyParcelData.rent
                      : props.subjectPropertyParcelData.sales) /
                    props.subjectPropertyParcelData.total_area_sq_ft
                  ).toFixed(2) +
                  ' per Sqft'
                : '-'}
            </div>
          </div>
        )}

        <div key="MLS row" className={styles.summaryCardAvgWrapper}>
          {props.cmaTabKey === '1' && (
            <>
              {props.searchingMode === 'Land' ? (
                // When isLandMode is true
                <>
                  <div
                    key="median land comps per sqft label"
                    className={styles.cardSubtitle}
                  >
                    Land Comps
                    <br />
                    Median Price
                    <br />
                    Per Sqft
                  </div>
                  <div
                    key="median land comps per sqft amount"
                    className={styles.summaryCardAvgSum}
                  >
                    {props.landCompMedianPricePerSqft
                      ? '$' + props.landCompMedianPricePerSqft.toFixed(2)
                      : '-'}
                  </div>
                </>
              ) : (
                // When isLandMode is false but cmaTabKey is '1'
                <>
                  <div key="MLS label" className={styles.cardSubtitle}>
                    Median MLS
                    <br />
                    Listings
                  </div>
                  <div key="MLS amount" className={styles.summaryCardAvgSum}>
                    {props.rentMedianMLS
                      ? '$' + formatter(props.rentMedianMLS)
                      : '-'}
                  </div>
                  <div
                    key="MLS unit amount"
                    className={styles.summaryCardAvgSqftSum}
                  >
                    {!props.userGroup.includes('BridgeTower')
                      ? props.rentAveragePerSqftMLS
                        ? '$' +
                          props.rentAveragePerSqftMLS.toFixed(2) +
                          ' per Sqft'
                        : '-'
                      : props.rentPerSqftMedianMLS
                      ? '$' +
                        props.rentPerSqftMedianMLS.toFixed(2) +
                        ' per Sqft'
                      : '-'}
                  </div>
                </>
              )}
            </>
          )}
        </div>

        {props.cmaTabKey === '1' && props.searchingMode === 'Land' && (
          <>
            <div key="MLS row 2" className={styles.summaryCardAvgWrapper}>
              <div
                key="median land comps per acre label"
                className={styles.cardSubtitle}
              >
                Land Comps
                <br />
                Median Price
                <br />
                Per Acre
              </div>
              <div
                key="median land comps per acre amount"
                className={styles.summaryCardAvgSum}
              >
                {props.landCompMedianPricePerAcre
                  ? '$' + formatter(props.landCompMedianPricePerAcre)
                  : '-'}
              </div>
            </div>
            {/* Land Showcase Median */}
            <div key="Land row 2" className={styles.summaryCardAvgWrapper}>
              <div
                key="median land showcase per sqft label"
                className={styles.cardSubtitle}
              >
                Land Showcase
                <br />
                Median Price
                <br />
                Per Sqft
              </div>
              <div
                key="median land showcase per sqft amount"
                className={styles.summaryCardAvgSum}
              >
                {props.landShowcaseMedianPricePerSqft
                  ? '$' + props.landShowcaseMedianPricePerSqft.toFixed(2)
                  : '-'}
              </div>
            </div>
            {/* Land Crexi Median */}
            <div key="Land row 3" className={styles.summaryCardAvgWrapper}>
              <div
                key="median land showcase per sqft label"
                className={styles.cardSubtitle}
              >
                Land Crexi
                <br />
                Median Price
                <br />
                Per Sqft
              </div>
              <div
                key="median land showcase per sqft amount"
                className={styles.summaryCardAvgSum}
              >
                {props.landCrexiMedianPricePerSqft
                  ? '$' + props.landCrexiMedianPricePerSqft.toFixed(2)
                  : '-'}
              </div>
            </div>
          </>
        )}

        {props.searchingMode === 'Lease' && props.cmaTabKey === '7' && (
          <div key="mf row" className={styles.summaryCardAvgWrapper}>
            <div key="SFR label" className={styles.cardSubtitle}>
              Median Multi
              <br />
              Family Listings
            </div>
            <div key="SFR amount" className={styles.summaryCardAvgSum}>
              {props.realtorMultiFamilyMedian
                ? '$' + formatter(props.realtorMultiFamilyMedian)
                : '-'}
            </div>
            <div key="SFR unit amount" className={styles.summaryCardAvgSqftSum}>
              {!props.userGroup.includes('BridgeTower')
                ? props.realtorMultiFamilyMedianPricePerSqft
                  ? '$' +
                    props.realtorMultiFamilyMedianPricePerSqft.toFixed(2) +
                    ' per Sqft'
                  : '-'
                : props.realtorMultiFamilyMedianPricePerSqft
                ? '$' +
                  props.realtorMultiFamilyMedianPricePerSqft.toFixed(2) +
                  ' per Sqft'
                : '-'}
            </div>
          </div>
        )}

        {/* Median Nat'l SFR */}
        {props.searchingMode === 'Lease' && props.cmaTabKey === '1' && (
          <div key="SFR row" className={styles.summaryCardAvgWrapper}>
            <div key="SFR label" className={styles.cardSubtitle}>
              Median Nat'l SFR
              <br />
              Operators Listings
            </div>
            <div key="SFR amount" className={styles.summaryCardAvgSum}>
              {props.rentMedianSFR ? '$' + formatter(props.rentMedianSFR) : '-'}
            </div>
            <div key="SFR unit amount" className={styles.summaryCardAvgSqftSum}>
              {!props.userGroup.includes('BridgeTower')
                ? props.rentAveragePerSqftNationalOperators
                  ? '$' +
                    props.rentAveragePerSqftNationalOperators.toFixed(2) +
                    ' per Sqft'
                  : '-'
                : props.rentPerSqftMedianSFR
                ? '$' + props.rentPerSqftMedianSFR.toFixed(2) + ' per Sqft'
                : '-'}
            </div>
          </div>
        )}
        {/* Median 3rd party */}
        {props.searchingMode === 'Lease' && props.cmaTabKey === '1' && (
          <div key="HotPads row" className={styles.summaryCardAvgWrapper}>
            <div key="HotPads label" className={styles.cardSubtitle}>
              Median Portal
              <br />
              Listings
            </div>
            <div key="HotPads amount" className={styles.summaryCardAvgSum}>
              {props.rentMedianHotPads
                ? '$' + formatter(props.rentMedianHotPads)
                : '-'}
            </div>
            <div
              key="HotPads unit amount"
              className={styles.summaryCardAvgSqftSum}
            >
              {!props.userGroup.includes('BridgeTower')
                ? props.rentAveragePerSqftHotPads
                  ? '$' +
                    props.rentAveragePerSqftHotPads.toFixed(2) +
                    ' per Sqft'
                  : '-'
                : props.rentPerSqftMedianHotPads
                ? '$' + props.rentPerSqftMedianHotPads.toFixed(2) + ' per Sqft'
                : '-'}
            </div>
          </div>
        )}
        {props.searchingMode === 'Lease' && props.cmaTabKey === '1' && (
          <div key="sf row" className={styles.summaryCardAvgWrapper}>
            <div key="SFR label" className={styles.cardSubtitle}>
              Median Secondary
              <br />
              Portal Listings
            </div>
            <div key="SFR amount" className={styles.summaryCardAvgSum}>
              {props.realtorSingleFamilyMedian
                ? '$' + formatter(props.realtorSingleFamilyMedian)
                : '-'}
            </div>
            <div key="SFR unit amount" className={styles.summaryCardAvgSqftSum}>
              {!props.userGroup.includes('BridgeTower')
                ? props.realtorSingleFamilyMedianPricePerSqft
                  ? '$' +
                    props.realtorSingleFamilyMedianPricePerSqft.toFixed(2) +
                    ' per Sqft'
                  : '-'
                : props.realtorSingleFamilyMedianPricePerSqft
                ? '$' +
                  props.realtorSingleFamilyMedianPricePerSqft.toFixed(2) +
                  ' per Sqft'
                : '-'}
            </div>
          </div>
        )}
        {/* Median Public Records */}
        {props.searchingMode === 'Sale' && (
          <div
            key="Last Sale Public Record row"
            className={styles.summaryCardAvgWrapper}
          >
            <div
              key="Last Sale Public Record label"
              className={styles.cardSubtitle}
            >
              Median Last Sale
              <br />
              Public Records
            </div>
            <div
              key="Last Sale Public Record amount"
              className={styles.summaryCardAvgSum}
            >
              {props.lastSalePublicRecordMedian
                ? //lastSalePublicRecordMedian
                  '$' + formatter(props.lastSalePublicRecordMedian)
                : '-'}
            </div>
            <div
              key="Last Sale Public Record unit amount"
              className={styles.summaryCardAvgSqftSum}
            >
              {props.lastSalePublicRecordMedianPricePerSqft
                ? '$' +
                  props.lastSalePublicRecordMedianPricePerSqft.toFixed(2) +
                  ' per Sqft'
                : '-'}
            </div>
          </div>
        )}
        {/* Market Rent / Value  */}
        {props.searchingMode !== 'Land' && props.cmaTabKey === '1' && (
          <div
            key="BT rent adjusted row"
            className={styles.summaryCardAvgWrapper}
            style={{ color: '#007e3f', fontWeight: 500 }}
          >
            <div
              key="BT rent adjusted label"
              className={styles.cardSubtitle}
              style={{ fontWeight: 500 }}
            >
              Market
              <br />
              {props.searchingMode === 'Lease' ? 'Rent' : 'Value'}
            </div>
            <Tooltip
              title={
                props.compingMode === 'intelligentComping'
                  ? props.searchingMode === 'Lease'
                    ? props.adjustedRentFormula
                    : props.adjustedSalesFormula
                  : props.rentAdjustedFormula
              }
              placement="top"
            >
              <div key="BT rent adjusted amount" className={styles.cardTitleH2}>
                {props.rentAdjustedBT ? (
                  '$' + formatter(props.rentAdjustedBT)
                ) : props.rentAverageMLS &&
                  props.subjectPropertyParcelData.rent ? (
                  <span key="manual review label" style={{ color: 'red' }}>
                    Manual Review
                  </span>
                ) : (
                  '-'
                )}
              </div>
            </Tooltip>
            <Tooltip title={getMarketRentAndValueDiffToolTip()}>
              <div
                key="BT rent adjusted difference"
                className={styles.summaryCardAvgSqftSum}
              >
                {props.MLSMedianToAVMRatio !== null &&
                Math.abs(props.MLSMedianToAVMRatio) < 0.2
                  ? 'Diff: ' +
                    Math.abs(props.MLSMedianToAVMRatio * 100).toFixed(1) +
                    '%'
                  : '-'}
              </div>
            </Tooltip>
          </div>
        )}

        {props.compingMode === 'BTRFilter' && (
          <div
            key="BTR row"
            className={styles.summaryCardAvgWrapper}
            style={{ color: '#007e3f', fontWeight: 500 }}
          >
            <div
              key="BTR label"
              className={styles.cardSubtitle}
              style={{ fontWeight: 500 }}
            >
              BTR
              <br />
              {props.searchingMode === 'Lease' ? 'Rent' : 'Value'}
            </div>
            <Tooltip placement="top">
              <div key="BTR amount" className={styles.cardTitleH2}>
                {props.rentAdjustedBT ? (
                  '$' + formatter(props.rentAdjustedBT * 1.1)
                ) : props.rentAverageMLS &&
                  props.subjectPropertyParcelData.rent ? (
                  <span key="manual review label" style={{ color: 'red' }}>
                    Manual Review
                  </span>
                ) : (
                  '-'
                )}
              </div>
            </Tooltip>
            <Tooltip title={getMarketRentAndValueDiffToolTip()}>
              <div
                key="BTR difference"
                className={styles.summaryCardAvgSqftSum}
              >
                {props.MLSMedianToAVMRatio !== null &&
                Math.abs(props.MLSMedianToAVMRatio) < 0.2
                  ? 'Diff: ' +
                    Math.abs(props.MLSMedianToAVMRatio * 1.1 * 100).toFixed(1) +
                    '%'
                  : '-'}
              </div>
            </Tooltip>
          </div>
        )}
      </Col>
    </Row>
  );
});

export default ResultRow;
