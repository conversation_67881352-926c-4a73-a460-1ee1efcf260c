export interface LandCrexi {
  geom: Geom;
  id: number;
  firstSeen: string;
  lastSeen: string;
  offersdueon: any;
  isInOpportunityZone: boolean;
  hasVideo: boolean;
  numberOfGalleryItems: number;
  showCountdownAsDate: boolean;
  userIsAssetOwner: boolean;
  numberOfImages: number;
  hasOm: boolean;
  hasFlyer: boolean;
  hasVirtualTour: boolean;
  isNew: boolean;
  locLatitude: number;
  locLongitude: number;
  detail: string;
  baseId: string;
  originalAskingPrice: any;
  currentAskingPrice: any;
  activatedOn: string;
  updatedOn: string;
  name: string;
  locFullAddress: string;
  locCity: string;
  types: string[];
  locCounty: string;
  locStateCode: string;
  description: string;
  thumbnailUrl: string;
  urlSlug: string;
  brokerTeamLogoUrl: string;
  brokerageName: string;
  status: string;
  locStateName: string;
  locZip: string;
  locAddress: string;
  type: string;
  distance: number;
}

export interface Geom {
  type: string;
  coordinates: number[];
}

export interface FilterOption {
  value: string;
  text: string;
}
