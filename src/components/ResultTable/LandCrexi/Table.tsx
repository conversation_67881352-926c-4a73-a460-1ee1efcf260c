import { arrayDifference } from '@/utils/arrayMethods';
import { formatCurrency } from '@/utils/lastSalePublicRecord';
import { Table, Tooltip } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { isEmpty, isEqual } from 'lodash';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import { useLandData } from './hooks/useLandData';
import { useTableFilters } from './hooks/useTableFilters';
import TableHeader from './TableHeader';
import {
  calculateMedian,
  calculateMedianPricePerSqFt,
} from './utils/functions';
import { LandCrexi } from './utils/type';

const createColumns = (
  onFlyTo: (lng: number, lat: number) => void,
): ColumnsType<LandCrexi> => [
  {
    title: 'Address',
    dataIndex: 'locAddress',
    key: 'locAddress',
    width: 180,
    fixed: 'left',
    align: 'left',
    render: (text: string, record: LandCrexi) => (
      <Tooltip
        title={
          record.thumbnailUrl ? (
            <div>
              <img
                src={record.thumbnailUrl}
                alt={record.locAddress}
                style={{
                  maxWidth: '200px',
                  maxHeight: '150px',
                  objectFit: 'cover',
                }}
              />
            </div>
          ) : null
        }
        placement="right"
        overlayStyle={{ maxWidth: 'none' }}
        mouseEnterDelay={0.3}
      >
        <p
          className="text-xs cursor-pointer"
          onClick={() => onFlyTo(record.locLongitude, record.locLatitude)}
        >
          {[
            record.locAddress,
            record.locCounty,
            record.locStateName,
            record.locZip,
          ]
            .filter(Boolean)
            .join(', ')}
        </p>
      </Tooltip>
    ),
  },
  {
    title: 'Dist.',
    dataIndex: 'distance',
    key: 'distance',
    width: 75,
    align: 'left',
    defaultSortOrder: 'ascend',
    sorter: (a, b) => a.distance - b.distance,
    render: (distance: number) => (
      <p className="text-xs">{(distance / 1609.34).toFixed(2)} mi</p>
    ),
  },
  {
    title: 'Status',
    dataIndex: 'type',
    key: 'type',
    width: 100,
    align: 'left',
    render: (type: string) => <p className="text-xs">{type || '-'}</p>,
    filters: [],
    onFilter: (value, record) => record.type === value,
  },
  {
    title: 'Price',
    dataIndex: 'currentAskingPrice',
    key: 'currentAskingPrice',
    width: 100,
    align: 'left',
    sorter: (a, b) => (a.currentAskingPrice || 0) - (b.currentAskingPrice || 0),
    render: (value: number) => (
      <p className="text-xs">{value ? formatCurrency(value) : '-'}</p>
    ),
  },
  {
    title: 'Type',
    dataIndex: 'types',
    key: 'types',
    width: 125,
    align: 'left',
    render: (types: string[]) => <p className="text-xs">{types?.[0] || '-'}</p>,
    filters: [],
    onFilter: (value, record) => record.types?.[0] === value,
  },
  {
    title: 'Subtype',
    dataIndex: 'detail',
    key: 'details',
    width: 100,
    align: 'left',
    render: (detail: string) => {
      try {
        const details = JSON.parse(detail || '{}');
        return <p className="text-xs">{details.Subtype || '-'}</p>;
      } catch {
        return <p className="text-xs">-</p>;
      }
    },
    filters: [],
    onFilter: (value, record) => {
      try {
        const details = JSON.parse(record.detail || '{}');
        return details.Subtype === value;
      } catch {
        return false;
      }
    },
  },
  {
    title: 'Last Updated',
    dataIndex: 'updatedOn',
    key: 'updatedOn',
    width: 125,
    align: 'left',
    render: (date: string) => (
      <p className="text-xs">{date?.split('T')[0] || '-'}</p>
    ),
    sorter: (a, b) =>
      new Date(a.updatedOn).getTime() - new Date(b.updatedOn).getTime(),
  },
  {
    title: 'First Seen',
    dataIndex: 'firstSeen',
    key: 'firstSeen',
    width: 125,
    align: 'left',
    render: (date: string) => (
      <p className="text-xs">{date?.split('T')[0] || '-'}</p>
    ),
    sorter: (a, b) =>
      new Date(a.firstSeen).getTime() - new Date(b.firstSeen).getTime(),
  },
];

const LandCrexiTable: React.FC = () => {
  const dispatch = useDispatch();
  const map = useSelector((state: any) => state.CMA.map);
  const landCrexiData = useSelector(
    (state: any) => state.CMA.landCrexiData || [],
  );
  const landCrexiDataForRender = useSelector(
    (state: any) => state.CMA.landCrexiDataForRender || [],
  );
  const landCrexiSelectedRowKey = useSelector(
    (state: any) => state.CMA.landCrexiSelectedRowKey || [],
  );
  const currentPropertyAddress = useSelector(
    (state: any) => state.CMA.currentPropertyAddress,
  );
  const currentRadiusMile = useSelector(
    (state: any) => state.CMA.currentRadiusMile,
  );
  const currentStartMLS = useSelector(
    (state: any) => state.CMA.currentStartMLS,
  );
  const currentEndMLS = useSelector((state: any) => state.CMA.currentEndMLS);
  const currentStatusMLS = useSelector(
    (state: any) => state.CMA.currentStatusMLS,
  );

  // Track current table data
  const [currentTableData, setCurrentTableData] = useState<LandCrexi[]>([]);

  // Track unselected row keys instead of selected ones for persistence
  const [unselectedRowKeys, setUnselectedRowKeys] = useState<React.Key[]>([]);

  // Track previous property address for reset
  const [prevPropertyAddress, setPrevPropertyAddress] = useState<string>(
    currentPropertyAddress,
  );

  // Store table filters
  const [tableFilters, setTableFilters] = useState<any>({});

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const landCompContainer = useRef<HTMLDivElement>(null);

  const { loading, error } = useLandData(
    currentPropertyAddress,
    currentRadiusMile,
    currentStartMLS,
    currentEndMLS,
    currentStatusMLS,
    dispatch,
  );

  const { statusOptions, propertyTypeOptions, propertySubtypeOptions } =
    useTableFilters(landCrexiData, landCrexiDataForRender);

  // Clear unselected row keys for new subject property
  if (!isEqual(currentPropertyAddress, prevPropertyAddress)) {
    setPrevPropertyAddress(currentPropertyAddress);
    setUnselectedRowKeys([]);
  }

  // Handle map navigation
  const handleFlyTo = useCallback(
    (longitude: number, latitude: number) => {
      if (map) {
        map.flyTo({
          center: [longitude, latitude],
          zoom: 16,
          speed: 2,
          curve: 1,
          easing: (t: number) => t,
        });
      }
    },
    [map],
  );

  // Create columns with filter options
  const columns = useCallback(() => {
    const baseColumns = createColumns(handleFlyTo);
    return baseColumns.map((column) => {
      if (column.key === 'type') {
        return { ...column, filters: statusOptions };
      }
      if (column.key === 'types') {
        return { ...column, filters: propertyTypeOptions };
      }
      if (column.key === 'details') {
        return { ...column, filters: propertySubtypeOptions };
      }
      return column;
    });
  }, [statusOptions, propertyTypeOptions, propertySubtypeOptions, handleFlyTo]);

  // Get current table data and calculate selected rows from unselected ones
  const getCurrentTableData = useCallback(() => {
    // Apply filters manually to current data
    const applyFilters = (data: LandCrexi[]) => {
      return data.filter((record) => {
        // Check each filter category
        for (const [key, values] of Object.entries(tableFilters)) {
          if (!values || (Array.isArray(values) && values.length === 0)) {
            continue; // Skip empty filters
          }

          if (key === 'type' && !values.includes(record.type)) {
            return false;
          }

          if (key === 'types' && !values.includes(record.types?.[0])) {
            return false;
          }

          if (key === 'details') {
            try {
              const details = JSON.parse(record.detail || '{}');
              if (!values.includes(details.Subtype)) {
                return false;
              }
            } catch {
              return false;
            }
          }
        }
        return true;
      });
    };

    const filteredData = applyFilters(landCrexiDataForRender);

    // Calculate selected row keys as difference between all rows and unselected rows
    const selectedRowKeys = arrayDifference(
      filteredData.map((row) => row.id),
      unselectedRowKeys,
    );

    return { currentTableData: filteredData, selectedRowKeys };
  }, [landCrexiDataForRender, tableFilters, unselectedRowKeys]);

  // Handle table filter/sort changes
  const handleTableChange = useCallback(
    (
      pagination: any,
      filters: any,
      sorter: any,
      { action, currentDataSource }: any,
    ) => {
      if (!landCrexiData.length) return;

      if (!isEqual(currentDataSource, currentTableData)) {
        setCurrentTableData(currentDataSource);
      }

      if (action === 'filter') {
        setTableFilters(filters);

        // Apply filters to the source data
        const filteredData = landCrexiData.filter((house) => {
          const matchesType =
            !filters.type?.length || filters.type.includes(house.type);
          const matchesPropertyType =
            !filters.types?.length || filters.types.includes(house.types?.[0]);
          let matchesSubtype = !filters.details?.length;

          if (filters.details?.length) {
            try {
              const details = JSON.parse(house.detail || '{}');
              matchesSubtype = filters.details.includes(details.Subtype);
            } catch {
              matchesSubtype = false;
            }
          }

          return matchesType && matchesPropertyType && matchesSubtype;
        });

        // Calculate and update selected row keys for the new filtered data
        const allRowKeys = filteredData.map((item) => item.id);
        const selectedRowKeys = arrayDifference(allRowKeys, unselectedRowKeys);

        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            landCrexiDataForRender: filteredData,
            landCrexiSelectedRowKey: selectedRowKeys,
          },
        });
      }
    },
    [landCrexiData, currentTableData, unselectedRowKeys, dispatch],
  );

  // Handle row selection change
  const handleRowSelection = useCallback(
    (selectedRowKeys: React.Key[]) => {
      // Calculate unselected row keys based on currently visible data
      const unselectedRowKeys = arrayDifference(
        currentTableData.map((row) => row.id),
        selectedRowKeys,
      );

      setUnselectedRowKeys(unselectedRowKeys);

      // Update redux state with selection and calculated values
      if (landCrexiDataForRender.length) {
        const median = calculateMedian(selectedRowKeys, landCrexiDataForRender);
        const medianPerSqft = calculateMedianPricePerSqFt(
          selectedRowKeys,
          landCrexiDataForRender,
        );

        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            landCrexiSelectedRowKey: selectedRowKeys,
            landCrexiMedian: median,
            landCrexiMedianPricePerSqft: medianPerSqft,
          },
        });
      }
    },
    [currentTableData, landCrexiDataForRender, dispatch],
  );

  // Handle row mouse events
  const handleRowEvents = useCallback(
    (record: LandCrexi) => ({
      onMouseEnter: () =>
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: { landCrexiHover: record },
        }),
      onMouseLeave: () =>
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: { landCrexiHover: null },
        }),
      onClick: () => handleFlyTo(record.locLongitude, record.locLatitude),
    }),
    [dispatch, handleFlyTo],
  );

  // Update current table data and selection when the render data changes
  useEffect(() => {
    if (landCrexiDataForRender.length) {
      const { currentTableData, selectedRowKeys } = getCurrentTableData();
      setCurrentTableData(currentTableData);

      if (!isEqual(landCrexiSelectedRowKey, selectedRowKeys)) {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            landCrexiSelectedRowKey: selectedRowKeys,
          },
        });
      }
    }
  }, [
    landCrexiDataForRender,
    getCurrentTableData,
    landCrexiSelectedRowKey,
    dispatch,
  ]);

  // Update calculated median values when selection changes
  useEffect(() => {
    if (landCrexiDataForRender.length && landCrexiSelectedRowKey?.length) {
      const median = calculateMedian(
        landCrexiSelectedRowKey,
        landCrexiDataForRender,
      );
      const medianPerSqft = calculateMedianPricePerSqFt(
        landCrexiSelectedRowKey,
        landCrexiDataForRender,
      );

      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          landCrexiMedian: median,
          landCrexiMedianPricePerSqft: medianPerSqft,
        },
      });
    }
  }, [landCrexiSelectedRowKey, landCrexiDataForRender, dispatch]);

  if (error) {
    return <div className="text-red-500">Error: {error}</div>;
  }

  return (
    <div className="mb-4" ref={landCompContainer}>
      <Table
        dataSource={landCrexiDataForRender}
        columns={columns()}
        rowKey="id"
        title={() => (
          <TableHeader selectedRowKeys={landCrexiSelectedRowKey || []} />
        )}
        loading={loading}
        size="small"
        rowSelection={{
          selectedRowKeys: landCrexiSelectedRowKey || [],
          onChange: handleRowSelection,
          selections: [
            Table.SELECTION_ALL,
            Table.SELECTION_INVERT,
            Table.SELECTION_NONE,
          ],
        }}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          onChange: (page, pageSize) => {
            setCurrentPage(page);
            setPageSize(pageSize);
          },
        }}
        onRow={handleRowEvents}
        scroll={{ x: 1200, y: '400px' }}
        onChange={handleTableChange}
        sticky={true}
      />
    </div>
  );
};

export default LandCrexiTable;
