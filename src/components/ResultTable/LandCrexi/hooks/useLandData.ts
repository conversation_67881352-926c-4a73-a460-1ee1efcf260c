import { getCrexi } from '@/services/data';
import { isEmpty } from 'lodash';
import { useEffect, useState } from 'react';
import { LandCrexi } from '../utils/type';

export const useLandData = (
  currentPropertyAddress: any,
  currentRadiusMile: number,
  currentStartMLS: string,
  currentEndMLS: string,
  currentStatusMLS: string,
  dispatch: any,
) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (
        isEmpty(currentPropertyAddress) ||
        !currentRadiusMile ||
        !currentStartMLS ||
        !currentEndMLS
      ) {
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const result = await getCrexi({
          lat: currentPropertyAddress.latitude,
          lng: currentPropertyAddress.longitude,
          distance: currentRadiusMile * 1609.344,
        });

        if (currentStatusMLS === 'status') {
          const startDate = new Date(currentStartMLS);
          const endDate = new Date(currentEndMLS);
          startDate.setHours(0, 0, 0, 0);
          endDate.setHours(23, 59, 59, 999);

          const filteredData = result?.filter((land: LandCrexi) => {
            const firstSeen = new Date(land.firstSeen);
            const dateInRange = firstSeen >= startDate && firstSeen <= endDate;
            console.log('result123', dateInRange);
            return dateInRange;
          });

          dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              landCrexiData: result,
              landCrexiDataForRender: filteredData,
            },
          });

          return;
        }

        const startDate = new Date(currentStartMLS);
        const endDate = new Date(currentEndMLS);
        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(23, 59, 59, 999);

        const filteredData = result?.filter((land: LandCrexi) => {
          console.log('result123', {
            startDate,
            endDate,
            firstSeen: land.firstSeen,
          });
          const firstSeen = new Date(land.firstSeen);
          const dateInRange = firstSeen >= startDate && firstSeen <= endDate;
          const statusMatches = land.type === currentStatusMLS;

          return dateInRange && statusMatches;
        });

        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            landCrexiData: result,
            landCrexiDataForRender: filteredData,
          },
        });
      } catch (error) {
        setError(
          error instanceof Error ? error.message : 'Failed to fetch land data',
        );
        console.error('Error fetching land data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [
    currentPropertyAddress,
    currentRadiusMile,
    currentStartMLS,
    currentEndMLS,
    currentStatusMLS,
    dispatch,
  ]);

  return { loading, error };
};
