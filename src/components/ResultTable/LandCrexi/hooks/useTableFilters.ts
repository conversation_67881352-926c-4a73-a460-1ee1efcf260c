import { useEffect, useState } from 'react';
import { FilterOption, LandCrexi } from '../utils/type';

export const useTableFilters = (
  landCrexiData: LandCrexi[],
  landCrexiDataForRender: LandCrexi[],
) => {
  const [statusOptions, setStatusOptions] = useState<FilterOption[]>([
    { value: 'Land', text: 'Land' }, // Default "Land" option for status
  ]);
  const [propertyTypeOptions, setPropertyTypeOptions] = useState<
    FilterOption[]
  >([]);
  const [propertySubtypeOptions, setPropertySubtypeOptions] = useState<
    FilterOption[]
  >([]);

  useEffect(() => {
    // Keep status and type options from full dataset
    if (landCrexiData?.length) {
      const createUniqueOptions = (
        extractFn: (item: LandCrexi) => string | undefined,
      ) => {
        const uniqueValues = new Set(
          landCrexiData.map(extractFn).filter(Boolean),
        );
        return Array.from(uniqueValues).map((value) => ({
          value,
          text: value,
        }));
      };

      // Add "Land" as default for status, then append other unique statuses
      const uniqueStatuses = createUniqueOptions((item) => item.type);
      setStatusOptions([
        { value: 'Land', text: 'Land' }, // Ensure "Land" is always first
        ...uniqueStatuses.filter((option) => option.value !== 'Land'), // Exclude "Land" if already in dataset to avoid duplication
      ]);

      // Set property types without a default
      setPropertyTypeOptions(createUniqueOptions((item) => item.types[0]));
    }
  }, [landCrexiData]);

  // Update subtype options based on filtered data
  useEffect(() => {
    if (landCrexiDataForRender?.length) {
      const uniqueSubtypes = new Set(
        landCrexiDataForRender
          .map((item) => {
            try {
              const details = JSON.parse(item.detail);
              return details.Subtype;
            } catch {
              return undefined;
            }
          })
          .filter(Boolean),
      );

      setPropertySubtypeOptions(
        Array.from(uniqueSubtypes).map((value) => ({
          value,
          text: value,
        })),
      );
    }
  }, [landCrexiDataForRender]);

  return { statusOptions, propertyTypeOptions, propertySubtypeOptions };
};
