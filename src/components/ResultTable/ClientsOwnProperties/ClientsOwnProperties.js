import styles from '@/components/ResultTable/resultTable.css';
import { getOwnPropertyTitleText } from '@/utils/geofenceParams';
import { formatter } from '@/utils/money';
import { sortString } from '@/utils/strings';
import { Col, Row, Switch, Table } from 'antd';
import isEmpty from 'lodash.isempty';
import isEqual from 'lodash.isequal';
import moment from 'moment';
import { useEffect, useRef, useState } from 'react';
import { connect } from 'umi';
import { capitalize } from '../../../utils/strings';
import EmptyState from '../../EmptyState';
import ExportToCSV from '../../ExportToCSV/ExportToCSV';
import {
  columnWidthSmall,
  columnWidthSmaller,
  dateFormat,
} from '../ResultTable';

const csvColumnDataIndex = [
  {
    columnName: 'Address',
    dataIndex: 'propertyaddress',
  },
  { columnName: 'Type', dataIndex: 'propertytype' },
  {
    columnName: 'Owners',
    dataIndex: 'owners',
  },
  {
    columnName: 'Bd',
    dataIndex: 'bdba',
  },
  {
    columnName: 'Ba',
    dataIndex: 'bdba',
  },
  {
    columnName: 'Sqft',
    dataIndex: 'sqft',
  },
  {
    columnName: 'YrB',
    dataIndex: 'yearbuilt',
  },
  {
    columnName: 'Status',
    dataIndex: 'status',
  },
  {
    columnName: 'Rent',
    dataIndex: 'rent',
  },
  {
    columnName: 'Lease_Exp.',
    dataIndex: 'leaseto',
  },
];

const ClientsOwnProperties = connect(({ CMA }) => ({
  currentBTOwnedProperties: CMA.currentBTOwnedProperties,
  currentHighlightCoordinates: CMA.currentHighlightCoordinates,
  expDateFilterOn: CMA.expDateFilterOn,

  searchingMode: CMA.searchingMode,
  userGroup: CMA.userGroup,
  mapExpandedView: CMA.mapExpandedView,
  eventCoordinates: CMA.eventCoordinates,
  currentRadiusMile: CMA.currentRadiusMile,
  currentStatusMLS: CMA.currentStatusMLS,
  currentStartMLS: CMA.currentStartMLS,
  currentEndMLS: CMA.currentEndMLS,
  ClientTableSort: CMA.ClientTableSort,
  drawnCustomPolygons: CMA.drawnCustomPolygons,
  mapLocateProperty: CMA.mapLocateProperty,
  cmaTabKey: CMA.cmaTabKey,
}))(function (props) {
  const BTContainer = useRef(null);
  const BTTooltip = useRef(null);

  const [scrollToSelectedRow, setScrollToSelectedRow] = useState(false);

  useEffect(() => {
    const click = (e) => {
      if (e.target.id === 'locatePropertyButton' || !BTContainer.current)
        return;

      const row = BTContainer.current.querySelector(
        `.${styles.mapLocatePropertySelected}`,
      );
      if (row) {
        row.classList.remove(styles.mapLocatePropertySelected);
        props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: { mapLocateProperty: {} },
        });
      }
    };

    document.addEventListener('click', click);
    return () => {
      document.removeEventListener('click', click);
    };
  }, []);

  useEffect(() => {
    if (scrollToSelectedRow && BTContainer.current) {
      const row = BTContainer.current.querySelector(
        `.${styles.mapLocatePropertySelected}`,
      );

      if (row) {
        row.scrollIntoView({ behavior: 'smooth', block: 'center' });
        setScrollToSelectedRow(false);
      }
    }
  }, [scrollToSelectedRow]);

  useEffect(() => {
    if (
      !isEmpty(props.mapLocateProperty) &&
      props.mapLocateProperty.type === 'BTOwned'
    ) {
      if (props.cmaTabKey !== '1') {
        props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: { cmaTabKey: '1' },
        });
      }
      setTimeout(() => {
        setScrollToSelectedRow(true);
      }, 100);
    }
  }, [props.mapLocateProperty]);

  const columnsBTOwned = [
    // {
    //   title: 'Property ID',
    //   dataIndex: 'propertyid',
    //   width: columnWidthSmaller,
    //   align: 'center',
    //   sorter: (a, b) => a.propertyid - b.propertyid,
    // },
    {
      title: 'Address',
      dataIndex: 'propertystreet1',
      width: 200,
      align: 'left',
      fixed: 'left',
      render: (text, record) =>
        capitalize(
          text +
            ', ' +
            record.propertycity +
            ', ' +
            record.propertystate +
            ' ' +
            record.propertyzip,
        ),
      // ellipsis: true,
    },
    {
      title: 'Type',
      dataIndex: 'propertytype',
      key: 'propertytype',
      // width: columnWidthSmaller,
      align: 'center',
      sorter: (a, b) => sortString(a.propertytype, b.propertytype),
      render: (text) => {
        if (text === 'Single-Family') {
          return 'SFR';
        } else {
          return text;
        }
      },
      sortOrder:
        props.ClientTableSort.columnKey === 'propertytype'
          ? props.ClientTableSort.order
          : null,
      // ellipsis: true,
    },
    {
      title: 'Owners',
      dataIndex: 'owners',
      key: 'owners',
      // width: columnWidthSmall,
      align: 'left',
      sorter: (a, b) => sortString(a.owners, b.owners),
      sortOrder:
        props.ClientTableSort.columnKey === 'owners'
          ? props.ClientTableSort.order
          : null,
      // ellipsis: true,
    },
    // {
    //   title: 'Unit ID',
    //   dataIndex: 'unitid',
    //   width: columnWidthSmaller,
    //   align: 'center',
    //   sorter: (a, b) => a.unitid - b.unitid,
    // },
    {
      title: 'Bd',
      dataIndex: 'bdba',
      key: 'bd',
      // width: columnWidthSmaller,
      align: 'center',
      render: (text) => (text !== '--' ? text.split('/')[0] : '-'),
      sorter: (a, b) => sortString(a.bdba.split('/')[0], b.bdba.split('/')[0]),
      sortOrder:
        props.ClientTableSort.columnKey === 'bd'
          ? props.ClientTableSort.order
          : null,
    },
    {
      title: 'Ba',
      dataIndex: 'bdba',
      key: 'ba',
      // width: columnWidthSmaller,
      align: 'center',
      render: (text) =>
        text !== '--' ? (+text.split('/')[1]).toFixed(1) : '-',
      sorter: (a, b) => sortString(a.bdba.split('/')[1], b.bdba.split('/')[1]),
      sortOrder:
        props.ClientTableSort.columnKey === 'ba'
          ? props.ClientTableSort.order
          : null,
    },
    {
      title: 'Sqft',
      dataIndex: 'sqft',
      key: 'sqft',
      // width: columnWidthSmaller,
      align: 'center',
      sorter: (a, b) => +a.sqft - +b.sqft,
      sortOrder:
        props.ClientTableSort.columnKey === 'sqft'
          ? props.ClientTableSort.order
          : null,
    },
    {
      title: 'YrB',
      dataIndex: 'yearbuilt',
      key: 'yearbuilt',
      // width: columnWidthSmaller,
      align: 'center',
      sorter: (a, b) => +a.yearbuilt - +b.yeatbuilt,
      sortOrder:
        props.ClientTableSort.columnKey === 'yearbuilt'
          ? props.ClientTableSort.order
          : null,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      // width: columnWidthSmall,
      align: 'left',
      sorter: (a, b) => sortString(a.status, b.status),
      sortOrder:
        props.ClientTableSort.columnKey === 'status'
          ? props.ClientTableSort.order
          : null,
    },
    {
      title: 'Rent',
      dataIndex: 'totalrent',
      key: 'totalrent',
      // width: columnWidthSmaller,
      align: 'center',
      render: (text) => '$' + formatter(+text),
      sorter: (a, b) => +a.totalrent - +b.totalrent,
      // render: (text, record) => '$' + formatter(+text + +record.section_8),
      // sorter: (a, b) => (+a.rent + +a.section_8) - (+b.rent + +b.section_8),
      sortOrder:
        props.ClientTableSort.columnKey === 'totalrent'
          ? props.ClientTableSort.order
          : null,
    },
    {
      title: 'Lease Exp.',
      dataIndex: 'leaseexpirationdate',
      key: 'leaseexpirationdate',
      // width: columnWidthSmall,
      align: 'center',
      render: (text) => {
        if (text) {
          return moment(text).format(dateFormat);
        } else {
          return '';
        }
      },
      sortOrder:
        props.ClientTableSort.columnKey === 'leaseexpirationdate'
          ? props.ClientTableSort.order
          : null,
    },
  ];

  const columnsBTOwnedMinified = [
    {
      title: 'Property Address',
      dataIndex: 'propertystreet1',
      // width: 250,
      align: 'left',
      render: (text, record) =>
        text +
        ', ' +
        record.propertycity +
        ', ' +
        record.propertystate +
        ' ' +
        record.propertyzip,
      // ellipsis: true,
    },
    {
      title: 'Sqft',
      dataIndex: 'sqft',
      key: 'sqft',
      // width: columnWidthSmaller,
      align: 'center',
      sorter: (a, b) => +a.sqft - +b.sqft,
      sortOrder:
        props.ClientTableSort.columnKey === 'sqft'
          ? props.ClientTableSort.order
          : null,
    },
    {
      title: 'Year Built',
      dataIndex: 'yearbuilt',
      key: 'yearbuilt',
      // width: columnWidthSmaller,
      align: 'center',
      sorter: (a, b) => +a.yearbuilt - +b.yeatbuilt,
      sortOrder:
        props.ClientTableSort.columnKey === 'yearbuilt'
          ? props.ClientTableSort.order
          : null,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      // width: columnWidthSmall,
      align: 'left',
      sorter: (a, b) => sortString(a.status, b.status),
      sortOrder:
        props.ClientTableSort.columnKey === 'status'
          ? props.ClientTableSort.order
          : null,
    },
  ];

  const onRowBTOwned = (record) => {
    return {
      onClick: (event) => {
        // user clicked on a row that is not currently highlighted
        // if (
        //   !isEqual(
        //     props.currentHighlightCoordinates,
        //     record.geography.coordinates,
        //   )
        // ) {
        //   props.dispatch({
        //     type: 'CMA/saveCMAStates',
        //     payload: {
        //       currentHighlightCoordinates: record.geography.coordinates,
        //       priceHighlightMarker: +record.totalrent,
        //       typeHighlightMarker: 'BTOwned',
        //     },
        //   });
        // } else {
        //   // user clicked on the current highlighted row to de-highlight it
        //   props.dispatch({
        //     type: 'CMA/saveCMAStates',
        //     payload: {
        //       currentHighlightCoordinates: [],
        //     },
        //   });
        // }
      },
      onMouseEnter: (event) => {
        // if (
        //   !isEqual(
        //     props.currentHighlightCoordinates,
        //     record.geography.coordinates,
        //   )
        // ) {
        props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            currentHighlightCoordinates: record.geography.coordinates,
            priceHighlightMarker: +record.totalrent,
            typeHighlightMarker: 'BTOwned',
          },
        });
        // }

        if (!props.mapExpandedView) return;
        const { propertytype, owners, bdba, yearbuilt, status } = record;

        const bed = bdba.split('/')[0];
        const bath = bdba.split('/')[1];
        const tooltipMessage = `Property Type: ${propertytype}\nOwners: ${owners}\nBeds: ${bed}\nBaths: ${bath}\nYear Built: ${yearbuilt}\nStatus: ${status}`;

        BTTooltip.current.innerText = tooltipMessage;
        BTTooltip.current.style.display = 'block';

        const containerPos = BTContainer.current.getBoundingClientRect();
        const rowPos = event.currentTarget.getBoundingClientRect();

        BTTooltip.current.style.top = `${
          rowPos.top - containerPos.top - BTTooltip.current.clientHeight - 15
        }px`;
        BTTooltip.current.style.left = '50%';
        BTTooltip.current.style.transform = 'translateX(-50%)';
      },

      onMouseLeave: (event) => {
        if (!props.mapExpandedView) return;
        BTTooltip.current.innerText = '';
        BTTooltip.current.style.display = 'none';
      },
    };
  };

  const onChangeFilterSwitchExpDate = (checked) => {
    if (checked) {
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          expDateFilterOn: true,
        },
      });
    } else {
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          expDateFilterOn: false,
        },
      });
    }
    // props.dispatch({
    //   type: 'CMA/getBTOwnedPropertiesWithinRadius',
    //   payload: {
    //     lat: props.eventCoordinates[1],
    //     lng: props.eventCoordinates[0],
    //     distance: props.currentRadiusMile * 1609.34,
    //     expDateFilterOn: checked ? 'yes' : 'no',
    //   },
    // });
    // fetch all data
    if (props.drawnCustomPolygons.length === 0) {
      props.dispatch({
        type: 'CMA/getAllPropertyData',
        payload: {
          mode: 'change BTOwned expiring only',
          lng: props.eventCoordinates[0],
          lat: props.eventCoordinates[1],
          // geofence: getGeofenceParamForGeocodingAPI(props.userGroup),
          status: props.currentStatusMLS,
          propertyType:
            props.searchingMode === 'Lease'
              ? 'Residential Lease'
              : 'Residential',
          startDate: props.currentStartMLS,
          endDate: props.currentEndMLS,
          distance: props.currentRadiusMile * 1609.34,
          exists: props.currentStatusMLS,
          expDateFilterOn: checked ? 'yes' : 'no',
        },
      });
    } else {
      props.dispatch({
        type: 'CMA/getAllPropertyData',
        payload: {
          mode: 'change BTOwned expiring only',
        },
      });
    }
  };

  return (
    <>
      {props.searchingMode === 'Lease' && (
        <div
          ref={BTContainer}
          key="BT owned card"
          className={styles.cardWrapper}
          style={{
            display: props.searchingMode === 'Lease' ? 'block' : 'none',
          }}
        >
          <Row
            key="table title row BT owned"
            align="bottom"
            justify="space-between"
            wrap={true}
            gutter={[0, 8]}
          >
            <Col key="table title" className={styles.cardTitleH2}>
              {/* Properties Owned by Bridge Tower */}
              {getOwnPropertyTitleText(props.userGroup)}

              {props.currentBTOwnedProperties.length > 0 && (
                <ExportToCSV
                  tableName={getOwnPropertyTitleText(props.userGroup)}
                  columnDataIndex={csvColumnDataIndex}
                  data={props.currentBTOwnedProperties}
                />
              )}
            </Col>
            <Col key="SFR summary row col">
              <Row
                key="SFR summary row"
                align="middle"
                justify="end"
                gutter={24}
              >
                <Col key="total unit">
                  <span
                    key="total unit text"
                    className={styles.cardSubtitle}
                    style={{ marginRight: 8 }}
                  >
                    Total
                  </span>
                  <span
                    key="total unit number"
                    className={styles.cardDataValue}
                  >
                    {props.currentBTOwnedProperties.length || '-'}
                  </span>
                </Col>
              </Row>
            </Col>
          </Row>

          <div key="divider title" className={styles.dividerCardHeader} />

          <Row
            key="control wrapper"
            align="middle"
            justify="start"
            className={styles.controlRow}
            gutter={48}
          >
            <Col key="exp date filter button">
              <Row
                key="filter control row"
                align="middle"
                justify="center"
                gutter={12}
              >
                <Col key="label">
                  <label key="title" htmlFor="smart filter switch">
                    Lease expiring within 90 days only
                  </label>
                </Col>
                <Col key="switch">
                  <Switch
                    key="exp date filter switch"
                    id="exp date filter switch"
                    defaultChecked={false}
                    checked={props.expDateFilterOn}
                    // checkedChildren="Auto"
                    // unCheckedChildren="Manual"
                    onChange={onChangeFilterSwitchExpDate}
                  />
                </Col>
              </Row>
            </Col>
          </Row>

          {props.currentBTOwnedProperties.length > 0 ? (
            <>
              <Table
                id="BT-owned-table"
                key="BT owned table"
                rowKey={(record) => record.unitid}
                columns={
                  props.mapExpandedView
                    ? columnsBTOwnedMinified
                    : columnsBTOwned
                }
                dataSource={props.currentBTOwnedProperties}
                size="small"
                variant={'filled'}
                pagination={false}
                rowClassName={(record, index) => {
                  let className = styles.propertyDataTableRow;
                  if (
                    !isEmpty(props.mapLocateProperty) &&
                    props.mapLocateProperty.type === 'BTOwned'
                  ) {
                    if (record.unitid == props.mapLocateProperty.id) {
                      className += ' ' + styles.mapLocatePropertySelected;
                    }
                  }
                  return className;
                }}
                onRow={onRowBTOwned}
                sticky={{
                  getContainer: () =>
                    document.getElementById('resultTableScrollWrapper'),
                }}
                scroll={{ x: 1000 }}
                onChange={(pagination, filters, sorter) => {
                  props.dispatch({
                    type: 'CMA/saveCMAStates',
                    payload: {
                      ClientTableSort: sorter,
                    },
                  });
                }}
              />
              <div ref={BTTooltip} className={styles.customToolTip}></div>
            </>
          ) : (
            <EmptyState />
          )}
        </div>
      )}
    </>
  );
});

export default ClientsOwnProperties;
