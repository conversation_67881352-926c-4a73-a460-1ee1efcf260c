import styles from '@/components/ResultTable/resultTable.css';
import { showSFROperatorsFullName } from '@/components/SFRBrandConvertFunction';
import { useElementSize } from '@/hooks';
import { arrayDifference } from '@/utils/arrayMethods';
import { formatter } from '@/utils/money';
import { default as turf_distance } from '@turf/distance';
import { point } from '@turf/helpers';
import { Col, DatePicker, Row, Table } from 'antd';
import isEmpty from 'lodash.isempty';
import isEqual from 'lodash.isequal';
import moment from 'moment';
import { useEffect, useRef, useState } from 'react';
import { renderToString } from 'react-dom/server';
import { connect } from 'umi';
import { capitalize } from '../../../utils/strings';
import EmptyState from '../../EmptyState';
import ExportToCSV from '../../ExportToCSV/ExportToCSV';
import { openMLSImageModal } from '../MLS/MLS';
import {
  columnWidthSmall,
  columnWidthSmaller,
  dateFormat,
} from '../ResultTable';

export let selectedRowKey;
export let rentAverageNationalOperators;

const NationalSFROperators = connect(({ CMA }) => ({
  subjectPropertyParcelData: CMA.subjectPropertyParcelData,
  currentStartMLS: CMA.currentStartMLS,
  currentEndMLS: CMA.currentEndMLS,
  currentStatusMLS: CMA.currentStatusMLS,
  currentNationalOperatorsPropertiesFiltered:
    CMA.currentNationalOperatorsPropertiesFiltered,
  currentHotPadsPropertiesFiltered: CMA.currentHotPadsPropertiesFiltered,
  currentMLSPropertiesFiltered: CMA.currentMLSPropertiesFiltered,
  currentRadiusMile: CMA.currentRadiusMile,
  eventCoordinates: CMA.eventCoordinates,
  searchingMode: CMA.searchingMode,
  mapExpandedView: CMA.mapExpandedView,
  userGroup: CMA.userGroup,
  NSFRTableSort: CMA.NSFRTableSort,
  HotPadTableSort: CMA.HotPadTableSort,
  sfrTableFilters: CMA.sfrTableFilters,
  hotpadsTableFilters: CMA.hotpadsTableFilters,
  drawnCustomPolygons: CMA.drawnCustomPolygons,
  mapLocateProperty: CMA.mapLocateProperty,
  cmaTabKey: CMA.cmaTabKey,
  compingMode: CMA.compingMode,
  fetchCompSFRDone: CMA.fetchCompSFRDone,
  fetchCompHotPadsDone: CMA.fetchCompHotPadsDone,
  selectedRowKeysNationalOperators: CMA.selectedRowKeysNationalOperators,
  selectedRowKeysHotPads: CMA.selectedRowKeysHotPads,
}))(function (props) {
  const NationalContainer = useRef(null);
  const NationalTooltip = useRef(null);

  const [scrollToSelectedRow, setScrollToSelectedRow] = useState(false);
  const [
    previousNationalOperatorsPropertiesFiltered,
    setPreviousNationalOperatorsPropertiesFiltered,
  ] = useState([]);
  const [
    previousHotPadsPropertiesFiltered,
    setPreviousHotPadsPropertiesFiltered,
  ] = useState([]);
  const [prevSubjectPropertyParcelData, setPrevSubjectPropertyParcelData] =
    useState(props.subjectPropertyParcelData);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [currentTableData, setCurrentTableData] = useState([]); // current table data with current sorting and filtering
  const [
    unselectedRowKeysNationalOperators,
    setUnselectedRowKeysNationalOperators,
  ] = useState([]);
  const [unselectedRowKeysHotPads, setUnselectedRowKeysHotPads] = useState([]);

  // sort the table data based on NSFRTableSort or HotPadTableSort
  const getInitialTableData = () => {
    if (props.type === 'SFR') {
      return props.currentNationalOperatorsPropertiesFiltered.sort(
        (a, b) =>
          a[props.NSFRTableSort.columnKey] - b[props.NSFRTableSort.columnKey],
      );
    } else if (props.type === 'HotPads') {
      return props.currentHotPadsPropertiesFiltered.sort(
        (a, b) =>
          a[props.HotPadTableSort.columnKey] -
          b[props.HotPadTableSort.columnKey],
      );
    }
  };

  // get current table data after currentNationalOperatorsPropertiesFiltered or currentHotPadsPropertiesFiltered has changed
  // and keep selection
  const getCurrentTableData = () => {
    // first, sort the data based on current sort order
    const allCompsDataSorted = getInitialTableData();
    // remove filters that are null, e.g. { propertysubtype: null }
    const tableFilters =
      props.type === 'SFR' ? props.sfrTableFilters : props.hotpadsTableFilters;
    const nonNullFilters = Object.fromEntries(
      Object.entries(tableFilters).filter(([_, value]) => value !== null),
    );
    // then, filter the data based on current filters
    const currentTableData = allCompsDataSorted.filter((row) => {
      return Object.keys(nonNullFilters).every((filter) => {
        return nonNullFilters[filter].includes(row[filter]);
      });
    });
    // then, remove unselected rows from all row keys
    const unselectedRowKeys =
      props.type === 'SFR'
        ? unselectedRowKeysNationalOperators
        : unselectedRowKeysHotPads;
    const selectedRowKeys = arrayDifference(
      currentTableData.map((row) => row.base_id),
      unselectedRowKeys,
    );
    return { currentTableData, selectedRowKeys };
  };

  // clear unselected row keys for new subject property
  if (
    !isEqual(props.subjectPropertyParcelData, prevSubjectPropertyParcelData)
  ) {
    setPrevSubjectPropertyParcelData(props.subjectPropertyParcelData);
    setUnselectedRowKeysNationalOperators([]);
    setUnselectedRowKeysHotPads([]);
  }

  // after currentNationalOperatorsPropertiesFiltered or currentHotPadsPropertiesFiltered has changed
  // get current table data and selected row keys
  if (
    (props.type === 'SFR' &&
      !isEqual(
        props.currentNationalOperatorsPropertiesFiltered,
        previousNationalOperatorsPropertiesFiltered,
      )) ||
    (props.type === 'HotPads' &&
      !isEqual(
        props.currentHotPadsPropertiesFiltered,
        previousHotPadsPropertiesFiltered,
      ))
  ) {
    const { currentTableData, selectedRowKeys } = getCurrentTableData();
    setCurrentTableData(currentTableData);
    if (props.type === 'SFR') {
      props.onSelectChangeNationalOperators(selectedRowKeys);
      setPreviousNationalOperatorsPropertiesFiltered(
        props.currentNationalOperatorsPropertiesFiltered,
      );
    } else if (props.type === 'HotPads') {
      props.onSelectChangeHotPads(selectedRowKeys);
      setPreviousHotPadsPropertiesFiltered(
        props.currentHotPadsPropertiesFiltered,
      );
    }
  }

  const tableSize = useElementSize(NationalContainer);

  selectedRowKey = props.selectedRowKeysNationalOperators;
  rentAverageNationalOperators = props.rentAverageNationalOperators;

  const dataSource =
    props.type === 'SFR'
      ? props.currentNationalOperatorsPropertiesFiltered
      : props.currentHotPadsPropertiesFiltered;
  const selectedRowKeys =
    props.type === 'SFR'
      ? props.selectedRowKeysNationalOperators
      : props.selectedRowKeysHotPads;
  const onSelectChange =
    props.type === 'SFR'
      ? props.onSelectChangeNationalOperators
      : props.onSelectChangeHotPads;
  const rentMedian =
    props.type === 'SFR' ? props.rentMedianSFR : props.rentMedianHotPads;

  const columnsNationalOperators = getTableColumns(
    props,
    'original',
    dataSource,
  );

  const columnsNationalOperatorsMinified = getTableColumns(props, 'minified');

  useEffect(() => {
    const click = (e) => {
      if (e.target.id === 'locatePropertyButton' || !NationalContainer.current)
        return;

      const row = NationalContainer.current.querySelector(
        `.${styles.mapLocatePropertySelected}`,
      );
      if (row) {
        row.classList.remove(styles.mapLocatePropertySelected);
        props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: { mapLocateProperty: {} },
        });
      }
    };

    document.addEventListener('click', click);
    return () => {
      document.removeEventListener('click', click);
    };
  }, []);

  useEffect(() => {
    if (scrollToSelectedRow && NationalContainer.current) {
      const row = NationalContainer.current.querySelector(
        `.${styles.mapLocatePropertySelected}`,
      );

      if (row) {
        row.scrollIntoView({ behavior: 'smooth', block: 'center' });
        setScrollToSelectedRow(false);
      }
    }
  }, [scrollToSelectedRow]);

  useEffect(() => {
    if (
      !isEmpty(props.mapLocateProperty) &&
      props.mapLocateProperty.type === props.type
    ) {
      const rowData = currentTableData.find(
        (property) => property.base_id === props.mapLocateProperty.id,
      );
      if (rowData) {
        const rowIndex = currentTableData.indexOf(rowData);
        const rowPage = Math.floor(rowIndex / pageSize) + 1;
        setCurrentPage(rowPage);
        if (props.cmaTabKey !== '1') {
          props.dispatch({
            type: 'CMA/saveCMAStates',
            payload: { cmaTabKey: '1' },
          });
        }
        setTimeout(() => {
          setScrollToSelectedRow(true);
        }, 100);
      }
    }
  }, [props.mapLocateProperty]);

  // can use on multi family also
  const onRowNationalOperator = (record) => {
    return {
      onClick: (event) => {
        if (
          event.target.localName === 'path' ||
          event.target.localName === 'svg'
        ) {
          // console.log('record', record);
          const mlsRecord = props.currentMLSPropertiesFiltered.find(
            (mls) => mls.placekey === record.placekey,
          );
          openMLSImageModal(mlsRecord, props);
        }
      },
      onMouseEnter: (event) => {
        if (
          !isEqual(props.currentHighlightCoordinates, record.geom.coordinates)
        ) {
          props.dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              currentHighlightCoordinates: record.geom.coordinates,
              priceHighlightMarker: record.rent,
              typeHighlightMarker:
                record.brand === 'Hotpads' ? 'HotPads' : record.brand,
            },
          });
        }

        if (!props.mapExpandedView) return;

        const { brand, bed_rooms, bath_rooms, status } = record;
        // const exists = record.exists ? 'Available' : 'Closed';
        const distance =
          (parseFloat(record.distance) / 1609.34).toFixed(1) + ' mi';
        const available_date = moment(record.available_date).format(dateFormat);
        const close_date = moment(record.close_date).format(dateFormat);
        const tooltipMessage = `Distance: ${distance}\nStatus: ${status}\nOwner: ${brand}\nBeds: ${bed_rooms}\nBaths: ${bath_rooms}\nAvail. Date: ${available_date}\nClose Date: ${close_date}`;

        NationalTooltip.current.innerText = tooltipMessage;
        NationalTooltip.current.style.display = 'block';

        const containerPos = NationalContainer.current.getBoundingClientRect();
        const rowPos = event.currentTarget.getBoundingClientRect();

        NationalTooltip.current.style.top = `${
          rowPos.top -
          containerPos.top -
          NationalTooltip.current.clientHeight -
          15
        }px`;
        NationalTooltip.current.style.left = '50%';
        NationalTooltip.current.style.transform = 'translateX(-50%)';
      },
      onMouseLeave: (event) => {
        if (!props.mapExpandedView) return;
        NationalTooltip.current.innerText = '';
        NationalTooltip.current.style.display = 'none';
      },
    };
  };

  const isBridgeTower =
    props.userGroup && props.userGroup.includes('BridgeTower');

  const tableHeader = (
    <Row
      id={`sfrTableHeader-${props.type}`}
      key="table title row national operators"
      align="bottom"
      justify="space-between"
      style={{ marginBottom: 12 }}
    >
      <div key="table title" className={styles.cardTitleH2}>
        {props.type === 'SFR'
          ? 'National SFR Operators Listings'
          : 'Portal Listings'}
      </div>
      <Row key="SFR summary row" align="middle" justify="end" gutter={24}>
        <Col key="total unit">
          <span
            key="total unit text"
            className={styles.cardSubtitle}
            style={{ marginRight: 8 }}
          >
            Total Selected
          </span>
          <span
            id={`sfrTable-Total-${props.type}`}
            key="total unit number"
            className={styles.cardDataValue}
          >
            {/* {(dataSource ? formatter(dataSource.length) : '-') || '-'} */}
            {props.type === 'SFR'
              ? props.selectedRowKeysNationalOperators.length || '-'
              : props.selectedRowKeysHotPads.length || '-'}
          </span>
        </Col>
        <Col key="SFR avg">
          <span
            key="SFR avg text"
            className={styles.cardSubtitle}
            style={{ marginRight: 8 }}
          >
            {/* {isBridgeTower ? 'Median' : 'Avg.'} Rent */}
            Median Rent
          </span>
          <span
            id={`sfrTable-Median-${props.type}`}
            key="SFR avg number"
            className={styles.cardDataValue}
          >
            {/* {isBridgeTower
                    ? rentMedian
                      ? '$' + formatter(rentMedian)
                      : '-'
                    : props.rentAverageNationalOperators
                    ? '$' + formatter(props.rentAverageNationalOperators)
                    : '-'} */}
            {rentMedian ? '$' + formatter(rentMedian) : '-'}
          </span>
        </Col>
      </Row>
    </Row>
  );

  // useEffect(() => {
  //   if (!NationalContainer.current) return;
  //   const headerTable = NationalContainer.current.querySelector(
  //     '.ant-table-header table',
  //   );

  //   const headerTableTitle = NationalContainer.current.querySelector(
  //     `#sfrTableHeader-${props.type}`,
  //   );

  //   if (headerTable && !headerTableTitle) {
  //     const el = renderToString(tableHeader);
  //     headerTable.insertAdjacentHTML('beforebegin', el);
  //   }
  // }, [NationalContainer.current, dataSource, rentMedian]);

  // useEffect(() => {
  //   if (!NationalContainer.current) return;

  //   const sfrTableTotal = NationalContainer.current.querySelector(
  //     `#sfrTable-Total-${props.type}`,
  //   );
  //   const sfrTableMedian = NationalContainer.current.querySelector(
  //     `#sfrTable-Median-${props.type}`,
  //   );
  //   if (sfrTableTotal) {
  //     sfrTableTotal.innerText = dataSource.length || '-';
  //   }
  //   if (sfrTableMedian) {
  //     sfrTableMedian.innerText = rentMedian ? '$' + formatter(rentMedian) : '-';
  //   }
  // }, [dataSource, rentMedian]);

  return (
    <>
      {props.searchingMode === 'Lease' && (
        <div
          ref={NationalContainer}
          key="national operators card"
          className={styles.cardWrapperSFR}
          style={{
            display: props.searchingMode === 'Lease' ? 'block' : 'none',
          }}
        >
          {/* <div key="divider title" className={styles.dividerCardHeader} /> */}
          <Table
            key="national providers table"
            className="table-sticky-title-header"
            rowKey={(record) => record.base_id}
            columns={
              props.mapExpandedView
                ? columnsNationalOperatorsMinified
                : columnsNationalOperators
            }
            dataSource={dataSource}
            size="small"
            variant={'filled'}
            // pagination={false}
            pagination={{
              current: currentPage,
              pageSize: pageSize,
              onChange: (page, pageSize) => {
                setCurrentPage(page);
                setPageSize(pageSize);
              },
            }}
            loading={
              props.type === 'SFR'
                ? !props.fetchCompSFRDone
                : !props.fetchCompHotPadsDone
            }
            rowSelection={{
              selectedRowKeys: selectedRowKeys,
              onChange: (selectedRowKeys) => {
                const unselectedRowKeys = arrayDifference(
                  dataSource.map((row) => row.base_id),
                  selectedRowKeys,
                );
                if (props.type === 'SFR') {
                  setUnselectedRowKeysNationalOperators(unselectedRowKeys);
                } else if (props.type === 'HotPads') {
                  setUnselectedRowKeysHotPads(unselectedRowKeys);
                }
                onSelectChange(selectedRowKeys);
              },
              selections: [
                Table.SELECTION_ALL,
                Table.SELECTION_INVERT,
                Table.SELECTION_NONE,
              ],
              getCheckboxProps: (record) => ({
                disabled: props.compingMode === 'intelligentComping',
              }),
            }}
            rowClassName={(record, index) => {
              let className = styles.propertyDataTableRow;
              if (
                !isEmpty(props.mapLocateProperty) &&
                props.mapLocateProperty.type === props.type
              ) {
                if (record.base_id === props.mapLocateProperty.id) {
                  className += ' ' + styles.mapLocatePropertySelected;
                }
              }
              return className;
            }}
            onRow={onRowNationalOperator}
            sticky={true}
            components={{
              header: {
                wrapper: ({ className, children }) => {
                  return (
                    <thead className={className}>
                      <div style={{ width: tableSize.width - 12 }}>
                        {tableHeader}
                      </div>
                      {children}
                    </thead>
                  );
                },
              },
            }}
            // sticky={{
            //   getContainer: () =>
            //     document.getElementById('resultTableScrollWrapper'),
            // }}
            scroll={props.mapExpandedView ? null : { x: 2000 }}
            defaultFilteredValue={dataSource}
            filterResetToDefaultFilteredValue={true}
            onChange={(pagination, filters, sorter, extra) => {
              // console.log(sorter);
              console.log('SFR/HotPads Table onChange extra', extra);
              if (!isEqual(extra.currentDataSource, currentTableData)) {
                setCurrentTableData(extra.currentDataSource);
              }
              switch (extra.action) {
                case 'sort':
                  props.dispatch({
                    type: 'CMA/saveCMAStates',
                    payload: {
                      ...(props.type === 'SFR'
                        ? { NSFRTableSort: sorter }
                        : { HotPadTableSort: sorter }),
                    },
                  });
                  break;
                case 'filter':
                  // selectedRowKeys are not guaranteed to update automatically
                  // so we need to manually update them
                  // and use unselectedRowKeys to keep selection
                  const allRowKeys = extra.currentDataSource.map(
                    (item) => item.base_id,
                  );
                  const unselectedRowKeys =
                    props.type === 'SFR'
                      ? unselectedRowKeysNationalOperators
                      : unselectedRowKeysHotPads;
                  const selectedRowKeys = arrayDifference(
                    allRowKeys,
                    unselectedRowKeys,
                  );
                  props.dispatch({
                    type: 'CMA/saveCMAStates',
                    payload: {
                      ...(props.type === 'SFR'
                        ? { sfrTableFilters: filters }
                        : { hotpadsTableFilters: filters }),

                      ...(props.type === 'SFR'
                        ? { selectedRowKeysNationalOperators: selectedRowKeys }
                        : { selectedRowKeysHotPads: selectedRowKeys }),
                    },
                  });
                  break;
                default:
                  break;
              }
            }}
            summary={(currentData) => {
              return (
                <Table.Summary fixed>
                  <Table.Summary.Row>
                    <Table.Summary.Cell
                      colSpan={
                        props.mapExpandedView ? 2 : props.type === 'SFR' ? 6 : 5
                      }
                    >
                      {/* {isBridgeTower ? 'Median' : 'Avg.'} Rent of Selected */}
                      Median Rent of Selected
                    </Table.Summary.Cell>
                    <Table.Summary.Cell
                      className={styles.tableSummaryCellTextAlignCenter}
                    >
                      $
                      {/* {isBridgeTower
                          ? rentMedian
                            ? formatter(rentMedian)
                            : '-'
                          : props.rentAverageNationalOperators
                          ? formatter(props.rentAverageNationalOperators)
                          : '-'} */}
                      {rentMedian ? formatter(rentMedian) : '-'}
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                </Table.Summary>
              );
            }}
          />
          <div ref={NationalTooltip} className={styles.customToolTip}></div>
        </div>
      )}
    </>
  );
});

export default NationalSFROperators;

const getTableColumns = (props, columnType, dataSource) => {
  const tableSorter =
    props.type === 'SFR' ? props.NSFRTableSort : props.HotPadTableSort;

  const permitTypeOptions = dataSource
    ? [
        ...new Set(
          dataSource
            .filter((property) => property.pmt_type) // Filter out null/undefined values
            .map((property) => property.pmt_type),
        ),
      ].map((type) => ({
        text: type,
        value: type,
      }))
    : [];
  console.log('permitTypeOptions', permitTypeOptions);
  if (columnType === 'original') {
    const columnsNationalOperators = [
      {
        title: 'Address',
        // dataIndex: ['properties', 'address'],
        dataIndex: 'address',
        width: 300,
        align: 'left',
        fixed: 'left',
        render: (text, record) => {
          const address = capitalize(
            text.includes(',')
              ? text.replace(/,(?! )/g, ', ') // find all comma without a following space, replace with comma and a spce
              : text +
                  ', ' +
                  record.standard_city +
                  ', ' +
                  record.standard_state,
          );

          const googleLink = `https://www.google.com/search?q=${address
            .trim()
            .replace(/,/g, '%2C')
            .replace(/ /g, '+')}`;

          const showImgIcon = props.currentMLSPropertiesFiltered.some(
            (mls) => mls.placekey === record.placekey,
          );

          return (
            <span>
              <a href={googleLink} className={styles.sfrLink} target="_blank">
                {address}
              </a>
              {showImgIcon && (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  width="16"
                  height="16"
                  className={styles.imageIconForAddress}
                >
                  <path d="M7.479 12.083h8.709l-2.626-3.937-2.208 3.146-1.729-2.188Zm-.646 3.5q-.895 0-1.541-.645-.646-.646-.646-1.542V3.375q0-.896.646-1.542.646-.645 1.541-.645h10.021q.917 0 1.552.645.636.646.636 1.542v10.021q0 .896-.636 1.542-.635.645-1.552.645Zm-3.687 3.688q-.917 0-1.552-.636Q.958 18 .958 17.083V4.875h2.188v12.208h12.208v2.188Z" />
                </svg>
              )}
            </span>
          );
        },
        // ellipsis: true,
      },
      {
        title: 'Dist.',
        // dataIndex: ['properties', 'address'],
        dataIndex: 'distance',
        key: 'distance',
        width: 100,
        align: 'center',
        render: (text, record) => {
          if (
            props.drawnCustomPolygons.length > 0 &&
            props.eventCoordinates.length > 0
          ) {
            const propertyPoint = point(record.geom.coordinates);
            const eventPoint = point(props.eventCoordinates);
            const distance = turf_distance(propertyPoint, eventPoint, 'miles');

            return distance.toFixed(1) + ' mi';
          }
          return (parseFloat(text) / 1609.34).toFixed(1) + ' mi';
        },
        sorter: (a, b) => {
          if (
            props.drawnCustomPolygons.length > 0 &&
            props.eventCoordinates.length > 0
          ) {
            const aPoint = point(a.geom.coordinates);
            const bPoint = point(b.geom.coordinates);
            const eventPoint = point(props.eventCoordinates);
            const adistance = turf_distance(aPoint, eventPoint, 'miles');
            const bdistance = turf_distance(bPoint, eventPoint, 'miles');

            return adistance - bdistance;
          }
          return a.distance - b.distance;
        },
        sortOrder:
          tableSorter.columnKey === 'distance' ? tableSorter.order : null,
      },
      {
        title: 'Cls.',
        // dataIndex: ['properties', 'available_date'],
        dataIndex: 'close_date',
        key: 'close_date',
        width: 100,
        align: 'center',
        render: (text) => {
          if (text) {
            return moment(text).format(dateFormat);
          } else {
            return '';
          }
        },
        sorter: (a, b) => {
          if (a.close_date && b.close_date) {
            return +moment(a.close_date) - +moment(b.close_date);
          } else {
            return a.close_date || b.close_date;
          }
        },
        sortOrder:
          tableSorter.columnKey === 'close_date' ? tableSorter.order : null,
      },
      {
        title: 'Status',
        // dataIndex: ['properties', 'exists'],
        // dataIndex: props.type === 'SFR' ? 'status' : 'exists',
        dataIndex: 'status',
        width: 100,
        align: 'left',
        // render: (text) =>
        //   props.type === 'SFR' ? text : text ? 'Available' : 'Closed',
      },
      // {
      //   title: 'Owner',
      //   // dataIndex: ['properties', 'brand'],
      //   dataIndex: 'brand',
      //   width: 100,
      //   align: 'left',
      //   render: (text) => showSFROperatorsFullName(text),
      //   // ellipsis: true,
      // },
      {
        title: 'Rent',
        // dataIndex: ['properties', 'rent'],
        dataIndex: 'rent',
        key: 'rent',
        width: 100,
        align: 'center',
        render: (text) => '$' + formatter(parseFloat(text).toFixed()),
        sorter: (a, b) => a.rent - b.rent,
        sortOrder: tableSorter.columnKey === 'rent' ? tableSorter.order : null,
      },

      {
        title: 'Last Sale Price',
        dataIndex: 'deed_last_sale_price',
        key: 'deed_last_sale_price',
        align: 'center',
        width: 100,
        render: (text) => {
          if (text) {
            if (text == '0') {
              return 'N/A';
            }
            return '$' + formatter(text);
          } else {
            return '';
          }
        },
        sorter: (a, b) => {
          if (a.deed_last_sale_price && b.deed_last_sale_price) {
            return +a.deed_last_sale_price - +b.deed_last_sale_price;
          } else if (a.deed_last_sale_price) {
            return -1;
          } else if (b.deed_last_sale_price) {
            return 1;
          }
        },
        sortOrder:
          tableSorter.columnKey === 'deed_last_sale_price'
            ? tableSorter.order
            : null,
      },
      {
        title: 'Last Sale Date',
        dataIndex: 'deed_last_sale_date',
        key: 'deed_last_sale_date',
        align: 'center',
        width: 100,
        render: (text) => {
          if (text) {
            return moment(text, dateFormat).format(dateFormat);
          } else {
            return '';
          }
        },
        sorter: (a, b) => {
          if (a.deed_last_sale_date && b.deed_last_sale_date) {
            return (
              +moment(a.deed_last_sale_date) - +moment(b.deed_last_sale_date)
            );
          } else if (a.deed_last_sale_date) {
            return -1;
          } else if (b.deed_last_sale_date) {
            return 1;
          }
        },
        sortOrder:
          tableSorter.columnKey === 'deed_last_sale_date'
            ? tableSorter.order
            : null,
      },
      {
        title: 'Type',
        dataIndex: 'propertysubtype',
        key: 'propertysubtype',
        width: 100,
        align: 'center',
        // sorter: (a, b) => sortString(a.propertysubtype, b.propertysubtype),
        filters: [
          ...dataSource.reduce((result, property) => {
            const alreadyExists = result.some(
              (r) => r.value === property.propertysubtype,
            );

            const textFormat = (text) => {
              if (['single family residential'].includes(text.toLowerCase()))
                return 'SFR';
              if (['single family residence'].includes(text.toLowerCase()))
                return 'SFR';
              if (['single family detached'].includes(text.toLowerCase()))
                return 'SFD';
              if (text === 'Townhouse') return 'TH';
              return text;
            };

            if (!alreadyExists && property.propertysubtype) {
              result.push({
                text: textFormat(property.propertysubtype),
                value: property.propertysubtype,
              });
            }
            return result;
          }, []),
        ],
        onFilter: (value, record) => record.propertysubtype === value,
        render: (text) => {
          if (text === null) {
            text = 'N/A';
          } else {
            if (
              text &&
              ['single family residential', 'single family residence'].includes(
                text.toLowerCase(),
              )
            ) {
              return 'SFR';
            } else if (
              text &&
              ['single family detached'].includes(text.toLowerCase())
            ) {
              return 'SFD';
            } else if (text === 'Townhouse') {
              return 'TH';
            } else {
              return text;
            }
          }
        },
        // sortDirections: ['ascend', 'descend'],

        ellipsis: true,
      },
      {
        title: 'Permit Type',
        // dataIndex: ['properties', 'rent'],
        dataIndex: 'pmt_type',
        key: 'pmt_type',
        width: 100,
        render: (text) => {
          return text;
        },
        filters: permitTypeOptions,
        onFilter: (value, record) => {
          // Add null check and handle empty values
          return record.pmt_type ? record.pmt_type.indexOf(value) === 0 : false;
        },
        // filters: () => {
        //   if (dataSource) {
        //     const filterOptions = [...new Set(dataSource.map(property => property.pmt_type))]
        //     .map(type => ({
        //       text: type,
        //       value: type
        //     }));
        //     console.log()
        //   return filterOptions;
        //   }
        //   else {
        //     return []
        //   }
        // },
      },
      {
        title: 'YrB',
        // dataIndex: ['properties', 'rent'],
        dataIndex: 'yearbuilt',
        key: 'yearbuilt',
        width: 100,
        align: 'center',
        // render: (text) => '$' + formatter(parseFloat(text).toFixed()),
        sorter: (a, b) => a.yearbuilt - b.yearbuilt,
        sortOrder:
          tableSorter.columnKey === 'yearbuilt' ? tableSorter.order : null,
      },
      {
        title: 'Bd',
        // dataIndex: ['properties', 'bed_rooms'],
        dataIndex: 'bed_rooms',
        key: 'bed_rooms',
        width: 100,
        align: 'center',
        sorter: (a, b) => a.bed_rooms - b.bed_rooms,
        sortOrder:
          tableSorter.columnKey === 'bed_rooms' ? tableSorter.order : null,
      },
      {
        title: 'Ba',
        // dataIndex: ['properties', 'bath_rooms'],
        dataIndex: 'bath_rooms',
        key: 'bath_rooms',
        width: 100,
        align: 'center',
        sorter: (a, b) => a.bath_rooms - b.bath_rooms,
        sortOrder:
          tableSorter.columnKey === 'bath_rooms' ? tableSorter.order : null,
      },
      {
        title: 'Sqft',
        // dataIndex: ['properties', 'square_feet'],
        dataIndex: 'square_feet',
        key: 'square_feet',
        width: 100,
        align: 'center',
        render: (text) => {
          if (text === null) {
            return '';
          } else {
            return formatter(text);
          }
        },
        sorter: (a, b) => a.square_feet - b.square_feet,
        sortOrder:
          tableSorter.columnKey === 'square_feet' ? tableSorter.order : null,
      },
      {
        title: 'Lot Size',
        dataIndex: 'area_acres',
        key: 'area_acres',
        align: 'center',
        width: 100,
        render: (text) => (text ? `${text.toFixed(2)} ac` : ''),
        sorter: (a, b) => a.area_acres - b.area_acres,
        sortOrder:
          tableSorter.columnKey === 'area_acres' ? tableSorter.order : null,
      },
      {
        title: 'CDOM',
        dataIndex: 'cdom',
        key: 'cdom',
        width: 100,
        align: 'center',
        sorter: (a, b) => a.cdom - b.cdom,
        sortOrder: tableSorter.columnKey === 'cdom' ? tableSorter.order : null,
      },
      // {
      //   title: 'Avail.',
      //   // dataIndex: ['properties', 'available_date'],
      //   dataIndex: 'available_date',
      //   // width: columnWidthSmall,
      //   align: 'center',
      //   render: (text) => {
      //     if (text) {
      //       return moment(text).format(dateFormat);
      //     } else {
      //       return '';
      //     }
      //   },
      //   sorter: (a, b) => {
      //     if (a.available_date && b.available_date) {
      //       return +moment(a.available_date) - +moment(b.available_date);
      //     } else {
      //       return a.available_date || b.available_date;
      //     }
      //   },
      // },

      {
        title: 'PSF',
        dataIndex: 'rent',
        key: 'psf',
        width: 100,
        align: 'center',
        render: (text, record) => {
          if (text && record.square_feet) {
            return '$' + (text / record.square_feet).toFixed(2);
          }
        },
        sorter: (a, b) => a.rent / a.square_feet - b.rent / b.square_feet,
        sortOrder: tableSorter.columnKey === 'psf' ? tableSorter.order : null,
      },
    ];
    // add owner column to SFR table
    // no owner column in HotPads table
    if (props.type === 'SFR') {
      const ownerColumn = {
        title: 'Owner',
        // dataIndex: ['properties', 'brand'],
        dataIndex: 'brand',
        width: 100,
        align: 'center',
        render: (text) => showSFROperatorsFullName(text),
        filters: [
          ...dataSource.reduce((result, property, idx) => {
            const alreadyExists = result.some(
              (r) => r.value === property.brand,
            );

            if (idx === 0) {
              result.push({
                text: 'N/A',
                value: 'N/A',
              });
            }

            if (
              !alreadyExists &&
              property.brand &&
              property.brand !== 'Rently'
            ) {
              result.push({
                value: property.brand,
                text: property.brand
                  ? showSFROperatorsFullName(property.brand) == ''
                    ? 'N/A'
                    : showSFROperatorsFullName(property.brand)
                  : 'N/A',
              });
            }
            return result;
          }, []),
        ],
        onFilter: (value, record) => {
          if (
            value === 'N/A' &&
            (!record.brand ||
              record.brand === 'Rently' ||
              showSFROperatorsFullName(record.brand) == '')
          )
            return true;
          return record.brand === value;
        },
        // ellipsis: true,
      };
      columnsNationalOperators.splice(3, 0, ownerColumn);
    }

    if (
      props.drawnCustomPolygons.length > 0 &&
      props.eventCoordinates.length === 0
    ) {
      columnsNationalOperators.splice(1, 1); // remove distance column
    }

    return columnsNationalOperators;
  } else if (columnType === 'minified') {
    const columnsNationalOperatorsMinified = [
      {
        title: 'Address',
        // dataIndex: ['properties', 'address'],
        dataIndex: 'address',
        width: 200,
        align: 'left',
        render: (text, record) => {
          const address = capitalize(
            text.includes(',')
              ? text.replace(/,(?! )/g, ', ') // find all comma without a following space, replace with comma and a spce
              : text +
                  ', ' +
                  record.standard_city +
                  ', ' +
                  record.standard_state,
          );

          const googleLink = `https://www.google.com/search?q=${address
            .trim()
            .replace(/,/g, '%2C')
            .replace(/ /g, '+')}`;

          return (
            <a href={googleLink} className={styles.sfrLink} target="_blank">
              {address}
            </a>
          );
        },
        // ellipsis: true,
      },
      {
        title: 'Rent',
        // dataIndex: ['properties', 'rent'],
        dataIndex: 'rent',
        key: 'rent',
        width: 100,
        align: 'center',
        render: (text) => '$' + formatter(parseFloat(text).toFixed()),
        sorter: (a, b) => a.rent - b.rent,
        sortOrder: tableSorter.columnKey === 'rent' ? tableSorter.order : null,
      },
      {
        title: 'Sqft',
        // dataIndex: ['properties', 'square_feet'],
        dataIndex: 'square_feet',
        key: 'square_feet',
        width: 100,
        align: 'center',
        render: (text) => {
          if (text === null) {
            return '';
          } else {
            return formatter(text);
          }
        },
        sorter: (a, b) => a.square_feet - b.square_feet,
        sortOrder:
          tableSorter.columnKey === 'square_feet' ? tableSorter.order : null,
      },
      {
        title: 'PSF',
        dataIndex: 'rent',
        key: 'psf',
        width: 100,
        align: 'center',
        render: (text, record) => {
          if (text && record.square_feet) {
            return '$' + (text / record.square_feet).toFixed(2);
          }
        },
        sorter: (a, b) => a.rent / a.square_feet - b.rent / b.square_feet,
        sortOrder: tableSorter.columnKey === 'psf' ? tableSorter.order : null,
      },
    ];
    return columnsNationalOperatorsMinified;
  }
};
