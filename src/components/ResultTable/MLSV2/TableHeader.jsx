// TableHeader.jsx
import styles from '@/components/ResultTable/resultTable.css';
import { formatter } from '@/utils/money';
import { SettingOutlined } from '@ant-design/icons';
import { Button, Col, Row, Switch, Tooltip } from 'antd';
import React, { useState } from 'react';
import ColumnManagerModal from '../ColumnManagerModal';

const TableHeader = ({
  searchingMode,
  selectedRowKeys,
  median,
  columns,
  defaultColumns,
  onColumnsChange,
  newConstructionOnly,
  onNewConstructionOnlyChange,
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);

  const handleOk = (updatedColumns) => {
    onColumnsChange(updatedColumns);
    setIsModalVisible(false);
  };

  return (
    <>
      <Row
        id="mlsTableHeader"
        key="table title row MLS"
        align="bottom"
        justify="space-between"
        wrap={true}
        gutter={[0, 8]}
        style={{ marginBottom: 12 }}
      >
        <Col key="table title" className={styles.cardTitleH2}>
          MLS
        </Col>
        <Col key="MLS summary row wrapper">
          <Row key="MLS summary row" align="middle" justify="end" gutter={24}>
            <Col
              key="new construction switch"
              style={{
                display: searchingMode === 'Sale' ? 'flex' : 'none',
                alignItems: 'center',
              }}
            >
              <span
                key="new construction only text"
                className={styles.cardSubtitle}
                style={{ marginRight: 8 }}
              >
                New Construction Only
              </span>
              <Switch
                size="small"
                checked={newConstructionOnly}
                onChange={onNewConstructionOnlyChange}
              />
            </Col>
            <Col key="total unit">
              <span
                key="total unit text"
                className={styles.cardSubtitle}
                style={{ marginRight: 8 }}
              >
                Total Selected
              </span>
              <span
                id="mlsTable-Total"
                key="total unit number"
                className={styles.cardDataValue}
              >
                {selectedRowKeys.length || '-'}
              </span>
            </Col>

            <Col key="MLS avg">
              <span
                key="MLS avg text"
                className={styles.cardSubtitle}
                style={{ marginRight: 8 }}
              >
                {searchingMode === 'Lease' ? 'Median Rent' : 'Median Sale'}
              </span>
              <span
                id="mlsTable-Median"
                key="MLS avg number"
                className={styles.cardDataValue}
              >
                {median ? '$' + formatter(median) : '-'}
              </span>
            </Col>
            <Col>
              <Tooltip title="Column Customization">
                <Button
                  icon={<SettingOutlined />}
                  onClick={() => setIsModalVisible(true)}
                ></Button>
              </Tooltip>
            </Col>
          </Row>
        </Col>
      </Row>
      <ColumnManagerModal
        visible={isModalVisible}
        columns={columns}
        defaultColumns={defaultColumns}
        onOk={handleOk}
        onCancel={() => setIsModalVisible(false)}
      />
    </>
  );
};

export default TableHeader;
