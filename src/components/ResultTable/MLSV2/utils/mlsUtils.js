// utils/mlsUtils.js
import {
  getCityCodeViaMetroName,
  getMetroNameForParam,
} from '@/utils/geography';

export const openMLSImageModal = (record, props) => {
  let key = record.listingkey;

  if (
    ['sanantonio', 'san antonio'].includes(record.metro.toLowerCase()) ||
    getMetroNameForParam(record, true) === 'sanantonio'
  ) {
    key = record.mlsid;
  }

  props.dispatch({
    type: 'CMA/getBatchMLSPropertyImages',
    payload: {
      key: key,
      city: getMetroNameForParam(record, true), // true for using realtrac instead of nashville
    },
  });

  props.dispatch({
    type: 'CMA/getHouseDetailsData',
    payload: {
      from: 'MLS Table',
      listingID: record.mlsid,
      cityCode: getCityCodeViaMetroName(record.metro),
    },
  });

  props.dispatch({
    type: 'CMA/getAPNOwnerName',
    payload: {
      from: 'MLS Table',
      placekey: record.placekey,
    },
  });

  props.dispatch({
    type: 'CMA/saveCMAStates',
    payload: {
      selectedMLProperty: record,
      openMLSImageModal: true,
    },
  });
};

// Utility function to format currency
export const formatter = (value) => {
  return new Intl.NumberFormat('en-US').format(Math.round(value));
};
