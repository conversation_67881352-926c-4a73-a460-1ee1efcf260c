// utils/columnConfig.js
import ImageDetailTooltip from '@/components/ImageDetailTooltip';
import styles from '@/components/ResultTable/resultTable.css';
import { showSFROperatorsFullName } from '@/components/SFRBrandConvertFunction';
import { formatter } from '@/utils/money';
import { capitalize, sortString } from '@/utils/strings';
import { default as turf_distance } from '@turf/distance';
import { point } from '@turf/helpers';
import moment from 'moment';
import { dateFormat } from '../../ResultTable';
// Generate columns for MLS data
export const getColumnsConfig = (
  props,
  columnType,
  dataSource,
  columnConfigs,
  imageDetailTooltipContainer,
) => {
  const leaseMode = props.searchingMode === 'Lease';
  const leaseSort = props.MLSTableSortLease;
  const saleSort = props.MLSTableSortSale;

  // For minified view (when map is expanded)
  if (columnType === 'minified') {
    return [
      {
        title: 'Address',
        dataIndex: 'fulladdress',
        key: 'fulladdress',
        width: 175,
        align: 'left',
        render: (text, record) => (
          <ImageDetailTooltip
            mlsProperty={record}
            customContainerRef={imageDetailTooltipContainer}
          >
            <span>
              {capitalize(text.replace(/ /g, ' ') + ', ' + record.city) +
                ', ' +
                record.stateorprovince +
                ' ' +
                record.zipcode}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                width="14"
                height="14"
                className={styles.imageIconForAddress}
              >
                <path d="M7.479 12.083h8.709l-2.626-3.937-2.208 3.146-1.729-2.188Zm-.646 3.5q-.895 0-1.541-.645-.646-.646-.646-1.542V3.375q0-.896.646-1.542.646-.645 1.541-.645h10.021q.917 0 1.552.645.636.646.636 1.542v10.021q0 .896-.636 1.542-.635.645-1.552.645Zm-3.687 3.688q-.917 0-1.552-.636Q.958 18 .958 17.083V4.875h2.188v12.208h12.208v2.188Z" />
              </svg>
            </span>
          </ImageDetailTooltip>
        ),
      },
      {
        title: leaseMode ? 'Rent' : 'Sold',
        dataIndex: 'latestPrice',
        key: 'latestPrice',
        width: 100,
        align: 'center',
        render: (text) => '$' + formatter(text),
        sorter: (a, b) => a.latestPrice - b.latestPrice,
        sortOrder:
          leaseMode && leaseSort.columnKey === 'latestPrice'
            ? leaseSort.order
            : !leaseMode && saleSort.columnKey === 'latestPrice'
            ? saleSort.order
            : null,
      },
      {
        title: 'Sqft',
        dataIndex: 'size',
        key: 'size',
        width: 100,
        align: 'center',
        render: (text) => (text ? formatter(text) : ''),
        sorter: (a, b) => a.size - b.size,
        sortOrder:
          leaseMode && leaseSort.columnKey === 'size'
            ? leaseSort.order
            : !leaseMode && saleSort.columnKey === 'size'
            ? saleSort.order
            : null,
      },
      {
        title: 'PSF',
        dataIndex: 'latestPrice',
        key: 'psf',
        width: 100,
        align: 'center',
        render: (text, record) => {
          if (text && record.size) {
            return '$' + (text / record.size).toFixed(2);
          }
        },
        sorter: (a, b) => a.latestPrice / a.size - b.latestPrice / b.size,
        sortOrder:
          leaseMode && leaseSort.columnKey === 'psf'
            ? leaseSort.order
            : !leaseMode && saleSort.columnKey === 'psf'
            ? saleSort.order
            : null,
      },
    ];
  }

  // For normal view (original columns)
  const columns = [
    {
      title: 'Address',
      dataIndex: 'fulladdress',
      key: 'fulladdress',
      width: 300,
      align: 'left',
      fixed: 'left',
      render: (text, record) => (
        <ImageDetailTooltip
          mlsProperty={record}
          customContainerRef={imageDetailTooltipContainer}
        >
          <span>
            {capitalize(text.replace(/ /g, ' ') + ', ' + record.city) +
              ', ' +
              record.stateorprovince +
              ' ' +
              record.zipcode +
              '  '}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              width="14"
              height="14"
              className={styles.imageIconForAddress}
            >
              <path d="M7.479 12.083h8.709l-2.626-3.937-2.208 3.146-1.729-2.188Zm-.646 3.5q-.895 0-1.541-.645-.646-.646-.646-1.542V3.375q0-.896.646-1.542.646-.645 1.541-.645h10.021q.917 0 1.552.645.636.646.636 1.542v10.021q0 .896-.636 1.542-.635.645-1.552.645Zm-3.687 3.688q-.917 0-1.552-.636Q.958 18 .958 17.083V4.875h2.188v12.208h12.208v2.188Z" />
            </svg>
          </span>
        </ImageDetailTooltip>
      ),
    },
    {
      title: 'Dist.',
      dataIndex: 'distance',
      key: 'distance',
      width: 100,
      align: 'center',
      render: (text, record) => {
        if (
          props.drawnCustomPolygons.length > 0 &&
          props.eventCoordinates.length > 0
        ) {
          const propertyPoint = point(record.geography.coordinates);
          const eventPoint = point(props.eventCoordinates);
          const distance = turf_distance(propertyPoint, eventPoint, 'miles');

          return distance.toFixed(1) + ' mi';
        }
        return (parseFloat(text) / 1609.34).toFixed(1) + ' mi';
      },
      sorter: (a, b) => {
        if (
          props.drawnCustomPolygons.length > 0 &&
          props.eventCoordinates.length > 0
        ) {
          const aPoint = point(a.geography.coordinates);
          const bPoint = point(b.geography.coordinates);
          const eventPoint = point(props.eventCoordinates);
          const adistance = turf_distance(aPoint, eventPoint, 'miles');
          const bdistance = turf_distance(bPoint, eventPoint, 'miles');

          return adistance - bdistance;
        }
        return a.distance - b.distance;
      },
      sortOrder:
        leaseMode && leaseSort.columnKey === 'distance'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'distance'
          ? saleSort.order
          : null,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      align: 'center',
      filters: [
        { text: 'Active', value: 'Active' },
        { text: 'Closed', value: 'Closed' },
        { text: 'Pending', value: 'Pending' },
      ],
      onFilter: (value, record) => record.status === value,
      filterMultiple: true,
    },
    {
      title: 'SFR Owner',
      dataIndex: 'sfr_owner',
      key: 'sfr_owner',
      width: 100,
      align: 'center',
      render: (text) => (text ? showSFROperatorsFullName(text) : ''),
      filters: [
        ...dataSource.reduce((result, property, idx) => {
          const exists = result.find((r) => r.value === property.sfr_owner);

          if (idx === 0) {
            result.push({
              text: 'N/A',
              value: 'N/A',
            });
          }

          if (
            !exists &&
            property.sfr_owner &&
            property.sfr_owner !== 'Rently'
          ) {
            result.push({
              value: property.sfr_owner,
              text: property.sfr_owner
                ? showSFROperatorsFullName(property.sfr_owner) == ''
                  ? 'N/A'
                  : showSFROperatorsFullName(property.sfr_owner)
                : 'N/A',
            });
          }
          return result;
        }, []),
      ],
      onFilter: (value, record) => {
        if (
          value === 'N/A' &&
          (!record.sfr_owner ||
            record.sfr_owner === 'Rently' ||
            showSFROperatorsFullName(record.sfr_owner) == '')
        )
          return true;
        return record.sfr_owner === value;
      },
    },
    {
      title: leaseMode ? 'Rent' : 'Latest Sold',
      dataIndex: 'latestPrice',
      key: 'latestPrice',
      width: 100,
      align: 'center',
      render: (text) => '$' + formatter(text),
      sorter: (a, b) => a.latestPrice - b.latestPrice,
      sortOrder:
        leaseMode && leaseSort.columnKey === 'latestPrice'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'latestPrice'
          ? saleSort.order
          : null,
    },
    {
      title: 'Last Rent',
      dataIndex: 'last_rent_info',
      key: 'last_rent_info',
      width: 140,
      align: 'center',
      render: (last_rent_info) => {
        if (!last_rent_info) return '-';

        // Parse the rent info string
        const rentEntries = last_rent_info.split(', ').map((entry) => {
          const [amount, date] = entry.split(' - ');
          return { amount: parseFloat(amount), date };
        });

        // Sort by date (most recent first)
        rentEntries.sort((a, b) => new Date(b.date) - new Date(a.date));

        // Filter entries older than 1 year
        const oneYearAgo = new Date();
        oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);

        const filteredEntries = rentEntries.filter(
          (entry) => new Date(entry.date) <= oneYearAgo,
        );

        // Show only the most recent entry if it's older than 1 year
        const entryToShow =
          filteredEntries.length > 0 ? filteredEntries[0] : null;

        if (!entryToShow) return '-';

        return (
          <div style={{ textAlign: 'left' }}>
            <div
              style={{
                fontSize: '12px',
                lineHeight: '1.4',
              }}
            >
              <span style={{ fontWeight: 'bold' }}>
                ${formatter(entryToShow.amount)} |{' '}
              </span>
              <span style={{ color: '#666' }}>{entryToShow.date}</span>
            </div>
          </div>
        );
      },
    },

    {
      title: 'Last Sale Price',
      dataIndex: 'deed_last_sale_price',
      key: 'deed_last_sale_price',
      align: 'center',
      width: 100,
      render: (text) => {
        if (text) {
          if (text == '0') {
            return 'N/A';
          }
          return '$' + formatter(text);
        } else {
          return '';
        }
      },
      sorter: (a, b) => {
        if (a.deed_last_sale_price && b.deed_last_sale_price) {
          return +a.deed_last_sale_price - +b.deed_last_sale_price;
        } else if (a.deed_last_sale_price) {
          return -1;
        } else if (b.deed_last_sale_price) {
          return 1;
        }
      },
      sortOrder:
        leaseMode && leaseSort.columnKey === 'deed_last_sale_price'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'deed_last_sale_price'
          ? saleSort.order
          : null,
    },
    {
      title: 'Last Sale Date',
      dataIndex: 'deed_last_sale_date',
      key: 'deed_last_sale_date',
      align: 'center',
      width: 100,
      render: (text) => {
        if (text) {
          return moment(text, dateFormat).format(dateFormat);
        } else {
          return '';
        }
      },
      sorter: (a, b) => {
        if (a.deed_last_sale_date && b.deed_last_sale_date) {
          return (
            +moment(a.deed_last_sale_date) - +moment(b.deed_last_sale_date)
          );
        } else if (a.deed_last_sale_date) {
          return -1;
        } else if (b.deed_last_sale_date) {
          return 1;
        }
      },
      sortOrder:
        leaseMode && leaseSort.columnKey === 'deed_last_sale_date'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'deed_last_sale_date'
          ? saleSort.order
          : null,
    },
    {
      title: 'Chg. $(%)',
      showSorterTooltip: {
        title: 'Total cumulative change since original listing',
      },
      dataIndex: 'latestPrice',
      key: 'change',
      align: 'center',
      width: 100,
      render: (text, record) => {
        const originalPrice = record.orginalprice;
        const latestPrice = record.latestPrice;
        const change = latestPrice - originalPrice;
        const changePercent = Math.round((change / originalPrice) * 100);

        if (change && change > 0 && isFinite(changePercent)) {
          return (
            <span style={{ color: '#3f8600' }}>
              +${formatter(change)}
              <br />
              (+{changePercent}%)
            </span>
          );
        } else if (change && change < 0 && isFinite(changePercent)) {
          return (
            <span style={{ color: '#cf1322' }}>
              -${formatter(Math.abs(change))}
              <br />({changePercent}%)
            </span>
          );
        } else {
          return <span>$0{'(0%)'}</span>;
        }
      },
      sorter: (a, b) => {
        const aChange = a.latestPrice - a.orginalprice;
        const bChange = b.latestPrice - b.orginalprice;
        return aChange - bChange;
      },
      sortOrder:
        leaseMode && leaseSort.columnKey === 'change'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'change'
          ? saleSort.order
          : null,
    },
    {
      title: 'Type',
      dataIndex: 'propertysubtype',
      key: 'propertysubtype',
      width: 100,
      align: 'center',
      filters: [
        ...dataSource.reduce((result, property) => {
          const exists = result.find(
            (r) => r.value === property.propertysubtype,
          );

          const textFormat = (text) => {
            if (['Single Family Residence'].includes(text))
              return 'Single Family';
            if (['Single Family Detached'].includes(text))
              return 'Single Family (Detached)';
            if (text === 'Townhouse') return 'TH';
            return text;
          };

          if (!exists && property.propertysubtype) {
            result.push({
              text: textFormat(property.propertysubtype),
              value: property.propertysubtype,
            });
          }
          return result;
        }, []),
      ],
      onFilter: (value, record) => record.propertysubtype === value,
      render: (text) => {
        if (['Single Family Residence'].includes(text)) {
          return 'Single Family';
        } else if (['Single Family Detached'].includes(text)) {
          return 'Single Family (Detached)';
        } else if (text === 'Townhouse') {
          return 'TH';
        } else {
          return text;
        }
      },
      sortOrder:
        leaseMode && leaseSort.columnKey === 'propertysubtype'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'propertysubtype'
          ? saleSort.order
          : null,
      ellipsis: true,
    },
    {
      title: 'YrB',
      dataIndex: 'yearbuilt',
      key: 'yearbuilt',
      width: 100,
      align: 'center',
      sorter: (a, b) => a.yearbuilt - b.yearbuilt,
      sortOrder:
        leaseMode && leaseSort.columnKey === 'yearbuilt'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'yearbuilt'
          ? saleSort.order
          : null,
    },
    {
      title: 'Bd',
      dataIndex: 'bed',
      key: 'bed',
      width: 100,
      align: 'center',
      sorter: (a, b) => a.bed - b.bed,
      sortOrder:
        leaseMode && leaseSort.columnKey === 'bed'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'bed'
          ? saleSort.order
          : null,
    },
    {
      title: 'Ba',
      dataIndex: 'bath',
      key: 'bath',
      width: 100,
      align: 'center',
      sorter: (a, b) => a.bath - b.bath,
      sortOrder:
        leaseMode && leaseSort.columnKey === 'bath'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'bath'
          ? saleSort.order
          : null,
    },
    {
      title: 'Sqft',
      dataIndex: 'size',
      key: 'size',
      width: 100,
      align: 'center',
      render: (text) => (text ? formatter(text) : ''),
      sorter: (a, b) => a.size - b.size,
      sortOrder:
        leaseMode && leaseSort.columnKey === 'size'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'size'
          ? saleSort.order
          : null,
    },
    {
      title: 'Lot Size',
      dataIndex: 'area_acres',
      key: 'area_acres',
      align: 'center',
      width: 100,
      render: (text) => (text ? `${text.toFixed(2)} ac` : ''),
      sorter: (a, b) => a.area_acres - b.area_acres,
      sortOrder:
        leaseMode && leaseSort.columnKey === 'area_acres'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'area_acres'
          ? saleSort.order
          : null,
    },
    {
      title: 'CDOM',
      dataIndex: 'cdom',
      key: 'cdom',
      width: 100,
      align: 'center',
      sorter: (a, b) => a.cdom - b.cdom,
      sortOrder:
        leaseMode && leaseSort.columnKey === 'cdom'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'cdom'
          ? saleSort.order
          : null,
    },
    {
      title: 'Cls.',
      dataIndex: 'closedate',
      key: 'closedate',
      width: 100,
      align: 'center',
      render: (text) => {
        if (text) {
          return moment(text).format(dateFormat);
        } else {
          return '';
        }
      },
      sorter: (a, b) => {
        if (a.closedate && b.closedate) {
          return +moment(a.closedate) - +moment(b.closedate);
        } else {
          return a.closedate || b.closedate;
        }
      },
      sortOrder:
        leaseMode && leaseSort.columnKey === 'closedate'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'closedate'
          ? saleSort.order
          : null,
    },
    {
      title: 'PSF',
      dataIndex: 'latestPrice',
      key: 'psf',
      width: 100,
      align: 'center',
      render: (text, record) => {
        if (text && record.size) {
          return '$' + (text / record.size).toFixed(2);
        }
      },
      sorter: (a, b) => a.latestPrice / a.size - b.latestPrice / b.size,
      sortOrder:
        leaseMode && leaseSort.columnKey === 'psf'
          ? leaseSort.order
          : !leaseMode && saleSort.columnKey === 'psf'
          ? saleSort.order
          : null,
    },
  ];

  // Remove distance column if no custom polygons
  if (
    props.drawnCustomPolygons.length > 0 &&
    props.eventCoordinates.length === 0
  ) {
    columns.splice(1, 1);
  }

  return columns;
};
