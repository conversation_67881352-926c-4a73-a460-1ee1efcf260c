// MLS.jsx
import ImageDetailTooltip from '@/components/ImageDetailTooltip';
import styles from '@/components/ResultTable/resultTable.css';
import { useElementSize } from '@/hooks';
import {
  getUserTableColumnSettings,
  postSaveUserTableColumnSettings,
} from '@/services/data';
import { arrayDifference } from '@/utils/arrayMethods';
import { capitalize } from '@/utils/strings';
import { useAuthenticator } from '@aws-amplify/ui-react';
import { Col, Row, Switch, Table } from 'antd';
import isEmpty from 'lodash.isempty';
import isEqual from 'lodash.isequal';
import { useEffect, useRef, useState } from 'react';
import { connect } from 'umi';
import TableHeader from './TableHeader';
import { getColumnsConfig } from './utils/columnConfig';
import { formatter, openMLSImageModal } from './utils/mlsUtils';
export let selectedRowKey;
export let rentAverageMLS;
let imageDetailTooltipContainer = null;

const MLSV2 = connect(({ CMA }) => ({
  subjectPropertyParcelData: CMA.subjectPropertyParcelData,
  currentMLSPropertiesFiltered: CMA.currentMLSPropertiesFiltered,
  searchingMode: CMA.searchingMode,
  mapExpandedView: CMA.mapExpandedView,
  openMLSImageModal: CMA.openMLSImageModal,
  userGroup: CMA.userGroup,
  MLSTableSortLease: CMA.MLSTableSortLease,
  MLSTableSortSale: CMA.MLSTableSortSale,
  mlsTableFilters: CMA.mlsTableFilters,
  drawnCustomPolygons: CMA.drawnCustomPolygons,
  eventCoordinates: CMA.eventCoordinates,
  mapLocateProperty: CMA.mapLocateProperty,
  cmaTabKey: CMA.cmaTabKey,
  compingMode: CMA.compingMode,
  fetchCompMLSDone: CMA.fetchCompMLSDone,
  selectedRowKeysMLSLease: CMA.selectedRowKeysMLSLease,
  selectedRowKeysMLSSale: CMA.selectedRowKeysMLSSale,
  mlsSaleNewConstructionOnly: CMA.mlsSaleNewConstructionOnly,
  mlsLeaseColumnSettings: CMA.mlsLeaseColumnSettings,
  mlsSaleColumnSettings: CMA.mlsSaleColumnSettings,
  user: CMA.user,
}))(function (props) {
  const mlsContainer = useRef(null);
  const mlsTooltip = useRef(null);
  imageDetailTooltipContainer = useRef(null);
  const { user } = useAuthenticator();
  const tableSize = useElementSize(mlsContainer);

  const [scrollToSelectedRow, setScrollToSelectedRow] = useState(false);
  const [previousMLSPropertiesFiltered, setPreviousMLSPropertiesFiltered] =
    useState([]);
  const [prevSubjectPropertyParcelData, setPrevSubjectPropertyParcelData] =
    useState(props.subjectPropertyParcelData);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [currentTableData, setCurrentTableData] = useState([]);
  const [unselectedRowKeysLease, setUnselectedRowKeysLease] = useState([]);
  const [unselectedRowKeysSales, setUnselectedRowKeysSales] = useState([]);
  const [scrollX, setScrollX] = useState(2000);

  // Define default columns
  const DEFAULT_MLS_COLUMNS = [
    { title: 'Address', key: 'fulladdress', visible: true },
    { title: 'Dist.', key: 'distance', visible: true },
    { title: 'Status', key: 'status', visible: true },
    { title: 'SFR Owner', key: 'sfr_owner', visible: true },

    {
      title: props.searchingMode === 'Lease' ? 'Rent' : 'Latest Sold',
      key: 'latestPrice',
      visible: true,
    },
    { title: 'Last Sale Price', key: 'deed_last_sale_price', visible: true },
    { title: 'Last Sale Date', key: 'deed_last_sale_date', visible: true },
    { title: 'Chg. $(%)', key: 'change', visible: true },

    { title: 'Type', key: 'propertysubtype', visible: true },
    { title: 'YrB', key: 'yearbuilt', visible: true },
    { title: 'Bd', key: 'bed', visible: true },
    { title: 'Ba', key: 'bath', visible: true },
    { title: 'Sqft', key: 'size', visible: true },
    { title: 'Lot Size', key: 'area_acres', visible: true },
    { title: 'CDOM', key: 'cdom', visible: true },
    { title: 'Cls.', key: 'closedate', visible: true },
    { title: 'PSF', key: 'psf', visible: true },
    { title: 'Last Rent', key: 'last_rent_info', visible: true },
  ];

  // State for column configurations
  const [columnConfigs, setColumnConfigs] = useState(
    props.searchingMode === 'Lease'
      ? props.mlsLeaseColumnSettings || DEFAULT_MLS_COLUMNS
      : props.mlsSaleColumnSettings || DEFAULT_MLS_COLUMNS,
  );

  // Fetch user table column settings
  const fetchUserTableColumnSettings = async () => {
    try {
      if (!user?.username) return;

      const data = await getUserTableColumnSettings({
        username: user.username,
      });

      if (data.mlsLease) {
        props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            mlsLeaseColumnSettings: Array.isArray(data.mlsLease)
              ? data.mlsLease
              : JSON.parse(data.mlsLease),
          },
        });
      }

      if (data.mlsSale) {
        props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            mlsSaleColumnSettings: Array.isArray(data.mlsSale)
              ? data.mlsSale
              : JSON.parse(data.mlsSale),
          },
        });
      }
    } catch (err) {
      console.error('Error loading MLS column settings:', err);
    }
  };

  useEffect(() => {
    if (props.searchingMode === 'Lease') {
      if (props.mlsLeaseColumnSettings) {
        setColumnConfigs(
          Array.isArray(props.mlsLeaseColumnSettings)
            ? props.mlsLeaseColumnSettings
            : JSON.parse(props.mlsLeaseColumnSettings),
        );
      }
    } else if (props.searchingMode === 'Sale') {
      if (props.mlsSaleColumnSettings) {
        setColumnConfigs(
          Array.isArray(props.mlsSaleColumnSettings)
            ? props.mlsSaleColumnSettings
            : JSON.parse(props.mlsSaleColumnSettings),
        );
      }
    }
  }, [props.mlsLeaseColumnSettings, props.mlsSaleColumnSettings, props.searchingMode]);

  // Fetch settings on mount or when username changes
  useEffect(() => {
    fetchUserTableColumnSettings();
  }, [user?.username]);

  // Save user settings
  const saveUserSettings = async (updatedColumns) => {
    try {
      if (!user?.username) return;

      // Use different table names based on search mode
      const tableName =
        props.searchingMode === 'Lease' ? 'mls_lease' : 'mls_sale';

      const response = await postSaveUserTableColumnSettings({
        body: {
          username: user.username,
          tableName: tableName,
          settings: JSON.stringify(updatedColumns),
        },
      });

      // Save to the appropriate state property based on search mode
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          ...(props.searchingMode === 'Lease'
            ? { mlsLeaseColumnSettings: updatedColumns }
            : { mlsSaleColumnSettings: updatedColumns }),
        },
      });
    } catch (error) {
      console.error('Error saving MLS column settings:', error);
    }
  };

  // Update scrollX based on visible columns
  useEffect(() => {
    const columns = getGeneratedColumns();
    const totalWidth = columns.reduce(
      (sum, col) => sum + (col.width || 100),
      0,
    );
    setScrollX(Math.max(totalWidth, 1600));
  }, [columnConfigs, props.mapExpandedView]);

  // sort the table data based on MLSTableSortLease or MLSTableSortSale
  const getInitialTableData = () => {
    if (props.searchingMode === 'Lease') {
      if (props.MLSTableSortLease.order === 'ascend') {
        return props.currentMLSPropertiesFiltered.sort(
          (a, b) =>
            a[props.MLSTableSortLease.columnKey] -
            b[props.MLSTableSortLease.columnKey],
        );
      } else {
        return props.currentMLSPropertiesFiltered.sort(
          (a, b) =>
            b[props.MLSTableSortLease.columnKey] -
            a[props.MLSTableSortLease.columnKey],
        );
      }
    } else if (props.searchingMode === 'Sale') {
      if (props.MLSTableSortSale.order === 'ascend') {
        return props.currentMLSPropertiesFiltered.sort(
          (a, b) =>
            a[props.MLSTableSortSale.columnKey] -
            b[props.MLSTableSortSale.columnKey],
        );
      } else {
        return props.currentMLSPropertiesFiltered.sort(
          (a, b) =>
            b[props.MLSTableSortSale.columnKey] -
            a[props.MLSTableSortSale.columnKey],
        );
      }
    }
  };

  // get current table data and keep selection
  const getCurrentTableData = () => {
    const allMLSCompsDataSorted = getInitialTableData();

    // remove filters that are null
    const nonNullFilters = Object.fromEntries(
      Object.entries(props.mlsTableFilters).filter(
        ([_, value]) => value !== null,
      ),
    );

    // filter the data based on current filters
    const currentTableData = allMLSCompsDataSorted.filter((row) => {
      return Object.keys(nonNullFilters).every((filter) => {
        return nonNullFilters[filter].includes(row[filter]);
      });
    });

    // remove unselected rows from all row keys
    const unselectedRowKeys =
      props.searchingMode === 'Lease'
        ? unselectedRowKeysLease
        : unselectedRowKeysSales;

    const selectedRowKeys = arrayDifference(
      currentTableData.map((row) => row.mlsid),
      unselectedRowKeys,
    );

    return { currentTableData, selectedRowKeys };
  };

  // clear unselected row keys for new subject property
  if (
    !isEqual(props.subjectPropertyParcelData, prevSubjectPropertyParcelData)
  ) {
    setPrevSubjectPropertyParcelData(props.subjectPropertyParcelData);
    setUnselectedRowKeysLease([]);
    setUnselectedRowKeysSales([]);
  }

  // after data changes, get current table data and selected row keys
  if (
    !isEqual(props.currentMLSPropertiesFiltered, previousMLSPropertiesFiltered)
  ) {
    const { currentTableData, selectedRowKeys } = getCurrentTableData();
    console.log('MLS selectedRowKeys', selectedRowKeys);
    setCurrentTableData(currentTableData);
    props.onSelectChangeMLS(selectedRowKeys);
    setPreviousMLSPropertiesFiltered(props.currentMLSPropertiesFiltered);
  }

  selectedRowKey = props.selectedRowKeysMLS;
  rentAverageMLS = props.rentAverageMLS;

  // Generate columns based on column configuration and view mode
  const getGeneratedColumns = () => {
    const allColumns = getColumnsConfig(
      props,
      props.mapExpandedView ? 'minified' : 'original',
      props.currentMLSPropertiesFiltered,
      columnConfigs,
      imageDetailTooltipContainer,
    );

    return props.mapExpandedView
      ? allColumns
      : allColumns
          .filter((col) => {
            const config = columnConfigs.find((c) => c.key === col.key);
            return config?.visible;
          })
          .sort((a, b) => {
            const aIndex = columnConfigs.findIndex((c) => c.key === a.key);
            const bIndex = columnConfigs.findIndex((c) => c.key === b.key);
            return aIndex - bIndex;
          });
  };

  useEffect(() => {
    const click = (e) => {
      if (e.target.id === 'locatePropertyButton' || !mlsContainer.current)
        return;

      const row = mlsContainer.current.querySelector(
        `.${styles.mapLocatePropertySelected}`,
      );
      if (row) {
        row.classList.remove(styles.mapLocatePropertySelected);
        props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: { mapLocateProperty: {} },
        });
      }
    };

    document.addEventListener('click', click);
    return () => {
      document.removeEventListener('click', click);
    };
  }, []);

  useEffect(() => {
    if (scrollToSelectedRow && mlsContainer.current) {
      const row = mlsContainer.current.querySelector(
        `.${styles.mapLocatePropertySelected}`,
      );

      if (row) {
        row.scrollIntoView({ behavior: 'smooth', block: 'center' });
        setScrollToSelectedRow(false);
      }
    }
  }, [scrollToSelectedRow]);

  useEffect(() => {
    if (
      !isEmpty(props.mapLocateProperty) &&
      props.mapLocateProperty.type === 'MLS'
    ) {
      const rowData = currentTableData.find(
        (property) => property.mlsid === props.mapLocateProperty.id,
      );

      if (rowData) {
        const rowIndex = currentTableData.indexOf(rowData);
        const rowPage = Math.floor(rowIndex / pageSize) + 1;
        setCurrentPage(rowPage);

        if (props.cmaTabKey !== '1') {
          props.dispatch({
            type: 'CMA/saveCMAStates',
            payload: { cmaTabKey: '1' },
          });
        }

        setTimeout(() => {
          setScrollToSelectedRow(true);
        }, 100);
      }
    }
  }, [props.mapLocateProperty]);

  const onRowMLS = (record) => {
    return {
      onClick: (event) => {
        openMLSImageModal(record, props);
      },
      onMouseEnter: (event) => {
        if (
          !isEqual(
            props.currentHighlightCoordinates,
            record.geography.coordinates,
          )
        ) {
          props.dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              currentHighlightCoordinates: record.geography.coordinates,
              priceHighlightMarker: record.latestPrice,
              typeHighlightMarker: 'MLS',
            },
          });
        }

        if (!props.mapExpandedView) return;

        const { status, propertytype, yearbuilt, bed, bath, cdom, closedate } =
          record;
        const distance =
          (parseFloat(record.distance) / 1609.34).toFixed(1) + ' mi';
        const tooltipMessage = `Distance: ${distance}\nStatus: ${status}\nProperty Type: ${propertytype}\nYear Built:${yearbuilt}\nBeds: ${bed}\bBaths: ${bath}\nDOM: ${cdom}\nClose Date: ${closedate}`;

        mlsTooltip.current.innerText = tooltipMessage;
        mlsTooltip.current.style.display = 'block';

        const containerPos = mlsContainer.current.getBoundingClientRect();
        const rowPos = event.currentTarget.getBoundingClientRect();

        mlsTooltip.current.style.top = `${
          rowPos.top - containerPos.top - mlsTooltip.current.clientHeight + 225
        }px`;
        mlsTooltip.current.style.left = '50%';
        mlsTooltip.current.style.transform = 'translateX(-50%)';
      },
      onMouseLeave: (event) => {
        if (!props.mapExpandedView) return;
        mlsTooltip.current.innerText = '';
        mlsTooltip.current.style.display = 'none';
      },
    };
  };

  const onChangeNewConstructionOnly = (checked) => {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        mlsSaleNewConstructionOnly: checked,
      },
    });
    props.dispatch({
      type: 'CMA/filterMLSAndSFRDataManual',
      payload: {
        dataSourceType: 'MLS',
      },
    });
  };

  return (
    <div
      ref={mlsContainer}
      key="MLS card"
      className={styles.cardWrapperMLS}
      style={{ marginTop: '16px' }}
    >
      <Table
        key="mls table"
        className="table-sticky-title-header"
        rowKey={(record) => record.mlsid}
        columns={getGeneratedColumns()}
        dataSource={props.currentMLSPropertiesFiltered}
        variant={'filled'}
        size="small"
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          onChange: (page, pageSize) => {
            setCurrentPage(page);
            setPageSize(pageSize);
          },
        }}
        loading={!props.fetchCompMLSDone}
        rowSelection={{
          selectedRowKeys: props.selectedRowKeysMLS,
          onChange: (selectedRowKeys) => {
            const unselectedRowKeys = arrayDifference(
              currentTableData.map((row) => row.mlsid),
              selectedRowKeys,
            );
            if (props.searchingMode === 'Lease') {
              setUnselectedRowKeysLease(unselectedRowKeys);
            } else if (props.searchingMode === 'Sale') {
              setUnselectedRowKeysSales(unselectedRowKeys);
            }
            props.onSelectChangeMLS(selectedRowKeys);
          },
          selections: [
            Table.SELECTION_ALL,
            Table.SELECTION_INVERT,
            Table.SELECTION_NONE,
          ],
          getCheckboxProps: (record) => ({
            disabled: props.compingMode === 'intelligentComping',
          }),
        }}
        rowClassName={(record, index) => {
          let className = styles.propertyDataTableRow;
          if (
            !isEmpty(props.mapLocateProperty) &&
            props.mapLocateProperty.type === 'MLS'
          ) {
            if (record.mlsid === props.mapLocateProperty.id) {
              className += ' ' + styles.mapLocatePropertySelected;
            }
          }
          return className;
        }}
        onRow={onRowMLS}
        sticky={true}
        components={{
          header: {
            wrapper: ({ className, children }) => {
              return (
                <thead className={className}>
                  <div style={{ width: tableSize.width - 12 }}>
                    <TableHeader
                      searchingMode={props.searchingMode}
                      selectedRowKeys={props.selectedRowKeysMLS}
                      columns={columnConfigs}
                      onColumnsChange={saveUserSettings}
                      defaultColumns={DEFAULT_MLS_COLUMNS}
                      median={props.rentMedianMLS}
                      newConstructionOnly={props.mlsSaleNewConstructionOnly}
                      onNewConstructionOnlyChange={onChangeNewConstructionOnly}
                    />
                  </div>
                  {children}
                </thead>
              );
            },
          },
        }}
        scroll={props.mapExpandedView ? null : { x: scrollX }}
        onChange={(pagination, filters, sorter, extra) => {
          if (!isEqual(extra.currentDataSource, currentTableData)) {
            setCurrentTableData(extra.currentDataSource);
          }

          switch (extra.action) {
            case 'sort':
              props.dispatch({
                type: 'CMA/saveCMAStates',
                payload: {
                  ...(props.searchingMode === 'Lease'
                    ? { MLSTableSortLease: sorter }
                    : { MLSTableSortSale: sorter }),
                },
              });
              break;
            case 'filter':
              const allRowKeys = extra.currentDataSource.map(
                (item) => item.mlsid,
              );
              const unselectedRowKeys =
                props.searchingMode === 'Lease'
                  ? unselectedRowKeysLease
                  : unselectedRowKeysSales;
              const selectedRowKeys = arrayDifference(
                allRowKeys,
                unselectedRowKeys,
              );
              props.dispatch({
                type: 'CMA/saveCMAStates',
                payload: {
                  mlsTableFilters: filters,
                  ...(props.searchingMode === 'Lease'
                    ? { selectedRowKeysMLSLease: selectedRowKeys }
                    : { selectedRowKeysMLSSale: selectedRowKeys }),
                },
              });
              break;
          }
        }}
        summary={(currentData) => {
          return (
            <Table.Summary fixed>
              <Table.Summary.Row>
                <Table.Summary.Cell colSpan={props.mapExpandedView ? 2 : 5}>
                  {props.searchingMode === 'Lease'
                    ? 'Median Rent of Selected'
                    : 'Median Sale of Selected'}
                </Table.Summary.Cell>
                <Table.Summary.Cell
                  className={styles.tableSummaryCellTextAlignCenter}
                >
                  ${props.rentMedianMLS ? formatter(props.rentMedianMLS) : '-'}
                </Table.Summary.Cell>
              </Table.Summary.Row>
            </Table.Summary>
          );
        }}
      />
      <div ref={mlsTooltip} className={styles.customToolTip}></div>
      <div
        ref={imageDetailTooltipContainer}
        className="image-detail-tooltip-container"
      ></div>
    </div>
  );
});

export default MLSV2;
