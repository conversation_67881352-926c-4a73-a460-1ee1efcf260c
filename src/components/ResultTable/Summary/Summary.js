import GeneratePDF from '@/components/GeneratePDF/GeneratePDF';
import styles from '@/components/ResultTable/resultTable.css';
import { formatter } from '@/utils/money';
import {
  Button,
  Col,
  Divider,
  Radio,
  Row,
  Segmented,
  Select,
  Switch,
  Tooltip,
  message,
} from 'antd';
import isEmpty from 'lodash.isempty';
import moment from 'moment';
import { connect } from 'umi';
import { getCityCodeViaZIPCode } from '../../../utils/geography';
import EditAddress from '../../EditAddress/EditAddress';
import filterValuesDefault from '../../Filters/filterValuesDefault.json';
import SavedFilters from '../../Filters/SavedFilters';
import InsightsPDF from '../../GeneratePDF/InsightsPDF';
import ShareSubjectPropertyButton from '../ShareSubjectPropertyButton';
import PropertyImageAndHouseDetail from './PropertyImageAndHouseDetail';

import { useEffect } from 'react';
import useWithinClientSubscription from '../../../hooks/useWithinClientSubscription';
import { getLastSoldPublicRecord, serverType } from '../../../services/data';
import { formatCurrency } from '../../../utils/lastSalePublicRecord';
import { capitalize, doesStringMatchAddress } from '../../../utils/strings';
import BookmarkButton from '../../Bookmarks/BookmarkButton';
import TaxDetailsModal from '../TaxDetails';
const dateFormat = 'YYYY-MM-DD';

export let rentAverageNationalOperators;
export let rentAveragePerSqftNationalOperators;
export let rentAverageHotPads;
export let rentAveragePerSqftHotPads;
export let rentAverageMLS;
export let rentAveragePerSqftMLS;
export let rentAverageAll;
export let rentAveragePerSqftAll;
export let rentAdjustedBT;
export let MLSAverageToAVMRatio;

const NON_DISCLOSURE_STATES = [
  'Alaska',
  'Idaho',
  'Kansas',
  'Mississippi',
  'Louisiana',
  'Wyoming',
  'Utah',
  'North Dakota',
  'New Mexico',
  'Montana',
  'Texas',
];

const findLastLotIndex = (strArr) => {
  const lastLT = strArr.lastIndexOf('LT');
  const lastLTS = strArr.lastIndexOf('LTS');
  const lastLOT = strArr.lastIndexOf('LOT');
  const lastLOTS = strArr.lastIndexOf('LOTS');

  return Math.max(lastLT, lastLTS, lastLOT, lastLOTS);
};

const findFirstINTVOLIndex = (str) => {
  const int = str.indexOf('INT');
  const vol = str.indexOf('VOL');

  if (int != -1 && vol === -1) return int;
  if (vol != -1 && int === -1) return vol;

  return Math.min(int, vol);
};

export const legalDescriptionTrimmer = (str) => {
  if (!str) return;
  const strSplit = str.split(' ');
  const lotIndex = findLastLotIndex(strSplit);

  if (lotIndex != -1) {
    if (strSplit[lotIndex + 2] == '&' || strSplit[lotIndex + 2] == 'AND') {
      return strSplit.splice(0, lotIndex + 4).join(' ');
    } else {
      return strSplit.splice(0, lotIndex + 2).join(' ');
    }
  } else {
    const intVolIndex = findFirstINTVOLIndex(str);

    if (intVolIndex != -1) {
      return str.substring(0, intVolIndex - 1);
    }
    if (str.length > 50) {
      return str.substring(0, str.length / 2).concat('...');
    } else {
      return str;
    }
  }
};

const Summary = connect(({ CMA }) => ({
  userGroup: CMA.userGroup,
  currentPropertyAddress: CMA.currentPropertyAddress,
  currentRadiusMile: CMA.currentRadiusMile,
  eventCoordinates: CMA.eventCoordinates,
  searchingMode: CMA.searchingMode,
  isLandMode: CMA.isLandMode,
  searchingMode: CMA.searchingMode,
  // modeToFindSubjectProperty: CMA.modeToFindSubjectProperty,
  subjectPropertyParcelData: CMA.subjectPropertyParcelData,
  userGroup: CMA.userGroup,
  selectedUserGroup: CMA.selectedUserGroup,
  // printMediaContent: CMA.printMediaContent,
  // scorecardModalOpen: CMA.scorecardModalOpen,
  // subjectPropertyValid: CMA.subjectPropertyValid,
  drawnCustomPolygons: CMA.drawnCustomPolygons,
  currentStatusMLS: CMA.currentStatusMLS,
  currentStartMLS: CMA.currentStartMLS,
  currentEndMLS: CMA.currentEndMLS,
  expDateFilterOn: CMA.expDateFilterOn,
  compingMode: CMA.compingMode,
  // adjustedRentCompParams: CMA.adjustedRentCompParams,
  // adjustedSalesCompParams: CMA.adjustedSalesCompParams,
  // adjustedRentFormula: CMA.adjustedRentFormula,
  // adjustedSalesFormula: CMA.adjustedSalesFormula,
  marketRentPreference: CMA.marketRentPreference,
  subjectPropertyPlacekey: CMA.subjectPropertyPlacekey,
  selectedCompTables: CMA.selectedCompTables,
  marketConditionStartDate: CMA.marketConditionStartDate,
  marketConditionEndDate: CMA.marketConditionEndDate,
  chartArea: CMA.chartArea,
  cmaTabKey: CMA.cmaTabKey,
  doneSendingPropertyToOffMarket: CMA.doneSendingPropertyToOffMarket,
  userEmail: CMA.userEmail,
  metrosAllowedOnIndividualAccountLevel:
    CMA.metrosAllowedOnIndividualAccountLevel,
  showFilterSideBar: CMA.showFilterSideBar,
  map: CMA.map,
  currentCompLastSaleData: CMA.currentCompLastSaleData,
  showTaxDetailsModal: CMA.showTaxDetailsModal,
}))(function (props) {
  // set exported variables
  rentAverageNationalOperators = props.rentAverageNationalOperators;
  rentAveragePerSqftNationalOperators =
    props.rentAveragePerSqftNationalOperators;
  rentAverageHotPads = props.rentAverageHotPads;
  rentAveragePerSqftHotPads = props.rentAveragePerSqftHotPads;
  rentAverageMLS = props.rentAverageMLS;
  rentAveragePerSqftMLS = props.rentAveragePerSqftMLS;
  rentAverageAll = props.rentAverageAll;
  rentAveragePerSqftAll = props.rentAveragePerSqftAll;
  rentAdjustedBT = props.rentAdjustedBT;
  MLSAverageToAVMRatio = props.MLSAverageToAVMRatio;

  const { withinClientSubscription, loading } = useWithinClientSubscription({
    lng: +props.eventCoordinates[0],
    lat: +props.eventCoordinates[1],
  });

  useEffect(() => {
    const { latitude, longitude } = props.currentPropertyAddress;
    if (!latitude || !longitude) return;

    const params = {
      lat: latitude,
      lng: longitude,
      radius: 0.01 * 1609,
    };
    const fetchLastSale = async () => {
      const result = await getLastSoldPublicRecord(params);
      console.log('test last sale result', result);
      if (result && result.length > 0) {
        //match address:
        const currentStreetAddress = props.currentPropertyAddress.streetAddress;
        const lastSaleData = result[0];
        console.log('test last sale data', params, result);
        const match = doesStringMatchAddress(
          currentStreetAddress,
          lastSaleData,
        );
        if (match) {
          props.dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              currentCompLastSaleData: lastSaleData,
            },
          });
        }
      } else {
        props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            currentCompLastSaleData: null,
          },
        });
      }
    };
    fetchLastSale();
  }, [props.currentPropertyAddress]);

  useEffect(() => {
    console.log('test currentCompLastSaleData', props.currentCompLastSaleData);
  }, [props.currentCompLastSaleData]);
  useEffect(() => {
    if (
      props.userGroup.includes('VentureREI') &&
      props.compingMode != 'preset-venture'
    ) {
      onChangeCompingMode('preset-venture');
    } else if (
      props.userGroup.includes('SVN') &&
      props.compingMode != 'preset-svn'
    ) {
      onChangeCompingMode('preset-svn');
    } else if (
      props.userGroup.includes('Sunroom') &&
      props.compingMode != 'preset-sunroom'
    ) {
      if (!props.map) return;
      onChangeCompingMode('noFilter');
    }
  }, [props.userGroup, props.map]);

  useEffect(() => {
    // need to consider when parcel data are not available
    if (props.compingMode === 'preset-venture') {
      const subjectSqft = props.subjectPropertyParcelData?.total_area_sq_ft;
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          // ...(subjectSqft && { maxSqft: subjectSqft + 300 }),
          maxSqft: subjectSqft
            ? subjectSqft + 300
            : filterValuesDefault.maxSqft,
          selectedPoolAllowed: true, // for VentureREI
        },
      });
    } else if (props.compingMode === 'preset-sunroom') {
      const subjectSqft = props.subjectPropertyParcelData?.total_area_sq_ft;
      const subjectBeds = props.subjectPropertyParcelData?.beds_count;
      const subjectBaths = props.subjectPropertyParcelData?.baths;
      const subjectPropertyType =
        props.subjectPropertyParcelData?.standardized_land_use_type;
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          // ...(subjectBeds && { minBeds: subjectBeds }),
          // ...(subjectBeds && { maxBeds: subjectBeds }),
          // ...(subjectBaths && { minBaths: subjectBaths }),
          // ...(subjectBaths && { maxBaths: subjectBaths + 1 }),
          // ...(subjectSqft && { minSqft: Math.round(subjectSqft * 0.925) }),
          // ...(subjectSqft && { maxSqft: Math.round(subjectSqft * 1.075) }),
          // ...(subjectPropertyType && {
          //   checkedPropertySubTypes: [
          //     capitalize(subjectPropertyType.toLowerCase()),
          //   ],
          // }),
          minBeds: subjectBeds ? subjectBeds : filterValuesDefault.minBeds,
          maxBeds: subjectBeds ? subjectBeds : filterValuesDefault.maxBeds,
          minBaths: subjectBaths ? subjectBaths : filterValuesDefault.minBaths,
          maxBaths: subjectBaths
            ? subjectBaths + 1
            : filterValuesDefault.maxBaths,
          minSqft: subjectSqft
            ? Math.round(subjectSqft * 0.925)
            : filterValuesDefault.minSqft,
          maxSqft: subjectSqft
            ? Math.round(subjectSqft * 1.075)
            : filterValuesDefault.maxSqft,
          // checkedPropertySubTypes: subjectPropertyType
          //   ? [capitalize(subjectPropertyType.toLowerCase())]
          //   : filterValuesDefault.checkedPropertySubTypes,
        },
      });
    }
  }, [props.subjectPropertyParcelData]);

  const onChangeCompingMode = (value) => {
    let payloadMode = '';
    let payloadStatusMLS = '';

    const subjectSqft = props.subjectPropertyParcelData?.total_area_sq_ft;
    const subjectBeds = props.subjectPropertyParcelData?.beds_count;
    const subjectBaths = props.subjectPropertyParcelData?.baths;
    const subjectPropertyType =
      props.subjectPropertyParcelData?.standardized_land_use_type;

    switch (value) {
      case 'noFilter':
        payloadMode = 'switch to no filter';
        payloadStatusMLS = 'status';
        props.dispatch({
          type: 'CMA/updateFilterValues',
          payload: {
            ...filterValuesDefault,
            minRentPrice: filterValuesDefault.minRentPrice,
            maxRentPrice: filterValuesDefault.maxRentPrice,
            minSoldPrice: filterValuesDefault.minSoldPrice,
            maxSoldPrice: filterValuesDefault.maxSoldPrice,
            isDistrictFilterOn: false,
            isZipCodeFilterOn: false,
            isCountyFilterOn: false,
            selectedPoolAllowed: true,
            currentStatusMLS: payloadStatusMLS,
            currentStartMLS: moment().subtract(365, 'days').format(dateFormat),
            currentEndMLS: moment().format(dateFormat),
            compingMode: 'noFilter',
          },
        });
        if (document.querySelector('#radiusSelectWrapper')?.style?.display) {
          document.querySelector('#radiusSelectWrapper').style.display = 'flex';
        }
        break;
      case 'smartFilter':
        payloadMode = 'switch to smart filter';
        payloadStatusMLS = ['ILE'].includes(props.selectedUserGroup)
          ? 'status'
          : 'Closed';
        const currentStartMLSWithinDays = ['ILE'].includes(
          props.selectedUserGroup,
        )
          ? 180
          : 90;
        props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            compingMode: 'smartFilter',
            currentStatusMLS: payloadStatusMLS,
            currentStartMLS: moment()
              .subtract(currentStartMLSWithinDays, 'days')
              .format(dateFormat),
            currentEndMLS: moment().format(dateFormat),
            selectedPoolAllowed: ['ILE'].includes(props.selectedUserGroup)
              ? true
              : false,
          },
        });
        if (document.querySelector('#radiusSelectWrapper')?.style?.display) {
          document.querySelector('#radiusSelectWrapper').style.display = 'flex';
        }
        break;
      case 'intelligentComping':
        payloadMode = 'switch to intelligent comping';
        props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            compingMode: 'intelligentComping',
          },
        });
        if (document.querySelector('#radiusSelectWrapper')?.style?.display) {
          document.querySelector('#radiusSelectWrapper').style.display = 'none';
        }
        break;
      case 'preset-venture':
        payloadMode = 'switch to preset';
        payloadStatusMLS = 'status';
        props.dispatch({
          type: 'CMA/updateFilterValues',
          payload: {
            ...filterValuesDefault,
            compingMode: 'preset-venture',
            isDistrictFilterOn: true,
            currentStartMLS: moment().subtract(180, 'days').format(dateFormat),
            currentEndMLS: moment().format(dateFormat),
            // ...(subjectSqft && { maxSqft: subjectSqft + 300 }),
            maxSqft: subjectSqft
              ? subjectSqft + 300
              : filterValuesDefault.maxSqft,
            currentStatusMLS: payloadStatusMLS, // All
            selectedPoolAllowed: true, // for VentureREI
          },
        });
        if (document.querySelector('#radiusSelectWrapper')) {
          document.querySelector('#radiusSelectWrapper').style.display = 'flex';
        }
        break;

      case 'preset-svn':
        payloadMode = 'switch to preset';
        payloadStatusMLS = 'status';
        props.dispatch({
          type: 'CMA/updateFilterValues',
          payload: {
            ...filterValuesDefault,
            compingMode: 'preset-svn',
            currentStartMLS: moment().subtract(180, 'days').format(dateFormat),
            currentEndMLS: moment().format(dateFormat),
            minBeds: 2,
            maxBeds: 4,
            minSqft: 1400,
            maxSqft: 2400,
            minYearBuilt: 2010,
            currentStatusMLS: payloadStatusMLS, // All
            // checkedPropertySubTypes: ['Single Family Residence', 'Townhouse'],
          },
        });
        if (document.querySelector('#radiusSelectWrapper')) {
          document.querySelector('#radiusSelectWrapper').style.display = 'flex';
        }
        break;

      case 'preset-sunroom':
        payloadMode = 'switch to preset';
        payloadStatusMLS = 'status';
        props.dispatch({
          type: 'CMA/updateFilterValues',
          payload: {
            ...filterValuesDefault,
            compingMode: 'preset-sunroom',
            currentStartMLS: moment().subtract(90, 'days').format(dateFormat),
            currentEndMLS: moment().format(dateFormat),
            isDistrictFilterOn: true,
            isZipCodeFilterOn: true,
            // ...(subjectBeds && { minBeds: subjectBeds }),
            // ...(subjectBeds && { maxBeds: subjectBeds }),
            // ...(subjectBaths && { minBaths: subjectBaths }),
            // ...(subjectBaths && { maxBaths: subjectBaths + 1 }),
            // ...(subjectSqft && { minSqft: Math.round(subjectSqft * 0.925) }),
            // ...(subjectSqft && { maxSqft: Math.round(subjectSqft * 1.075) }),
            // ...(subjectPropertyType && {
            //   checkedPropertySubTypes: [
            //     capitalize(subjectPropertyType.toLowerCase()),
            //   ],
            // }),
            minBeds: subjectBeds ? subjectBeds : filterValuesDefault.minBeds,
            maxBeds: subjectBeds ? subjectBeds : filterValuesDefault.maxBeds,
            minBaths: subjectBaths
              ? subjectBaths
              : filterValuesDefault.minBaths,
            maxBaths: subjectBaths
              ? subjectBaths + 1
              : filterValuesDefault.maxBaths,
            minSqft: subjectSqft
              ? Math.round(subjectSqft * 0.925)
              : filterValuesDefault.minSqft,
            maxSqft: subjectSqft
              ? Math.round(subjectSqft * 1.075)
              : filterValuesDefault.maxSqft,
            // checkedPropertySubTypes: subjectPropertyType
            //   ? [capitalize(subjectPropertyType.toLowerCase())]
            //   : filterValuesDefault.checkedPropertySubTypes,
            // checkedPropertySubTypes: ['Single Family Residence', 'Townhouse'],
            currentStatusMLS: payloadStatusMLS, // All
          },
        });

        props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            currentRadiusMile: 2,
          },
        });

        props.map.fire('selectRadius.setRadius', {
          payload: {
            currentRadiusMile: 2,
          },
        });

        if (document.querySelector('#radiusSelectWrapper')) {
          document.querySelector('#radiusSelectWrapper').style.display = 'flex';
        }
        break;

      case 'BTRFilter':
        payloadMode = 'switch to BTRFilter';
        payloadStatusMLS = 'status';
        props.dispatch({
          type: 'CMA/updateFilterValues',
          payload: {
            ...filterValuesDefault,
            compingMode: 'BTRFilter',
            currentStartMLS: moment().subtract(365, 'days').format(dateFormat),
            currentEndMLS: moment().format(dateFormat),
            minYearBuilt: 2018,
            currentStatusMLS: payloadStatusMLS, // All
          },
        });

        props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            currentRadiusMile: 5,
          },
        });

        props.map.fire('selectRadius.setRadius', {
          payload: {
            currentRadiusMile: 5,
          },
        });

        if (document.querySelector('#radiusSelectWrapper')) {
          document.querySelector('#radiusSelectWrapper').style.display = 'flex';
        }
        break;

      default:
        break;
    }
    if (props.eventCoordinates.length === 2) {
      // console.log('payloadMode', payloadMode);
      props.dispatch({
        type: 'CMA/getAllPropertyData',
        payload: {
          mode: payloadMode,
          lng: props.eventCoordinates[0],
          lat: props.eventCoordinates[1],
          status: payloadStatusMLS || props.currentStatusMLS,
          propertyType:
            props.searchingMode === 'Lease'
              ? 'Residential Lease'
              : 'Residential',
          exists: payloadStatusMLS || props.currentStatusMLS,
          expDateFilterOn: props.expDateFilterOn ? 'yes' : 'no',
        },
      });
    }
  };

  const onChangeMarketRentPreference = (value) => {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        marketRentPreference: value,
      },
    });
    // if there's a subject property, re-fetch /adjusted
    if (
      props.compingMode === 'intelligentComping' &&
      props.eventCoordinates.length === 2
    ) {
      props.dispatch({
        type: 'CMA/getAllPropertyData',
        payload: {
          preference: value,
          // for others
          mode: 'switch to intelligent comping',
          lng: props.eventCoordinates[0],
          lat: props.eventCoordinates[1],
          // geofence: getGeofenceParamForGeocodingAPI(props.userGroup),
          status: props.currentStatusMLS,
          propertyType:
            props.searchingMode === 'Lease'
              ? 'Residential Lease'
              : 'Residential',
          distance: props.currentRadiusMile * 1609.34, // for multi-family
          exists: props.currentStatusMLS,
          expDateFilterOn: props.expDateFilterOn ? 'yes' : 'no',
        },
      });
    }
  };

  const compingModeOptions = [
    { label: 'All', value: 'noFilter', tooltip: 'Reveal All' },
    {
      label: 'Match',
      value: 'smartFilter',
      tooltip: 'Match to subject property with moderate filters you can adjust',
    },
    // ...(!['ILE'].includes(props.selectedUserGroup) // no intelligent comping for ILE
    //   ? [
    //       {
    //         label: 'Intelligent',
    //         value: 'intelligentComping',
    //         tooltip: 'Aggressive automatic filters',
    //       },
    //     ]
    //   : []),
  ];

  if (props.userGroup.includes('VentureREI')) {
    compingModeOptions.push({ label: 'Preset', value: 'preset-venture' });
  } else if (props.userGroup.includes('SVN')) {
    compingModeOptions.push({ label: 'Preset', value: 'preset-svn' });
  } else if (props.userGroup.includes('Sunroom')) {
    compingModeOptions.push({ label: 'Preset', value: 'preset-sunroom' });
  }

  if (
    !loading &&
    !withinClientSubscription &&
    props.marketRentPreference === 'mls'
  ) {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        marketRentPreference: 'sfr',
      },
    });
  }
  // delete props.subjectPropertyParcelData.hoa_fees;
  // console.log('SUBJECT HOA: ', props.subjectPropertyParcelData.hoa_fees);
  // console.log('test currentPropertyAddress', props.currentPropertyAddress);

  useEffect(() => {
    console.log('compingMode', props.compingMode), [props.compingMode];
  });
  const searchingModeChnage = (value) => {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        searchingMode: value,
      },
    });
    if (value === 'Sale') {
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          cmaTabKey: '1',
        },
      });
    }
    if (value === 'Land') {
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          cmaTabKey: '1',
        },
      });
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          currentStartMLS: moment().subtract(365, 'days').format(dateFormat),
          currentEndMLS: moment().format(dateFormat),
        },
      });
    }
    if (props.drawnCustomPolygons.length === 0) {
      if (props.eventCoordinates.length === 2) {
        // fetch all data
        props.dispatch({
          type: 'CMA/getAllPropertyData',
          payload: {
            mode: value === 'Lease' ? 'to lease mode' : 'to sales mode',
            lng: props.eventCoordinates[0],
            lat: props.eventCoordinates[1],
            // geofence: getGeofenceParamForGeocodingAPI(props.userGroup),
            status: props.currentStatusMLS,
            propertyType:
              value === 'Lease' ? 'Residential Lease' : 'Residential',
            startDate: moment(props.currentStartMLS).format(dateFormat),
            endDate: moment(props.currentEndMLS).format(dateFormat),
            distance: props.currentRadiusMile * 1609.34,
            exists: props.currentStatusMLS,
            expDateFilterOn: props.expDateFilterOn ? 'yes' : 'no',
          },
        });
      }
    } else {
      props.dispatch({
        type: 'CMA/getAllPropertyData',
        payload: {
          mode: value === 'Lease' ? 'to lease mode' : 'to sales mode',
          status: props.currentStatusMLS,
        },
      });
    }
  };

  const saved = true;

  return (
    <Row
      key="subject property card"
      align="middle"
      justify="space-between"
      wrap={true} // for when map expands
      className={styles.cardWrapperSticky}
      // style={{ alignItems: 'stretch' }}
      gutter={[0, 16]} // [horizontal gutter, vertical gutter]
    >
      <Col key="summary row" span={24}>
        <div key="full address">
          {!isEmpty(props.currentPropertyAddress) && (
            <span key="street address text" className={styles.cardTitleH1}>
              <div className="inline-flex items-center gap-2">
                <BookmarkButton />
                <span>
                  {props.currentPropertyAddress.streetAddress +
                    ', ' +
                    props.currentPropertyAddress.city +
                    ', ' +
                    props.currentPropertyAddress.region +
                    ' ' +
                    props.currentPropertyAddress.postalCode}
                </span>
              </div>
            </span>
          )}
          {props.eventCoordinates.length > 0 && (
            <>
              <EditAddress type="default" />
              <ShareSubjectPropertyButton />
              {props.cmaTabKey === '1' && (
                <GeneratePDF isIcon={true} source="summary" />
              )}
              {props.cmaTabKey === '2' && (
                <InsightsPDF
                  lat={props.eventCoordinates[1] || undefined}
                  lng={props.eventCoordinates[0] || undefined}
                  radius={props.currentRadiusMile || undefined}
                  customPolygon={props.drawnCustomPolygons || undefined}
                  searchingMode={props.searchingMode}
                  marketStartDate={props.marketConditionStartDate || undefined}
                  marketEndDate={props.marketConditionEndDate || undefined}
                  // chartArea={props.chartArea || undefined}
                  chartArea={'aoi'}
                  serverType={serverType}
                  userGroup={
                    props.userGroup.includes('Sunroom')
                      ? 'Sunroom'
                      : 'demo-users'
                  } // Sunroom uses their own logo, while all other clients use LocateAlpha logo
                />
              )}
              <PropertyImageAndHouseDetail />
              <Button
                key="send to off-market button"
                type="default"
                size="small"
                style={{
                  marginLeft: 8,
                  fontSize: 12,
                  lineHeight: 1,
                  height: 20,
                  padding: '2px 8px',
                  color: '#1890ff',
                  border: 'none',
                  backgroundColor: 'rgba(255,255,255,.8)',
                }}
                loading={props.sendPropertyToOffMarketLoading}
                onClick={() => {
                  props.dispatch({
                    type: 'CMA/sendPropertyToOffMarket',
                    payload: {
                      body: [
                        {
                          userId: null,
                          propertySource: props.userEmail,
                          placekey: props.subjectPropertyPlacekey,
                          fips: props.subjectPropertyParcelData.fips,
                          apn: props.subjectPropertyParcelData.apn,
                          stage: 'custom',
                          address: props.currentPropertyAddress.streetAddress,
                          city: props.currentPropertyAddress.city,
                          state: props.currentPropertyAddress.region,
                          zipCode: props.currentPropertyAddress.postalCode,
                          beds: props.subjectPropertyParcelData.beds_count,
                          baths: props.subjectPropertyParcelData.baths,
                          sqft: props.subjectPropertyParcelData
                            .total_area_sq_ft,
                          yearbuilt: props.subjectPropertyParcelData.year_built,
                          lng: props.eventCoordinates[0],
                          lat: props.eventCoordinates[1],
                          meta: {
                            source: 'CMA',
                            timestamp: new Date().toISOString(),
                            citycode: getCityCodeViaZIPCode(
                              props.currentPropertyAddress.postalCode,
                            )
                              ? getCityCodeViaZIPCode(
                                  props.currentPropertyAddress.postalCode,
                                )
                              : '00',
                          },
                          citycode: getCityCodeViaZIPCode(
                            props.currentPropertyAddress.postalCode,
                          )
                            ? getCityCodeViaZIPCode(
                                props.currentPropertyAddress.postalCode,
                              )
                            : null,
                        },
                      ],
                    },
                  });
                }}
              >
                Send to Off-Market
              </Button>
            </>
          )}
        </div>
        <Row
          key="property detail row"
          align="bottom"
          justify="start"
          wrap={true}
          gutter={8}
          style={{ marginTop: 8 }}
        >
          <Col key="beds wrapper">
            <span key="beds number" className={styles.summaryCardDetail}>
              {props.subjectPropertyParcelData.beds_count || '-'}
            </span>
            <span key="beds text" className={styles.cardSubtitle}>
              bd
            </span>
          </Col>
          <Divider key="divider1" type="vertical" />
          <Col key="baths wrapper">
            <span key="baths number" className={styles.summaryCardDetail}>
              {props.subjectPropertyParcelData.baths || '-'}
            </span>
            <span key="baths text" className={styles.cardSubtitle}>
              ba
            </span>
          </Col>
          <Divider key="divider2" type="vertical" />
          <Col key="sqft wrapper">
            <span key="sqft number" className={styles.summaryCardDetail}>
              {props.subjectPropertyParcelData.total_area_sq_ft || '-'}
            </span>
            <span key="sqft text" className={styles.cardSubtitle}>
              sqft
            </span>
          </Col>
          <Divider key="divider3" type="vertical" />
          <Col key="lot size wrapper">
            <span key="lot size number" className={styles.summaryCardDetail}>
              {props.subjectPropertyParcelData.area_acres
                ? `${props.subjectPropertyParcelData.area_acres.toFixed(2)}`
                : '-'}
            </span>
            <span key="lot size text" className={styles.cardSubtitle}>
              ac
            </span>
          </Col>
          <Divider key="divider4" type="vertical" />
          <Col key="year built wrapper">
            <span key="year built text" className={styles.cardSubtitle}>
              built in
            </span>
            <span
              key="year built number"
              className={styles.summaryCardDetailYearBuilt}
            >
              {props.subjectPropertyParcelData.year_built || '-'}
            </span>
          </Col>
          {/* //Tax Rate */}
          <Divider key="divider5-2" type="vertical" />
          {props.subjectPropertyParcelData.tax_rate_percent && (
            <Col key="tax rate wrapper">
              <span key="tax rate text" className={styles.cardSubtitle}>
                Property Tax Rate
              </span>

              <Tooltip title={'Click to show details'}>
                <span
                  className={styles.summaryCardDetailYearBuilt}
                  style={{
                    color: 'black',
                    fontWeight: '600',
                    cursor: 'pointer',
                    textDecoration: 'none',
                  }}
                  onClick={() => {
                    props.dispatch({
                      type: 'CMA/saveCMAStates',
                      payload: {
                        showTaxDetailsModal: true,
                      },
                    });
                  }}
                >
                  {props.subjectPropertyParcelData.tax_rate_percent.toFixed(2) +
                    '%'}
                </span>
              </Tooltip>
            </Col>
          )}
          <Divider key="divider5" type="vertical" />
          <Col key="HOA fee wrapper">
            <span key="HOA fee text" className={styles.cardSubtitle}>
              HOA fee
            </span>
            <span
              key="HOA fee number"
              className={styles.summaryCardDetailYearBuilt}
            >
              {props.subjectPropertyParcelData.hoa_fees ||
              props.subjectPropertyParcelData.hoa_fees === 0
                ? '$' + props.subjectPropertyParcelData.hoa_fees
                : '-'}
            </span>
          </Col>
          {props.subjectPropertyParcelData.marker &&
            props.subjectPropertyParcelData.marker === 'Estimated' && (
              <Col key="marker wrapper">
                <span key="marker" className={styles.cardSubtitleLighter}>
                  {props.subjectPropertyParcelData.marker}
                </span>
              </Col>
            )}

          {!props.userGroup.includes('MarketplaceHomes') &&
            props.subjectPropertyParcelData.legal_description && (
              <Col key="subdivision wrapper">
                <Tooltip
                  placement="bottom"
                  title={props.subjectPropertyParcelData.legal_description}
                >
                  <span
                    className={styles.summaryCardDetailYearBuilt}
                    // style={{ maxWidth: '350px', fontSize: '11px' }}
                  >
                    {legalDescriptionTrimmer(
                      props.subjectPropertyParcelData.legal_description,
                    )}
                  </span>
                </Tooltip>
              </Col>
            )}
        </Row>
      </Col>

      {/* Last Sale Data */}
      {(props.subjectPropertyParcelData.deed_last_sale_price ||
        props.subjectPropertyParcelData.deed_last_sale_date) && (
        <Col key="last sale data">
          <Row
            key="property detail row"
            align="bottom"
            justify="start"
            wrap={true}
            gutter={2}
          >
            {props.subjectPropertyParcelData.deed_last_sale_date && (
              <Col key="subdivision wrapper">
                <span key="HOA fee text" className={styles.cardSubtitle}>
                  Last Sale Date:
                </span>

                <span
                  className={styles.summaryCardDetailYearBuilt}
                  // style={{ maxWidth: '350px', fontSize: '11px' }}
                >
                  {props.subjectPropertyParcelData.deed_last_sale_date}
                </span>
              </Col>
            )}
            <Divider key="divider6" type="vertical" />
            {props.subjectPropertyParcelData.deed_last_sale_price &&
              !NON_DISCLOSURE_STATES.includes(
                props.currentPropertyAddress.region,
              ) && (
                <Col key="subdivision wrapper">
                  <span key="HOA fee text" className={styles.cardSubtitle}>
                    Last Sale Price:
                  </span>

                  <span
                    className={styles.summaryCardDetailYearBuilt}
                    // style={{ maxWidth: '350px', fontSize: '11px' }}
                  >
                    {formatCurrency(
                      props.subjectPropertyParcelData.deed_last_sale_price,
                    )}
                  </span>
                </Col>
              )}
          </Row>
        </Col>
      )}

      <Col
        key="mode selectors col"
        id="modeSelectorWrapper"
        span={24}
        // className={styles.cardWrapper}
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          columnGap: 0,
          rowGap: 0,
          flexWrap: 'wrap',
          width: '100%',
          paddingTop: 4,
          paddingBottom: 4,
          backgroundColor: 'rgba(255,255,255,.8)',
          // border: '1px solid #1890ff',
          // borderRadius: 8,
        }}
      >
        <SavedFilters />
        <div
          key="lease sale mode selector wrapper"
          className={styles.modeSelectorWrapper}
        >
          <Segmented
            options={['Lease', 'Sale', 'Land']}
            value={props.searchingMode}
            onChange={searchingModeChnage}
            className="ml-2"
          />
        </div>
        <div
          key="comping mode selector wrapper"
          className={styles.modeSelectorWrapper}
        >
          <Select
            key="comping mode selector"
            // options={compingModeOptions}
            options={compingModeOptions.map((option) => ({
              label: (
                <Tooltip title={option.tooltip}>
                  <span>{option.label}</span>
                </Tooltip>
              ),
              value: option.value,
            }))}
            value={props.compingMode}
            onChange={onChangeCompingMode}
            dropdownMatchSelectWidth={false}
            className={styles.controlSelect}
            style={{ width: '100%' }}
          />
        </div>
        <div
          key="adjusted preference selector wrapper"
          className={styles.modeSelectorWrapper}
          style={{
            display: props.searchingMode === 'Lease' ? 'flex' : 'none',
          }}
        >
          {/* <label
            key="title"
            htmlFor="adjusted preference selector"
            style={{
              marginRight: 8,
            }}
          >
            Preference:
          </label> */}
          <Select
            key="adjusted preference selector"
            id="adjusted preference selector"
            value={props.marketRentPreference}
            onChange={onChangeMarketRentPreference}
            options={[
              {
                label: 'Favor MLS',
                value: 'mls',
                disabled: !withinClientSubscription,
              },
              { label: 'Favor SFR Operator', value: 'sfr' },
              { label: 'Favor 3rd Party', value: 'hotpads' },
            ]}
            popupMatchSelectWidth={false}
            className={styles.controlSelect}
            style={{
              width: '100%',
            }}
          />
        </div>
        {!['1', 'Comp Insights', '6', '9'].includes(props.cmaTabKey) ||
        props.showFilterSideBar ? (
          <></>
        ) : (
          <Button
            key="filter modal button"
            type="text"
            onClick={() => {
              props.dispatch({
                type: 'CMA/saveCMAStates',
                payload: {
                  // showFilterModal: true,
                  showFilterSideBar: !props.showFilterSideBar,
                },
              });
            }}
            style={{
              // marginTop: 16,
              // display:
              //   props.compingMode === 'intelligentComping' ? 'none' : 'flex',
              display: 'flex',
              color: '#1890ff',
            }}
          >
            {/* <div
              key="edit icon"
              style={{
                display: 'block',
                width: 24,
                height: 24,
                fill: '#1890ff',
                marginRight: 4,
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 96 960 960">
                <path d="M150 854q-12.75 0-21.375-8.675-8.625-8.676-8.625-21.5 0-12.825 8.625-21.325T150 794h187q12.75 0 21.375 8.675 8.625 8.676 8.625 21.5 0 12.825-8.625 21.325T337 854H150Zm0-496q-12.75 0-21.375-8.675-8.625-8.676-8.625-21.5 0-12.825 8.625-21.325T150 298h353q12.75 0 21.375 8.675 8.625 8.676 8.625 21.5 0 12.825-8.625 21.325T503 358H150Zm306.825 578Q444 936 435.5 927.375T427 906V741q0-12.75 8.675-21.375 8.676-8.625 21.5-8.625 12.825 0 21.325 8.625T487 741v53h323q12.75 0 21.375 8.675 8.625 8.676 8.625 21.5 0 12.825-8.625 21.325T810 854H487v52q0 12.75-8.675 21.375-8.676 8.625-21.5 8.625Zm-120-248Q324 688 315.5 679.375T307 658v-52H150q-12.75 0-21.375-8.675-8.625-8.676-8.625-21.5 0-12.825 8.625-21.325T150 546h157v-54q0-12.75 8.675-21.375 8.676-8.625 21.5-8.625 12.825 0 21.325 8.625T367 492v166q0 12.75-8.675 21.375-8.676 8.625-21.5 8.625ZM457 606q-12.75 0-21.375-8.675-8.625-8.676-8.625-21.5 0-12.825 8.625-21.325T457 546h353q12.75 0 21.375 8.675 8.625 8.676 8.625 21.5 0 12.825-8.625 21.325T810 606H457Zm165.825-165Q610 441 601.5 432.375T593 411V246q0-12.75 8.675-21.375 8.676-8.625 21.5-8.625 12.825 0 21.325 8.625T653 246v52h157q12.75 0 21.375 8.675 8.625 8.676 8.625 21.5 0 12.825-8.625 21.325T810 358H653v53q0 12.75-8.675 21.375-8.676 8.625-21.5 8.625Z" />
              </svg>
            </div> */}
            {/* {props.compingMode === 'intelligentComping'
              ? 'Show Criteria'
              : 'Edit Criteria'} */}
            Filters
          </Button>
        )}
      </Col>
    </Row>
  );
});

export default Summary;
