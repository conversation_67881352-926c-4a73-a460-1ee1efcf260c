import ImageDetailTooltip from '@/components/ImageDetailTooltip';
import styles from '@/components/ResultTable/resultTable.css';
import { getSingleMLSRecordData } from '@/services/data';
import { Button, Tooltip } from 'antd';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import {
  getCityCodeViaMetroName,
  getMetroNameForParam,
} from '../../../utils/geography';

function MLSImageAndHouseDetail() {
  const subjectPropertyParcelData = useSelector(
    (state) => state.CMA.subjectPropertyParcelData,
  );
  const eventCoordinates = useSelector((state) => state.CMA.eventCoordinates);

  const dispatch = useDispatch();

  const [matchingProperty, setMatchingProperty] = useState(null);

  useEffect(() => {
    const getSingleMLS = async () => {
      if (
        eventCoordinates[0] === undefined ||
        eventCoordinates[1] === undefined
      )
        return;
      const response = await getSingleMLSRecordData({
        placekey: subjectPropertyParcelData.placekey || undefined,
        lng: eventCoordinates[0],
        lat: eventCoordinates[1],
      });

      if (response && response.length > 0) {
        setMatchingProperty(response[0]);
        return;
      }
      setMatchingProperty(null);
    };

    getSingleMLS();
  }, [subjectPropertyParcelData, eventCoordinates]);

  const openMLSModal = () => {
    let key = matchingProperty.listingkey;

    if (
      ['sanantonio', 'san antonio'].includes(
        matchingProperty.metro.toLowerCase(),
      ) ||
      getMetroNameForParam(matchingProperty, true) === 'sanantonio'
    ) {
      key = matchingProperty.mlsid;
    }

    dispatch({
      type: 'CMA/getBatchMLSPropertyImages',
      payload: {
        key: key,
        city: getMetroNameForParam(matchingProperty, true), // true for using realtrac instead of nashville
      },
    });

    dispatch({
      type: 'CMA/getHouseDetailsData',
      payload: {
        listingID: matchingProperty.mlsid,
        cityCode: getCityCodeViaMetroName(matchingProperty.metro),
      },
    });
    dispatch({
      type: 'CMA/getAPNOwnerName',
      payload: {
        placekey: matchingProperty.placekey,
      },
    });

    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        selectedMLProperty: matchingProperty,
        openMLSImageModal: true,
      },
    });
  };

  if (matchingProperty != null) {
    return (
      // <Tooltip title="View Images and House Details">
      <ImageDetailTooltip mlsProperty={matchingProperty}>
        <Button
          type="default"
          size="small"
          style={{
            marginLeft: 8,
            fontSize: 12,
            lineHeight: 1,
            height: 20,
            padding: '2px 8px',
            color: '#1890ff',
            border: 'none',
            backgroundColor: 'rgba(255,255,255,.8)',
            transform: 'translateY(4px)',
          }}
          // loading={props.sendPropertyToOffMarketLoading}
          onClick={openMLSModal}
        >
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              gap: '5px',
              alignItems: 'center',
            }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              width="16"
              height="16"
              className={styles.imageIconForAddress}
            >
              <path d="M7.479 12.083h8.709l-2.626-3.937-2.208 3.146-1.729-2.188Zm-.646 3.5q-.895 0-1.541-.645-.646-.646-.646-1.542V3.375q0-.896.646-1.542.646-.645 1.541-.645h10.021q.917 0 1.552.645.636.646.636 1.542v10.021q0 .896-.636 1.542-.635.645-1.552.645Zm-3.687 3.688q-.917 0-1.552-.636Q.958 18 .958 17.083V4.875h2.188v12.208h12.208v2.188Z" />
            </svg>
            <span>Details</span>
          </div>
        </Button>
        {/* // </ImageDetailTooltip> */}
      </ImageDetailTooltip>
    );
  } else {
    return null;
  }
}

export default MLSImageAndHouseDetail;
