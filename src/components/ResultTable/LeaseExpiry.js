import { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'umi';
import styles from '@/components/ResultTable/resultTable.css';
import { Statistic, Select } from 'antd';
import { default as turf_centerOfMass } from '@turf/center-of-mass';
import { multiPolygon } from '@turf/helpers';
import isEmpty from 'lodash.isempty';

const LeaseExpiry = () => {
  const [area, setArea] = useState('aoi');
  const [loading, setLoading] = useState(false);

  const eventCoordinates = useSelector((state) => state.CMA.eventCoordinates);
  const currentRadiusMile = useSelector((state) => state.CMA.currentRadiusMile);
  const drawnCustomPolygons = useSelector(
    (state) => state.CMA.drawnCustomPolygons,
  );
  const insightsLeaseExpiry = useSelector(
    (state) => state.CMA.insightsLeaseExpiry,
  );
  const dispatch = useDispatch();

  useEffect(() => {
    if (drawnCustomPolygons.length > 0) {
      // multipolygon
      const payload = {};
      if (area === 'aoi') {
        payload.area = 'multipolygon';
        payload.body = drawnCustomPolygons;
      } else if (area === 'zipcode') {
        const centerOfMass = turf_centerOfMass(
          multiPolygon([drawnCustomPolygons]),
        );
        payload.area = 'zipcode';
        payload.lng = centerOfMass.geometry.coordinates[0];
        payload.lat = centerOfMass.geometry.coordinates[1];
      }
      dispatch({
        type: 'CMA/getLeaseExpiryInsights',
        payload,
      });
      setLoading(true);
    } else if (
      eventCoordinates.length > 0 &&
      drawnCustomPolygons.length === 0
    ) {
      const payload = {};
      if (area === 'aoi') {
        payload.area = 'aoi';
        payload.lng = eventCoordinates[0];
        payload.lat = eventCoordinates[1];
        payload.distance = currentRadiusMile * 1609.34;
      } else if (area === 'zipcode') {
        payload.area = 'zipcode';
        payload.lng = eventCoordinates[0];
        payload.lat = eventCoordinates[1];
      }
      dispatch({
        type: 'CMA/getLeaseExpiryInsights',
        payload,
      });
      setLoading(true);
    } else {
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: { insightsLeaseExpiry: {} },
      });
    }
  }, [area, eventCoordinates, currentRadiusMile, drawnCustomPolygons]);

  useEffect(() => {
    if (!isEmpty(insightsLeaseExpiry)) {
      setLoading(false);
    }
  }, [insightsLeaseExpiry]);

  console.log('insightsLeaseExpiry', insightsLeaseExpiry);

  return (
    <div className={styles.cardWrapper}>
      <div>
        <h2 className={styles.cardTitleH2}>Lease Expiry</h2>
      </div>
      {(!isEmpty(insightsLeaseExpiry) || loading) && (
        <>
          <div key="divider 1" className={styles.dividerCardHeader} />
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '10px',
              padding: '10px',
            }}
          >
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: '10px',
              }}
            >
              <span>Area: </span>
              <Select
                value={area}
                onChange={(value) => setArea(value)}
                options={[
                  { label: 'AOI', value: 'aoi' },
                  { label: 'ZIP Code', value: 'zipcode' },
                ]}
                style={{ width: 120 }}
                size="small"
              />
            </div>
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'space-between',
                flexWrap: 'wrap',
              }}
            >
              <Statistic
                loading={loading}
                title="Currently active"
                value={insightsLeaseExpiry.active_now}
              />
              <Statistic
                loading={loading}
                title="Expired in 1 Month"
                value={insightsLeaseExpiry.lease_expire_one_month}
              />
              <Statistic
                loading={loading}
                title="Expired in 2 Month"
                value={insightsLeaseExpiry.lease_expire_two_months}
              />
              <Statistic
                loading={loading}
                title="Expired in 3 Month"
                value={insightsLeaseExpiry.lease_expire_three_months}
              />
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default LeaseExpiry;
