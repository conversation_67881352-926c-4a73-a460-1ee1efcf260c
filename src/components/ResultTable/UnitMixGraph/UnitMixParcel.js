import { scaleLinear } from 'd3-scale';
import isEmpty from 'lodash.isempty';
import { useEffect, useState } from 'react';
import { useSelector } from 'umi';
import {
  getParcelPolygonData,
  getParcelRadiusData,
  getParcelUnitMixData,
} from '../../../services/data';
import {
  filterTableDataSource,
  generateManualFilterExpression,
  generateManualFilterValues,
} from '../../Filters/filterFunctions';
import filterValuesDefault from '../../Filters/filterValuesDefault.json';
import Chart from './Chart';

const year = new Date().getFullYear();

/*
  {
    x: '1-500',
    y: 2,
    type: 'Square Feet',
    // match: 'false'
    percent: 10,
  }
 */

const bars = [
  { min: 1000, max: 1960, count: 0 },
  { min: 1961, max: 1970, count: 0 },
  { min: 1971, max: 1980, count: 0 },
  { min: 1981, max: 1990, count: 0 },
  { min: 1991, max: 2000, count: 0 },
  { min: 2001, max: 2010, count: 0 },
  { min: 2011, max: year, count: 0 },
];

const calculateYearBands = (data) => {
  const min = Math.min(...data);
  const max = Math.max(...data);

  const range = max - min;
  const rangeSize = Math.floor(range / 10) * 10;

  const bandScale = scaleLinear()
    .domain([min, max])
    .range([0, numGroups - 1]);
};

const calculateSqftBands = (data, type) => {
  let fixedBars;
  if (type === 'Square Feet') {
    fixedBars = [
      { min: 0, max: 500, count: 0 },
      { min: 501, max: 1000, count: 0 },
      { min: 1001, max: 1500, count: 0 },
      { min: 1501, max: 2000, count: 0 },
      { min: 2001, max: 2500, count: 0 },
      { min: 2501, max: 3000, count: 0 },
      { min: 3001, max: 3500, count: 0 },
      { min: 3501, max: 4000, count: 0 },
      { min: 4001, max: 4500, count: 0 },
      { min: 4501, max: Infinity, count: 0 },
    ];
  } else {
    const maxYear = Math.floor(year / 10) * 10;
    const minMaxYear = maxYear - 60;
    fixedBars = [
      { min: 0, max: minMaxYear, count: 0 },
      { min: minMaxYear + 1, max: minMaxYear + 10, count: 0 },
      { min: minMaxYear + 11, max: minMaxYear + 20, count: 0 },
      { min: minMaxYear + 21, max: minMaxYear + 30, count: 0 },
      { min: minMaxYear + 31, max: minMaxYear + 40, count: 0 },
      { min: minMaxYear + 41, max: minMaxYear + 50, count: 0 },
      { min: minMaxYear + 51, max: Infinity, count: 0 },
    ];
    console.log('year FixedBars', fixedBars);
  }

  for (let i = 0; i < data.length; i++) {
    for (let j = 0; j < fixedBars.length; j++) {
      if (data[i] >= fixedBars[j].min && data[i] <= fixedBars[j].max) {
        fixedBars[j].count++;
        break;
      }
    }
  }

  const sum = fixedBars.reduce((a, b) => a + b.count, 0);

  const result = fixedBars
    .map((bar) => {
      let xLabel;
      if (bar.min === 0) {
        xLabel = `<${bar.max}`;
      } else if (bar.max === Infinity) {
        xLabel = `>${bar.min}`;
      } else {
        xLabel = `${bar.min}-${bar.max}`;
      }

      return {
        x: xLabel,
        y: bar.count,
        percent: (bar.count / sum) * 100,
        type: type,
      };
    })
    .filter((bar) => bar.y !== 0);

  return result;
};

// Calculate the groups/ranges for chart bands
const calculateBandGroups = (data, rangeSize, type) => {
  // Determine the minimum and maximum values in the data
  const min = Math.min(...data);
  const max = Math.max(...data);

  // Determine the number of groups based on the range size
  const numGroups = Math.ceil((max - min + 1) / rangeSize);

  // Create a D3 scale for the square footage
  const bandScale = scaleLinear()
    .domain([min, max])
    .range([0, numGroups - 1]);

  // Calculate the groups/ranges and their occurrences
  const groups = {};
  data.forEach((d) => {
    const rangeStart = Math.floor(bandScale(d)) * rangeSize + min;
    const rangeEnd = rangeStart + rangeSize - 1;
    const range = [rangeStart, rangeEnd];

    if (!groups.hasOwnProperty(range.toString())) {
      groups[range.toString()] = 1;
    } else {
      groups[range.toString()]++;
    }
  });

  console.log('groups', groups);

  const totalParcels = Object.values(groups).reduce((a, b) => a + b, 0);
  // groups.sort((a, b) => a.range[0] - b.range[0]);
  const result = Object.entries(groups).map(([range, occurrence]) => ({
    x: range.replace(',', '-'),
    y: occurrence,
    percent: (occurrence / totalParcels) * 100,
    type: type,
  }));

  result.sort((a, b) => {
    const aRange = Number(a.x.split('-')[0]);
    const bRange = Number(b.x.split('-')[0]);
    return aRange - bRange;
  });

  return result;
};

const calculateBandGroupsSqft = (data, rangeSize, type) => {
  // Determine the minimum and maximum values in the data
  const min = Math.min(...data);
  const max = Math.max(...data);

  const adjustedMin = min < 500 ? 500 : min;
  const adjustedMax = max > 5000 ? 5000 : max;

  // Determine the number of groups based on the range size
  const numGroups = Math.ceil((adjustedMax - adjustedMin + 1) / rangeSize);

  // Create a D3 scale for the square footage
  const bandScale = scaleLinear()
    .domain([adjustedMin, adjustedMax])
    .range([0, numGroups - 1]);

  // Calculate the groups/ranges and their occurrences
  const groups = {};
  data.forEach((d) => {
    let rangeStart, rangeEnd;
    if (adjustedMin != min && d < adjustedMin) {
      rangeStart = min;
      rangeEnd = adjustedMin - 1;
      console.log('HERE', adjustedMin, min, d);
    } else if (adjustedMax != max && d > adjustedMax) {
      rangeStart = adjustedMax + 1;
      rangeEnd = max;
    } else {
      rangeStart = Math.floor(bandScale(d)) * rangeSize + adjustedMin;
      rangeEnd = rangeStart + rangeSize - 1;
    }
    const range = [rangeStart, rangeEnd];

    if (rangeStart === 1 && rangeEnd === 250) {
      console.log('WRONG: ', d);
    }
    if (!groups.hasOwnProperty(range.toString())) {
      groups[range.toString()] = 1;
    } else {
      groups[range.toString()]++;
    }
  });

  console.log('groups', groups);
  // groups.sort((a, b) => a.range[0] - b.range[0]);
  const result = Object.entries(groups).map(([range, occurrence]) => ({
    x: range.replace(',', '-'),
    y: occurrence,
    type: type,
  }));

  result.sort((a, b) => {
    const aRange = Number(a.x.split('-')[0]);
    const bRange = Number(b.x.split('-')[0]);
    return aRange - bRange;
  });

  return result;
};

// based on comp selection ALL or manual filter
const filterData = (data, type) => {
  let filteredData = [];
  if (type === 'MLS') {
    const mlsFilterExpression = generateManualFilterExpression(
      generateManualFilterValues('MLS', filterValuesDefault),
    );
    filteredData = filterTableDataSource(mlsFilterExpression, data);
  } else if (type === 'SFR') {
    const sfrFilterExpression = generateManualFilterExpression(
      generateManualFilterValues('SFR', filterValuesDefault),
    );
    filteredData = filterTableDataSource(sfrFilterExpression, data);
  }
  return filteredData;
};

const formatBands = (data) => {
  const max = Math.max(...data.map((d) => d.y));
  const minLimit = max * 0.15;

  // combine bands until it is greater than minLimit
  // left most bands
  let count = 0;
  for (let i = 0; i < data.length; i++) {
    count += data[i].y;
    if (count > minLimit) {
      const minX = data[0].x.split('-')[0];
      const maxX = data[i].x.split('-')[1];
      data[i].x = `${minX}-${maxX}`;
      data[i].y = count;
      data.splice(0, i);
      break;
    }
  }

  // right most bands
  count = 0;
  for (let i = data.length - 1; i >= 0; i--) {
    count += data[i].y;
    if (count > minLimit) {
      const minX = data[i].x.split('-')[0];
      const maxX = data[data.length - 1].x.split('-')[1];
      data[i].x = `${minX}-${maxX}`;
      data[i].y = count;
      data.splice(i + 1, data.length - 1);
      break;
    }
  }

  return data;
};

const UnitMixParcel = () => {
  const [loading, setLoading] = useState(false);
  const [graphData, setGraphData] = useState([]);

  const subjectProperty = useSelector(
    (state) => state.CMA.subjectPropertyParcelData,
  );
  const eventCoordinates = useSelector((state) => state.CMA.eventCoordinates);
  const currentRadiusMile = useSelector((state) => state.CMA.currentRadiusMile);
  const drawnCustomPolygons = useSelector(
    (state) => state.CMA.drawnCustomPolygons,
  );

  // MLS keys: bed, bath, size, yearbuilt, lot_size
  const [parcelData, setParcelData] = useState({});
  const [updateChart, setUpdateChart] = useState(false);

  useEffect(() => {
    // if (eventCoordinates.length > 0 && subjectProperty) {
    //   setLoading(false);
    // } else {
    if (
      (eventCoordinates.length > 0 || drawnCustomPolygons.length > 0) &&
      // subjectProperty &&
      !loading
    ) {
      const fetchParcelData = async () => {
        let response;
        if (eventCoordinates.length > 0 && drawnCustomPolygons.length === 0) {
          response = await getParcelUnitMixData({
            lng: eventCoordinates[0],
            lat: eventCoordinates[1],
            radius: currentRadiusMile,
          });
        } else if (drawnCustomPolygons.length > 0) {
          response = await getParcelPolygonData({ body: drawnCustomPolygons });
        }

        if (response) {
          setParcelData(response);
        } else {
          setParcelData({});
        }
      };
      setLoading(true);
      fetchParcelData();
    } else {
      setParcelData({});
    }
    // }
  }, [
    // eventCoordinates,
    currentRadiusMile,
    subjectProperty,
    // drawnCustomPolygons,
  ]);

  useEffect(() => {
    // if (loading && eventCoordinates.length > 0 && subjectProperty) {
    if (
      (eventCoordinates.length > 0 || drawnCustomPolygons.length > 0) &&
      subjectProperty
    ) {
      if (Object.keys(parcelData).length > 0) {
        const data = [];
        if (parcelData.bedrooms) {
          data.push(...parcelData.bedrooms);
        }
        if (parcelData.bathrooms) {
          data.push(...parcelData.bathrooms);
        }
        if (parcelData.sqft) {
          data.push(...parcelData.sqft);
        }
        if (parcelData.year) {
          data.push(...parcelData.year);
        }

        for (let i = 0; i < data.length; i++) {
          if (data[i].type === 'Bedrooms' || data[i].type === 'Bathrooms') {
            const subjectType =
              data[i].type === 'Bedrooms' ? 'beds_count' : 'baths';
            if (data[i].x === '6+') {
              const value = 6;
              const match =
                Math.floor(subjectProperty[subjectType]) >= value
                  ? 'true'
                  : 'false';
              data[i].match = match;
            } else {
              const value = Number(data[i].x);
              const match =
                Math.floor(subjectProperty[subjectType]) === value
                  ? 'true'
                  : 'false';
              data[i].match = match;
            }
          } else if (data[i].type === 'Square Feet') {
            if (!data[i].x.includes('<') && !data[i].x.includes('>')) {
              const values = data[i].x.split('-').map(Number);
              const subjectValue = subjectProperty.total_area_sq_ft;
              const match =
                subjectValue >= values[0] && subjectValue <= values[1]
                  ? 'true'
                  : 'false';
              data[i].match = match;
            } else {
              if (data[i].x.includes('<')) {
                const value = Number(data[i].x.split('<')[1]);
                const subjectValue = subjectProperty.total_area_sq_ft;
                const match = subjectValue < value ? 'true' : 'false';
                data[i].match = match;
              } else if (data[i].x.includes('>')) {
                const value = Number(data[i].x.split('>')[1]);
                const subjectValue = subjectProperty.total_area_sq_ft;
                const match = subjectValue > value ? 'true' : 'false';
                data[i].match = match;
              }
            }
          } else if (data[i].type === 'Year Built') {
            if (!data[i].x.includes('<') && !data[i].x.includes('>')) {
              const values = data[i].x.split('-').map(Number);
              const subjectValue = subjectProperty.year_built;
              const match =
                subjectValue >= values[0] && subjectValue <= values[1]
                  ? 'true'
                  : 'false';
              data[i].match = match;
            } else {
              if (data[i].x.includes('<')) {
                const value = Number(data[i].x.split('<')[1]);
                const subjectValue = subjectProperty.year_built;
                const match = subjectValue < value ? 'true' : 'false';
                data[i].match = match;
              } else if (data[i].x.includes('>')) {
                const value = Number(data[i].x.split('>')[1]);
                const subjectValue = subjectProperty.year_built;
                const match = subjectValue > value ? 'true' : 'false';
                data[i].match = match;
              }
            }
          }
        }

        setGraphData(data);
        setUpdateChart(true);
      } else {
        setGraphData([]);
      }
    } else {
      if (eventCoordinates.length == 0 || drawnCustomPolygons.length == 0) {
        setGraphData([]);
      }
    }
    setLoading(false);
  }, [
    // loading,
    subjectProperty,
    parcelData,
    // currentMLSProperties,
    // currentNationalOperatorsProperties,
    // currentHotPadsProperties,
  ]);

  useEffect(() => {
    if (updateChart) setUpdateChart(false);
  }, [updateChart]);

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
      {((graphData.length > 0 && !loading) || loading) && (
        <div style={{ position: 'relative' }}>
          {/* <small>* Based on all comps in the area.</small> */}
          <Chart
            data={graphData}
            loading={loading}
            updateChart={updateChart}
            yAxisLabel="# of Homes"
          />
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'center',
              gap: '10px',
            }}
          >
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: '5px',
                alignItems: 'center',
              }}
            >
              <div
                style={{
                  width: '10px',
                  height: '10px',
                  backgroundColor: '#e200ff',
                }}
              ></div>
              <span style={{ fontSize: '12px' }}>Subject Property</span>
            </div>
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: '5px',
                alignItems: 'center',
              }}
            >
              <div
                style={{
                  width: '10px',
                  height: '10px',
                  backgroundColor: '#5B8FF9',
                }}
              ></div>
              <span style={{ fontSize: '12px' }}>Nearby Properties</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UnitMixParcel;
