import { scaleLinear } from 'd3-scale';
import isEmpty from 'lodash.isempty';
import { useEffect, useState } from 'react';
import { useSelector } from 'umi';
import {
  filterTableDataSource,
  generateManualFilterExpression,
  generateManualFilterValues,
} from '../../Filters/filterFunctions';
import filterValuesDefault from '../../Filters/filterValuesDefault.json';
import Chart from './Chart';

const year = new Date().getFullYear();

const calculateSqftBands = (data, type) => {
  let fixedBars;
  if (type === 'Square Feet') {
    fixedBars = [
      { min: 0, max: 500, count: 0 },
      { min: 501, max: 1000, count: 0 },
      { min: 1001, max: 1500, count: 0 },
      { min: 1501, max: 2000, count: 0 },
      { min: 2001, max: 2500, count: 0 },
      { min: 2501, max: 3000, count: 0 },
      { min: 3001, max: 3500, count: 0 },
      { min: 3501, max: 4000, count: 0 },
      { min: 4001, max: 4500, count: 0 },
      { min: 4501, max: Infinity, count: 0 },
    ];
  } else {
    const maxYear = Math.floor(year / 10) * 10;
    const minMaxYear = maxYear - 60;
    fixedBars = [
      { min: 0, max: minMaxYear, count: 0 },
      { min: minMaxYear + 1, max: minMaxYear + 10, count: 0 },
      { min: minMaxYear + 11, max: minMaxYear + 20, count: 0 },
      { min: minMaxYear + 21, max: minMaxYear + 30, count: 0 },
      { min: minMaxYear + 31, max: minMaxYear + 40, count: 0 },
      { min: minMaxYear + 41, max: minMaxYear + 50, count: 0 },
      { min: minMaxYear + 51, max: Infinity, count: 0 },
    ];
    console.log('year FixedBars', fixedBars);
  }

  for (let i = 0; i < data.length; i++) {
    for (let j = 0; j < fixedBars.length; j++) {
      if (data[i] >= fixedBars[j].min && data[i] <= fixedBars[j].max) {
        fixedBars[j].count++;
        break;
      }
    }
  }

  const sum = fixedBars.reduce((a, b) => a + b.count, 0);

  const result = fixedBars
    .map((bar) => {
      let xLabel;
      if (bar.min === 0) {
        xLabel = `<${bar.max}`;
      } else if (bar.max === Infinity) {
        xLabel = `>${bar.min}`;
      } else {
        xLabel = `${bar.min}-${bar.max}`;
      }

      return {
        x: xLabel,
        y: bar.count,
        percent: (bar.count / sum) * 100,
        type: type,
      };
    })
    .filter((bar) => bar.y !== 0);

  return result;
};

// Calculate the groups/ranges for chart bands
const calculateBandGroups = (data, rangeSize, type) => {
  // Determine the minimum and maximum values in the data
  const min = Math.min(...data);
  const max = Math.max(...data);

  // Determine the number of groups based on the range size
  const numGroups = Math.ceil((max - min + 1) / rangeSize);

  // Create a D3 scale for the square footage
  const bandScale = scaleLinear()
    .domain([min, max])
    .range([0, numGroups - 1]);

  // Calculate the groups/ranges and their occurrences
  const groups = {};
  data.forEach((d) => {
    const rangeStart = Math.floor(bandScale(d)) * rangeSize + min;
    const rangeEnd = rangeStart + rangeSize - 1;
    const range = [rangeStart, rangeEnd];

    if (!groups.hasOwnProperty(range.toString())) {
      groups[range.toString()] = 1;
    } else {
      groups[range.toString()]++;
    }
  });

  console.log(groups);
  const totalUnits = Object.values(groups).reduce((a, b) => a + b, 0);
  // groups.sort((a, b) => a.range[0] - b.range[0]);
  const result = Object.entries(groups).map(([range, occurrence]) => ({
    x: range.replace(',', '-'),
    y: occurrence,
    percent: (occurrence / totalUnits) * 100,
    type: type,
  }));

  result.sort((a, b) => {
    const aRange = Number(a.x.split('-')[0]);
    const bRange = Number(b.x.split('-')[0]);
    return aRange - bRange;
  });

  return result;
};

// based on comp selection ALL or manual filter
const filterData = (data, type) => {
  let filteredData = [];
  if (type === 'MLS') {
    const mlsFilterExpression = generateManualFilterExpression(
      generateManualFilterValues('MLS', filterValuesDefault),
    );
    filteredData = filterTableDataSource(mlsFilterExpression, data);
  } else if (type === 'SFR') {
    const sfrFilterExpression = generateManualFilterExpression(
      generateManualFilterValues('SFR', filterValuesDefault),
    );
    filteredData = filterTableDataSource(sfrFilterExpression, data);
  }
  return filteredData;
};

const UnitMixGraph = () => {
  const [loading, setLoading] = useState(false);
  const [graphData, setGraphData] = useState([]);

  const subjectProperty = useSelector(
    (state) => state.CMA.subjectPropertyParcelData,
  );
  const currentMLSProperties = useSelector(
    (state) => state.CMA.currentMLSProperties,
  );
  const currentNationalOperatorsProperties = useSelector(
    (state) => state.CMA.currentNationalOperatorsProperties,
  );
  const currentHotPadsProperties = useSelector(
    (state) => state.CMA.currentHotPadsProperties,
  );
  const fetchCompMLSDone = useSelector((state) => state.CMA.fetchCompMLSDone);
  const fetchCompSFRDone = useSelector((state) => state.CMA.fetchCompSFRDone);
  const fetchCompHotPadsDone = useSelector(
    (state) => state.CMA.fetchCompHotPadsDone,
  );
  const searchingMode = useSelector((state) => state.CMA.searchingMode);

  // MLS keys: bed, bath, size, yearbuilt, lot_size

  useEffect(() => {
    if (
      ((searchingMode === 'Lease' &&
        fetchCompMLSDone &&
        fetchCompSFRDone &&
        fetchCompHotPadsDone) ||
        (searchingMode === 'Sale' && fetchCompMLSDone)) &&
      subjectProperty
    ) {
      setLoading(false);
    } else {
      setLoading(true);
    }
  }, [
    searchingMode,
    fetchCompMLSDone,
    fetchCompSFRDone,
    fetchCompHotPadsDone,
    subjectProperty,
  ]);

  useEffect(() => {
    if (!loading) {
      if (!Object.keys(subjectProperty) && graphData.length) {
        setGraphData([]);
        return;
      }

      const bed = [];
      const bath = [];
      const sqft = [];
      const yearBuilt = [];
      const lotSize = [];

      const mlsProperties = filterData(currentMLSProperties, 'MLS');
      const sfrOperatorsProperties = filterData(
        currentNationalOperatorsProperties,
        'SFR',
      );
      const thirdPartyProperties = filterData(currentHotPadsProperties, 'SFR');

      for (let i = 0; i < mlsProperties.length; i++) {
        if (mlsProperties[i].bed) bed.push(mlsProperties[i].bed);
        if (mlsProperties[i].bath) bath.push(mlsProperties[i].bath);
        if (mlsProperties[i].size) sqft.push(mlsProperties[i].size);
        if (mlsProperties[i].lot_size) lotSize.push(mlsProperties[i].lot_size);
        if (mlsProperties[i].yearbuilt)
          yearBuilt.push(mlsProperties[i].yearbuilt);
      }

      for (let i = 0; i < sfrOperatorsProperties.length; i++) {
        if (sfrOperatorsProperties[i].bed_rooms)
          bed.push(sfrOperatorsProperties[i].bed_rooms);
        if (sfrOperatorsProperties[i].bath_rooms)
          bath.push(sfrOperatorsProperties[i].bath_rooms);
        if (sfrOperatorsProperties[i].square_feet)
          sqft.push(sfrOperatorsProperties[i].square_feet);
        if (sfrOperatorsProperties[i].yearbuilt)
          yearBuilt.push(sfrOperatorsProperties[i].yearbuilt);

        // lotSize.push(sfrOperatorsProperties[i].lot_size);
      }

      for (let i = 0; i < thirdPartyProperties.length; i++) {
        if (thirdPartyProperties[i].bed_rooms)
          bed.push(thirdPartyProperties[i].bed_rooms);
        if (thirdPartyProperties[i].bath_rooms)
          bath.push(thirdPartyProperties[i].bath_rooms);
        if (thirdPartyProperties[i].square_feet)
          sqft.push(thirdPartyProperties[i].square_feet);
        if (thirdPartyProperties[i].yearbuilt) {
          yearBuilt.push(thirdPartyProperties[i].yearbuilt);
        }
        // lotSize.push(thirdPartyProperties.data[i].lot_size);
      }

      // if (!isEmpty(subjectProperty)) {
      //   if (subjectProperty.beds_count) bed.push(subjectProperty.beds_count);
      //   if (subjectProperty.baths) bath.push(subjectProperty.baths);
      //   if (subjectProperty.total_area_sq_ft)
      //     sqft.push(subjectProperty.total_area_sq_ft);
      //   if (subjectProperty.year_built) {
      //     yearBuilt.push(subjectProperty.year_built);
      //   }
      // }

      const data = [];

      let bedData = new Map();
      for (let i = 0; i < bed.length; i++) {
        if (bed[i] < 6) {
          if (bedData.has(bed[i])) {
            bedData.set(bed[i], bedData.get(bed[i]) + 1);
          } else {
            bedData.set(bed[i], 1);
          }
        } else {
          if (bedData.has('6')) {
            bedData.set('6', bedData.get('6') + 1);
          } else {
            bedData.set('6', 1);
          }
        }
      }

      bedData = new Map([...bedData].sort());
      const bedTotal = bedData.reduce((a, b) => a + b, 0);
      for (let [key, value] of bedData.entries()) {
        if (key && value !== 0) {
          data.push({
            x: key === '6' ? '6+' : key.toString(),
            y: value,
            percent: (value / bedTotal) * 100,
            type: 'Bedrooms',
          });
        }
      }

      let bathData = new Map();
      for (let i = 0; i < bath.length; i++) {
        if (bath[i] < 6) {
          if (bathData.has(Math.floor(bath[i]))) {
            bathData.set(
              Math.floor(bath[i]),
              bathData.get(Math.floor(bath[i])) + 1,
            );
          } else {
            bathData.set(Math.floor(bath[i]), 1);
          }
        } else {
          if (bathData.has('6')) {
            bathData.set('6', bathData.get('6') + 1);
          } else {
            bathData.set('6', 1);
          }
        }
      }
      bathData = new Map([...bathData].sort());
      const bathTotal = bathData.reduce((a, b) => a + b, 0);
      for (let [key, value] of bathData.entries()) {
        if (key && value !== 0) {
          data.push({
            x: key === '6' ? '6+' : key.toString(),
            y: value,
            percent: (value / bathTotal) * 100,
            type: 'Bathrooms',
          });
        }
      }

      // const sqftData = calculateBandGroups(sqft, 250, 'Square Feet');
      // data.push(...sqftData);
      const sqftData = calculateSqftBands(sqft, 'Square Feet');
      data.push(...sqftData);

      // const yearBuiltData = calculateBandGroups(yearBuilt, 5, 'Year Built');
      // data.push(...yearBuiltData);
      const yearBuiltData = calculateSqftBands(yearBuilt, 'Year Built');
      data.push(...yearBuiltData);

      // console.log('bed', bed);
      // console.log('bath', bath);
      // console.log('sqft', sqft);
      // console.log('age', age);
      // console.log('lotSize', lotSize);

      // console.log('subjectProperty', subjectProperty);

      for (let i = 0; i < data.length; i++) {
        if (data[i].type === 'Bedrooms' || data[i].type === 'Bathrooms') {
          const subjectType =
            data[i].type === 'Bedrooms' ? 'beds_count' : 'baths';
          if (data[i].x === '6+') {
            const value = 6;
            const match =
              Math.floor(subjectProperty[subjectType]) >= value
                ? 'true'
                : 'false';
            data[i].match = match;
          } else {
            const value = Number(data[i].x);
            const match =
              Math.floor(subjectProperty[subjectType]) === value
                ? 'true'
                : 'false';
            data[i].match = match;
          }
        } else if (data[i].type === 'Square Feet') {
          if (!data[i].x.includes('<') && !data[i].x.includes('>')) {
            const values = data[i].x.split('-').map(Number);
            const subjectValue = subjectProperty.total_area_sq_ft;
            const match =
              subjectValue >= values[0] && subjectValue <= values[1]
                ? 'true'
                : 'false';
            data[i].match = match;
          } else {
            if (data[i].x.includes('<')) {
              const value = Number(data[i].x.split('<')[1]);
              const subjectValue = subjectProperty.total_area_sq_ft;
              const match = subjectValue < value ? 'true' : 'false';
              data[i].match = match;
            } else if (data[i].x.includes('>')) {
              const value = Number(data[i].x.split('>')[1]);
              const subjectValue = subjectProperty.total_area_sq_ft;
              const match = subjectValue > value ? 'true' : 'false';
              data[i].match = match;
            }
          }
        } else if (data[i].type === 'Year Built') {
          if (!data[i].x.includes('<') && !data[i].x.includes('>')) {
            const values = data[i].x.split('-').map(Number);
            const subjectValue = subjectProperty.year_built;
            const match =
              subjectValue >= values[0] && subjectValue <= values[1]
                ? 'true'
                : 'false';
            data[i].match = match;
          } else {
            if (data[i].x.includes('<')) {
              const value = Number(data[i].x.split('<')[1]);
              const subjectValue = subjectProperty.year_built;
              const match = subjectValue < value ? 'true' : 'false';
              data[i].match = match;
            } else if (data[i].x.includes('>')) {
              const value = Number(data[i].x.split('>')[1]);
              const subjectValue = subjectProperty.year_built;
              const match = subjectValue > value ? 'true' : 'false';
              data[i].match = match;
            }
          }
        }
      }

      setGraphData(data);
    }
  }, [
    loading,
    subjectProperty,
    // currentMLSProperties,
    // currentNationalOperatorsProperties,
    // currentHotPadsProperties,
  ]);

  console.log('graphData: ', graphData);
  console.log('graphData: loading', loading);

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
      {((graphData.length > 0 && !loading) || loading) && (
        <div style={{ position: 'relative' }}>
          <small>* Based on all comps in the area.</small>
          <Chart data={graphData} loading={loading} yAxisLabel="# of Comps" />
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'center',
              gap: '10px',
            }}
          >
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: '5px',
                alignItems: 'center',
              }}
            >
              <div
                style={{
                  width: '10px',
                  height: '10px',
                  backgroundColor: '#e200ff',
                }}
              ></div>
              <span style={{ fontSize: '12px' }}>Subject Property</span>
            </div>
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: '5px',
                alignItems: 'center',
              }}
            >
              <div
                style={{
                  width: '10px',
                  height: '10px',
                  backgroundColor: '#5B8FF9',
                }}
              ></div>
              <span style={{ fontSize: '12px' }}>Nearby Properties</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UnitMixGraph;
