import { Mix } from '@ant-design/charts';
import isEqual from 'lodash.isequal';
import React, { memo } from 'react';

const matchColor = '#e200ff';
const otherColor = '#5B8FF9';

const Chart = memo(
  ({ data, yAxisLabel, loading }) => {
    const bedroom = data.filter((d) => d.type === 'Bedrooms') || [];
    const bathroom = data.filter((d) => d.type === 'Bathrooms') || [];
    const sqft = data.filter((d) => d.type === 'Square Feet') || [];
    const yearBuilt = data.filter((d) => d.type === 'Year Built') || [];

    const labelConfig = {
      position: 'top',
      offsetY: 10,
      style: {
        fill: '#000',
        opacity: 0.6,
      },
      formatter: ({ percent }) => {
        if (percent > 1) {
          return Math.round(percent) + '%';
        }
        return '';
        // return percent.toFixed(2) + '%';
      },
    };

    const config = {
      loading,
      height: 350,
      padding: 'auto',
      tooltip: {
        customContent: (title, data) => {
          if (data.length === 0) return null;
          const type = data[0].data.type;
          const x = data[0].data.x;
          const y = data[0].data.y;
          return (
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                padding: '10px 0',
                gap: '10px',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  gap: '20px',
                }}
              >
                <span>{type}:</span>
                <span style={{ fontWeight: 'bold' }}>{x}</span>
              </div>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  gap: '20px',
                }}
              >
                <span>Count:</span>
                <span style={{ fontWeight: 'bold' }}>{y}</span>
              </div>
            </div>
          );
        },
      },
      plots: [
        {
          type: 'column',
          region: {
            start: { x: 0, y: 0 },
            end: { x: 0.45, y: 0.45 },
          },
          options: {
            data: bedroom,
            xField: 'x',
            yField: 'y',
            seriesField: 'match',
            label: labelConfig,
            appendPadding: [15, 0, 0, 0],
            color: ({ match }) => {
              if (match === 'true') {
                return matchColor;
              }
              return otherColor;
            },
            ...(yAxisLabel && yAxisLabel.length > 0
              ? {
                  yAxis: {
                    title: {
                      text: yAxisLabel,
                      style: {
                        fontSize: 14,
                      },
                      spacing: 3,
                    },
                  },
                }
              : {}),
            xAxis: {
              title: {
                text: 'Bedrooms',
                spacing: 3,
              },
            },
          },
        },
        {
          type: 'column',
          region: {
            start: { x: 0.55, y: 0 },
            end: { x: 1, y: 0.45 },
          },
          options: {
            data: bathroom,
            xField: 'x',
            yField: 'y',
            seriesField: 'match',
            label: labelConfig,
            appendPadding: [15, 0, 0, 0],
            color: ({ match }) => {
              if (match === 'true') {
                return matchColor;
              }
              return otherColor;
            },
            ...(yAxisLabel && yAxisLabel.length > 0
              ? {
                  yAxis: {
                    title: {
                      text: yAxisLabel,
                      style: {
                        fontSize: 14,
                      },
                      spacing: 3,
                    },
                  },
                }
              : {}),
            xAxis: {
              title: {
                text: 'Bathrooms',
                spacing: 3,
              },
            },
          },
        },
        {
          type: 'column',
          region: {
            start: { x: 0, y: 0.55 },
            end: { x: 0.45, y: 1 },
          },
          options: {
            data: sqft,
            xField: 'x',
            yField: 'y',
            seriesField: 'match',
            label: labelConfig,
            appendPadding: [15, 0, 0, 0],
            color: ({ match }) => {
              if (match === 'true') {
                return matchColor;
              }
              return otherColor;
            },
            ...(yAxisLabel && yAxisLabel.length > 0
              ? {
                  yAxis: {
                    title: {
                      text: yAxisLabel,
                      style: {
                        fontSize: 14,
                      },
                      spacing: 3,
                    },
                  },
                }
              : {}),
            xAxis: {
              title: {
                text: 'Square Feet',
                spacing: 3,
              },
            },
          },
        },
        {
          type: 'column',
          region: {
            start: { x: 0.55, y: 0.55 },
            end: { x: 1, y: 1 },
          },
          options: {
            data: yearBuilt,
            xField: 'x',
            yField: 'y',
            seriesField: 'match',
            label: labelConfig,
            appendPadding: [15, 0, 0, 0],
            color: ({ match }) => {
              if (match === 'true') {
                return matchColor;
              }
              return otherColor;
            },
            ...(yAxisLabel && yAxisLabel.length > 0
              ? {
                  yAxis: {
                    title: {
                      text: yAxisLabel,
                      style: {
                        fontSize: 14,
                      },
                      spacing: 3,
                    },
                  },
                }
              : {}),
            xAxis: {
              title: {
                text: 'Year Built',
                spacing: 3,
              },
            },
          },
        },
      ],
    };

    return <Mix {...config} />;
  },
  (pre, next) => {
    // return isEqual(pre.updateChart, next.updateChart);
    return (
      isEqual(pre.loading, next.loading) &&
      isEqual(pre.updateChart, next.updateChart)
    );
  },
);

export default Chart;
