import { formatCurrency } from '@/utils/lastSalePublicRecord';
import { EyeInvisibleOutlined, EyeOutlined } from '@ant-design/icons';
import { Button, Col, Row, Select, Switch } from 'antd';
import { useEffect, useState } from 'react';
import { connect, useDispatch } from 'umi';
import { TAffordableHousing } from './AHPanel';
import { filterAffordableHousing } from './utils/ah';

const AHFilter = connect(({ CMA }: any) => ({
  currentPropertyAddress: CMA.currentPropertyAddress,
  currentRadiusMile: CMA.currentRadiusMile,
  currentMapLayerOptions: CMA.currentMapLayerOptions,
  affordableHousingData: CMA.affordableHousingData,
  affordableHousingDataForRender: CMA.affordableHousingDataForRender,
  affordableHousingMedian: CMA.affordableHousingMedian,
  affordableHousingSelectedRowKey: CMA.affordableHousingSelectedRowKey,
  affordableHousingMedianPricePerSqft: CMA.affordableHousingMedianPricePerSqft,
  subjectPropertyParcelData: CMA.subjectPropertyParcelData,
}))(function (props: any) {
  const dispatch = useDispatch();

  const statusOptions = [
    { value: 'all', label: 'All Status' },
    ...[
      ...new Set(
        props.affordableHousingData.map(
          (house: TAffordableHousing) => house.status,
        ),
      ),
    ].map((status) => ({ value: status, label: status })),
  ];

  const propertyTypeOptions = [
    { value: 'all', label: 'All Property Type' },
    ...[
      ...new Set(
        props.affordableHousingData.map(
          (house: TAffordableHousing) => house.propertyTypeCategory,
        ),
      ),
    ].map((type) => ({ value: type, label: type })),
  ];

  const handleChangeStatus = (value: string) => {
    console.log(`selected ${value}`);
    if (value !== 'all') {
      const newDFR = props.affordableHousingData.filter(
        (house: TAffordableHousing) => {
          return house.status === value;
        },
      );
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          affordableHousingDataForRender: newDFR,
        },
      });
    } else {
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          affordableHousingDataForRender: props.affordableHousingData,
        },
      });
    }
  };
  const handleChangePropertyType = (value: string) => {
    console.log(`selected ${value}`);
    if (value !== 'all') {
      const newDFR = props.affordableHousingData.filter(
        (house: TAffordableHousing) => {
          return house.propertyTypeCategory === value;
        },
      );
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          affordableHousingDataForRender: newDFR,
        },
      });
    } else {
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          affordableHousingDataForRender: props.affordableHousingData,
        },
      });
    }
  };

  useEffect(() => {}, [props.affordableHousingDataForRender]);
  const [filterOpened, setFilterOpened] = useState<boolean>(false);
  const [matchMode, setmatchMode] = useState<boolean>(false);
  const onChangeMatchMode = (checked: boolean) => {
    console.log(`switch to ${checked}`);
    setmatchMode(checked);
  };
  const onClickFilter = () => {
    setFilterOpened(!filterOpened);
  };

  useEffect(() => {
    if (matchMode) {
      // Match mode
      const filtered = filterAffordableHousing(
        props.subjectPropertyParcelData,
        props.affordableHousingData,
      );
      console.log('matchMode', filtered);
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          affordableHousingDataForRender: filtered,
        },
      });
    } else {
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          affordableHousingDataForRender: props.affordableHousingData,
        },
      });
    }
  }, [matchMode]);
  return (
    <div className="mb-2">
      <Row>
        <Col span={12}>
          {/* <Button onClick={onClickFilter}>
            {!filterOpened ? (
              <>
                <EyeOutlined /> Filter{' '}
              </>
            ) : (
              <>
                <EyeInvisibleOutlined /> Filter
              </>
            )}
          </Button> */}
        </Col>
        <Col span={12} className="pt-2">
          <Row>
            <p className="pr-2">
              Total: <b>{props.affordableHousingSelectedRowKey.length}</b>
            </p>
            <p className="pr-2">
              Median Rent:{' '}
              <b>
                {props.affordableHousingMedian
                  ? formatCurrency(props.affordableHousingMedian)
                  : '-'}
              </b>
            </p>
            <p>
              Median Rent Per Sqft:{' '}
              <b>
                $
                {props.affordableHousingMedianPricePerSqft
                  ? props.affordableHousingMedianPricePerSqft.toFixed(2)
                  : '-'}
              </b>
            </p>
          </Row>
        </Col>
      </Row>

      {filterOpened && (
        <Row className="my-2">
          <Col span={4}>
            <Select
              className="w-36"
              defaultValue={statusOptions[0].value as string} // Explicitly cast to string
              options={statusOptions}
              onChange={handleChangeStatus}
            />
          </Col>
          <Col span={6}>
            <Select
              className="w-48"
              onChange={handleChangePropertyType}
              defaultValue={propertyTypeOptions[0].value as string}
              options={propertyTypeOptions}
            />
          </Col>
          <Col span={2}>
            <div className="pt-1">
              <Switch
                checkedChildren="Match"
                unCheckedChildren="All"
                onChange={onChangeMatchMode}
                defaultValue={matchMode}
              />
            </div>
          </Col>
          <Col span={2}>
            {/* {' '}
            <Switch
              checkedChildren="开启"
              unCheckedChildren="关闭"
              onChange={onChangeMatchMode}
              defaultValue={matchMode}
            /> */}
          </Col>
        </Row>
      )}

      {/* Status */}
    </div>
  );
});

export default AHFilter;
