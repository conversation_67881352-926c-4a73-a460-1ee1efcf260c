import { formatCurrency } from '@/utils/lastSalePublicRecord';
import { SettingOutlined } from '@ant-design/icons';
import { Button, Col, Row, Tooltip } from 'antd';
import React from 'react';
import { ColumnConfig } from '../ColumnManagerModal';

interface AffordableHousingHeaderProps {
  selectedRowKeysLength: number;
  median: number | null;
  medianPerSqft: number | null;
  onColumnSettingsClick: () => void;
  onExportCSV: () => void;
  csvLoading: boolean;
  dataLength: number;
}

const AffordableHousingHeader: React.FC<AffordableHousingHeaderProps> = ({
  selectedRowKeysLength,
  median,
  medianPerSqft,
  onColumnSettingsClick,
  onExportCSV,
  csvLoading,
  dataLength,
}) => {
  return (
    <Row align="middle" justify="space-between" style={{ marginBottom: 12 }}>
      <Col>
        <p className="text-lg mb-2 font-semibold">Affordable Housing</p>
      </Col>

      <Col>
        <Row align="middle" gutter={24}>
          <Col>
            <span>Total: </span>
            <span className="font-bold">{selectedRowKeysLength}</span>
          </Col>
          <Col>
            <span>Median Rent: </span>
            <span className="font-bold">
              {median ? formatCurrency(median) : '-'}
            </span>
          </Col>
          <Col>
            <span>Median Rent Per Sqft: </span>
            <span className="font-bold">
              ${medianPerSqft ? medianPerSqft.toFixed(2) : '-'}
            </span>
          </Col>
          <Col>
            <Tooltip title="Column Customization">
              <Button
                icon={<SettingOutlined />}
                onClick={onColumnSettingsClick}
              />
            </Tooltip>
          </Col>
          <Col>
            <Button
              type="default"
              loading={csvLoading}
              onClick={onExportCSV}
              disabled={dataLength === 0}
            >
              Export CSV
            </Button>
          </Col>
        </Row>
      </Col>
    </Row>
  );
};

export default AffordableHousingHeader;
