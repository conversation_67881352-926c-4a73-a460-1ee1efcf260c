import { TAffordableHousing, TFilter } from './types';

export function isEmpty(obj: Object) {
  return Object.keys(obj).length === 0 && obj.constructor === Object;
}

export function filterAffordableData(
  data: TAffordableHousing[],
  filter: TFilter,
): TAffordableHousing[] {
  return data.filter((house) => {
    const matchesBeds =
      house.beds >= filter.minBeds && house.beds <= filter.maxBeds;
    const matchesBaths =
      house.baths >= filter.minBaths && house.baths <= filter.maxBaths;
    const matchesSqft =
      house.sqft >= filter.minSqft && house.sqft <= filter.maxSqft;

    // Zip code filter
    const matchesZipCode = filter.isZipCodeFilterOn
      ? house.zip === filter.zipCode?.toString()
      : true;

    // Date range filter for last_seen_mls
    const matchesDateRange = (() => {
      // Skip date filtering if filter dates are not properly defined
      if (!filter.currentStartMLS || !filter.currentEndMLS) {
        return true; // Don't filter by date if date range is not set
      }

      if (!house.last_seen_mls) return false; // Skip if last_seen_mls is null/undefined

      try {
        const lastSeenDate = new Date(house.last_seen_mls);
        const startDate = new Date(filter.currentStartMLS);
        const endDate = new Date(filter.currentEndMLS);
        const today = new Date();

        // Check if dates are valid
        if (
          isNaN(lastSeenDate.getTime()) ||
          isNaN(startDate.getTime()) ||
          isNaN(endDate.getTime())
        ) {
          return false;
        }

        // Filter out records with invalid future dates (more than 1 day in the future to account for timezone differences)
        const oneDayFromNow = new Date(today);
        oneDayFromNow.setDate(today.getDate() + 1);
        if (lastSeenDate > oneDayFromNow) {
          return false;
        }

        // Normalize dates to remove time component for accurate comparison
        const normalizedLastSeen = new Date(
          lastSeenDate.getFullYear(),
          lastSeenDate.getMonth(),
          lastSeenDate.getDate(),
        );
        const normalizedStart = new Date(
          startDate.getFullYear(),
          startDate.getMonth(),
          startDate.getDate(),
        );
        const normalizedEnd = new Date(
          endDate.getFullYear(),
          endDate.getMonth(),
          endDate.getDate(),
        );

        return (
          normalizedLastSeen >= normalizedStart &&
          normalizedLastSeen <= normalizedEnd
        );
      } catch (error) {
        console.warn('Error parsing date in affordable housing filter:', error);
        return false;
      }
    })();

    return (
      matchesBeds &&
      matchesBaths &&
      matchesSqft &&
      matchesZipCode &&
      matchesDateRange
    );
  });
}

export function calculateMedianRentsByBedroom(data: TAffordableHousing[]) {
  // Group rents by bedroom count
  const rentsByBedroom: { [key: number]: number[] } = {
    2: [],
    3: [],
    4: [],
  };

  // Collect all rents for each bedroom type
  data.forEach((item) => {
    if (item.beds in rentsByBedroom && item.rent) {
      rentsByBedroom[item.beds].push(item.rent);
    }
  });

  // Function to calculate median
  const getMedian = (arr: number[]) => {
    if (arr.length === 0) return '';
    const sorted = [...arr].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);

    if (sorted.length % 2 === 0) {
      return `$${Math.round(
        (sorted[mid - 1] + sorted[mid]) / 2,
      ).toLocaleString()}`;
    }
    return `$${Math.round(sorted[mid]).toLocaleString()}`;
  };

  // Calculate median rents
  return {
    2: getMedian(rentsByBedroom[2]),
    3: getMedian(rentsByBedroom[3]),
    4: getMedian(rentsByBedroom[4]),
  };
}
