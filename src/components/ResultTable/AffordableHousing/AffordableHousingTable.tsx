import { formatCurrency } from '@/utils/lastSalePublicRecord';
import { FileImageOutlined } from '@ant-design/icons';
import { Table, Tooltip } from 'antd';
import { ColumnsType } from 'antd/es/table';
import React, { useCallback, useRef } from 'react';
import { TAffordableHousing } from './types';

interface AffordableHousingTableProps {
  data: TAffordableHousing[];
  loading: boolean;
  selectedRowKeys: React.Key[];
  onSelectChange: (selectedRowKeys: React.Key[]) => void;
  onRowEvents: (record: TAffordableHousing, rowIndex: number) => any;
  onTableChange: (
    pagination: any,
    filters: any,
    sorter: any,
    extra: any,
  ) => void;
  scrollX: number;
  columnConfigs: any[];
  statusOptions: any[];
  propertyTypeOptions: any[];
  imageDetailTooltipContainer: React.MutableRefObject<any>;
}

const AffordableHousingTable: React.FC<AffordableHousingTableProps> = ({
  data,
  loading,
  selectedRowKeys,
  onSelectChange,
  onRowEvents,
  onTableChange,
  scrollX,
  columnConfigs,
  statusOptions,
  propertyTypeOptions,
  imageDetailTooltipContainer,
}) => {
  // Generate columns based on configuration
  const generateColumns = useCallback((): ColumnsType<TAffordableHousing> => {
    const containerRef = useRef(null);

    const allColumns: ColumnsType<TAffordableHousing> = [
      {
        title: 'Address',
        dataIndex: 'address',
        key: 'address',
        fixed: 'left',
        width: 250,
        align: 'left',
        render: (text: any, record: TAffordableHousing) => (
          <div ref={containerRef} className="image-detail-tooltip-container">
            {record.photoLink ? (
              <Tooltip
                title={
                  <div
                    style={{
                      width: '200px',
                      height: '150px',
                      overflow: 'hidden',
                    }}
                  >
                    <img
                      src={record.photoLink}
                      alt="Property"
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover',
                      }}
                    />
                  </div>
                }
                placement="top"
                getPopupContainer={() =>
                  imageDetailTooltipContainer?.current || containerRef.current
                }
              >
                <a
                  href={record.housingurl}
                  className="text-blue-500 text-xs"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {record.address}, {record.city}, {record.state}, {record.zip}{' '}
                  <FileImageOutlined className="text-blue-500" />
                </a>
              </Tooltip>
            ) : (
              <a
                href={record.housingurl}
                className="text-blue-500 text-xs"
                target="_blank"
                rel="noopener noreferrer"
              >
                {record.address}, {record.city}, {record.state}, {record.zip}
              </a>
            )}
          </div>
        ),
      },
      {
        title: 'Price',
        dataIndex: 'rent',
        key: 'rent',
        align: 'left',
        width: 100,
        fixed: 'left',
        sorter: (a: TAffordableHousing, b: TAffordableHousing) =>
          (a.rent || 0) - (b.rent || 0),
        render: (text: any) => (
          <p className="text-xs">{formatCurrency(text)}</p>
        ),
      },
      {
        title: 'Dist.',
        dataIndex: 'distance',
        key: 'distance',
        width: 75,
        align: 'left',
        defaultSortOrder: 'ascend',
        sorter: (a: TAffordableHousing, b: TAffordableHousing) =>
          a.distance - b.distance,
        render: (text: any, record: TAffordableHousing) => (
          <p className="text-xs">{record.distance.toFixed(2)} mi</p>
        ),
      },
      {
        title: 'Status',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        align: 'left',
        render: (text: any) => <p className="text-xs">{text}</p>,
      },
      {
        title: 'Property Type',
        dataIndex: 'propertyTypeCategory',
        key: 'propertyTypeCategory',
        width: 150,
        align: 'left',
        render: (text: any) => <p className="text-xs">{text}</p>,
        filters: propertyTypeOptions,
      },
      {
        title: 'Year Built',
        dataIndex: 'yearBuilt',
        key: 'yearBuilt',
        width: 100,
        align: 'left',
        sorter: (a: TAffordableHousing, b: TAffordableHousing) =>
          a.yearBuilt - b.yearBuilt,
        render: (text: any) => <p className="text-xs">{text}</p>,
      },
      {
        title: 'Beds',
        dataIndex: 'beds',
        key: 'beds',
        width: 50,
        align: 'left',
        sorter: (a: TAffordableHousing, b: TAffordableHousing) =>
          a.beds - b.beds,
        render: (text: any) => <p className="text-xs">{text}</p>,
      },
      {
        title: 'Baths',
        dataIndex: 'baths',
        key: 'baths',
        width: 50,
        align: 'left',
        sorter: (a: TAffordableHousing, b: TAffordableHousing) =>
          a.baths - b.baths,
        render: (text: any, record: TAffordableHousing) => (
          <p className="text-xs">{text + record.halfBaths / 2}</p>
        ),
      },
      {
        title: 'Sqft',
        dataIndex: 'sqft',
        key: 'sqft',
        width: 75,
        align: 'left',
        sorter: (a: TAffordableHousing, b: TAffordableHousing) =>
          a.sqft - b.sqft,
        render: (text: any) => (
          <p className="text-xs">{Number(text).toLocaleString()}</p>
        ),
      },
      {
        title: 'DOM',
        dataIndex: 'dom',
        key: 'dom',
        width: 50,
        align: 'left',
        sorter: (a: TAffordableHousing, b: TAffordableHousing) => a.dom - b.dom,
        render: (text: any) => <p className="text-xs">{text}</p>,
      },
      {
        title: 'PSF',
        dataIndex: 'psf',
        key: 'psf',
        align: 'left',
        width: 50,
        sorter: (a: TAffordableHousing, b: TAffordableHousing) => a.psf - b.psf,
        render: (text: any, record: TAffordableHousing) => (
          <p className="text-xs">
            ${record.psf ? record.psf.toFixed(2) : 'N/A'}
          </p>
        ),
      },
      {
        title: 'Owner Name',
        dataIndex: 'owner_name',
        key: 'owner_name',
        align: 'left',
        width: 200,
        render: (text: any) => <p className="text-xs">{text}</p>,
      },
      {
        title: 'Last Updated',
        dataIndex: 'last_seen_mls',
        key: 'last_seen_mls',
        align: 'left',
        width: 100,
        render: (text: any, record: TAffordableHousing) => {
          const date = record.last_seen_mls
            ? new Date(record.last_seen_mls)
            : null;
          return (
            <p className="text-xs">
              {date instanceof Date ? date.toISOString().split('T')[0] : ''}
            </p>
          );
        },
      },
      {
        title: 'Sales Price',
        dataIndex: 'mls_close_price',
        key: 'mls_close_price',
        width: 100,
        align: 'left',
        render: (text: any, record: TAffordableHousing) => (
          <p className="text-xs">
            {record.mls_close_price ? formatCurrency(text) : ''}
          </p>
        ),
        sorter: (a: TAffordableHousing, b: TAffordableHousing) =>
          (a.mls_close_price || 0) - (b.mls_close_price || 0),
      },
      {
        title: 'Sales Status',
        dataIndex: 'mls_status',
        key: 'mls_status',
        width: 125,
        align: 'left',
        render: (text: any) => <p className="text-xs">{text}</p>,
        filters: statusOptions,
      },
      {
        title: 'Rent Price',
        dataIndex: 'real_estate_last_rent',
        key: 'real_estate_last_rent',
        width: 100,
        align: 'left',
        render: (text: any, record: TAffordableHousing) => (
          <p className="text-xs">
            {record.real_estate_last_rent ? formatCurrency(text) : ''}
          </p>
        ),
        sorter: (a: TAffordableHousing, b: TAffordableHousing) =>
          (a.real_estate_last_rent || 0) - (b.real_estate_last_rent || 0),
      },
      {
        title: 'Rent Status',
        dataIndex: 'real_estate_available',
        key: 'real_estate_available',
        width: 100,
        align: 'left',
        render: () => <p className="text-xs"></p>,
      },
    ];

    // Filter and sort columns based on configuration
    return allColumns
      .filter((col) => {
        const config = columnConfigs.find((c) => c.key === col.key);
        return config?.visible !== false;
      })
      .sort((a, b) => {
        const aIndex = columnConfigs.findIndex((c) => c.key === a.key);
        const bIndex = columnConfigs.findIndex((c) => c.key === b.key);
        return aIndex - bIndex;
      });
  }, [statusOptions, propertyTypeOptions, columnConfigs]);

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  return (
    <Table
      key="ah table"
      dataSource={data}
      columns={generateColumns()}
      rowKey={(record) => record.key}
      loading={loading}
      size="small"
      pagination={false}
      rowSelection={rowSelection}
      onChange={onTableChange}
      scroll={{ x: scrollX, y: '400px' }}
      onRow={onRowEvents}
    />
  );
};

export default AffordableHousingTable;
