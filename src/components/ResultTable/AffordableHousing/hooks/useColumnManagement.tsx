import {
  getUserTableColumnSettings,
  postSaveUserTableColumnSettings,
} from '@/services/data';
import { useAuthenticator } from '@aws-amplify/ui-react';
import { useCallback, useEffect, useState } from 'react';
import { useDispatch } from 'umi';
import { ColumnConfig } from '../../ColumnManagerModal';

interface UseColumnManagementProps {
  tableName: string;
  defaultColumns: ColumnConfig[];
  columnSettings: any;
  onSettingsUpdate: (settings: any) => void;
}

export const useColumnManagement = ({
  tableName,
  defaultColumns,
  columnSettings,
  onSettingsUpdate,
}: UseColumnManagementProps) => {
  const { user } = useAuthenticator();
  const dispatch = useDispatch();

  const [columnConfigs, setColumnConfigs] = useState<ColumnConfig[]>(
    columnSettings
      ? Array.isArray(columnSettings)
        ? columnSettings
        : JSON.parse(columnSettings)
      : defaultColumns,
  );

  const [scrollX, setScrollX] = useState(1200);

  // Fetch user table column settings
  const fetchColumnSettings = useCallback(async () => {
    if (!user?.username) return;

    try {
      const data = await getUserTableColumnSettings({
        username: user.username,
      });

      const settingsKey = tableName.replace(/_/g, '');
      if (data[settingsKey]) {
        onSettingsUpdate(data[settingsKey]);
      }
    } catch (err) {
      console.error('Error loading column settings:', err);
    }
  }, [user?.username, tableName, onSettingsUpdate]);

  // Save user column settings
  const saveColumnSettings = useCallback(
    async (updatedColumns: ColumnConfig[]) => {
      if (!user?.username) return;

      try {
        await postSaveUserTableColumnSettings({
          body: {
            username: user.username,
            tableName,
            settings: JSON.stringify(updatedColumns),
          },
        });

        onSettingsUpdate(updatedColumns);
      } catch (error) {
        console.error('Error saving column settings:', error);
      }
    },
    [user?.username, tableName, onSettingsUpdate],
  );

  // Update column configs when settings change
  useEffect(() => {
    if (columnSettings) {
      const parsedSettings = Array.isArray(columnSettings)
        ? columnSettings
        : JSON.parse(columnSettings);
      setColumnConfigs(parsedSettings);
    }
  }, [columnSettings]);

  // Fetch settings on mount
  useEffect(() => {
    fetchColumnSettings();
  }, [fetchColumnSettings]);

  // Calculate scroll width based on visible columns
  const updateScrollWidth = useCallback((visibleColumns: any[]) => {
    const totalWidth = visibleColumns.reduce(
      (sum, col) => sum + ((col.width as number) || 100),
      0,
    );
    setScrollX(Math.max(totalWidth, 1200));
  }, []);

  return {
    columnConfigs,
    setColumnConfigs,
    scrollX,
    updateScrollWidth,
    saveColumnSettings,
    fetchColumnSettings,
  };
};
