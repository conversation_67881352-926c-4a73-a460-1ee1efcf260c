import { useElementSize } from '@/hooks';
import {
  getAffordableHousingBuffer,
  getUserTableColumnSettings,
  postAffordableHousingCSV,
  postSaveUserTableColumnSettings,
} from '@/services/data';
import {
  calculateMedianLastSalePrice,
  calculateMedianPricePerSqFt,
  formatCurrency,
} from '@/utils/lastSalePublicRecord';
import { SettingOutlined } from '@ant-design/icons';
import { useAuthenticator } from '@aws-amplify/ui-react';
import { Button, Col, Row, Tooltip } from 'antd';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { connect, useDispatch } from 'umi';
import ColumnManagerModal, { ColumnConfig } from '../ColumnManagerModal';
import FairMarketRent from '../FairMarketRent/FairMarketRent';
import styles from '../resultTable.css';
import AffordableHousingTable from './AffordableHousingTable';
import { TAffordableHousing, TFilter } from './types';
import { filterAffordableHousing } from './utils/ah';

// Utility functions
function isEmpty(obj: Object) {
  return Object.keys(obj).length === 0 && obj.constructor === Object;
}

function filterAffordableData(
  data: TAffordableHousing[],
  filter: TFilter,
): TAffordableHousing[] {
  console.log('filterAffordableData filter', filter);

  return data.filter((house) => {
    const matchesBeds =
      house.beds >= filter.minBeds && house.beds <= filter.maxBeds;
    const matchesBaths =
      house.baths >= filter.minBaths && house.baths <= filter.maxBaths;
    const matchesSqft =
      house.sqft >= filter.minSqft && house.sqft <= filter.maxSqft;

    // Zip code filter
    const matchesZipCode = filter.isZipCodeFilterOn
      ? house.zip === filter.zipCode.toString()
      : true;

    // Date range filter for last_seen_mls
    const matchesDateRange = (() => {
      // Skip date filtering if filter dates are not properly defined
      if (!filter.currentStartMLS || !filter.currentEndMLS) {
        return true; // Don't filter by date if date range is not set
      }

      if (!house.last_seen_mls) return false;

      try {
        const lastSeenDate = new Date(house.last_seen_mls);
        const startDate = new Date(filter.currentStartMLS);
        const endDate = new Date(filter.currentEndMLS);
        const today = new Date();

        // Check if dates are valid
        if (
          isNaN(lastSeenDate.getTime()) ||
          isNaN(startDate.getTime()) ||
          isNaN(endDate.getTime())
        ) {
          return false;
        }

        // Filter out records with invalid future dates (more than 1 day in the future to account for timezone differences)
        const oneDayFromNow = new Date(today);
        oneDayFromNow.setDate(today.getDate() + 1);
        if (lastSeenDate > oneDayFromNow) {
          return false;
        }

        // Normalize dates to remove time component for accurate comparison
        const normalizedLastSeen = new Date(
          lastSeenDate.getFullYear(),
          lastSeenDate.getMonth(),
          lastSeenDate.getDate(),
        );
        const normalizedStart = new Date(
          startDate.getFullYear(),
          startDate.getMonth(),
          startDate.getDate(),
        );
        const normalizedEnd = new Date(
          endDate.getFullYear(),
          endDate.getMonth(),
          endDate.getDate(),
        );

        return (
          normalizedLastSeen >= normalizedStart &&
          normalizedLastSeen <= normalizedEnd
        );
      } catch (error) {
        console.warn('Error parsing date in affordable housing filter:', error);
        return false;
      }
    })();

    return (
      matchesBeds &&
      matchesBaths &&
      matchesSqft &&
      matchesZipCode &&
      matchesDateRange
    );
  });
}
function calculateMedianRentsByBedroom(data: TAffordableHousing[]) {
  const rentsByBedroom = { 2: [], 3: [], 4: [] };

  data.forEach((item) => {
    if (item.beds in rentsByBedroom && item.rent) {
      rentsByBedroom[item.beds].push(item.rent);
    }
  });

  const getMedian = (arr: number[]) => {
    if (arr.length === 0) return '';
    const sorted = [...arr].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);

    if (sorted.length % 2 === 0) {
      return `$${Math.round(
        (sorted[mid - 1] + sorted[mid]) / 2,
      ).toLocaleString()}`;
    }
    return `$${Math.round(sorted[mid]).toLocaleString()}`;
  };

  return {
    2: getMedian(rentsByBedroom[2]),
    3: getMedian(rentsByBedroom[3]),
    4: getMedian(rentsByBedroom[4]),
  };
}

const AffordableHousingPage = connect(({ CMA }: any) => ({
  map: CMA.map,
  currentPropertyAddress: CMA.currentPropertyAddress,
  currentRadiusMile: CMA.currentRadiusMile,
  affordableHousingData: CMA.affordableHousingData,
  affordableHousingDataForRender: CMA.affordableHousingDataForRender,
  affordableHousingSelectedRowKey: CMA.affordableHousingSelectedRowKey,
  affordableHousingMedian: CMA.affordableHousingMedian,
  affordableHousingMedianPricePerSqft: CMA.affordableHousingMedianPricePerSqft,
  affordableHousingColumnSettings: CMA.affordableHousingColumnSettings,
  compingMode: CMA.compingMode,
  currentStatusMLS: CMA.currentStatusMLS,
  minBeds: CMA.minBeds,
  maxBeds: CMA.maxBeds,
  minBaths: CMA.minBaths,
  maxBaths: CMA.maxBaths,
  minSqft: CMA.minSqft,
  maxSqft: CMA.maxSqft,
  currentStartMLS: CMA.currentStartMLS,
  currentEndMLS: CMA.currentEndMLS,
  subjectPropertyParcelData: CMA.subjectPropertyParcelData,
  rangeClosingDate: CMA.rangeClosingDate,
  isZipCodeFilterOn: CMA.isZipCodeFilterOn,
}))(function (props: any) {
  const dispatch = useDispatch();
  const { user } = useAuthenticator();

  const mlsContainer = useRef(null);
  const imageDetailTooltipContainer = useRef(null);
  const tableSize = useElementSize(mlsContainer);

  // State
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [CSVButtonLoading, setCSVButtonLoading] = useState<boolean>(false);
  const [isColumnModalVisible, setIsColumnModalVisible] = useState(false);
  const [scrollX, setScrollX] = useState(1200);
  const [activePropertyTypeFilters, setActivePropertyTypeFilters] = useState<
    string[]
  >([]);
  const [activeStatusFilters, setActiveStatusFilters] = useState<string[]>([]);

  // Column configurations
  const DEFAULT_COLUMNS: ColumnConfig[] = [
    { title: 'Address', key: 'address', visible: true },
    { title: 'Price', key: 'rent', visible: true },
    { title: 'Dist.', key: 'distance', visible: true },
    { title: 'Status', key: 'status', visible: true },
    { title: 'Property Type', key: 'propertyTypeCategory', visible: true },
    { title: 'Year Built', key: 'yearBuilt', visible: true },
    { title: 'Beds', key: 'beds', visible: true },
    { title: 'Baths', key: 'baths', visible: true },
    { title: 'Sqft', key: 'sqft', visible: true },
    { title: 'DOM', key: 'dom', visible: true },
    { title: 'PSF', key: 'psf', visible: true },
    { title: 'Owner Name', key: 'owner_name', visible: true },
    { title: 'Sales Price', key: 'mls_close_price', visible: true },
    { title: 'Sales Status', key: 'mls_status', visible: true },
    { title: 'Rent Price', key: 'real_estate_last_rent', visible: true },
    { title: 'Rent Status', key: 'real_estate_available', visible: true },
    { title: 'Last Updated', key: 'last_seen_mls', visible: true },
  ];

  const [columnConfigs, setColumnConfigs] = useState<ColumnConfig[]>(
    props.affordableHousingColumnSettings
      ? Array.isArray(props.affordableHousingColumnSettings)
        ? props.affordableHousingColumnSettings
        : JSON.parse(props.affordableHousingColumnSettings)
      : DEFAULT_COLUMNS,
  );

  // Filter options
  const statusOptions = [
    ...[
      ...new Set(
        props.affordableHousingData.map(
          (house: TAffordableHousing) => house.status,
        ),
      ),
    ].map((status) => ({ value: status, text: status })),
  ];

  const propertyTypeOptions = [
    ...[
      ...new Set(
        props.affordableHousingData.map(
          (house: TAffordableHousing) => house.propertyTypeCategory,
        ),
      ),
    ].map((type) => ({ value: type, text: type })),
  ];

  // Column management functions
  const fetchUserTableColumnSettings = async () => {
    try {
      const data = await getUserTableColumnSettings({
        username: user.username,
      });
      if (data.affordableHousing) {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: { affordableHousingColumnSettings: data.affordableHousing },
        });
      }
    } catch (err) {
      console.error('Error loading column settings:', err);
    }
  };

  const saveUserSettings = async (updatedColumns: ColumnConfig[]) => {
    try {
      await postSaveUserTableColumnSettings({
        body: {
          username: user.username,
          tableName: 'affordable_housing',
          settings: JSON.stringify(updatedColumns),
        },
      });
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: { affordableHousingColumnSettings: updatedColumns },
      });
    } catch (error) {
      console.error('Error saving column settings:', error);
    }
  };

  const handleColumnModalOk = (updatedColumns: ColumnConfig[]) => {
    saveUserSettings(updatedColumns);
    setIsColumnModalVisible(false);
  };

  // Effects
  useEffect(() => {
    if (props.affordableHousingColumnSettings) {
      const parsedSettings = Array.isArray(
        props.affordableHousingColumnSettings,
      )
        ? props.affordableHousingColumnSettings
        : JSON.parse(props.affordableHousingColumnSettings);
      setColumnConfigs(parsedSettings);
    }
  }, [props.affordableHousingColumnSettings]);

  useEffect(() => {
    if (user?.username) {
      fetchUserTableColumnSettings();
    }
  }, [user?.username]);

  // Calculate scroll width
  useEffect(() => {
    // Calculate total width based on visible columns
    const visibleColumnCount = columnConfigs.filter(
      (col) => col.visible,
    ).length;
    const estimatedWidth = visibleColumnCount * 100; // Rough estimate
    setScrollX(Math.max(estimatedWidth, 1200));
  }, [columnConfigs]);

  // Filter logic
  const applyAllFilters = useCallback(() => {
    let filteredData = [...props.affordableHousingData];

    // Apply bed/bath/sqft filters
    filteredData = filterAffordableData(filteredData, {
      minBeds: props.minBeds,
      maxBeds: props.maxBeds,
      minBaths: props.minBaths,
      maxBaths: props.maxBaths,
      minSqft: props.minSqft,
      maxSqft: props.maxSqft,
      currentStartMLS: props.currentStartMLS,
      currentEndMLS: props.currentEndMLS,
      isZipCodeFilterOn: props.isZipCodeFilterOn,
      zipCode: props.currentPropertyAddress?.postalCode,
    });

    // Apply property type filters
    if (activePropertyTypeFilters.length > 0) {
      filteredData = filteredData.filter((house: TAffordableHousing) =>
        activePropertyTypeFilters.includes(house.propertyTypeCategory),
      );
    }

    // Apply status filters
    if (activeStatusFilters.length > 0) {
      filteredData = filteredData.filter((house: TAffordableHousing) =>
        activeStatusFilters.includes(house.status),
      );
    } else if (props.currentStatusMLS !== 'status') {
      filteredData = filteredData.filter(
        (house: TAffordableHousing) => house.status === props.currentStatusMLS,
      );
    }

    dispatch({
      type: 'CMA/saveCMAStates',
      payload: { affordableHousingDataForRender: filteredData },
    });
  }, [
    props.affordableHousingData,
    props.minBeds,
    props.maxBeds,
    props.minBaths,
    props.maxBaths,
    props.minSqft,
    props.maxSqft,
    props.currentStatusMLS,
    props.currentStartMLS,
    props.currentEndMLS,
    props.isZipCodeFilterOn,
    activePropertyTypeFilters,
    activeStatusFilters,
    dispatch,
  ]);

  // Data fetching
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const response = await getAffordableHousingBuffer({
          lat: Number(props.currentPropertyAddress.latitude),
          lng: Number(props.currentPropertyAddress.longitude),
          distance: Number(props.currentRadiusMile * 1609),
        });

        if (Array.isArray(response) && response.length > 0) {
          const updatedResponse = response.map((item, index) => ({
            ...item,
            key: index,
          }));
          const medianRents = calculateMedianRentsByBedroom(updatedResponse);

          dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              affordableHousingData: updatedResponse,
              affordableHousingDataForRender: updatedResponse,
              affordableHousingBedroomsRent: medianRents,
            },
          });

          setActivePropertyTypeFilters([]);
          setActiveStatusFilters([]);
        } else {
          dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              affordableHousingData: [],
              affordableHousingDataForRender: [],
              affordableHousingBedroomsRent: { 2: '', 3: '', 4: '' },
            },
          });
        }
      } catch (error) {
        console.error('Error fetching affordable housing data:', error);
      } finally {
        setLoading(false);
      }
    };

    if (!isEmpty(props.currentPropertyAddress)) {
      fetchData();
    } else {
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          affordableHousingData: [],
          affordableHousingDataForRender: [],
          affordableHousingBedroomsRent: {},
        },
      });
    }
  }, [props.currentPropertyAddress, props.currentRadiusMile, dispatch]);

  // Other effects
  useEffect(() => {
    const median = calculateMedianLastSalePrice(
      selectedRowKeys,
      props.affordableHousingDataForRender,
      'ah',
    );
    const medianPerSqft = calculateMedianPricePerSqFt(
      selectedRowKeys,
      props.affordableHousingDataForRender,
      'ah',
    );
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        affordableHousingSelectedRowKey: selectedRowKeys,
        affordableHousingMedian: median,
        affordableHousingMedianPricePerSqft: medianPerSqft,
      },
    });
  }, [selectedRowKeys, props.affordableHousingDataForRender, dispatch]);

  useEffect(() => {
    if (props.compingMode === 'smartFilter') {
      const filtered = filterAffordableHousing(
        props.subjectPropertyParcelData,
        props.affordableHousingData,
      );
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: { affordableHousingDataForRender: filtered },
      });
    } else {
      applyAllFilters();
    }
  }, [props.compingMode, props.subjectPropertyParcelData, props.affordableHousingData, applyAllFilters]);

  useEffect(() => {
    setActiveStatusFilters([]);
    applyAllFilters();
  }, [props.currentStatusMLS]);

  useEffect(() => {
    if (props.affordableHousingData.length > 0) {
      applyAllFilters();
    }
  }, [applyAllFilters]);

  useEffect(() => {
    let allKeys: number[] = [];
    if (props.affordableHousingDataForRender.length > 0) {
      allKeys = props.affordableHousingDataForRender.map(
        (record: any) => record.key,
      );
      setSelectedRowKeys(allKeys);
    }
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: { affordableHousingSelectedRowKey: allKeys },
    });
  }, [props.affordableHousingDataForRender, dispatch]);

  // Event handlers
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const onClickCSVButton = async () => {
    setCSVButtonLoading(true);
    try {
      const response = await postAffordableHousingCSV({
        body: {
          lat: props.currentPropertyAddress.latitude,
          lng: props.currentPropertyAddress.longitude,
          distance: props.currentRadiusMile,
          status: props.currentStatusMLS,
          filter: activePropertyTypeFilters,
        },
      });

      if (response) {
        const blob = new Blob([response], { type: 'text/csv;charset=utf-8;' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `affordable_housing_${new Date().toISOString()}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        a.remove();
      }
    } catch (error) {
      console.error('Error downloading CSV:', error);
    } finally {
      setCSVButtonLoading(false);
    }
  };

  const handleTableChange = (
    pagination: any,
    filters: any,
    sorter: any,
    { currentDataSource }: any,
  ) => {
    // Update filter tracking state
    if (filters.propertyTypeCategory) {
      setActivePropertyTypeFilters(filters.propertyTypeCategory as string[]);
    } else {
      setActivePropertyTypeFilters([]);
    }

    if (filters.mls_status) {
      setActiveStatusFilters(filters.mls_status as string[]);
    } else {
      setActiveStatusFilters([]);
    }

    // Apply all filters
    applyAllFilters();
  };

  const handleRowEvents = (record: TAffordableHousing, rowIndex: number) => ({
    onMouseEnter: () => {
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: { affordableHousingRowHover: record },
      });
    },
    onMouseLeave: () => {
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: { affordableHousingRowHover: null },
      });
    },
    onClick: () => {
      props.map.flyTo({
        center: [record.longitude, record.latitude],
        zoom: 16,
        speed: 2,
        curve: 1,
        easing: (t: number) => t,
      });
    },
  });

  useEffect(() => {
    console.log('testing ah', props.affordableHousingDataForRender);
  }, [props.affordableHousingDataForRender]);

  return (
    <>
      <FairMarketRent />
      <div className={styles.cardWrapper}>
        <Row
          align="middle"
          justify="space-between"
          style={{ marginBottom: 12 }}
        >
          <Col>
            <p className="text-lg mb-2 font-semibold">Affordable Housing</p>
          </Col>

          <Col>
            <Row align="middle" gutter={24}>
              <Col>
                <span>Total: </span>
                <span className="font-bold">
                  {props.affordableHousingSelectedRowKey.length}
                </span>
              </Col>
              <Col>
                <span>Median Rent: </span>
                <span className="font-bold">
                  {props.affordableHousingMedian
                    ? formatCurrency(props.affordableHousingMedian)
                    : '-'}
                </span>
              </Col>
              <Col>
                <span>Median Rent Per Sqft: </span>
                <span className="font-bold">
                  $
                  {props.affordableHousingMedianPricePerSqft
                    ? props.affordableHousingMedianPricePerSqft.toFixed(2)
                    : '-'}
                </span>
              </Col>
              <Col>
                <Tooltip title="Column Customization">
                  <Button
                    icon={<SettingOutlined />}
                    onClick={() => setIsColumnModalVisible(true)}
                  />
                </Tooltip>
              </Col>
              <Col>
                <Button
                  type="default"
                  loading={CSVButtonLoading}
                  onClick={onClickCSVButton}
                  disabled={props.affordableHousingDataForRender.length === 0}
                >
                  Export CSV
                </Button>
              </Col>
            </Row>
          </Col>
        </Row>

        <AffordableHousingTable
          data={props.affordableHousingDataForRender}
          loading={loading}
          selectedRowKeys={selectedRowKeys}
          onSelectChange={onSelectChange}
          onRowEvents={handleRowEvents}
          onTableChange={handleTableChange}
          scrollX={scrollX}
          columnConfigs={columnConfigs}
          statusOptions={statusOptions}
          propertyTypeOptions={propertyTypeOptions}
          imageDetailTooltipContainer={imageDetailTooltipContainer}
        />

        <div
          ref={imageDetailTooltipContainer}
          className="image-detail-tooltip-container"
        />

        <ColumnManagerModal
          visible={isColumnModalVisible}
          columns={columnConfigs}
          defaultColumns={DEFAULT_COLUMNS}
          onOk={handleColumnModalOk}
          onCancel={() => setIsColumnModalVisible(false)}
        />
      </div>
    </>
  );
});

export default AffordableHousingPage;
