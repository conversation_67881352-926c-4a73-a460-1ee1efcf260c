import { TAffordableHousing } from '../AHPanel';

interface SubjectPropertyParcelData {
  total_area_sq_ft: number | null;
  year_built: number | null;
  beds_count: number | null;
  baths: number | null;
}

interface AffordableHousingData {
  address: string;
  addressLine2: string | null;
  city: string;
  state: string;
  zip: string;
  status: string;
  rent: number;
  propertyTypeCategory: string;
  yearBuilt: number;
  beds: number;
  baths: number;
  halfBaths: number;
  sqft: number;
  dom: number;
  psf: number;
  photoLink: string;
  latitude: number;
  longitude: number;
  distance: number;
  key: number;
}

export const filterAffordableHousing = (
  subjectPropertyParcelData: SubjectPropertyParcelData,
  affordableHousingData: AffordableHousingData[],
): AffordableHousingData[] => {
  if (!subjectPropertyParcelData) return [];
  const { total_area_sq_ft, year_built, beds_count, baths } =
    subjectPropertyParcelData;

  // Filter the affordableHousingData based on the criteria
  return affordableHousingData.filter((property) => {
    const sizeWithinRange =
      total_area_sq_ft === null
        ? true
        : property.sqft >= Math.round((total_area_sq_ft * 0.75) / 10) * 10 &&
          property.sqft <= Math.round((total_area_sq_ft * 1.25) / 10) * 10;

    const yearBuiltWithinRange =
      property.yearBuilt >= (year_built ? year_built - 30 : 1900) &&
      property.yearBuilt <= (year_built ? year_built + 30 : 2022);

    const bathroomsWithinRange =
      property.baths >= (baths ? baths - 1 : 1) &&
      property.baths <= (baths ? baths + 1 : 99);

    const bedroomsWithinRange =
      property.beds >= (beds_count ? beds_count - 1 : 1) &&
      property.beds <= (beds_count ? beds_count + 1 : 99);

    return (
      sizeWithinRange &&
      yearBuiltWithinRange &&
      bathroomsWithinRange &&
      bedroomsWithinRange
    );
  });
};

export const calculateMedianRent = (data: TAffordableHousing[]) => {
  const rents = data.map((house) => house.rent).sort((a, b) => a - b);
  const mid = Math.floor(rents.length / 2);
  return rents.length % 2 !== 0
    ? rents[mid]
    : (rents[mid - 1] + rents[mid]) / 2;
};
