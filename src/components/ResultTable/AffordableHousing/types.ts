export interface TAffordableHousing {
  key: number;
  address: string;
  addressLine2: any;
  city: string;
  state: string;
  zip: string;
  status: string;
  rent: number;
  propertyTypeCategory: string;
  yearBuilt: number;
  beds: number;
  baths: number;
  halfBaths: number;
  sqft: number;
  dom: number;
  psf: number;
  photoLink: string;
  latitude: number;
  longitude: number;
  distance: number;
  housingurl: string;
  mls_status: boolean;
  realestate_availability: boolean;
  owner_name: string;
  last_seen_mls: any;
  mls_close_price: any;
  last_seen_real_estate: any;
  real_estate_last_rent: any;
  real_estate_available: any;
}

export interface TFilter {
  minBeds: number;
  maxBeds: number;
  minBaths: number;
  maxBaths: number;
  minSqft: number;
  maxSqft: number;
  currentStartMLS: Date | string;
  currentEndMLS: Date | string;
  isZipCodeFilterOn: boolean;
  zipCode: number | string;
}
