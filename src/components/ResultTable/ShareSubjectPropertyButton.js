import styles from '@/components/ResultTable/resultTable.css';
import { Button, Modal, Row, Table, Tooltip, message } from 'antd';
import React, { PureComponent, useState } from 'react';
import { connect } from 'umi';
import filterValuesDefault from '../Filters/filterValuesDefault.json';

const ShareSubjectPropertyButton = (props) => {
  const filterProperties = [
    'Beds',
    'Baths',
    'Sqft',
    'LotSize',
    'YearBuilt',
    'CumulativeDaysOnMarket',
  ];

  const generateFilterValuesInShareableURL = () => {
    let filterValuesInShareableURL = '';

    // do not include filter values if comping mode is intelligentComping
    if (props.compingMode === 'intelligentComping') {
      filterValuesInShareableURL = `&searchingMode=${props.searchingMode}&compingMode=${props.compingMode}`;
      return filterValuesInShareableURL;
    }

    // generate min and max from all relations
    for (const item of filterProperties) {
      switch (props['relation' + item]) {
        case 'equalTo':
          filterValuesInShareableURL += `&min${item}=${
            props['min' + item]
          }&max${item}=${props['min' + item]}`;
          break;
        case 'greaterThan':
          filterValuesInShareableURL += `&min${item}=${
            props['min' + item]
          }&max${item}=${filterValuesDefault['max' + item]}`;
          break;
        case 'lessThan':
        case 'fewerThan':
          filterValuesInShareableURL += `&min${item}=${
            filterValuesDefault['min' + item]
          }&max${item}=${props['max' + item]}`;
          break;
        case 'between':
          filterValuesInShareableURL += `&min${item}=${
            props['min' + item]
          }&max${item}=${props['max' + item]}`;
          break;
        default:
          break;
      }
    }
    // add in status and start and end dates and marketRentPreference
    filterValuesInShareableURL += `&currentStatusMLS=${props.currentStatusMLS}&currentStartMLS=${props.currentStartMLS}&currentEndMLS=${props.currentEndMLS}&searchingMode=${props.searchingMode}&marketRentPreference=${props.marketRentPreference}`;
    return filterValuesInShareableURL;
  };

  const getShareableURL = () => {
    console.log(
      'props.modeToFindSubjectProperty',
      props.modeToFindSubjectProperty,
    );
    const urlOrigin =
      window.location.hostname === 'localhost'
        ? 'https://test.locatealpha.com'
        : window.location.origin;

    // if (props.modeToFindSubjectProperty === 'Click on Map') {
    //   // when clicking on a point that is not in a parcel, using lngLat is better than using address
    //   return `${urlOrigin}/?lng=${props.eventCoordinates[0]}&lat=${
    //     props.eventCoordinates[1]
    //   }&radius=${
    //     props.currentRadiusMile
    //   }${generateFilterValuesInShareableURL()}`;
    // } else if (props.modeToFindSubjectProperty === 'Search an Address') {
    //   // currently not in use as we share using lngLat only
    //   return `${urlOrigin}/?address=${
    //     props.currentPropertyAddress.fullAddress
    //   }&radius=${
    //     props.currentRadiusMile
    //   }${generateFilterValuesInShareableURL()}`;
    // }

    if (props.compingMode === 'intelligentComping') {
      // do not attach radius if comping mode is intelligentComping
      return `${urlOrigin}/?lng=${props.eventCoordinates[0]}&lat=${
        props.eventCoordinates[1]
      }${generateFilterValuesInShareableURL()}`;
    } else {
      return `${urlOrigin}/?lng=${props.eventCoordinates[0]}&lat=${
        props.eventCoordinates[1]
      }&radius=${
        props.currentRadiusMile
      }${generateFilterValuesInShareableURL()}`;
    }
  };

  return (
    <Tooltip
      key="share subject property tooltip"
      title="Copy link to clipboard"
    >
      <button
        key="share subject property button"
        className={styles.buttonWrapperSummaryRow}
        onClick={() => {
          navigator.clipboard.writeText(getShareableURL());
          message.success('Shareable link copied to clipboard');
        }}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          width={20}
          height={20}
          className={styles.shareIconSummaryRow}
        >
          <path d="M10 13.438q-.354 0-.615-.261-.26-.26-.26-.615V4.146l-.625.646q-.25.25-.594.25t-.614-.271q-.25-.25-.25-.615 0-.364.25-.614l2.104-2.104q.146-.146.302-.209.156-.062.323-.062t.323.062q.156.063.302.209l2.125 2.124q.25.25.26.594.011.344-.26.615-.25.25-.615.25-.364 0-.614-.25l-.667-.646v8.437q0 .355-.26.615-.261.261-.615.261Zm-4.917 5.729q-.729 0-1.239-.511-.511-.51-.511-1.239V8.521q0-.729.511-1.24.51-.51 1.239-.51H6.5q.354 0 .615.26.26.261.26.615t-.26.614q-.261.261-.615.261H5.083v8.896h9.834V8.521H13.5q-.354 0-.615-.261-.26-.26-.26-.614t.26-.615q.261-.26.615-.26h1.417q.729 0 1.239.51.511.511.511 1.24v8.896q0 .729-.511 1.239-.51.511-1.239.511Z" />
        </svg>
      </button>
    </Tooltip>
  );
};

export default connect(({ CMA }) => ({
  modeToFindSubjectProperty: CMA.modeToFindSubjectProperty,
  currentRadiusMile: CMA.currentRadiusMile,
  eventCoordinates: CMA.eventCoordinates,
  compingMode: CMA.compingMode,
  searchingMode: CMA.searchingMode,
  currentStatusMLS: CMA.currentStatusMLS,
  currentStartMLS: CMA.currentStartMLS,
  currentEndMLS: CMA.currentEndMLS,
  marketRentPreference: CMA.marketRentPreference,

  minBeds: CMA.minBeds,
  maxBeds: CMA.maxBeds,
  relationBeds: CMA.relationBeds,
  minBaths: CMA.minBaths,
  maxBaths: CMA.maxBaths,
  relationBaths: CMA.relationBaths,
  minSqft: CMA.minSqft,
  maxSqft: CMA.maxSqft,
  relationSqft: CMA.relationSqft,
  minLotSize: CMA.minLotSize,
  maxLotSize: CMA.maxLotSize,
  relationLotSize: CMA.relationLotSize,
  minYearBuilt: CMA.minYearBuilt,
  maxYearBuilt: CMA.maxYearBuilt,
  relationYearBuilt: CMA.relationYearBuilt,
  minCumulativeDaysOnMarket: CMA.minCumulativeDaysOnMarket,
  maxCumulativeDaysOnMarket: CMA.maxCumulativeDaysOnMarket,
  relationCumulativeDaysOnMarket: CMA.relationCumulativeDaysOnMarket,

  currentPropertyAddress: CMA.currentPropertyAddress,
}))(ShareSubjectPropertyButton);
