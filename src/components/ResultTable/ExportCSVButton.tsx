import { Button } from 'antd';
import React from 'react';

interface ButtonProps {
  loading: boolean;
  onClick: () => void;
  data: any;
}

const ExportCSVButton = ({ loading, onClick, data }: ButtonProps) => {
  return (
    <Button
      type="default"
      loading={loading}
      onClick={onClick}
      disabled={data.length === 0}
    >
      Export CSV
    </Button>
  );
};

export default ExportCSVButton;
