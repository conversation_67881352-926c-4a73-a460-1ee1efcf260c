// ColumnManagerModal.tsx
import { Button, Checkbox, Modal, Space } from 'antd';
import { useState } from 'react';
import {
  DragDropContext,
  Draggable,
  DraggableProvided,
  Droppable,
  DroppableProvided,
  DropResult,
} from 'react-beautiful-dnd';

export interface ColumnConfig {
  title: string;
  key: string;
  visible: boolean;
}

interface ColumnManagerModalProps {
  visible: boolean;
  columns: ColumnConfig[];
  defaultColumns: ColumnConfig[]; // Add defaultColumns prop
  onOk: (updatedColumns: ColumnConfig[]) => void;
  onCancel: () => void;
}

const ColumnManagerModal: React.FC<ColumnManagerModalProps> = ({
  visible,
  columns,
  defaultColumns,
  onOk,
  onCancel,
}) => {
  const [tempColumns, setTempColumns] = useState<ColumnConfig[]>(columns || []);

  const handleCheckboxChange = (key: string, checked: boolean) => {
    setTempColumns((prev) =>
      prev.map((col) => (col.key === key ? { ...col, visible: checked } : col)),
    );
  };

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const newColumns = [...tempColumns];
    const [reorderedItem] = newColumns.splice(result.source.index, 1);
    newColumns.splice(result.destination.index, 0, reorderedItem);
    setTempColumns(newColumns);
  };

  const handleReset = () => {
    setTempColumns([...defaultColumns]); // Reset to initial order and visibility
  };

  return (
    <Modal
      title="Manage Columns"
      visible={visible}
      onOk={() => onOk(tempColumns)}
      onCancel={onCancel}
      width={400}
      footer={[
        <Button key="reset" onClick={handleReset}>
          Reset to Default
        </Button>,
        <Button key="cancel" onClick={onCancel}>
          Cancel
        </Button>,
        <Button key="submit" type="primary" onClick={() => onOk(tempColumns)}>
          Save
        </Button>,
      ]}
    >
      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="columns">
          {(provided: DroppableProvided) => (
            <div
              {...provided.droppableProps}
              ref={provided.innerRef}
              style={{ minHeight: '100px' }}
            >
              {tempColumns.map((column, index) => (
                <Draggable
                  key={column.key}
                  draggableId={column.key}
                  index={index}
                >
                  {(provided: DraggableProvided) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      {...provided.dragHandleProps}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        padding: '8px 0',
                        borderBottom: '1px solid #f0f0f0',
                        userSelect: 'none',
                        ...provided.draggableProps.style,
                      }}
                    >
                      <Checkbox
                        checked={column.visible}
                        onChange={(e) =>
                          handleCheckboxChange(column.key, e.target.checked)
                        }
                        style={{ marginRight: 8 }}
                      >
                        {column.title}
                      </Checkbox>
                      <span style={{ marginLeft: 'auto', cursor: 'grab' }}>
                        ⋮⋮
                      </span>
                    </div>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>
    </Modal>
  );
};

export default ColumnManagerModal;
