import { default as turf_centerOfMass } from '@turf/center-of-mass';
import { multiPolygon } from '@turf/helpers';
import { Card, DatePicker, InputNumber, Spin, Tooltip } from 'antd';
import isEmpty from 'lodash.isempty';
import moment from 'moment';
import { useEffect, useRef, useState } from 'react';
import { GoTriangleDown, GoTriangleUp } from 'react-icons/go';
import { IoInformationCircle } from 'react-icons/io5';
import { useDispatch, useSelector } from 'umi';
import { dateFormat } from '../../../constants';
import { formatter } from '../../../utils/money';
import styles from '../resultTable.css';
import ListingCard from './ListingCard';

const getWithinDaysValue = (dateRange) => {
  // end date must be today for within
  if (moment().isSame(dateRange[1], 'day')) {
    const numberOfDays = moment(dateRange[1]).diff(dateRange[0], 'days');
    return numberOfDays;
  } else {
    return null;
  }
};

function MarketCondition() {
  const map = useSelector((state) => state.CMA.map);

  const marketConditionStartDate = useSelector(
    (state) => state.CMA.marketConditionStartDate,
  );
  const marketConditionEndDate = useSelector(
    (state) => state.CMA.marketConditionEndDate,
  );

  const eventCoordinates = useSelector((state) => state.CMA.eventCoordinates);
  const currentRadiusMile = useSelector((state) => state.CMA.currentRadiusMile);
  const searchingMode = useSelector((state) => state.CMA.searchingMode);
  const currentStatusMLS = useSelector((state) => state.CMA.currentStatusMLS);
  const expDateFilterOn = useSelector((state) => state.CMA.expDateFilterOn);
  const drawnCustomPolygons = useSelector(
    (state) => state.CMA.drawnCustomPolygons,
  );
  const currentAOIMarketCondition = useSelector(
    (state) => state.CMA.currentAOIMarketCondition,
  );
  const currentAOILoading = useSelector((state) => state.CMA.currentAOILoading);
  const currentZIPCodeMarketCondition = useSelector(
    (state) => state.CMA.currentZIPCodeMarketCondition,
  );
  const currentZIPCodeLoading = useSelector(
    (state) => state.CMA.currentZIPCodeLoading,
  );
  const currentDistrictMarketCondition = useSelector(
    (state) => state.CMA.currentDistrictMarketCondition,
  );
  const currentDistrictLoading = useSelector(
    (state) => state.CMA.currentDistrictLoading,
  );
  const currentCountyMarketCondition = useSelector(
    (state) => state.CMA.currentCountyMarketCondition,
  );
  const currentCountyLoading = useSelector(
    (state) => state.CMA.currentCountyLoading,
  );
  const currentMetroMarketCondition = useSelector(
    (state) => state.CMA.currentMetroMarketCondition,
  );
  const currentMetroLoading = useSelector(
    (state) => state.CMA.currentMetroLoading,
  );

  const fetchAllPropertyDataDone = useSelector(
    (state) => state.CMA.fetchAllPropertyDataDone,
  );
  const lastMarketConditionPoint = useSelector(
    (state) => state.CMA.lastMarketConditionPoint,
  );

  const dispatch = useDispatch();

  const cardContainerRef = useRef(null);
  const [cardContainerWidth, setCardContainerWidth] = useState(0);
  const [inputNumberStatus, setInputNumberStatus] = useState('');
  const [inputNumberValue, setInputNumberValue] = useState(
    getWithinDaysValue([marketConditionStartDate, marketConditionEndDate]),
  );

  // const [currentStartMLS, setCurrentStartMLS] = useState(
  //   moment().subtract(90, 'days').format(dateFormat),
  // );
  // const [currentEndMLS, setCurrentEndMLS] = useState(
  //   moment().format(dateFormat),
  // );

  // currentStartMLS: moment().subtract(90, 'days').format(dateFormat),
  //   currentEndMLS: moment().format(dateFormat),

  // comparison state
  const [comparisonAdded, setComparisonAdded] = useState(false);
  const [compStartMLS, setCompStartMLS] = useState(
    moment().subtract(180, 'days').format(dateFormat),
  );
  const [compEndMLS, setCompEndMLS] = useState(
    moment().subtract(90, 'days').format(dateFormat),
  );
  const compAOIMarketCondition = useSelector(
    (state) => state.CMA.compAOIMarketCondition,
  );
  const compAOILoading = useSelector((state) => state.CMA.compAOILoading);
  const compZIPCodeMarketCondition = useSelector(
    (state) => state.CMA.compZIPCodeMarketCondition,
  );
  const compZIPCodeLoading = useSelector(
    (state) => state.CMA.compZIPCodeLoading,
  );
  const compDistrictMarketCondition = useSelector(
    (state) => state.CMA.compDistrictMarketCondition,
  );
  const compDistrictLoading = useSelector(
    (state) => state.CMA.compDistrictLoading,
  );
  const compCountyMarketCondition = useSelector(
    (state) => state.CMA.compCountyMarketCondition,
  );
  const compCountyLoading = useSelector((state) => state.CMA.compCountyLoading);
  const compMetroMarketCondition = useSelector(
    (state) => state.CMA.compMetroMarketCondition,
  );
  const compMetroLoading = useSelector((state) => state.CMA.compMetroLoading);
  const cmaTabKey = useSelector((state) => state.CMA.cmaTabKey);

  const [lastFetchIsLeaseMode, setLastFetchIsLeaseMode] = useState(null);

  const {
    MKnumberOfMonths: numberOfMonths,
    MKendYear: endYear,
    MKendMonth: endMonth,
  } = useSelector((state) => state.CMA);

  const getPayload = (saveType) => {
    const coord = {};
    if (eventCoordinates.length > 0) {
      coord.lng = eventCoordinates[0];
      coord.lat = eventCoordinates[1];
    } else if (
      eventCoordinates.length === 0 &&
      drawnCustomPolygons.length > 0
    ) {
      const centerOfMass = turf_centerOfMass(
        multiPolygon([drawnCustomPolygons]),
      );
      coord.lng = centerOfMass.geometry.coordinates[0];
      coord.lat = centerOfMass.geometry.coordinates[1];
    }

    const numberOfDays = moment(marketConditionEndDate).diff(
      marketConditionStartDate,
      'days',
    );
    const compStartDate = moment(marketConditionStartDate).subtract(
      numberOfDays,
      'days',
    );
    const compEndDate = moment(marketConditionEndDate).subtract(
      numberOfDays,
      'days',
    );

    const compEndYear = moment()
      .subtract(numberOfMonths + 1, 'month')
      .year();
    const compEndMonth = moment()
      .subtract(numberOfMonths + 1, 'month')
      .month();

    return {
      propertyType:
        searchingMode === 'Lease' ? 'Residential Lease' : 'Residential',
      // startDate: moment(
      //   saveType === 'current' ? marketConditionStartDate : compStartDate,
      // ).format(dateFormat),
      // endDate: moment(
      //   saveType === 'current' ? marketConditionEndDate : compEndDate,
      // ).format(dateFormat),
      // // lng: eventCoordinates[0],

      year: saveType === 'current' ? endYear : compEndYear,
      month: (saveType === 'current' ? endMonth : compEndMonth) + 1, // moment month is 0 based
      numberOfMonths: numberOfMonths,

      // lat: eventCoordinates[1],
      ...coord,
      distance: currentRadiusMile * 1609.34,
      saveType: saveType,
    };
  };

  useEffect(async () => {
    // const numberOfDays = moment(currentEndMLS).diff(currentStartMLS, 'days');
    if (cmaTabKey !== '2') return;
    if (
      eventCoordinates.length > 0 &&
      drawnCustomPolygons.length === 0 &&
      fetchAllPropertyDataDone
      // &&numberOfDays <= 180
    ) {
      if (
        lastFetchIsLeaseMode === null ||
        (lastFetchIsLeaseMode !== searchingMode) === 'Lease'
      ) {
        setLastFetchIsLeaseMode(searchingMode === 'Lease');
      }

      dispatch({
        type: 'CMA/getCurrentMLSListingSummaryWithinSphere',
        payload: getPayload('current'),
      });

      const response = await dispatch({
        type: 'CMA/getMLSListingSummaryPointWithinLayer',
        payload: {
          lng: eventCoordinates[0],
          lat: eventCoordinates[1],
        },
      });
      let zipCodeChange = true;
      let districtChange = true;
      let countyChange = true;
      let metroChange = true;

      if (
        lastMarketConditionPoint.length > 0 &&
        (lastFetchIsLeaseMode === searchingMode) === 'Lease'
      ) {
        for (let i = 0; i < lastMarketConditionPoint.length; i++) {
          const data = lastMarketConditionPoint[i];
          for (let j = 0; j < response.length; j++) {
            const res = response[j];
            if (
              data.type === 'zipcode' &&
              res.type === 'zipcode' &&
              res.id === data.id &&
              res.key === data.key
            ) {
              zipCodeChange = false;
            }
            if (
              data.type === 'district' &&
              res.type === 'district' &&
              res.obj_id === data.obj_id &&
              res.obj_name === data.obj_name
            ) {
              districtChange = false;
            }
            if (
              data.type === 'county' &&
              res.type === 'county' &&
              res.key === data.key &&
              res.name === data.name
            ) {
              countyChange = false;
            }
            if (
              data.type === 'metro' &&
              res.type === 'metro' &&
              res.key === data.key &&
              res.key === data.key
            ) {
              metroChange = false;
            }
          }
        }
      }

      console.log('countyChanged: ', countyChange);

      if (zipCodeChange || (!zipCodeChange && currentZIPCodeLoading)) {
        dispatch({
          type: 'CMA/getCurrentMLSListingSummaryWithinZIPCode',
          payload: getPayload('current'),
        });
      }

      if (districtChange || (!districtChange && currentDistrictLoading)) {
        dispatch({
          type: 'CMA/getCurrentMLSListingSummaryWithinDistrict',
          payload: getPayload('current'),
        });
      }

      if (countyChange || (!countyChange && currentCountyLoading)) {
        dispatch({
          type: 'CMA/getCurrentMLSListingSummaryWithinCounty',
          payload: getPayload('current'),
        });
      }

      if (metroChange || (!metroChange && currentMetroLoading)) {
        dispatch({
          type: 'CMA/getCurrentMLSListingSummaryWithinMetro',
          payload: getPayload('current'),
        });
      }

      if (comparisonAdded) {
        dispatch({
          type: 'CMA/getCompMLSListingSummaryWithinSphere',
          payload: getPayload('comp'),
        });

        if (zipCodeChange || (!zipCodeChange && compZIPCodeLoading)) {
          dispatch({
            type: 'CMA/getCompMLSListingSummaryWithinZIPCode',
            payload: getPayload('comp'),
          });
        }
        if (districtChange || (!districtChange && compDistrictLoading)) {
          dispatch({
            type: 'CMA/getCompMLSListingSummaryWithinDistrict',
            payload: getPayload('comp'),
          });
        }
        if (countyChange || (!countyChange && compCountyLoading)) {
          dispatch({
            type: 'CMA/getCompMLSListingSummaryWithinCounty',
            payload: getPayload('comp'),
          });
        }
        if (metroChange || (!metroChange && compMetroLoading)) {
          dispatch({
            type: 'CMA/getCompMLSListingSummaryWithinMetro',
            payload: getPayload('comp'),
          });
        }
      }
    }
  }, [
    // currentStartMLS,
    // currentEndMLS,
    eventCoordinates,
    currentRadiusMile,
    searchingMode,
    compStartMLS,
    compEndMLS,
    fetchAllPropertyDataDone,
    cmaTabKey,
  ]);

  useEffect(() => {
    if (cmaTabKey !== '2') return;
    // const numberOfDays = moment(currentEndMLS).diff(currentStartMLS, 'days');
    if (
      eventCoordinates.length > 0 &&
      drawnCustomPolygons.length === 0
      // && numberOfDays <= 180
    ) {
      dispatch({
        type: 'CMA/getMLSListingSummaryPointWithinLayer',
        payload: {
          lng: eventCoordinates[0],
          lat: eventCoordinates[1],
        },
      });
      dispatch({
        type: 'CMA/getCurrentMLSListingSummaryWithinSphere',
        payload: getPayload('current'),
      });

      dispatch({
        type: 'CMA/getCurrentMLSListingSummaryWithinZIPCode',
        payload: getPayload('current'),
      });

      dispatch({
        type: 'CMA/getCurrentMLSListingSummaryWithinDistrict',
        payload: getPayload('current'),
      });

      dispatch({
        type: 'CMA/getCurrentMLSListingSummaryWithinCounty',
        payload: getPayload('current'),
      });

      dispatch({
        type: 'CMA/getCurrentMLSListingSummaryWithinMetro',
        payload: getPayload('current'),
      });
      if (comparisonAdded) {
        dispatch({
          type: 'CMA/getCompMLSListingSummaryWithinSphere',
          payload: getPayload('comp'),
        });

        dispatch({
          type: 'CMA/getCompMLSListingSummaryWithinZIPCode',
          payload: getPayload('comp'),
        });

        dispatch({
          type: 'CMA/getCompMLSListingSummaryWithinDistrict',
          payload: getPayload('comp'),
        });

        dispatch({
          type: 'CMA/getCompMLSListingSummaryWithinCounty',
          payload: getPayload('comp'),
        });

        dispatch({
          type: 'CMA/getCompMLSListingSummaryWithinMetro',
          payload: getPayload('comp'),
        });
      }
    }
  }, [
    marketConditionStartDate,
    marketConditionEndDate,
    cmaTabKey,
    numberOfMonths,
  ]);

  useEffect(() => {
    if (cmaTabKey !== '2') return;
    if (eventCoordinates.length > 0 && drawnCustomPolygons.length === 0) {
      dispatch({
        type: 'CMA/getCurrentMLSListingSummaryWithinSphere',
        payload: getPayload('current'),
      });

      if (comparisonAdded) {
        dispatch({
          type: 'CMA/getCompMLSListingSummaryWithinSphere',
          payload: getPayload('comp'),
        });
      }
    }
  }, [currentRadiusMile, cmaTabKey]);

  useEffect(() => {
    if (cmaTabKey !== '2') return;
    if (comparisonAdded) {
      if (eventCoordinates.length > 0) {
        dispatch({
          type: 'CMA/getCompMLSListingSummaryWithinSphere',
          payload: getPayload('comp'),
        });
      } else if (
        eventCoordinates.length === 0 &&
        drawnCustomPolygons.length > 0
      ) {
        dispatch({
          type: 'CMA/getCompMLSListingSummaryWithinPolygon',
          payload: {
            ...getPayload('comp'),
            body: drawnCustomPolygons,
          },
        });
      }
      dispatch({
        type: 'CMA/getCompMLSListingSummaryWithinZIPCode',
        payload: getPayload('comp'),
      });
      dispatch({
        type: 'CMA/getCompMLSListingSummaryWithinDistrict',
        payload: getPayload('comp'),
      });
      dispatch({
        type: 'CMA/getCompMLSListingSummaryWithinCounty',
        payload: getPayload('comp'),
      });
      dispatch({
        type: 'CMA/getCompMLSListingSummaryWithinMetro',
        payload: getPayload('comp'),
      });
    }
  }, [comparisonAdded, cmaTabKey]);

  useEffect(() => {
    if (cmaTabKey !== '2') return;
    if (drawnCustomPolygons.length > 0 && fetchAllPropertyDataDone) {
      dispatch({
        type: 'CMA/getCurrentMLSListingSummaryWithinPolygon',
        payload: {
          ...getPayload('current'),
          body: drawnCustomPolygons,
        },
      });

      dispatch({
        type: 'CMA/getCurrentMLSListingSummaryWithinZIPCode',
        payload: getPayload('current'),
      });

      dispatch({
        type: 'CMA/getCurrentMLSListingSummaryWithinDistrict',
        payload: getPayload('current'),
      });
      dispatch({
        type: 'CMA/getCurrentMLSListingSummaryWithinCounty',
        payload: getPayload('current'),
      });

      dispatch({
        type: 'CMA/getCurrentMLSListingSummaryWithinMetro',
        payload: getPayload('current'),
      });

      if (comparisonAdded) {
        dispatch({
          type: 'CMA/getCompMLSListingSummaryWithinPolygon',
          payload: {
            ...getPayload('comp'),
            body: drawnCustomPolygons,
          },
        });
        dispatch({
          type: 'CMA/getCompMLSListingSummaryWithinZIPCode',
          payload: getPayload('comp'),
        });
        dispatch({
          type: 'CMA/getCompMLSListingSummaryWithinDistrict',
          payload: getPayload('comp'),
        });
        dispatch({
          type: 'CMA/getCompMLSListingSummaryWithinCounty',
          payload: getPayload('comp'),
        });
        dispatch({
          type: 'CMA/getCompMLSListingSummaryWithinMetro',
          payload: getPayload('comp'),
        });
      }
    }
  }, [
    marketConditionStartDate,
    marketConditionEndDate,
    currentRadiusMile,
    searchingMode,
    drawnCustomPolygons,
    compStartMLS,
    compEndMLS,
    fetchAllPropertyDataDone,
    cmaTabKey,
    numberOfMonths,
  ]);

  useEffect(() => {
    if (!map) return;

    map.on('selectRadius.clear', () => {
      setComparisonAdded(false);
    });

    map.on('mapDraw.clear', () => {
      setComparisonAdded(false);
    });
  }, [map]);

  const onChangeWithin = (value) => {
    console.log(value);

    if (value && typeof value === 'number') {
      // if (value >= 0 && value <= 180) {
      //   const dateRange = [moment().subtract(value, 'days'), moment()];
      //   getAllPropertyDataAndSaveDateRange(dateRange);
      if (inputNumberStatus === 'error') {
        setInputNumberStatus('');
      }
    } else {
      setInputNumberStatus('error');
    }

    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        MKnumberOfMonths: value,
      },
    });
    // setNumberOfMonths(value);
    // setInputNumberValue(value);
  };

  const getAllPropertyDataAndSaveDateRange = (dateRange) => {
    // setCurrentStartMLS(dateRange[0].format(dateFormat));
    // setCurrentEndMLS(dateRange[1].format(dateFormat));
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        marketConditionStartDate: dateRange[0].format(dateFormat),
        marketConditionEndDate: dateRange[1].format(dateFormat),
      },
    });
    if (drawnCustomPolygons.length === 0) {
      if (eventCoordinates.length === 2) {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            fetchAllPropertyDataDone: true,
          },
        });
      }
    } else {
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          fetchAllPropertyDataDone: true,
        },
      });
      // dispatch({
      //   type: 'CMA/getAllPropertyData',
      //   payload: {
      //     mode: 'change MLS & SFR date range',
      //     status: currentStatusMLS,
      //   },
      // });
    }
  };

  const onChangeWithinComparison = (value) => {
    if (value && typeof value === 'number') {
      const dateRange = [moment().subtract(value, 'days'), moment()];
      getAllPropertyDataAndSaveDateRangeComparison(dateRange);
    }
  };

  const getAllPropertyDataAndSaveDateRangeComparison = (dateRange) => {
    setCompStartMLS(dateRange[0].format(dateFormat));
    setCompEndMLS(dateRange[1].format(dateFormat));
  };

  const resizeObserver = new ResizeObserver((entries) => {
    for (const entry of entries) {
      if (entry.contentRect) {
        setCardContainerWidth(entry.contentRect.width);
      }
    }
  });

  useEffect(() => {
    if (cardContainerRef != null && cardContainerRef.current != null) {
      resizeObserver.observe(cardContainerRef.current);
    }
  });

  const generateJSX = (column, type, analysis) => {
    let currentData = {};
    let compData = {};
    let currentLoading = false;
    let compLoading = false;

    if (column === 'AOI') {
      currentData = currentAOIMarketCondition;
      compData = compAOIMarketCondition;
      currentLoading = currentAOILoading;
      compLoading = compAOILoading;
    } else if (column === 'ZIPCode') {
      currentData = currentZIPCodeMarketCondition;
      compData = compZIPCodeMarketCondition;
      currentLoading = currentZIPCodeLoading;
      compLoading = compZIPCodeLoading;
    } else if (column === 'District') {
      currentData = currentDistrictMarketCondition;
      compData = compDistrictMarketCondition;
      currentLoading = currentDistrictLoading;
      compLoading = compDistrictLoading;
    } else if (column === 'County') {
      currentData = currentCountyMarketCondition;
      compData = compCountyMarketCondition;
      currentLoading = currentCountyLoading;
      compLoading = compCountyLoading;
    } else if (column === 'Metro') {
      currentData = currentMetroMarketCondition;
      compData = compMetroMarketCondition;
      currentLoading = currentMetroLoading;
      compLoading = compMetroLoading;
    }

    if (type === 'current') {
      if (analysis === 'active') {
        const text = formatter(currentData.active);
        if (comparisonAdded) {
          if (currentLoading) return <Spin />;
          if (currentData.active > compData.active) {
            return (
              <div style={{ display: 'flex', justifyContent: 'center' }}>
                {text} <GoTriangleUp style={{ color: 'limegreen' }} />
              </div>
            );
          } else if (currentData.active < compData.active) {
            return (
              <div style={{ display: 'flex', justifyContent: 'center' }}>
                {text} <GoTriangleDown style={{ color: 'red' }} />
              </div>
            );
          }
          return text;
        } else {
          return currentLoading ? <Spin /> : text;
        }
      } else if (analysis === 'active-median-psf') {
        if (comparisonAdded) {
          if (currentLoading) return <Spin />;
          let value =
            (currentData.activeMedianPSF / compData.activeMedianPSF - 1) * 100;
          if (!isFinite(value)) {
            value = 'N/A';
          } else {
            value = `${value.toFixed(2)}%`;
          }

          if (+currentData.activeMedianPSF > +compData.activeMedianPSF) {
            return (
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'center',
                  }}
                >
                  {`$${currentData.activeMedianPSF}`}{' '}
                  <GoTriangleUp style={{ color: 'limegreen' }} />
                </div>
                <div>{value}</div>
              </div>
            );
          } else if (+currentData.activeMedianPSF < +compData.activeMedianPSF) {
            return (
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'center',
                  }}
                >
                  {`$${currentData.activeMedianPSF}`}{' '}
                  <GoTriangleDown style={{ color: 'red' }} />
                </div>
                <div>{value}</div>
              </div>
            );
          }
          return (
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
              }}
            >
              {`$${currentData.activeMedianPSF}`}
            </div>
          );
        } else {
          return currentLoading ? (
            <Spin />
          ) : (
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
              }}
            >
              {`$${currentData.activeMedianPSF}`}
            </div>
          );
        }
      } else if (analysis === 'closed') {
        const text = formatter(currentData.closed);
        if (comparisonAdded) {
          if (currentLoading) return <Spin />;
          if (currentData.closed > compData.closed) {
            return (
              <div style={{ display: 'flex', justifyContent: 'center' }}>
                {text} <GoTriangleUp style={{ color: 'limegreen' }} />
              </div>
            );
          } else if (currentData.closed < compData.closed) {
            return (
              <div style={{ display: 'flex', justifyContent: 'center' }}>
                {text} <GoTriangleDown style={{ color: 'red' }} />
              </div>
            );
          }
          return text;
        } else {
          return currentLoading ? <Spin /> : text;
        }
      } else if (analysis === 'closed-median-psf') {
        if (comparisonAdded) {
          if (currentLoading) return <Spin />;
          let value =
            (currentData.closedMedianPSF / compData.closedMedianPSF - 1) * 100;
          if (!isFinite(value)) {
            value = 'N/A';
          } else {
            value = `${value.toFixed(2)}%`;
          }

          if (+currentData.closedMedianPSF > +compData.closedMedianPSF) {
            return (
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'center',
                  }}
                >
                  {`$${currentData.closedMedianPSF}`}{' '}
                  <GoTriangleUp style={{ color: 'limegreen' }} />
                </div>
                <div>{value}</div>
              </div>
            );
          } else if (+currentData.closedMedianPSF < +compData.closedMedianPSF) {
            return (
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'center',
                  }}
                >
                  {`$${currentData.closedMedianPSF}`}{' '}
                  <GoTriangleDown style={{ color: 'red' }} />
                </div>
                <div>{value}</div>
              </div>
            );
          }
          return (
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
              }}
            >
              {`$${currentData.closedMedianPSF}`}
            </div>
          );
        } else {
          return currentLoading ? (
            <Spin />
          ) : (
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
              }}
            >
              {`$${currentData.closedMedianPSF}`}
            </div>
          );
        }
      } else if (analysis === 'median-dom') {
        if (comparisonAdded) {
          if (currentLoading) return <Spin />;
          if (currentData.medianDOM > compData.medianDOM) {
            return (
              <div style={{ display: 'flex', justifyContent: 'center' }}>
                {currentData.medianDOM}{' '}
                <GoTriangleUp style={{ color: 'red' }} />
              </div>
            );
          } else if (currentData.medianDOM < compData.medianDOM) {
            return (
              <div style={{ display: 'flex', justifyContent: 'center' }}>
                {currentData.medianDOM}{' '}
                <GoTriangleDown style={{ color: 'limegreen' }} />
              </div>
            );
          }
          return currentData.medianDOM;
        } else {
          return currentLoading ? <Spin /> : currentData.medianDOM;
        }
      } else if (analysis === 'months-of-inventory') {
        if (comparisonAdded) {
          if (currentLoading) return <Spin />;
          if (currentData.monthsOfInventory > compData.monthsOfInventory) {
            return (
              <div style={{ display: 'flex', justifyContent: 'center' }}>
                {currentData.monthsOfInventory}{' '}
                <GoTriangleUp style={{ color: 'red' }} />
              </div>
            );
          } else if (
            currentData.monthsOfInventory < compData.monthsOfInventory
          ) {
            return (
              <div style={{ display: 'flex', justifyContent: 'center' }}>
                {currentData.monthsOfInventory}{' '}
                <GoTriangleDown style={{ color: 'limegreen' }} />
              </div>
            );
          }
          return currentData.monthsOfInventory;
        } else {
          return currentLoading ? <Spin /> : currentData.monthsOfInventory;
        }
      }
    } else if (type === 'comp') {
      if (analysis === 'active') {
        return compLoading ? <Spin /> : formatter(compData.active);
      } else if (analysis === 'active-median-psf') {
        return compLoading ? <Spin /> : `$${compData.activeMedianPSF}`;
      } else if (analysis === 'closed') {
        return compLoading ? <Spin /> : formatter(compData.closed);
      } else if (analysis === 'closed-median-psf') {
        return compLoading ? <Spin /> : `$${compData.closedMedianPSF}`;
      } else if (analysis === 'median-dom') {
        return compLoading ? <Spin /> : compData.medianDOM;
      } else if (analysis === 'months-of-inventory') {
        return compLoading ? <Spin /> : compData.monthsOfInventory;
      }
    }
  };

  const getAOIDataSource = (type, analysis) => {
    return generateJSX('AOI', type, analysis);
  };

  const getZIPCodeDataSource = (type, analysis) => {
    return generateJSX('ZIPCode', type, analysis);
  };

  const getDistrictDataSource = (type, analysis) => {
    return generateJSX('District', type, analysis);
  };

  const getCountyDataSource = (type, analysis) => {
    return generateJSX('County', type, analysis);
  };

  const getMetroDataSource = (type, analysis) => {
    return generateJSX('Metro', type, analysis);
  };

  const currentDataSource = [
    {
      key: 'active',
      analysis: (
        <div
          style={{
            height: '36.5px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
          }}
        >
          Active:
        </div>
      ),
      // analysis: 'active:',
      aoi: getAOIDataSource('current', 'active'),
      zipcode: getZIPCodeDataSource('current', 'active'),
      district: getDistrictDataSource('current', 'active'),
      county: getCountyDataSource('current', 'active'),
      metro: getMetroDataSource('current', 'active'),
    },
    {
      key: 'active-median-psf',
      analysis: (
        <div
          style={{
            height: '44px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
          }}
        >
          Median PSF:
        </div>
      ),
      aoi: getAOIDataSource('current', 'active-median-psf'),
      zipcode: getZIPCodeDataSource('current', 'active-median-psf'),
      district: getDistrictDataSource('current', 'active-median-psf'),
      county: getCountyDataSource('current', 'active-median-psf'),
      metro: getMetroDataSource('current', 'active-median-psf'),
    },
    {
      key: 'closed',
      // analysis: 'Closed:',
      analysis: (
        <div
          style={{
            height: '36.5px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
          }}
        >
          Closed:
        </div>
      ),
      aoi: getAOIDataSource('current', 'closed'),
      zipcode: getZIPCodeDataSource('current', 'closed'),
      district: getDistrictDataSource('current', 'closed'),
      county: getCountyDataSource('current', 'closed'),
      metro: getMetroDataSource('current', 'closed'),
    },
    {
      key: 'closed-median-psf',
      analysis: (
        <div
          style={{
            height: '44px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
          }}
        >
          Median PSF:
        </div>
      ),
      aoi: getAOIDataSource('current', 'closed-median-psf'),
      zipcode: getZIPCodeDataSource('current', 'closed-median-psf'),
      district: getDistrictDataSource('current', 'closed-median-psf'),
      county: getCountyDataSource('current', 'closed-median-psf'),
      metro: getMetroDataSource('current', 'closed-median-psf'),
    },
    {
      key: 'median-dom',
      // analysis: 'Median DOM',
      analysis: (
        <div
          style={{
            height: '36.5px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
          }}
        >
          Median DOM:
        </div>
      ),
      aoi: getAOIDataSource('current', 'median-dom'),
      zipcode: getZIPCodeDataSource('current', 'median-dom'),
      district: getDistrictDataSource('current', 'median-dom'),
      county: getCountyDataSource('current', 'median-dom'),
      metro: getMetroDataSource('current', 'median-dom'),
    },
    {
      key: 'months-of-inventory',
      // analysis: 'Months of Inventory:',
      analysis: (
        <div
          style={{
            height: '36.5px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
          }}
        >
          Months of Inventory:
        </div>
      ),
      aoi: getAOIDataSource('current', 'months-of-inventory'),
      zipcode: getZIPCodeDataSource('current', 'months-of-inventory'),
      district: getDistrictDataSource('current', 'months-of-inventory'),
      county: getCountyDataSource('current', 'months-of-inventory'),
      metro: getMetroDataSource('current', 'months-of-inventory'),
    },
  ];

  const compDataSource = [
    {
      key: 'active',
      analysis: (
        <div
          style={{
            height: '36.5px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
          }}
        >
          Active:
        </div>
      ),
      aoi: getAOIDataSource('comp', 'active'),
      zipcode: getZIPCodeDataSource('comp', 'active'),
      district: getDistrictDataSource('comp', 'active'),
      county: getCountyDataSource('comp', 'active'),
      metro: getMetroDataSource('comp', 'active'),
    },
    {
      key: 'active-median-psf',
      analysis: (
        <div
          style={{
            height: '44px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
          }}
        >
          Median PSF:
        </div>
      ),
      aoi: getAOIDataSource('comp', 'active-median-psf'),
      zipcode: getZIPCodeDataSource('comp', 'active-median-psf'),
      district: getDistrictDataSource('comp', 'active-median-psf'),
      county: getCountyDataSource('comp', 'active-median-psf'),
      metro: getMetroDataSource('comp', 'active-median-psf'),
    },
    {
      key: 'closed',
      analysis: (
        <div
          style={{
            height: '36.5px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
          }}
        >
          Closed:
        </div>
      ),
      aoi: getAOIDataSource('comp', 'closed'),
      zipcode: getZIPCodeDataSource('comp', 'closed'),
      district: getDistrictDataSource('comp', 'closed'),
      county: getCountyDataSource('comp', 'closed'),
      metro: getMetroDataSource('comp', 'closed'),
    },
    {
      key: 'closed-median-psf',
      analysis: (
        <div
          style={{
            height: '44px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
          }}
        >
          Median PSF:
        </div>
      ),
      aoi: getAOIDataSource('comp', 'closed-median-psf'),
      zipcode: getZIPCodeDataSource('comp', 'closed-median-psf'),
      district: getDistrictDataSource('comp', 'closed-median-psf'),
      county: getCountyDataSource('comp', 'closed-median-psf'),
      metro: getMetroDataSource('comp', 'closed-median-psf'),
    },
    {
      key: 'median-dom',
      analysis: (
        <div
          style={{
            height: '36.5px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
          }}
        >
          Median DOM:
        </div>
      ),
      aoi: getAOIDataSource('comp', 'median-dom'),
      zipcode: getZIPCodeDataSource('comp', 'median-dom'),
      district: getDistrictDataSource('comp', 'median-dom'),
      county: getCountyDataSource('comp', 'median-dom'),
      metro: getMetroDataSource('comp', 'median-dom'),
    },
    {
      key: 'months-of-inventory',
      analysis: (
        <div
          style={{
            height: '36.5px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
          }}
        >
          Months of Inventory:
        </div>
      ),
      aoi: getAOIDataSource('comp', 'months-of-inventory'),
      zipcode: getZIPCodeDataSource('comp', 'months-of-inventory'),
      district: getDistrictDataSource('comp', 'months-of-inventory'),
      county: getCountyDataSource('comp', 'months-of-inventory'),
      metro: getMetroDataSource('comp', 'months-of-inventory'),
    },
  ];

  return (
    <div className={styles.cardWrapper}>
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}
      >
        <div className={styles.cardTitleH2}>Market Conditions</div>
        {(eventCoordinates.length > 0 ||
          (eventCoordinates.length === 0 && drawnCustomPolygons.length > 0)) &&
          !comparisonAdded && (
            <button
              onClick={() => setComparisonAdded(true)}
              className={styles.comparisonButton}
            >
              Add Comparison
            </button>
          )}
      </div>
      {(eventCoordinates.length > 0 ||
        (eventCoordinates.length === 0 && drawnCustomPolygons.length > 0)) && (
        <div ref={cardContainerRef}>
          <div
            key="divider 1"
            className={styles.dividerCardHeader}
            style={{ marginBottom: '10px' }}
          />
          <div
            style={{
              display: 'flex',
              flexDirection: cardContainerWidth > 875 ? 'row' : 'column',
              // justifyContent: 'space-around',
              gap: '20px',
            }}
          >
            <div style={{ width: '100%' }}>
              <h3>Most Recent</h3>
              <ListingCard
                title={
                  <>
                    {/* <span style={{ fontSize: '14px' }}>Closed within: </span>
                    <InputNumber
                      key="date range input number"
                      // min={0}
                      // max={180}
                      status={inputNumberStatus}
                      value={inputNumberValue}
                      onChange={onChangeWithin}
                      precision={0} // no decimal
                      step={30}
                      addonAfter="Days"
                      style={{ width: 120, textAlign: 'right' }}
                    /> */}
                    <span style={{ fontSize: '14px' }}>
                      Previous completed{' '}
                      {numberOfMonths == 1 ? 'month' : 'months'}:{' '}
                    </span>
                    <InputNumber
                      min={1}
                      // max={180}
                      status={inputNumberStatus}
                      value={numberOfMonths}
                      onChange={onChangeWithin}
                      precision={0} // no decimal
                      step={1}
                      addonAfter={numberOfMonths == 1 ? 'Month' : 'Months'}
                      style={{ width: 120, textAlign: 'right' }}
                    />
                  </>
                }
                startDate={marketConditionStartDate}
                endDate={marketConditionEndDate}
                inputNumberHandler={onChangeWithin}
                datePickerHandler={getAllPropertyDataAndSaveDateRange}
                dataSource={currentDataSource}
              />
            </div>
            {comparisonAdded && (
              <div style={{ width: '100%' }}>
                <h3>Compared to</h3>
                <ListingCard
                  title={(() => {
                    // const numberOfDays = getWithinDaysValue([
                    //   marketConditionStartDate,
                    //   marketConditionEndDate,
                    // ]);
                    // if (numberOfDays) {
                    //   const compStartDate = moment(marketConditionStartDate)
                    //     .subtract(numberOfDays, 'days')
                    //     .format(dateFormat);
                    //   const compEndDate = moment(marketConditionEndDate)
                    //     .subtract(numberOfDays, 'days')
                    //     .format(dateFormat);
                    //   return (
                    //     <span style={{ fontSize: '14px' }}>
                    //       {`Closed within prior ${numberOfDays} days`}
                    //       <Tooltip title={`${compStartDate} to ${compEndDate}`}>
                    //         <IoInformationCircle />
                    //       </Tooltip>
                    //     </span>
                    //   );
                    // } else {
                    //   return null;
                    // }
                    const endDate = moment()
                      .subtract(numberOfMonths + 1, 'months')
                      .endOf('month')
                      .format(dateFormat);
                    const startDate = moment(endDate)
                      .subtract(numberOfMonths - 1, 'months')
                      .startOf('month')
                      .format(dateFormat);
                    return (
                      <span style={{ fontSize: '14px' }}>
                        {`Previous prior completed months ${numberOfMonths}`}
                        <Tooltip title={`${startDate} to ${endDate}`}>
                          <IoInformationCircle />
                        </Tooltip>
                      </span>
                    );
                  })()}
                  startDate={compStartMLS}
                  endDate={compEndMLS}
                  inputNumberHandler={onChangeWithinComparison}
                  datePickerHandler={
                    getAllPropertyDataAndSaveDateRangeComparison
                  }
                  dataSource={compDataSource}
                />
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default MarketCondition;
