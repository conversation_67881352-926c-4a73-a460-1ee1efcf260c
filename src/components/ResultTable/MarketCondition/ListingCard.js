import { useState } from 'react';
import { useSelector, useDispatch } from 'umi';
import moment from 'moment';
import { DatePicker, InputNumber, Card, Table } from 'antd';
import styles from '../resultTable.css';
import { dateFormat } from '../../../constants';
import isEmpty from 'lodash.isempty';

function ListingCard(props) {
  const columns = [
    {
      title: '',
      dataIndex: 'analysis',
      key: 'analysis',
      width: 100,
    },
    {
      title: 'AOI',
      dataIndex: 'aoi',
      key: 'aoi',
    },
    {
      title: 'ZIP Code',
      dataIndex: 'zipcode',
      key: 'zipcode',
    },
    {
      title: (
        <span>
          School
          <br />
          District
        </span>
      ),
      dataIndex: 'district',
      key: 'district',
    },
    {
      title: 'County',
      dataIndex: 'county',
      key: 'county',
    },
    {
      title: 'Metro',
      dataIndex: 'metro',
      key: 'metro',
    },
  ];

  return (
    <div>
      <Card
        title={
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              padding: '0 10px',
              justifyContent: 'center',
              alignItems: 'center',
              gap: '10px',
              height: '32px',
            }}
          >
            {props.title}
          </div>
        }
        headStyle={{ padding: 0, fontSize: '12px' }}
        bodyStyle={{ padding: '10px' }}
        style={{
          // minWidth: 350,
          boxShadow: 'rgba(0, 0, 0, 0.15) 0px 5px 15px 0px',
        }}
      >
        <Table
          id="listing-card-table"
          rowKey={(record) => record.key}
          columns={columns}
          dataSource={props.dataSource || []}
          pagination={false}
          rowClassName={(record, index) => {
            if (record.key === 'closed' || record.key === 'closed-median-psf') {
              return styles.marketConditionGrayRow;
            }
          }}
        />
      </Card>
    </div>
  );
}

export default ListingCard;
