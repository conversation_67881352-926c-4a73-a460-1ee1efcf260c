import { LandCompData } from './type';

export function testLog(stuff: any) {
  console.log('test6', stuff);
}

export function sqftToAcre(sqft: number) {
  const sqftPerAcre = 43560;
  return sqft / sqftPerAcre;
}

export function formatPricePerSqftArce(number: any) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 1,
    maximumFractionDigits: 1,
  }).format(number);
}
export function calculateMedian(
  selectedRowKeys: import('react').Key[],
  landCompsDataForRender: LandCompData[],
): number {
  // Filter the data to only include selected rows
  const selectedData = landCompsDataForRender.filter((record) =>
    selectedRowKeys.includes(record.mlsid),
  );

  // Extract the relevant prices based on the status of each record
  const prices = selectedData.map((record) => {
    return record.status === 'Closed' ? record.closeprice : record.currentprice;
  });

  // Sort the prices to prepare for median calculation
  prices.sort((a, b) => a - b);

  // Calculate the median price
  const mid = Math.floor(prices.length / 2);
  let median;
  if (prices.length % 2 === 0) {
    // If even, average the two middle values
    median = (prices[mid - 1] + prices[mid]) / 2;
  } else {
    // If odd, take the middle value
    median = prices[mid];
  }

  return median;
}

export function calculateMedianPricePerSqFt(
  selectedRowKeys: import('react').Key[],
  landCompsDataForRender: LandCompData[],
): number {
  // Filter the data to only include selected rows
  const selectedData = landCompsDataForRender.filter((record) =>
    selectedRowKeys.includes(record.mlsid),
  );

  // Calculate the price per square foot for each selected entry
  const pricesPerSqFt = selectedData
    .map((record) => {
      const price =
        record.status === 'Closed' ? record.closeprice : record.currentprice;
      if (record.lot_size > 0) {
        // Ensure lot_size is positive to avoid division by zero
        return price / record.lot_size;
      }
      return null; // or handle zero lot_size in a specific way if needed
    })
    .filter((price) => price !== null); // Filter out null values if lot_size was zero

  // Sort the prices per square foot to prepare for median calculation
  pricesPerSqFt.sort((a, b) => a - b);

  // Calculate the median price per square foot
  const mid = Math.floor(pricesPerSqFt.length / 2);
  let medianPricePerSqFt;
  if (pricesPerSqFt.length % 2 === 0) {
    // If even, average the two middle values
    medianPricePerSqFt = (pricesPerSqFt[mid - 1] + pricesPerSqFt[mid]) / 2;
  } else {
    // If odd, take the middle value
    medianPricePerSqFt = pricesPerSqFt[mid];
  }

  return medianPricePerSqFt;
}

export function calculateMedianPricePerAcre(
  selectedRowKeys: import('react').Key[],
  landCompsDataForRender: LandCompData[],
): number {
  // Filter the data to only include selected rows
  const selectedData = landCompsDataForRender.filter((record) =>
    selectedRowKeys.includes(record.mlsid),
  );

  // Calculate the price per square foot for each selected entry
  const pricesPerAcre = selectedData
    .map((record) => {
      const price =
        record.status === 'Closed' ? record.closeprice : record.currentprice;
      if (record.lot_size > 0) {
        // Ensure lot_size is positive to avoid division by zero
        return price / sqftToAcre(record.lot_size);
      }
      return null; // or handle zero lot_size in a specific way if needed
    })
    .filter((price) => price !== null); // Filter out null values if lot_size was zero

  // Sort the prices per square foot to prepare for median calculation
  pricesPerAcre.sort((a, b) => a - b);

  // Calculate the median price per square foot
  const mid = Math.floor(pricesPerAcre.length / 2);
  let medianPricePerAcre;
  if (pricesPerAcre.length % 2 === 0) {
    // If even, average the two middle values
    medianPricePerAcre = (pricesPerAcre[mid - 1] + pricesPerAcre[mid]) / 2;
  } else {
    // If odd, take the middle value
    medianPricePerAcre = pricesPerAcre[mid];
  }

  return medianPricePerAcre;
}
