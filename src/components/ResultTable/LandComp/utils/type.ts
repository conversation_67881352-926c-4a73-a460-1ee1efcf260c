export interface LandCompData {
  mlsid: string;
  distance: number;
  fulladdress: string;
  stateorprovince: string;
  zipcode: string;
  city: string;
  subdivision: any;
  status: string;
  originalprice: number;
  closeprice: any;
  currentprice: number;
  cdom: number;
  dom: number;
  contractdate: Date;
  closedate: any;
  offmarketdate: Date;
  propertytype: string;
  propertysubtype: any;
  yearbuilt: any;
  metro: string;
  modificationtimestamp: Date;
  placekey: string;
  listingkey: string;
  latitude: number;
  longitude: number;
  county: string;
  lot_size: any;
  first_entry_timestamp: Date;
  watersource: any;
  special_listing_conditions: any;
  price_change: any;
  price_change_timestamp: any;
  price_change_history: any;
  status_change_history: any;
  status_change_timestamp: any;
}
