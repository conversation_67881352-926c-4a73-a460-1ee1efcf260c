import { useElementSize } from '@/hooks';
import { getLandComps } from '@/services/data';
import { arrayDifference } from '@/utils/arrayMethods';
import { formatCurrency } from '@/utils/lastSalePublicRecord';
import { Table } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { isEmpty, isEqual } from 'lodash';
import { MutableRefObject, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import TableHeader from './TableHeader';
import {
  calculateMedian,
  calculateMedianPricePerAcre,
  calculateMedianPricePerSqFt,
  formatPricePerSqftArce,
  sqftToAcre,
  testLog,
} from './utils/functions';
import { LandCompData } from './utils/type';

const LandCompTable = () => {
  const dispatch = useDispatch();
  const map = useSelector((state: any) => state.CMA.map);
  const landCompsData = useSelector((state: any) => state.CMA.landCompsData);
  const landCompsDataForRender = useSelector(
    (state: any) => state.CMA.landCompsDataForRender,
  );
  const currentPropertyAddress = useSelector(
    (state: any) => state.CMA.currentPropertyAddress,
  );
  const currentRadiusMile = useSelector(
    (state: any) => state.CMA.currentRadiusMile,
  );
  const currentStartMLS = useSelector(
    (state: any) => state.CMA.currentStartMLS,
  );
  const currentEndMLS = useSelector((state: any) => state.CMA.currentEndMLS);
  const compingMode = useSelector((state: any) => state.CMA.compingMode);
  const selectedCompTables = useSelector(
    (state: any) => state.CMA.selectedCompTables,
  );
  const currentStatusMLS = useSelector(
    (state: any) => state.CMA.currentStatusMLS,
  );
  const landCompsSelectedRowKey = useSelector(
    (state: any) => state.CMA.landCompsSelectedRowKey,
  );

  const [statusOptions, setStatusOptions] = useState<any>([]);
  const [propertySubtypeOptions, setPropertySubtypeOptions] = useState<any>();
  const [loading, setLoading] = useState(false);
  const landCompContainer = useRef(null);
  const tableSize = useElementSize(landCompContainer);

  // Track currently displayed table data
  const [currentTableData, setCurrentTableData] = useState<LandCompData[]>([]);

  // Track unselected row keys instead of selected ones
  const [unselectedRowKeys, setUnselectedRowKeys] = useState<React.Key[]>([]);

  // Track previous property address for reset
  const [prevPropertyAddress, setPrevPropertyAddress] = useState<string>(
    currentPropertyAddress,
  );

  // Store table filters
  const [tableFilters, setTableFilters] = useState<any>({});

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const columns: ColumnsType<LandCompData> = [
    {
      title: 'Address',
      dataIndex: 'fulladdress',
      key: 'fulladdress',
      width: 180,
      fixed: 'left',
      align: 'left',
      render: (text: string, record: LandCompData) => {
        return (
          <p className="text-xs">
            {record.fulladdress}, {record.city}, {record.stateorprovince},{' '}
            {record.zipcode}
          </p>
        );
      },
    },
    {
      title: 'Dist. ',
      dataIndex: 'distance',
      key: 'distance',
      width: 75,
      align: 'left',
      defaultSortOrder: 'ascend',
      sorter: (a: LandCompData, b: LandCompData) => a.distance - b.distance, // Added sorter
      render: (text: any, record: LandCompData) => (
        <p className="text-xs">{(record.distance / 1609.34).toFixed(2)} mi</p>
      ),
    },

    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 125,
      align: 'left',
      render: (text: any, record: LandCompData) => (
        <p className="text-xs">{record.status}</p>
      ),
      // filters: statusOptions,
    },
    {
      title: 'County',
      dataIndex: 'county',
      key: 'county',
      width: 100,
      align: 'left',
      render: (text: any, record: LandCompData) => (
        <p className="text-xs">{record.county}</p>
      ),
    },
    {
      title: 'Property Subtype',
      dataIndex: 'propertysubtype',
      key: 'propertysubtype',
      width: 100,
      align: 'left',
      render: (text: any, record: LandCompData) => (
        <p className="text-xs">{record.propertysubtype}</p>
      ),
      filters: propertySubtypeOptions,
    },
    {
      title: 'Price',
      dataIndex: 'currentprice',
      key: 'currentprice',
      width: 100,
      align: 'left',
      render: (text: any, record: LandCompData) => {
        const price =
          record.status === 'Closed' ? record.closeprice : record.currentprice;
        return <p className="text-xs">{formatCurrency(price)}</p>;
      },
      sorter: (a: LandCompData, b: LandCompData) =>
        a.currentprice - b.currentprice, // Added sorter
    },
    {
      title: 'Price per Sqft',
      dataIndex: 'closeprice',
      key: 'closeprice',
      width: 100,
      align: 'left',
      render: (text: any, record: LandCompData) => {
        const price =
          record.status === 'Closed' ? record.closeprice : record.currentprice;
        const pps = record.lot_size > 0 ? price / record.lot_size : 0;
        return (
          <p className="text-xs">{pps ? formatPricePerSqftArce(pps) : 'N/A'}</p>
        );
      },
      sorter: (a: LandCompData, b: LandCompData) => {
        // Determine the appropriate price based on status
        const priceA = a.status === 'Closed' ? a.closeprice : a.currentprice;
        const priceB = b.status === 'Closed' ? b.closeprice : b.currentprice;

        // Calculate PPS for each record
        const ppsA = priceA / a.lot_size;
        const ppsB = priceB / b.lot_size;

        return ppsA - ppsB; // Compare the PPS values
      },
    },

    {
      title: 'Price per Acre',
      dataIndex: 'closeprice',
      key: 'closeprice',
      width: 120,
      align: 'left',
      render: (text, record) => {
        const price =
          record.status === 'Closed' ? record.closeprice : record.currentprice;
        const ppa =
          record.lot_size > 0 ? price / sqftToAcre(record.lot_size) : 0;
        return <p className="text-xs">{ppa ? formatCurrency(ppa) : 'N/A'}</p>;
      },
      sorter: (a, b) => {
        // Determine the appropriate price based on status for each record
        const priceA = a.status === 'Closed' ? a.closeprice : a.currentprice;
        const priceB = b.status === 'Closed' ? b.closeprice : b.currentprice;

        // Calculate PPA for each record
        const ppaA = priceA / sqftToAcre(a.lot_size);
        const ppaB = priceB / sqftToAcre(b.lot_size);

        return ppaA - ppaB; // Compare the PPA values
      },
    },

    {
      title: 'Closed Date',
      dataIndex: 'closedate',
      key: 'closedate',
      width: 100,
      align: 'left',
      render: (text: any, record: LandCompData) => (
        <p className="text-xs">{record.closedate ? record.closedate : 'N/A'}</p>
      ),
      sorter: (a: LandCompData, b: LandCompData) => a.closedate - b.closedate, // Added sorter
    },
    {
      title: 'Lot Size',
      dataIndex: 'lot_size',
      key: 'lot_size',
      width: 100,
      align: 'left',
      render: (text: any, record: LandCompData) => (
        <p className="text-xs">
          {record.lot_size
            ? sqftToAcre(record.lot_size).toFixed(2) + ' ac'
            : 'N/A'}
        </p>
      ),
      sorter: (a: LandCompData, b: LandCompData) => a.lot_size - b.lot_size, // Added sorter
    },
    {
      title: 'CDOM',
      dataIndex: 'cdom',
      key: 'cdom',
      width: 100,
      align: 'left',
      render: (text: any, record: LandCompData) => (
        <p className="text-xs">{record.cdom ? record.cdom : 'N/A'}</p>
      ),
      sorter: (a: LandCompData, b: LandCompData) => a.cdom - b.cdom, // Added sorter
    },
  ];

  // clear unselected row keys for new subject property
  if (!isEqual(currentPropertyAddress, prevPropertyAddress)) {
    setPrevPropertyAddress(currentPropertyAddress);
    setUnselectedRowKeys([]);
  }

  // Handle fetch land comps data
  useEffect(() => {
    testLog({
      currentPropertyAddress,
      currentRadiusMile,
      currentStartMLS,
      currentEndMLS,
    });
    const fetchLandComp = async () => {
      testLog('fetchLandComp');
      testLog(selectedCompTables);

      if (
        currentPropertyAddress &&
        currentRadiusMile &&
        currentStartMLS &&
        currentEndMLS
      ) {
        setLoading(true);
        console.log('statusFiltered', {
          lat: currentPropertyAddress.latitude,
          lng: currentPropertyAddress.longitude,
          distance: currentRadiusMile * 1609.344,
          startDate: currentStartMLS,
          endDate: currentEndMLS,
          status: currentStatusMLS,
        });

        if (currentStatusMLS !== 'status') {
          const result = await getLandComps({
            lat: currentPropertyAddress.latitude,
            lng: currentPropertyAddress.longitude,
            distance: currentRadiusMile * 1609.344,
            startDate: currentStartMLS,
            endDate: currentEndMLS,
            status: currentStatusMLS,
          });
          console.log('statusFiltered', currentStatusMLS);
          console.log('statusFiltered compingMode', compingMode);
          console.log('statusFiltered result', result);
          const statusFiltered = result?.filter(
            (land: LandCompData) => land.status === currentStatusMLS,
          );

          console.log('statusFiltered', statusFiltered);
          dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              landCompsData: statusFiltered,
              landCompsDataForRender: statusFiltered,
              showAVM: false,
            },
          });
        } else {
          const result = await getLandComps({
            lat: currentPropertyAddress.latitude,
            lng: currentPropertyAddress.longitude,
            distance: currentRadiusMile * 1609.344,
            startDate: currentStartMLS,
            endDate: currentEndMLS,
            status: 'status',
          });
          dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              landCompsData: result,
              landCompsDataForRender: result,
              showAVM: false,
            },
          });
        }

        setLoading(false);
      }
    };

    fetchLandComp();
  }, [
    currentPropertyAddress,
    currentRadiusMile,
    currentStartMLS,
    currentEndMLS,
    currentStatusMLS,
  ]);

  // Get current table data and calculate selected rows from unselected ones
  const getCurrentTableData = () => {
    // remove filters that are null, e.g. { propertysubtype: null }
    const nonNullFilters = Object.fromEntries(
      Object.entries(tableFilters).filter(([_, value]) => value !== null),
    );

    // filter the data based on current filters
    const currentTableData = landCompsDataForRender.filter((row) => {
      return Object.keys(nonNullFilters).every((filter) => {
        return nonNullFilters[filter].includes(row[filter]);
      });
    });

    // calculate selected row keys as difference between all rows and unselected rows
    const selectedRowKeys = arrayDifference(
      currentTableData.map((row) => row.mlsid),
      unselectedRowKeys,
    );

    return { currentTableData, selectedRowKeys };
  };

  // Update selection state when table data changes
  useEffect(() => {
    if (landCompsDataForRender && landCompsDataForRender.length > 0) {
      const { currentTableData, selectedRowKeys } = getCurrentTableData();
      setCurrentTableData(currentTableData);

      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          landCompsSelectedRowKey: selectedRowKeys,
        },
      });
    }
  }, [landCompsDataForRender]);

  // Update calculated median values when selection changes
  useEffect(() => {
    if (
      landCompsDataForRender &&
      landCompsDataForRender.length > 0 &&
      landCompsSelectedRowKey
    ) {
      const median = calculateMedian(
        landCompsSelectedRowKey,
        landCompsDataForRender,
      );
      const medianPerSqft = calculateMedianPricePerSqFt(
        landCompsSelectedRowKey,
        landCompsDataForRender,
      );
      const medianPerAcre = calculateMedianPricePerAcre(
        landCompsSelectedRowKey,
        landCompsDataForRender,
      );

      testLog(median);
      testLog(medianPerSqft);
      testLog(medianPerAcre);

      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          landCompsMedian: median,
          landCompMedianPricePerSqft: medianPerSqft,
          landCompMedianPricePerAcre: medianPerAcre,
        },
      });
    }
  }, [landCompsSelectedRowKey, landCompsDataForRender]);

  // Update dropdown filter options
  useEffect(() => {
    if (landCompsData && landCompsData.length > 0) {
      setStatusOptions([
        ...[
          ...new Set(landCompsData.map((house: LandCompData) => house.status)),
        ].map((status) => ({ value: status, text: status })),
      ]);
      setPropertySubtypeOptions([
        ...[
          ...new Set(
            landCompsData.map((house: LandCompData) => house.propertysubtype),
          ),
        ].map((status) => ({ value: status, text: status })),
      ]);
    }
  }, [landCompsData]);

  // Handle row selection change
  const onSelectChange = (selectedRowKeys: React.Key[]) => {
    const unselectedRowKeys = arrayDifference(
      currentTableData.map((row) => row.mlsid),
      selectedRowKeys,
    );
    setUnselectedRowKeys(unselectedRowKeys);

    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        landCompsSelectedRowKey: selectedRowKeys,
      },
    });
  };

  // Row selection configuration
  const rowSelection = {
    selectedRowKeys: landCompsSelectedRowKey || [],
    onChange: onSelectChange,
    selections: [
      Table.SELECTION_ALL,
      Table.SELECTION_INVERT,
      Table.SELECTION_NONE,
    ],
  };

  return (
    <div className="mb-4" ref={landCompContainer}>
      <Table
        dataSource={landCompsDataForRender}
        columns={columns}
        key="lc table"
        title={() => (
          <TableHeader selectedRowKeys={landCompsSelectedRowKey || []} />
        )}
        rowKey={(record) => record.mlsid}
        loading={loading}
        size="small"
        rowSelection={rowSelection}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          onChange: (page, pageSize) => {
            setCurrentPage(page);
            setPageSize(pageSize);
          },
        }}
        onRow={(record, rowIndex) => {
          return {
            onMouseEnter: (event: any) => {
              dispatch({
                type: 'CMA/saveCMAStates',
                payload: {
                  landCompsHover: record,
                },
              });
            },
            onMouseLeave: (event: any) => {
              dispatch({
                type: 'CMA/saveCMAStates',
                payload: {
                  landCompsHover: null,
                },
              });
            },
            onClick: (event: any) => {
              // Use flyTo function with latitude and longitude from the record
              map.flyTo({
                center: [record.longitude, record.latitude],
                zoom: 16,
                speed: 2,
                curve: 1,
                easing: (t: any) => t,
              });
            },
          };
        }}
        scroll={{
          x: 1200,
          y: '400px',
        }}
        onChange={(
          pagination,
          filters,
          sorter,
          { action, currentDataSource },
        ) => {
          if (!isEqual(currentDataSource, currentTableData)) {
            setCurrentTableData(currentDataSource);
          }

          switch (action) {
            case 'filter':
              // Update unselected row keys when filtering
              const allRowKeys = currentDataSource.map(
                (item: any) => item.mlsid,
              );
              const selectedRowKeys = arrayDifference(
                allRowKeys,
                unselectedRowKeys,
              );

              dispatch({
                type: 'CMA/saveCMAStates',
                payload: {
                  landCompsSelectedRowKey: selectedRowKeys,
                },
              });

              setTableFilters(filters);

              if (filters.propertysubtype) {
                const newDFR = landCompsData.filter((house: LandCompData) => {
                  return filters.propertysubtype?.includes(
                    house.propertysubtype,
                  );
                });
                dispatch({
                  type: 'CMA/saveCMAStates',
                  payload: {
                    landCompsDataForRender: newDFR,
                  },
                });
              } else if (filters.status) {
                const newDFR = landCompsData.filter((house: LandCompData) => {
                  return filters.status?.includes(house.status);
                });
                dispatch({
                  type: 'CMA/saveCMAStates',
                  payload: {
                    landCompsDataForRender: newDFR,
                  },
                });
              } else {
                dispatch({
                  type: 'CMA/saveCMAStates',
                  payload: {
                    landCompsDataForRender: landCompsData,
                  },
                });
              }
              break;
          }
        }}
        sticky={true}
      />
    </div>
  );
};

export default LandCompTable;
