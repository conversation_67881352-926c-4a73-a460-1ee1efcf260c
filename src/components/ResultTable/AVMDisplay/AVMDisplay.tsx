import { connect } from 'umi';
import { Row, Col } from 'antd';
import isEmpty from 'lodash.isempty';
import styles from '@/components/ResultTable/resultTable.css';
import { formatter } from '@/utils/money';

type AVMDisplayProps = { subjectPropertyParcelData: any; mapExpandedView: any };
const AVMDisplay = connect(({ CMA }: any) => ({
  subjectPropertyParcelData: CMA.subjectPropertyParcelData,
  mapExpandedView: CMA.mapExpandedView,
}))(function (props: AVMDisplayProps) {
  return (
    <Row
      key="avm card"
      className={styles.cardWrapper}
      align="middle"
      justify="space-between"
      wrap={true}
      gutter={[0, 16]}
    >
      <Col key="rent avm row title" className={styles.cardTitleH2}>
        {/* Automated Valuation Model */}
        AVM
      </Col>
      <Col
        key="summary average rows wrapper"
        style={{
          textAlign: 'center',
          display: 'flex',
          justifyContent: props.mapExpandedView ? 'flex-start' : 'flex-end',
          alignItems: 'stretch',
          gap: 72,
        }}
      >
        <div key="rent avm row" className={styles.summaryCardAvgWrapper}>
          <div key="rent avm label" className={styles.cardSubtitle}>
            Rent AVM
          </div>
          <div key="rent avm amount" className={styles.summaryCardAvgSum}>
            {!isEmpty(props.subjectPropertyParcelData) &&
            props.subjectPropertyParcelData.rent
              ? '$' + formatter(props.subjectPropertyParcelData.rent)
              : '-'}
          </div>
          <div key="range" className={styles.cardDataValueLighter}>
            {!isEmpty(props.subjectPropertyParcelData) &&
            props.subjectPropertyParcelData.rent
              ? `
                  $${formatter(
                    props.subjectPropertyParcelData.rent *
                      (1 + props.subjectPropertyParcelData.low_ratio),
                  )} 
                  - $${formatter(
                    props.subjectPropertyParcelData.rent *
                      (1 + props.subjectPropertyParcelData.high_ratio),
                  )}
                `
              : '-'}
          </div>
        </div>
        <div key="sales avm row" className={styles.summaryCardAvgWrapper}>
          <div key="sales avm label" className={styles.cardSubtitle}>
            Sales AVM
          </div>
          <div key="sales avm amount" className={styles.summaryCardAvgSum}>
            {!isEmpty(props.subjectPropertyParcelData) &&
            props.subjectPropertyParcelData.sales
              ? '$' + formatter(props.subjectPropertyParcelData.sales)
              : '-'}
          </div>
          <div key="range" className={styles.cardDataValueLighter}>
            {!isEmpty(props.subjectPropertyParcelData) &&
            props.subjectPropertyParcelData.sales
              ? `
                  $${formatter(
                    props.subjectPropertyParcelData.sales *
                      (1 + props.subjectPropertyParcelData.low_ratio),
                  )} 
                  - $${formatter(
                    props.subjectPropertyParcelData.sales *
                      (1 + props.subjectPropertyParcelData.high_ratio),
                  )}
                `
              : '-'}
          </div>
        </div>
      </Col>
    </Row>
  );
});

export default AVMDisplay;
