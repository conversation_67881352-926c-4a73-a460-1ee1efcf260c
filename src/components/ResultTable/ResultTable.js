import BatchProcessor from '@/components/BatchProcessor';
import FiltersLegacy from '@/components/Filters/Filters';
import { serverType } from '@/services/data';
import {
  MarketCondition,
  MarketConditionProvider,
  MarketConditionQueryProvider,
} from '@spatiallaser/market-condition';
import {
  MarketConditionHistory,
  MarketConditionHistoryProvider,
  MarketConditionHistoryQueryProvider,
} from '@spatiallaser/market-condition-history';
import {
  Badge,
  Button,
  Checkbox,
  ConfigProvider,
  Dropdown,
  Radio,
  Tabs,
  Tooltip,
} from 'antd';
import isEmpty from 'lodash.isempty';
import isEqual from 'lodash.isequal';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { FaTableList } from 'react-icons/fa6';
import { connect } from 'umi';
import { calculateMedian } from '../../utils/calculations';
import {
  getCityCodeViaZIPCode,
  getMetroNameForParam,
} from '../../utils/geography';
import { getAverageWithZeroValueCheck } from '../../utils/marketResultCalculationHelpers';
import { userGroupHasAccess } from '../../utils/userGroup';
import BTRSubmissionModal from '../BTRSubmission/BTRSubmissionModal';
import EmptyState from '../EmptyState';
import ExportAllCSV from '../ExportToCSV/ExportAllCSV';
import ExportBFRCSV from '../ExportToCSV/ExportBFRCSV';
import ExportLandCSV from '../ExportToCSV/ExportLandCSV';
import ExportNewBuildsCSV from '../ExportToCSV/ExportNewBuildsCSV';
import SidebarWrapper from '../Filters/SidebarWrapper';
import GenerateScorecard from '../GenerateScorecard/GenerateScorecard';
import ImportKMLModal from '../ImportKMLModal';
import TargetUploadModal from '../TargetUpload/TargetUploadModal';
import AffordableHousingPage from './AffordableHousing/panel';
import BTRCommunityInfoTable from './BTRCommunityInfo/Table';
import BuildingPermitPanel from './BuildingPermit/BuildingPermitPanel';
import BuiltForRentTable2 from './BuiltForRent/BuiltForRentTable';
import ClientsOwnProperties from './ClientsOwnProperties/ClientsOwnProperties';
import CombinedTable from './CombinedTable/CombinedTable';
import { CompInsights } from './CompInsights';
import LandCompTable from './LandComp/Table';
import LandCrexiTable from './LandCrexi/Table';
import LandShowcaseTable from './LandShowcase/Table';
import LastSalePublicRecord from './LastSalePublicRecord/LastSalePublicRecord';
import LeaseExpiry from './LeaseExpiry';
import MLS from './MLS/MLS';
import MLSModal from './MLS/MLSModal';
import MLSV2 from './MLSV2/MLS';
import MultiFamily from './MultiFamily/MultiFamily';
import { MultiFamilyApartments } from './MultiFamily/MultiFamilyApartments';
import NationalSFROperators from './NationalSFROperators/NationalSFROperators';
import NewBuilds from './NewBuilds/NewBuilds';
import PadSplit from './PadSplit/PadSplit';
import ParcelOwnerSummary from './ParcelOwnerSummary/ParcelOwnerSummary';
import BTRPipelinePanel from './Pipeline/BTRPipeLine/BTRPipelinePanel';
import NewlySubdividedPipeLineTable from './Pipeline/NewlySubdividedPipeline/table';
import PortalListingTable from './PortalListings/PortalListingTable';
import RealtorMFTable from './RealtorMultiFamily/Table';
import RealtorSFTable from './RealtorSingleFamily/Table';
import ResultRow from './ResultRow/ResultRow';
import styles from './resultTable.css';
import SchoolAndDemographic from './SchoolScore/SchoolAndDemographic';
import Summary from './Summary/Summary';
import UnitMixGraph from './UnitMixGraph/UnitMixGraph';
import UnitMixParcel from './UnitMixGraph/UnitMixParcel';

export const columnWidthSmall = 100;
export const columnWidthSmaller = 50;
export const dateFormat = 'YYYY-MM-DD';
export const sideBarWidth = 216;

const ResultTable = (props) => {
  const [prevSubjectProperty, setPrevSubjectProperty] = useState({});
  const [prevCurrentRadiusMile, setPrevCurrentRadiusMile] = useState(0.5);
  const [tableView, setTableView] = useState('default');

  const [showRoomRental, setShowRoomRental] = useState(false);
  const [showMultiFamily, setShowMultiFamily] = useState(false);

  const tabContentContainerRef = React.useRef(null);

  useEffect(() => {
    const tabContentContainer = document.querySelector(
      '.ant-tabs-content-holder',
    );
    if (tabContentContainer) {
      tabContentContainerRef.current = tabContentContainer;
    }
  }, []);

  useEffect(() => {
    const shouldShowRoomRental = ['VentureREI', 'dev'].includes(
      props.selectedUserGroup,
    );

    const shouldShowMultiFamily =
      props.userGroup.includes('BridgeTower') ||
      props.userGroup.includes('Avanta') ||
      props.userGroup.includes('demo-users') ||
      props.userGroup.includes('dev') ||
      (props.userGroup.includes('demo-CMA-DFW-only') &&
        (props.userEmail.includes('marchcapitalfund.com') ||
          props.userEmail.includes('greystar.com') ||
          props.userEmail.includes('allcommonsenses')));

    setShowRoomRental(shouldShowRoomRental);
    setShowMultiFamily(shouldShowMultiFamily);

    if (shouldShowRoomRental) {
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          selectedCompTables: [...props.selectedCompTables, 'Room Rental'],
        },
      });
    }
    if (props.isLandMode) {
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          selectedCompTables: [],
        },
      });
    }
    if (shouldShowMultiFamily) {
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          selectedCompTables: [
            ...props.selectedCompTables,
            'Multi-Family Listings',
          ],
        },
      });
    }
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        selectedCompTables: [...props.selectedCompTables, 'Last Sale'],
      },
    });

    if (props.userGroup.includes('Sunroom')) {
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          selectedCompTables: props.selectedCompTables.filter(
            (tableName) => !tableName.toLowerCase().includes('built for rent'),
          ),
        },
      });
    }
  }, [props.userGroup, props.userEmail]);

  // for MPH: set initial comping mode to intelligent for every property
  // so that ARV matches what user sees in single portfolio view
  if (
    ['MarketplaceHomes', 'MPH-sandbox'].includes(props.selectedUserGroup) &&
    !isEqual(prevSubjectProperty, props.subjectPropertyParcelData)
  ) {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        compingMode: 'intelligentComping',
      },
    });
  }

  // when subject property changes
  if (
    !isEqual(prevSubjectProperty, props.subjectPropertyParcelData) ||
    prevCurrentRadiusMile != props.currentRadiusMile
  ) {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        ClientTableSort: {},
        NSFRTableSort: { columnKey: 'distance', order: 'ascend' },
        HotPadTableSort: { columnKey: 'distance', order: 'ascend' },
        MLSTableSortLease: { columnKey: 'distance', order: 'ascend' },
        MLSTableSortSale: { columnKey: 'distance', order: 'ascend' },
        realtorSingleFamilyTableSorter: {
          columnKey: 'distance',
          order: 'ascend',
        },
        MultiFamilyTableSort: {},
      },
    });
    // get saved comps
    if (
      props.subjectPropertyParcelData &&
      Object.hasOwn(props.subjectPropertyParcelData, 'placekey') &&
      props.subjectPropertyParcelData.placekey &&
      props.eventCoordinates.length > 0
    ) {
      props.dispatch({
        type: 'CMA/getSavedComps',
        payload: {
          placekey: props.subjectPropertyParcelData.placekey,
          lng: props.eventCoordinates[0],
          lat: props.eventCoordinates[1],
          stage: 1, // temp
        },
      });
    }
    setPrevSubjectProperty(props.subjectPropertyParcelData);
    setPrevCurrentRadiusMile(props.currentRadiusMile);
  }

  const onSelectChangeNationalOperators = (selectedRowKeys) => {
    if (!isEqual(props.selectedRowKeysNationalOperators, selectedRowKeys)) {
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          selectedRowKeysNationalOperators: selectedRowKeys,
        },
      });
    }
  };

  const onSelectChangeHotPads = (selectedRowKeys) => {
    if (!isEqual(props.selectedRowKeysHotPads, selectedRowKeys)) {
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          selectedRowKeysHotPads: selectedRowKeys,
        },
      });
    }
  };

  const onSelectChangeMLS = (selectedRowKeys) => {
    if (!isEqual(props.selectedRowKeysMLSLease, selectedRowKeys)) {
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          ...(props.searchingMode === 'Lease'
            ? { selectedRowKeysMLSLease: selectedRowKeys }
            : { selectedRowKeysMLSSale: selectedRowKeys }),
        },
      });
    }
  };

  // save selected comps
  const saveSelectedComps = () => {
    let payloadSubjectProperty = {
      placekey: props.subjectPropertyParcelData.placekey,
      lng: props.eventCoordinates[0],
      lat: props.eventCoordinates[1],
    };
    const selectedRowKeysMLS =
      props.searchingMode === 'Lease'
        ? props.selectedRowKeysMLSLease
        : props.selectedRowKeysMLSSale;
    const payloadMLS = selectedRowKeysMLS.map((key) => {
      const property = props.currentMLSPropertiesFiltered.find(
        (property) => property.mlsid === key,
      );
      console.log('property.zipCode', property.zipcode);
      return {
        mlsid: key,
        citycode: getCityCodeViaZIPCode(property.zipcode),
        metro: getMetroNameForParam(property, false),
      };
    });
    // const payloadNationalOperators = props.selectedRowKeysNationalOperators.map((key) => {
    //   const property = props.currentNationalOperatorsPropertiesFiltered.find((property) => property.base_id === key);
    //   return property.base_id;
    // });
    props.dispatch({
      type: 'CMA/saveComps',
      payload: {
        body: {
          ...payloadSubjectProperty,
          to_clients: [],
          mls_ids: payloadMLS,
          sfr_ids: props.selectedRowKeysNationalOperators,
          stage: 1, // temp
          timestamp: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
        },
      },
    });
  };

  // if no comps have been selected, don't show the save comps button
  const checkIfCompsSelected = () => {
    const selectedRowKeysMLS =
      props.searchingMode === 'Lease'
        ? props.selectedRowKeysMLSLease
        : props.selectedRowKeysMLSSale;
    // there's an item that is 'undefined' in the initial value of selectedRowKeysMLS
    // therefore, we need to filter it out
    const selectedRowKeysMLSFiltered = selectedRowKeysMLS.filter(
      (item) => item !== 'undefined',
    );
    // console.log('props.selectedRowKeysMLSLease', props.selectedRowKeysMLSLease, 'selectedRowKeysMLS', selectedRowKeysMLS, 'props.selectedRowKeysNationalOperators', props.selectedRowKeysNationalOperators);
    return (
      selectedRowKeysMLSFiltered.length > 0 ||
      props.selectedRowKeysNationalOperators.length > 0
    );
  };

  const showSavedComps = () => {
    if (props.savedMLSComps.length > 0 || props.savedSFRComps.length > 0) {
      // get the largest distance, then set the radius if necessary
      const distancesMLS = props.savedMLSComps.map(
        (property) => property.distance,
      );
      const distanceSFR = props.savedSFRComps.map(
        (property) => property.distance,
      );
      const largestDistance =
        Math.max(...distancesMLS, ...distanceSFR) / 1609.34;
      console.log('largestDistance', largestDistance);
      const radiusOptions = [0.1, 0.3, 0.5, 1, 1.5, 2, 3, 5];
      const closestRadius = radiusOptions.reduce((prev, curr) =>
        Math.abs(curr - largestDistance) < Math.abs(prev - largestDistance) &&
        curr - largestDistance >= 0
          ? curr
          : prev,
      );
      console.log(
        'closestRadius',
        closestRadius,
        'props.currentRadiusMile',
        props.currentRadiusMile,
      );
      if (closestRadius > props.currentRadiusMile) {
        props.map.fire('selectRadius.radius', {
          payload: { currentRadiusMile: closestRadius },
        });
        props.map.fire('selectRadius.setRadius', {
          payload: { currentRadiusMile: closestRadius },
        });
      }
      const savedRowKeysMLS = props.savedMLSComps.map(
        (property) => property.mlsid,
      );
      const savedRowKeysSFR = props.savedSFRComps.map(
        (property) => property.base_id,
      );

      const payloadMLS =
        props.searchingMode === 'Lease'
          ? { selectedRowKeysMLSLease: savedRowKeysMLS }
          : { selectedRowKeysMLSSale: savedRowKeysMLS };
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          ...payloadMLS,
          selectedRowKeysNationalOperators: savedRowKeysSFR,
        },
      });
    }
  };

  // calculate all rent averages
  // except multi family
  const getRentAverages = (
    selectedRowKeysMLSParam,
    selectedRowKeysNationalOperatorsParam,
    selectedRowKeysHotPadsParam,
  ) => {
    // console.log('getRentAverages called');
    let rentAverageMLS = 0,
      rentAverageNationalOperators = 0,
      rentAverageHotPads = 0,
      rentAverageAll = 0,
      MLSAverageToAVMRatio = 0,
      rentSumMLS = 0,
      rentSumNationalOperators = 0,
      rentSumHotPads = 0,
      hasAverageMLS = 0, // for deciding total sum for average all is divided by how many
      hasAverageNationalOperators = 0,
      hasAverageHotPads = 0,
      hasAverageSubjectProperty = 0,
      // per Sqft
      rentAveragePerSqftMLS = 0,
      rentAveragePerSqftNationalOperators = 0,
      rentAveragePerSqftHotPads = 0,
      rentAveragePerSqftAll = 0,
      rentPerSqftSumMLS = 0,
      rentPerSqftSumNationalOperators = 0,
      rentPerSqftSumHotPads = 0,
      hasAveragePerSqftMLS = 0,
      hasAveragePerSqftNationalOperators = 0,
      hasAveragePerSqftHotPads = 0,
      hasAveragePerSqftSubjectProperty = 0,
      propertyWithPerSqftMLS = 0, // number of MLS properties that has per Sqft
      propertyWithPerSqftNationalOperators = 0,
      propertyWithPerSqftHotPads = 0,
      // median
      rentMedianMLS = 0,
      rentMedianSFR = 0,
      rentMedianHotPads = 0,
      rentArrayMLS = [],
      rentArraySFR = [],
      rentArrayHotPads = [],
      rentPerSqftMedianMLS = 0,
      rentPerSqftMedianSFR = 0,
      rentPerSqftMedianHotPads = 0,
      rentPerSqftArrayMLS = [],
      rentPerSqftArraySFR = [],
      rentPerSqftArrayHotPads = [],
      rentAdjustedBT = 0,
      rentAdjustedFormula = '',
      MLSMedianToAVMRatio = null;

    // calculate MLS average for selected rows
    if (selectedRowKeysMLSParam && selectedRowKeysMLSParam.length > 0) {
      props.currentMLSPropertiesFiltered.forEach((property) => {
        if (selectedRowKeysMLSParam.includes(property.mlsid)) {
          rentSumMLS += property.latestPrice;
          if (property.latestPrice && property.size) {
            rentPerSqftSumMLS += property.latestPrice / property.size;
            propertyWithPerSqftMLS++;
          }
          // for MLS median
          rentArrayMLS.push(property.latestPrice);
          // for MLS per Sqft median
          if (property.latestPrice && property.size) {
            rentPerSqftArrayMLS.push(property.latestPrice / property.size);
          }
        }
      });
      rentAverageMLS = rentSumMLS / selectedRowKeysMLSParam.length;
      // if selectedRowKeys.length > 0, it's in calculation, therefore hasAverage = 1
      hasAverageMLS = 1;
      // calculate median
      rentMedianMLS = calculateMedian(rentArrayMLS);
      // calculate per Sqft median
      rentPerSqftMedianMLS = calculateMedian(rentPerSqftArrayMLS);

      if (propertyWithPerSqftMLS) {
        rentAveragePerSqftMLS = rentPerSqftSumMLS / propertyWithPerSqftMLS;
        hasAveragePerSqftMLS = 1;
      }
    }
    // calculate national operators average for selected rows
    if (
      selectedRowKeysNationalOperatorsParam &&
      selectedRowKeysNationalOperatorsParam.length > 0
    ) {
      props.currentNationalOperatorsPropertiesFiltered.forEach((property) => {
        if (selectedRowKeysNationalOperatorsParam.includes(property.base_id)) {
          rentSumNationalOperators += property.rent;
          if (property.rent && property.square_feet) {
            rentPerSqftSumNationalOperators +=
              property.rent / property.square_feet;
            propertyWithPerSqftNationalOperators++;
          }
          // for median SFR
          rentArraySFR.push(property.rent);
          // for per Sqft median SFR
          if (property.rent && property.square_feet) {
            rentPerSqftArraySFR.push(property.rent / property.square_feet);
          }
        }
      });
      rentAverageNationalOperators =
        rentSumNationalOperators / selectedRowKeysNationalOperatorsParam.length;
      // if selectedRowKeys.length > 0, it's in calculation, therefore hasAverage = 1
      hasAverageNationalOperators = 1;
      // calculate median
      rentMedianSFR = calculateMedian(rentArraySFR);
      // calculate per Sqft median
      rentPerSqftMedianSFR = calculateMedian(rentPerSqftArraySFR);

      if (propertyWithPerSqftNationalOperators) {
        rentAveragePerSqftNationalOperators =
          rentPerSqftSumNationalOperators /
          propertyWithPerSqftNationalOperators;
        hasAveragePerSqftNationalOperators = 1;
      }
    }
    // calculate Hot Pads average for selected rows
    if (selectedRowKeysHotPadsParam && selectedRowKeysHotPadsParam.length > 0) {
      props.currentHotPadsPropertiesFiltered.forEach((property) => {
        if (selectedRowKeysHotPadsParam.includes(property.base_id)) {
          rentSumHotPads += property.rent;
          if (property.rent && property.square_feet) {
            rentPerSqftSumHotPads += property.rent / property.square_feet;
            propertyWithPerSqftHotPads++;
          }
          // for median SFR
          rentArrayHotPads.push(property.rent);
          // for per Sqft median SFR
          if (property.rent && property.square_feet) {
            rentPerSqftArrayHotPads.push(property.rent / property.square_feet);
          }
        }
      });
      rentAverageHotPads = rentSumHotPads / selectedRowKeysHotPadsParam.length;
      // if selectedRowKeys.length > 0, it's in calculation, therefore hasAverage = 1
      hasAverageHotPads = 1;
      // calculate median
      rentMedianHotPads = calculateMedian(rentArrayHotPads);
      // calculate per Sqft median
      rentPerSqftMedianHotPads = calculateMedian(rentPerSqftArrayHotPads);

      if (propertyWithPerSqftHotPads) {
        rentAveragePerSqftHotPads =
          rentPerSqftSumHotPads / propertyWithPerSqftHotPads;
        hasAveragePerSqftHotPads = 1;
      }
    }
    // if selectedRowKeys.length > 0, it's in calculation, therefore hasAverage = 1
    if (
      (props.searchingMode === 'Lease' &&
        props.subjectPropertyParcelData.rent > 0) ||
      (props.searchingMode === 'Sale' &&
        props.subjectPropertyParcelData.sales > 0)
    ) {
      hasAverageSubjectProperty = 1;
      if (props.subjectPropertyParcelData.total_area_sq_ft > 0) {
        hasAveragePerSqftSubjectProperty = 1;
      }
    }
    // for cases when rent is missing in parcel data
    const subjectPropertyAVM =
      (props.searchingMode === 'Lease'
        ? props.subjectPropertyParcelData.rent
        : props.subjectPropertyParcelData.sales) || 0;
    const subjectPropertyRentPerSqft =
      (props.searchingMode === 'Lease'
        ? props.subjectPropertyParcelData.rent
        : props.subjectPropertyParcelData.sales) /
        props.subjectPropertyParcelData.total_area_sq_ft || 0;
    // calculate average of all methods
    // for cases when all 3 sources are empty
    if (
      hasAverageMLS +
      hasAverageNationalOperators +
      hasAverageHotPads +
      hasAverageSubjectProperty
    ) {
      rentAverageAll =
        (rentAverageMLS +
          rentAverageNationalOperators +
          rentAverageHotPads +
          subjectPropertyAVM) /
        (hasAverageMLS +
          hasAverageNationalOperators +
          hasAverageHotPads +
          hasAverageSubjectProperty);
    }
    if (
      hasAveragePerSqftMLS +
      hasAveragePerSqftNationalOperators +
      hasAveragePerSqftHotPads +
      hasAveragePerSqftSubjectProperty
    ) {
      rentAveragePerSqftAll =
        (rentAveragePerSqftMLS +
          rentAveragePerSqftNationalOperators +
          rentAveragePerSqftHotPads +
          subjectPropertyRentPerSqft) /
        (hasAveragePerSqftMLS +
          hasAveragePerSqftNationalOperators +
          hasAveragePerSqftHotPads +
          hasAveragePerSqftSubjectProperty);
    }

    // under intelligent comping mode
    // calculate adjusted rent using formula in adjusted API return
    if (props.compingMode === 'intelligentComping') {
      if (props.searchingMode === 'Lease') {
        if (props.adjustedRentFormula) {
          // if user deselects all comps, return AVM
          switch (props.adjustedRentFormula) {
            case '((AVM Rent + Median Rent) / 2)':
              // rentAdjustedBT = (subjectPropertyAVM + rentMedianMLS) / 2;
              rentAdjustedBT = getAverageWithZeroValueCheck(
                subjectPropertyAVM,
                rentMedianMLS,
              );
              break;
            case 'Median Rent':
              rentAdjustedBT = rentMedianMLS || subjectPropertyAVM;
              break;
            case 'Median Rent * 1.02':
              rentAdjustedBT = rentMedianMLS * 1.02 || subjectPropertyAVM;
              break;
            case '((AVM Rent + Median SFR) / 2)':
              // rentAdjustedBT = (subjectPropertyAVM + rentMedianSFR) / 2;
              rentAdjustedBT = getAverageWithZeroValueCheck(
                subjectPropertyAVM,
                rentMedianSFR,
              );
              break;
            case 'Median SFR':
              rentAdjustedBT = rentMedianSFR || subjectPropertyAVM;
              break;
            case 'Median SFR * 1.02':
              rentAdjustedBT = rentMedianSFR * 1.02 || subjectPropertyAVM;
              break;
            case '((AVM Rent + Median 3rd Party) / 2)':
              // rentAdjustedBT = (subjectPropertyAVM + rentMedianHotPads) / 2;
              rentAdjustedBT = getAverageWithZeroValueCheck(
                subjectPropertyAVM,
                rentMedianHotPads,
              );
              break;
            case 'Median 3rd Party':
              rentAdjustedBT = rentMedianHotPads || subjectPropertyAVM;
              break;
            case 'Median 3rd Party * 1.02':
              rentAdjustedBT = rentMedianHotPads * 1.02 || subjectPropertyAVM;
              break;
            case 'AVM Rent':
              rentAdjustedBT = subjectPropertyAVM;
              break;
            default:
              rentAdjustedBT = subjectPropertyAVM;
              break;
          }
        }
      } else {
        switch (props.adjustedSalesFormula) {
          case '((AVM Sales + Median Sales) / 2)':
            // rentAdjustedBT = (subjectPropertyAVM + rentMedianMLS) / 2;
            rentAdjustedBT = getAverageWithZeroValueCheck(
              subjectPropertyAVM,
              rentMedianMLS,
            );
            break;
          case 'Median Sales':
            rentAdjustedBT = rentMedianMLS || subjectPropertyAVM;
            break;
          case 'Median Sales * 1.02':
            rentAdjustedBT = rentMedianMLS * 1.02 || subjectPropertyAVM;
            break;
          default:
            rentAdjustedBT = subjectPropertyAVM;
            break;
        }
      }
    } else {
      // the mode is show all or smart filter
      // we need to choose the market rent formula based on preference, comp availability, and difference between AVM and the preferred input
      if (props.searchingMode === 'Lease') {
        const getRentAVM = () => {
          if (
            !isEmpty(props.subjectPropertyParcelData) &&
            props.subjectPropertyParcelData.rent
          ) {
            return props.subjectPropertyParcelData.rent;
          } else return 'Missing Rent AVM';
        };
        const rentAVM = getRentAVM();
        let preferredInputName = '';
        const getCompBasedOnPreference = (preference) => {
          switch (preference) {
            case 'mls':
              if (
                selectedRowKeysMLSParam.length >= 3 ||
                // all comps are not enough and AVM is missing
                (selectedRowKeysMLSParam.length < 3 &&
                  selectedRowKeysMLSParam.length > 0 &&
                  selectedRowKeysNationalOperatorsParam.length < 3 &&
                  selectedRowKeysHotPadsParam.length < 3 &&
                  rentAVM === 'Missing Rent AVM') ||
                // MLS comps are fewer than 3 but more than 0, and the difference with AVM is greater than 20%
                (selectedRowKeysMLSParam.length > 0 &&
                  selectedRowKeysMLSParam.length < 3 &&
                  selectedRowKeysNationalOperatorsParam.length < 3 &&
                  selectedRowKeysHotPadsParam.length < 3 &&
                  rentAVM !== 'Missing Rent AVM' &&
                  Math.abs((rentAVM - rentMedianMLS) / rentAVM) > 0.2)
              ) {
                preferredInputName = 'MLS Median';
                return rentMedianMLS;
              } else if (
                selectedRowKeysNationalOperatorsParam.length >= 3 ||
                // all comps are not enough, MLS has no comps, and AVM is missing
                (selectedRowKeysMLSParam.length === 0 &&
                  selectedRowKeysNationalOperatorsParam.length < 3 &&
                  selectedRowKeysNationalOperatorsParam.length > 0 &&
                  selectedRowKeysHotPadsParam.length < 3 &&
                  rentAVM === 'Missing Rent AVM') ||
                // MLS has no comps, and the difference with AVM is greater than 20%
                (selectedRowKeysMLSParam.length === 0 &&
                  selectedRowKeysNationalOperatorsParam.length < 3 &&
                  selectedRowKeysNationalOperatorsParam.length > 0 &&
                  selectedRowKeysHotPadsParam.length < 3 &&
                  rentAVM !== 'Missing Rent AVM' &&
                  Math.abs((rentAVM - rentMedianSFR) / rentAVM) > 0.2)
              ) {
                preferredInputName = 'SFR Operator Median';
                return rentMedianSFR;
              } else if (
                selectedRowKeysHotPadsParam.length >= 3 ||
                // all comps are not enough, MLS and SFR have no comps, and AVM is missing
                (selectedRowKeysMLSParam.length === 0 &&
                  selectedRowKeysNationalOperatorsParam.length === 0 &&
                  selectedRowKeysHotPadsParam.length < 3 &&
                  selectedRowKeysHotPadsParam.length > 0 &&
                  rentAVM === 'Missing Rent AVM') ||
                // MLS and SFR have no comps, and the difference with AVM is greater than 20%
                (selectedRowKeysMLSParam.length === 0 &&
                  selectedRowKeysNationalOperatorsParam.length === 0 &&
                  selectedRowKeysHotPadsParam.length < 3 &&
                  selectedRowKeysHotPadsParam.length > 0 &&
                  rentAVM !== 'Missing Rent AVM' &&
                  Math.abs((rentAVM - rentMedianHotPads) / rentAVM) > 0.2)
              ) {
                preferredInputName = '3rd Party Median';
                return rentMedianHotPads;
              } else if (rentAVM !== 'Missing Rent AVM') {
                preferredInputName = 'AVM';
                rentAdjustedFormula = 'AVM';
                return getRentAVM();
              }
            case 'sfr':
              if (
                selectedRowKeysNationalOperatorsParam.length >= 3 ||
                // all comps are not enough and AVM is missing
                (selectedRowKeysNationalOperatorsParam.length < 3 &&
                  selectedRowKeysNationalOperatorsParam.length > 0 &&
                  selectedRowKeysMLSParam.length < 3 &&
                  selectedRowKeysHotPadsParam.length < 3 &&
                  rentAVM === 'Missing Rent AVM') ||
                // SFR comps are fewer than 3 but more than 0, and the difference with AVM is greater than 20%
                (selectedRowKeysNationalOperatorsParam.length > 0 &&
                  selectedRowKeysNationalOperatorsParam.length < 3 &&
                  selectedRowKeysMLSParam.length < 3 &&
                  selectedRowKeysHotPadsParam.length < 3 &&
                  rentAVM !== 'Missing Rent AVM' &&
                  Math.abs((rentAVM - rentMedianSFR) / rentAVM) > 0.2)
              ) {
                preferredInputName = 'SFR Operator Median';
                return rentMedianSFR;
              } else if (
                selectedRowKeysMLSParam.length >= 3 ||
                // all comps are not enough, SFR has no comps, and AVM is missing
                (selectedRowKeysNationalOperatorsParam.length === 0 &&
                  selectedRowKeysMLSParam.length < 3 &&
                  selectedRowKeysMLSParam.length > 0 &&
                  selectedRowKeysHotPadsParam.length < 3 &&
                  rentAVM === 'Missing Rent AVM') ||
                // SFR has no comps, and the difference with AVM is greater than 20%
                (selectedRowKeysNationalOperatorsParam.length === 0 &&
                  selectedRowKeysMLSParam.length < 3 &&
                  selectedRowKeysMLSParam.length > 0 &&
                  selectedRowKeysHotPadsParam.length < 3 &&
                  rentAVM !== 'Missing Rent AVM' &&
                  Math.abs((rentAVM - rentMedianMLS) / rentAVM) > 0.2)
              ) {
                preferredInputName = 'MLS Median';
                return rentMedianMLS;
              } else if (
                selectedRowKeysHotPadsParam.length >= 3 ||
                // all comps are not enough, SFR and MLS have no comps, and AVM is missing
                (selectedRowKeysNationalOperatorsParam.length === 0 &&
                  selectedRowKeysMLSParam.length === 0 &&
                  selectedRowKeysHotPadsParam.length < 3 &&
                  selectedRowKeysHotPadsParam.length > 0 &&
                  rentAVM === 'Missing Rent AVM') ||
                // SFR and MLS have no comps, and the difference with AVM is greater than 20%
                (selectedRowKeysNationalOperatorsParam.length === 0 &&
                  selectedRowKeysMLSParam.length === 0 &&
                  selectedRowKeysHotPadsParam.length < 3 &&
                  selectedRowKeysHotPadsParam.length > 0 &&
                  rentAVM !== 'Missing Rent AVM' &&
                  Math.abs((rentAVM - rentMedianHotPads) / rentAVM) > 0.2)
              ) {
                preferredInputName = '3rd Party Median';
                return rentMedianHotPads;
              } else if (rentAVM !== 'Missing Rent AVM') {
                preferredInputName = 'AVM';
                rentAdjustedFormula = 'AVM';
                return getRentAVM();
              }
            case 'hotpads':
              if (
                selectedRowKeysHotPadsParam.length >= 3 ||
                // all comps are not enough and AVM is missing
                (selectedRowKeysHotPadsParam.length < 3 &&
                  selectedRowKeysHotPadsParam.length > 0 &&
                  selectedRowKeysMLSParam.length < 3 &&
                  selectedRowKeysNationalOperatorsParam.length < 3 &&
                  rentAVM === 'Missing Rent AVM') ||
                // 3rd party comps are fewer than 3 but more than 0, and the difference with AVM is greater than 20%
                (selectedRowKeysHotPadsParam.length > 0 &&
                  selectedRowKeysHotPadsParam.length < 3 &&
                  selectedRowKeysMLSParam.length < 3 &&
                  selectedRowKeysNationalOperatorsParam.length < 3 &&
                  rentAVM !== 'Missing Rent AVM' &&
                  Math.abs((rentAVM - rentMedianHotPads) / rentAVM) > 0.2)
              ) {
                preferredInputName = '3rd Party Median';
                return rentMedianHotPads;
              } else if (
                selectedRowKeysMLSParam.length >= 3 ||
                // all comps are not enough, 3rd party has no comps, and AVM is missing
                (selectedRowKeysHotPadsParam.length === 0 &&
                  selectedRowKeysMLSParam.length < 3 &&
                  selectedRowKeysMLSParam.length > 0 &&
                  selectedRowKeysNationalOperatorsParam.length < 3 &&
                  rentAVM === 'Missing Rent AVM') ||
                // 3rd party has no comps, and the difference with AVM is greater than 20%
                (selectedRowKeysHotPadsParam.length === 0 &&
                  selectedRowKeysMLSParam.length < 3 &&
                  selectedRowKeysMLSParam.length > 0 &&
                  selectedRowKeysNationalOperatorsParam.length < 3 &&
                  rentAVM !== 'Missing Rent AVM' &&
                  Math.abs((rentAVM - rentMedianMLS) / rentAVM) > 0.2)
              ) {
                preferredInputName = 'MLS Median';
                return rentMedianMLS;
              } else if (
                selectedRowKeysNationalOperatorsParam.length >= 3 ||
                // all comps are not enough, 3rd party and MLS have no comps, and AVM is missing
                (selectedRowKeysHotPadsParam.length === 0 &&
                  selectedRowKeysMLSParam.length === 0 &&
                  selectedRowKeysNationalOperatorsParam.length < 3 &&
                  selectedRowKeysNationalOperatorsParam.length > 0 &&
                  rentAVM === 'Missing Rent AVM') ||
                // 3rd party and MLS have no comps, and the difference with AVM is greater than 20%
                (selectedRowKeysHotPadsParam.length === 0 &&
                  selectedRowKeysMLSParam.length === 0 &&
                  selectedRowKeysNationalOperatorsParam.length < 3 &&
                  selectedRowKeysNationalOperatorsParam.length > 0 &&
                  rentAVM !== 'Missing Rent AVM' &&
                  Math.abs((rentAVM - rentMedianSFR) / rentAVM) > 0.2)
              ) {
                preferredInputName = 'SFR Operator Median';
                return rentMedianSFR;
              } else if (rentAVM !== 'Missing Rent AVM') {
                preferredInputName = 'AVM';
                rentAdjustedFormula = 'AVM';
                return getRentAVM();
              }
            default:
              return 'Invalid preference';
          }
        };
        const getDifferenceBetweenAVMAndPreferredInput = (preference) => {
          console.log('preference', preference);
          const preferredInput = getCompBasedOnPreference(preference);
          // console.log(
          //   'preferredInputName',
          //   preferredInputName,
          //   'preferredInput',
          //   preferredInput,
          // );
          if (
            preferredInputName !== 'AVM' &&
            preferredInput &&
            !['Not enough comps', 'Invalid preference'].includes(preferredInput)
          ) {
            if (rentAVM !== 'Missing Rent AVM') {
              const differentRatio = (rentAVM - preferredInput) / rentAVM;
              return differentRatio;
            } else if (rentAVM === 'Missing Rent AVM' && preferredInput) {
              rentAdjustedBT = preferredInput;
              rentAdjustedFormula = `${preferredInputName}`;
              MLSMedianToAVMRatio = null;
              return null;
            } else {
              rentAdjustedFormula = 'Cannot be calculated';
              return null;
            }
          } else if (preferredInputName === 'AVM') {
            rentAdjustedBT = rentAVM;
            rentAdjustedFormula = 'AVM';
            MLSMedianToAVMRatio = null;
            return null;
          }
        };
        const preferredInput = getCompBasedOnPreference(
          props.marketRentPreference,
        );
        console.log('preferredInput', preferredInput);
        const differentRatio = getDifferenceBetweenAVMAndPreferredInput(
          props.marketRentPreference,
        );
        console.log('differentRatio', differentRatio);
        switch (true) {
          case typeof differentRatio === 'number' &&
            differentRatio >= 10 / 100 &&
            differentRatio < 20 / 100:
          case typeof differentRatio === 'number' &&
            differentRatio < -10 / 100 &&
            differentRatio >= -20 / 100:
          case typeof differentRatio === 'number' &&
            differentRatio >= -5 / 100 &&
            differentRatio < 5 / 100:
            rentAdjustedBT = (rentAVM + preferredInput) / 2;
            rentAdjustedFormula = `(AVM + ${preferredInputName}) / 2`;
            MLSMedianToAVMRatio = differentRatio;
            break;
          case typeof differentRatio === 'number' &&
            differentRatio >= -10 / 100 &&
            differentRatio < -5 / 100:
          case typeof differentRatio === 'number' && differentRatio >= 20 / 100:
          case typeof differentRatio === 'number' && differentRatio < -20 / 100:
            rentAdjustedBT = preferredInput;
            rentAdjustedFormula = `${preferredInputName}`;
            MLSMedianToAVMRatio = differentRatio;
            break;
          case typeof differentRatio === 'number' &&
            differentRatio >= 5 / 100 &&
            differentRatio < 10 / 100:
            rentAdjustedBT = preferredInput * (1 + 2 / 100);
            rentAdjustedFormula = `${preferredInputName} * 1.02`;
            MLSMedianToAVMRatio = differentRatio;
            break;
          default:
            break;
        }
      } else {
        // sale mode for show all and match modes
        const getSalesAVM = () => {
          if (
            !isEmpty(props.subjectPropertyParcelData) &&
            props.subjectPropertyParcelData.sales
          ) {
            return props.subjectPropertyParcelData.sales;
          } else return 'Missing Sales AVM';
        };
        const salesAVM = getSalesAVM();
        const getDifferenceBetweenAVMAndMedianMLS = () => {
          if (salesAVM !== 'Missing Sales AVM') {
            if (selectedRowKeysMLSParam.length >= 3) {
              const differentRatio = (salesAVM - rentMedianMLS) / salesAVM;
              return differentRatio;
            } else {
              return 'Not enough comps';
            }
          } else if (salesAVM === 'Missing Sales AVM' && rentMedianMLS) {
            // AVM is missing but MLS median is available
            return 'Use MLS median when AVM is missing';
          } else {
            // AVM is missing and not enough comps
            rentAdjustedFormula = 'Cannot be calculated';
            return;
          }
        };
        const differentRatio = getDifferenceBetweenAVMAndMedianMLS();
        console.log('differentRatio', differentRatio);
        switch (true) {
          case typeof differentRatio === 'number' &&
            differentRatio >= 5 / 100 &&
            differentRatio < 20 / 100:
          case typeof differentRatio === 'number' &&
            differentRatio < -5 / 100 &&
            differentRatio >= -20 / 100:
          case typeof differentRatio === 'number' &&
            differentRatio >= -2.5 / 100 &&
            differentRatio < 2.5 / 100:
            rentAdjustedBT = (salesAVM + rentMedianMLS) / 2;
            rentAdjustedFormula = `(AVM + MLS Median) / 2`;
            MLSMedianToAVMRatio = differentRatio;
            break;
          case typeof differentRatio === 'number' &&
            differentRatio >= -5 / 100 &&
            differentRatio < -2.5 / 100:
          case typeof differentRatio === 'number' && differentRatio >= 20 / 100:
          case typeof differentRatio === 'number' && differentRatio < -20 / 100:
            rentAdjustedBT = rentMedianMLS;
            rentAdjustedFormula = `MLS Median`;
            MLSMedianToAVMRatio = differentRatio;
            break;
          case typeof differentRatio === 'number' &&
            differentRatio >= 2.5 / 100 &&
            differentRatio < 5 / 100:
            rentAdjustedBT = rentMedianMLS * (1 + 2 / 100);
            rentAdjustedFormula = `MLS Median * 1.02`;
            MLSMedianToAVMRatio = differentRatio;
            break;
          case differentRatio === 'Not enough comps':
            rentAdjustedBT = salesAVM;
            rentAdjustedFormula = 'AVM';
            break;
          case differentRatio === 'Use MLS median when AVM is missing':
            rentAdjustedBT = rentMedianMLS;
            rentAdjustedFormula = 'MLS Median';
            break;
          default:
            break;
        }
      }
    }

    console.log('rentAdjustedBT', rentAdjustedBT);

    return {
      rentAverageMLS: rentAverageMLS,
      rentAverageNationalOperators: rentAverageNationalOperators,
      rentAverageHotPads: rentAverageHotPads,
      rentAverageAll: rentAverageAll,
      MLSAverageToAVMRatio: MLSAverageToAVMRatio,
      MLSMedianToAVMRatio: MLSMedianToAVMRatio,
      rentAdjustedBT: rentAdjustedBT,
      rentAdjustedFormula: rentAdjustedFormula,
      rentAveragePerSqftMLS: rentAveragePerSqftMLS,
      rentAveragePerSqftNationalOperators: rentAveragePerSqftNationalOperators,
      rentAveragePerSqftHotPads: rentAveragePerSqftHotPads,
      rentAveragePerSqftAll: rentAveragePerSqftAll,
      rentMedianMLS: rentMedianMLS,
      rentMedianSFR: rentMedianSFR,
      rentMedianHotPads: rentMedianHotPads,
      rentPerSqftMedianMLS: rentPerSqftMedianMLS,
      rentPerSqftMedianSFR: rentPerSqftMedianSFR,
      rentPerSqftMedianHotPads: rentPerSqftMedianHotPads,
    };
  };

  // calculate average rent for PadSplit
  const getAveragePadSplit = (
    currentPadSplitPropertiesFilteredParam,
    selectedRowKeysPadSplit,
  ) => {
    let rentTotal = 0,
      roomTotal = 0,
      rentTotalPrivateBath = 0,
      roomTotalPrivateBath = 0,
      rentTotalSharedBath = 0,
      roomTotalSharedBath = 0,
      rentAveragePadSplit = 0,
      rentAveragePadSplitPrivateBath = 0,
      rentAveragePadSplitSharedBath = 0;

    const selectedProperties = currentPadSplitPropertiesFilteredParam.filter(
      (property) => {
        return selectedRowKeysPadSplit.includes(property.property_id);
      },
    );

    for (const property of selectedProperties) {
      if (
        property.rooms_with_shared_bath_price &&
        property.active_rooms_with_shared_bath
      ) {
        rentTotal +=
          property.rooms_with_shared_bath_price *
          property.active_rooms_with_shared_bath;
        roomTotal += property.active_rooms_with_shared_bath;
        rentTotalSharedBath +=
          property.rooms_with_shared_bath_price *
          property.active_rooms_with_shared_bath;
        roomTotalSharedBath += property.active_rooms_with_shared_bath;
      }
      if (
        property.rooms_with_private_bath_price &&
        property.active_rooms_with_private_bath
      ) {
        rentTotal +=
          property.rooms_with_private_bath_price *
          property.active_rooms_with_private_bath;
        roomTotal += property.active_rooms_with_private_bath;
        rentTotalPrivateBath +=
          property.rooms_with_private_bath_price *
          property.active_rooms_with_private_bath;
        roomTotalPrivateBath += property.active_rooms_with_private_bath;
      }
    }

    // calculate average
    rentAveragePadSplit = rentTotal / roomTotal;
    rentAveragePadSplitPrivateBath =
      rentTotalPrivateBath / roomTotalPrivateBath;
    rentAveragePadSplitSharedBath = rentTotalSharedBath / roomTotalSharedBath;

    return {
      rentAveragePadSplit: rentAveragePadSplit,
      rentAveragePadSplitPrivateBath: rentAveragePadSplitPrivateBath,
      rentAveragePadSplitSharedBath: rentAveragePadSplitSharedBath,
    };
  };

  // calculate multi family average rents
  const getRentAverageMultiFamily = (currentMultiFamilyPropertiesParam) => {
    let twoBRSum = 0,
      threeBRSum = 0,
      twoBRAvailCalculate = 0, // for calculating avg., only counted when a listing has both rent and avail
      threeBRAvailCalculate = 0,
      twoBRAvail = 0,
      threeBRAvail = 0,
      totalRelevantUnits = 0;
    for (const property of currentMultiFamilyPropertiesParam) {
      if (property.two_br_avail) {
        if (property.two_br_rent) {
          twoBRSum += property.two_br_avail * property.two_br_rent;
          twoBRAvailCalculate += property.two_br_avail;
        }
        twoBRAvail += property.two_br_avail;
      }
      if (property.three_br_avail) {
        if (property.three_br_rent) {
          threeBRSum += property.three_br_avail * property.three_br_rent;
          threeBRAvailCalculate += property.three_br_avail;
        }
        threeBRAvail += property.three_br_avail;
      }
      totalRelevantUnits +=
        property.total_relevant_units - property.four_br_units;
    }

    let results = {};

    if (twoBRAvailCalculate) {
      results.rentAverageTwoBR = twoBRSum / twoBRAvailCalculate;
      results.twoBRAvail = twoBRAvail;
    } else {
      results.rentAverageTwoBR = 0;
      results.twoBRAvail = twoBRAvail;
    }

    if (threeBRAvailCalculate) {
      results.rentAverageThreeBR = threeBRSum / threeBRAvailCalculate;
      results.threeBRAvail = threeBRAvail;
    } else {
      results.rentAverageThreeBR = 0;
      results.threeBRAvail = threeBRAvail;
    }

    results.multiFamilyTotalRelevantUnit = totalRelevantUnits;

    return results;
  };

  const resultsRentAverageMLSAndSFR = getRentAverages(
    props.searchingMode === 'Lease'
      ? props.selectedRowKeysMLSLease
      : props.selectedRowKeysMLSSale,
    props.selectedRowKeysNationalOperators,
    props.selectedRowKeysHotPads,
  );
  console.log('resultsRentAverageMLSAndSFR', resultsRentAverageMLSAndSFR);

  const resultsRentAveragePadSplit = getAveragePadSplit(
    props.currentPadSplitPropertiesFiltered,
    props.selectedRowKeysPadSplit,
  );

  const resultsRentAverageMultiFamily = getRentAverageMultiFamily(
    props.currentMultiFamilyProperties,
  );

  console.log('userGroup: ', props.userGroup);

  const onChangeSelectedCompTables = (checked, tableName) => {
    if (checked && !props.selectedCompTables.includes(tableName)) {
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          selectedCompTables: [...props.selectedCompTables, tableName],
        },
      });
    } else if (!checked && props.selectedCompTables.includes(tableName)) {
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          selectedCompTables: props.selectedCompTables.filter(
            (table) => table !== tableName,
          ),
        },
      });
    }
  };

  //* Table Select Options for SFR Comps Lease
  const tableViewSettings = [
    {
      label: (
        <Checkbox
          checked={props.selectedCompTables.includes('MLS')}
          onClick={(e) => e.stopPropagation()}
          onChange={(e) => onChangeSelectedCompTables(e.target.checked, 'MLS')}
          style={{ width: '100%', height: '100%' }}
        >
          MLS
        </Checkbox>
      ),
    },
    {
      label: (
        <Checkbox
          checked={props.selectedCompTables.includes(
            'National SFR Operators Listings',
          )}
          onClick={(e) => e.stopPropagation()}
          onChange={(e) =>
            onChangeSelectedCompTables(
              e.target.checked,
              'National SFR Operators Listings',
            )
          }
        >
          National SFR Operators Listings
        </Checkbox>
      ),
    },
    // {
    //   label: (
    //     <Checkbox
    //       checked={props.selectedCompTables.includes('Built For Rent')}
    //       onClick={(e) => e.stopPropagation()}
    //       onChange={(e) =>
    //         onChangeSelectedCompTables(e.target.checked, 'Built For Rent')
    //       }
    //     >
    //       Built For Rent
    //     </Checkbox>
    //   ),
    // },
    {
      label: (
        <Checkbox
          checked={props.selectedCompTables.includes('Portal Listings')}
          onClick={(e) => e.stopPropagation()}
          onChange={(e) =>
            onChangeSelectedCompTables(e.target.checked, 'Portal Listings')
          }
        >
          Portal Listings
        </Checkbox>
      ),
    },
    {
      label: (
        <Checkbox
          checked={props.selectedCompTables.includes('Single-Family')}
          onClick={(e) => e.stopPropagation()}
          onChange={(e) =>
            onChangeSelectedCompTables(e.target.checked, 'Single-Family')
          }
          style={{ width: '100%', height: '100%' }}
        >
          Secondary Portal Listings
        </Checkbox>
      ),
    },
  ];
  //* Table Select Options for SFR Comps Sale
  const tableViewSettingsForSale = [
    {
      label: (
        <Checkbox
          checked={props.selectedCompTables.includes('MLS')}
          onClick={(e) => e.stopPropagation()}
          onChange={(e) => onChangeSelectedCompTables(e.target.checked, 'MLS')}
          style={{ width: '100%', height: '100%' }}
        >
          MLS
        </Checkbox>
      ),
    },
    {
      label: (
        <Checkbox
          checked={props.selectedCompTables.includes('Last Sale')}
          onClick={(e) => e.stopPropagation()}
          onChange={(e) =>
            onChangeSelectedCompTables(e.target.checked, 'Last Sale')
          }
          style={{ width: '100%', height: '100%' }}
        >
          Last Sale Public Record
        </Checkbox>
      ),
    },
  ];
  //* Table Select Options for MFR Comps Sale
  const tableViewSettingsForMFRComps = [
    {
      label: (
        <Checkbox
          checked={props.selectedCompTables.includes('Multi-family-2')}
          onClick={(e) => e.stopPropagation()}
          onChange={(e) =>
            onChangeSelectedCompTables(e.target.checked, 'Multi-family-2')
          }
          style={{ width: '100%', height: '100%' }}
        >
          Multi Family
        </Checkbox>
      ),
    },
  ];

  if (showRoomRental) {
    tableViewSettings.splice(3, 0, {
      label: (
        <Checkbox
          checked={props.selectedCompTables.includes('Room Rental')}
          onClick={(e) => e.stopPropagation()}
          onChange={(e) =>
            onChangeSelectedCompTables(e.target.checked, 'Room Rental')
          }
        >
          Room Rental
        </Checkbox>
      ),
    });
  }
  if (showMultiFamily) {
    tableViewSettingsForMFRComps.push({
      label: (
        <Checkbox
          checked={props.selectedCompTables.includes('Multi-Family Listings')}
          onClick={(e) => e.stopPropagation()}
          onChange={(e) =>
            onChangeSelectedCompTables(
              e.target.checked,
              'Multi-Family Listings',
            )
          }
        >
          Multi-Family Listings
        </Checkbox>
      ),
    });
  }

  const getItems = () => {
    const baseItem = [
      //* SFR Comps
      {
        key: '1',
        // label: 'Comps',
        label: (
          <div className={styles.tabLabelText}>
            {props.searchingMode === 'Lease' || props.searchingMode === 'Sale'
              ? 'SFR'
              : 'Land'}
            <br />
            Comps
          </div>
        ),
        forceRender: true,
        children: (
          <div
            id="CMATabWrapper"
            key="CMA tab wrapper"
            className={styles.singleTabWrapperDoublePanels}
            // style={{
            //   height: '100%',
            // }}
          >
            <div
              key="scroll wrapper"
              id="resultTableScrollWrapper"
              className={styles.scrollWrapper}
              style={{
                // 8px is for gap between tab and filter side bar
                width: `calc(100% - 8px - ${
                  props.showFilterSideBar ? sideBarWidth : 0
                }px)`,
                transition: 'width 0.3s ease-in-out',
                overflow: 'auto',
              }}
            >
              {props.userGroup.includes('BridgeTower') && (
                <ClientsOwnProperties />
              )}

              <div key="MLS and SFR wrapper" className={styles.cardWrapper}>
                <ResultRow
                  key="result row"
                  rentAverageNationalOperators={
                    resultsRentAverageMLSAndSFR.rentAverageNationalOperators
                  }
                  rentAveragePerSqftNationalOperators={
                    resultsRentAverageMLSAndSFR.rentAveragePerSqftNationalOperators
                  }
                  rentAverageHotPads={
                    resultsRentAverageMLSAndSFR.rentAverageHotPads
                  }
                  rentAveragePerSqftHotPads={
                    resultsRentAverageMLSAndSFR.rentAveragePerSqftHotPads
                  }
                  rentAverageMLS={resultsRentAverageMLSAndSFR.rentAverageMLS}
                  rentAveragePerSqftMLS={
                    resultsRentAverageMLSAndSFR.rentAveragePerSqftMLS
                  }
                  rentAdjustedBT={resultsRentAverageMLSAndSFR.rentAdjustedBT}
                  rentAdjustedFormula={
                    resultsRentAverageMLSAndSFR.rentAdjustedFormula
                  }
                  MLSAverageToAVMRatio={
                    resultsRentAverageMLSAndSFR.MLSAverageToAVMRatio
                  }
                  rentAverageAll={resultsRentAverageMLSAndSFR.rentAverageAll}
                  rentAveragePerSqftAll={
                    resultsRentAverageMLSAndSFR.rentAveragePerSqftAll
                  }
                  rentMedianMLS={resultsRentAverageMLSAndSFR.rentMedianMLS}
                  rentMedianSFR={resultsRentAverageMLSAndSFR.rentMedianSFR}
                  rentMedianHotPads={
                    resultsRentAverageMLSAndSFR.rentMedianHotPads
                  }
                  rentPerSqftMedianMLS={
                    resultsRentAverageMLSAndSFR.rentPerSqftMedianMLS
                  }
                  rentPerSqftMedianSFR={
                    resultsRentAverageMLSAndSFR.rentPerSqftMedianSFR
                  }
                  rentPerSqftMedianHotPads={
                    resultsRentAverageMLSAndSFR.rentPerSqftMedianHotPads
                  }
                  MLSMedianToAVMRatio={
                    resultsRentAverageMLSAndSFR.MLSMedianToAVMRatio
                  }
                />

                <div key="divider" className={styles.dividerCardHeader} />
                {props.searchingMode === 'Lease' ? (
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      justifyContent: 'flex-end',
                      gap: '12px',
                      marginTop: '16px',
                    }}
                  >
                    <Radio.Group
                      value={tableView}
                      size="small"
                      onChange={(e) => {
                        setTableView(e.target.value);
                      }}
                    >
                      <Radio.Button value="default">Default</Radio.Button>
                      <Radio.Button value="combined">Combined</Radio.Button>
                    </Radio.Group>
                    <Dropdown
                      placement="bottomRight"
                      menu={{
                        items: tableViewSettings,
                      }}
                    >
                      <Button size="small">
                        <FaTableList color={'#555'} />
                      </Button>
                    </Dropdown>
                  </div>
                ) : (
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      justifyContent: 'flex-end',
                      gap: '12px',
                      marginTop: '16px',
                    }}
                  >
                    {props.searchingMode === 'Sale' && (
                      <Dropdown
                        placement="bottomRight"
                        menu={{
                          items: tableViewSettingsForSale,
                        }}
                      >
                        <Button size="small">
                          <FaTableList color={'#555'} />
                        </Button>
                      </Dropdown>
                    )}
                  </div>
                )}

                <ConfigProvider renderEmpty={EmptyState}>
                  {props.selectedCompTables.includes('MLS') &&
                    tableView === 'default' &&
                    props.searchingMode != 'Land' && (
                      <MLSV2
                        rentAverageMLS={
                          resultsRentAverageMLSAndSFR.rentAverageMLS
                        }
                        selectedRowKeysMLS={
                          props.searchingMode === 'Lease'
                            ? props.selectedRowKeysMLSLease
                            : props.selectedRowKeysMLSSale
                        }
                        onSelectChangeMLS={onSelectChangeMLS}
                        rentMedianMLS={
                          resultsRentAverageMLSAndSFR.rentMedianMLS
                        }
                      />
                      // <MLS
                      //   rentAverageMLS={
                      //     resultsRentAverageMLSAndSFR.rentAverageMLS
                      //   }
                      //   selectedRowKeysMLS={
                      //     props.searchingMode === 'Lease'
                      //       ? props.selectedRowKeysMLSLease
                      //       : props.selectedRowKeysMLSSale
                      //   }
                      //   onSelectChangeMLS={onSelectChangeMLS}
                      //   rentMedianMLS={
                      //     resultsRentAverageMLSAndSFR.rentMedianMLS
                      //   }
                      // />
                    )}

                  {/* //! ----------Lease Start------------ */}
                  {props.searchingMode === 'Lease' && (
                    <>
                      {tableView === 'default' && (
                        <>
                          {/* //! SFR */}
                          {props.selectedCompTables.includes(
                            'National SFR Operators Listings',
                          ) && (
                            <PortalListingTable
                              key="SFR"
                              type="SFR"
                              rentAverageNationalOperators={
                                resultsRentAverageMLSAndSFR.rentAverageNationalOperators
                              }
                              // rentAverageHotPads={resultsRentAverageMLSAndSFR.rentAverageHotPads}
                              selectedRowKeysNationalOperators={
                                props.selectedRowKeysNationalOperators
                              }
                              // selectedRowKeysHotPads={props.selectedRowKeysHotPads}
                              onSelectChangeNationalOperators={
                                onSelectChangeNationalOperators
                              }
                              // onSelectChangeHotPads={onSelectChangeHotPads}
                              rentMedianSFR={
                                resultsRentAverageMLSAndSFR.rentMedianSFR
                              }
                              // rentMedianHotPads={resultsRentAverageMLSAndSFR.rentMedianHotPads}
                            />
                          )}

                          {/* //! PadSplit */}
                          {showRoomRental &&
                            props.selectedCompTables.includes(
                              'Room Rental',
                            ) && (
                              <PadSplit
                                key="PadSplit"
                                type="PadSplit"
                                rentAveragePadSplit={
                                  resultsRentAveragePadSplit.rentAveragePadSplit
                                }
                                rentAveragePadSplitPrivateBath={
                                  resultsRentAveragePadSplit.rentAveragePadSplitPrivateBath
                                }
                                rentAveragePadSplitSharedBath={
                                  resultsRentAveragePadSplit.rentAveragePadSplitSharedBath
                                }
                              />
                            )}

                          {/* //! Portal Listings */}
                          {props.selectedCompTables.includes(
                            'Portal Listings',
                          ) && (
                            <PortalListingTable
                              key="HotPads"
                              type="HotPads"
                              rentAverageHotPads={
                                resultsRentAverageMLSAndSFR.rentAverageHotPads
                              }
                              selectedRowKeysHotPads={
                                props.selectedRowKeysHotPads
                              }
                              onSelectChangeHotPads={onSelectChangeHotPads}
                              rentMedianHotPads={
                                resultsRentAverageMLSAndSFR.rentMedianHotPads
                              }
                            />
                          )}
                          {/* {props.selectedCompTables.includes(
                            'Portal Listings',
                          ) && (
                            <NationalSFROperators
                              key="HotPads"
                              type="HotPads"
                              rentAverageHotPads={
                                resultsRentAverageMLSAndSFR.rentAverageHotPads
                              }
                              selectedRowKeysHotPads={
                                props.selectedRowKeysHotPads
                              }
                              onSelectChangeHotPads={onSelectChangeHotPads}
                              rentMedianHotPads={
                                resultsRentAverageMLSAndSFR.rentMedianHotPads
                              }
                            />
                          )} */}

                          {/* //! SFR From Realtor */}
                          {props.selectedCompTables.includes(
                            'Single-Family',
                          ) && <RealtorSFTable />}
                        </>
                      )}
                      {/* //! Combined Table */}
                      {tableView === 'combined' && <CombinedTable />}
                    </>
                  )}
                  {/* //! ----------Lease End------------ */}

                  {/* //! ----------Sale Start------------ */}
                  {props.searchingMode === 'Sale' &&
                    props.selectedCompTables.includes('Last Sale') && (
                      <LastSalePublicRecord />
                    )}
                  {/* //! ----------Sale End------------ */}

                  {/* //! ----------Land Start------------ */}
                  {props.searchingMode === 'Land' && (
                    <>
                      <LandCompTable />
                      <LandShowcaseTable />
                      <LandCrexiTable />
                    </>
                  )}
                  {/* //! ----------Land End------------ */}
                </ConfigProvider>
              </div>

              <div id="parcel-owner-temp-solution" style={{ display: 'none' }}>
                <ParcelOwnerSummary />
              </div>
            </div>
            <SidebarWrapper />
          </div>
        ),
      },
      //* Neighborhood Insights
      {
        key: '2',
        // label: 'Insights',
        label: (
          <div className={styles.tabLabelText}>
            Neighborhood
            <br />
            Insights
          </div>
        ),
        children: (
          <div className={styles.singleTabWrapperSinglePanel}>
            <ParcelOwnerSummary />
            {props.searchingMode === 'Lease' && <LeaseExpiry />}

            {/* <MLSListingSummary /> */}
            <div className={styles.cardWrapper}>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                }}
              >
                <div
                  className={styles.cardTitleH2}
                  style={{ marginBottom: '10px' }}
                >
                  Composition of Nearby Homes
                </div>
                <UnitMixParcel />
              </div>
            </div>
            <MarketConditionQueryProvider>
              <MarketConditionProvider
                isLeaseMode={props.searchingMode === 'Lease' ? true : false}
                serverType={serverType}
                searchingMode={props.searchingMode}
                areaData={
                  props.drawnCustomPolygons.length
                    ? [props.drawnCustomPolygons]
                    : props.eventCoordinates.length
                    ? {
                        latitude: props.eventCoordinates[1],
                        longitude: props.eventCoordinates[0],
                        radius: props.currentRadiusMile * 1609.34,
                      }
                    : undefined
                }
              >
                <MarketCondition />
              </MarketConditionProvider>
            </MarketConditionQueryProvider>
            <MarketConditionHistoryQueryProvider>
              <MarketConditionHistoryProvider
                isLeaseMode={props.searchingMode === 'Lease' ? true : false}
                serverType={serverType}
                searchingMode={props.searchingMode}
                areaData={
                  props.drawnCustomPolygons.length
                    ? {
                        type: 'multipolygon',
                        coordinates: [props.drawnCustomPolygons],
                      }
                    : props.eventCoordinates.length
                    ? {
                        type: 'point',
                        lat: props.eventCoordinates[1],
                        lng: props.eventCoordinates[0],
                        distance: props.currentRadiusMile * 1609.34,
                      }
                    : undefined
                }
              >
                <MarketConditionHistory />
              </MarketConditionHistoryProvider>
            </MarketConditionHistoryQueryProvider>
            {/* <MarketConditionGraph /> */}
            {/* <FairMarketRent /> */}
          </div>
        ),
        forceRender: true,
      },
      //* New Construction
      {
        key: '3',
        label: (
          <div className={styles.tabLabelText}>
            New
            <br />
            Construction
          </div>
        ),
        children: (
          <div className={styles.singleTabWrapperSinglePanel}>
            <NewBuilds />
          </div>
        ),
        forceRender: true,
      },
      //* School & Demographics
      {
        key: '4',
        label: (
          <div className={styles.tabLabelText}>
            School &<br />
            Demographics
          </div>
        ),
        children: (
          <div className={styles.singleTabWrapperSinglePanel}>
            <SchoolAndDemographic />
          </div>
        ),
        forceRender: true,
      },
      //* Building Permits
      {
        key: '5',
        label: (
          <div className={styles.tabLabelText}>
            Building
            <br />
            Permits
          </div>
        ),
        children: (
          <div className={styles.singleTabWrapperSinglePanel}>
            <BuildingPermitPanel />
          </div>
        ),

        forceRender: true,
      },
    ];

    // Pipeline
    if (props.searchingMode != 'Land') {
      baseItem.splice(
        6,
        0, //* PipeLine
        {
          key: '8',
          label: (
            <div className={styles.tabLabelText}>
              Pipe
              <br />
              Line
            </div>
          ),
          children: (
            <div className={styles.singleTabWrapperSinglePanel}>
              <BTRPipelinePanel />
              <NewlySubdividedPipeLineTable />
            </div>
          ),
          forceRender: true,
        },
      );
    }
    // Comp Insight
    if (props.searchingMode != 'Land') {
      baseItem.splice(
        1,
        0, //* Comp Insights
        {
          key: 'Comp Insights',
          // label: 'Comp Insights',
          label: (
            <div className={styles.tabLabelText}>
              Comp
              <br />
              Insights
            </div>
          ),
          children: (
            <div
              key="Comp Insights tab wrapper"
              className={styles.singleTabWrapperDoublePanels}
              // style={{
              //   height: '100%',
              // }}
            >
              <div
                key="scroll wrapper"
                // id="resultTableScrollWrapper"
                className={styles.scrollWrapper}
                style={{
                  // 8px is for gap between tab and filter side bar
                  width: `calc(100% - 8px - ${
                    props.showFilterSideBar ? sideBarWidth : 0
                  }px)`,
                  transition: 'width 0.3s ease-in-out',
                  overflow: 'auto',
                }}
              >
                <CompInsights />
                {/* <CompInsightsV2 /> */}
                <div className={styles.cardWrapper}>
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'space-between',
                    }}
                  >
                    <div className={styles.cardTitleH2}>
                      Composition of Nearby Transactions
                    </div>
                    <UnitMixGraph />
                  </div>
                </div>
              </div>
              <SidebarWrapper />
            </div>
          ),
        },
      );
    }
    // MFR
    if (props.searchingMode === 'Lease') {
      baseItem.splice(
        1,
        0, //* MFR Comps
        {
          key: '7',
          disabled: !props.searchingMode === 'Lease',
          // label: 'Comps',
          label: (
            <Badge count="New" size="small" offset={[5, -5]}>
              <div className={styles.tabLabelText}>
                MFR
                <br />
                Comps
              </div>
            </Badge>
          ),
          forceRender: true,
          children: userGroupHasAccess(
            props.selectedUserGroup,
            'Muilty Family',
          ) ? (
            <div
              id="CMATabWrapper"
              key="CMA tab wrapper"
              className={styles.singleTabWrapperDoublePanels}
              // style={{
              //   height: '100%',
              // }}
            >
              <div
                key="scroll wrapper"
                id="resultTableScrollWrapper"
                className={styles.scrollWrapper}
                style={{
                  // 8px is for gap between tab and filter side bar
                  width: `calc(100% - 8px - ${
                    props.showFilterSideBar ? sideBarWidth : 0
                  }px)`,
                  transition: 'width 0.3s ease-in-out',
                  overflow: 'auto',
                }}
              >
                <div key="MLS and SFR wrapper" className={styles.cardWrapper}>
                  {/* <ResultRow
                    key="result row"
                    rentAverageNationalOperators={
                      resultsRentAverageMLSAndSFR.rentAverageNationalOperators
                    }
                    rentAveragePerSqftNationalOperators={
                      resultsRentAverageMLSAndSFR.rentAveragePerSqftNationalOperators
                    }
                    rentAverageHotPads={
                      resultsRentAverageMLSAndSFR.rentAverageHotPads
                    }
                    rentAveragePerSqftHotPads={
                      resultsRentAverageMLSAndSFR.rentAveragePerSqftHotPads
                    }
                    rentAverageMLS={resultsRentAverageMLSAndSFR.rentAverageMLS}
                    rentAveragePerSqftMLS={
                      resultsRentAverageMLSAndSFR.rentAveragePerSqftMLS
                    }
                    rentAdjustedBT={resultsRentAverageMLSAndSFR.rentAdjustedBT}
                    rentAdjustedFormula={
                      resultsRentAverageMLSAndSFR.rentAdjustedFormula
                    }
                    MLSAverageToAVMRatio={
                      resultsRentAverageMLSAndSFR.MLSAverageToAVMRatio
                    }
                    rentAverageAll={resultsRentAverageMLSAndSFR.rentAverageAll}
                    rentAveragePerSqftAll={
                      resultsRentAverageMLSAndSFR.rentAveragePerSqftAll
                    }
                    rentMedianMLS={resultsRentAverageMLSAndSFR.rentMedianMLS}
                    rentMedianSFR={resultsRentAverageMLSAndSFR.rentMedianSFR}
                    rentMedianHotPads={
                      resultsRentAverageMLSAndSFR.rentMedianHotPads
                    }
                    rentPerSqftMedianMLS={
                      resultsRentAverageMLSAndSFR.rentPerSqftMedianMLS
                    }
                    rentPerSqftMedianSFR={
                      resultsRentAverageMLSAndSFR.rentPerSqftMedianSFR
                    }
                    rentPerSqftMedianHotPads={
                      resultsRentAverageMLSAndSFR.rentPerSqftMedianHotPads
                    }
                    MLSMedianToAVMRatio={
                      resultsRentAverageMLSAndSFR.MLSMedianToAVMRatio
                    }
                  /> */}
                  {/* <div key="divider" className={styles.dividerCardHeader} /> */}

                  {/* <div
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      justifyContent: 'flex-end',
                      gap: '12px',
                      marginTop: '16px',
                    }}
                  >
                    {!props.isLandMode && (
                      <Dropdown
                        placement="bottomRight"
                        menu={{
                          items: tableViewSettingsForMFRComps,
                          //TODO
                        }}
                      >
                        <Button size="small">
                          <FaTableList color={'#555'} />
                        </Button>
                      </Dropdown>
                    )}
                  </div> */}

                  <ConfigProvider renderEmpty={EmptyState}>
                    {/* {props.selectedCompTables.includes('Multi-family-2') && (
                      <RealtorMFTable />
                    )} */}

                    <MultiFamilyApartments />

                    {/* {props.selectedCompTables.includes(
                      'Multi-Family Listings',
                    ) && (
                      <MultiFamily
                        multiFamilyTotalRelevantUnit={
                          resultsRentAverageMultiFamily.multiFamilyTotalRelevantUnit
                        }
                        twoBRAvail={resultsRentAverageMultiFamily.twoBRAvail}
                        rentAverageTwoBR={
                          resultsRentAverageMultiFamily.rentAverageTwoBR
                        }
                        threeBRAvail={
                          resultsRentAverageMultiFamily.threeBRAvail
                        }
                        rentAverageThreeBR={
                          resultsRentAverageMultiFamily.rentAverageThreeBR
                        }
                      />
                    )} */}
                  </ConfigProvider>
                </div>

                <div
                  id="parcel-owner-temp-solution"
                  style={{ display: 'none' }}
                >
                  <ParcelOwnerSummary />
                </div>
              </div>
              <SidebarWrapper />
            </div>
          ) : (
            <div
              id="CMATabWrapper"
              key="CMA tab wrapper"
              className={styles.singleTabWrapperDoublePanels}
              // style={{
              //   height: '100%',
              // }}
            >
              <div
                key="scroll wrapper"
                id="resultTableScrollWrapper"
                className={styles.scrollWrapper}
                style={{
                  // 8px is for gap between tab and filter side bar
                  width: `calc(100% - 8px - ${
                    props.showFilterSideBar ? sideBarWidth : 0
                  }px)`,
                  transition: 'width 0.3s ease-in-out',
                  overflow: 'auto',
                }}
              >
                <div key="MLS and SFR wrapper" className={styles.cardWrapper}>
                  <p>Contact Us for Demo</p>
                </div>
              </div>
            </div>
          ),
        },
      );
    }
    // BFR
    if (props.searchingMode === 'Lease') {
      baseItem.splice(
        2,
        0,
        //* BFR Comps
        {
          key: '9',
          label: (
            <div className={styles.tabLabelText}>
              BFR
              <br />
              Comps
            </div>
          ),
          forceRender: true,
          children: (
            <div
              id="CMATabWrapper"
              key="CMA tab wrapper 2"
              className={styles.singleTabWrapperDoublePanels}
              // style={{
              //   height: '100%',
              // }}
            >
              <div
                key="scroll wrapper"
                id="resultTableScrollWrapper"
                className={styles.scrollWrapper}
                style={{
                  // 8px is for gap between tab and filter side bar
                  width: `calc(100% - 8px - ${
                    props.showFilterSideBar ? sideBarWidth : 0
                  }px)`,
                  transition: 'width 0.3s ease-in-out',
                  overflow: 'auto',
                }}
              >
                <BuiltForRentTable2 />
                <BTRCommunityInfoTable />
              </div>

              <SidebarWrapper />
            </div>
          ),
        },
      );
    }

    if (
      userGroupHasAccess(props.selectedUserGroup, 'Affordable Housing') &&
      props.searchingMode != 'Land'
    ) {
      baseItem.push(
        //* Affordable Housing
        {
          key: '6',
          label: (
            <div className={styles.tabLabelText}>
              Affordable
              <br />
              Housing
            </div>
          ),
          children: (
            <div
              key="affordable housing tab wrapper"
              className={styles.singleTabWrapperDoublePanels}
              // style={{
              //   height: '100%',
              // }}
            >
              <div
                key="scroll wrapper"
                // id="resultTableScrollWrapper"
                className={styles.scrollWrapper}
                style={{
                  // 8px is for gap between tab and filter side bar
                  width: `calc(100% - 8px - ${
                    props.showFilterSideBar ? sideBarWidth : 0
                  }px)`,
                  transition: 'width 0.3s ease-in-out',
                  overflow: 'auto',
                }}
              >
                <AffordableHousingPage />
              </div>
              <SidebarWrapper />
            </div>
          ),
          forceRender: true,
        },
      );
    }

    return baseItem;
  };

  let exportCSVElement = null;
  if (props.cmaTabKey === '1' && props.searchingMode != 'Land') {
    exportCSVElement = <ExportAllCSV />;
  } else if (props.cmaTabKey === '3') {
    exportCSVElement = <ExportNewBuildsCSV />;
  } else if (props.cmaTabKey === '9') {
    exportCSVElement = <ExportBFRCSV />;
  } else if (props.searchingMode === 'Land' && props.cmaTabKey === '1') {
    exportCSVElement = <ExportLandCSV />;
  }

  console.log('csv', props.searchingMode, props.cmaTabKey);

  return (
    <div
      key="result table wrapper"
      className={styles.colWrapper}
      id="resultTableWrapper"
    >
      {/* TODO: Add TargetModal */}
      <TargetUploadModal />
      <BTRSubmissionModal />
      <BatchProcessor key="batch processor" />
      <ImportKMLModal key="import kml modal" />

      {props.showFilterModal && <FiltersLegacy />}
      {props.scorecardModalOpen && <GenerateScorecard />}
      {props.openMLSImageModal && <MLSModal />}

      {/* <HeatmapMenuContainer /> */}

      <Summary />

      <div className={styles.tabContainer}>
        <div
          key="tab wrapper"
          style={{
            width: `calc(100% - 8px)`,
          }}
        >
          <Tabs
            id="CMA_TAB"
            defaultActiveKey="1"
            activeKey={props.cmaTabKey}
            items={getItems()}
            tabBarGutter={20}
            tabBarExtraContent={
              <div
                key="tab bar extra wrapper"
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'flex-end',
                  alignItems: 'center',
                  gap: '8px',
                }}
              >
                {props.cmaTabKey === '1' &&
                ((props.savedMLSComps.length &&
                  props.savedMLSComps.length > 0) ||
                  (props.savedSFRComps && props.savedSFRComps.length > 0)) ? (
                  <button
                    key="show comp"
                    onClick={showSavedComps}
                    className={styles.showSavedCompsButton}
                  >
                    Saved
                    <br />
                    Comps
                  </button>
                ) : props.cmaTabKey === '1' ? (
                  <Tooltip title="Save MLS and natioinal SFR operators comps that are currently selected">
                    <button
                      key="save comp"
                      onClick={saveSelectedComps}
                      className={styles.csvButton}
                      style={{
                        display: checkIfCompsSelected() ? 'block' : 'none',
                        fontSize: 12,
                        lineHeight: 1.25,
                        textAlign: 'center',
                      }}
                    >
                      Save
                      <br />
                      Comps
                    </button>
                  </Tooltip>
                ) : null}
                {exportCSVElement}
              </div>
            }
            // centered set to false when tab bar extra buttons are shown
            // otherwise tab bar can't scroll back to the first tab
            // centered={
            //   props.cmaTabKey !== '1' ||
            //   (props.cmaTabKey === '1' &&
            //     !(
            //       props.savedMLSComps.length && props.savedMLSComps.length > 0
            //     ) &&
            //     !(props.savedSFRComps && props.savedSFRComps.length > 0) &&
            //     !checkIfCompsSelected())
            // }
            centered={true}
            tabPosition="top"
            onChange={(value) => {
              tabContentContainerRef.current.scrollTo({
                top: 0,
                behavior: 'smooth',
              });

              props.dispatch({
                type: 'CMA/saveCMAStates',
                payload: { cmaTabKey: value },
              });
              // close the filter sidebar if user switches to New Construction Homes tab
              if (value === '3') {
                props.dispatch({
                  type: 'CMA/saveCMAStates',
                  payload: {
                    showFilterSideBar: false,
                  },
                });
              }
              if (value === '6') {
                props.dispatch({
                  type: 'CMA/saveCMAStates',
                  payload: {
                    showFilterSideBar: false,
                  },
                });
              }
              if (value === '7') {
                props.dispatch({
                  type: 'CMA/saveCMAStates',
                  payload: {
                    selectedCompTables: [
                      ...props.selectedCompTables,
                      'Multi-family-2',
                    ],
                    compingMode: 'noFilter',
                  },
                });
              }
            }}
          />
        </div>
      </div>
      {/* </Spin> */}
    </div>
  );
};
export default connect(({ CMA }) => ({
  currentNationalOperatorsPropertiesFiltered:
    CMA.currentNationalOperatorsPropertiesFiltered,
  currentHotPadsPropertiesFiltered: CMA.currentHotPadsPropertiesFiltered,
  currentMultiFamilyProperties: CMA.currentMultiFamilyProperties,
  currentMLSPropertiesFiltered: CMA.currentMLSPropertiesFiltered,
  currentPadSplitPropertiesFiltered: CMA.currentPadSplitPropertiesFiltered,
  subjectPropertyParcelData: CMA.subjectPropertyParcelData,
  searchingMode: CMA.searchingMode,
  isLandMode: CMA.isLandMode,
  userGroup: CMA.userGroup,
  selectedUserGroup: CMA.selectedUserGroup,
  userEmail: CMA.userEmail,
  openMLSImageModal: CMA.openMLSImageModal,
  selectedRowKeysMLSLease: CMA.selectedRowKeysMLSLease,
  selectedRowKeysMLSSale: CMA.selectedRowKeysMLSSale,
  selectedRowKeysNationalOperators: CMA.selectedRowKeysNationalOperators,
  selectedRowKeysHotPads: CMA.selectedRowKeysHotPads,
  selectedRowKeysPadSplit: CMA.selectedRowKeysPadSplit,
  ClientTableSort: CMA.ClientTableSort,
  NSFRTableSort: CMA.NSFRTableSort,
  MLSTableSortLease: CMA.MLSTableSortLease,
  MLSTableSortSale: CMA.MLSTableSortSale,
  MultiFamilyTableSort: CMA.MultiFamilyTableSort,
  currentRadiusMile: CMA.currentRadiusMile,
  scorecardModalOpen: CMA.scorecardModalOpen,
  adjustedRentFormula: CMA.adjustedRentFormula,
  adjustedSalesFormula: CMA.adjustedSalesFormula,
  showFilterModal: CMA.showFilterModal,
  showFilterSideBar: CMA.showFilterSideBar,
  compingMode: CMA.compingMode,
  marketRentPreference: CMA.marketRentPreference,
  cmaTabKey: CMA.cmaTabKey,
  landTypeOptionsWithinMetro: CMA.landTypeOptionsWithinMetro,
  savedMLSComps: CMA.savedMLSComps,
  savedSFRComps: CMA.savedSFRComps,
  eventCoordinates: CMA.eventCoordinates,
  currentRadiusMile: CMA.currentRadiusMile,
  drawnCustomPolygons: CMA.drawnCustomPolygons,
  selectedCompTables: CMA.selectedCompTables,

  map: CMA.map,
}))(ResultTable);
