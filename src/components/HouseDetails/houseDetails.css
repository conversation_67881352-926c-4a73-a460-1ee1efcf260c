.houseDetailContainer {
  padding: 10px;
}
.houseDetailContainer p span {
  font-weight: bold;
}

.headingContainer {
  background-color: rgb(240, 240, 240);
  padding: 2px 10px;
  box-sizing: border-box;
  margin-bottom: 5px;
}
.headingContainer h3 {
  margin: 0;
}

.detailContainer {
  border: 1px solid rgb(240, 240, 240);
}

.detailListContainer {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 10px;
  padding: 0 10px;
}

@media only screen and (max-width: 950px) {
  .detailListContainer {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

.listContainer h4 {
  margin: 0;
}
