import React from 'react';
import { capitalize } from '../../../utils/strings';
import { formatter } from '../../../utils/money';
import styles from '../houseDetails.css';

const DetailList = (props) => {
  const title = props.title;
  const listItems = props.data;

  const listText = [];
  for (let [key, value] of Object.entries(listItems)) {
    if (value && value.length > 0) {
      let label = capitalize(key.replace(/_/g, ' '));

      let text = !isNaN(value) && value.length === 1 ? Number(value) : value; // for 1/0 for yes/no

      text =
        isNaN(value) && !label.toLowerCase().includes('email')
          ? capitalize(value.replace(/,/g, ', '))
          : text;

      if (label.slice(-2).toLowerCase().includes('yn')) {
        label = label.slice(0, -2) + 'YN';

        if (!isNaN(text)) {
          text = text === 0 ? 'No' : 'Yes';
        } else {
          text = text.toLowerCase().includes('true') ? 'Yes' : 'No';
        }
      } else if (isNaN(text) && text.includes('[') && text.includes(']')) {
        text = capitalize(
          text
            .replace('[', '')
            .replace(']', '')
            .replace(/"/g, '')
            .replace('/,/g', ', '),
        );
      }

      if (
        label.includes('Original List Price') ||
        label.includes('List Price') ||
        label.includes('Association Fee')
      ) {
        text = `$${formatter(text)}`;
      } else if (
        label.includes('Living Area') ||
        label.includes('Lot Size Square Feet') ||
        label.includes('Lot Size Acres') ||
        label.includes('Lot Size Area')
      ) {
        text = formatter(text);
      }

      listText.push(`${label}: ${text}`);
    }
  }

  if (listText.length > 0) {
    return (
      <section className={styles.listContainer}>
        <h4>{title}</h4>
        <ul>
          {listText.map((text, index) =>
            // temp fix
            title === 'Virtual Tour' ? (
              <li key={index}>
                Virtual Tour:{' '}
                <a
                  href={text.split('Toururlunbranded')[1].substring(1)}
                  target="_blank"
                >
                  Link
                </a>
              </li>
            ) : (
              <li key={index}>{text}</li>
            ),
          )}
        </ul>
      </section>
    );
  }
  return null;
};

export default DetailList;
