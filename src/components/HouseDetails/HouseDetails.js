import DescriptionDetail from './details/DescriptionDetail';
import ExteriorDetail from './details/ExteriorDetail';
import FinancialDetail from './details/FinancialDetail';
import InteriorDetail from './details/InteriorDetail';
import LocationDetail from './details/LocationDetail';
import OtherDetail from './details/OtherDetail';
import ParkingDetail from './details/ParkingDetail';
import UtilityDetail from './details/UtilityDetail';
import styles from './houseDetails.css';
import { capitalize } from '../../utils/strings';

function HouseDetails(props) {
  let ownerName = '';
  if (
    props.currentAPNOwner &&
    props.currentAPNOwner.owner_name &&
    props.currentAPNOwner.owner_name.length > 0
  ) {
    ownerName = props.currentAPNOwner.owner_name;
  }

  return (
    <div className={styles.houseDetailContainer}>
      {ownerName.length > 0 && (
        <p>
          Owned by: <b>{capitalize(ownerName)}</b>
        </p>
      )}
      <DescriptionDetail currentHouseDetails={props.currentHouseDetails} />
      <ParkingDetail currentHouseDetails={props.currentHouseDetails} />
      <InteriorDetail currentHouseDetails={props.currentHouseDetails} />
      <ExteriorDetail currentHouseDetails={props.currentHouseDetails} />
      <LocationDetail currentHouseDetails={props.currentHouseDetails} />
      <FinancialDetail currentHouseDetails={props.currentHouseDetails} />
      <UtilityDetail currentHouseDetails={props.currentHouseDetails} />
      <OtherDetail
        currentHouseDetails={props.currentHouseDetails}
        currentSingleListingBrokerOfficeInfo={
          props.currentSingleListingBrokerOfficeInfo
        }
      />
    </div>
  );
}

export default HouseDetails;
