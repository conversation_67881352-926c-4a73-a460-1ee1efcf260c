import Heading from '../utils/Heading';
import DetailList from '../utils/DetailList';
import styles from '../houseDetails.css';

function ExteriorDetail(props) {
  const {
    architectural_style,
    attached_garage_yn,
    construction_materials,
    exterior_features,
    fencing,
    foundation_details,
    lot_number,
    lot_size_acres,
    lot_size_area,
    lot_size_square_feet,
    lot_size_units,
    patio_and_porch_features,
    parcel_number,
    pool_yn,
    property_key,
    property_sub_type,
    property_type,
    roof,
    stories,
    stories_total,
    structural_style,
    us_property_mui,
    unexempt_taxes,
    window_features,
    year_built,
    year_built_details,
    year_built_effective,
    year_of_purchase,
  } = props.currentHouseDetails;

  return (
    <div className={styles.detailContainer}>
      <Heading>Exterior Details</Heading>
      <div className={styles.detailListContainer}>
        <div>
          <DetailList
            title="Building Information"
            data={{
              roof,
              construction_materials,
              foundation_details,
              architectural_style,
              structural_style,
              year_built,
              year_built_details,
              year_built_effective,
              year_of_purchase,
            }}
          />
          <DetailList
            title="Lot Information"
            data={{
              lot_number,
              lot_size_acres,
              lot_size_area,
              lot_size_square_feet,
              lot_size_units,
              parcel_number,
            }}
          />
        </div>
        <div>
          <DetailList
            title="Property Information"
            data={{
              property_type,
              property_sub_type,
              property_key,
              us_property_mui,
              fencing,
              unexempt_taxes,
              pool_yn,
              patio_and_porch_features,
            }}
          />
          <DetailList
            title="Exterior Features"
            data={{
              attached_garage_yn,
              stories,
              stories_total,
              window_features,
              exterior_features,
            }}
          />
        </div>
      </div>
    </div>
  );
}

export default ExteriorDetail;
