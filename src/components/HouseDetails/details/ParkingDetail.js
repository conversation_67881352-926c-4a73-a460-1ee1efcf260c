import Heading from '../utils/Heading';
import DetailList from '../utils/DetailList';
import styles from '../houseDetails.css';

function ParkingDetail(props) {
  const { garage_spaces, garage_yn, covered_spaces, parking_features } =
    props.currentHouseDetails;

  return (
    <div className={styles.detailContainer}>
      <Heading>Parking Details</Heading>
      <div className={styles.detailListContainer}>
        <div>
          <DetailList
            title="Parking Information"
            data={{ covered_spaces, parking_features }}
          />
        </div>
        <div>
          <DetailList
            title="Parking & Garage"
            data={{ garage_yn, garage_spaces }}
          />
        </div>
      </div>
    </div>
  );
}

export default ParkingDetail;
