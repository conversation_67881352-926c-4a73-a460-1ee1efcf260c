import Heading from '../utils/Heading';
import DetailList from '../utils/DetailList';
import styles from '../houseDetails.css';

function LocationDetail(props) {
  const {
    city,
    community_features,
    country,
    county_or_parish,
    directions,
    primary_school_name,
    elementary_school_name,
    intermediate_school_name,
    middle_school_name,
    junior_high_school_name,
    high_school_name,
    senior_high_school_name,
    latitude,
    longitude,
    postal_code,
    postal_code_plus4,
    school_district,
    state_or_province,
    street_dir_prefix,
    street_dir_suffix,
    street_name,
    street_number,
    street_number_numeric,
    street_suffix,
    subdivision_name,
    tax_block,
    tax_legal_description,
    waterfront_yn,
  } = props.currentHouseDetails;

  return (
    <div className={styles.detailContainer}>
      <Heading>Location Details</Heading>
      <div className={styles.detailListContainer}>
        <div>
          <DetailList
            title="School Information"
            data={{
              primary_school_name,
              elementary_school_name,
              intermediate_school_name,
              middle_school_name,
              junior_high_school_name,
              high_school_name,
              senior_high_school_name,
              school_district,
            }}
          />
          <DetailList
            title="Location Features"
            data={{
              community_features,
              waterfront_yn,
            }}
          />
        </div>
        <div>
          <DetailList
            title="Location Information"
            data={{
              city,
              county_or_parish,
              state_or_province,
              postal_code,
              postal_code_plus4,
              country,
              directions,
              latitude,
              longitude,
              subdivision_name,
              tax_block,
              tax_legal_description,
            }}
          />
        </div>
      </div>
    </div>
  );
}

export default LocationDetail;
