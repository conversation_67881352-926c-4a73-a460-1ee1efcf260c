import Heading from '../utils/Heading';
import DetailList from '../utils/DetailList';
import styles from '../houseDetails.css';

function FinancialDetail(props) {
  const {
    association_fee,
    association_fee_frequency,
    association_type,
    buyer_agency_compensation,
    buyer_agency_compensation_type,
    hoamanagement_company,
    hoamanagement_company_phone,
  } = props.currentHouseDetails;

  return (
    <div className={styles.detailContainer}>
      <Heading>Financial Details</Heading>
      <div className={styles.detailListContainer}>
        <div>
          <DetailList
            title="Financial Information"
            data={{
              association_fee,
              association_fee_frequency,
              association_type,
              buyer_agency_compensation,
              buyer_agency_compensation_type,
            }}
          />
        </div>
        <div>
          <DetailList
            title="HOA Information"
            data={{ hoamanagement_company, hoamanagement_company_phone }}
          />
        </div>
      </div>
    </div>
  );
}

export default FinancialDetail;
