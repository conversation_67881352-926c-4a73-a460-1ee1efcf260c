import Heading from '../utils/Heading';
import DetailList from '../utils/DetailList';
import styles from '../houseDetails.css';

function InteriorDetail(props) {
  const {
    appliances,
    basement_yn,
    bathrooms_full,
    bathrooms_half,
    bathrooms_total_integer,
    bedrooms_total,
    cooling,
    fireplace_features,
    fireplaces_total,
    flooring,
    interior_features,
    laundry_features,
    levels,
    heating,
    living_area,
    number_of_dining_areas,
    number_of_living_areas,
    virtual_toururlunbranded,
  } = props.currentHouseDetails;

  return (
    <div className={styles.detailContainer}>
      <Heading>Interior Details</Heading>
      <div className={styles.detailListContainer}>
        <div>
          <DetailList
            title="Virtual Tour"
            data={{ virtual_toururlunbranded }}
          />
          <DetailList
            title="Bathroom Information"
            data={{
              bathrooms_full,
              bathrooms_half,
              bathrooms_total_integer,
            }}
          />
          <DetailList
            title="Bedroom Information"
            data={{
              bedrooms_total,
            }}
          />
          <DetailList
            title="Fireplace Information"
            data={{
              fireplaces_total,
              fireplace_features,
            }}
          />
          <DetailList
            title="Heating & Cooling"
            data={{
              heating,
              cooling,
            }}
          />
        </div>
        <div>
          <DetailList
            title="Interior Features"
            data={{
              appliances,
              number_of_dining_areas,
              number_of_living_areas,
              living_area,
              basement_yn,
              levels,
              flooring,
              interior_features,
            }}
          />
          <DetailList
            title="Laundry Features"
            data={{
              laundry_features,
            }}
          />
        </div>
      </div>
    </div>
  );
}

export default InteriorDetail;
