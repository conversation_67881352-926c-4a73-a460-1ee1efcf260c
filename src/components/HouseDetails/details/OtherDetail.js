import Heading from '../utils/Heading';
import DetailList from '../utils/DetailList';
import styles from '../houseDetails.css';

function OtherDetail(props) {
  const {
    cumulative_days_on_market,
    days_on_market,
    exclusions,
    list_agent_direct_phone,
    list_agent_email,
    list_agent_full_name,
    list_agent_key_numeric,
    list_agent_mls_provider,
    list_agent_mls_id,
    list_agent_texting_allowed_yn,
    list_mls_provider,
    list_office_key_numeric,
    list_office_manager,
    list_office_manager_key_numeric,
    list_office_manager_license,
    list_office_manager_mls_id,
    list_office_manager_phone,
    list_office_mls_id,
    list_office_name,
    list_office_phone,
    list_price,
    listing_contract_date,
    listing_id,
    listing_key_numeric,
    mls_status,
    modification_timestamp,
    original_list_price,
    owner_name,
    photos_change_timestamp,
    previous_list_price,
    previous_status,
    price_change_timestamp,
    private_remarks,
    showing_contact_phone,
    showing_instructions,
    showing_requirements,
    standard_status,
    status_change_timestamp,
    sub_agency_compensation,
    sub_agency_compensation_type,
  } = props.currentHouseDetails;

  const {
    license_number,
    member_email,
    member_mls_id,
    member_phone,
    office_broker_mls_id,
    office_corporate_license,
    office_key_numeric,
    office_mail_address,
    office_mail_city,
    office_phone,
    office_postal_code,
    office_state_or_province,
  } = props.currentSingleListingBrokerOfficeInfo;

  // console.log(
  //   'props.currentSingleListingBrokerOfficeInfo: ',
  //   props.currentSingleListingBrokerOfficeInfo,
  // );

  return (
    <div className={styles.detailContainer}>
      <Heading>Other Details</Heading>
      <div className={styles.detailListContainer}>
        <div>
          <DetailList
            title="Listing Information"
            data={{
              listing_id,
              listing_key_numeric,
              mls_status,
              previous_status,
              standard_status,
              list_mls_provider,
              owner_name,
              showing_contact_phone,
              showing_instructions,
              showing_requirements,
            }}
          />
          <DetailList
            title="Listing Price Information"
            data={{ original_list_price, previous_list_price, list_price }}
          />
          <DetailList
            title="Listing Date Information"
            data={{
              days_on_market,
              cumulative_days_on_market,
              listing_contract_date,
              modification_timestamp,
              photos_change_timestamp,
              status_change_timestamp,
              price_change_timestamp,
            }}
          />
        </div>
        <div>
          <DetailList
            title="Agent Information"
            data={{
              list_agent_license_number: license_number,
              list_agent_key_numeric,
              list_agent_mls_id: member_mls_id,
              list_agent_full_name,
              list_agent_email: member_email,
              list_agent_direct_phone: member_phone,
              list_agent_texting_allowed_yn,
              list_agent_mls_provider,
              sub_agency_compensation,
              sub_agency_compensation_type,
            }}
          />
          <DetailList
            title="Agent Office Information"
            data={{
              list_office_corporate_license: office_corporate_license,
              list_office_key_numeric: office_key_numeric,
              list_office_mls_id: office_broker_mls_id,
              list_office_name,
              list_office_phone: office_phone,
              list_office_manager,
              list_office_manager_key_numeric,
              list_office_manager_license,
              list_office_manager_mls_id,
              list_office_manager_phone,
              list_office_mail_address:
                office_mail_address +
                ', ' +
                office_mail_city +
                ', ' +
                office_state_or_province +
                ' ' +
                office_postal_code,
            }}
          />
          <DetailList
            title="Other Information"
            data={{
              exclusions,
              private_remarks,
            }}
          />
        </div>
      </div>
    </div>
  );
}

export default OtherDetail;
