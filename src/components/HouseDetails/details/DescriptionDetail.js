import Heading from '../utils/Heading';
import DetailList from '../utils/DetailList';
import styles from '../houseDetails.css';

function DescriptionDetail(props) {
  const { public_remarks } = props.currentHouseDetails;

  if (public_remarks && public_remarks.length > 0) {
    return (
      <div className={styles.detailContainer}>
        <Heading>Description</Heading>
        {/* <div className={styles.detailListContainer}> */}
        <div style={{ padding: '0 10px' }}>
          <p>{public_remarks}</p>
        </div>
      </div>
    );
  }
  return null;
}

export default DescriptionDetail;
