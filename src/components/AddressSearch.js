import { trackCoordinateActivity } from '@spatiallaser/map';
import { AutoComplete, Input, Row, Select, message } from 'antd';
import moment from 'moment';
import { useRef, useState } from 'react';
import { connect, history } from 'umi';
import { getGeofenceParamForGeocodingAPI } from '../utils/geofenceParams';
import styles from './search.css';
const dateFormat = 'YYYY-MM-DD';
const { Option } = AutoComplete;

function AddressSearch(props) {
  // state = {
  //   chooseList: [],
  //   addressOptions: [],
  //   addressInputText: '',
  // };
  const [searchType, setSearchType] = useState('address');
  const [coordinateInputStatus, setCoordinateInputStatus] = useState('');
  const [subdivisionNameInput, setSubdivisionNameInput] = useState('');
  const [subdivisionCityInput, setSubdivisionCityInput] = useState('');
  const [subdivisionZipcodeInput, setSubdivisionZipcodeInput] = useState('');

  const addressInputRef = useRef(null);

  const onChangeSearchText = (searchText) => {
    // if (
    //   (searchText !== '' || searchText.trim() !== '') &&
    //   searchText.length > 4 &&
    //   /[a-z]/i.test(searchText) // contains at least one letter
    // ) {
    // props.dispatch({
    //   type: 'CMA/getMapboxForwardGeocoding',
    //   payload: {
    //     searchText: searchText,
    //     geofence: getGeofenceParamForGeocodingAPI(props.userGroup),
    //   },
    // });
    props.dispatch({
      type: 'CMA/getAddressAutoComplete',
      payload: {
        address: searchText,
        // geofence: getGeofenceParamForGeocodingAPI(props.userGroup),
      },
    });
    // }
  };

  const chooseTheLocationContext = async (value) => {
    props.map.fire('mapDraw.clear');
    // props.dispatch({
    //   type: 'Map/saveState',
    //   payload: {
    //     drawnCustomPolygons: [],
    //   },
    // });
    const selectedOption = props.chooseList.filter(
      (item) => item.fullAddress === value,
    )[0];
    console.log('selectedOption', selectedOption);

    if (selectedOption.searchType === 'city') {
      props.map.flyTo({
        center: [selectedOption.longitude, selectedOption.latitude],
        zoom: 10.5, // Optional, set to your desired zoom level
        speed: 2, // Optional, the speed of the flight
        curve: 1, // Optional, the curvature of the flight path
        easing: (t) => t, // Optional, easing function
      });
      return;
    }
    if (selectedOption.placeType.includes('place')) {
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          eventCoordinates: [selectedOption.longitude, selectedOption.latitude],
        },
      });
      return;
    }

    // if (isWithinSubscribedMetro || !isWithinAnyMetro) {
    console.log('props.chooseList', props.chooseList, 'value', value);
    if (process.env.UMI_APP_SERVER_TYPE == 'prod') {
      const body = {
        lat: selectedOption.latitude,
        lng: selectedOption.longitude,
        type: 'address search',
        app: 'CMA',
      };
      console.debug('trackCoordinateActivity request body', body);
      const result = await trackCoordinateActivity(body);
      console.log('trackCoordinateActivity', result);
    }
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        eventCoordinates: [selectedOption.longitude, selectedOption.latitude],
        currentPropertyAddress: selectedOption,
      },
    });
    // fetch all data
    if (selectedOption.searchType === 'address') {
      props.dispatch({
        type: 'CMA/getAllPropertyData',
        payload: {
          mode: 'search address',
          ...selectedOption,
          lng: selectedOption.longitude,
          lat: selectedOption.latitude,
          // geofence: getGeofenceParamForGeocodingAPI(props.userGroup),
          status: props.currentStatusMLS,
          propertyType:
            props.searchingMode === 'Lease'
              ? 'Residential Lease'
              : 'Residential',
          startDate: moment(props.currentStartMLS).format(dateFormat),
          endDate: moment(props.currentEndMLS).format(dateFormat),
          distance: props.currentRadiusMile * 1609.34,
          exists: props.currentStatusMLS,
          expDateFilterOn: props.expDateFilterOn ? 'yes' : 'no',
        },
      });
    }

    if (selectedOption.searchType === 'postcode' && selectedOption.postalCode) {
      props.dispatch({
        type: 'CMA/getZIPCodeBySearch',
        payload: {
          zipcode: selectedOption.postalCode,
        },
      });
    }
    // in order to be able to tell if we are loading from a url
    history.push({
      search: '',
    });
  };

  const selectHandler = (value) => {
    setSearchType(value);
  };

  const handleCoordinateEnter = (e) => {
    const [latitude, longitude] = e.target.value.replace(/\s/g, '').split(',');

    // validate coordinates
    if (
      isFinite(latitude) &&
      Math.abs(latitude) <= 90 &&
      isFinite(longitude) &&
      Math.abs(longitude) <= 180
    ) {
      // props.dispatch({
      //   type: 'CMA/getMapboxReverseGeocoding',
      //   payload: {
      //     lat: latitude,
      //     lng: longitude,
      //     geofence: getGeofenceParamForGeocodingAPI(props.userGroup),
      //   },
      // });
      props.map.fire('mapDraw.clear');
      // props.dispatch({
      //   type: 'Map/saveState',
      //   payload: {
      //     drawnCustomPolygons: [],
      //   },
      // });
      // fetch all data
      props.dispatch({
        type: 'CMA/getAllPropertyData',
        payload: {
          mode: 'search coordinates',
          lng: longitude,
          lat: latitude,
          geofence: getGeofenceParamForGeocodingAPI(props.userGroup),
          status: props.currentStatusMLS,
          propertyType:
            props.searchingMode === 'Lease'
              ? 'Residential Lease'
              : 'Residential',
          startDate: moment(props.currentStartMLS).format(dateFormat),
          endDate: moment(props.currentEndMLS).format(dateFormat),
          distance: props.currentRadiusMile * 1609.34,
          exists: props.currentStatusMLS,
          expDateFilterOn: props.expDateFilterOn ? 'yes' : 'no',
        },
      });
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          eventCoordinates: [longitude, latitude],
        },
      });
    } else {
      // not coordinates
      setCoordinateInputStatus('error');
    }
  };

  // subdivision search
  const onChangeSearchTextSubdivision = (searchText, source) => {
    switch (source) {
      case 'subdivision':
        setSubdivisionNameInput(searchText);
        break;
      case 'city':
        setSubdivisionCityInput(searchText);
        break;
      case 'zip_code':
        setSubdivisionZipcodeInput(searchText);
        break;
      default:
        break;
    }

    if (
      (searchText !== '' || searchText.trim() !== '') &&
      searchText.length > 3 &&
      // subdivision or city contains at least one letter
      (['subdivision', 'city'].includes(source)
        ? /[a-z]/i.test(searchText)
        : true)
    ) {
      props.dispatch({
        type: 'CMA/getNearestSubdivisions',
        payload: {
          lat:
            props.coordinatesSubdivision.length === 2
              ? props.coordinatesSubdivision[1]
              : 32.89748,
          lng:
            props.coordinatesSubdivision.length === 2
              ? props.coordinatesSubdivision[0]
              : -97.040443,
          // lat: props.eventCoordinates[1],
          // lng: props.eventCoordinates[0],
          subdivision:
            source === 'subdivision' ? searchText : subdivisionNameInput,
          city: source === 'city' ? searchText : subdivisionCityInput,
          zip_code:
            source === 'zip_code' ? searchText : subdivisionZipcodeInput,
        },
      });
    }
  };

  const chooseTheLocationContextSubdivision = (value) => {
    const choosenSubdivision = props.allSubdivisionsFromSearch.filter(
      (item) => item.subdivisio === value,
    )[0];
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        currentSubdivision: choosenSubdivision,
      },
    });
  };

  const getDevicePositionSuccess = (pos) => {
    const crd = pos.coords;
    console.log('crd', crd, 'pos', pos);
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        coordinatesSubdivision: [crd.longitude, crd.latitude],
      },
    });
  };

  const getDevicePositionError = (err) => {
    console.warn(`ERROR(${err.code}): ${err.message}`);
  };

  const getDevicePositionOptions = {
    enableHighAccuracy: true,
    timeout: 5000,
    maximumAge: 0,
  };

  return (
    <>
      <div
        key="search input wrapper"
        id="searchInputWrapper"
        style={{
          display: 'flex',
          flex: '1 1 auto',
          minWidth: '0px',
          // flexDirection: 'row',
          // width: '100%',
          // minWidth: '200px',
          // maxWidth: '800px',
        }}
      >
        <Input.Group
          compact
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'center',
            flex: '1 1 auto',
            minWidth: '0px',
          }}
        >
          <Select
            defaultValue="address"
            style={{ minWidth: '115px' }}
            onChange={selectHandler}
          >
            <Option value="address">Address</Option>
            <Option value="coordinates">Coordinates</Option>
            <Option value="subdivision">Subdivision</Option>
          </Select>

          {searchType === 'address' && (
            <AutoComplete
              ref={addressInputRef}
              // className='SearchInputAutoComplete'
              id="headerAddressSearchInput"
              // className={styles.autoCompleteInputFullLength}
              onSelect={chooseTheLocationContext}
              onSearch={onChangeSearchText}
              placeholder="Enter an address, city or ZIP Code"
              popupMatchSelectWidth={true}
              allowClear={true}
              backfill={true}
              // autoFocus={true}
              // onFocus={showSearchModal}
              style={{
                // width: 600,
                // width: '60%',
                display: 'flex',
                flex: '1 1 auto',
                minWidth: '0px',
              }}
            >
              {props.chooseList.length > 0 &&
                props.chooseList.map((item, index) => {
                  return (
                    <Option key={item.fullAddress} value={item.fullAddress}>
                      <Row
                        type="flex"
                        justify="start"
                        align="middle"
                        style={{
                          marginTop: 8,
                          marginBottom: 8,
                          cursor: 'pointer',
                        }}
                      >
                        <div>
                          <div className={styles.addressContentLine1}>
                            {item.fullAddress}
                          </div>
                        </div>
                      </Row>
                    </Option>
                  );
                })}
            </AutoComplete>
          )}
          {searchType === 'coordinates' && (
            <>
              <Input
                placeholder="latitude, longitude (eg. 32.77729, -96.80709)"
                onPressEnter={handleCoordinateEnter}
                onChange={() => setCoordinateInputStatus('')}
                status={coordinateInputStatus}
                style={{
                  // width: 600,
                  width: '60%',
                }}
              />
            </>
          )}
          {searchType === 'subdivision' && (
            <>
              <AutoComplete
                key="subdivision input"
                className="SearchInputZeroCornerRadius"
                id="AutoCompleteSubdivisionName"
                // className={styles.autoCompleteInputFullLength}
                onSelect={chooseTheLocationContextSubdivision}
                onSearch={(value) =>
                  onChangeSearchTextSubdivision(value, 'subdivision')
                }
                onFocus={() =>
                  navigator.geolocation.getCurrentPosition(
                    getDevicePositionSuccess,
                    getDevicePositionError,
                    getDevicePositionOptions,
                  )
                }
                placeholder="Subdivision"
                allowClear={true}
                backfill={true}
                // autoFocus={true}
                popupMatchSelectWidth={400}
                style={{
                  // width: 600,
                  width: '30%',
                }}
              >
                {props.allSubdivisionsFromSearch.length > 0 &&
                  props.allSubdivisionsFromSearch.map((item, index) => {
                    return (
                      <Option key={item.subdivisio} value={item.subdivisio}>
                        <Row
                          type="flex"
                          justify="start"
                          align="middle"
                          style={{
                            marginTop: 8,
                            marginBottom: 8,
                            cursor: 'pointer',
                          }}
                        >
                          <div>
                            <div className={styles.addressContentLine1}>
                              {item.subdivisio}, {item.city} {item.zip_code}
                            </div>
                          </div>
                        </Row>
                      </Option>
                    );
                  })}
              </AutoComplete>
              <AutoComplete
                key="city input"
                className="SearchInputZeroCornerRadius"
                id="AutoCompleteSubdivisionCity"
                // className={styles.autoCompleteInputFullLength}
                onSelect={chooseTheLocationContextSubdivision}
                onSearch={(value) =>
                  onChangeSearchTextSubdivision(value, 'city')
                }
                onFocus={() =>
                  navigator.geolocation.getCurrentPosition(
                    getDevicePositionSuccess,
                  )
                }
                placeholder="City"
                allowClear={true}
                backfill={true}
                // autoFocus={true}
                popupMatchSelectWidth={400}
                style={{
                  // width: 600,
                  width: '15%',
                }}
              >
                {props.allSubdivisionsFromSearch.length > 0 &&
                  props.allSubdivisionsFromSearch.map((item, index) => {
                    return (
                      <Option key={item.subdivisio} value={item.city}>
                        <Row
                          type="flex"
                          justify="start"
                          align="middle"
                          style={{
                            marginTop: 8,
                            marginBottom: 8,
                            cursor: 'pointer',
                          }}
                        >
                          <div>
                            <div className={styles.addressContentLine1}>
                              {item.subdivisio}, {item.city} {item.zip_code}
                            </div>
                          </div>
                        </Row>
                      </Option>
                    );
                  })}
              </AutoComplete>
              <AutoComplete
                key="zip code input"
                // className='SearchInputAutoComplete'
                id="AutoCompleteSubdivisionZIPCode"
                // className={styles.autoCompleteInputFullLength}
                onSelect={chooseTheLocationContextSubdivision}
                onSearch={(value) =>
                  onChangeSearchTextSubdivision(value, 'zip_code')
                }
                onFocus={() =>
                  navigator.geolocation.getCurrentPosition(
                    getDevicePositionSuccess,
                  )
                }
                placeholder="ZIP Code"
                allowClear={true}
                backfill={true}
                // autoFocus={true}
                // onFocus={showSearchModal}
                popupMatchSelectWidth={400}
                style={{
                  // width: 600,
                  width: '15%',
                }}
              >
                {props.allSubdivisionsFromSearch.length > 0 &&
                  props.allSubdivisionsFromSearch.map((item, index) => {
                    return (
                      <Option key={item.subdivisio} value={item.zip_code}>
                        <Row
                          type="flex"
                          justify="start"
                          align="middle"
                          style={{
                            marginTop: 8,
                            marginBottom: 8,
                            cursor: 'pointer',
                          }}
                        >
                          <div>
                            <div className={styles.addressContentLine1}>
                              {item.subdivisio}, {item.city} {item.zip_code}
                            </div>
                          </div>
                        </Row>
                      </Option>
                    );
                  })}
              </AutoComplete>
            </>
          )}
        </Input.Group>
      </div>
    </>
  );
}

export default connect(({ CMA }) => ({
  map: CMA.map,
  chooseList: CMA.chooseList,
  userGroup: CMA.userGroup,
  currentStatusMLS: CMA.currentStatusMLS,
  searchingMode: CMA.searchingMode,
  currentStartMLS: CMA.currentStartMLS,
  currentEndMLS: CMA.currentEndMLS,
  currentRadiusMile: CMA.currentRadiusMile,
  expDateFilterOn: CMA.expDateFilterOn,
  allSubdivisionsFromSearch: CMA.allSubdivisionsFromSearch,
  currentSubdivision: CMA.currentSubdivision,
  // eventCoordinates: CMA.eventCoordinates, // temp
  coordinatesSubdivision: CMA.coordinatesSubdivision,
}))(AddressSearch);
