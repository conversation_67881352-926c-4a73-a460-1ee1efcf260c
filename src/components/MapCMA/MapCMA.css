.mapContainer {
  position: relative;
  /* left: 56px; */
  height: 100%;
  /* width: calc((9 / 24) * 100%); */
  /* TODO: fix - width 100% messes with tab content widdth too small */
  width: 100%;
  transition: width 250ms ease-in-out;
  border-radius: 8px;
  /* overflow: hidden; */
}

.mapContainerExpanded {
  width: calc((15 / 24) * 100%);
}

.mapLandDevelopmentMode {
  width: 100%;
}

.resultContainerExpanded {
  width: calc((15 / 24) * 100%);
}

.clearMobileDataBtn {
  position: absolute;
  bottom: 75px;
  left: 50%;
  transform: translateX(-50%);
  padding: 10px;
  border: 1px solid lightgrey;
  background-color: white;
  cursor: pointer;
  opacity: 0.75;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  font-weight: bold;
}

.clearMobileDataBtn:hover {
  opacity: 1;
}
