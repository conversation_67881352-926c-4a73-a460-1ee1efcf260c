import { useEffect, useRef } from 'react';
import { useSelector, useDispatch, connect } from 'umi';

const zoomLevelToShowPriceMarkers = 12;

function MapZoomEnd() {
  const map = useSelector((state) => state.CMA.map);
  const showPriceMarkers = useSelector((state) => state.CMA.showPriceMarkers);
  const scorecardModalOpen = useSelector(
    (state) => state.CMA.scorecardModalOpen,
  );
  const dispatch = useDispatch();

  const showPriceMarkersRef = useRef(showPriceMarkers);
  showPriceMarkersRef.current = showPriceMarkers;
  const scorecardModalOpenRef = useRef(scorecardModalOpen);
  scorecardModalOpenRef.current = scorecardModalOpen;

  useEffect(() => {
    if (!map) return;

    // show/hide price markers when zoom ends at certain zoom level
    const zoomEnd = () => {
      const currentZoomLevel = map.getZoom();

      if (
        currentZoomLevel >= zoomLevelToShowPriceMarkers &&
        !showPriceMarkersRef.current &&
        !scorecardModalOpenRef.current
      ) {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            showPriceMarkers: true,
          },
        });
      } else if (
        zoomLevelToShowPriceMarkers > currentZoomLevel &&
        showPriceMarkersRef.current
      ) {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            showPriceMarkers: false,
          },
        });
      }
    };

    map.on('zoomend', zoomEnd);
    return () => {
      map.off('zoomend', zoomEnd);
    };
  }, [map]);

  return null;
}

export default MapZoomEnd;
