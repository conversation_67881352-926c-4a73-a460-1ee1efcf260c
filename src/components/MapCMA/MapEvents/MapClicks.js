import { usePrevious } from '@/hooks';
import { trackCoordinateActivity } from '@spatiallaser/map';
import { isEqual } from 'lodash';
import isEmpty from 'lodash.isempty';
import mapboxgl from 'mapbox-gl';
import moment from 'moment';
import { useEffect, useRef } from 'react';
import { connect, history, useDispatch, useSelector } from 'umi';
import { dateFormat } from '../../../constants';
import { getGeofenceParamForGeocodingAPI } from '../../../utils/geofenceParams';
import { getMetroNameForParam } from '../../../utils/geography';

let tooltipPopup = null;

function MapClicks(props) {
  const map = useSelector((state) => state.CMA.map);
  const drawingMode = useSelector((state) => state.CMA.drawingMode);
  const drawnCustomPolygons = useSelector(
    (state) => state.CMA.drawnCustomPolygons,
  );
  const measureMode = useSelector((state) => state.CMA.measureMode);
  const userGroup = useSelector((state) => state.CMA.userGroup);
  const parcelMode = useSelector((state) => state.CMA.parcelMode);

  const appViewMode = useSelector((state) => state.CMA.appViewMode);

  const currentMapLayerOptions = useSelector(
    (state) => state.CMA.currentMapLayerOptions,
  );

  const eventCoordinates = useSelector((state) => state.CMA.eventCoordinates);
  const prevEventCoordinates = usePrevious(eventCoordinates);

  const dispatch = useDispatch();

  const propsRef = useRef(props);
  propsRef.current = props;

  const drawingModeRef = useRef(drawingMode);
  drawingModeRef.current = drawingMode;

  const drawnCustomPolygonsRef = useRef(drawnCustomPolygons);
  drawnCustomPolygonsRef.current = drawnCustomPolygons;

  const measureModeRef = useRef(measureMode);
  measureModeRef.current = measureMode;

  // useEffect(() => {
  //   if (!map) return;
  //   if (landDevelopmentMethod != 'poi') return;

  //   const clickHandler = (e) => {
  //     console.log('HERE');
  //     console.log(e.lngLat);
  //   };

  //   map.on('click', clickHandler);
  //   return () => {
  //     map.off('click', clickHandler);
  //   };
  // }, [map, landDevelopmentMethod]);

  useEffect(() => {
    // Fetch CMA comps when drive time is turned off while there is still coordinate state
    if (currentMapLayerOptions.includes('drive time')) return;
    if (eventCoordinates.length === 0) return;
    if (!isEqual(eventCoordinates, prevEventCoordinates)) return;

    dispatch({
      type: 'CMA/getAllPropertyData',
      payload: {
        mode: 'click on map',
        lng: eventCoordinates[0],
        lat: eventCoordinates[1],
        geofence: getGeofenceParamForGeocodingAPI(propsRef.current.userGroup),
        status: propsRef.current.currentStatusMLS,
        // propertyType: propsRef.current.isLeaseMode
        propertyType:
          propsRef.current.searchingMode === 'Lease'
            ? 'Residential Lease'
            : 'Residential',
        startDate: moment(propsRef.current.currentStartMLS).format(dateFormat),
        endDate: moment(propsRef.current.currentEndMLS).format(dateFormat),
        distance: propsRef.current.currentRadiusMile * 1609.34,
        exists: propsRef.current.currentStatusMLS,
        expDateFilterOn: propsRef.current.expDateFilterOn ? 'yes' : 'no',
      },
    });
  }, [currentMapLayerOptions, eventCoordinates]);

  useEffect(() => {
    if (!map) return;
    if (appViewMode.mode != 'CMA') return;

    tooltipPopup = new mapboxgl.Popup({
      offset: 15,
    });

    const clickHandler = (e) => {
      if (parcelMode) return;
      if (drawingModeRef.current || measureModeRef.current) return;
      const features = map.queryRenderedFeatures(e.point);
      if (
        features.some(
          (f) =>
            f.source.includes('fundrise') ||
            f.source.includes('water-district-source-geojson'),
        )
      )
        return;

      const getAllPropertyData = (lngLatObject) => {
        if (!currentMapLayerOptions.includes('drive time')) {
          dispatch({
            type: 'CMA/getAllPropertyData',
            payload: {
              mode: 'click on map',
              lng: lngLatObject.lng,
              lat: lngLatObject.lat,
              geofence: getGeofenceParamForGeocodingAPI(
                propsRef.current.userGroup,
              ),
              status: propsRef.current.currentStatusMLS,
              propertyType:
                propsRef.current.searchingMode === 'Lease'
                  ? 'Residential Lease'
                  : 'Residential',
              startDate: moment(propsRef.current.currentStartMLS).format(
                dateFormat,
              ),
              endDate: moment(propsRef.current.currentEndMLS).format(
                dateFormat,
              ),
              distance: propsRef.current.currentRadiusMile * 1609.34,
              exists: propsRef.current.currentStatusMLS,
              expDateFilterOn: propsRef.current.expDateFilterOn ? 'yes' : 'no',
            },
          });
        }

        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            eventCoordinates: [lngLatObject.lng, lngLatObject.lat],
          },
        });
        console.log('test map click');
        const track = async (body) => {
          const result = await trackCoordinateActivity(body);
          console.log('trackCoordinateActivity', result);
        };
        if (process.env.UMI_APP_SERVER_TYPE == 'prod') {
          const body = {
            lng: lngLatObject.lng,
            lat: lngLatObject.lat,
            type: 'click on map',
            app: 'CMA',
          };
          track(body);
        }
        map.fire('map.click', { ...e });
      };

      const getSubjectForCustomPolygon = (lngLatObject) => {
        dispatch({
          type: 'CMA/getSubjectForCustomPolygon',
          // type: 'CMA/getSubjectProperty',
          payload: {
            lng: lngLatObject.lng,
            lat: lngLatObject.lat,
            geofence: getGeofenceParamForGeocodingAPI(
              propsRef.current.userGroup,
            ),
          },
        });

        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            eventCoordinates: [lngLatObject.lng, lngLatObject.lat],
          },
        });
        map.fire('map.click', { ...e });
      };

      const clickableLayers = [
        'MLSLayerSymbol',
        'nationalOperatorsLayer',
        'HotPadsLayer',
        'PadSplitLayer',
        'BTOwnedLayer',
      ];

      if (
        userGroup.includes('BridgeTower') ||
        userGroup.includes('Avanta') ||
        userGroup.includes('demo-users') ||
        userGroup.includes('dev')
      ) {
        clickableLayers.push('multiFamilyLayer');
      }

      const clickedFeatures = map.queryRenderedFeatures(e.point, {
        layers: clickableLayers,
      });

      // extract mls property data from map click (pricetag)
      // Other sources: BTOwnedData, nationalOperatorsData, multiFamilyData
      if (clickedFeatures.length > 0 && clickedFeatures[0]) {
        if (clickedFeatures[0].source === 'MLS') {
          const mlsProperty = clickedFeatures[0].properties;

          let key = mlsProperty.listingkey;
          dispatch({
            type: 'CMA/getBatchMLSPropertyImages',
            payload: {
              key: key,
              city: getMetroNameForParam(mlsProperty, true), // true for using realtrac instead of nashville
            },
          });

          dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              selectedMLProperty: mlsProperty,
              openMLSImageModal: true,
            },
          });
        } else if (
          clickedFeatures[0].source === 'BTOwned' ||
          clickedFeatures[0].source === 'nationalOperators' ||
          clickedFeatures[0].source === 'multiFamily' ||
          clickedFeatures[0].source === 'HotPads'
        ) {
          const coordinates = [e.lngLat.lng, e.lngLat.lat];
          const description =
            '<div style="padding: 20px 10px"><strong>Images are for MLS only</strong></div>';

          while (Math.abs(e.lngLat.lng - coordinates[0]) > 180) {
            coordinates[0] += e.lngLat.lng > coordinates[0] ? 360 : -360;
          }
          tooltipPopup.setLngLat(coordinates).setHTML(description).addTo(map);
        } else {
          if (drawnCustomPolygonsRef.current.length > 0) {
            getSubjectForCustomPolygon(e.lngLat);
          } else {
            getAllPropertyData(e.lngLat);
          }
        }
      } else {
        if (drawnCustomPolygonsRef.current.length > 0) {
          getSubjectForCustomPolygon(e.lngLat);
        } else {
          getAllPropertyData(e.lngLat);
        }
      }

      // remove all existing queries in url
      // in order to be able to tell if we are loading from a url
      history.push({
        search: '',
      });
    };

    map.on('click', clickHandler);
    return () => {
      map.off('click', clickHandler);
    };
  }, [map, parcelMode, appViewMode, currentMapLayerOptions]);

  return null;
}

export default connect(({ CMA }) => ({
  userGroup: CMA.userGroup,
  currentStatusMLS: CMA.currentStatusMLS,
  // isLeaseMode: CMA.isLeaseMode,
  currentStartMLS: CMA.currentStartMLS,
  currentEndMLS: CMA.currentEndMLS,
  currentRadiusMile: CMA.currentRadiusMile,
  expDateFilterOn: CMA.expDateFilterOn,
  searchingMode: CMA.searchingMode,
}))(MapClicks);
