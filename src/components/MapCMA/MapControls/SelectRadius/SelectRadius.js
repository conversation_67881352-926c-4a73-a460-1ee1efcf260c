import { default as turf_bbox } from '@turf/bbox';
import { default as turf_circle } from '@turf/circle';
import moment from 'moment';
import { useEffect, useRef } from 'react';
import { connect, useDispatch, useSelector } from 'umi';
import { dateFormat, geojsonTemplate } from '../../../../constants';

function SelectRadius(props) {
  const map = useSelector((state) => state.CMA.map);
  const dispatch = useDispatch();

  const propsRef = useRef(props);
  propsRef.current = props;

  useEffect(() => {
    if (!map) return;

    map.on('selectRadius.radius', (e) => {
      const radius = e.payload.currentRadiusMile;
      console.log('radius', radius);
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          currentRadiusMile: radius,
        },
      });
      if (
        propsRef.current.eventCoordinates &&
        propsRef.current.eventCoordinates.length === 2
      ) {
        dispatch({
          type: 'CMA/getAllPropertyData',
          payload: {
            mode: 'change radius',
            lng: propsRef.current.eventCoordinates[0],
            lat: propsRef.current.eventCoordinates[1],
            status: propsRef.current.currentStatusMLS,
            propertyType:
              propsRef.current.searchingMode === 'Lease'
                ? 'Residential Lease'
                : 'Residential',
            startDate: moment(propsRef.current.currentStartMLS).format(
              dateFormat,
            ),
            endDate: moment(propsRef.current.currentEndMLS).format(dateFormat),
            distance: radius * 1609.34,
            exists: propsRef.current.currentStatusMLS,
            expDateFilterOn: propsRef.current.expDateFilterOn ? 'yes' : 'no',
          },
        });
      }
    });

    map.on('selectRadius.clear', (e) => {
      console.log('clear');
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          circleBbox: [],
          eventCoordinates: [],
          currentPropertyAddress: {},
          subjectPropertyParcelData: {},
          currentParcelOwnerSummary: {},
          currentMLSListingSummary: {},
          compMLSListingSummary: {},
          currentBTOwnedProperties: [],
          currentBTOwnedGeoJSON: geojsonTemplate,
          currentNationalOperatorsProperties: [],
          currentNationalOperatorsPropertiesFiltered: [],
          currentNationalOperatorsGeoJSON: geojsonTemplate,
          selectedRowKeysNationalOperators: [],
          currentHotPadsProperties: [],
          currentHotPadsPropertiesFiltered: [],
          currentHotPadsGeoJSON: geojsonTemplate,
          selectedRowKeysHotPads: [],
          currentMLSProperties: [],
          currentMLSPropertiesFiltered: [],
          currentMLSGeoJSON: geojsonTemplate,
          selectedRowKeysMLSLease: [],
          currentMultiFamilyProperties: [],
          currentMultiFamilyGeoJSON: geojsonTemplate,
          selectedRowKeysNewBuilds: [],
          currentNewBuildsProperties: [],
          realtorSingleFamilyDataForRender: [],
          currentRealtorDotComGeoJSON: geojsonTemplate,
          selectedRowKeysRealtorDotCom: [],
        },
      });
    });

    map.on('selectRadius.parcelMode', (e) => {
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          parcelMode: e.payload.parcelMode,
        },
      });
    });
  }, [map]);

  useEffect(() => {
    if (props.eventCoordinates.length > 0) {
      if (map.style && map.style._loaded && map.isStyleLoaded()) {
        if (map.isMoving()) {
          map.once('moveend', () => {
            map.fire('selectRadius.setEventCoordinates', {
              payload: {
                eventCoordinates: props.eventCoordinates,
              },
            });
          });
        } else {
          map.fire('selectRadius.setEventCoordinates', {
            payload: {
              eventCoordinates: props.eventCoordinates,
            },
          });
        }
      } else {
        map.once('data', () => {
          map.fire('selectRadius.setEventCoordinates', {
            payload: {
              eventCoordinates: props.eventCoordinates,
            },
          });
        });
      }

      const circleBbox = turf_bbox(
        turf_circle(props.eventCoordinates, props.currentRadiusMile, {
          units: 'miles',
        }),
      );

      dispatch({
        type: 'CMA/saveCMAStates',
        payload: { circleBbox: circleBbox },
      });
    }
  }, [props.eventCoordinates, props.currentRadiusMile]);

  return null;
}

export default connect(({ CMA }) => ({
  eventCoordinates: CMA.eventCoordinates,
  currentRadiusMile: CMA.currentRadiusMile,
  currentStatusMLS: CMA.currentStatusMLS,
  isLeaseMode: CMA.isLeaseMode,
  searchingMode: CMA.searchingMode,
  currentStartMLS: CMA.currentStartMLS,
  currentEndMLS: CMA.currentEndMLS,
  expDateFilterOn: CMA.expDateFilterOn,
}))(SelectRadius);
