import { useEffect } from 'react';
import { useSelector, useDispatch } from 'umi';

function MapRuler() {
  const map = useSelector((state) => state.CMA.map);
  const dispatch = useDispatch();

  useEffect(() => {
    if (!map) return;

    map.on('mapRuler.enter', (e) => {
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: { measureMode: true },
      });
    });

    map.on('mapRuler.exit', (e) => {
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: { measureMode: false },
      });
    });
  }, [map]);

  return null;
}

export default MapRuler;
