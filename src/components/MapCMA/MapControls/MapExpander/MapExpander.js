import { useEffect } from 'react';
import { useSelector, useDispatch } from 'umi';

function MapExpander() {
  const map = useSelector((state) => state.CMA.map);
  const dispatch = useDispatch();

  useEffect(() => {
    if (!map) return;

    map.on('mapExpandedView', (e) => {
      const expanded = e.payload.mapExpandedView;

      dispatch({
        type: 'CMA/saveCMAStates',
        payload: { mapExpandedView: expanded },
      });
    });
  }, [map]);

  return null;
}

export default MapExpander;
