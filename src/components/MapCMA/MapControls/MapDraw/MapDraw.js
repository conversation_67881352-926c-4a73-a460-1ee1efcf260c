import { useEffect } from 'react';
import { useDispatch, useSelector } from 'umi';
import { geojsonTemplate } from '../../../../constants';
import { polygonWithinMetro } from '../../../../utils/geography';

function MapDraw() {
  const map = useSelector((state) => state.CMA.map);
  const userGroup = useSelector((state) => state.CMA.userGroup);
  const selectedUserGroup = useSelector((state) => state.CMA.selectedUserGroup);
  const currentStatusMLS = useSelector((state) => state.CMA.currentStatusMLS);
  const metrosAllowedOnIndividualAccountLevel = useSelector(
    (state) => state.CMA.metrosAllowedOnIndividualAccountLevel,
  );

  const dispatch = useDispatch();

  useEffect(() => {
    if (!map) return;

    map.on('mapDraw.enter', (e) => {
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: { drawingMode: true },
      });
    });

    map.on('mapDraw.exit', (e) => {
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: { drawingMode: false },
      });
    });

    map.on('mapDraw.clear', (e) => {
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: { drawnCustomPolygons: [], validDrawnCustomPolygons: [] },
      });

      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          circleBbox: [],
          eventCoordinates: [],
          currentPropertyAddress: {},
          subjectPropertyParcelData: {},
          currentParcelOwnerSummary: {},
          currentMLSListingSummary: {},
          compMLSListingSummary: {},
          currentBTOwnedProperties: [],
          currentBTOwnedGeoJSON: geojsonTemplate,
          currentNationalOperatorsProperties: [],
          currentNationalOperatorsPropertiesFiltered: [],
          currentNationalOperatorsGeoJSON: geojsonTemplate,
          selectedRowKeysNationalOperators: [],
          currentHotPadsProperties: [],
          currentHotPadsPropertiesFiltered: [],
          currentHotPadsGeoJSON: geojsonTemplate,
          selectedRowKeysNationalOperators: [],
          currentMLSProperties: [],
          currentMLSPropertiesFiltered: [],
          currentMLSGeoJSON: geojsonTemplate,
          selectedRowKeysMLSLease: [],
          currentMultiFamilyProperties: [],
          currentMultiFamilyGeoJSON: geojsonTemplate,
          selectedRowKeysNewBuilds: [],
          currentNewBuildsProperties: [],
          realtorSingleFamilyDataForRender: [],
          currentRealtorDotComGeoJSON: geojsonTemplate,
          selectedRowKeysRealtorDotCom: [],
        },
      });
    });

    map.on('mapDraw.apply', async (e) => {
      const mapDrawData = e.payload.drawnCustomPolygons;
      let drawnCustomPolygons = [];
      for (let i = 0; i < mapDrawData.length; i++) {
        const { isWithinSubscribedMetro, isWithinAnyMetro } =
          await polygonWithinMetro(
            mapDrawData[i],
            selectedUserGroup,
            metrosAllowedOnIndividualAccountLevel,
          );
        if (!(!isWithinSubscribedMetro && isWithinAnyMetro)) {
          drawnCustomPolygons.push(mapDrawData[i]);
        }
      }

      console.log(drawnCustomPolygons);

      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          drawnCustomPolygons: drawnCustomPolygons,
        },
      });
      if (drawnCustomPolygons.length > 0) {
        dispatch({
          type: 'CMA/getParcelOwnerSummaryWithinPolygons',
        });

        dispatch({
          type: 'CMA/getAllPropertyData',
          payload: {
            mode: 'from polygon',
            status: currentStatusMLS,
          },
        });
      }
    });

    // map.on('mapDraw.cancel', (e) => {});
  }, [map]);

  return null;
}

export default MapDraw;
