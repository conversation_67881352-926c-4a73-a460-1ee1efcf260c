import { Layer, Source } from '@spatiallaser/map';
import { default as turf_centerOfMass } from '@turf/center-of-mass';
import isEmpty from 'lodash.isempty';
import moment from 'moment';
import { useCallback, useEffect, useRef } from 'react';
import { renderToString } from 'react-dom/server';
import { connect, useDispatch, useSelector } from 'umi';
import {
  MAP_LAYER_NAME_BASE,
  dateFormat,
  geojsonTemplate,
} from '../../../constants';
import { getGeofenceParamForGeocodingAPI } from '../../../utils/geofenceParams';

const sourceId = MAP_LAYER_NAME_BASE.kml;

const fillStyle = {
  id: `${sourceId}Fill`,
  type: 'line',
  paint: {
    // 'line-color': '#32a852',
    'line-color': '#e200ff',
  },
};

function KMLLayer(props) {
  const map = useSelector((state) => state.CMA.map);
  const eventCoordinates = useSelector((state) => state.CMA.eventCoordinates);
  const GeoJSONFromKML = useSelector((state) => state.CMA.GeoJSONFromKML);
  const shouldPanToKML = useSelector((state) => state.CMA.shouldPanToKML);
  const dispatch = useDispatch();

  const GeoJSONFromKMLRef = useRef(GeoJSONFromKML);
  GeoJSONFromKMLRef.current = GeoJSONFromKML;

  useEffect(() => {
    if (!map) return;

    const click = (e) => {
      if (
        GeoJSONFromKMLRef.current &&
        GeoJSONFromKMLRef.current.features.length > 0
      ) {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            GeoJSONFromKML: geojsonTemplate,
            shouldPanToKML: false,
          },
        });
      }
    };

    map.on('click', click);
    return () => {
      map.off('click', click);
    };
  }, [map]);

  useEffect(() => {
    if (!map) return;

    if (
      !isEmpty(GeoJSONFromKML) &&
      GeoJSONFromKML.features &&
      GeoJSONFromKML.features.length > 0
    ) {
      // get comp with the polygon's center of mass
      // return a geojson object
      const KMLCenter = turf_centerOfMass(GeoJSONFromKML.features[0].geometry);
      console.log('KMLCenter', KMLCenter);
      // load subject property using lngLat
      // same as load from url lnglat
      dispatch({
        type: 'CMA/getAllPropertyData',
        payload: {
          mode: 'from url with lngLat',
          lng: KMLCenter.geometry.coordinates[0],
          lat: KMLCenter.geometry.coordinates[1],
          geofence: getGeofenceParamForGeocodingAPI(props.userGroup),
          status: props.currentStatusMLS,
          propertyType:
            props.searchingMode === 'Lease'
              ? 'Residential Lease'
              : 'Residential',
          startDate: moment(props.currentStartMLS).format(dateFormat),
          endDate: moment(props.currentEndMLS).format(dateFormat),
          distance: props.currentRadiusMile * 1609.34,
          exists: props.currentStatusMLS,
          expDateFilterOn: props.expDateFilterOn ? 'yes' : 'no',
        },
      });
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          // KMLCenter sometimes have elevation as the third element in coordinates
          eventCoordinates: [
            KMLCenter.geometry.coordinates[0],
            KMLCenter.geometry.coordinates[1],
          ],
        },
      });
    }
  }, [GeoJSONFromKML]);

  useEffect(() => {
    if (!map) return;

    if (shouldPanToKML) {
      map.flyTo({ center: eventCoordinates });
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          shouldPanToKML: false,
        },
      });
    }
  }, [shouldPanToKML]);

  return (
    <Source id={sourceId} type="geojson" data={GeoJSONFromKML}>
      <Layer {...fillStyle} />
    </Source>
  );
}

export default connect(({ CMA }) => ({
  userGroup: CMA.userGroup,
  currentStatusMLS: CMA.currentStatusMLS,
  searchingMode: CMA.searchingMode,
  currentStartMLS: CMA.currentStartMLS,
  currentEndMLS: CMA.currentEndMLS,
  currentRadiusMile: CMA.currentRadiusMile,
  expDateFilterOn: CMA.expDateFilterOn,
}))(KMLLayer);
