import { Layer, Source } from '@spatiallaser/map';
import { useEffect, useMemo, useState } from 'react';
import { renderToString } from 'react-dom/server';
import { useDispatch, useSelector } from 'umi';
import { geojsonTemplate } from '../../../constants';
import { getBuiltForRentData } from '../../../services/data';
import { convertToGeoJSON } from '../../../utils/generateGeoJSON';
import { capitalize } from '../../../utils/strings';
import { initPopup } from '../MapUtility/general';

const sourceId = 'builtForRent';
const circleStyleInit = {
  id: `${sourceId}LayerCircle`,
  type: 'circle',
  paint: {
    'circle-radius': 8,
    'circle-color': '#808080',
    'circle-stroke-color': '#fff',
    'circle-stroke-width': 2,
  },
};
const popup = initPopup();

const formatKey = (key) => (key === 'zip_code' ? 'ZIP Code' : capitalize(key));
const formatValue = (value) =>
  typeof value === 'string'
    ? capitalize(value)
    : typeof value === 'number' && value % 1 !== 0
    ? value.toFixed(2)
    : value || '-';

const renderPopup = ({ owner_name, ...rest }) =>
  renderToString(
    <div style={{ padding: '10px 20px' }}>
      <div style={{ textAlign: 'center' }}>
        <strong>Built For Rent</strong>
      </div>
      <div style={{ display: 'flex', flexDirection: 'column' }}>
        <div>
          <span>Owner: </span>
          <span>{capitalize(owner_name)}</span>
        </div>
        {Object.keys(rest)
          .filter((key) => rest[key] != null)
          .map((key) => (
            <div key={key}>
              <span>{formatKey(key)}: </span>
              <span>{formatValue(rest[key])}</span>
            </div>
          ))}
      </div>
    </div>,
  );

const BuiltForRentLayer = () => {
  const map = useSelector((state) => state.CMA.map);
  const searchingMode = useSelector((state) => state.CMA.searchingMode);
  const eventCoordinates = useSelector((state) => state.CMA.eventCoordinates);
  const currentRadiusMile = useSelector((state) => state.CMA.currentRadiusMile);
  const currentStartMLS = useSelector((state) => state.CMA.currentStartMLS);
  const currentEndMLS = useSelector((state) => state.CMA.currentEndMLS);
  const compingMode = useSelector((state) => state.CMA.compingMode);
  const minBeds = useSelector((state) => state.CMA.minBeds);
  const maxBeds = useSelector((state) => state.CMA.maxBeds);
  const minBaths = useSelector((state) => state.CMA.minBaths);
  const maxBaths = useSelector((state) => state.CMA.maxBaths);
  const minSqft = useSelector((state) => state.CMA.minSqft);
  const maxSqft = useSelector((state) => state.CMA.maxSqft);

  const builtForRentGeoJSON = useSelector(
    (state) => state.CMA.builtForRentGeoJSON,
  );
  const builtForRentSelectedRowKeys = useSelector(
    (state) => state.CMA.builtForRentSelectedRowKeys,
  );
  const builtForRentTableRowHover = useSelector(
    (state) => state.CMA.builtForRentTableRowHover,
  );
  const dispatch = useDispatch();
  const [circleStyle] = useState(circleStyleInit);

  const filteredGeoJSON = useMemo(() => {
    if (!builtForRentGeoJSON) return geojsonTemplate;
    return {
      ...builtForRentGeoJSON,
      features: builtForRentGeoJSON.features.filter((feature) =>
        builtForRentSelectedRowKeys.includes(feature.properties.apn),
      ),
    };
  }, [builtForRentGeoJSON, builtForRentSelectedRowKeys]);

  const handleMouseEnter = (e) => {
    const {
      properties,
      geometry: { coordinates },
    } = e.features[0];
    popup.setLngLat(coordinates).setHTML(renderPopup(properties)).addTo(map);
  };

  const handleMouseLeave = () => popup.remove();
  const handleClear = () =>
    dispatch({ type: 'CMA/saveCMAStates', payload: { builtForRentData: [] } });

  useEffect(() => {
    if (!map) return;

    map.on('selectRadius.clear', handleClear);
    map.on('mouseenter', `${sourceId}LayerCircle`, handleMouseEnter);
    map.on('mouseleave', `${sourceId}LayerCircle`, handleMouseLeave);

    return () => {
      map.off('selectRadius.clear', handleClear);
      map.off('mouseenter', `${sourceId}LayerCircle`, handleMouseEnter);
      map.off('mouseleave', `${sourceId}LayerCircle`, handleMouseLeave);
    };
  }, [map]);

  useEffect(() => {
    if (!builtForRentTableRowHover && popup.isOpen()) popup.remove();
    else if (builtForRentTableRowHover) {
      const [fips, apn] = builtForRentTableRowHover.split(' ');
      const feature = builtForRentGeoJSON.features.find(
        (feature) =>
          feature.properties.fips === fips && feature.properties.apn === apn,
      );
      if (feature) {
        const {
          geometry: { coordinates },
          properties,
        } = feature;
        popup
          .setLngLat(coordinates)
          .setHTML(renderPopup(properties))
          .addTo(map);
      }
    }
  }, [builtForRentTableRowHover, builtForRentGeoJSON, map]);

  useEffect(() => {
    if (eventCoordinates.length === 0 || searchingMode === 'Sale') return;

    const fetchBTRData = async () => {
      const data = await getBuiltForRentData({
        lat: eventCoordinates[1],
        lng: eventCoordinates[0],
        distance: currentRadiusMile * 1609.34,
      });

      if (!data) {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            builtForRentData: [],
            builtForRentGeoJSON: geojsonTemplate,
          },
        });
        return;
      }

      const isWithinDateRange = (date, startDate, endDate) => {
        const targetDate = new Date(date);
        return (
          targetDate >= new Date(startDate) && targetDate <= new Date(endDate)
        );
      };

      let filteredData;
      if (compingMode === 'noFilter') {
        filteredData = data.filter((item) => {
          return item.currentprice;
        });
      } else {
        filteredData = data.filter((item) => {
          // Base date range filter
          // const dateInRange = item.closedate &&
          //   isWithinDateRange(item.closedate, currentStartMLS, currentEndMLS);

          // Additional property filters
          const bedsInRange =
            (!minBeds || (item.bed && item.bed >= minBeds)) &&
            (!maxBeds || (item.bed && item.bed <= maxBeds));

          const bathsInRange =
            (!minBaths || (item.bath && item.bath >= minBaths)) &&
            (!maxBaths || (item.bath && item.bath <= maxBaths));

          const sqftInRange =
            (!minSqft || (item.size && item.size >= minSqft)) &&
            (!maxSqft || (item.size && item.size <= maxSqft));

          return (
            bedsInRange && bathsInRange && sqftInRange && item.currentprice
          );
        });
      }
      const dataWithDistanceFixed = filteredData.map((item) => {
        console.log('item', item);
        return {
          ...item,
          distance: item.distance / 100,
        };
      });

      const geoJSONData = convertToGeoJSON(
        dataWithDistanceFixed,
        (item) => ({
          type: 'Point',
          coordinates: [Number(item.longitude), Number(item.latitude)],
        }),
        (item) => {
          const { longitude, latitude, ...properties } = item;
          return properties;
        },
      );

      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          builtForRentData: dataWithDistanceFixed,
          builtForRentGeoJSON: geoJSONData,
        },
      });
    };

    fetchBTRData();

    return () => {
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          builtForRentData: [],
          builtForRentGeoJSON: geojsonTemplate,
        },
      });
    };
  }, [
    eventCoordinates,
    currentRadiusMile,
    currentStartMLS,
    currentEndMLS,
    searchingMode,
    compingMode,
    minBeds,
    maxBeds,
    minBaths,
    maxBaths,
    minSqft,
    maxSqft,
    dispatch,
  ]);

  return (
    <Source id={sourceId} type="geojson" data={filteredGeoJSON}>
      <Layer {...circleStyle} />
    </Source>
  );
};

export default BuiltForRentLayer;
