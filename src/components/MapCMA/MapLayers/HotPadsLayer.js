import { Layer, Source } from '@spatiallaser/map';
import isEmpty from 'lodash.isempty';
import { useCallback, useEffect, useRef, useState } from 'react';
import { renderToString } from 'react-dom/server';
import { useDispatch, useSelector } from 'umi';
import { geojsonTemplate, MAP_LAYER_NAME_BASE } from '../../../constants';
import PropertyDetailPopup from '../../PropertyDetailPopup';
import { locatePropertyHandler, propertyDetailPopup } from '../MapCMA';
import {
  generateGeoJSONData,
  removePriceMarkers,
  setPriceMarkers,
} from '../MapUtility/general';
import { sourceId as sourceIdNationalOperatorLayer } from './NationalOperatorLayer';

export const sourceId = MAP_LAYER_NAME_BASE.hotPads;

// Add zoom level constant for showing markers
const zoomLevelToShowPriceMarkers = 12;

const circleStyle = {
  id: `${sourceId}Layer`,
  type: 'circle',
  paint: {
    'circle-radius': 8,
    'circle-color': '#ED6041',
    'circle-stroke-color': '#fff',
    'circle-stroke-width': 2,
  },
};

let priceMarkersHotPads = {};

export const removeHotPadsPriceMarkers = () => {
  if (!isEmpty(priceMarkersHotPads)) {
    priceMarkersHotPads = removePriceMarkers(priceMarkersHotPads);
  }
};

export const generateHotPadsPriceMarkers = (map, currentHotPadsGeoJSON) => {
  if (currentHotPadsGeoJSON && currentHotPadsGeoJSON.features.length > 0) {
    if (!isEmpty(priceMarkersHotPads)) {
      priceMarkersHotPads = removePriceMarkers(priceMarkersHotPads);
    }
    priceMarkersHotPads = setPriceMarkers(
      map,
      currentHotPadsGeoJSON.features,
      'property_id',
      'rent',
      sourceId,
    );
  }
};

function HotPadsLayer() {
  const map = useSelector((state) => state.CMA.map);
  const cmaTabKey = useSelector((state) => state.CMA.cmaTabKey);
  const searchingMode = useSelector((state) => state.CMA.searchingMode);
  const selectedRowKeysHotPads = useSelector(
    (state) => state.CMA.selectedRowKeysHotPads,
  );
  const currentHotPadsPropertiesFiltered = useSelector(
    (state) => state.CMA.currentHotPadsPropertiesFiltered,
  );
  const currentHotPadsGeoJSON = useSelector(
    (state) => state.CMA.currentHotPadsGeoJSON,
  );
  const showPriceMarkers = useSelector((state) => state.CMA.showPriceMarkers);
  const currentMapThemeOption = useSelector(
    (state) => state.CMA.currentMapThemeOption,
  );

  const dispatch = useDispatch();

  const [geojonData, setGeojsonData] = useState(geojsonTemplate);

  // Create refs for values that need to be accessed in callbacks
  const currentMapThemeOptionRef = useRef(currentMapThemeOption);
  const selectedRowKeysHotPadsRef = useRef(selectedRowKeysHotPads);
  const mouseOnPopup = useRef(false);
  const cmaTabKeyRef = useRef(cmaTabKey);
  const searchingModeRef = useRef(searchingMode);
  const showPriceMarkersRef = useRef(showPriceMarkers);
  const geojonDataRef = useRef(geojonData);

  // Update refs when values change
  currentMapThemeOptionRef.current = currentMapThemeOption;
  selectedRowKeysHotPadsRef.current = selectedRowKeysHotPads;
  cmaTabKeyRef.current = cmaTabKey;
  searchingModeRef.current = searchingMode;
  showPriceMarkersRef.current = showPriceMarkers;
  geojonDataRef.current = geojonData;

  const showHidePriceMarkers = useCallback(() => {
    // console.log('HotPads showHidePriceMarkers called');

    if (
      map &&
      showPriceMarkersRef.current &&
      searchingModeRef.current !== 'Land' &&
      (cmaTabKeyRef.current === '1' ||
        cmaTabKeyRef.current === 'Comp Insights') && // Show on either tab
      geojonDataRef.current &&
      geojonDataRef.current.features.length > 0
    ) {
      // Check current zoom level
      const currentZoom = map.getZoom();
      // console.log('HotPads current zoom level:', currentZoom);

      if (currentZoom >= zoomLevelToShowPriceMarkers) {
        // console.log('HotPads zoom level sufficient, showing price markers');
        // Clean up existing markers first
        if (!isEmpty(priceMarkersHotPads)) {
          priceMarkersHotPads = removePriceMarkers(priceMarkersHotPads);
        }
        // Create new markers
        priceMarkersHotPads = setPriceMarkers(
          map,
          geojonDataRef.current.features,
          'property_id',
          'rent',
          sourceId,
        );
      } else {
        // console.log('HotPads zoom level too low, removing price markers');
        priceMarkersHotPads = removePriceMarkers(priceMarkersHotPads);
      }
    } else {
      // console.log('HotPads conditions not met, removing price markers');
      priceMarkersHotPads = removePriceMarkers(priceMarkersHotPads);
    }
  }, [map]);

  useEffect(() => {
    if (!map) return;

    const mouseEnter = (e) => {
      const coordinates = e.features[0].geometry.coordinates.slice();
      const htmlText = renderToString(
        <PropertyDetailPopup
          hoverPropertyDetails={{
            ...e.features[0].properties,
            type: sourceId,
          }}
        />,
      );

      propertyDetailPopup.setLngLat(coordinates).setHTML(htmlText).addTo(map);

      const popup = propertyDetailPopup.getElement();

      popup.addEventListener('mouseleave', () => {
        mouseOnPopup.current = false;
        propertyDetailPopup.remove();
      });

      popup.addEventListener('mouseenter', () => {
        mouseOnPopup.current = true;
      });

      const locateButton = popup.querySelector('#locatePropertyButton');
      const removeButton = popup.querySelector('#removePropertyButton');

      locateButton.addEventListener('click', (e) => {
        e.stopPropagation();
        locatePropertyHandler(
          locateButton.dataset.propertyType,
          locateButton.dataset.propertyId,
        );
      });

      removeButton.addEventListener('click', (e) => {
        e.stopPropagation();
        if (removeButton.dataset.propertyType != sourceId) return;

        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            selectedRowKeysHotPads: selectedRowKeysHotPadsRef.current.filter(
              (id) => id !== removeButton.dataset.propertyId,
            ),
          },
        });
      });
    };

    const mouseLeave = (e) => {
      setTimeout(() => {
        if (mouseOnPopup.current) return;
        map.getCanvas().style.cursor = '';
        propertyDetailPopup.remove();
      }, 250);
    };

    const styleLoad = () => {
      showHidePriceMarkers();
    };

    const zoomEnd = () => {
      // Handle markers on zoom change
      showHidePriceMarkers();

      // Copy over markers when map style changes
      if (currentMapThemeOptionRef.current === 'Automatic') {
        map.on('style.load', styleLoad);
      }
    };

    // Add zoom change handler
    const handleZoomChange = () => {
      const currentZoom = map.getZoom();
      // console.log('HotPads zoom changed:', currentZoom);
      showHidePriceMarkers();
    };

    // remove then re-create all markert after map changes
    // so only visible markers are created
    const mapIdle = () => {
      if (searchingModeRef.current === 'Land') return;
      showHidePriceMarkers();
    };

    map.on('mouseenter', `${sourceId}Layer`, mouseEnter);
    map.on('mouseleave', `${sourceId}Layer`, mouseLeave);
    map.on('zoomend', zoomEnd);
    map.on('zoom', handleZoomChange);
    map.on('idle', mapIdle);

    return () => {
      map.off('mouseenter', `${sourceId}Layer`, mouseEnter);
      map.off('mouseleave', `${sourceId}Layer`, mouseLeave);
      map.off('zoomend', zoomEnd);
      map.off('zoom', handleZoomChange);
      map.off('style.load', styleLoad);
      map.off('idle', mapIdle);
      priceMarkersHotPads = removePriceMarkers(priceMarkersHotPads);
    };
  }, [map, showHidePriceMarkers]);

  useEffect(() => {
    if (cmaTabKey !== '1' && cmaTabKey !== 'Comp Insights') {
      // Remove markers when not on tab 1 or Comp Insights
      priceMarkersHotPads = removePriceMarkers(priceMarkersHotPads);
      return;
    }
    showHidePriceMarkers();
  }, [cmaTabKey, showHidePriceMarkers]);

  // Update markers when relevant data changes
  useEffect(() => {
    showHidePriceMarkers();
  }, [currentHotPadsGeoJSON, showHidePriceMarkers]);

  useEffect(() => {
    showHidePriceMarkers();
  }, [showPriceMarkers, showHidePriceMarkers]);

  useEffect(() => {
    if (
      selectedRowKeysHotPads &&
      selectedRowKeysHotPads.length > 0 &&
      searchingMode === 'Lease'
    ) {
      const geoData = generateGeoJSONData(
        currentHotPadsPropertiesFiltered,
        selectedRowKeysHotPads,
        'SFR',
      );
      setGeojsonData(geoData);
    } else {
      setGeojsonData(geojsonTemplate);
    }
  }, [currentHotPadsPropertiesFiltered, selectedRowKeysHotPads, searchingMode]);

  useEffect(() => {
    showHidePriceMarkers();
  }, [geojonData, showHidePriceMarkers]);

  if (cmaTabKey === '1' || cmaTabKey === 'Comp Insights') {
    return (
      <Source id={sourceId} type="geojson" data={geojonData}>
        <Layer
          {...circleStyle}
          beforeId={`${sourceIdNationalOperatorLayer}Layer`}
        />
      </Source>
    );
  }

  return null;
}

export default HotPadsLayer;
