import { Layer, Source } from '@spatiallaser/map';
import React from 'react';
import { useSelector } from 'umi';

const sourceId = 'landDevChains';

const layerStyle = {
  id: sourceId + 'Style',
  type: 'circle',
  paint: {
    'circle-radius': 4,
    'circle-color': '#4287f5',
    'circle-stroke-color': '#fff',
    'circle-stroke-width': 2,
  },
};

const LandDevChains = () => {
  const map = useSelector((state) => state.CMA.map);
  const landDevChainGeoJSON = useSelector(
    (state) => state.CMA.landDevChainGeoJSON,
  );

  return (
    <Source id={sourceId} type="geojson" data={landDevChainGeoJSON}>
      <Layer {...layerStyle} />
    </Source>
  );
};

export default LandDevChains;
