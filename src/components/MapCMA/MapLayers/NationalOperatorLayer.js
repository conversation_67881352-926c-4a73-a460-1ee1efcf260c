import { Layer, Source } from '@spatiallaser/map';
import isEmpty from 'lodash.isempty';
import { useCallback, useEffect, useRef, useState } from 'react';
import { renderToString } from 'react-dom/server';
import { useDispatch, useSelector } from 'umi';
import { MAP_LAYER_NAME_BASE, geojsonTemplate } from '../../../constants';
import PropertyDetailPopup from '../../PropertyDetailPopup';
import { locatePropertyHandler, propertyDetailPopup } from '../MapCMA';
import {
  generateGeoJSONData,
  removePriceMarkers,
  setPriceMarkers,
} from '../MapUtility/general';
import { sourceId as sourceIdMLSLayer } from './MLSLayer';

export const sourceId = MAP_LAYER_NAME_BASE.nationalOperator;

const circleStyle = {
  id: `${sourceId}Layer`,
  type: 'circle',
  paint: {
    'circle-radius': 8,
    'circle-color': [
      'match',
      ['get', 'brand'],
      'AH4R',
      '#af2730',
      'Invitatio',
      '#206f06',
      'HRG',
      '#a41618',
      'PR',
      '#045d67',
      'CPM',
      '#3a4737',
      'TR',
      '#00B2E2',
      'MYND',
      '#485c6c',
      'KP',
      '#063f41',
      'RW',
      '#ee2375',
      // other
      '#333',
    ],
    'circle-stroke-color': '#fff',
    'circle-stroke-width': 2,
  },
};

let priceMarkersNationalOperators = {};

export const removeNationalOperatorsPriceMarkers = () => {
  if (!isEmpty(priceMarkersNationalOperators)) {
    priceMarkersNationalOperators = removePriceMarkers(
      priceMarkersNationalOperators,
    );
  }
};

export const generateNationalOperatorsPriceMarkers = (
  map,
  currentNationalOperatorsGeoJSON,
) => {
  if (
    currentNationalOperatorsGeoJSON &&
    currentNationalOperatorsGeoJSON.features.length > 0
  ) {
    if (!isEmpty(priceMarkersNationalOperators)) {
      priceMarkersNationalOperators = removePriceMarkers(
        priceMarkersNationalOperators,
      );
    }
    priceMarkersNationalOperators = setPriceMarkers(
      map,
      currentNationalOperatorsGeoJSON.features,
      'property_id',
      'rent',
      sourceId,
    );
  }
};

function NationalOperatorLayer() {
  const map = useSelector((state) => state.CMA.map);
  const searchingMode = useSelector((state) => state.CMA.searchingMode);
  const selectedRowKeysNationalOperators = useSelector(
    (state) => state.CMA.selectedRowKeysNationalOperators,
  );
  const currentNationalOperatorsPropertiesFiltered = useSelector(
    (state) => state.CMA.currentNationalOperatorsPropertiesFiltered,
  );
  const currentNationalOperatorsGeoJSON = useSelector(
    (state) => state.CMA.currentNationalOperatorsGeoJSON,
  );
  const showPriceMarkers = useSelector((state) => state.CMA.showPriceMarkers);
  const currentMapThemeOption = useSelector(
    (state) => state.CMA.currentMapThemeOption,
  );

  const dispatch = useDispatch();

  const [geojonData, setGeojsonData] = useState(geojsonTemplate);
  const searchingModeRef = useRef(searchingMode);
  searchingModeRef.current = searchingMode;
  const showPriceMarkersRef = useRef(showPriceMarkers);
  showPriceMarkersRef.current = showPriceMarkers;
  const geojsonDataRef = useRef(geojonData);
  geojsonDataRef.current = geojonData;
  const currentMapThemeOptionRef = useRef(currentMapThemeOption);
  currentMapThemeOptionRef.current = currentMapThemeOption;
  const mouseOnTopRef = useRef(false);
  const mouseOnTopOfPopupRef = useRef(false);
  const selectedRowKeysNationalOperatorsRef = useRef(
    selectedRowKeysNationalOperators,
  );
  selectedRowKeysNationalOperatorsRef.current =
    selectedRowKeysNationalOperators;

  const mouseOnPopup = useRef(false);

  const showHidePriceMarkers = useCallback(() => {
    if (
      map &&
      showPriceMarkersRef.current &&
      geojsonDataRef.current &&
      geojsonDataRef.current.features.length > 0
    ) {
      if (!isEmpty(priceMarkersNationalOperators)) {
        priceMarkersNationalOperators = removePriceMarkers(
          priceMarkersNationalOperators,
        );
      }
      priceMarkersNationalOperators = setPriceMarkers(
        map,
        geojsonDataRef.current.features,
        'property_id',
        'rent',
        sourceId,
      );
    } else {
      priceMarkersNationalOperators = removePriceMarkers(
        priceMarkersNationalOperators,
      );
    }
  }, [map, showPriceMarkers, geojonData]);

  useEffect(() => {
    if (!map) return;

    const mouseEnter = (e) => {
      // if (propertyDetailPopup.getElement() && mouseOnTopRef.current) {
      //   return;
      // }
      const coordinates = e.features[0].geometry.coordinates.slice();
      const htmlText = renderToString(
        <PropertyDetailPopup
          hoverPropertyDetails={{
            ...e.features[0].properties,
            type: sourceId,
          }}
        />,
      );
      // mouseOnTopRef.current = true;

      propertyDetailPopup.setLngLat(coordinates).setHTML(htmlText).addTo(map);

      const popup = propertyDetailPopup.getElement();

      popup.addEventListener('mouseleave', () => {
        mouseOnPopup.current = false;
        propertyDetailPopup.remove();
      });

      popup.addEventListener('mouseenter', () => {
        mouseOnPopup.current = true;
      });

      const locateButton = popup.querySelector('#locatePropertyButton');
      const removeButton = popup.querySelector('#removePropertyButton');

      // popup.addEventListener('mouseenter', () => {
      //   mouseOnTopOfPopupRef.current = true;
      // });
      // popup.addEventListener('mousemove', () => {
      //   mouseOnTopOfPopupRef.current = true;
      // });
      // popup.addEventListener('mouseleave', () => {
      //   mouseOnTopOfPopupRef.current = false;
      //   if (
      //     propertyDetailPopup.getElement() &&
      //     mouseOnTopRef.current === false
      //   ) {
      //     propertyDetailPopup.remove();
      //   }
      // });

      locateButton.addEventListener('click', (e) => {
        e.stopPropagation();
        locatePropertyHandler('SFR', locateButton.dataset.propertyId);
      });

      removeButton.addEventListener('click', (e) => {
        e.stopPropagation();
        if (removeButton.dataset.propertyType != sourceId) return;

        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            selectedRowKeysNationalOperators:
              selectedRowKeysNationalOperatorsRef.current.filter(
                (id) => id !== removeButton.dataset.propertyId,
              ),
          },
        });
        propertyDetailPopup.remove();
      });
    };

    const mouseLeave = (e) => {
      setTimeout(() => {
        if (mouseOnPopup.current) return;
        map.getCanvas().style.cursor = '';
        propertyDetailPopup.remove();
      }, 250);
      // mouseOnTopRef.current = false;
      // if (
      //   propertyDetailPopup.getElement() &&
      //   mouseOnTopOfPopupRef.current === false
      // ) {
      //   if (!e.originalEvent || !e.originalEvent.relatedTarget) {
      //     propertyDetailPopup.remove();
      //   }
      //   if (
      //     e.originalEvent &&
      //     e.originalEvent.relatedTarget &&
      //     !e.originalEvent.relatedTarget.classList.contains('mapboxgl-popup')
      //   ) {
      //     propertyDetailPopup.remove();
      //   }
      // }
    };

    const styleLoad = () => {
      showHidePriceMarkers();
    };

    const zoomEnd = () => {
      // Copy over markers when map style changes
      if (currentMapThemeOptionRef.current === 'Automatic') {
        map.on('style.load', styleLoad);
      }
    };

    // remove then re-create all markert after map changes
    // so only visible markers are created
    const mapIdle = () => {
      if (searchingModeRef.current === 'Land') return;
      showHidePriceMarkers();
    };

    map.on('mouseenter', `${sourceId}Layer`, mouseEnter);
    map.on('mouseleave', `${sourceId}Layer`, mouseLeave);
    map.on('zoomend', zoomEnd);
    map.on('idle', mapIdle);
    return () => {
      map.off('mouseenter', `${sourceId}Layer`, mouseEnter);
      map.off('mouseleave', `${sourceId}Layer`, mouseLeave);
      map.off('zoomend', zoomEnd);
      map.off('style.load', styleLoad);
      map.off('idle', mapIdle);
      priceMarkersNationalOperators = removePriceMarkers(
        priceMarkersNationalOperators,
      );
    };
  }, [map]);

  useEffect(() => {
    showHidePriceMarkers();
  }, [currentNationalOperatorsGeoJSON]);

  useEffect(() => {
    showHidePriceMarkers();
  }, [showPriceMarkers]);

  useEffect(() => {
    if (
      selectedRowKeysNationalOperators &&
      selectedRowKeysNationalOperators.length > 0 &&
      searchingMode === 'Lease'
    ) {
      const geoData = generateGeoJSONData(
        currentNationalOperatorsPropertiesFiltered,
        selectedRowKeysNationalOperators,
        'SFR',
      );
      setGeojsonData(geoData);
    } else {
      setGeojsonData(geojsonTemplate);
    }
  }, [
    currentNationalOperatorsPropertiesFiltered,
    selectedRowKeysNationalOperators,
    searchingMode,
  ]);

  useEffect(() => {
    showHidePriceMarkers();
  }, [geojonData]);

  return (
    <Source id={sourceId} type="geojson" data={geojonData}>
      <Layer {...circleStyle} beforeId={`${sourceIdMLSLayer}LayerCircle`} />
    </Source>
  );
}

export default NationalOperatorLayer;
