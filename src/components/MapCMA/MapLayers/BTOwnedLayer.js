import { Layer, Source } from '@spatiallaser/map';
import isEmpty from 'lodash.isempty';
import { useCallback, useEffect, useRef } from 'react';
import { renderToString } from 'react-dom/server';
import { useDispatch, useSelector } from 'umi';
import { MAP_LAYER_NAME_BASE } from '../../../constants';
import PropertyDetailPopup from '../../PropertyDetailPopup';
import { locatePropertyHandler, propertyDetailPopup } from '../MapCMA';
import { removePriceMarkers, setPriceMarkers } from '../MapUtility/general';
import { hideVisibility, showVisibility } from '../MapUtility/layer';

const sourceId = MAP_LAYER_NAME_BASE.BTOwned;

const circleStyle = {
  id: `${sourceId}Layer`,
  type: 'circle',
  paint: {
    'circle-radius': 8,
    'circle-color': '#164686',
    'circle-stroke-color': '#fff',
    'circle-stroke-width': 2,
  },
};

export let priceMarkersBTOwned = {};

export const removeBTPriceMarkers = () => {
  if (!isEmpty(priceMarkersBTOwned)) {
    priceMarkersBTOwned = removePriceMarkers(priceMarkersBTOwned);
  }
};

export const generateBTPriceMarkers = (map, currentBTOwnedGeoJSON) => {
  if (currentBTOwnedGeoJSON && currentBTOwnedGeoJSON.features.length > 0) {
    if (!isEmpty(priceMarkersBTOwned)) {
      priceMarkersBTOwned = removePriceMarkers(priceMarkersBTOwned);
    }
    priceMarkersBTOwned = setPriceMarkers(
      map,
      currentBTOwnedGeoJSON.features,
      'unitid',
      'totalrent',
      sourceId,
    );
  }
};

function BTOwnedLayer() {
  const map = useSelector((state) => state.CMA.map);
  const currentBTOwnedGeoJSON = useSelector(
    (state) => state.CMA.currentBTOwnedGeoJSON,
  );
  const showPriceMarkers = useSelector((state) => state.CMA.showPriceMarkers);
  const currentMapThemeOption = useSelector(
    (state) => state.CMA.currentMapThemeOption,
  );
  const searchingMode = useSelector((state) => state.CMA.searchingMode);

  const currentMapThemeOptionRef = useRef(currentMapThemeOption);
  currentMapThemeOptionRef.current = currentMapThemeOption;
  const mouseOnTopRef = useRef(false);
  const mouseOnTopOfPopupRef = useRef(false);

  const showHidePriceMarkers = useCallback(() => {
    if (
      map &&
      showPriceMarkers &&
      currentBTOwnedGeoJSON &&
      currentBTOwnedGeoJSON.features.length > 0
    ) {
      if (!isEmpty(priceMarkersBTOwned)) {
        priceMarkersBTOwned = removePriceMarkers(priceMarkersBTOwned);
      }
      priceMarkersBTOwned = setPriceMarkers(
        map,
        currentBTOwnedGeoJSON.features,
        'unitid',
        'totalrent',
        sourceId,
      );
    } else {
      if (!isEmpty(priceMarkersBTOwned)) {
        priceMarkersBTOwned = removePriceMarkers(priceMarkersBTOwned);
      }
    }
  }, [map, showPriceMarkers, currentBTOwnedGeoJSON]);

  useEffect(() => {
    if (!map) return;

    const mouseEnter = (e) => {
      if (propertyDetailPopup.getElement() && mouseOnTopRef.current) {
        return;
      }
      const coordinates = e.features[0].geometry.coordinates.slice();

      const htmlText = renderToString(
        <PropertyDetailPopup
          hoverPropertyDetails={{
            ...e.features[0].properties,
            type: sourceId,
          }}
        />,
      );

      mouseOnTopRef.current = true;

      propertyDetailPopup.setLngLat(coordinates).setHTML(htmlText).addTo(map);

      const popup = propertyDetailPopup.getElement();
      const locateButton = popup.querySelector('#locatePropertyButton');

      popup.addEventListener('mouseenter', () => {
        mouseOnTopOfPopupRef.current = true;
      });
      popup.addEventListener('mousemove', () => {
        mouseOnTopOfPopupRef.current = true;
      });
      popup.addEventListener('mouseleave', () => {
        mouseOnTopOfPopupRef.current = false;
        if (
          propertyDetailPopup.getElement() &&
          mouseOnTopRef.current === false
        ) {
          propertyDetailPopup.remove();
        }
      });

      locateButton.addEventListener('click', () => {
        locatePropertyHandler(
          locateButton.dataset.propertyType,
          locateButton.dataset.propertyId,
        );
      });
    };

    const mouseLeave = (e) => {
      mouseOnTopRef.current = false;
      if (
        propertyDetailPopup.getElement() &&
        mouseOnTopOfPopupRef.current === false
      ) {
        if (!e.originalEvent || !e.originalEvent.relatedTarget) {
          propertyDetailPopup.remove();
        }
        if (
          e.originalEvent &&
          e.originalEvent.relatedTarget &&
          !e.originalEvent.relatedTarget.classList.contains('mapboxgl-popup')
        ) {
          propertyDetailPopup.remove();
        }
      }
    };

    const styleLoad = () => {
      showHidePriceMarkers();
    };

    const zoomEnd = () => {
      // Copy over markers when map style changes
      if (currentMapThemeOptionRef.current === 'Automatic') {
        map.on('style.load', styleLoad);
      }
    };

    map.on('mouseenter', `${sourceId}Layer`, mouseEnter);
    map.on('mouseleave', `${sourceId}Layer`, mouseLeave);
    map.on('zoomend', zoomEnd);
    return () => {
      map.off('mouseenter', `${sourceId}Layer`, mouseEnter);
      map.off('mouseleave', `${sourceId}Layer`, mouseLeave);
      map.off('zoomend', zoomEnd);
      map.off('style.load', styleLoad);
    };
  }, [map]);

  useEffect(() => {
    showHidePriceMarkers();
  }, [currentBTOwnedGeoJSON]);

  useEffect(() => {
    showHidePriceMarkers();
  }, [showPriceMarkers]);

  useEffect(() => {
    if (!map) return;

    if (searchingMode === 'Lease') {
      showVisibility(map, `${sourceId}Layer`);
    } else {
      hideVisibility(map, `${sourceId}Layer`);
      if (!isEmpty(priceMarkersBTOwned)) {
        priceMarkersBTOwned = removePriceMarkers(priceMarkersBTOwned);
      }
    }
  }, [searchingMode]);

  return (
    <Source id={sourceId} type="geojson" data={currentBTOwnedGeoJSON}>
      <Layer {...circleStyle} />
    </Source>
  );
}

export default BTOwnedLayer;
