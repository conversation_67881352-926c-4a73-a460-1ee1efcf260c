import { Layer, Source } from '@spatiallaser/map';
import mapboxgl from 'mapbox-gl';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import { renderToString } from 'react-dom/server';
import { IoConstructSharp } from 'react-icons/io5';
import { useSelector } from 'umi';
import constructionIcon from '../../../assets/images/mapbox/marker/support.png';
import { geojsonTemplate } from '../../../constants';
const sourceId = 'buildingPermit';

let circleStyle = {
  id: `${sourceId}LayerCircle`,
  type: 'circle',
  filter: ['!', ['has', 'point_count']],
  // use <IoConstructSharp /> instead of circle
  paint: {
    'circle-radius': 6,
    'circle-color': '#ffcc00',
    'circle-stroke-color': '#000',
    'circle-stroke-width': 1,
  },
};
function convertToGeoJSONFormat(listOfObjects) {
  return listOfObjects.map((item) => {
    const { geom, ...properties } = item;
    return {
      type: 'Feature',
      geometry: geom,
      properties,
    };
  });
}

function BuildingPermitToolTip({ feature }) {
  const properties = feature.properties;
  console.log('BuildingPermitToolTip', properties);

  if (properties.building_permit_id) {
    return (
      <div
        style={{
          backgroundColor: 'white',
          padding: '10px',
          fontSize: '12px',
          zIndex: '999',
          width: '200px',
        }}
      >
        <div>
          <strong>Address:</strong> {properties.street}, {properties.city},{' '}
          {properties.state}, {properties.zip_code}
        </div>

        <div>
          <strong>Type:</strong> {properties.type}
        </div>
        <div>
          <strong>Description:</strong> {properties.description}
        </div>

        <div>
          <strong>Initial Status:</strong> {properties.initial_status}
        </div>
        <div>
          <strong>Issue Date:</strong> {properties.initial_status_date}
        </div>
        <div>
          <strong>Latest Status:</strong> {properties.latest_status}
        </div>
        <div>
          <strong>Latest Status Date:</strong> {properties.latest_status_date}
        </div>
      </div>
    );
  } else {
    return (
      <div
        style={{
          backgroundColor: 'white',
          padding: '10px',
          fontSize: '12px',
          zIndex: '999',
          width: '200px',
        }}
      >
        <div>
          <strong>Address:</strong> {properties.street}, {properties.city},{' '}
          {properties.state}, {properties.zip_code}
        </div>
        <div>
          <p>
            Number of Building Permits:{' '}
            {properties.building_permit
              ? JSON.parse(properties.building_permit).length
              : ''}
          </p>
        </div>
      </div>
    );
  }
}

function BuildingPermitLayer() {
  const [geoJson, setGeoJson] = useState(geojsonTemplate);
  const map = useSelector((state) => state.CMA.map);
  const filteredBuildingPermitData = useSelector(
    (state) => state.CMA.filteredBuildingPermitData,
  );
  const buildingPermitHover = useSelector(
    (state) => state.CMA.buildingPermitHover,
  );
  const tooltipRef = useRef(new mapboxgl.Popup({ offset: 15 }));

  useEffect(() => {
    const newGeoJson = {
      ...geojsonTemplate,
      features: convertToGeoJSONFormat(filteredBuildingPermitData),
    };
    setGeoJson(newGeoJson);
    console.log('newGeoJson', newGeoJson);
  }, [filteredBuildingPermitData]);

  const showTooltip = (e) => {
    const feature = e.features[0];
    console.log('tt', feature);
    const coordinates = feature.geometry.coordinates.slice();
    console.log('test coordinates', coordinates);
    const placeholder = document.createElement('div');
    ReactDOM.render(<BuildingPermitToolTip feature={feature} />, placeholder);

    tooltipRef.current
      .setLngLat(coordinates)
      .setDOMContent(placeholder.firstChild)
      .addTo(map);
  };
  const hideTooltip = () => {
    tooltipRef.current.remove();
  };

  useEffect(() => {
    if (!map) return;

    map.on('mouseenter', `${sourceId}LayerCircle`, showTooltip);
    map.on('mouseleave', `${sourceId}LayerCircle`, hideTooltip);

    return () => {
      if (map) {
        map.off('mouseenter', `${sourceId}LayerCircle`, showTooltip);
        map.off('mouseleave', `${sourceId}LayerCircle`, hideTooltip);
      }
    };
  }, [map, geoJson]);
  useEffect(() => {
    if (!map) return;

    const imgId = 'building-permit-icon';
    map.loadImage(constructionIcon, (error, image) => {
      if (error) throw error;
      if (!map.hasImage(imgId)) {
        map.addImage(imgId, image);
      }
    });

    if (buildingPermitHover) {
      console.log('buildingPermitHover', buildingPermitHover);
      const coordinates = buildingPermitHover.geom.coordinates.slice();
      const feature = {
        properties: buildingPermitHover,
        geometry: buildingPermitHover.geom,
      };
      const htmlString = renderToString(
        <BuildingPermitToolTip feature={feature} />,
      );
      const placeholder = document.createElement('div');
      placeholder.innerHTML = htmlString;
      tooltipRef.current
        .setLngLat(coordinates)
        .setDOMContent(placeholder)
        .addTo(map);
    } else {
      // Hide the tooltip if there's no data to show
      tooltipRef.current.remove();
    }
  }, [buildingPermitHover, map]);

  //  circleStyle = {
  //   id: `${sourceId}LayerCircle`,
  //   type: 'symbol',
  //   filter: ['!', ['has', 'point_count']],
  //   // use <IoConstructSharp /> instead of circle
  //   layout: {
  //     'icon-image': 'building-permit-icon', // Use the custom icon
  //     'icon-size': 0.07, // Adjust the icon size as needed
  //     'icon-allow-overlap': true, // Allow icons to overlap
  //   },
  // };

  return (
    <Source id={sourceId} type="geojson" data={geoJson}>
      <Layer {...circleStyle} />
      {/* <Layer {...symbolStyle} />
      <Layer {...clusterStyle} />
      <Layer {...clusterSymbolStyle} /> */}
    </Source>
  );
}

export default BuildingPermitLayer;
