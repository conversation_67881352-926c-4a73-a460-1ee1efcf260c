import { Layer, Source } from '@spatiallaser/map';
import mapboxgl from 'mapbox-gl';
import { useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'umi';
import { geojsonTemplate, MAP_LAYER_NAME_BASE } from '../../../constants';
import styles from '../MapCMA.css';

const sourceId = MAP_LAYER_NAME_BASE.mobile;

const heatStyle = {
  id: 'earthquakes-heat',
  type: 'heatmap',
  // maxzoom: 9,
  paint: {
    'heatmap-color': [
      'interpolate',
      ['exponential', 1],
      ['heatmap-density'],
      0,
      'hsla(0, 0%, 0%, 0)',
      0.14,
      'hsl(63, 95%, 54%)',
      0.29,
      'hsl(47, 88%, 58%)',
      0.43,
      'hsl(30, 77%, 61%)',
      0.57,
      'hsl(5, 56%, 61%)',
      0.71,
      'hsl(333, 45%, 50%)',
      0.86,
      'hsl(304, 63%, 36%)',
      1,
      'hsl(279, 100%, 31%)',
    ],
    'heatmap-radius': [
      'interpolate',
      ['exponential', 1],
      ['zoom'],
      0,
      2,
      22,
      30,
    ],
    'heatmap-opacity': 0.8,
  },
};

let poiMarker;

const createMarker = (map, type) => {
  poiMarker = new mapboxgl.Marker({ color: '#00FF00' });

  if (type === 'Toyota') {
    poiMarker.setLngLat([-86.63869320193554, 34.8295515668324]).addTo(map);
  } else if (type === 'TrinityFalls') {
    poiMarker.setLngLat([-96.61882045483974, 33.28079749918225]).addTo(map);
  }
};

function Marker(props) {
  const map = useSelector((state) => state.CMA.map);
  const currentMapThemeOption = useSelector(
    (state) => state.CMA.currentMapThemeOption,
  );

  const currentMapThemeOptionRef = useRef(currentMapThemeOption);
  currentMapThemeOptionRef.current = currentMapThemeOption;

  const propsRef = useRef(props);
  propsRef.current = props;

  const styleLoad = () => {
    if (poiMarker) {
      poiMarker.remove();
    }
    createMarker(map, propsRef.current.mobileDataType);
  };

  useEffect(() => {
    if (!map) return;
    const zoomEnd = () => {
      // Copy over markers when map style changes
      if (currentMapThemeOptionRef.current === 'Automatic') {
        map.on('style.load', styleLoad);
      }
    };
    map.on('zoomend', zoomEnd);
    return () => {
      map.off('zoomend', zoomEnd);
      map.off('style.load', styleLoad);
    };
  }, [map]);

  useEffect(() => {
    poiMarker = new mapboxgl.Marker({ color: '#00FF00' });

    createMarker(map, props.mobileDataType);
    return () => {
      poiMarker.remove();
      poiMarker = null;
    };
  }, [props.mobileDataType]);

  return null;
}

function MobileDataLayer() {
  const map = useSelector((state) => state.CMA.map);
  const mobileDataType = useSelector((state) => state.CMA.mobileDataType);
  const currentMobileGeoJSON = useSelector(
    (state) => state.CMA.currentMobileGeoJSON,
  );
  const measureMode = useSelector((state) => state.CMA.measureMode);
  const dispatch = useDispatch();

  const mobileDataTypeRef = useRef(mobileDataType);
  mobileDataTypeRef.current = mobileDataType;

  useEffect(() => {
    if (!map) return;

    const moveEnd = () => {
      if (mobileDataTypeRef.current) {
        const mapScopeBBox = map.getBounds().toArray();
        dispatch({
          type: 'CMA/getMobile',
          payload: {
            polygon_id: mobileDataTypeRef.current,
            lng1: mapScopeBBox[0][0],
            lat1: mapScopeBBox[0][1],
            lng2: mapScopeBBox[1][0],
            lat2: mapScopeBBox[1][1],
          },
        });
      }
    };

    map.on('moveend', moveEnd);
    return () => {
      map.off('moveend', moveEnd);
    };
  }, [map]);

  useEffect(() => {
    if (mobileDataType) {
      let bbox;
      if (mobileDataType === 'Toyota') {
        bbox = [-87.914311, 33.427761, -85.273806, 36.161719];
        map.fitBounds(bbox, { padding: 20 });
      } else if (mobileDataType === 'TrinityFalls') {
        bbox = [-97.432822, 32.010011, -95.217924, 33.986586];
        map.fitBounds(bbox, { padding: 20 });
      }

      dispatch({
        type: 'CMA/getMobile',
        payload: {
          polygon_id: mobileDataType,
          lng1: bbox[0],
          lat1: bbox[1],
          lng2: bbox[2],
          lat2: bbox[3],
        },
      });
    } else {
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          currentMobileGeoJSON: geojsonTemplate,
        },
      });
    }
  }, [mobileDataType]);

  return (
    <>
      <Source id={sourceId} type="geojson" data={currentMobileGeoJSON}>
        <Layer {...heatStyle} />
      </Source>
      {mobileDataType && <Marker mobileDataType={mobileDataType} />}
      {mobileDataType && (
        <button
          className={styles.clearMobileDataBtn}
          style={{ bottom: measureMode ? '140px' : '75px' }}
          onClick={() => {
            dispatch({
              type: 'CMA/saveCMAStates',
              payload: {
                mobileDataType: null,
              },
            });
          }}
        >
          Clear Mobile Data
        </button>
      )}
    </>
  );
}

export default MobileDataLayer;
