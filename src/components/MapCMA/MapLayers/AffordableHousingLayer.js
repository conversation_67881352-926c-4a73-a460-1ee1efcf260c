import { Layer, Source } from '@spatiallaser/map';
import isEmpty from 'lodash.isempty';
import mapboxgl from 'mapbox-gl';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import { renderToString } from 'react-dom/server';
import { useSelector } from 'umi';
import { geojsonTemplate } from '../../../constants';
import { formatCurrency } from '../../../utils/lastSalePublicRecord';
import { removePriceMarkers, setPriceMarkers } from '../MapUtility/general';
const sourceId = 'affordable-housing';
let priceMarkersAH = {};
const zoomLevelToShowParcelAVM = 17;
const zoomLevelToShowPriceMarkers = 12; // Define the zoom level to show price markers
export const removePRPriceMarkers = () => {
  if (!isEmpty(priceMarkersAH)) {
    priceMarkersAH = removePriceMarkers(priceMarkersAH);
  }
};
const symbolStyle = {
  id: `${sourceId}LayerSymbol`,
  type: 'symbol',
  filter: ['!', ['has', 'point_count']],
  layout: {
    'text-field': ['concat', '$', ['get', 'rent']],
    'text-variable-anchor': ['center'],
    'text-justify': 'center',
    'text-radial-offset': 1,
    // On how to use custom fonts in Mapbox, see: https://github.com/mapbox/mapbox-gl-js/issues/6666
    'text-font': [
      'Source Sans Pro Bold',
      'Open Sans Bold', // fallback font
    ],
    'text-size': [
      'interpolate',
      ['linear'],
      ['zoom'],
      zoomLevelToShowParcelAVM,
      15,
      22,
      20,
    ],
    'icon-allow-overlap': true,
    'text-allow-overlap': true,
  },
  paint: {
    'text-color': '#fff',
    'text-halo-color': '#000',
    'text-halo-width': 20, // use halo instead of an icon for text background
    // 'background-color': "#000"
    'text-opacity': 0,
    'icon-opacity': 0,
  },
};
const clusterStyle = {
  id: `${sourceId}LayerClusters`,
  type: 'circle',
  filter: ['has', 'point_count'],
  paint: {
    'circle-color': '#A9BBC0',
    // 'circle-color': '#0d1738',
    'circle-radius': [
      'interpolate',
      ['linear'],
      ['zoom'],
      0,
      ['max', ['*', ['^', ['get', 'point_count'], 0.5], 0.1], 12],
      // 10.4, ["max", ["*", ['^', ["get", "point_count"], 0.5], 1], 12],
      // zoomLevelToShowPriceMarkers, ["max", ["*", ['^', ["get", "point_count"], 0.5], 4], 12]
    ],
    'circle-opacity': 0.75,
    'circle-stroke-width': 1,
    'circle-stroke-color': 'rgba(255,255,255,1)',
  },
};

const clusterSymbolStyle = {
  id: `${sourceId}LayerClustersPointCount`,
  type: 'symbol',
  filter: ['has', 'point_count'],
  layout: {
    'text-font': ['Open Sans Bold'],
    'text-field': '{point_count}',
    'text-size': 14,
    'text-justify': 'auto',
  },
  paint: {
    // 'text-color': 'rgba(255,255,255,1)',
    'text-color': 'rgba(0,0,0,1)',
    // 'text-color': '#022386',
  },
};
export const generatePRPriceMarkers = (map, GeoJSON) => {
  console.log('testv5 GEOJson', GeoJSON);
  if (GeoJSON && GeoJSON.features.length > 0) {
    if (!isEmpty(priceMarkersAH)) {
      priceMarkersAH = removePriceMarkers(priceMarkersAH);
    }
    priceMarkersAH = setPriceMarkers(
      map,
      GeoJSON.features,
      'key',
      'rent',
      sourceId,
    );
  }
};
function convertToGeoJSONFormat(listOfObjects, keys) {
  if (listOfObjects) {
    const filteredData = listOfObjects
      .filter((item) => keys.includes(item.key))
      .map((property) => {
        const geom = {
          type: 'Point',
          coordinates: [property.longitude, property.latitude],
        };
        return { geom, ...property }; // Include geom and all other properties
      });
    console.log('listOfObjects123', filteredData);
    return filteredData.map((item) => {
      const { geom, ...properties } = item;
      return {
        type: 'Feature',
        geometry: geom,
        properties,
      };
    });
  }
  return [];
}

function PublicRecordTooltip({ feature }) {
  const properties = feature.properties;
  console.log('testv5 state', properties);

  return (
    <div
      style={{
        backgroundColor: 'white',
        padding: '10px',
        fontSize: '14px',
        zIndex: '999',
      }}
    >
      <div>
        <strong>Address:</strong> {properties.address}, {properties.city},{' '}
        {properties.state}, {properties.zip}
      </div>
      <div>
        <strong>Sqft:</strong>{' '}
        {properties.sqft === 0
          ? 'N/A'
          : new Intl.NumberFormat('en-US').format(properties.sqft)}
      </div>
      <div>
        <span>
          <strong>Bd:</strong> {properties.beds}
        </span>{' '}
        |{' '}
        <span>
          <strong>Ba:</strong>{' '}
          {Number(properties.baths + properties.halfBaths / 2)}
        </span>
      </div>

      <div>
        <strong>Rent:</strong> {formatCurrency(properties.rent)}
      </div>

      <div>
        <strong>Status:</strong> {properties.status}
      </div>
      <div>
        <strong>Type:</strong> {properties.propertyTypeCategory}
      </div>
    </div>
  );
}

function AffordableHousingLayer() {
  const [geoJson, setGeoJson] = useState(geojsonTemplate);

  useEffect(() => {
    console.log('AffordableHousingLayer', geoJson);
  }, [geoJson]);
  const map = useSelector((state) => state.CMA.map);
  const showPriceMarkers = useSelector((state) => state.CMA.showPriceMarkers);
  const affordableHousingDataForRender = useSelector(
    (state) => state.CMA.affordableHousingDataForRender,
  );
  const cmaTabKey = useSelector((state) => state.CMA.cmaTabKey);
  const affordableHousingSelectedRowKey = useSelector(
    (state) => state.CMA.affordableHousingSelectedRowKey,
  );
  const affordableHousingRowHover = useSelector(
    (state) => state.CMA.affordableHousingRowHover,
  );

  const tooltipRef = useRef(new mapboxgl.Popup({ offset: 15 }));
  const showHidePriceMarkers = useCallback(() => {
    if (
      map &&
      cmaTabKey === '6' &&
      showPriceMarkers &&
      geoJson &&
      geoJson.features.length > 0
    ) {
      const currentZoom = map.getZoom(); // Get the current zoom level
      if (currentZoom >= zoomLevelToShowPriceMarkers && showPriceMarkers) {
        if (!isEmpty(priceMarkersAH)) {
          priceMarkersAH = removePriceMarkers(priceMarkersAH);
        }
        priceMarkersAH = setPriceMarkers(
          map,
          geoJson.features,
          'key',
          'rent',
          sourceId,
        );
      } else {
        priceMarkersAH = removePriceMarkers(priceMarkersAH);
      }
    } else {
      priceMarkersAH = removePriceMarkers(priceMarkersAH);
    }
  }, [map, showPriceMarkers, geoJson, cmaTabKey]);

  useEffect(() => {
    showHidePriceMarkers();
  }, [map, geoJson]);

  useEffect(() => {
    console.log('ah cmaTabKey', cmaTabKey);
  }, [cmaTabKey]);
  useEffect(() => {
    showHidePriceMarkers();
  }, [showPriceMarkers]);

  useEffect(() => {
    const newGeoJson = {
      ...geojsonTemplate,
      features: convertToGeoJSONFormat(
        affordableHousingDataForRender,
        affordableHousingSelectedRowKey,
      ),
    };
    setGeoJson(newGeoJson);
  }, [affordableHousingDataForRender, affordableHousingSelectedRowKey]);

  useEffect(() => {
    if (affordableHousingDataForRender.length === 0) {
      priceMarkersAH = removePriceMarkers(priceMarkersAH);
    }
  }, [affordableHousingDataForRender]);
  const showTooltip = (e) => {
    const feature = e.features[0];
    const coordinates = feature.geometry.coordinates.slice();
    console.log('test coordinates', coordinates);
    const placeholder = document.createElement('div');
    ReactDOM.render(<PublicRecordTooltip feature={feature} />, placeholder);

    tooltipRef.current
      .setLngLat(coordinates)
      .setDOMContent(placeholder.firstChild)
      .addTo(map);
  };
  const hideTooltip = () => {
    tooltipRef.current.remove();
  };

  useEffect(() => {
    if (!map) return;

    map.on('mouseenter', `${sourceId}LayerCircle`, showTooltip);
    map.on('mouseleave', `${sourceId}LayerCircle`, hideTooltip);

    return () => {
      if (map) {
        map.off('mouseenter', `${sourceId}LayerCircle`, showTooltip);
        map.off('mouseleave', `${sourceId}LayerCircle`, hideTooltip);
      }
    };
  }, [map, geoJson]);
  useEffect(() => {
    return () => {
      if (!isEmpty(priceMarkersAH)) {
        priceMarkersAH = removePriceMarkers(priceMarkersAH);
      }
    };
  }, []);
  useEffect(() => {
    if (!map) return;

    if (affordableHousingRowHover) {
      const coordinates = [
        affordableHousingRowHover.longitude,
        affordableHousingRowHover.latitude,
      ];
      const feature = {
        properties: affordableHousingRowHover,
        geometry: affordableHousingRowHover.geom,
      };
      const htmlString = renderToString(
        <PublicRecordTooltip feature={feature} />,
      );
      const placeholder = document.createElement('div');
      placeholder.innerHTML = htmlString;
      tooltipRef.current
        .setLngLat(coordinates)
        .setDOMContent(placeholder)
        .addTo(map);
    } else {
      // Hide the tooltip if there's no data to show
      tooltipRef.current.remove();
    }
  }, [affordableHousingRowHover, map]);

  const circleStyle = {
    id: `${sourceId}LayerCircle`,
    type: 'circle',
    filter: ['!', ['has', 'point_count']],
    paint: {
      'circle-radius': 8,
      'circle-color': '#1777FF',
      'circle-stroke-color': '#fff',
      'circle-stroke-width': 1.5,
    },
  };

  return (
    <Source id={sourceId} type="geojson" data={geoJson}>
      <Layer {...circleStyle} />
      <Layer {...symbolStyle} />
      <Layer {...clusterStyle} />
      <Layer {...clusterSymbolStyle} />
    </Source>
  );
}

export default AffordableHousingLayer;
