// @ts-ignore
import { Layer, Source } from '@spatiallaser/map';
import { default as turf_bbox } from '@turf/bbox';
import { default as turf_circle } from '@turf/circle';
import { GeoJsonTypes } from 'geojson';
import { toPng } from 'html-to-image';
import isEqual from 'lodash.isequal';
import React, { useEffect, useState } from 'react';
import { useDispatch } from 'umi';
import { getMapboxIsochroneData, getNearestRoad } from '../../../services/data';

interface ScorecardLayerProps {
  map: any;
  open: boolean;
  lat: number;
  lng: number;
  areaType: 'drive_time' | 'buffer';
  getScreenShotData?: (data: string | null) => void;
}
// prettier-ignore
const ScorecardLayer = ({map, open, lat, lng, areaType, getScreenShotData}: ScorecardLayerProps) => {
  const dispatch = useDispatch();
  
  if (!map || !open || !lat || !lng || !areaType ) return null;
  
  // snap to nearest road - lazy quick method
  React.useEffect(() => {
    if (!map || !lat || !lng) return;

    const getSnapToRoad = async () => {
      const res = await getNearestRoad({lat, lng}) as any;
      if (res) {
        const coords = res.geometry?.coordinates;
        if (coords && coords.length) {
          if (coords[0] !== lng || coords[1] !== lat) {
            console.log('snapped to road', coords);   
            dispatch({
              type: 'CMA/saveCMAStates',
              payload: {
                eventCoordinates: [coords[0], coords[1]],
              },
            });
          }
        }
      }
    };
    getSnapToRoad();
  }, [lat, lng]);

  if (areaType === 'drive_time')
    return <ScorecardISO map={map} lat={lat} lng={lng} getScreenShotData={getScreenShotData} />;
  return (
    <ScorecardRadius map={map} lat={lat} lng={lng} getScreenShotData={getScreenShotData} />
  );
};

export default ScorecardLayer;

const getLayerStyle = (type: string, variant: string) => ({
  id: `socrecard-${type}-${variant}-fill`,
  type: 'fill',
  paint: {
    'fill-color': '#5a3fc0',
    'fill-opacity': 0.3,
  },
});

// prettier-ignore
const ScorecardISO = ({ map, lat, lng, getScreenShotData }: { map:any, lat: number; lng: number, getScreenShotData?: (data: string | null) => void }) => {
  const [geojson, setGeojson] = useState<Array<GeoJsonTypes> | null>(null);

  useEffect(() => {
    const getMapboxIsochrone = async () => {
      try {
        // prettier-ignore
        const results = await Promise.all([
          getMapboxIsochroneData({profile: 'driving', minutes: 5, lat, lng}),
          getMapboxIsochroneData({profile: 'driving', minutes: 10, lat, lng}),
          getMapboxIsochroneData({profile: 'driving', minutes: 15, lat, lng}),
          getMapboxIsochroneData({profile: 'driving', minutes: 20, lat, lng})
        ])

        map.fitBounds(turf_bbox(results[3]), {
          padding: 32,
        }).once('idle', async () => {
          if (getScreenShotData) {
            const imgData = await takeMapScreenshotForScorecard(map) as string;
            getScreenShotData(imgData);
          }
        })

        setGeojson(results);
      } catch (error) {
        console.error(error);
        setGeojson(null);
        getScreenShotData && getScreenShotData(null);
      }
    };

    getScreenShotData && getScreenShotData(null);
    getMapboxIsochrone();

    return () => {
      setGeojson(null);
      getScreenShotData && getScreenShotData(null);
    };
  }, [lat, lng]);

  if (!geojson) return null;
  return <ISOLayers geojson={geojson} />;
};
const DRIVE_TIMES = ['5min', '10min', '15min', '20min'];
const ISOLayers = React.memo(
  ({ geojson }: { geojson: Array<GeoJsonTypes> }) =>
    DRIVE_TIMES.map((time, idx) => (
      <Source
        key={time}
        id={`scorecard-iso-${time}`}
        type="geojson"
        data={geojson[idx]}
      >
        <Layer {...getLayerStyle('iso', time)} beforeId={'poi-label'} />
      </Source>
    )),
  (prevProps, nextProps) => {
    return isEqual(prevProps.geojson, nextProps.geojson);
  },
);

// prettier-ignore
const ScorecardRadius = ({ map, lat, lng , getScreenShotData }: { map:any, lat: number; lng: number, getScreenShotData?: (data: string | null) => void  }) => {
  const [geojson, setGeojson] = useState<Array<GeoJsonTypes> | null>(null);

  useEffect(() => {
    getScreenShotData && getScreenShotData(null);
    // prettier-ignore
    const results = [
      turf_circle([lng, lat], 1, { units: 'miles' }),
      turf_circle([lng, lat], 3, { units: 'miles' }),
      turf_circle([lng, lat], 5, { units: 'miles' }),
      turf_circle([lng, lat], 10, { units: 'miles' }),
    ];

    map.once('idle', () => {
      map.fitBounds(turf_bbox(results[3]), {
        padding: 32,
      }).once('idle', async () => {
        if (getScreenShotData) {
          const imgData = await takeMapScreenshotForScorecard(map) as string;
          getScreenShotData(imgData);
        }
      });
    });

    setGeojson(results);
    return () => {
      setGeojson(null);
      getScreenShotData && getScreenShotData(null);
    };
  }, [lat, lng]);

  if (!geojson) return null;
  return <RadiusLayers geojson={geojson} />;
};
const RADIUS_MILES = ['1mi', '3mi', '5mi', '10mi'];
const RadiusLayers = React.memo(
  ({ geojson }: { geojson: Array<GeoJsonTypes> }) =>
    RADIUS_MILES.map((miles, idx) => (
      <Source
        key={miles}
        id={`scorecard-radius-${miles}`}
        type="geojson"
        data={geojson[idx]}
      >
        <Layer {...getLayerStyle('radius', miles)} beforeId={'poi-label'} />
      </Source>
    )),
  (prevProps, nextProps) => {
    return isEqual(prevProps.geojson, nextProps.geojson);
  },
);

// prettier-ignore
const getScreenshotDimensions = (size: number, imgWidth:number, imgHeight:number) => {
  let newHeight, newWidth, xStart, yStart;
  if (imgHeight > imgWidth) {
    newHeight = imgHeight * (size / imgWidth);
    newWidth = size;
    xStart = 0;
    yStart = -(newHeight / 2 - size / 2);
  } else if (imgWidth > imgHeight) {
    newWidth = imgWidth * (size / imgHeight);
    newHeight = size;
    yStart = 0;
    xStart = -(newWidth / 2 - size / 2);
  } else {
    newWidth = 1000;
    newHeight = 1000;
    xStart = 0;
    yStart = 0;
  }
  return { xStart, yStart, width: newWidth, height: newHeight };
};

const takeMapScreenshotForScorecard = (map: any) => {
  // https://github.com/mapbox/mapbox-gl-js/issues/2766
  return new Promise(function (resolve, reject) {
    map.once('render', async function () {
      // const imgSrc = map.getCanvas().toDataURL();

      const zoomBtns = document.querySelector(
        `.mapboxgl-ctrl-top-right`,
      ) as HTMLElement;
      zoomBtns.style.visibility = 'hidden';

      const imgSrc = await toPng(
        document.querySelector(`.mapboxgl-map`) as HTMLElement,
      );

      zoomBtns.style.visibility = 'visible';

      // crop image
      const img = new Image();
      img.src = imgSrc;
      img.onload = () => {
        // const size = img.width; // height and width of crop image
        const size = 1000; // height and width of crop image

        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d') as CanvasRenderingContext2D;
        canvas.width = size;
        canvas.height = size;
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
        // ctx.globalCompositeOperation = 'darken';
        ctx.globalCompositeOperation = 'multiply';

        const dim = getScreenshotDimensions(size, img.width, img.height);
        ctx.drawImage(img, dim.xStart, dim.yStart, dim.width, dim.height);

        resolve(canvas.toDataURL());
      };

      // resolve(map.getCanvas().toDataURL());
    });
    /* trigger render */
    map.setBearing(map.getBearing());
  });
};
