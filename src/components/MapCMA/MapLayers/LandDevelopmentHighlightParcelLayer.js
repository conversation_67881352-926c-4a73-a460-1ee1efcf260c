import { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'umi';
import { Source, Layer } from '@spatiallaser/map';
import booleanPointInPolygon from '@turf/boolean-point-in-polygon';
import { default as turf_bbox } from '@turf/bbox';
import { getFloodZoneData } from '../../../services/data';
import { default as turf_flatten } from '@turf/flatten';
import { multiPolygon } from '@turf/helpers';
import { default as turf_intersect } from '@turf/intersect';
import { default as turf_area } from '@turf/area';
import { geojsonTemplate } from '../../../constants';
// import { geojsonTemplate, MAP_LAYER_NAME_BASE } from '../../../constants';
// import mapboxgl from 'mapbox-gl';
// import styles from '../MapCMA.css';

const sourceId = 'landDevelopmentHighlightParcelLayerSource';

const circleStyle = {
  id: 'landDevelopmentHighlightParcelLayerCircle',
  type: 'circle',
  paint: {
    'circle-radius': 8,
    'circle-color': '#ff333a',
    'circle-stroke-color': '#fff',
    'circle-stroke-width': 2,
  },
};

const defaultFilterFn = () => true;

function LandDevelopmentHighlightParcelLayer() {
  const map = useSelector((state) => state.CMA.map);

  // const landDevelopmentFilteredParcelWithinMetro = useSelector(
  //   (state) => state.CMA.landDevelopmentFilteredParcelWithinMetro,
  // );
  const currentLandDevelopmentParcelGeoJSON = useSelector(
    (state) => state.CMA.currentLandDevelopmentParcelGeoJSON,
  );
  const [geojson, setGeojson] = useState(geojsonTemplate);
  const dispatch = useDispatch();
  const [heatmapType, setHeatmapType] = useState(null);

  // const [filterFn, setFilterFn] = useState(() => defaultFilterFn);

  // useEffect(() => {
  //   if (!map) return;

  //   const updateBGFilterFn = ({ payload }) => {
  //     console.log('FILETER BG');
  //     const bgFilterFn = payload.bgFilterFn;
  //     setFilterFn(bgFilterFn);
  //   };

  //   const updateHeatmapType = ({ payload }) => {
  //     const heatmapType = payload.heatmapType;
  //     setHeatmapType(heatmapType);
  //   };

  //   map.on('heatmap.menu.setBlockGroupFilter', updateBGFilterFn);
  //   map.on('heatmap.updateType', updateHeatmapType);
  // }, [map]);

  // useEffect(() => {
  //   // if (!map) return;

  //   const geojson = {
  //     type: 'FeatureCollection',
  //     features: [],
  //   };

  //   for (let parcel of landDevelopmentFilteredParcelWithinMetro) {
  //     const { geog, ...properties } = parcel;
  //     const feature = {
  //       type: 'Feature',
  //       geometry: geog,
  //       properties,
  //     };
  //     geojson.features.push(feature);
  //   }

  //   if (heatmapType != null) {
  //     geojson.features = geojson.features.filter((feature) => {
  //       const isInside =
  //         typeof filterFn == 'function'
  //           ? filterFn(feature.properties, 'block_group_id')
  //           : true;
  //       console.log('isInside', isInside);
  //       return isInside;
  //     });
  //   } else {
  //     setFilterFn(defaultFilterFn);
  //   }

  //   setGeojson(geojson);
  // }, [landDevelopmentFilteredParcelWithinMetro, filterFn, heatmapType]);

  // useEffect(() => {
  //   if (!map) return;

  //   if (
  //     currentLandDevelopmentParcelGeoJSON &&
  //     Object.hasOwn(currentLandDevelopmentParcelGeoJSON, 'coordinates') &&
  //     currentLandDevelopmentParcelGeoJSON.coordinates.length > 0
  //   ) {
  //     map.flyTo({
  //       center: currentLandDevelopmentParcelGeoJSON.coordinates,
  //       zoom: 16,
  //     });

  //     // map.once('idle', async () => {
  //     //   console.log('HJERE');
  //     //   console.log(
  //     //     map
  //     //       .getStyle()
  //     //       .layers.find((layer) => layer.id === 'parcelsFillStyle'),
  //     //   );
  //     //   const regrid = map
  //     //     .getStyle()
  //     //     .layers.find((layer) => layer.id === 'parcelsFillStyle');

  //     //   const features = map.queryRenderedFeatures({
  //     //     layers: ['parcelsFillStyle'],
  //     //   });

  //     //   const matchingBoundary = features.find((feature) => {
  //     //     const { _x, _y, _z } = feature;
  //     //     const geojson = feature._vectorTileFeature.toGeoJSON(_x, _y, _z);
  //     //     const isInside = booleanPointInPolygon(
  //     //       currentLandDevelopmentParcelGeoJSON,
  //     //       geojson,
  //     //     );
  //     //     if (isInside) {
  //     //       return geojson;
  //     //     }
  //     //   });

  //     //   console.log('matchingBoundary', matchingBoundary);
  //     //   if (matchingBoundary) {
  //     //     const bounds = turf_bbox(matchingBoundary);
  //     //     const floodZoneData = await getFloodZoneData({
  //     //       lat1: bounds[2],
  //     //       lng1: bounds[3],
  //     //       lat2: bounds[0],
  //     //       lng2: bounds[1],
  //     //     });

  //     //     const floodGeometry = floodZoneData.reduce((acc, curr) => {
  //     //       if (
  //     //         curr.shape.coordinates.length > 0 &&
  //     //         curr.shape.coordinates[0].length > 0
  //     //       )
  //     //         acc.push(curr.shape);

  //     //       return acc;
  //     //     }, []);

  //     //     if (floodGeometry.length > 0) {
  //     //       const mergedFloodZone = multiPolygon([]);

  //     //       for (let i = 0; i < floodGeometry.length; i++) {
  //     //         if (floodGeometry[i].type === 'MultiPolygon') {
  //     //           mergedFloodZone.geometry.coordinates =
  //     //             mergedFloodZone.geometry.coordinates.concat(
  //     //               floodGeometry[i].coordinates,
  //     //             );
  //     //         } else if (floodGeometry[i].type === 'Polygon') {
  //     //           mergedFloodZone.geometry.coordinates.push(
  //     //             floodGeometry[i].coordinates,
  //     //           );
  //     //         }
  //     //       }

  //     //       const intersect = turf_intersect(
  //     //         matchingBoundary._geometry,
  //     //         mergedFloodZone,
  //     //       );

  //     //       if (intersect) {
  //     //         const area = turf_area(intersect);
  //     //         const total_area = turf_area(matchingBoundary._geometry);
  //     //         const percent = (area / total_area) * 100;

  //     //         console.log('intersect area', area);
  //     //         console.log('parcel_boundary area', total_area);
  //     //         console.log('percent', percent);
  //     //       }
  //     //     }
  //     //   }
  //     // });
  //   }
  // }, [map, currentLandDevelopmentParcelGeoJSON]);

  useEffect(() => {
    if (!map) return;

    map.on('error', (e) => {
      console.log(e);
    });
  }, [map]);

  console.log(
    'currentLandDevelopmentParcelGeoJSON',
    currentLandDevelopmentParcelGeoJSON,
  );

  return (
    <Source
      id={sourceId}
      type="geojson"
      data={currentLandDevelopmentParcelGeoJSON}
    >
      <Layer {...circleStyle} />
    </Source>
  );
}

export default LandDevelopmentHighlightParcelLayer;
