// import { useEffect, useRef, useCallback } from 'react';
// import { renderToString } from 'react-dom/server';
// import { Source, Layer } from '@spatiallaser/map';
// import { MAP_LAYER_NAME_BASE, dateFormat } from '../../../constants';
// import { useSelector, useDispatch, connect } from 'umi';
// import { default as turf_bbox } from '@turf/bbox';
// import {
//   removeBTPriceMarkers,
//   generateBTPriceMarkers,
// } from '../MapLayers/BTOwnedLayer';
// import {
//   removeHotPadsPriceMarkers,
//   generateHotPadsPriceMarkers,
// } from '../MapLayers/HotPadsLayer';
// import {
//   removeNationalOperatorsPriceMarkers,
//   generateNationalOperatorsPriceMarkers,
// } from '../MapLayers/NationalOperatorLayer';
// import {
//   removeMLSPriceMarkers,
//   generateMLSPriceMarkers,
// } from '../MapLayers/MLSLayer';
// import {
//   removeMultiFamilyPriceMarkers,
//   generateMultiFamilyPriceMarkers,
// } from '../MapLayers/MultiFamilyLayer';
// import {
//   removePadSplitPriceMarkers,
//   generatePadSplitPriceMarkers,
// } from '../MapLayers/PadSplitLayer';
// import { remove } from 'jszip';

// const sourceId = MAP_LAYER_NAME_BASE.iso;

// const fillStyle5Min = {
//   id: `${sourceId}5minLayer`,
//   type: 'fill',
//   paint: {
//     // The fill color for the layer is set to a light purple
//     'fill-color': '#5a3fc0',
//     'fill-opacity': 0.3,
//   },
// };

// const fillStyle10Min = {
//   id: `${sourceId}10minLayer`,
//   type: 'fill',
//   paint: {
//     // The fill color for the layer is set to a light purple
//     'fill-color': '#5a3fc0',
//     'fill-opacity': 0.3,
//   },
// };

// const fillStyle15Min = {
//   id: `${sourceId}15minLayer`,
//   type: 'fill',
//   paint: {
//     // The fill color for the layer is set to a light purple
//     'fill-color': '#5a3fc0',
//     'fill-opacity': 0.3,
//   },
// };

// const fillStyle20Min = {
//   id: `${sourceId}20minLayer`,
//   type: 'fill',
//   paint: {
//     // The fill color for the layer is set to a light purple
//     'fill-color': '#5a3fc0',
//     'fill-opacity': 0.3,
//   },
// };

// function ISOLayer() {
//   const map = useSelector((state) => state.CMA.map);
//   const scorecardModalOpen = useSelector(
//     (state) => state.CMA.scorecardModalOpen,
//   );
//   const isochrone5MinGeoJSON = useSelector(
//     (state) => state.CMA.isochrone5MinGeoJSON,
//   );
//   const isochrone10MinGeoJSON = useSelector(
//     (state) => state.CMA.isochrone10MinGeoJSON,
//   );
//   const isochrone15MinGeoJSON = useSelector(
//     (state) => state.CMA.isochrone15MinGeoJSON,
//   );
//   const isochrone20MinGeoJSON = useSelector(
//     (state) => state.CMA.isochrone20MinGeoJSON,
//   );
//   const eventCoordinates = useSelector((state) => state.CMA.eventCoordinates);
//   const circleBbox = useSelector((state) => state.CMA.circleBbox);

//   const dispatch = useDispatch();

//   useEffect(() => {
//     if (scorecardModalOpen && isochrone20MinGeoJSON.features.length > 0) {
//       map.fitBounds(turf_bbox(isochrone20MinGeoJSON), {
//         padding: 32,
//       });
//     }
//   }, [isochrone20MinGeoJSON]);

//   useEffect(() => {
//     if (!map) return;

//     map.fire('cma.scorecard', {
//       payload: { scorecardModalOpen: scorecardModalOpen },
//     });

//     const layers = [
//       `${MAP_LAYER_NAME_BASE.BTOwned}Layer`,
//       `${MAP_LAYER_NAME_BASE.multiFamily}Layer`,
//       `${MAP_LAYER_NAME_BASE.nationalOperator}Layer`,
//       `${MAP_LAYER_NAME_BASE.hotPads}Layer`,
//       `${MAP_LAYER_NAME_BASE.mls}LayerCircle`,
//       `${MAP_LAYER_NAME_BASE.mls}LayerSymbol`,
//       `${MAP_LAYER_NAME_BASE.mls}LayerClusters`,
//       `${MAP_LAYER_NAME_BASE.mls}LayerClustersPointCount`,
//     ];

//     if (scorecardModalOpen) {
//       dispatch({
//         type: 'CMA/saveCMAStates',
//         payload: { showPriceMarkers: false },
//       });

//       removeBTPriceMarkers();
//       removeNationalOperatorsPriceMarkers();
//       removeHotPadsPriceMarkers();
//       removeMLSPriceMarkers();
//       removeMultiFamilyPriceMarkers();
//       removePadSplitPriceMarkers();

//       layers.forEach((layer) => {
//         if (
//           map.getLayer(layer) &&
//           map.getLayoutProperty(layer, 'visibility') === 'visible'
//         ) {
//           map.setLayoutProperty(layer, 'visibility', 'none');
//         }
//       });
//     } else {
//       dispatch({
//         type: 'CMA/saveCMAStates',
//         payload: { showPriceMarkers: true },
//       });

//       layers.forEach((layer) => {
//         if (
//           map.getLayer(layer) &&
//           map.getLayoutProperty(layer, 'visibility') === 'none'
//         ) {
//           map.setLayoutProperty(layer, 'visibility', 'visible');
//         }
//       });

//       generateBTPriceMarkers();
//       generateNationalOperatorsPriceMarkers();
//       generateHotPadsPriceMarkers();
//       generateMLSPriceMarkers();
//       generateMultiFamilyPriceMarkers();
//       generatePadSplitPriceMarkers();

//       if (eventCoordinates.length > 0) {
//         map.fitBounds(circleBbox);
//       }
//     }
//   }, [scorecardModalOpen]);

//   return (
//     <>
//       <Source id={`${sourceId}5min`} type="geojson" data={isochrone5MinGeoJSON}>
//         <Layer {...fillStyle5Min} beforeId={'poi-label'} />
//       </Source>
//       <Source
//         id={`${sourceId}10min`}
//         type="geojson"
//         data={isochrone10MinGeoJSON}
//       >
//         <Layer {...fillStyle10Min} beforeId={'poi-label'} />
//       </Source>
//       <Source
//         id={`${sourceId}15min`}
//         type="geojson"
//         data={isochrone15MinGeoJSON}
//       >
//         <Layer {...fillStyle15Min} beforeId={'poi-label'} />
//       </Source>
//       <Source
//         id={`${sourceId}20min`}
//         type="geojson"
//         data={isochrone20MinGeoJSON}
//       >
//         <Layer {...fillStyle20Min} beforeId={'poi-label'} />
//       </Source>
//     </>
//   );
// }

// export default ISOLayer;
