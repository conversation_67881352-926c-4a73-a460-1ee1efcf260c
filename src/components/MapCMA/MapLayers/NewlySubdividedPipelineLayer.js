import { Layer, Source } from '@spatiallaser/map';
import mapboxgl from 'mapbox-gl';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import { renderToString } from 'react-dom/server';
import { IoConstructSharp } from 'react-icons/io5';
import { useSelector } from 'umi';
import constructionIcon from '../../../assets/images/mapbox/marker/support.png';
import { geojsonTemplate } from '../../../constants';
import { transformProjectsList } from '../../ResultTable/Pipeline/BTRPipeLine/util';
const sourceId = 'newly-subdivided-pipeline';
let circleStyle = {
  id: `${sourceId}LayerCircle`,
  type: 'circle',
  filter: ['!', ['has', 'point_count']],
  // use <IoConstructSharp /> instead of circle
  paint: {
    'circle-radius': 6,
    'circle-color': '#88Aaf5',
    'circle-stroke-color': '#000',
    'circle-stroke-width': 1,
  },
};
function convertToGeoJSONFormat(listOfObjects) {
  return listOfObjects.map((item) => {
    const { geom, latitude, longitude, ...properties } = item;
    return {
      type: 'Feature',
      geometry: {
        type: 'Point',
        coordinates: [longitude, latitude], // Note: GeoJSON uses [lng, lat] order
      },
      properties,
    };
  });
}

function BTRPipeLineTooltip({ feature }) {
  const properties = feature.properties;
  console.log('BTRPipeLineTooltip', properties);

  return (
    <div
      style={{
        backgroundColor: 'white',
        padding: '10px',
        fontSize: '12px',
        zIndex: '999',
        width: '200px',
      }}
    >
      <div>
        <strong>{properties.title}</strong>
      </div>
      <div>
        <strong>Location:</strong> {properties.locationinfo},{' '}
        {properties.county}, {properties.state}
      </div>
      <div>
        <strong>Acreage:</strong> {properties.acreage}
      </div>
      <div>
        <strong>Stage:</strong> {properties.stage}
      </div>
      <div>
        <strong>Builder:</strong> {properties.builder}
      </div>

      <div>
        <strong>Units Count:</strong> {properties.unitscount}
      </div>
    </div>
  );
}

function NewlySubdividedPipelineLayer() {
  const [geoJson, setGeoJson] = useState(geojsonTemplate);
  const map = useSelector((state) => state.CMA.map);
  const NewlySubdividedPipeLineData = useSelector(
    (state) => state.CMA.NewlySubdividedPipeLineData,
  );
  const NewlySubdividedPipeLineHover = useSelector(
    (state) => state.CMA.NewlySubdividedPipeLineHover,
  );
  const NewlySubdividedPipeLineSelectedRowKey = useSelector(
    (state) => state.CMA.NewlySubdividedPipeLineSelectedRowKey,
  );
  const tooltipRef = useRef(new mapboxgl.Popup({ offset: 15 }));

  useEffect(() => {
    const filtered = NewlySubdividedPipeLineData.filter((data) =>
      NewlySubdividedPipeLineSelectedRowKey.includes(data.cluster_id),
    );
    const newGeoJson = {
      ...geojsonTemplate,
      features: convertToGeoJSONFormat(filtered),
    };
    setGeoJson(newGeoJson);
    console.log('newGeoJson11', newGeoJson);
  }, [NewlySubdividedPipeLineData, NewlySubdividedPipeLineSelectedRowKey]);

  const showTooltip = (e) => {
    const feature = e.features[0];
    console.log('tt', feature);
    const coordinates = feature.geometry.coordinates.slice();
    console.log('test coordinates', coordinates);
    const placeholder = document.createElement('div');
    ReactDOM.render(<BTRPipeLineTooltip feature={feature} />, placeholder);

    tooltipRef.current
      .setLngLat(coordinates)
      .setDOMContent(placeholder.firstChild)
      .addTo(map);
  };
  const hideTooltip = () => {
    tooltipRef.current.remove();
  };

  useEffect(() => {
    if (!map) return;

    map.on('mouseenter', `${sourceId}LayerCircle`, showTooltip);
    map.on('mouseleave', `${sourceId}LayerCircle`, hideTooltip);

    return () => {
      if (map) {
        map.off('mouseenter', `${sourceId}LayerCircle`, showTooltip);
        map.off('mouseleave', `${sourceId}LayerCircle`, hideTooltip);
      }
    };
  }, [map, geoJson]);
  useEffect(() => {
    if (!map) return;

    if (NewlySubdividedPipeLineHover) {
      console.log('NewlySubdividedPipeLineHover', NewlySubdividedPipeLineHover);
      // Change coordinates order here for setLngLat
      const coordinates = [
        NewlySubdividedPipeLineHover.longitude,
        NewlySubdividedPipeLineHover.latitude,
      ];
      const feature = {
        properties: NewlySubdividedPipeLineHover,
        geometry: {
          type: 'Point',
          coordinates: [
            NewlySubdividedPipeLineHover.longitude,
            NewlySubdividedPipeLineHover.latitude,
          ],
        },
      };
      const htmlString = renderToString(
        <BTRPipeLineTooltip feature={feature} />,
      );
      const placeholder = document.createElement('div');
      placeholder.innerHTML = htmlString;
      tooltipRef.current
        .setLngLat(coordinates) // Now using [lng, lat] order
        .setDOMContent(placeholder)
        .addTo(map);
    } else {
      tooltipRef.current.remove();
    }
  }, [NewlySubdividedPipeLineHover, map]);
  return (
    <Source id={sourceId} type="geojson" data={geoJson}>
      <Layer {...circleStyle} />
    </Source>
  );
}

export default NewlySubdividedPipelineLayer;
