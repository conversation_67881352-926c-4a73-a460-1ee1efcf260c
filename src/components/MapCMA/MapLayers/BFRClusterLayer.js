import { Layer, Source } from '@spatiallaser/map';
import mapboxgl from 'mapbox-gl';
import React, { useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import { useSelector } from 'react-redux';
import { geojsonTemplate } from '../../../constants';
import { getBFRClusterData } from '../../../services/data';

const sourceId = 'bfr cluster-source';
const pointStyle = {
  id: 'bfr cluster-point',
  type: 'circle',
  source: sourceId,
  paint: {
    'circle-radius': ['step', ['zoom'], 8, 11, 14],
    'circle-color': '#FFa770',
    'circle-stroke-color': '#ffffff',
    'circle-stroke-width': 2,
  },
};

const textStyle = {
  id: 'bfr cluster-label',
  type: 'symbol',
  source: sourceId,
  layout: {
    'text-field': ['step', ['zoom'], '', 11, ['get', 'totalCount']],
    'text-font': ['Open Sans Bold', 'Arial Unicode MS Bold'],
    'text-size': 12,
    'text-offset': [0, -0.55],
    'text-anchor': 'top',
    'text-allow-overlap': true,
    'icon-allow-overlap': true,
    'text-ignore-placement': true,
  },
  paint: {
    'text-color': '#000',
  },
};

// Helper function to get first 3-4 words of text
const getFirst3Words = (str) => {
  if (!str) return '';
  return (
    str.split(' ').slice(0, 4).join(' ').trim() +
    (str.split(' ').length > 3 ? '...' : '')
  );
};

// Tooltip component
function BFRClusterTooltip({ properties }) {
  // Handle website click to prevent closing popup
  const handleWebsiteClick = (e) => {
    e.stopPropagation();
  };

  return (
    <div
      style={{
        backgroundColor: 'white',
        padding: '10px',
        fontSize: '14px',
        zIndex: '999',
        borderRadius: '4px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        maxWidth: '300px',
      }}
    >
      <div style={{ marginBottom: '5px' }}>
        <h4 style={{ margin: '0 0 5px 0' }}>
          {getFirst3Words(properties.commonSubdivision)}
        </h4>
      </div>
      {/* 
      {properties.totalCount && (
        <div style={{ marginBottom: '4px' }}>
          <strong>Total Properties:</strong> {properties.totalCount}
        </div>
      )} */}

      {properties.commonSubdivision && (
        <div style={{ marginBottom: '4px' }}>
          <strong>Community:</strong> {properties.commonSubdivision}
        </div>
      )}

      {properties.website && (
        <div>
          <a
            href={properties.website}
            target="_blank"
            rel="noopener noreferrer"
            onClick={handleWebsiteClick}
            style={{
              color: '#1890ff',
              textDecoration: 'none',
              display: 'block',
            }}
          >
            Visit Website →
          </a>
        </div>
      )}
    </div>
  );
}

const BFRClusterLayer = () => {
  const map = useSelector((state) => state.CMA.map);
  const currentMapLayerOptions = useSelector(
    (state) => state.CMA.currentMapLayerOptions,
  );
  const [geojson, setGeojson] = useState(geojsonTemplate);
  const popupRef = useRef(null);
  const mouseInsidePopupRef = useRef(false);
  const activeFeatureRef = useRef(null);
  const timerRef = useRef(null);

  // Fetch data effect
  useEffect(() => {
    if (!map || !currentMapLayerOptions.includes('bfr cluster')) return;

    const fetchData = async () => {
      const data = await getBFRClusterData();
      const convertedGeojson = convertToGeoJSON({
        data,
        geomAccessor: (item) => item.geom,
        propertiesAccessor: (item) => {
          const { geom, ...properties } = item;
          return properties;
        },
      });
      setGeojson(convertedGeojson);
    };

    fetchData();
  }, [map, currentMapLayerOptions]);

  // Initialize popup
  useEffect(() => {
    if (!map) return;

    popupRef.current = new mapboxgl.Popup({
      offset: 15,
      closeButton: false,
      closeOnClick: false,
      className: 'bfr-interactive-popup',
    });

    // Add event listeners to the popup element after it's created
    const setupPopupEvents = () => {
      const popupElement = document.querySelector('.bfr-interactive-popup');
      if (popupElement) {
        // Mouse enter popup
        popupElement.addEventListener('mouseenter', () => {
          mouseInsidePopupRef.current = true;
          if (timerRef.current) {
            clearTimeout(timerRef.current);
            timerRef.current = null;
          }
        });

        // Mouse leave popup
        popupElement.addEventListener('mouseleave', () => {
          mouseInsidePopupRef.current = false;
          // Start a short delay before closing to allow movement back to marker
          timerRef.current = setTimeout(() => {
            if (!mouseInsidePopupRef.current) {
              popupRef.current.remove();
              activeFeatureRef.current = null;
            }
          }, 300);
        });
      }
    };

    // Listen for the popup to be added to the DOM
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.addedNodes.length) {
          const popupElement = document.querySelector('.bfr-interactive-popup');
          if (popupElement) {
            setupPopupEvents();
          }
        }
      });
    });

    observer.observe(document.body, { childList: true, subtree: true });

    return () => {
      if (popupRef.current) {
        popupRef.current.remove();
      }
      observer.disconnect();
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [map]);

  // Setup mouse events for markers
  useEffect(() => {
    if (!map || !currentMapLayerOptions.includes('bfr cluster')) return;

    const handleMouseEnter = (e) => {
      if (e.features.length === 0) return;

      const feature = e.features[0];
      activeFeatureRef.current = feature;

      // Clear any existing timer
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }

      // Create the popup content
      const placeholder = document.createElement('div');

      // Use ReactDOM to render the tooltip
      ReactDOM.render(
        <BFRClusterTooltip properties={feature.properties} />,
        placeholder,
      );

      // Set the popup location and content
      popupRef.current
        .setLngLat(feature.geometry.coordinates.slice())
        .setDOMContent(placeholder)
        .addTo(map);
    };

    const handleMouseLeave = () => {
      // Only start the timer if mouse is not inside popup
      if (!mouseInsidePopupRef.current) {
        timerRef.current = setTimeout(() => {
          if (!mouseInsidePopupRef.current) {
            popupRef.current.remove();
            activeFeatureRef.current = null;
          }
        }, 300);
      }
    };

    // Add event listeners
    map.on('mouseenter', 'bfr cluster-point', handleMouseEnter);
    map.on('mouseleave', 'bfr cluster-point', handleMouseLeave);

    return () => {
      map.off('mouseenter', 'bfr cluster-point', handleMouseEnter);
      map.off('mouseleave', 'bfr cluster-point', handleMouseLeave);
    };
  }, [map, currentMapLayerOptions]);

  if (!currentMapLayerOptions.includes('bfr cluster')) return null;

  return (
    <>
      <Source id={sourceId} type="geojson" data={geojson}>
        <Layer {...pointStyle} />
        <Layer {...textStyle} />
      </Source>
    </>
  );
};

export default BFRClusterLayer;

const convertToGeoJSON = ({ data, geomAccessor, propertiesAccessor }) => {
  const geojson = structuredClone(geojsonTemplate);

  geojson.features = data.map((item) => {
    return {
      type: 'Feature',
      geometry: geomAccessor(item),
      properties: propertiesAccessor ? propertiesAccessor(item) : item,
    };
  });

  return geojson;
};
