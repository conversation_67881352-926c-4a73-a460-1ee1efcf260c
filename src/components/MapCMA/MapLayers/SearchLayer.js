import { useEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'umi';
import { Source, Layer } from '@spatiallaser/map';
import { default as turf_bbox } from '@turf/bbox';
import { geojsonTemplate } from '../../../constants';

const sourceId = 'search';

const lineStyle = {
  id: `${sourceId}LineStyle`,
  type: 'line',
  paint: {
    'line-color': '#ff0000',
    'line-width': 3,
  },
};

function SearchLayer() {
  const map = useSelector((state) => state.CMA.map);
  const eventCoordinates = useSelector((state) => state.CMA.eventCoordinates);
  const drawnCustomPolygons = useSelector(
    (state) => state.CMA.drawnCustomPolygons,
  );
  const zipCodeSearchGeoJSON = useSelector(
    (state) => state.CMA.zipCodeSearchGeoJSON,
  );
  const dispatch = useDispatch();

  useEffect(() => {
    if (zipCodeSearchGeoJSON.features.length > 0) {
      map.fitBounds(turf_bbox(zipCodeSearchGeoJSON), {
        padding: 32,
      });
    }
  }, [zipCodeSearchGeoJSON]);

  useEffect(() => {
    if (zipCodeSearchGeoJSON.features.length > 0) {
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          zipCodeSearchGeoJSON: geojsonTemplate,
        },
      });
    }
  }, [eventCoordinates, drawnCustomPolygons]);

  return (
    <Source id={sourceId} type="geojson" data={zipCodeSearchGeoJSON}>
      <Layer {...lineStyle} />
    </Source>
  );
}

export default SearchLayer;
