import { Layer, Source } from '@spatiallaser/map';
import isEmpty from 'lodash.isempty';
import { useCallback, useEffect, useRef } from 'react';
import { renderToString } from 'react-dom/server';
import { useDispatch, useSelector } from 'umi';
import { MAP_LAYER_NAME_BASE } from '../../../constants';
import PropertyDetailPopup from '../../PropertyDetailPopup';
import { locatePropertyHandler, propertyDetailPopup } from '../MapCMA';
import { removePriceMarkers, setPriceMarkers } from '../MapUtility/general';
import { hideVisibility, showVisibility } from '../MapUtility/layer';

const sourceId = MAP_LAYER_NAME_BASE.multiFamily;

const circleStyle = {
  id: `${sourceId}Layer`,
  type: 'circle',
  paint: {
    'circle-radius': 8,
    'circle-color': '#14828c',
    'circle-stroke-color': '#fff',
    'circle-stroke-width': 2,
  },
};

let priceMarkersMultiFamily = {};

export const removeMultiFamilyPriceMarkers = () => {
  if (!isEmpty(priceMarkersMultiFamily)) {
    priceMarkersMultiFamily = removePriceMarkers(priceMarkersMultiFamily);
  }
};

export const generateMultiFamilyPriceMarkers = (
  map,
  currentMultiFamilyGeoJSON,
) => {
  if (
    currentMultiFamilyGeoJSON &&
    currentMultiFamilyGeoJSON.features.length > 0
  ) {
    if (!isEmpty(priceMarkersMultiFamily)) {
      priceMarkersMultiFamily = removePriceMarkers(priceMarkersMultiFamily);
    }
    priceMarkersMultiFamily = setPriceMarkers(
      map,
      currentMultiFamilyGeoJSON.features,
      'uid',
      'total_relevant_units',
      sourceId,
    );
  }
};

function MultiFamilyLayer() {
  const map = useSelector((state) => state.CMA.map);
  const currentMultiFamilyGeoJSON = useSelector(
    (state) => state.CMA.currentMultiFamilyGeoJSON,
  );
  const showPriceMarkers = useSelector((state) => state.CMA.showPriceMarkers);
  const currentMapThemeOption = useSelector(
    (state) => state.CMA.currentMapThemeOption,
  );
  const MultiFamilyDisplayedOnMap = useSelector(
    (state) => state.CMA.MultiFamilyDisplayedOnMap,
  );
  const searchingMode = useSelector((state) => state.CMA.searchingMode);

  const searchingModeRef = useRef(searchingMode);
  searchingModeRef.current = searchingMode;
  const showPriceMarkersRef = useRef(showPriceMarkers);
  showPriceMarkersRef.current = showPriceMarkers;
  const currentMultiFamilyGeoJSONRef = useRef(currentMultiFamilyGeoJSON);
  currentMultiFamilyGeoJSONRef.current = currentMultiFamilyGeoJSON;
  const currentMapThemeOptionRef = useRef(currentMapThemeOption);
  currentMapThemeOptionRef.current = currentMapThemeOption;
  const mouseOnTopRef = useRef(false);
  const mouseOnTopOfPopupRef = useRef(false);

  const showHidePriceMarkers = useCallback(() => {
    if (
      map &&
      showPriceMarkersRef.current &&
      currentMultiFamilyGeoJSONRef.current &&
      currentMultiFamilyGeoJSONRef.current.features.length > 0
    ) {
      if (!isEmpty(priceMarkersMultiFamily)) {
        priceMarkersMultiFamily = removePriceMarkers(priceMarkersMultiFamily);
      }
      priceMarkersMultiFamily = setPriceMarkers(
        map,
        currentMultiFamilyGeoJSONRef.current.features,
        'uid',
        'total_relevant_units',
        sourceId,
      );
    } else {
      priceMarkersMultiFamily = removePriceMarkers(priceMarkersMultiFamily);
    }
  }, [map, showPriceMarkers, currentMultiFamilyGeoJSON]);

  useEffect(() => {
    if (!map) return;

    const mouseEnter = (e) => {
      if (propertyDetailPopup.getElement() && mouseOnTopRef.current) {
        return;
      }
      const coordinates = e.features[0].geometry.coordinates.slice();
      const htmlText = renderToString(
        <PropertyDetailPopup
          hoverPropertyDetails={{
            ...e.features[0].properties,
            type: sourceId,
          }}
        />,
      );

      mouseOnTopRef.current = true;

      propertyDetailPopup.setLngLat(coordinates).setHTML(htmlText).addTo(map);

      const popup = propertyDetailPopup.getElement();
      const locateButton = popup.querySelector('#locatePropertyButton');

      popup.addEventListener('mouseenter', () => {
        mouseOnTopOfPopupRef.current = true;
      });
      popup.addEventListener('mousemove', () => {
        mouseOnTopOfPopupRef.current = true;
      });
      popup.addEventListener('mouseleave', () => {
        mouseOnTopOfPopupRef.current = false;
        if (
          propertyDetailPopup.getElement() &&
          mouseOnTopRef.current === false
        ) {
          propertyDetailPopup.remove();
        }
      });

      locateButton.addEventListener('click', () => {
        locatePropertyHandler(
          locateButton.dataset.propertyType,
          locateButton.dataset.propertyId,
        );
      });
    };

    const mouseLeave = (e) => {
      mouseOnTopRef.current = false;
      if (
        propertyDetailPopup.getElement() &&
        mouseOnTopOfPopupRef.current === false
      ) {
        if (!e.originalEvent || !e.originalEvent.relatedTarget) {
          propertyDetailPopup.remove();
        }
        if (
          e.originalEvent &&
          e.originalEvent.relatedTarget &&
          !e.originalEvent.relatedTarget.classList.contains('mapboxgl-popup')
        ) {
          propertyDetailPopup.remove();
        }
      }
    };

    const styleLoad = () => {
      showHidePriceMarkers();
    };

    const zoomEnd = () => {
      // Copy over markers when map style changes
      if (currentMapThemeOptionRef.current === 'Automatic') {
        map.on('style.load', styleLoad);
      }
    };

    map.on('mouseenter', `${sourceId}Layer`, mouseEnter);
    map.on('mouseleave', `${sourceId}Layer`, mouseLeave);
    map.on('zoomend', zoomEnd);
    return () => {
      map.off('mouseenter', `${sourceId}Layer`, mouseEnter);
      map.off('mouseleave', `${sourceId}Layer`, mouseLeave);
      map.off('zoomend', zoomEnd);
      map.off('style.load', styleLoad);
      priceMarkersMultiFamily = removePriceMarkers(priceMarkersMultiFamily);
    };
  }, [map]);
  useEffect(() => {
    return () => {
      if (!isEmpty(priceMarkersMultiFamily)) {
        priceMarkersMultiFamily = removePriceMarkers(priceMarkersMultiFamily);
      }
    };
  }, []);
  useEffect(() => {
    showHidePriceMarkers();
  }, [currentMultiFamilyGeoJSON]);

  useEffect(() => {
    showHidePriceMarkers();
  }, [showPriceMarkers]);

  useEffect(() => {
    if (!map) return;

    if (searchingMode === 'Lease') {
      showVisibility(map, `${sourceId}Layer`);
    } else {
      hideVisibility(map, `${sourceId}Layer`);
      if (!isEmpty(priceMarkersMultiFamily)) {
        priceMarkersMultiFamily = removePriceMarkers(priceMarkersMultiFamily);
      }
    }
  }, [searchingMode]);

  useEffect(() => {
    if (!map) return;

    if (MultiFamilyDisplayedOnMap) {
      showHidePriceMarkers();
      if (map.getLayer(`${sourceId}Layer`)) {
        map.setLayoutProperty(`${sourceId}Layer`, 'visibility', 'visible');
      }
    } else {
      if (!isEmpty(priceMarkersMultiFamily)) {
        priceMarkersMultiFamily = removePriceMarkers(priceMarkersMultiFamily);
      }

      // if (highlightPropertyMarker) {
      //   highlightPropertyMarker.remove();
      // }

      if (map.getLayer(`${sourceId}Layer`)) {
        map.setLayoutProperty(`${sourceId}Layer`, 'visibility', 'none');
      }
    }
  }, [MultiFamilyDisplayedOnMap]);

  return (
    <Source id={sourceId} type="geojson" data={currentMultiFamilyGeoJSON}>
      <Layer {...circleStyle} />
    </Source>
  );
}

export default MultiFamilyLayer;
