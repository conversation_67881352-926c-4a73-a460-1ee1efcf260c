import { Layer, Source } from '@spatiallaser/map';
import mapboxgl from 'mapbox-gl';
import React, { useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import { useSelector } from 'react-redux';
import { geojsonTemplate } from '../../../constants';
import { getScorecardHistory } from '../../../services/data';

const pointSourceId = 'scorecard-history-source';

const pointStyle = {
  id: 'scorecard-history-point',
  type: 'circle',
  source: pointSourceId,
  paint: {
    'circle-radius': ['step', ['zoom'], 6, 11, 10],
    'circle-color': [
      'interpolate',
      ['linear'],
      [
        'case',
        ['<=', ['to-number', ['get', 'overall_score']], 2],
        3,
        ['>=', ['to-number', ['get', 'overall_score']], 9],
        8,
        ['to-number', ['get', 'overall_score']],
      ],
      3,
      '#FF0000', // Red for score 3 (includes 1,2)
      4,
      '#FF4500', // Orange-red for score 4
      5,
      '#FFA500', // Orange for score 5
      6,
      '#FFD700', // Gold for score 6
      7,
      '#ADFF2F', // Green-yellow for score 7
      8,
      '#00FF00', // Green for score 8 (includes 9,10)
    ],
    'circle-stroke-color': '#ffffff',
    'circle-stroke-width': 2,
  },
};

const textStyle = {
  id: 'scorecard-history-label',
  type: 'symbol',
  source: pointSourceId,
  layout: {
    'text-field': ['step', ['zoom'], '', 11, ['get', 'overall_score']],
    'text-font': ['Open Sans Bold', 'Arial Unicode MS Bold'],
    'text-size': 10,
    'text-offset': [0, -0.55],
    'text-anchor': 'top',
    'text-allow-overlap': true,
    'icon-allow-overlap': true,
    'text-ignore-placement': true,
  },
  paint: {
    'text-color': '#fff',
  },
};

// Tooltip component
function ScorecardHistoryTooltip({ properties }) {
  return (
    <div
      style={{
        backgroundColor: 'white',
        padding: '12px',
        fontSize: '14px',
        zIndex: '999',
        borderRadius: '6px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
        maxWidth: '320px',
        border: '1px solid #e0e0e0',
      }}
    >
      {properties.id && (
        <div style={{ marginBottom: '6px' }}>
          <strong>ID:</strong> {properties.id}
        </div>
      )}

      {properties.timestamp && (
        <div style={{ marginBottom: '6px' }}>
          <strong>Timestamp:</strong> {properties.timestamp}
        </div>
      )}

      {properties.overall_score && (
        <div style={{ marginBottom: '6px' }}>
          <strong>Overall Score:</strong>{' '}
          {parseFloat(properties.overall_score).toFixed(2)}
        </div>
      )}

      {properties.development_manager && (
        <div style={{ marginBottom: '6px' }}>
          <strong>Development Manager:</strong> {properties.development_manager}
        </div>
      )}
    </div>
  );
}

// Legend component
function ScorecardLegend() {
  return (
    <div
      className="mapboxgl-ctrl mapboxgl-ctrl-group"
      style={{
        position: 'absolute',
        bottom: '50px',
        left: '10px',
        backgroundColor: 'white',
        padding: '8px',
        borderRadius: '4px',
        fontSize: '11px',
        minWidth: '160px',
        maxWidth: '180px',
        boxShadow: '0 0 0 2px rgba(0,0,0,.1)',
      }}
    >
      <div
        style={{
          fontWeight: 'bold',
          marginBottom: '6px',
          fontSize: '12px',
          color: '#333',
        }}
      >
        Score
      </div>

      {/* Gradient bar */}
      <div style={{ marginBottom: '4px' }}>
        <div
          style={{
            width: '100%',
            height: '12px',
            background:
              'linear-gradient(to right, #FF0000, #FF4500, #FFA500, #FFD700, #ADFF2F, #00FF00)',
            borderRadius: '2px',
            border: '1px solid #ddd',
          }}
        />

        {/* Score labels */}
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            marginTop: '2px',
          }}
        >
          <span style={{ fontSize: '11px', color: '#666' }}>1</span>
          <span style={{ fontSize: '11px', color: '#666' }}>10</span>
        </div>
      </div>

      {/* Quality labels */}
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          fontSize: '11px',
          color: '#666',
        }}
      >
        <span>Poor</span>
        <span>Excellent</span>
      </div>
    </div>
  );
}

const ScorecardHistoryLayer = () => {
  const map = useSelector((state) => state.CMA.map);
  const displayScorecardHistory = useSelector(
    (state) => state.CMA.displayScorecardHistory,
  );
  const userEmail = useSelector((state) => state.CMA.userEmail);
  const [pointGeojson, setPointGeojson] = useState(geojsonTemplate);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const popupRef = useRef(null);
  const mouseInsidePopupRef = useRef(false);
  const activeFeatureRef = useRef(null);
  const timerRef = useRef(null);

  // Fetch data effect
  useEffect(() => {
    if (!map || !displayScorecardHistory || !userEmail) return;

    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        const data = await getScorecardHistory({ find: userEmail });

        if (data && Array.isArray(data)) {
          // Convert API response to GeoJSON format
          const pointsGeoJson = {
            type: 'FeatureCollection',
            features: data.map((item) => ({
              type: 'Feature',
              geometry: {
                type: 'Point',
                coordinates: [
                  parseFloat(item.longitude),
                  parseFloat(item.latitude),
                ],
              },
              properties: {
                id: item.id,
                timestamp: item.timestamp,
                overall_score: item.overall_score,
                development_manager: item.development_manager,
              },
            })),
          };
          setPointGeojson(pointsGeoJson);
        } else {
          setPointGeojson(geojsonTemplate);
        }
      } catch (err) {
        console.error('Error fetching scorecard history:', err);
        setError(err.message);
        setPointGeojson(geojsonTemplate);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [map, displayScorecardHistory, userEmail]);

  // Initialize popup
  useEffect(() => {
    if (!map) return;

    popupRef.current = new mapboxgl.Popup({
      offset: 15,
      closeButton: false,
      closeOnClick: false,
      className: 'scorecard-history-interactive-popup',
    });

    // Add event listeners to the popup element after it's created
    const setupPopupEvents = () => {
      const popupElement = document.querySelector(
        '.scorecard-history-interactive-popup',
      );
      if (popupElement) {
        // Mouse enter popup
        popupElement.addEventListener('mouseenter', () => {
          mouseInsidePopupRef.current = true;
          if (timerRef.current) {
            clearTimeout(timerRef.current);
            timerRef.current = null;
          }
        });

        // Mouse leave popup
        popupElement.addEventListener('mouseleave', () => {
          mouseInsidePopupRef.current = false;
          // Start a short delay before closing to allow movement back to marker
          timerRef.current = setTimeout(() => {
            if (!mouseInsidePopupRef.current) {
              popupRef.current.remove();
              activeFeatureRef.current = null;
            }
          }, 300);
        });
      }
    };

    // Listen for the popup to be added to the DOM
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.addedNodes.length) {
          const popupElement = document.querySelector(
            '.scorecard-history-interactive-popup',
          );
          if (popupElement) {
            setupPopupEvents();
          }
        }
      });
    });

    observer.observe(document.body, { childList: true, subtree: true });

    return () => {
      if (popupRef.current) {
        popupRef.current.remove();
      }
      observer.disconnect();
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [map]);

  // Setup mouse events for markers
  useEffect(() => {
    if (!map || !displayScorecardHistory) return;

    const handleMouseEnter = (e) => {
      if (e.features.length === 0) return;

      const feature = e.features[0];
      activeFeatureRef.current = feature;

      // Clear any existing timer
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }

      // Create the popup content
      const placeholder = document.createElement('div');

      // Use ReactDOM to render the tooltip
      ReactDOM.render(
        <ScorecardHistoryTooltip properties={feature.properties} />,
        placeholder,
      );

      // Set the popup location and content
      popupRef.current
        .setLngLat(feature.geometry.coordinates.slice())
        .setDOMContent(placeholder)
        .addTo(map);
    };

    const handleMouseLeave = () => {
      // Only start the timer if mouse is not inside popup
      if (!mouseInsidePopupRef.current) {
        timerRef.current = setTimeout(() => {
          if (!mouseInsidePopupRef.current) {
            popupRef.current.remove();
            activeFeatureRef.current = null;
          }
        }, 300);
      }
    };

    // Add event listeners
    map.on('mouseenter', 'scorecard-history-point', handleMouseEnter);
    map.on('mouseleave', 'scorecard-history-point', handleMouseLeave);

    return () => {
      map.off('mouseenter', 'scorecard-history-point', handleMouseEnter);
      map.off('mouseleave', 'scorecard-history-point', handleMouseLeave);
    };
  }, [map, displayScorecardHistory]);

  // Add legend to map when layer is active
  useEffect(() => {
    if (!map || !displayScorecardHistory) return;

    // Create legend container
    const legendContainer = document.createElement('div');
    legendContainer.className = 'scorecard-legend-container';

    // Render legend into the container
    ReactDOM.render(<ScorecardLegend />, legendContainer);

    // Add to map container
    const mapContainer = map.getContainer();
    mapContainer.appendChild(legendContainer);

    return () => {
      // Cleanup: remove legend when component unmounts or layer is disabled
      if (legendContainer.parentNode) {
        ReactDOM.unmountComponentAtNode(legendContainer);
        legendContainer.parentNode.removeChild(legendContainer);
      }
    };
  }, [map, displayScorecardHistory]);

  if (!displayScorecardHistory) return null;

  return (
    <Source id={pointSourceId} type="geojson" data={pointGeojson}>
      <Layer {...pointStyle} />
      <Layer {...textStyle} />
    </Source>
  );
};

export default ScorecardHistoryLayer;
