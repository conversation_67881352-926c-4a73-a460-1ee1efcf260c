import { Layer, Source } from '@spatiallaser/map';
import { useCallback, useEffect, useRef, useState } from 'react';
import { renderToString } from 'react-dom/server';
import { useSelector } from 'umi';
import { serverType, tileURLRoot } from '../../../services/data';
import { initPopup } from '../MapUtility/general';

// const getTileURL = (serverType, token, segment, minValue, maxValue) => //1
//   `http://localhost:8080/segments/filtered/tile/{z}/{x}/{y}.mvt?access_token=${token}&key=${segment}&minValue=${minValue}&maxValue=${maxValue}`;
const getTileURL = (
  serverType,
  token,
  segment,
  minValue,
  maxValue, //2
) =>
  `${tileURLRoot(
    'cma',
    serverType,
  )}/segments/filtered/tile/{z}/{x}/{y}.mvt?access_token=${token}&key=${segment}&minValue=${minValue}&maxValue=${maxValue}`;

// const getTileURL = (serverType, token, segment) => //3
//   `${tileURLRoot(
//     'cma',
//     serverType,
//   )}/segments/${segment}/tile/{z}/{x}/{y}.mvt?access_token=${token}`;

const SegmentOverviewLayer = () => {
  const segment = useSelector((state) => state.CMA.selectedSegment);
  const selectedSegmentHeatmapMinValue = useSelector(
    (state) => state.CMA.selectedSegmentHeatmapMinValue,
  );
  const selectedSegmentHeatmapMaxValue = useSelector(
    (state) => state.CMA.selectedSegmentHeatmapMaxValue,
  );
  const map = useSelector((state) => state.CMA.map);

  const currentMapLayerOptions = useSelector(
    (state) => state.CMA.currentMapLayerOptions,
  );
  const displaySelectedSegmentHeatmap = useSelector(
    (state) => state.CMA.displaySelectedSegmentHeatmap,
  );

  const [token, setToken] = useState(null);
  const popupRef = useRef(null);
  const currentListenersRef = useRef({ layerId: null, added: false });

  console.log(
    'getTile with tileURLRoot',
    getTileURL(
      serverType,
      token,
      segment,
      selectedSegmentHeatmapMinValue,
      selectedSegmentHeatmapMaxValue,
    ),
  );
  // Initialize popup once
  useEffect(() => {
    if (map && !popupRef.current) {
      popupRef.current = initPopup();
    }
  }, [map]);

  const getTooltipHTML = useCallback((properties) => {
    return renderToString(
      <div
        style={{
          padding: '12px',
          display: 'flex',
          flexDirection: 'column',
          gap: '4px',
          minWidth: '200px',
        }}
      >
        <span
          style={{
            fontWeight: 'bold',
            marginBottom: '8px',
            fontSize: '14px',
            borderBottom: '1px solid #ddd',
            paddingBottom: '4px',
          }}
        >
          Block Group: {properties.geoid}
        </span>

        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <span>Segment Percentage:</span>
          <span style={{ fontWeight: '600' }}>
            {properties.segment_percentage.toFixed(2)}%
          </span>
        </div>

        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <span>Segment Count:</span>
          <span style={{ fontWeight: '600' }}>
            {properties.segment_count?.toLocaleString()}
          </span>
        </div>

        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <span>Total Population:</span>
          <span style={{ fontWeight: '600' }}>
            {properties.total_population?.toLocaleString()}
          </span>
        </div>

        <div
          style={{
            marginTop: '8px',
            paddingTop: '8px',
            borderTop: '1px solid #eee',
          }}
        >
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              marginBottom: '2px',
            }}
          >
            <span>Dominant Segment:</span>
            <span style={{ fontWeight: '600' }}>
              {properties.dominant_segment}
            </span>
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <span>Dominant Family:</span>
            <span style={{ fontWeight: '600' }}>
              {properties.dominant_family}
            </span>
          </div>
        </div>
      </div>,
    );
  }, []);

  // Mouse event handlers - use useCallback to prevent recreation
  const onMouseMove = useCallback(
    (e) => {
      console.log('Mouse move event triggered', e.features);

      if (e.features && e.features.length > 0 && popupRef.current && map) {
        const feature = e.features[0];
        const coordinates = e.lngLat;
        const properties = feature.properties;

        console.log('Feature properties:', properties);

        popupRef.current
          .setLngLat(coordinates)
          .setHTML(getTooltipHTML(properties))
          .addTo(map);

        // Change cursor to pointer
        map.getCanvas().style.cursor = 'pointer';
      }
    },
    [map, getTooltipHTML],
  );

  const onMouseLeave = useCallback(() => {
    console.log('Mouse leave event triggered');
    if (popupRef.current) {
      popupRef.current.remove();
    }
    if (map) {
      map.getCanvas().style.cursor = '';
    }
  }, [map]);

  // Function to clean up existing event listeners
  const cleanupEventListeners = useCallback(() => {
    if (
      currentListenersRef.current.added &&
      currentListenersRef.current.layerId &&
      map
    ) {
      console.log(
        'Cleaning up listeners for:',
        currentListenersRef.current.layerId,
      );
      map.off('mousemove', currentListenersRef.current.layerId, onMouseMove);
      map.off('mouseleave', currentListenersRef.current.layerId, onMouseLeave);
      currentListenersRef.current.added = false;
      currentListenersRef.current.layerId = null;
    }
  }, [map, onMouseMove, onMouseLeave]);

  // Function to add event listeners
  const addEventListeners = useCallback(
    (layerId) => {
      if (!map) return;

      const attemptToAddListeners = (retryCount = 0) => {
        console.log(
          `Attempting to add listeners for: ${layerId} (attempt ${
            retryCount + 1
          })`,
        );

        if (map.getLayer(layerId)) {
          console.log('Layer found, adding listeners');
          map.on('mousemove', layerId, onMouseMove);
          map.on('mouseleave', layerId, onMouseLeave);
          currentListenersRef.current.added = true;
          currentListenersRef.current.layerId = layerId;
          console.log('Event listeners added successfully for:', layerId);
        } else if (retryCount < 10) {
          console.log('Layer not found, retrying in 200ms...');
          setTimeout(() => attemptToAddListeners(retryCount + 1), 200);
        } else {
          console.warn('Failed to find layer after 10 attempts:', layerId);
        }
      };

      attemptToAddListeners();
    },
    [map, onMouseMove, onMouseLeave],
  );

  // Create layer styles
  const fillStyle = {
    id: segment ? `segment-${segment}-fill` : 'segment-fill',
    type: 'fill',
    'source-layer': 'segment_data',
    paint: {
      'fill-color': [
        'interpolate',
        ['linear'],
        ['get', 'segment_percentage'],
        0,
        'rgba(0,0,0,0)',
        10,
        '#f3e5f5',
        20,
        '#e1bee7',
        30,
        '#ce93d8',
        40,
        '#ba68c8',
        50,
        '#ab47bc',
        60,
        '#9c27b0',
        70,
        '#8e24aa',
        80,
        '#7b1fa2',
        100,
        '#4a148c',
      ],
      'fill-opacity': [
        'interpolate',
        ['linear'],
        ['get', 'segment_percentage'],
        0,
        0,
        0.1,
        0.6,
        1.0,
        0.75,
        5.0,
        0.85,
        10.0,
        0.95,
      ],
    },
    filter: ['>', ['get', 'segment_count'], 0],
  };

  const strokeStyle = {
    id: segment ? `segment-${segment}-stroke` : 'segment-stroke',
    type: 'line',
    'source-layer': 'segment_data',
    paint: {
      'line-color': '#666666',
      'line-width': ['interpolate', ['linear'], ['zoom'], 0, 0.5, 10, 1, 14, 2],
      'line-opacity': 0.5,
    },
    filter: ['>', ['get', 'segment_count'], 0],
  };

  const sourceId = 'segment-heatmap-source';

  // US bounds for fitting the map view
  const US_BOUNDS = [
    [-128.0, 20.0],
    [-65.0, 50.0],
  ];

  // Token management
  useEffect(() => {
    if (!map) return;

    const checkForLatestToken = async () => {
      const newToken = await window.spatiallaser.getUserToken('access');
      if (token !== newToken) {
        setToken(newToken);
      }
    };

    checkForLatestToken();
    map.on('movestart', checkForLatestToken);

    return () => {
      map.off('movestart', checkForLatestToken);
    };
  }, [map, token]);

  // Main effect to handle event listeners
  useEffect(() => {
    if (!map || !displaySelectedSegmentHeatmap || !segment || !token) {
      cleanupEventListeners();
      return;
    }

    // Set map theme
    map.fire('map.setThemeOption', {
      payload: { currentMapThemeOption: 'Monochrome' },
    });

    const fillLayerId = `segment-${segment}-fill`;

    // Clean up old listeners first
    cleanupEventListeners();

    // Remove any existing popup
    if (popupRef.current) {
      popupRef.current.remove();
    }

    // Add new listeners after a delay to ensure layer is rendered
    setTimeout(() => {
      addEventListeners(fillLayerId);
    }, 1000);

    return cleanupEventListeners;
  }, [
    map,
    displaySelectedSegmentHeatmap,
    segment,
    token,
    cleanupEventListeners,
    addEventListeners,
  ]);

  // Debug layer existence
  useEffect(() => {
    if (!map || !segment || !displaySelectedSegmentHeatmap || !token) return;

    setTimeout(() => {
      const fillLayerId = `segment-${segment}-fill`;
      const strokeLayerId = `segment-${segment}-stroke`;

      console.log('=== DEBUG INFO ===');
      console.log('Fill layer exists:', !!map.getLayer(fillLayerId));
      console.log('Stroke layer exists:', !!map.getLayer(strokeLayerId));
      console.log('Source exists:', !!map.getSource(sourceId));
      console.log('Token:', token);
      console.log('Segment:', segment);
      console.log('Display heatmap:', displaySelectedSegmentHeatmap);
      console.log('Current listeners:', currentListenersRef.current);

      if (map.getSource(sourceId)) {
        const features = map.querySourceFeatures(sourceId);
        console.log('Features in source:', features.length);
        if (features.length > 0) {
          console.log('Sample feature properties:', features[0].properties);
        }
      }

      const renderedFeatures = map.queryRenderedFeatures(null, {
        layers: [fillLayerId],
      });
      console.log('Rendered features:', renderedFeatures.length);
      console.log('==================');
    }, 2000);
  }, [map, segment, displaySelectedSegmentHeatmap, token]);

  // Don't render if no token, layer not enabled, or no segment selected
  if (!token || !displaySelectedSegmentHeatmap || !segment) {
    return null;
  }

  return (
    <Source
      id={sourceId}
      type="vector"
      tiles={[
        getTileURL(
          serverType,
          token,
          segment,
          selectedSegmentHeatmapMinValue,
          selectedSegmentHeatmapMaxValue,
        ),
      ]}
      key={`${segment}-${token}`} // Force re-render when segment or token changes
    >
      <Layer {...fillStyle} />
      <Layer {...strokeStyle} />
    </Source>
  );
};

export default SegmentOverviewLayer;
