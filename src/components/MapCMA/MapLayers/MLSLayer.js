import { Layer, Source } from '@spatiallaser/map';
import isEmpty from 'lodash.isempty';
import { useCallback, useEffect, useRef, useState } from 'react';
import * as ReactDOM from 'react-dom';
import { renderToString } from 'react-dom/server';
import { useDispatch, useSelector } from 'umi';
import { geojsonTemplate, MAP_LAYER_NAME_BASE } from '../../../constants';
import { getMetroNameForParam } from '../../../utils/geography';
import PropertyDetailPopup from '../../PropertyDetailPopup';
import { locatePropertyHandler, propertyDetailPopup } from '../MapCMA';
import {
  generateGeoJSONData,
  removePriceMarkers,
  setPriceMarkers,
} from '../MapUtility/general';
import { sourceId as sourceIdNationalOperatorLayer } from './NationalOperatorLayer';

export const sourceId = MAP_LAYER_NAME_BASE.mls;

const zoomLevelToShowParcelAVM = 17;
// Define a zoom level threshold for showing price markers
const zoomLevelToShowPriceMarkers = 12;

const circleStyle = {
  id: `${sourceId}LayerCircle`,
  type: 'circle',
  filter: ['!', ['has', 'point_count']],
  paint: {
    'circle-radius': 8,
    'circle-color': '#17c220',
    'circle-stroke-color': '#fff',
    'circle-stroke-width': 2,
  },
};

const symbolStyle = {
  id: `${sourceId}LayerSymbol`,
  type: 'symbol',
  filter: ['!', ['has', 'point_count']],
  layout: {
    'text-field': ['concat', '$', ['get', 'latestPrice']],
    'text-variable-anchor': ['center'],
    'text-justify': 'center',
    'text-radial-offset': 1,
    'text-font': ['Source Sans Pro Bold', 'Open Sans Bold'],
    'text-size': [
      'interpolate',
      ['linear'],
      ['zoom'],
      zoomLevelToShowParcelAVM,
      15,
      22,
      20,
    ],
    'icon-allow-overlap': true,
    'text-allow-overlap': true,
  },
  paint: {
    'text-color': '#fff',
    'text-halo-color': '#000',
    'text-halo-width': 20,
    'text-opacity': 0,
    'icon-opacity': 0,
  },
};

const clusterStyle = {
  id: `${sourceId}LayerClusters`,
  type: 'circle',
  filter: ['has', 'point_count'],
  paint: {
    'circle-color': '#A9BBC0',
    'circle-radius': [
      'interpolate',
      ['linear'],
      ['zoom'],
      0,
      ['max', ['*', ['^', ['get', 'point_count'], 0.5], 0.1], 12],
    ],
    'circle-opacity': 0.75,
    'circle-stroke-width': 1,
    'circle-stroke-color': 'rgba(255,255,255,1)',
  },
};

const clusterSymbolStyle = {
  id: `${sourceId}LayerClustersPointCount`,
  type: 'symbol',
  filter: ['has', 'point_count'],
  layout: {
    'text-font': ['Open Sans Bold'],
    'text-field': '{point_count}',
    'text-size': 14,
    'text-justify': 'auto',
  },
  paint: {
    'text-color': 'rgba(0,0,0,1)',
  },
};

let priceMarkersMLS = {};

export const removeMLSPriceMarkers = () => {
  if (!isEmpty(priceMarkersMLS)) {
    priceMarkersMLS = removePriceMarkers(priceMarkersMLS);
  }
};

export const generateMLSPriceMarkers = (map, currentMLSGeoJSON) => {
  if (currentMLSGeoJSON && currentMLSGeoJSON.features.length > 0) {
    if (!isEmpty(priceMarkersMLS)) {
      priceMarkersMLS = removePriceMarkers(priceMarkersMLS);
    }
    priceMarkersMLS = setPriceMarkers(
      map,
      currentMLSGeoJSON.features,
      'mlsid',
      'latestPrice',
      sourceId,
    );
  }
};

function MLSLayer() {
  const map = useSelector((state) => state.CMA.map);
  const cmaTabKey = useSelector((state) => state.CMA.cmaTabKey);
  const currentMLSPropertiesFiltered = useSelector(
    (state) => state.CMA.currentMLSPropertiesFiltered,
  );
  const cmaTabKeyRef = useRef(cmaTabKey);
  cmaTabKeyRef.current = cmaTabKey;
  const currentMLSGeoJSON = useSelector((state) => state.CMA.currentMLSGeoJSON);
  const searchingMode = useSelector((state) => state.CMA.searchingMode);
  const showPriceMarkers = useSelector((state) => state.CMA.showPriceMarkers);
  const currentMapThemeOption = useSelector(
    (state) => state.CMA.currentMapThemeOption,
  );
  const selectedRowKeysMLSLease = useSelector(
    (state) => state.CMA.selectedRowKeysMLSLease,
  );
  const selectedRowKeysMLSSale = useSelector(
    (state) => state.CMA.selectedRowKeysMLSSale,
  );

  const dispatch = useDispatch();

  const [geojsonData, setGeojsonData] = useState(geojsonTemplate);

  // Create refs to access state variables in callback functions
  const searchingModeRef = useRef(searchingMode);
  const currentMapThemeOptionRef = useRef(currentMapThemeOption);
  const selectedRowKeysMLSLeaseRef = useRef(selectedRowKeysMLSLease);
  const selectedRowKeysMLSSaleRef = useRef(selectedRowKeysMLSSale);
  const mouseOnTopRef = useRef(false);
  const mouseOnTopOfPopupRef = useRef(false);
  const showPriceMarkersRef = useRef(showPriceMarkers);
  const geojsonDataRef = useRef(geojsonData);

  // Update refs when values change
  searchingModeRef.current = searchingMode;
  currentMapThemeOptionRef.current = currentMapThemeOption;
  selectedRowKeysMLSLeaseRef.current = selectedRowKeysMLSLease;
  selectedRowKeysMLSSaleRef.current = selectedRowKeysMLSSale;
  showPriceMarkersRef.current = showPriceMarkers;
  geojsonDataRef.current = geojsonData;

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (!isEmpty(priceMarkersMLS)) {
        priceMarkersMLS = removePriceMarkers(priceMarkersMLS);
      }
    };
  }, []);

  useEffect(() => {
    if (searchingMode === 'Land') {
      if (!isEmpty(priceMarkersMLS)) {
        priceMarkersMLS = removePriceMarkers(priceMarkersMLS);
      }
    }
  }, [searchingMode]);

  const showHidePriceMarkers = useCallback(() => {
    // console.log('showHidePriceMarkers called');

    if (
      map &&
      showPriceMarkersRef.current &&
      searchingModeRef.current !== 'Land' &&
      (cmaTabKeyRef.current === '1' ||
        cmaTabKeyRef.current === 'Comp Insights') && // Show on either tab
      geojsonDataRef.current &&
      geojsonDataRef.current.features.length > 0
    ) {
      // Check current zoom level
      const currentZoom = map.getZoom();
      // console.log('Current zoom level:', currentZoom);

      if (currentZoom >= zoomLevelToShowPriceMarkers) {
        // console.log(
        //   'Zoom level sufficient and tab is valid, showing price markers',
        // );
        // Clean up existing markers first
        if (!isEmpty(priceMarkersMLS)) {
          priceMarkersMLS = removePriceMarkers(priceMarkersMLS);
        }
        // Create new markers
        priceMarkersMLS = setPriceMarkers(
          map,
          geojsonDataRef.current.features,
          'mlsid',
          'latestPrice',
          sourceId,
        );
      } else {
        // console.log('Zoom level too low, removing price markers');
        priceMarkersMLS = removePriceMarkers(priceMarkersMLS);
      }
    } else {
      // console.log(
      //   'Conditions not met, removing price markers. cmaTabKey:',
      //   cmaTabKeyRef.current,
      // );
      priceMarkersMLS = removePriceMarkers(priceMarkersMLS);
    }
  }, [map]);

  useEffect(() => {
    if (!map) return;

    const mouseEnter = (e) => {
      // refer to https://docs.mapbox.com/mapbox-gl-js/example/popup-on-hover/
      const coordinates = e.features[0].geometry.coordinates.slice();

      while (Math.abs(e.lngLat.lng - coordinates[0]) > 180) {
        coordinates[0] += e.lngLat.lng > coordinates[0] ? 360 : -360;
      }

      const mlsProperty = e.features[0].properties;

      let key = mlsProperty.listingkey;
      mouseOnTopRef.current = true;
      mouseOnTopOfPopupRef.current = true;

      if (
        ['sanantonio', 'san antonio'].includes(
          mlsProperty.metro.toLowerCase(),
        ) ||
        getMetroNameForParam(mlsProperty, true) === 'sanantonio'
      ) {
        key = mlsProperty.mlsid;
      }

      dispatch({
        type: 'CMA/getMLSPopupImages',
        payload: {
          key: key,
          city: getMetroNameForParam(mlsProperty, true), // true for using realtrac instead of nashville
        },
      }).then((imageURL) => {
        // console.log(imageURL);
        const htmlText = renderToString(
          <PropertyDetailPopup
            hoverPropertyDetails={{
              ...mlsProperty,
              type: 'MLS',
              MLSPopupImageSrc: imageURL,
              isLeaseMode: searchingModeRef.current === 'Lease',
            }}
          />,
        );

        if (mouseOnTopRef.current) {
          propertyDetailPopup
            .setLngLat(coordinates)
            .setHTML(htmlText)
            .addTo(map);

          const popup = propertyDetailPopup.getElement();
          const locateButton = popup.querySelector('#locatePropertyButton');
          const removeButton = popup.querySelector('#removePropertyButton');

          popup.addEventListener('mouseenter', () => {
            mouseOnTopOfPopupRef.current = true;
          });
          popup.addEventListener('mousemove', () => {
            mouseOnTopOfPopupRef.current = true;
          });
          popup.addEventListener('mouseleave', () => {
            mouseOnTopOfPopupRef.current = false;
            if (propertyDetailPopup.getElement() && !mouseOnTopRef.current) {
              propertyDetailPopup.remove();
            }
          });

          locateButton.addEventListener('click', () => {
            locatePropertyHandler(
              locateButton.dataset.propertyType,
              locateButton.dataset.propertyId,
            );
          });

          removeButton.addEventListener('click', () => {
            if (removeButton.dataset.propertyType != 'MLS') return;

            if (searchingModeRef.current === 'Lease') {
              dispatch({
                type: 'CMA/saveCMAStates',
                payload: {
                  selectedRowKeysMLSLease:
                    selectedRowKeysMLSLeaseRef.current.filter(
                      (id) => id !== removeButton.dataset.propertyId,
                    ),
                },
              });
            } else {
              dispatch({
                type: 'CMA/saveCMAStates',
                payload: {
                  selectedRowKeysMLSSale:
                    selectedRowKeysMLSSaleRef.current.filter(
                      (id) => id !== removeButton.dataset.propertyId,
                    ),
                },
              });
            }
            propertyDetailPopup.remove();
          });
        }
      });
    };

    const mouseMove = (e) => {
      const features = map.queryRenderedFeatures(e.point, {
        layers: [`${sourceId}LayerSymbol`],
      });
      if (features.length > 0) {
        mouseOnTopRef.current = true;
      } else {
        mouseOnTopRef.current = false;
      }
    };

    const mouseLeave = (e) => {
      mouseOnTopRef.current = false;

      if (
        propertyDetailPopup.getElement() &&
        mouseOnTopOfPopupRef.current === false
      ) {
        if (!e.originalEvent || !e.originalEvent.relatedTarget) {
          propertyDetailPopup.remove();
        }
        if (
          e.originalEvent &&
          e.originalEvent.relatedTarget &&
          !e.originalEvent.relatedTarget.classList.contains('mapboxgl-popup')
        ) {
          propertyDetailPopup.remove();
        }
      }
    };

    const styleLoad = () => {
      if (searchingModeRef.current === 'Land') return;
      showHidePriceMarkers();
    };

    // Zoom event handler - key for refreshing markers on zoom
    const zoomEnd = () => {
      // console.log('zoomEnd triggered');
      showHidePriceMarkers();

      // Copy over markers when map style changes
      if (currentMapThemeOptionRef.current === 'Automatic') {
        map.on('style.load', styleLoad);
      }
    };

    // remove then re-create all markert after map changes
    // so only visible markers are created
    const mapIdle = () => {
      if (searchingModeRef.current === 'Land') return;
      showHidePriceMarkers();
    };

    // Set up all event listeners
    map.on('mouseenter', `${sourceId}LayerSymbol`, mouseEnter);
    map.on('mousemove', `${sourceId}LayerSymbol`, mouseMove);
    map.on('mouseleave', `${sourceId}LayerSymbol`, mouseLeave);
    map.on('zoomend', zoomEnd);
    map.on('idle', mapIdle);
    // Clean up on unmount
    return () => {
      map.off('mouseenter', `${sourceId}LayerSymbol`, mouseEnter);
      map.off('mousemove', `${sourceId}LayerSymbol`, mouseMove);
      map.off('mouseleave', `${sourceId}LayerSymbol`, mouseLeave);
      map.off('zoomend', zoomEnd);
      map.off('style.load', styleLoad);
      map.off('idle', mapIdle);
      priceMarkersMLS = removePriceMarkers(priceMarkersMLS);
    };
  }, [map, searchingMode, showHidePriceMarkers]);

  // Update markers when map or geojson data changes
  useEffect(() => {
    if (searchingMode === 'Land') return;
    showHidePriceMarkers();
  }, [
    map,
    currentMLSGeoJSON,
    geojsonData,
    searchingMode,
    showHidePriceMarkers,
  ]);

  // Update markers when showPriceMarkers changes
  useEffect(() => {
    if (searchingMode === 'Land') return;
    showHidePriceMarkers();
  }, [showPriceMarkers, searchingMode, showHidePriceMarkers]);

  // Handle lease data changes
  useEffect(() => {
    // console.log('MLS Layer bug', { cmaTabKey, cmaTabKeyRef });
    if (searchingMode === 'Sale') return;
    priceMarkersMLS = removePriceMarkers(priceMarkersMLS);
    // console.log('MLS Layer Lease', selectedRowKeysMLSLease);
    if (
      selectedRowKeysMLSLease.length > 0 &&
      selectedRowKeysMLSLease[0] != 'undefined'
    ) {
      const geoData = generateGeoJSONData(
        currentMLSPropertiesFiltered,
        selectedRowKeysMLSLease,
        'MLS',
      );
      // console.log('MLS Layer1', geoData);
      setGeojsonData(geoData);
    } else {
      // console.log('MLS Layer empty triggered');
      setGeojsonData(geojsonTemplate);
    }
  }, [currentMLSPropertiesFiltered, selectedRowKeysMLSLease, searchingMode]);
  useEffect(() => {
    if (
      searchingMode === 'Land' ||
      (cmaTabKey !== '1' && cmaTabKey !== 'Comp Insights')
    ) {
      // Remove markers if not on tab 1 or Comp Insights
      priceMarkersMLS = removePriceMarkers(priceMarkersMLS);
      return;
    }
    showHidePriceMarkers();
  }, [cmaTabKey, searchingMode, showHidePriceMarkers]);
  // Handle sale data changes
  useEffect(() => {
    if (searchingMode === 'Lease') return;
    priceMarkersMLS = removePriceMarkers(priceMarkersMLS);
    // console.log('MLS Layer Sale', selectedRowKeysMLSSale);
    if (
      selectedRowKeysMLSSale.length > 0 &&
      selectedRowKeysMLSSale[0] != 'undefined'
    ) {
      const geoData = generateGeoJSONData(
        currentMLSPropertiesFiltered,
        selectedRowKeysMLSSale,
        'MLS',
      );
      // console.log('MLS Layer1', geoData);
      setGeojsonData(geoData);
    } else {
      // console.log('MLS Layer empty triggered');
      setGeojsonData(geojsonTemplate);
    }
  }, [currentMLSPropertiesFiltered, selectedRowKeysMLSSale, searchingMode]);

  // Debug log when geojsonData changes
  useEffect(() => {
    // console.log('MLS Layer', geojsonData);
  }, [geojsonData]);

  // Update markers when geojsonData changes
  useEffect(() => {
    if (searchingMode === 'Land') return;
    showHidePriceMarkers();
  }, [geojsonData, searchingMode, showHidePriceMarkers]);

  return cmaTabKey === '1' || cmaTabKey === 'Comp Insights' ? (
    <Source id={sourceId} type="geojson" data={geojsonData}>
      <Layer {...circleStyle} />
      <Layer {...symbolStyle} />
      <Layer {...clusterStyle} />
      <Layer {...clusterSymbolStyle} />
    </Source>
  ) : null;
}

export default MLSLayer;
