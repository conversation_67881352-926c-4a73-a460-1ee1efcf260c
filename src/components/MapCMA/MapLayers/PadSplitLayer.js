import { Layer, Source } from '@spatiallaser/map';
import isEmpty from 'lodash.isempty';
import { useCallback, useEffect, useRef, useState } from 'react';
import { renderToString } from 'react-dom/server';
import { useDispatch, useSelector } from 'umi';
import { geojsonTemplate, MAP_LAYER_NAME_BASE } from '../../../constants';
import PropertyDetailPopup from '../../PropertyDetailPopup';
import { locatePropertyHandler, propertyDetailPopup } from '../MapCMA';
import {
  generateGeoJSONData,
  removePriceMarkers,
  setPriceMarkers,
} from '../MapUtility/general';

const sourceId = MAP_LAYER_NAME_BASE.padSplit;

const circleStyle = {
  id: `${sourceId}Layer`,
  type: 'circle',
  paint: {
    'circle-radius': 8,
    'circle-color': '#16457e',
    'circle-stroke-color': '#fff',
    'circle-stroke-width': 2,
  },
};

let priceMarkersPadSplit = {};

export const removePadSplitPriceMarkers = () => {
  if (!isEmpty(priceMarkersPadSplit)) {
    priceMarkersPadSplit = removePriceMarkers(priceMarkersPadSplit);
  }
};

export const generatePadSplitPriceMarkers = (map, currentPadSplitGeoJSON) => {
  if (currentPadSplitGeoJSON && currentPadSplitGeoJSON.features.length > 0) {
    if (!isEmpty(priceMarkersPadSplit)) {
      priceMarkersPadSplit = removePriceMarkers(priceMarkersPadSplit);
    }
    priceMarkersPadSplit = setPriceMarkers(
      map,
      currentPadSplitGeoJSON.features,
      'property_id',
      'currentAvgRoomsPrivateBathPrice+currentAvgRoomsSharedBathPrice',
      sourceId,
    );
  }
};

function PadSplitLayer() {
  const map = useSelector((state) => state.CMA.map);
  const searchingMode = useSelector((state) => state.CMA.searchingMode);
  const selectedRowKeysPadSplit = useSelector(
    (state) => state.CMA.selectedRowKeysPadSplit,
  );
  const currentPadSplitPropertiesFiltered = useSelector(
    (state) => state.CMA.currentPadSplitPropertiesFiltered,
  );
  const currentPadSplitGeoJSON = useSelector(
    (state) => state.CMA.currentPadSplitGeoJSON,
  );
  const showPriceMarkers = useSelector((state) => state.CMA.showPriceMarkers);
  const currentMapThemeOption = useSelector(
    (state) => state.CMA.currentMapThemeOption,
  );

  const dispatch = useDispatch();

  const [geojsonData, setGeojsonData] = useState(geojsonTemplate);

  const showPriceMarkersRef = useRef(showPriceMarkers);
  showPriceMarkersRef.current = showPriceMarkers;
  const geojsonDataRef = useRef(geojsonData);
  geojsonDataRef.current = geojsonData;
  const currentMapThemeOptionRef = useRef(currentMapThemeOption);
  currentMapThemeOptionRef.current = currentMapThemeOption;
  const mouseOnTopRef = useRef(false);
  const mouseOnTopOfPopupRef = useRef(false);
  const selectedRowKeysPadSplitRef = useRef(selectedRowKeysPadSplit);
  selectedRowKeysPadSplitRef.current = selectedRowKeysPadSplit;

  const showHidePriceMarkers = useCallback(() => {
    if (
      map &&
      showPriceMarkersRef.current &&
      geojsonDataRef.current &&
      geojsonDataRef.current.features.length > 0
    ) {
      if (!isEmpty(priceMarkersPadSplit)) {
        priceMarkersPadSplit = removePriceMarkers(priceMarkersPadSplit);
      }
      priceMarkersPadSplit = setPriceMarkers(
        map,
        geojsonDataRef.current.features,
        'property_id',
        'rooms_with_private_bath_price+rooms_with_shared_bath_price',
        sourceId,
      );
    } else {
      priceMarkersPadSplit = removePriceMarkers(priceMarkersPadSplit);
    }
  }, [map, showPriceMarkers, geojsonData]);

  useEffect(() => {
    if (!map) return;

    const mouseEnter = (e) => {
      if (propertyDetailPopup.getElement() && mouseOnTopRef.current) {
        return;
      }
      const coordinates = e.features[0].geometry.coordinates.slice();
      const htmlText = renderToString(
        <PropertyDetailPopup
          hoverPropertyDetails={{
            ...e.features[0].properties,
            type: sourceId,
          }}
        />,
      );
      mouseOnTopRef.current = true;

      propertyDetailPopup.setLngLat(coordinates).setHTML(htmlText).addTo(map);

      const popup = propertyDetailPopup.getElement();
      const locateButton = popup.querySelector('#locatePropertyButton');
      const removeButton = popup.querySelector('#removePropertyButton');

      popup.addEventListener('mouseenter', () => {
        mouseOnTopOfPopupRef.current = true;
      });
      popup.addEventListener('mousemove', () => {
        mouseOnTopOfPopupRef.current = true;
      });
      popup.addEventListener('mouseleave', () => {
        mouseOnTopOfPopupRef.current = false;
        if (
          propertyDetailPopup.getElement() &&
          mouseOnTopRef.current === false
        ) {
          propertyDetailPopup.remove();
        }
      });

      locateButton.addEventListener('click', () => {
        locatePropertyHandler(
          locateButton.dataset.property_type,
          locateButton.dataset.property_id,
        );
      });

      removeButton.addEventListener('click', () => {
        if (removeButton.dataset.property_type != sourceId) return;

        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            selectedRowKeysPadSplit: selectedRowKeysPadSplitRef.current.filter(
              (id) => id !== removeButton.dataset.property_id,
            ),
          },
        });
        propertyDetailPopup.remove();
      });
    };

    const mouseLeave = (e) => {
      mouseOnTopRef.current = false;
      if (
        propertyDetailPopup.getElement() &&
        mouseOnTopOfPopupRef.current === false
      ) {
        if (!e.originalEvent || !e.originalEvent.relatedTarget) {
          propertyDetailPopup.remove();
        }
        if (
          e.originalEvent &&
          e.originalEvent.relatedTarget &&
          !e.originalEvent.relatedTarget.classList.contains('mapboxgl-popup')
        ) {
          propertyDetailPopup.remove();
        }
      }
    };

    const styleLoad = () => {
      showHidePriceMarkers();
    };

    const zoomEnd = () => {
      // Copy over markers when map style changes
      if (currentMapThemeOptionRef.current === 'Automatic') {
        map.on('style.load', styleLoad);
      }
    };

    // map.on('mousemove', (e) => {
    //   const features = map.queryRenderedFeatures(e.point, {
    //     layers: [`${sourceId}Layer`],
    //   });

    //   if (features.length > 0) {
    //     console.log(features[0]);
    //   }

    //   console.log(e);
    // });
    map.on('mouseenter', `${sourceId}Layer`, mouseEnter);
    map.on('mouseleave', `${sourceId}Layer`, mouseLeave);
    map.on('zoomend', zoomEnd);
    return () => {
      map.off('mouseenter', `${sourceId}Layer`, mouseEnter);
      map.off('mouseleave', `${sourceId}Layer`, mouseLeave);
      map.off('zoomend', zoomEnd);
      map.off('style.load', styleLoad);
    };
  }, [map]);

  useEffect(() => {
    showHidePriceMarkers();
  }, [currentPadSplitGeoJSON]);

  useEffect(() => {
    showHidePriceMarkers();
  }, [showPriceMarkers]);

  useEffect(() => {
    if (
      selectedRowKeysPadSplit &&
      selectedRowKeysPadSplit.length > 0 &&
      searchingMode === 'Lease'
    ) {
      const geoData = generateGeoJSONData(
        currentPadSplitPropertiesFiltered,
        selectedRowKeysPadSplit,
        'PadSplit',
      );
      console.log('tesrv2 geoData', geoData);
      setGeojsonData(geoData);
    } else {
      setGeojsonData(geojsonTemplate);
    }
  }, [
    currentPadSplitPropertiesFiltered,
    selectedRowKeysPadSplit,
    searchingMode,
  ]);

  useEffect(() => {
    showHidePriceMarkers();
  }, [geojsonData]);

  return (
    <Source id={sourceId} type="geojson" data={geojsonData}>
      <Layer {...circleStyle} />
    </Source>
  );
}

export default PadSplitLayer;
