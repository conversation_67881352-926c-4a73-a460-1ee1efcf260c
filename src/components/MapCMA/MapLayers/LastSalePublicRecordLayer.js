import { Layer, Source } from '@spatiallaser/map';
import isEmpty from 'lodash.isempty';
import mapboxgl from 'mapbox-gl';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import { renderToString } from 'react-dom/server';
import { useSelector } from 'umi';
import { geojsonTemplate } from '../../../constants';
import { formatCurrency } from '../../../utils/lastSalePublicRecord';
import { removePriceMarkers, setPriceMarkers } from '../MapUtility/general';
const sourceId = 'publicRecord';
const NON_DISCLOSURE_STATES = [
  'AK',
  'ID',
  'KS',
  'MS',
  'LA',
  'WY',
  'UT',
  'ND',
  'NM',
  'MT',
  'TX',
];

let priceMarkersPR = {};
const zoomLevelToShowParcelAVM = 17;
export const removePRPriceMarkers = () => {
  if (!isEmpty(priceMarkersPR)) {
    priceMarkersPR = removePriceMarkers(priceMarkersPR);
  }
};
const symbolStyle = {
  id: `${sourceId}LayerSymbol`,
  type: 'symbol',
  filter: ['!', ['has', 'point_count']],
  layout: {
    'text-field': ['concat', '$', ['get', 'last_sale_price']],
    'text-variable-anchor': ['center'],
    'text-justify': 'center',
    'text-radial-offset': 1,
    // On how to use custom fonts in Mapbox, see: https://github.com/mapbox/mapbox-gl-js/issues/6666
    'text-font': [
      'Source Sans Pro Bold',
      'Open Sans Bold', // fallback font
    ],
    'text-size': [
      'interpolate',
      ['linear'],
      ['zoom'],
      zoomLevelToShowParcelAVM,
      15,
      22,
      20,
    ],
    'icon-allow-overlap': true,
    'text-allow-overlap': true,
  },
  paint: {
    'text-color': '#fff',
    'text-halo-color': '#000',
    'text-halo-width': 20, // use halo instead of an icon for text background
    // 'background-color': "#000"
    'text-opacity': 0,
    'icon-opacity': 0,
  },
};
const clusterStyle = {
  id: `${sourceId}LayerClusters`,
  type: 'circle',
  filter: ['has', 'point_count'],
  paint: {
    'circle-color': '#A9BBC0',
    // 'circle-color': '#0d1738',
    'circle-radius': [
      'interpolate',
      ['linear'],
      ['zoom'],
      0,
      ['max', ['*', ['^', ['get', 'point_count'], 0.5], 0.1], 12],
      // 10.4, ["max", ["*", ['^', ["get", "point_count"], 0.5], 1], 12],
      // zoomLevelToShowPriceMarkers, ["max", ["*", ['^', ["get", "point_count"], 0.5], 4], 12]
    ],
    'circle-opacity': 0.75,
    'circle-stroke-width': 1,
    'circle-stroke-color': 'rgba(255,255,255,1)',
  },
};

const clusterSymbolStyle = {
  id: `${sourceId}LayerClustersPointCount`,
  type: 'symbol',
  filter: ['has', 'point_count'],
  layout: {
    'text-font': ['Open Sans Bold'],
    'text-field': '{point_count}',
    'text-size': 14,
    'text-justify': 'auto',
  },
  paint: {
    // 'text-color': 'rgba(255,255,255,1)',
    'text-color': 'rgba(0,0,0,1)',
    // 'text-color': '#022386',
  },
};
export const generatePRPriceMarkers = (map, GeoJSON) => {
  console.log('testv5 GEOJson', GeoJSON);
  if (GeoJSON && GeoJSON.features.length > 0) {
    if (!isEmpty(priceMarkersPR)) {
      priceMarkersPR = removePriceMarkers(priceMarkersPR);
    }
    priceMarkersPR = setPriceMarkers(
      map,
      GeoJSON.features,
      'key',
      'last_sale_price',
      sourceId,
    );
  }
};
function convertToGeoJSONFormat(listOfObjects, listOfKeys) {
  console.log('listOfObjects', listOfObjects);
  const filteredObjects = listOfObjects.filter((item) =>
    listOfKeys.includes(item.key),
  );

  return filteredObjects.map((item) => {
    const { geom, ...properties } = item;
    return {
      type: 'Feature',
      geometry: geom,
      properties,
    };
  });
}

function PublicRecordTooltip({ feature }) {
  const properties = feature.properties;
  console.log('testv5 state', properties);
  const disclosure = !NON_DISCLOSURE_STATES.includes(properties.state);

  return (
    <div
      style={{
        backgroundColor: 'white',
        padding: '10px',
        fontSize: '14px',
        zIndex: '999',
      }}
    >
      <div>
        <strong>Address:</strong> {properties.address}
      </div>
      <div>
        <strong>Sqft:</strong>{' '}
        {new Intl.NumberFormat('en-US').format(properties.sqft)}
      </div>
      <div>
        <span>
          <strong>Bd:</strong> {properties.bedroom}
        </span>{' '}
        |{' '}
        <span>
          <strong>Ba:</strong> {properties.bathroom}
        </span>
      </div>
      {disclosure && (
        <div>
          <strong>Last Sale Price:</strong>{' '}
          {formatCurrency(properties.last_sale_price)}
        </div>
      )}

      <div>
        <strong>Last Sale Date:</strong> {properties.last_sale_date}
      </div>
      <div>
        <strong>Type:</strong> {properties.propertysubtype}
      </div>
    </div>
  );
}

function PublicRecordLayer() {
  const [geoJson, setGeoJson] = useState(geojsonTemplate);
  const map = useSelector((state) => state.CMA.map);
  const showPriceMarkers = useSelector((state) => state.CMA.showPriceMarkers);
  const lastSalePublicRecordDataForRender = useSelector(
    (state) => state.CMA.lastSalePublicRecordDataForRender,
  );
  const lastSalePublicRecordSelectedRowKey = useSelector(
    (state) => state.CMA.lastSalePublicRecordSelectedRowKey,
  );
  const lastSalePublicRecordHover = useSelector(
    (state) => state.CMA.lastSalePublicRecordHover,
  );
  const tooltipRef = useRef(new mapboxgl.Popup({ offset: 15 }));
  const showHidePriceMarkers = useCallback(() => {
    if (
      map &&
      showPriceMarkers &&
      geoJson &&
      geoJson.features.length > 0 &&
      !NON_DISCLOSURE_STATES.includes(geoJson.features[0].properties.state)
    ) {
      if (!isEmpty(priceMarkersPR)) {
        priceMarkersPR = removePriceMarkers(priceMarkersPR);
      }
      priceMarkersPR = setPriceMarkers(
        map,
        geoJson.features,
        'key',
        'last_sale_price',
        sourceId,
      );
    } else {
      priceMarkersPR = removePriceMarkers(priceMarkersPR);
    }
  }, [map, showPriceMarkers, geoJson]);

  useEffect(() => {
    showHidePriceMarkers();
  }, [map, geoJson]);

  useEffect(() => {
    showHidePriceMarkers();
  }, [showPriceMarkers]);

  useEffect(() => {
    const newGeoJson = {
      ...geojsonTemplate,
      features: convertToGeoJSONFormat(
        lastSalePublicRecordDataForRender,
        lastSalePublicRecordSelectedRowKey,
      ),
    };
    setGeoJson(newGeoJson);
  }, [lastSalePublicRecordDataForRender, lastSalePublicRecordSelectedRowKey]);

  const showTooltip = (e) => {
    const feature = e.features[0];
    const coordinates = feature.geometry.coordinates.slice();
    console.log('test coordinates', coordinates);
    const placeholder = document.createElement('div');
    ReactDOM.render(<PublicRecordTooltip feature={feature} />, placeholder);

    tooltipRef.current
      .setLngLat(coordinates)
      .setDOMContent(placeholder.firstChild)
      .addTo(map);
  };
  const hideTooltip = () => {
    tooltipRef.current.remove();
  };

  useEffect(() => {
    if (!map) return;

    map.on('mouseenter', `${sourceId}LayerCircle`, showTooltip);
    map.on('mouseleave', `${sourceId}LayerCircle`, hideTooltip);

    return () => {
      if (map) {
        map.off('mouseenter', `${sourceId}LayerCircle`, showTooltip);
        map.off('mouseleave', `${sourceId}LayerCircle`, hideTooltip);
      }
    };
  }, [map, geoJson]);
  useEffect(() => {
    if (!map) return;

    if (lastSalePublicRecordHover) {
      const coordinates = lastSalePublicRecordHover.geom.coordinates.slice();
      const feature = {
        properties: lastSalePublicRecordHover,
        geometry: lastSalePublicRecordHover.geom,
      };
      const htmlString = renderToString(
        <PublicRecordTooltip feature={feature} />,
      );
      const placeholder = document.createElement('div');
      placeholder.innerHTML = htmlString;
      tooltipRef.current
        .setLngLat(coordinates)
        .setDOMContent(placeholder)
        .addTo(map);
    } else {
      // Hide the tooltip if there's no data to show
      tooltipRef.current.remove();
    }
  }, [lastSalePublicRecordHover, map]);

  const circleStyle = {
    id: `${sourceId}LayerCircle`,
    type: 'circle',
    filter: ['!', ['has', 'point_count']],
    paint: {
      'circle-radius': 8,
      'circle-color': '#39a39c',
      'circle-stroke-color': '#fff',
      'circle-stroke-width': 2,
    },
  };

  return (
    <Source id={sourceId} type="geojson" data={geoJson}>
      <Layer {...circleStyle} />
      <Layer {...symbolStyle} />
      <Layer {...clusterStyle} />
      <Layer {...clusterSymbolStyle} />
    </Source>
  );
}

export default PublicRecordLayer;
