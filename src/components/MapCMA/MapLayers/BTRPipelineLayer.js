import { Layer, Source } from '@spatiallaser/map';
import mapboxgl from 'mapbox-gl';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import { renderToString } from 'react-dom/server';
import { IoConstructSharp } from 'react-icons/io5';
import { useSelector } from 'umi';
import constructionIcon from '../../../assets/images/mapbox/marker/support.png';
import { geojsonTemplate } from '../../../constants';
import { transformProjectsList } from '../../ResultTable/Pipeline/BTRPipeLine/util';
const sourceId = 'btr-pipeline';
let circleStyle = {
  id: `${sourceId}LayerCircle`,
  type: 'circle',
  filter: ['!', ['has', 'point_count']],
  // use <IoConstructSharp /> instead of circle
  paint: {
    'circle-radius': 6,
    'circle-color': '#21ffc0',
    'circle-stroke-color': '#000',
    'circle-stroke-width': 1,
  },
};
function convertToGeoJSONFormat(listOfObjects) {
  return listOfObjects.map((item) => {
    const { geom, ...properties } = item;
    return {
      type: 'Feature',
      geometry: geom,
      properties,
    };
  });
}

function BTRPipeLineTooltip({ feature }) {
  const properties = feature.properties;
  console.log('BTRPipeLineTooltip', properties);

  return (
    <div
      style={{
        backgroundColor: 'white',
        padding: '10px',
        fontSize: '12px',
        zIndex: '999',
        width: '200px',
      }}
    >
      <div>
        <strong>{properties.title}</strong>
      </div>
      <div>
        <strong>Location:</strong> {properties.locationinfo}, {properties.city},{' '}
        {properties.state}, {properties.postalcode}
      </div>
      <div>
        <strong>Stage:</strong> {properties.stage}
      </div>
      <div>
        <strong>Units Count:</strong> {properties.unitscount}
      </div>

      <div>
        <strong>Project Type:</strong> {properties.projecttype}
      </div>
      <div>
        <strong>Submitted Date:</strong> {properties.submitteddate}
      </div>
      <div>
        <strong>Updated Date:</strong> {properties.updateddate}
      </div>
    </div>
  );
}

function BTRPipelineLayer() {
  const [geoJson, setGeoJson] = useState(geojsonTemplate);
  const map = useSelector((state) => state.CMA.map);
  const BTRPipeLineData = useSelector((state) => state.CMA.BTRPipeLineData);
  const BTRPipeLineHover = useSelector((state) => state.CMA.BTRPipeLineHover);
  const BTRPipeLineSelectedRowKey = useSelector(
    (state) => state.CMA.BTRPipeLineSelectedRowKey,
  );
  const tooltipRef = useRef(new mapboxgl.Popup({ offset: 15 }));

  useEffect(() => {
    const filtered = BTRPipeLineData.filter((data) =>
      BTRPipeLineSelectedRowKey.includes(data.projectid),
    );
    const newGeoJson = {
      ...geojsonTemplate,
      features: convertToGeoJSONFormat(filtered),
    };
    setGeoJson(newGeoJson);
    console.log('newGeoJson', newGeoJson);
  }, [BTRPipeLineData, BTRPipeLineSelectedRowKey]);

  const showTooltip = (e) => {
    const feature = e.features[0];
    console.log('tt', feature);
    const coordinates = feature.geometry.coordinates.slice();
    console.log('test coordinates', coordinates);
    const placeholder = document.createElement('div');
    ReactDOM.render(<BTRPipeLineTooltip feature={feature} />, placeholder);

    tooltipRef.current
      .setLngLat(coordinates)
      .setDOMContent(placeholder.firstChild)
      .addTo(map);
  };
  const hideTooltip = () => {
    tooltipRef.current.remove();
  };

  useEffect(() => {
    if (!map) return;

    map.on('mouseenter', `${sourceId}LayerCircle`, showTooltip);
    map.on('mouseleave', `${sourceId}LayerCircle`, hideTooltip);

    return () => {
      if (map) {
        map.off('mouseenter', `${sourceId}LayerCircle`, showTooltip);
        map.off('mouseleave', `${sourceId}LayerCircle`, hideTooltip);
      }
    };
  }, [map, geoJson]);
  useEffect(() => {
    if (!map) return;

    const imgId = 'building-permit-icon';
    map.loadImage(constructionIcon, (error, image) => {
      if (error) throw error;
      if (!map.hasImage(imgId)) {
        map.addImage(imgId, image);
      }
    });

    if (BTRPipeLineHover) {
      console.log('BTRPipeLineHover', BTRPipeLineHover);
      const coordinates = BTRPipeLineHover.geom.coordinates.slice();
      const feature = {
        properties: BTRPipeLineHover,
        geometry: BTRPipeLineHover.geom,
      };
      const htmlString = renderToString(
        <BTRPipeLineTooltip feature={feature} />,
      );
      const placeholder = document.createElement('div');
      placeholder.innerHTML = htmlString;
      tooltipRef.current
        .setLngLat(coordinates)
        .setDOMContent(placeholder)
        .addTo(map);
    } else {
      // Hide the tooltip if there's no data to show
      tooltipRef.current.remove();
    }
  }, [BTRPipeLineHover, map]);

  //  circleStyle = {
  //   id: `${sourceId}LayerCircle`,
  //   type: 'symbol',
  //   filter: ['!', ['has', 'point_count']],
  //   // use <IoConstructSharp /> instead of circle
  //   layout: {
  //     'icon-image': 'building-permit-icon', // Use the custom icon
  //     'icon-size': 0.07, // Adjust the icon size as needed
  //     'icon-allow-overlap': true, // Allow icons to overlap
  //   },
  // };

  return (
    <Source id={sourceId} type="geojson" data={geoJson}>
      <Layer {...circleStyle} />
      {/* <Layer {...symbolStyle} />
      <Layer {...clusterStyle} />
      <Layer {...clusterSymbolStyle} /> */}
    </Source>
  );
}

export default BTRPipelineLayer;
