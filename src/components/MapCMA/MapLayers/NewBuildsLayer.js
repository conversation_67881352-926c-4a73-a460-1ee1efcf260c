import { Layer, Source } from '@spatiallaser/map';
import isEmpty from 'lodash.isempty';
import { useCallback, useEffect, useRef, useState } from 'react';
import { renderToString } from 'react-dom/server';
import { useDispatch, useSelector } from 'umi';
import { MAP_LAYER_NAME_BASE, geojsonTemplate } from '../../../constants';
import { formatter } from '../../../utils/money';
import PropertyDetailPopup from '../../PropertyDetailPopup';
import { locatePropertyHandler, propertyDetailPopup } from '../MapCMA';
import {
  generateGeoJSONData,
  removePriceMarkers,
  setPriceMarkers,
} from '../MapUtility/general';

const sourceId = MAP_LAYER_NAME_BASE.newbuilds;

const circleStyle = {
  id: `${sourceId}Layer`,
  type: 'circle',
  paint: {
    'circle-radius': 8,
    'circle-color': '#ED6041',
    'circle-stroke-color': '#fff',
    'circle-stroke-width': 2,
  },
};

let priceMarkers = {};

export const removeHotPadsPriceMarkers = () => {
  if (!isEmpty(priceMarkers)) {
    priceMarkers = removePriceMarkers(priceMarkers);
  }
};

export const generateNewBuildPriceMarkers = (map, geojson) => {
  if (geojson && geojson.features.length > 0) {
    if (!isEmpty(priceMarkers)) {
      priceMarkers = removePriceMarkers(priceMarkers);
    }
    priceMarkers = setPriceMarkers(
      map,
      geojson.features,
      'base_id',
      'price',
      sourceId,
    );
  }
};

function NewBuildsLayer() {
  const map = useSelector((state) => state.CMA.map);
  const selectedRowKeysNewBuilds = useSelector(
    (state) => state.CMA.selectedRowKeysNewBuilds,
  );
  const currentNewBuildsProperties = useSelector(
    (state) => state.CMA.currentNewBuildsProperties,
  );
  const showPriceMarkers = useSelector((state) => state.CMA.showPriceMarkers);
  const currentMapThemeOption = useSelector(
    (state) => state.CMA.currentMapThemeOption,
  );

  const dispatch = useDispatch();

  const [geojsonData, setGeojsonData] = useState(geojsonTemplate);

  const showPriceMarkersRef = useRef(showPriceMarkers);
  showPriceMarkersRef.current = showPriceMarkers;
  const geojsonDataRef = useRef(geojsonData);
  geojsonDataRef.current = geojsonData;
  const currentMapThemeOptionRef = useRef(currentMapThemeOption);
  currentMapThemeOptionRef.current = currentMapThemeOption;
  const mouseOnTopRef = useRef(false);
  const mouseOnTopOfPopupRef = useRef(false);
  const selectedRowKeysNewBuildsRef = useRef(selectedRowKeysNewBuilds);
  selectedRowKeysNewBuildsRef.current = selectedRowKeysNewBuilds;

  const showHidePriceMarkers = useCallback(() => {
    if (
      map &&
      showPriceMarkersRef.current &&
      geojsonDataRef.current &&
      geojsonDataRef.current.features.length > 0
    ) {
      if (!isEmpty(priceMarkers)) {
        priceMarkers = removePriceMarkers(priceMarkers);
      }
      priceMarkers = setPriceMarkers(
        map,
        geojsonDataRef.current.features,
        'base_id',
        'price',
        sourceId,
      );
    } else {
      priceMarkers = removePriceMarkers(priceMarkers);
    }
  }, [map, showPriceMarkers, geojsonData]);

  useEffect(() => {
    if (!map) return;

    const mouseEnter = (e) => {
      e.originalEvent.preventDefault();
      if (propertyDetailPopup.getElement() && mouseOnTopRef.current) {
        return;
      }
      const coordinates = e.features[0].geometry.coordinates.slice();

      const htmlText = renderToString(
        <PropertyDetailPopup
          hoverPropertyDetails={{
            ...e.features[0].properties,
            type: sourceId,
          }}
        />,
      );

      mouseOnTopRef.current = true;

      propertyDetailPopup.setLngLat(coordinates).setHTML(htmlText).addTo(map);

      const popup = propertyDetailPopup.getElement();
      const locateButton = popup.querySelector('#locatePropertyButton');
      const removeButton = popup.querySelector('#removePropertyButton');

      popup.addEventListener('mouseenter', () => {
        mouseOnTopOfPopupRef.current = true;
      });
      popup.addEventListener('mousemove', () => {
        mouseOnTopOfPopupRef.current = true;
      });
      popup.addEventListener('mouseleave', () => {
        mouseOnTopOfPopupRef.current = false;
        if (
          propertyDetailPopup.getElement() &&
          mouseOnTopRef.current === false
        ) {
          propertyDetailPopup.remove();
        }
      });

      locateButton.addEventListener('click', () => {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            locateNewBuildId: locateButton.dataset.propertyId,
          },
        });
        // locatePropertyHandler(
        //   locateButton.dataset.propertyType,
        //   locateButton.dataset.propertyId,
        // );
      });

      removeButton.addEventListener('click', () => {
        if (removeButton.dataset.propertyType != sourceId) return;

        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            selectedRowKeysNewBuilds:
              selectedRowKeysNewBuildsRef.current.filter(
                (id) => id !== removeButton.dataset.propertyId,
              ),
          },
        });
        propertyDetailPopup.remove();
      });
    };

    const mouseLeave = (e) => {
      mouseOnTopRef.current = false;
      if (
        propertyDetailPopup.getElement() &&
        mouseOnTopOfPopupRef.current === false
      ) {
        if (!e.originalEvent || !e.originalEvent.relatedTarget) {
          propertyDetailPopup.remove();
        }
        if (
          e.originalEvent &&
          e.originalEvent.relatedTarget &&
          !e.originalEvent.relatedTarget.classList.contains('mapboxgl-popup')
        ) {
          propertyDetailPopup.remove();
        }
      }
    };

    const styleLoad = () => {
      showHidePriceMarkers();
    };

    const zoomEnd = () => {
      // Copy over markers when map style changes
      if (currentMapThemeOptionRef.current === 'Automatic') {
        map.on('style.load', styleLoad);
      }
    };

    map.on('mouseenter', `${sourceId}Layer`, mouseEnter);
    map.on('mouseleave', `${sourceId}Layer`, mouseLeave);
    map.on('zoomend', zoomEnd);
    return () => {
      map.off('mouseenter', `${sourceId}Layer`, mouseEnter);
      map.off('mouseleave', `${sourceId}Layer`, mouseLeave);
      map.off('zoomend', zoomEnd);
      map.off('style.load', styleLoad);
      priceMarkers = removePriceMarkers(priceMarkers);
    };
  }, [map]);

  // useEffect(() => {
  //   showHidePriceMarkers();
  // }, [currentHotPadsGeoJSON]);

  useEffect(() => {
    showHidePriceMarkers();
  }, [showPriceMarkers]);

  useEffect(() => {
    if (selectedRowKeysNewBuilds.length > 0) {
      const geoData = generateGeoJSONData(
        currentNewBuildsProperties,
        selectedRowKeysNewBuilds,
        'SFR',
      );
      setGeojsonData(geoData);
    } else {
      setGeojsonData(geojsonTemplate);
    }
  }, [selectedRowKeysNewBuilds]);

  useEffect(() => {
    showHidePriceMarkers();
  }, [geojsonData]);

  return (
    <Source id={sourceId} type="geojson" data={geojsonData}>
      <Layer {...circleStyle} />
    </Source>
  );
}

export default NewBuildsLayer;
