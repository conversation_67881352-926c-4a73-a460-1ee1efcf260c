import { Layer, Source } from '@spatiallaser/map';
import mapboxgl from 'mapbox-gl';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import { renderToString } from 'react-dom/server';
import { IoConstructSharp } from 'react-icons/io5';
import { useSelector } from 'umi';
import constructionIcon from '../../../assets/images/mapbox/marker/support.png';
import { geojsonTemplate } from '../../../constants';
import { getAllBTRPipeline } from '../../../services/data';
import { transformProjectsList } from '../../ResultTable/Pipeline/BTRPipeLine/util';
const sourceId = 'btr-pipeline-ld';
let circleStyle = {
  id: `${sourceId}LayerCircle`,
  type: 'circle',
  filter: ['!', ['has', 'point_count']],
  // use <IoConstructSharp /> instead of circle
  paint: {
    'circle-radius': 6,
    'circle-color': '#21ffc0',
    'circle-stroke-color': '#000',
    'circle-stroke-width': 1,
  },
};
function convertToGeoJSONFormat(listOfObjects) {
  return transformProjectsList(listOfObjects).map((item) => {
    const { geom, ...properties } = item;
    return {
      type: 'Feature',
      geometry: geom,
      properties,
    };
  });
}

function LDBTRPipeLineTooltip({ feature }) {
  const properties = feature.properties;
  console.log('BTRPipeLineTooltip', properties);

  return (
    <div
      style={{
        backgroundColor: 'white',
        padding: '10px',
        fontSize: '12px',
        zIndex: '999',
        width: '200px',
      }}
    >
      <div>
        <strong>{properties.title}</strong>
      </div>
      <div>
        <strong>Location:</strong> {properties.locationinfo}, {properties.city},{' '}
        {properties.state}, {properties.postalcode}
      </div>
      <div>
        <strong>Stage:</strong> {properties.stage}
      </div>
      <div>
        <strong>Units Count:</strong> {properties.unitscount}
      </div>

      <div>
        <strong>Project Type:</strong> {properties.projecttype}
      </div>
      <div>
        <strong>Submitted Date:</strong> {properties.submitteddate}
      </div>
      <div>
        <strong>Updated Date:</strong> {properties.updateddate}
      </div>
    </div>
  );
}

function LandDevBTRPipeLineLayer() {
  const [data, setData] = useState(null);
  const [geoJson, setGeoJson] = useState(geojsonTemplate);
  const map = useSelector((state) => state.CMA.map);
  const currentMapLayerOptions = useSelector(
    (state) => state.CMA.currentMapLayerOptions,
  );

  const tooltipRef = useRef(new mapboxgl.Popup({ offset: 15 }));

  useEffect(() => {
    const fetchData = async () => {
      const result = await getAllBTRPipeline();
      if (result) {
        setData(result);
        const newGeoJson = {
          ...geojsonTemplate,
          features: convertToGeoJSONFormat(result),
        };
        setGeoJson(newGeoJson);
      }
    };
    if (currentMapLayerOptions.includes('btr-pipeline-ld')) {
      fetchData();
    }
  }, [currentMapLayerOptions]);

  const showTooltip = (e) => {
    const feature = e.features[0];
    console.log('tt', feature);
    const coordinates = feature.geometry.coordinates.slice();
    console.log('test coordinates', coordinates);
    const placeholder = document.createElement('div');
    ReactDOM.render(<LDBTRPipeLineTooltip feature={feature} />, placeholder);

    tooltipRef.current
      .setLngLat(coordinates)
      .setDOMContent(placeholder.firstChild)
      .addTo(map);
  };
  const hideTooltip = () => {
    tooltipRef.current.remove();
  };

  useEffect(() => {
    if (!map) return;

    map.on('mouseenter', `${sourceId}LayerCircle`, showTooltip);
    map.on('mouseleave', `${sourceId}LayerCircle`, hideTooltip);

    return () => {
      if (map) {
        map.off('mouseenter', `${sourceId}LayerCircle`, showTooltip);
        map.off('mouseleave', `${sourceId}LayerCircle`, hideTooltip);
      }
    };
  }, [map, geoJson]);

  if (currentMapLayerOptions.includes('btr-pipeline-ld'))
    return (
      <Source id={sourceId} type="geojson" data={geoJson}>
        <Layer {...circleStyle} />
        {/* <Layer {...symbolStyle} />
      <Layer {...clusterStyle} />
      <Layer {...clusterSymbolStyle} /> */}
      </Source>
    );
}

export default LandDevBTRPipeLineLayer;
