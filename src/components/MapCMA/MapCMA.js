import { getTokenFromLocalStorage } from '@/utils/auth';
import { useInterval } from '@/utils/useInterval';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import {
  ChainStoresLayerBoundary,
  ChainStoresLayerRadius,
  ChainStoresMenu,
  ChainStoresProvider,
  DemographicLayer,
  DemographicMenu,
  DemographicProvider,
  GentrifyingMenu,
  Map,
  PHMenu,
  useMap,
} from '@spatiallaser/map';
import { default as turf_bbox } from '@turf/bbox';
import { default as turf_circle } from '@turf/circle';
import { Modal } from 'antd';
import isEmpty from 'lodash.isempty';
import isEqual from 'lodash.isequal';
import { useCallback, useEffect, useRef, useState } from 'react';
import { connect, useDispatch, useSelector } from 'umi';
import cityCodes from '../../cityCodes.json';
import { MAPBOX_TOKEN, geojsonTemplate } from '../../constants';
import { serverType } from '../../services/data';
import LandMonthlyImages from '../LandMonthlyImages/LandMonthlyImages';
import LandShowcaseLayer from '../MapCMA/MapLayers/LandShowcaseLayer';
import BTRCommunityInfoLayer from '../ResultTable/BTRCommunityInfo/BTRCommunityInfoLayer';
import { default as LandMultiFamilyLayer } from '../ResultTable/RealtorMultiFamily/LandMultiFamilyLayer';
import LandSingleFamilyLayer from '../ResultTable/RealtorSingleFamily/LandSingleFamilyLayer';
import './mapboxStyle.css';
import styles from './MapCMA.css';
import MapDraw from './MapControls/MapDraw/MapDraw';
import MapExpander from './MapControls/MapExpander/MapExpander';
import MapRuler from './MapControls/MapRuler/MapRuler';
import SelectRadius from './MapControls/SelectRadius/SelectRadius';
import MapClicks from './MapEvents/MapClicks';
import MapZoomEnd from './MapEvents/MapZoomEnd';
import AffordableHousingLayer from './MapLayers/AffordableHousingLayer';
import BFRClusterLayer from './MapLayers/BFRClusterLayer';
import BTOwnedLayer from './MapLayers/BTOwnedLayer';
import BTRPipelineLayer from './MapLayers/BTRPipelineLayer';
import BuildingPermitLayer from './MapLayers/BuildingPermitLayer';
import BuiltForRentLayer from './MapLayers/BuiltForRentLayer';
import HotPadsLayer from './MapLayers/HotPadsLayer';
import KMLLayer from './MapLayers/KMLLayer';
import LandCompLayer from './MapLayers/LandCompLayer';
import LandCrexiLayer from './MapLayers/LandCrexiLayer';
import LandDevBTRPipeLineLayer from './MapLayers/LandDevBTRPipeLineLayer';
import LandDevChains from './MapLayers/LandDevChains';
import LandDevelopmentHighlightParcelLayer from './MapLayers/LandDevelopmentHighlightParcelLayer';
import PublicRecordLayer from './MapLayers/LastSalePublicRecordLayer';
import MLSLayer from './MapLayers/MLSLayer';
import MobileDataLayer from './MapLayers/MobileDataLayer';
import MultiFamilyLayer from './MapLayers/MultiFamilyLayer';
import NationalOperatorLayer from './MapLayers/NationalOperatorLayer';
import NewBuildsLayer from './MapLayers/NewBuildsLayer';
import NewlySubdividedPipelineLayer from './MapLayers/NewlySubdividedPipelineLayer';
import PadSplitLayer from './MapLayers/PadSplitLayer';
import ScorecardHistoryLayer from './MapLayers/ScorecardHistoryLayer';
import ScorecardLayer from './MapLayers/ScorecardLayer';
import SearchLayer from './MapLayers/SearchLayer';
import SegmentOverviewLayer from './MapLayers/SegmentOverviewLayer';
import {
  generateHighlightMarker,
  getMapInitializaitonPropertiesByUserGroup,
  highlightPropertyMarker,
  initPopup,
  loadSubjectPropertyFromURL,
} from './MapUtility/general';
export let propertyDetailPopup;
export let locatePropertyHandler;

const DEFAULT_ZOOM = 10.39787299330436;
function MapCMA(props) {
  const { setMap: setProviderMap, setActiveLayers } = useMap();
  const map = useSelector((state) => state.CMA.map);
  const currentMapLayerOptions = useSelector(
    (state) => state.CMA.currentMapLayerOptions,
  );
  const dispatch = useDispatch();
  const eventCoordinates = useSelector((state) => state.CMA.eventCoordinates);
  const currentRadiusMile = useSelector((state) => state.CMA.currentRadiusMile);
  // for [delay] in useInterval hook, only state and props will update their values
  // other variables will keep the first value they were assigned
  const [isRadiusSelectWrapperReady, setIsRadiusSelectWrapperReady] =
    useState(false);
  // set whether to show radius selector when map loaded for the first time
  const currentMapThemeOptionRef = useRef(props.currentMapThemeOption);
  currentMapThemeOptionRef.current = props.currentMapThemeOption;

  useInterval(
    () => {
      if (document.querySelector('#radiusSelectWrapper')) {
        if (props.compingMode === 'intelligentComping') {
          document.querySelector('#radiusSelectWrapper').style.display = 'none';
        } else {
          document.querySelector('#radiusSelectWrapper').style.display = 'flex';
        }
        setIsRadiusSelectWrapperReady(true);
      }
    },
    isRadiusSelectWrapperReady ? null : 1000,
  );

  const showHideHighlightMarker = useCallback(
    (fitBounds = false) => {
      if (!map) return;

      if (highlightPropertyMarker) {
        highlightPropertyMarker.remove();
      }
      // user clicked on a row in a table to highlight it on map
      if (
        props.currentHighlightCoordinates.length === 2 &&
        props.eventCoordinates.length > 0 &&
        props.eventCoordinates.every(
          (coordinate) => typeof coordinate === 'number',
        )
      ) {
        // NOTE: fitBound was too aggressive, so we removed it
        // NOTE: map radius selector has a center button
        // if (fitBounds) {
        //   // fitBounds
        //   const drawCircle = turf_circle(
        //     props.eventCoordinates,
        //     props.currentRadiusMile,
        //     {
        //       units: 'miles',
        //     },
        //   );
        //   map.current.fitBounds(turf_bbox(drawCircle), { padding: 32 });
        // }
        // add marker
        generateHighlightMarker(map, props);
      }
    },
    [
      map,
      props.typeHighlightMarker,
      props.priceHighlightMarker,
      props.currentHighlightCoordinates,
    ],
  );

  const showHideHighlightMarkerRef = useRef(showHideHighlightMarker);
  showHideHighlightMarkerRef.current = showHideHighlightMarker;

  useEffect(() => {
    if (!map) return;

    propertyDetailPopup = initPopup();

    map.on('map.themeOption', (e) => {
      const theme = e.payload.currentMapThemeOption;
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          currentMapThemeOption: theme,
        },
      });
    });

    // ONCE on mount set radius
    map.once('selectRadius.radius', (e) => {
      const radius = e.payload.currentRadiusMile;
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          currentRadiusMile: radius,
        },
      });
    });

    map.once('load', () => {
      if (!map) return;
      map.fire('cma.leaseMode', {
        // payload: { leaseMode: props.isLeaseMode },
        payload: { leaseMode: props.searchingMode === 'Lease' },
      });
      loadSubjectPropertyFromURL(map, props);

      map.on('click', (e) => {
        if (!isEmpty(propertyDetailPopup)) {
          propertyDetailPopup.remove();
        }
      });

      map.on('dragstart', (e) => {
        if (!isEmpty(propertyDetailPopup)) {
          propertyDetailPopup.remove();
        }
      });
    });

    locatePropertyHandler = (type, id) => {
      console.log('type: ', type, 'id: ', id);
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          cmaTabKey: '1',
          mapLocateProperty: { type: type, id: id },
        },
      });
    };

    const removehighlightPropertyMarker = (e) => {
      if (
        e.explicitOriginalTarget &&
        e.explicitOriginalTarget.className &&
        e.explicitOriginalTarget.className.includes &&
        e.explicitOriginalTarget.className.includes('ant-table-cell')
      )
        return;
      if (highlightPropertyMarker) {
        highlightPropertyMarker.remove();
        props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            currentHighlightCoordinates: [],
            priceHighlightMarker: 0,
            typeHighlightMarker: '',
          },
        });
      }
    };

    const styleLoad = () => {
      showHideHighlightMarkerRef.current();
    };

    const zoomEnd = () => {
      // Copy over markers when map style changes
      if (currentMapThemeOptionRef.current === 'Automatic') {
        map.on('style.load', styleLoad);
      }
    };

    document.addEventListener('click', removehighlightPropertyMarker);
    map.on('zoomend', zoomEnd);
    return () => {
      document.removeEventListener('click', removehighlightPropertyMarker);
      if (map) {
        map.off('zoomend', zoomEnd);
        map.off('style.load', styleLoad);
      }
    };
  }, [map]);

  useEffect(() => {
    if (!map) return;
    map.fire('cma.leaseMode', {
      payload: { leaseMode: props.searchingMode === 'Lease' },
    });
  }, [props.searchingMode]);

  useEffect(() => {
    if (!map) return;

    showHideHighlightMarkerRef.current(true);
  }, [props.currentHighlightCoordinates]);

  useEffect(() => {
    if (highlightPropertyMarker && highlightPropertyMarker.remove) {
      highlightPropertyMarker.remove();
    }
  }, [props.eventCoordinates]);

  useEffect(() => {
    if (!map) return;
    if (!isEmpty(props.currentSubdivision)) {
      const subdivisionBbox = turf_bbox(props.currentSubdivision.geom);
      map.fitBounds(subdivisionBbox, { padding: 32 });
    }
  }, [props.currentSubdivision]);

  // change radius display on map when applying parameters from adjusted API
  useEffect(() => {
    if (!map) return;
    const adjustedCompParams =
      props.searchingMode === 'Lease'
        ? props.adjustedRentCompParams
        : props.adjustedSalesCompParams;
    if (
      (adjustedCompParams && adjustedCompParams.distance) ||
      !adjustedCompParams
    ) {
      const setRadius = () => {
        map.fire('selectRadius.setRadius', {
          payload: {
            currentRadiusMile:
              adjustedCompParams && adjustedCompParams.distance
                ? adjustedCompParams.distance
                : 0.5,
          },
        });
      };

      try {
        setRadius();
      } catch (e) {
        map.once('style.load', () => {
          setRadius();
        });
      }

      const drawCircle = turf_circle(
        props.eventCoordinates,
        adjustedCompParams && adjustedCompParams.distance
          ? adjustedCompParams.distance
          : 0.5,
        {
          units: 'miles',
        },
      );
      map.fitBounds(turf_bbox(drawCircle), { padding: 32 });
    }
  }, [
    props.adjustedRentCompParams,
    props.adjustedSalesCompParams,
    props.adjustedPlacekey,
    props.searchingMode,
  ]); // track props.adjustedPlacekey to trigger this for every /adjusted request

  console.log(
    'metrosAllowedForCurrentTrialUser',
    props.metrosAllowedOnIndividualAccountLevel.map(
      (cityCode) => cityCodes.find((city) => city.value === cityCode).abbrev,
    ),
  );

  if (map && map.style && map.style._loaded) {
    console.log('layers', map && map.getStyle().layers);
  }
  console.log('currentMapLayerOptions', currentMapLayerOptions);

  return (
    <div
      id="cma-map-container"
      className={styles.mapContainer}
      style={props.style}
    >
      <Map
        // ref={map}
        getMap={(m) => {
          if (isEqual(map, m)) return;

          setProviderMap(m);
          props.dispatch({
            type: 'CMA/saveCMAStates',
            payload: { map: m },
          });
        }}
        token={MAPBOX_TOKEN}
        initProperties={{
          // zoom: DEFAULT_ZOOM,
          center: [-98.35, 39.5],
          zoom: 2.5,
        }}
        configure={{
          showParcelBoundaryLayer: true,
          sentinelHub: true,
          platlabsOnly: false,
          mapExpander: {
            enabled: true,
            init: {
              mapExpandedView: false,
            },
          },
          mapToImageDownload: true,
          mapDraw: true,
          mapRuler: true,
          mapNavigation: { enabled: true },
          mapControlsMapLayers: false,
          selectRadius: {
            enabled: true,
            init: {
              showClearButton: true,
              defaultRadius: 0.5,
            },
          },
          streetview: {
            enabled: true,
          },
        }}
        serverType={serverType}
        user={{
          userGroup: props.userGroup,
          metrosAllowedForCurrentTrialUser:
            props.metrosAllowedOnIndividualAccountLevel.map(
              (cityCode) =>
                cityCodes.find((city) => city.value === cityCode).abbrev,
            ), // convert city code to metro abbrev
        }}
      >
        {/* Gentrifying Neighborhood */}
        {currentMapLayerOptions.includes('gn') && (
          <>
            <GentrifyingMenu
              onClose={() => {
                map.fire('map.setThemeOption', {
                  payload: { currentMapThemeOption: 'Automatic' },
                });
                const payload = {
                  currentMapLayerOptions: currentMapLayerOptions.filter(
                    (l) => l !== 'gn',
                  ),
                };
                map.fire('mapLayers.currentMapLayerOptions', {
                  payload: payload,
                });

                props.dispatch({
                  type: 'CMA/saveCMAStates',
                  payload: payload,
                });
              }}
            />
          </>
        )}
        {/* Public Housing */}
        {currentMapLayerOptions.includes('public housing menu') && (
          <>
            <PHMenu
              onClose={() => {
                map.fire('map.setThemeOption', {
                  payload: { currentMapThemeOption: 'Automatic' },
                });
                const payload = {
                  currentMapLayerOptions: currentMapLayerOptions.filter(
                    (l) => !l.startsWith('public housing'),
                  ),
                };
                map.fire('mapLayers.currentMapLayerOptions', {
                  payload: payload,
                });

                props.dispatch({
                  type: 'CMA/saveCMAStates',
                  payload: payload,
                });
              }}
              openLayer={(layerName) => {
                const payload = {
                  // currentMapLayerOptions: currentMapLayerOptions.filter(
                  //   (l) => l !== 'public housing menu',
                  // ),
                  currentMapLayerOptions: [
                    ...currentMapLayerOptions,
                    layerName,
                  ],
                };
                map.fire('mapLayers.currentMapLayerOptions', {
                  payload: payload,
                });

                props.dispatch({
                  type: 'CMA/saveCMAStates',
                  payload: payload,
                });
              }}
              closeLayer={(layerName) => {
                const payload = {
                  currentMapLayerOptions: currentMapLayerOptions.filter(
                    (l) => l !== layerName,
                  ),
                  // currentMapLayerOptions: [...currentMapLayerOptions, layerName],
                };
                map.fire('mapLayers.currentMapLayerOptions', {
                  payload: payload,
                });

                props.dispatch({
                  type: 'CMA/saveCMAStates',
                  payload: payload,
                });
              }}
            />
          </>
        )}
        {/* Chain Store */}
        <ChainStoresProvider>
          {currentMapLayerOptions.includes('chain stores') && (
            <>
              <ChainStoresMenu
                onClose={() => {
                  const payload = {
                    currentMapLayerOptions: currentMapLayerOptions.filter(
                      (l) => l !== 'chain stores',
                    ),
                  };
                  map.fire('mapLayers.currentMapLayerOptions', {
                    payload: payload,
                  });

                  props.dispatch({
                    type: 'CMA/saveCMAStates',
                    payload: payload,
                  });
                }}
              />
              <ChainStoresLayerBoundary map={map} />
            </>
          )}
          {props.cmaTabKey === '4' && (
            <ChainStoresLayerRadius
              map={map}
              lat={eventCoordinates[1] || null}
              lng={eventCoordinates[0] || null}
              radius={props.sadChainStoreDistance * 1609.34}
              viewLayers={
                currentMapLayerOptions.includes('chain stores')
                  ? false
                  : {
                      unFavorable: false,
                    }
              }
            />
          )}
        </ChainStoresProvider>
      </Map>

      {!currentMapLayerOptions.includes('drive time') && (
        <>
          {props.selectedCompTables.includes('MLS') &&
            props.cmaTabKey === '1' &&
            props.searchingMode != 'Land' && <MLSLayer />}
          {props.selectedCompTables.includes(
            'National SFR Operators Listings',
          ) &&
            props.cmaTabKey === '1' && <NationalOperatorLayer />}

          {props.selectedCompTables.includes('Portal Listings') &&
            props.cmaTabKey === '1' && <HotPadsLayer />}

          <BTOwnedLayer />
          {props.cmaTabKey !== '3' &&
            props.cmaTabKey !== '6' &&
            props.cmaTabKey !== '5' &&
            props.cmaTabKey !== '7' &&
            props.cmaTabKey !== '8' &&
            props.searchingMode != 'Land' && (
              <>
                {/* Lease */}
                {props.searchingMode === 'Lease' && (
                  <>
                    {props.selectedCompTables.includes('Room Rental') &&
                      (props.userGroup.includes('VentureREI') ||
                        props.userGroup.includes('dev')) && <PadSplitLayer />}

                    {props.cmaTabKey === '9' && <BuiltForRentLayer />}
                    {props.searchingMode === 'Lease' &&
                      props.selectedCompTables.includes(
                        'Multi-Family Listings',
                      ) &&
                      (props.userGroup.includes('BridgeTower') ||
                        props.userGroup.includes('Avanta') ||
                        props.userGroup.includes('demo-users') ||
                        props.userGroup.includes('dev') ||
                        (props.userGroup.includes('demo-CMA-DFW-only') &&
                          (props.userEmail.includes('marchcapitalfund.com') ||
                            props.userEmail.includes('allcommonsenses')))) && (
                        <MultiFamilyLayer />
                      )}
                  </>
                )}
                {/* Lease End */}
                {props.searchingMode === 'Sale' && (
                  <>
                    {props.lastSalePublicRecordShowLayer &&
                      props.selectedCompTables.includes('Last Sale') && (
                        <PublicRecordLayer />
                      )}
                  </>
                )}
              </>
            )}
        </>
      )}
      {props.cmaTabKey == '6' && <AffordableHousingLayer />}
      {props.cmaTabKey === '9' && <BuiltForRentLayer />}
      <KMLLayer />
      <LandDevBTRPipeLineLayer />
      <BFRClusterLayer />
      {/* <ISOLayer /> */}
      {props.eventCoordinates.length === 2 && (
        <ScorecardLayer
          map={map}
          lat={props.eventCoordinates[1]}
          lng={props.eventCoordinates[0]}
          open={props.scorecardModalOpen}
          areaType={props.scorecardAreaType}
          getScreenShotData={(data) => {
            props.dispatch({
              type: 'CMA/saveCMAStates',
              payload: {
                scorecardImageData: data,
              },
            });
          }}
        />
      )}

      <MobileDataLayer />
      <SearchLayer />
      <ScorecardHistoryLayer />
      {props.displaySelectedSegmentHeatmap && <SegmentOverviewLayer />}
      {props.cmaTabKey === '3' && <NewBuildsLayer />}
      {props.cmaTabKey === '5' && <BuildingPermitLayer />}
      {props.cmaTabKey === '8' && <BTRPipelineLayer />}
      {props.cmaTabKey === '9' && <BTRCommunityInfoLayer />}
      {props.cmaTabKey === '8' && <NewlySubdividedPipelineLayer />}
      <LandDevelopmentHighlightParcelLayer />
      <LandDevChains />

      {props.cmaTabKey === '1' && props.searchingMode === 'Land' && (
        <>
          <LandCompLayer />
          <LandShowcaseLayer />
          <LandCrexiLayer />
        </>
      )}
      {props.cmaTabKey === '7' &&
        props.selectedCompTables.includes('Multi-family-2') &&
        props.searchingMode === 'Lease' && (
          <div id="multi-family-layers-container">
            <LandMultiFamilyLayer />
          </div>
        )}
      {props.cmaTabKey === '1' &&
        props.selectedCompTables.includes('Single-Family') && (
          <LandSingleFamilyLayer />
        )}

      <LandMonthlyImages />

      {props.openDemographicsHeatmap && (
        <DemographicProvider userGroup={props.userGroup[0]}>
          <DemographicLayer serverType={serverType} />
          <DemographicMenu
            onClose={() => {
              setActiveLayers((prevState) =>
                prevState.filter((l) => l !== 'heatmap-demographics'),
              );
              props.dispatch({
                type: 'CMA/saveCMAStates',
                payload: {
                  openDemographicsHeatmap: false,
                },
              });
            }}
          />
        </DemographicProvider>
      )}
      <MapExpander />
      <MapDraw />
      <MapRuler />
      <SelectRadius />
      <MapClicks />
      <MapZoomEnd />
    </div>
  );
}

export default connect(({ CMA }) => ({
  accessToken: CMA.accessToken,
  userGroup: CMA.userGroup,
  userEmail: CMA.userEmail,
  currentRadiusMile: CMA.currentRadiusMile,
  eventCoordinates: CMA.eventCoordinates,
  currentStatusMLS: CMA.currentStatusMLS,
  appViewMode: CMA.appViewMode,
  isLeaseMode: CMA.isLeaseMode,
  searchingMode: CMA.searchingMode,
  currentStartMLS: CMA.currentStartMLS,
  currentEndMLS: CMA.currentEndMLS,
  expDateFilterOn: CMA.expDateFilterOn,
  currentHighlightCoordinates: CMA.currentHighlightCoordinates,
  typeHighlightMarker: CMA.typeHighlightMarker,
  priceHighlightMarker: CMA.priceHighlightMarker,
  currentSubdivision: CMA.currentSubdivision,
  adjustedRentCompParams: CMA.adjustedRentCompParams,
  adjustedSalesCompParams: CMA.adjustedSalesCompParams,
  compingMode: CMA.compingMode,
  currentMapThemeOption: CMA.currentMapThemeOption,
  metrosAllowedOnIndividualAccountLevel:
    CMA.metrosAllowedOnIndividualAccountLevel,
  selectedCompTables: CMA.selectedCompTables,
  cmaTabKey: CMA.cmaTabKey,
  openDemographicsHeatmap: CMA.openDemographicsHeatmap,
  lastSalePublicRecordShowLayer: CMA.lastSalePublicRecordShowLayer,
  scorecardModalOpen: CMA.scorecardModalOpen,
  scorecardAreaType: CMA.scorecardAreaType,
  cmaTabKey: CMA.cmaTabKey,
  sadChainStoreDistance: CMA.sadChainStoreDistance,
  displaySelectedSegmentHeatmap: CMA.displaySelectedSegmentHeatmap,
}))(MapCMA);
