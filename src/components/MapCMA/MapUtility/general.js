import turf_area from '@turf/area';
import turf_bbox from '@turf/bbox';
import booleanPointInPolygon from '@turf/boolean-point-in-polygon';
import turf_flatten from '@turf/flatten';
import { multiPolygon } from '@turf/helpers';
import turf_intersect from '@turf/intersect';
import turf_union from '@turf/union';
import isEmpty from 'lodash.isempty';
import mapboxgl from 'mapbox-gl';
import moment from 'moment';
import queryString from 'query-string';
import { history } from 'umi';
import { MAP_LAYER_NAME_BASE, dateFormat } from '../../../constants';
import { getFloodZoneData } from '../../../services/data';
import { getGeofenceParamForGeocodingAPI } from '../../../utils/geofenceParams';
import { combineFormatter } from '../../../utils/money';

export let highlightPropertyMarker = null;

export const initPopup = () => {
  return new mapboxgl.Popup({
    closeButton: false,
    closeOnClick: false,
    offset: -5,
  });
};

export const getMapInitializaitonPropertiesByUserGroup = (userGroup) => {
  switch (true) {
    case userGroup.includes('Avanta'):
      return {
        center: [-98.35, 39.5],
        zoom: 2.5,
      };
    case userGroup.includes('CommonGroundCapital'):
      return {
        center: [-97.040443, 32.89748],
        maxBounds: [
          [-98.36250660282381, 32.17494189615582],
          [-95.87561207161805, 33.838764143245825],
        ],
      };
    case userGroup.includes('demo-CMA-DFW-only'):
      return {
        center: [-97.040443, 32.89748],
        maxBounds: [
          [-98.36250660282381, 32.17494189615582],
          [-95.87561207161805, 33.838764143245825],
        ],
      };
    case userGroup.includes('demo-CMA-Charlotte-only'):
      return {
        center: [-80.776978, 35.151916],
        maxBounds: [
          [-81.430664, 34.888184],
          [-80.123978, 35.414236],
        ],
      };
    default:
      return {
        center: [-97.040443, 32.89748],
      };
  }
};

export const setPriceMarkers = (map, data, listingKey, rentKey, type) => {
  let markersObject = {};
  for (let i = 0; i < data.length; i++) {
    const property = data[i];

    // check if the property is visible on the map
    // only generate markers for properties that are visible on the map
    // to optimize performance
    const isMarkerVisible = map
      .getBounds()
      .contains(property.geometry.coordinates);
    if (!isMarkerVisible) continue;

    // FIX: possible bug, property.properties could be undefined
    if (!(property[listingKey] in markersObject)) {
      let el = document.createElement('div');
      if (
        [
          'latestPrice',
          'rent',
          'totalrent',
          'price',
          'last_sale_price',
          'currentprice',
          'closeprice',
        ].includes(rentKey)
      ) {
        if (type === 'land-comp') {
          const key =
            property.properties.status === 'Closed'
              ? 'closeprice'
              : 'currentprice';
          el.innerHTML = '$' + combineFormatter(property.properties[key]);
        } else {
          el.innerHTML = '$' + combineFormatter(property.properties[rentKey]);
        }
      } else if (
        ['rooms_with_private_bath_price+rooms_with_shared_bath_price'].includes(
          rentKey,
        )
      ) {
        el.innerHTML =
          '$' +
          combineFormatter(
            (property.properties.rooms_with_private_bath_price *
              property.properties.active_rooms_with_private_bath +
              property.properties.rooms_with_shared_bath_price *
                property.properties.active_rooms_with_shared_bath) /
              (property.properties.active_rooms_with_private_bath +
                property.properties.active_rooms_with_shared_bath),
          );
        // '$' +
        // combineFormatter(
        //   property.properties.currentAvgRoomsPrivateBathPrice,
        // ) +
        // ' / $' +
        // combineFormatter(property.properties.currentAvgRoomsSharedBathPrice);
      } else {
        el.innerHTML = property.properties[rentKey] + ' Units';
      }

      if (type === MAP_LAYER_NAME_BASE.BTOwned) {
        el.className = 'listingMarker marker_BTOwned';
      } else if (type === MAP_LAYER_NAME_BASE.nationalOperator) {
        const brand = property.properties.brand.replace(/ /g, ''); // remove whitespace
        el.className = 'listingMarker marker_nationalOperators_' + brand;
      } else if (type === MAP_LAYER_NAME_BASE.hotPads) {
        el.className = 'listingMarker marker_HotPads';
      } else if (type === MAP_LAYER_NAME_BASE.padSplit) {
        el.className = 'listingMarker marker_PadSplit';
      } else if (type === MAP_LAYER_NAME_BASE.mls) {
        el.className = 'listingMarker marker_MLS';
      } else if (type === MAP_LAYER_NAME_BASE.multiFamily) {
        el.className = 'listingMarker marker_multiFamily';
      } else if (type === MAP_LAYER_NAME_BASE.newbuilds) {
        el.className = 'listingMarker marker_newBuild';
      } else if (type === MAP_LAYER_NAME_BASE.publicRecord) {
        el.className = 'listingMarker marker_PR';
      } else if (type === MAP_LAYER_NAME_BASE.affordableHousing) {
        el.className = 'listingMarker marker_AH';
      } else if (type === MAP_LAYER_NAME_BASE.landComp) {
        el.className = 'listingMarker marker_LC';
      } else if (type === MAP_LAYER_NAME_BASE.landMF) {
        el.className = 'listingMarker marker_LMF';
      } else if (type === MAP_LAYER_NAME_BASE.landSF) {
        el.className = 'listingMarker marker_LSF';
      }

      markersObject[property.properties[listingKey]] = new mapboxgl.Marker({
        element: el,
      });
      markersObject[property.properties[listingKey]]
        .setLngLat(property.geometry.coordinates)
        .addTo(map);
    }
  }
  return markersObject;
};

export const removePriceMarkers = (markers) => {
  if (!isEmpty(markers)) {
    const markersObject = markers;
    if (markersObject && Object.keys(markersObject).length > 0) {
      for (const marker in markersObject) {
        markersObject[marker].remove();
      }
    }
    return {};
  }
  return markers;
};

export const loadSubjectPropertyFromURL = async (map, props) => {
  // console.log('history.location.search.isLeaseMode type', typeof history.location.search.isLeaseMode); // string
  // console.log('formula', history.location.search.formula);
  console.log('history.location.search', history.location.search);
  // load intelligent comping params from URL
  const query = queryString.parse(history.location.search);

  if (
    Object.hasOwn(query, 'compingMode') &&
    query.compingMode === 'intelligentComping' &&
    props.eventCoordinates.length === 0
  ) {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        compingMode: 'intelligentComping',
        searchingMode: query.isLeaseMode === 'false' ? 'Sale' : 'Lease',
      },
    });
  }

  // load filter values from URL
  if (query.maxBeds && props.eventCoordinates.length === 0) {
    props.dispatch({
      type: 'CMA/updateFilterValues',
      payload: {
        compingMode: 'noFilter',
        searchingMode: query.isLeaseMode === 'false' ? 'Sale' : 'Lease',
        currentStatusMLS: query.currentStatusMLS,
        currentStartMLS: query.currentStartMLS,
        currentEndMLS: query.currentEndMLS,
        marketRentPreference: query.marketRentPreference,
        minBeds: +query.minBeds,
        maxBeds: +query.maxBeds,
        relationBeds: 'between',
        minBaths: +query.minBaths,
        maxBaths: +query.maxBaths,
        relationBaths: 'between',
        minSqft: +query.minSqft,
        maxSqft: +query.maxSqft,
        relationSqft: 'between',
        minLotSize: +query.minLotSize,
        maxLotSize: +query.maxLotSize,
        relationLotSize: 'between',
        minYearBuilt: +query.minYearBuilt,
        maxYearBuilt: +query.maxYearBuilt,
        relationYearBuilt: 'between',
        minCumulativeDaysOnMarket: +query.minCumulativeDaysOnMarket,
        maxCumulativeDaysOnMarket: +query.maxCumulativeDaysOnMarket,
        relationCumulativeDaysOnMarket: 'between',
        adjustedRentFormula: query.isLeaseMode === 'true' ? query.formula : '',
        adjustedSalesFormula:
          query.isLeaseMode === 'false' ? query.formula : '',
      },
    });
  }

  // load subject property from URL using address as query
  if (query.address && props.eventCoordinates.length === 0) {
    // set MLS & SFR status if status is avialable in url query
    if (Object.hasOwn(query, 'status')) {
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          currentStatusMLS: query.status,
        },
      });
    }
    // fetch all data
    const eventCoordinates = await props.dispatch({
      type: 'CMA/getAllPropertyData',
      payload: {
        mode: 'from url with address',
        searchText: query.address,
        geofence: getGeofenceParamForGeocodingAPI(props.userGroup),
        // status: props.currentStatusMLS,
        status: Object.hasOwn(query, 'status')
          ? query.status
          : props.currentStatusMLS, // allow setting MLS & SFR status from url
        propertyType:
          query.isLeaseMode === 'false' ? 'Residential' : 'Residential Lease',
        startDate: moment(props.currentStartMLS).format(dateFormat),
        endDate: moment(props.currentEndMLS).format(dateFormat),
        distance: +query.radius * 1609.34,
        exists: props.currentStatusMLS,
        expDateFilterOn: props.expDateFilterOn ? 'yes' : 'no',
        preference: query.marketRentPreference,
      },
    });

    if (eventCoordinates.length > 0) {
      map.fire('selectRadius.setEventCoordinates', {
        payload: {
          eventCoordinates: eventCoordinates,
        },
      });

      props.dispatch({
        type: 'CMA/getParcelOwnerSummary',
        payload: {
          lng: eventCoordinates[0],
          lat: eventCoordinates[1],
          distance: +query.radius * 1609.34,
        },
      });
    }

    if (props.currentRadiusMile !== +query.radius) {
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          currentRadiusMile: +query.radius,
        },
      });
      map.fire('selectRadius.setRadius', {
        payload: { currentRadiusMile: +query.radius },
      });
    }
    console.log('load from url address');
  }

  // load subject property from URL using lngLat as query
  if (
    props.eventCoordinates.length === 0 &&
    query.lng &&
    query.lat &&
    (query.radius || query.compingMode === 'intelligentComping')
  ) {
    if (Object.hasOwn(query, 'radius')) {
      console.log('load from url lnglat');
      // fetch all data
      props.dispatch({
        type: 'CMA/getAllPropertyData',
        payload: {
          mode: 'from url with lngLat',
          lng: query.lng,
          lat: query.lat,
          geofence: getGeofenceParamForGeocodingAPI(props.userGroup),
          status: props.currentStatusMLS,
          propertyType:
            props.searchingMode === 'Lease'
              ? 'Residential Lease'
              : 'Residential',
          startDate: moment(props.currentStartMLS).format(dateFormat),
          endDate: moment(props.currentEndMLS).format(dateFormat),
          distance: +query.radius * 1609.34,
          exists: props.currentStatusMLS,
          expDateFilterOn: props.expDateFilterOn ? 'yes' : 'no',
          preference: query.marketRentPreference,
        },
      });
    } else if (
      Object.hasOwn(query, 'compingMode') &&
      query.compingMode === 'intelligentComping'
    ) {
      props.dispatch({
        type: 'CMA/getAllPropertyData',
        payload: {
          mode: 'from url with lngLat for intelligent comping',
          lng: query.lng,
          lat: query.lat,
          status: props.currentStatusMLS,
          propertyType:
            props.searchingMode === 'Lease'
              ? 'Residential Lease'
              : 'Residential',
          exists: props.currentStatusMLS,
          expDateFilterOn: props.expDateFilterOn ? 'yes' : 'no',
          preference: query.preference,
          beds: query.beds,
          baths: query.baths,
          sqft: query.sqft,
          yearbuilt: query.yearbuilt,
          preference: query.preference,
        },
      });
    }

    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        eventCoordinates: [+query.lng, +query.lat],
      },
    });
    map.fire('selectRadius.setEventCoordinates', {
      payload: {
        eventCoordinates: [+query.lng, +query.lat],
      },
    });

    if (
      Object.hasOwn(query, 'radius') &&
      props.currentRadiusMile !== +query.radius
    ) {
      props.dispatch({
        type: 'CMA/getParcelOwnerSummary',
        payload: {
          lng: +query.lng,
          lat: +query.lat,
          distance: +query.radius * 1609.34,
        },
      });
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          currentRadiusMile: +query.radius,
        },
      });
      map.fire('selectRadius.setRadius', {
        payload: { currentRadiusMile: +query.radius },
      });
    }
  }
};

export const generateHighlightMarker = (map, props) => {
  let el = document.createElement('div');
  if (props.typeHighlightMarker === 'multiFamily') {
    el.innerHTML = props.priceHighlightMarker + ' Units';
  } else if (props.typeHighlightMarker === 'PadSplit') {
    console.log('props.priceHighlightMarker', props.priceHighlightMarker);
    el.innerHTML = props.priceHighlightMarker; // formatted in PadSplit.js because it involves two fields
  } else {
    el.innerHTML = '$' + combineFormatter(props.priceHighlightMarker);
    // (props.isLeaseMode
    //   ? formatter(props.priceHighlightMarker)
    //   : formatterKM(props.priceHighlightMarker));
  }

  const brand = props.typeHighlightMarker.replace(/ /g, ''); // remove whitespace

  el.className = `listingMarkerSelected markerSelected_${brand}`;
  highlightPropertyMarker = new mapboxgl.Marker({ element: el });
  highlightPropertyMarker
    .setLngLat(props.currentHighlightCoordinates)
    .addTo(map);
};

export const generateGeoJSONData = (data, selectedRowKeys, type) => {
  let geojsonFeatures = [];

  for (const property of data) {
    if (type === 'MLS' && selectedRowKeys.includes(property.mlsid)) {
      let { geography, ...geojsonProperties } = property;
      geojsonFeatures.push({
        type: 'Feature',
        geometry: property.geography,
        properties: geojsonProperties,
      });
    } else if (type === 'SFR' && selectedRowKeys.includes(property.base_id)) {
      let { geom, ...geojsonProperties } = property;
      geojsonFeatures.push({
        type: 'Feature',
        geometry: property.geom,
        properties: geojsonProperties,
      });
    } else if (
      type === 'PadSplit' &&
      selectedRowKeys.includes(property.property_id)
    ) {
      let { geometry, ...geojsonProperties } = property;
      console.log('testv2 property', property);
      geojsonFeatures.push({
        type: 'Feature',
        geometry: property.geometry,
        properties: geojsonProperties,
      });
    }
  }

  return {
    type: 'FeatureCollection',
    features: geojsonFeatures,
  };
};

export const getParcelBoundariesFromMap = (map, parcel) => {
  return new Promise(function (resolve, reject) {
    map.jumpTo({
      center: parcel.geog.coordinates,
      zoom: 16,
    });
    map.once('idle', () => {
      const features = map.queryRenderedFeatures({
        layers: ['parcelsFillStyle'],
      });

      // Parcel boundary can be broken down into multiple tiles
      const matchingTile = features.find((feature) => {
        const { _x, _y, _z } = feature;
        const geojson = feature._vectorTileFeature.toGeoJSON(_x, _y, _z);
        const isInside = booleanPointInPolygon(parcel.geog, geojson);
        return isInside;
      });

      if (!matchingTile) {
        resolve(null);
        return;
      }

      // Find related tiles
      const tiles = features.filter((feature) => {
        return feature.properties.ll_uuid === matchingTile.properties.ll_uuid;
      });

      // using union to merge polygons
      const mergedMultiPolgyon = tiles.reduce((acc, curr) => {
        const { _x, _y, _z } = curr;
        const geojson = curr._vectorTileFeature.toGeoJSON(_x, _y, _z);

        if (isEmpty(acc)) {
          acc = geojson;
        } else {
          acc = turf_union(acc, geojson);
        }

        return acc;
      }, {});

      resolve(mergedMultiPolgyon);
    });
  });
};

export const injectMapProcessingLoader = () => {
  const mapProcessingLoader = document.createElement('div');
  mapProcessingLoader.id = 'mapProcessingLoader';
  mapProcessingLoader.style = `position: absolute; top: 0; left: 0; right:0; bottom:0; z-index: 999; display: flex; flex-direction: column; align-items: center; justify-content: center;`;
  mapProcessingLoader.innerHTML = `
    <div style="background-color: white; padding: 20px; border-radius: 8px; border: 1px solid black; box-shadow: rgba(50, 50, 93, 0.25) 0px 30px 60px -12px, rgba(0, 0, 0, 0.3) 0px 18px 36px -18px;">Processing...</div>
  `;
  document.getElementById('Map').style = 'filter: blur(5px);';
  document.getElementById('cma-map-container').appendChild(mapProcessingLoader);
};

export const removeMapProcessingLoader = () => {
  document.getElementById('Map').style = '';
  const loader = document.getElementById('mapProcessingLoader');
  if (loader) loader.remove();
};

export const getFloodDataForMapParcel = async (map, parcelArr) => {
  try {
    const boundaries = [];
    const saveLocationPosition = map.getBounds();
    injectMapProcessingLoader();
    for (let i = 0; i < parcelArr.length; i++) {
      const parcel = parcelArr[i];
      const boundary = await getParcelBoundariesFromMap(map, parcel);
      boundaries.push({ parcel, boundary });
    }
    removeMapProcessingLoader();
    map.fitBounds(saveLocationPosition, {
      animate: false,
    });

    for (let i = 0; i < boundaries.length; i++) {
      if (!boundaries[i].boundary) continue;

      const bounds = turf_bbox(boundaries[i].boundary);
      const padding = 0.001;

      const floodZoneData = await getFloodZoneData({
        lat1: bounds[2] + padding,
        lng1: bounds[3] + padding,
        lat2: bounds[0] - padding,
        lng2: bounds[1] - padding,
      });

      const floodGeometry = floodZoneData.reduce((acc, curr) => {
        if (
          curr.shape.coordinates.length > 0 &&
          curr.shape.coordinates[0].length > 0
        )
          acc.push(curr.shape);
        return acc;
      }, []);

      if (floodGeometry.length > 0) {
        const mergedFloodZone = floodGeometry.reduce((acc, curr) => {
          if (isEmpty(acc)) {
            return curr;
          } else {
            acc = turf_union(acc, curr);
          }
          return acc;
        }, {});

        const intersect = turf_intersect(
          boundaries[i].boundary,
          mergedFloodZone,
        );

        if (intersect) {
          const area = turf_area(intersect);
          const total_area = turf_area(boundaries[i].boundary);
          const percent = (area / total_area) * 100;

          // console.log('area', area);
          // console.log('percent', percent);
          boundaries[i].parcel.flood_coverage = percent;
        } else {
          boundaries[i].parcel.flood_coverage = null;
        }
      } else {
        boundaries[i].parcel.flood_coverage = null;
      }
    }

    return boundaries.map((boundary) => boundary.parcel);
  } catch (e) {
    console.log(e);
    removeMapProcessingLoader();
  }
};
