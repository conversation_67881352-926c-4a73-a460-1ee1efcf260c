export const showVisibility = (map, layerName) => {
  if (!map || !map.style || !map.style._loaded) return;
  if (!map.getLayer(layerName)) return;

  map.setLayoutProperty(layerName, 'visibility', 'visible');
};
export const hideVisibility = (map, layerName) => {
  if (!map || !map.style || !map.style._loaded) return;
  if (!map.getLayer(layerName)) return;

  map.setLayoutProperty(layerName, 'visibility', 'none');
};
