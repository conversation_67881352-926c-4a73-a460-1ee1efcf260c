import { CloseOutlined, DownloadOutlined } from '@ant-design/icons';
import { Pirlo } from '@spatiallaser/spreadsheet-upload';
import {
  Button,
  Col,
  DatePicker,
  Modal,
  Progress,
  Row,
  Select,
  Spin,
  Table,
  Tooltip,
} from 'antd';
import dayjs from 'dayjs';
import moment from 'moment/moment';
import React, { useEffect, useRef, useState } from 'react';
import { connect } from 'umi';
import {
  getBatchProcessingProgressDataV2,
  postBatchProcessingFileDataV2,
} from '../services/data';
import styles from './batchProcessing.css';

// Helper function to generate CSV from Pirlo data
const generateCSVFromPirloData = (dataArray) => {
  const baseHeaders = [
    'address',
    'city',
    'state',
    'zip_code',
    'beds',
    'baths',
    'sqft',
    'yearbuilt',
    'latitude',
    'longitude',
  ];
  // Collect additional headers from meta objects, excluding duplicates of base headers
  const additionalHeaders = new Set();
  dataArray.forEach((item) => {
    const data = item.matched || item.data || item || {};
    const meta = data.meta && typeof data.meta === 'object' ? data.meta : {};
    Object.keys(meta).forEach((key) => {
      const normalizedKey = key.toLowerCase();
      // Exclude keys that are duplicates of base headers or unwanted fields
      if (
        !baseHeaders.includes(normalizedKey) &&
        !['zip', 'zipcode', 'size', 'year built', 'year_built'].includes(
          normalizedKey,
        )
      ) {
        additionalHeaders.add(key);
      }
    });
  });

  // Combine base headers with additional headers from meta
  const headers = [...baseHeaders, ...Array.from(additionalHeaders)];

  const rows = dataArray.map((item) => {
    const data = item.matched || item.data || item || {};
    const meta = data.meta && typeof data.meta === 'object' ? data.meta : {};

    const getField = (field) => {
      let value =
        data[field] ||
        data[field.toLowerCase()] ||
        data[field.toUpperCase()] ||
        data[`matched_${field}`] ||
        meta[field] || // Check meta for additional fields
        '';

      // Handle non-string values (e.g., objects)
      if (typeof value === 'object') {
        value =
          JSON.stringify(value) === '[object Object]'
            ? ''
            : JSON.stringify(value);
      }

      return String(value || '').replace(/"/g, '""');
    };

    return headers.map((header) => {
      let value;
      if (baseHeaders.includes(header)) {
        // Handle base headers with their specific fallbacks
        switch (header) {
          case 'zip_code':
            value =
              getField('zip_code') || getField('zip') || getField('zipcode');
            break;
          case 'beds':
            value = getField('beds') || getField('Beds');
            break;
          case 'baths':
            value = getField('baths') || getField('Baths');
            break;
          case 'sqft':
            value = getField('sqft') || getField('Size') || getField('size');
            break;
          case 'yearbuilt':
            value =
              getField('yearbuilt') ||
              getField('Year built') ||
              getField('Year_built') ||
              getField('year_built');
            break;
          default:
            value = getField(header);
        }
      } else {
        // Handle additional headers from meta
        value = getField(header);
      }
      return `"${value}"`;
    });
  });

  return [headers.join(','), ...rows.map((row) => row.join(','))].join('\n');
};
const BatchProcessor = (props) => {
  const csvInputRef = useRef(null);
  const [csvFile, setCsvFile] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [uploadReady, setUploadReady] = useState(false);
  const [fileName, setFileName] = useState('');
  const [loading, setLoading] = useState(true);
  const [showPirloModal, setShowPirloModal] = useState(false);
  const [csvMode, setCsvMode] = useState('simple');
  const [processDate, setProcessDate] = useState(dayjs());
  const [uploadStatus, setUploadStatus] = useState({
    processing: false,
    progress: [0, 0],
    downloadLink: '',
  });
  const [csvContent, setCsvContent] = useState(null);
  const columnsUserUploadRecord = [
    {
      title: 'Id',
      dataIndex: 'id',
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: 'Time Uploaded',
      dataIndex: 'creation_time',
      width: 150,
      render: (text) => moment.parseZone(text).format('YYYY-MM-DD'),
      sorter: (a, b) => {
        // Using parseZone to properly handle the timezone information in the ISO format
        const dateA = moment.parseZone(a.creation_time);
        const dateB = moment.parseZone(b.creation_time);
        return dateA.valueOf() - dateB.valueOf(); // Returns numeric difference for proper sorting
      },
      defaultSortOrder: 'descend', // Added default sort order to this column
    },
    {
      title: 'File',
      dataIndex: 'csv_url',
      render: (text) => (
        <a href={text} target="_blank" rel="noreferrer">
          {decodeURIComponent(text.split('/').pop())}
        </a>
      ),
    },
  ];

  useEffect(() => {
    if (props.showBatchProcessor) {
      props.dispatch({
        type: 'CMA/getUserBatchProcessingRecord',
      });
      setLoading(false);
    }
  }, [props.showBatchProcessor]);

  const handleFileChange = (e) => {
    setUploadReady(false);
    setUploading(true);
    setUploadStatus({
      processing: false,
      progress: [0, 0],
      downloadLink: '',
    });
    setCsvContent(null);
    const files = e.currentTarget.files;
    if (files && files.length > 0) {
      setCsvFile(files[0]);
      setFileName(files[0].name);
      setUploadReady(true);
      setUploading(false);
    }
  };
  const handleDownloadCSV = () => {
    if (!csvContent) return;

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', fileName || 'processed_data.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handlePirloFinish = (data, originalFileName) => {
    console.log('Pirlo finish data (raw):', data);
    const fileName = originalFileName.split('.')[0];

    if (!data || !Array.isArray(data) || data.length === 0) {
      console.error('Invalid or empty Pirlo data:', data);
      return;
    }

    try {
      const csvContent = generateCSVFromPirloData(data);
      console.log('Generated CSV content:', csvContent);
      setCsvContent(csvContent);
      if (csvContent.split('\n').length <= 1) {
        console.error('CSV generation failed - no data rows created');
        return;
      }

      const csvBlob = new Blob([csvContent], {
        type: 'text/csv;charset=utf-8;',
      });
      const csvFile = new File([csvBlob], `${fileName}.csv`, {
        type: 'text/csv',
      });

      setCsvFile(csvFile);
      setFileName('processed_data.csv');
      setUploadReady(true);
      setShowPirloModal(false);
      console.log('CSV file successfully generated and ready for upload');

      // Automatically start processing immediately after Pirlo finishes
      setTimeout(() => {
        const formData = new FormData();
        formData.append('file', csvFile);
        formData.append('csv_mode', csvMode);
        formData.append('process_date', processDate.format('YYYY-MM-DD'));

        setUploadStatus({
          processing: true,
          progress: [0, 0],
          downloadLink: '',
        });

        console.log(
          'Auto-starting processing with file:',
          csvFile,
          'mode:',
          csvMode,
          'date:',
          processDate.format('YYYY-MM-DD'),
        );

        postBatchProcessingFileDataV2({
          date: processDate.format('YYYY-MM-DD'),
          body: formData,
        })
          .then((uploadResponse) => {
            console.log('Upload response:', uploadResponse);

            const interval = setInterval(async () => {
              try {
                const progressResponse = await getBatchProcessingProgressDataV2(
                  {
                    job_id: uploadResponse?.job_id,
                  },
                );

                console.log('Batch processing progress:', progressResponse);

                if (progressResponse?.status === 'done') {
                  console.log('Processing done');
                  setUploadStatus((prev) => ({
                    ...prev,
                    processing: false,
                    downloadLink: progressResponse.s3_link || '',
                  }));
                  clearInterval(interval);

                  props.dispatch({
                    type: 'CMA/getUserBatchProcessingRecord',
                  });
                } else if (progressResponse?.status === 'processing') {
                  console.log('Updating progress');
                  if (
                    !Number.isNaN(progressResponse.processed) &&
                    progressResponse.processed !== undefined &&
                    !Number.isNaN(progressResponse.total) &&
                    progressResponse.total !== undefined
                  ) {
                    setUploadStatus((prev) => ({
                      ...prev,
                      progress: [
                        Number(progressResponse.processed),
                        Number(progressResponse.total),
                      ],
                    }));
                  }
                }
              } catch (error) {
                console.error('Progress check failed:', error);
                // Don't stop processing on progress check error
              }
            }, 5000);
          })
          .catch((error) => {
            console.error('Upload failed:', error);
            setUploadStatus((prev) => ({
              ...prev,
              processing: false,
            }));
          });
      }, 500); // Small delay to ensure state updates have completed
    } catch (error) {
      console.error('Error processing Pirlo data:', error);
    }
  };

  const uploadCSV = async (event) => {
    event.preventDefault();
    console.log('uploadCSV called');
    if (!csvFile) {
      console.error('No CSV file available to upload');
      return;
    }

    setUploadStatus({
      processing: true,
      progress: [0, 0],
      downloadLink: '',
    });

    const formData = new FormData();
    formData.append('file', csvFile);
    formData.append('csv_mode', csvMode);
    console.log(
      'FormData prepared with file:',
      csvFile,
      'mode:',
      csvMode,
      'date:',
      processDate.format('YYYY-MM-DD'),
    );

    try {
      console.log('Calling postBatchProcessingFileDataV2');
      const uploadResponse = await postBatchProcessingFileDataV2({
        body: formData,
        date: processDate.format('YYYY-MM-DD'),
      });
      console.log('Upload response:', uploadResponse);

      const interval = setInterval(async () => {
        try {
          const progressResponse = await getBatchProcessingProgressDataV2({
            job_id: uploadResponse?.job_id,
          });

          console.log('Batch processing progress:', progressResponse);

          if (progressResponse?.status === 'done') {
            console.log('Processing done');
            setUploadStatus((prev) => ({
              ...prev,
              processing: false,
              downloadLink: progressResponse.s3_link || '',
            }));
            clearInterval(interval);

            props.dispatch({
              type: 'CMA/getUserBatchProcessingRecord',
            });
          } else if (progressResponse?.status === 'processing') {
            console.log('Updating progress');
            if (
              !Number.isNaN(progressResponse.processed) &&
              progressResponse.processed !== undefined &&
              !Number.isNaN(progressResponse.total) &&
              progressResponse.total !== undefined
            ) {
              setUploadStatus((prev) => ({
                ...prev,
                progress: [
                  Number(progressResponse.processed),
                  Number(progressResponse.total),
                ],
              }));
            }
          }
        } catch (error) {
          console.error('Progress check failed:', error);
          // Don't stop processing on progress check error
        }
      }, 5000);
    } catch (error) {
      console.error('Upload failed:', error);
      setUploadStatus((prev) => ({
        ...prev,
        processing: false,
      }));
    }
  };

  const resetUpload = () => {
    setCsvFile(null);
    setFileName('');
    setUploadReady(false);
    setUploadStatus({
      processing: false,
      progress: [0, 0],
      downloadLink: '',
    });
    if (csvInputRef.current) {
      csvInputRef.current.value = '';
    }
  };

  const handleProcessClick = () => {
    console.log('Process button clicked');
    if (uploadStatus.downloadLink) {
      resetUpload();
      return;
    }
    if (!csvFile) {
      console.error('No file selected');
      return;
    }
    uploadCSV({ preventDefault: () => {} });
  };

  const closeModal = () => {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: { showBatchProcessor: false },
    });
  };

  return (
    <Modal
      open={props.showBatchProcessor}
      closable={true}
      maskClosable={true}
      onCancel={closeModal}
      centered={true}
      footer={null}
      width={'calc(100% - 64px)'}
      style={{
        backgroundColor: 'rgba(253,253,253,0.92)',
        height: 'calc(100vh - 64px)',
        zIndex: 999,
      }}
      styles={{ body: { height: 'calc(100vh - 64px)' } }}
    >
      <div className={styles.modalTitle}>Batch Process</div>
      <div className={styles.divider} />

      <Row className={styles.modalRowWrapper}>
        <Col span={12} className={styles.modalColWrapper}>
          <div className={styles.instruction}>
            Click the blue button to start the batch processing.
          </div>

          <div style={{ marginBottom: 16 }}>
            <div
              style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}
            >
              <span style={{ marginRight: 8, width: 120 }}>
                Processing Mode:
              </span>
              <Select
                value={csvMode}
                onChange={(value) => setCsvMode(value)}
                options={[
                  { value: 'simple', label: 'Simple' },
                  { value: 'legacy', label: 'Legacy' },
                ]}
                style={{ width: 120 }}
              />
            </div>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span style={{ marginRight: 8, width: 120 }}>
                Valuation as of :
              </span>
              <DatePicker
                value={processDate}
                onChange={(date) => setProcessDate(date)}
                style={{ width: 120 }}
                format="YYYY-MM-DD"
                inputReadOnly={true}
                defaultPickerValue={dayjs()} // Ensure calendar opens to today
              />
            </div>
            {/* {csvContent && (
              <Button
                type="primary"
                icon={<DownloadOutlined />}
                onClick={handleDownloadCSV}
                className={styles.downloadCSVButton}
                style={{ marginTop: 8 }}
              >
                Download CSV
              </Button>
            )} */}
          </div>

          <Button
            type="primary"
            onClick={() => setShowPirloModal(true)}
            className={styles.uploadCSVButton}
          >
            Batch Upload
          </Button>
          <Button
            style={{ display: 'none' }}
            type="primary"
            onClick={handleProcessClick}
            disabled={
              (!uploadReady && !uploadStatus.downloadLink) ||
              uploadStatus.processing
            }
            className={styles.uploadCSVButtonElixir}
          >
            {uploadStatus.downloadLink
              ? 'Upload Again'
              : uploadReady
              ? `Process ${fileName}`
              : 'Start Processing'}
          </Button>

          {uploadStatus.processing && (
            <div style={{ marginTop: 16 }}>
              <Progress
                percent={Math.round(
                  (uploadStatus.progress[0] / uploadStatus.progress[1]) * 100,
                )}
                status="active"
                format={() =>
                  `Processing ${uploadStatus.progress[0]}/${uploadStatus.progress[1]}`
                }
              />
            </div>
          )}

          {showPirloModal && (
            <div
              className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
              onClick={() => setShowPirloModal(false)}
            >
              <div
                className="relative z-60"
                onClick={(e) => e.stopPropagation()}
              >
                <Pirlo
                  onClose={() => setShowPirloModal(false)}
                  apiFields={[
                    { name: 'address', mandatory: true },
                    { name: 'city', mandatory: true },
                    { name: 'state', mandatory: false },
                    { name: 'zip', mandatory: true },
                    {
                      name: 'latitude',
                      mandatory: false,
                      validate: (value) => {
                        if (!value || value === null || value === '-')
                          return null;
                        const num = parseFloat(value);
                        return isNaN(num) || num < 24.396 || num > 49.384
                          ? 'Latitude must be between 24.396 and 49.384'
                          : null;
                      },
                    },
                    {
                      name: 'longitude',
                      mandatory: false,
                      validate: (value) => {
                        if (!value || value === null || value === '-')
                          return null;
                        const num = parseFloat(value);
                        return isNaN(num) || num < -125.0 || num > -66.934
                          ? 'Longitude must be between -125.000 and -66.934'
                          : null;
                      },
                    },
                    {
                      name: 'Beds',
                      mandatory: false,
                    },
                    {
                      name: 'Baths',
                      mandatory: false,
                    },
                    {
                      name: 'Size',
                      mandatory: false,
                    },
                    {
                      name: 'Year built',
                      mandatory: false,
                    },
                  ]}
                  onFinish={handlePirloFinish}
                />
              </div>
            </div>
          )}
        </Col>

        <Col span={12} className={styles.modalColWrapper}>
          <Table
            className={styles.recordTable}
            rowKey={(record) => record.id}
            columns={columnsUserUploadRecord}
            dataSource={props.UserBatchProcessingRecord}
            size="small"
            pagination={{ pageSize: 10 }}
            loading={loading}
          />
        </Col>
      </Row>
    </Modal>
  );
};

export default connect(({ CMA }) => ({
  showBatchProcessor: CMA.showBatchProcessor,
  UserBatchProcessingRecord: CMA.UserBatchProcessingRecord,
}))(BatchProcessor);
