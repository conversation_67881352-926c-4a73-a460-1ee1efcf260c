import { dateFormat } from '@/constants';
import {
  deleteBookmark,
  getBookmarkProperty,
  getUserBookmarks,
  getUserTableColumnSettings,
  patchUpdateBookmarkName,
} from '@/services/data';
import { DeleteOutlined, EditOutlined, StarFilled } from '@ant-design/icons';
import { useAuthenticator } from '@aws-amplify/ui-react';
import { Button, Dropdown, List, MenuProps, Spin } from 'antd';
import moment from 'moment';
import VirtualList from 'rc-virtual-list';
import React, { useEffect, useState } from 'react';
import { history, useDispatch, useSelector } from 'umi';
import { BookmarkNameInputModal } from './BookmarkButton';

interface Bookmark {
  id: number;
  latitude: number;
  longitude: number;
  address: string;
  city: string;
  state: string;
  postalCode: string;
  userId: string;
  clientId: string;
  name?: string | null;
}

const ITEM_HEIGHT = 64;
const LIST_HEIGHT = 280;
const LIST_WIDTH = 450;

const BookmarkListContent: React.FC<{
  userBookmarks: Bookmark[];
  onSelectBookmark: (bookmark: Bookmark) => void;
  onModifyBookmark: (bookmark: Bookmark) => void;
  onDeleteBookmark: (bookmarkId: number) => void;
}> = ({
  userBookmarks,
  onSelectBookmark,
  onModifyBookmark,
  onDeleteBookmark,
}) => (
  <div
    className="bookmark-list-container bg-white rounded-md shadow-lg"
    style={{
      padding: '8px 0',
      width: LIST_WIDTH,
    }}
  >
    {userBookmarks.length > 0 ? (
      <List style={{ width: '100%' }}>
        <VirtualList
          data={userBookmarks}
          height={LIST_HEIGHT}
          itemHeight={ITEM_HEIGHT}
          itemKey="id"
          fullHeight={false}
        >
          {(bookmark: Bookmark) => (
            <List.Item
              key={bookmark.id}
              className="cursor-pointer hover:bg-gray-50 w-full"
              style={{ padding: '8px 16px' }}
            >
              <div
                className="w-full select-none flex items-center justify-between"
                onClick={() => onSelectBookmark(bookmark)}
              >
                <div className="flex-grow">
                  <div className="font-base truncate">
                    {bookmark.name && `${bookmark.name}`}{' '}
                  </div>
                  <div className="text-gray-400 text-sm truncate max-w-80">
                    {bookmark.address}
                  </div>
                </div>
                <div
                  className="flex gap-2 ml-4"
                  onClick={(e) => e.stopPropagation()}
                >
                  <Button
                    type="text"
                    icon={<EditOutlined />}
                    onClick={() => onModifyBookmark(bookmark)}
                    className="text-gray-500 hover:text-blue-500"
                  />
                  <Button
                    type="text"
                    icon={<DeleteOutlined />}
                    onClick={() => onDeleteBookmark(bookmark.id)}
                    className="text-gray-500 hover:text-red-500"
                  />
                </div>
              </div>
            </List.Item>
          )}
        </VirtualList>
      </List>
    ) : (
      <div className="text-gray-400 py-2 px-4">No bookmarked properties.</div>
    )}
  </div>
);

const BookmarksDropdown: React.FC = () => {
  const dispatch = useDispatch();
  const userBookmarks = useSelector((state: any) => state.CMA.userBookmarks);
  const currentStatusMLS = useSelector(
    (state: any) => state.CMA.currentStatusMLS,
  );
  const searchingMode = useSelector((state: any) => state.CMA.searchingMode);
  const currentStartMLS = useSelector(
    (state: any) => state.CMA.currentStartMLS,
  );
  const currentEndMLS = useSelector((state: any) => state.CMA.currentEndMLS);
  const currentRadiusMile = useSelector(
    (state: any) => state.CMA.currentRadiusMile,
  );
  const expDateFilterOn = useSelector(
    (state: any) => state.CMA.expDateFilterOn,
  );
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [bookmarkToModify, setBookmarkToModify] = useState<Bookmark | null>(
    null,
  );

  const handleModifyBookmark = (bookmark: Bookmark) => {
    setBookmarkToModify(bookmark);
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        bookmarkUpdateModalOpened: true,
        bookmarkInputNameValue: bookmark.name || '',
      },
    });
  };

  const handleDeleteBookmark = async (bookmarkId: number) => {
    try {
      // Delete the bookmark
      await deleteBookmark({ id: bookmarkId });

      // Refresh the bookmarks list
      const newBookmarks = await getUserBookmarks();
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          userBookmarks: newBookmarks,
        },
      });
    } catch (error) {
      console.error('Error deleting bookmark:', error);
    }
  };

  const closeModal = () => {
    setBookmarkToModify(null);
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        bookmarkUpdateModalOpened: false,
        bookmarkInputNameValue: '',
      },
    });
  };

  const bookmarkInputNameValue = useSelector(
    (state: any) => state.CMA.bookmarkInputNameValue,
  );

  const submitBookmark = async () => {
    try {
      if (bookmarkToModify) {
        console.log('submitBookmark called');
        // Handle modification of existing bookmark
        await patchUpdateBookmarkName({
          id: bookmarkToModify.id,
          name: bookmarkInputNameValue,
        });
      }

      // Refresh the bookmarks list
      const newBookmarks = await getUserBookmarks();
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          userBookmarks: newBookmarks,
        },
      });

      closeModal();
    } catch (error) {
      console.error('Error updating bookmark name:', error);
      // You might want to show an error message to the user here
    }
  };

  async function onSelectBookmark(bookmark: Bookmark) {
    const property = await getBookmarkProperty({ id: bookmark.id });
    if (property) {
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          eventCoordinates: [property.longitude, property.latitude],
          currentPropertyAddress: {
            ...property,
            // fullAddress: property.address,
            streetAddress: property.address,
          },
        },
      });

      dispatch({
        type: 'CMA/getAllPropertyData',
        payload: {
          mode: 'search address',
          ...property,
          lng: property.longitude,
          lat: property.latitude,
          status: currentStatusMLS,
          propertyType:
            searchingMode === 'Lease' ? 'Residential Lease' : 'Residential',
          startDate: moment(currentStartMLS).format(dateFormat),
          endDate: moment(currentEndMLS).format(dateFormat),
          distance: currentRadiusMile * 1609.34,
          exists: currentStatusMLS,
          expDateFilterOn: expDateFilterOn ? 'yes' : 'no',
        },
      });

      history.push({ search: '' });
    }
  }

  useEffect(() => {
    fetchBookmarks();
  }, []);

  const fetchBookmarks = async () => {
    try {
      setLoading(true);
      const data = await getUserBookmarks();
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          userBookmarks: data,
        },
      });
      setError(null);
    } catch (err) {
      setError('Failed to load bookmarks');
      console.error('Error loading bookmarks:', err);
    } finally {
      setLoading(false);
    }
  };
  const bookmarkUpdateModalOpened = useSelector(
    (state: any) => state.CMA.bookmarkUpdateModalOpened,
  );
  return (
    <>
      <Dropdown
        dropdownRender={() => (
          <BookmarkListContent
            userBookmarks={userBookmarks}
            onSelectBookmark={onSelectBookmark}
            onModifyBookmark={handleModifyBookmark}
            onDeleteBookmark={handleDeleteBookmark}
          />
        )}
        trigger={['click']}
        placement="bottomLeft"
        disabled={loading}
        overlayStyle={{
          minWidth: LIST_WIDTH,
        }}
      >
        <Button type="default" className="flex items-center">
          {loading ? (
            <span className="ml-2 flex items-center">
              <Spin size="small" />
              <span className="ml-2">Loading...</span>
            </span>
          ) : (
            <span>
              <span className="text-yellow-400 hover:text-yellow-500 pr-1">
                <StarFilled />
              </span>
              Bookmarks{' '}
              {userBookmarks && userBookmarks.length > 0
                ? `(${userBookmarks.length})`
                : ''}
            </span>
          )}
        </Button>
      </Dropdown>
      <BookmarkNameInputModal
        title={'Update Bookmark Name'}
        state={bookmarkUpdateModalOpened}
        submitBookmarkFnc={submitBookmark}
        closeModal={closeModal}
      />
    </>
  );
};

export default BookmarksDropdown;
