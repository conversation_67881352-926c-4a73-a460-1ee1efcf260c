import {
  deleteBookmark,
  getUserBookmarks,
  postSaveBookmark,
} from '@/services/data';
import { StarFilled, StarOutlined } from '@ant-design/icons';
import { Button, Input, Modal, Tooltip } from 'antd';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'umi';

const BookmarkButton: React.FC = () => {
  const dispatch = useDispatch();
  const [isHovered, setIsHovered] = useState<boolean>(false);
  const [isSaved, setIsSaved] = useState<boolean>(false);
  const [currentBookmarkId, setCurrentBookmarkId] = useState<number | null>(
    null,
  );
  const currentPropertyAddress = useSelector(
    (state: any) => state.CMA.currentPropertyAddress,
  );
  const userBookmarks = useSelector((state: any) => state.CMA.userBookmarks);
  const bookmarkInputModalOpened = useSelector(
    (state: any) => state.CMA.bookmarkInputModalOpened,
  );
  const bookmarkInputNameValue = useSelector(
    (state: any) => state.CMA.bookmarkInputNameValue,
  );

  const openModal = () => {
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        bookmarkInputModalOpened: true,
      },
    });
  };

  const closeModal = () => {
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        bookmarkInputModalOpened: false,
      },
    });
  };
  const submitBookmark = async () => {
    console.log('submitBookmark button called');
    await postSaveBookmark({
      body: {
        latitude: currentPropertyAddress.latitude,
        longitude: currentPropertyAddress.longitude,
        address: currentPropertyAddress.fullAddress,
        city: currentPropertyAddress.city,
        state: currentPropertyAddress.region,
        postalCode: currentPropertyAddress.postalCode,
        name: bookmarkInputNameValue,
      },
    });
    const newBookmarks = await getUserBookmarks();
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        userBookmarks: newBookmarks,
      },
    });
    closeModal();
  };

  // Determine which star to show based on saved state and hover state
  const StarIcon =
    (isSaved && !isHovered) || (!isSaved && isHovered)
      ? StarFilled
      : StarOutlined;

  // Determine tooltip text based on saved state
  const tooltipText = isSaved ? 'Remove from bookmarks' : 'Add to bookmarks';

  const onToggle = async () => {
    try {
      // Immediately update UI state
      const newSavedState = !isSaved;
      setIsSaved(newSavedState);

      if (!newSavedState && currentBookmarkId) {
        // Remove bookmark
        await deleteBookmark({ id: currentBookmarkId });
        setCurrentBookmarkId(null);
      } else {
        // Add bookmark
        openModal();
      }
      // Refresh bookmarks list
    } catch (error) {
      // Revert UI state if the action failed
      setIsSaved(!isSaved);
      if (isSaved) {
        setCurrentBookmarkId(currentBookmarkId);
      }
      console.error('Error toggling bookmark:', error);
    }
  };

  useEffect(() => {
    if (currentPropertyAddress && userBookmarks) {
      // Check if current property exists in bookmarks
      const bookmarkedProperty = userBookmarks.find(
        (bookmark: any) =>
          bookmark.address === currentPropertyAddress.fullAddress &&
          bookmark.latitude === currentPropertyAddress.latitude &&
          bookmark.longitude === currentPropertyAddress.longitude,
      );

      setIsSaved(!!bookmarkedProperty);
      setCurrentBookmarkId(bookmarkedProperty?.id || null);
    }
  }, [currentPropertyAddress, userBookmarks]);

  return (
    <>
      <Tooltip title={tooltipText} className="relative">
        <div
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          onClick={onToggle}
          className="cursor-pointer text-yellow-400 hover:text-yellow-500 transition-colors relative"
          style={{ width: 'fit-content' }}
        >
          <StarIcon />
        </div>
      </Tooltip>
      <BookmarkNameInputModal
        title={'Create Bookmark Name'}
        state={bookmarkInputModalOpened}
        submitBookmarkFnc={submitBookmark}
        closeModal={closeModal}
      />
    </>
  );
};

export default BookmarkButton;

export interface BookmarkNameInputModalProps {
  submitBookmarkFnc: () => Promise<void>;
  closeModal: () => void;
  state: any;
  title: string;
}
export const BookmarkNameInputModal = ({
  submitBookmarkFnc,
  closeModal,
  state,
  title,
}: BookmarkNameInputModalProps) => {
  const dispatch = useDispatch();
  const bookmarkInputNameValue = useSelector(
    (state: any) => state.CMA.bookmarkInputNameValue,
  );
  const inputOnchange = (value: any) => {
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        bookmarkInputNameValue: value.target.value,
      },
    });
  };
  return (
    <Modal
      title={title}
      open={state}
      onOk={submitBookmarkFnc}
      onCancel={closeModal}
    >
      <Input
        placeholder=""
        value={bookmarkInputNameValue}
        onChange={inputOnchange}
      />
    </Modal>
  );
};
