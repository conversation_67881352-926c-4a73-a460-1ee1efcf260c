@import '../globalVars.css';

.appShortcutsButton {
  width: 40px;
  height: 40px;
  background: none;
  border: none;
  padding: 0;
}

.appShortcutsButtonSVG {
  width: 40px;
  height: 40px;
  fill: var(--antd-active-blue);
}

.appShortcutsAnchorWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 160px;
  height: 148px;
  padding: 16px 24px;
}

.appShortcutsAnchorWrapperMirror {
  composes: appShortcutsAnchorWrapper;
  padding: 16px 32px 16px 16px;
}

.appShortcutsAnchorWrapper:hover {
  background-color: rgba(0, 0, 0, .04);
}

.appShortcutsSVG {
  width: 48px;
  height: 48px;
}

.appShortcutsText {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.25;
  /* color: var(--antd-active-blue); */
  color: #333;
  text-align: center;
}

.appShortcutsHomeLinkText {
  composes: appShortcutsText;
  font-size: 16px;
  font-weight: 400;
  color: var(--antd-active-blue);
  margin-bottom: 16px;
}