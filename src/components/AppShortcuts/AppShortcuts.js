import { dateFormat, dateFormatAPI, dateFormatDatePicker } from '@/constants';
import userGroupAccess from '@/userGroupAccess.json';
import {
  Button,
  Col,
  DatePicker,
  Divider,
  Form,
  InputNumber,
  Modal,
  Popconfirm,
  Row,
  Select,
  Switch,
  Table,
} from 'antd';
import isEmpty from 'lodash.isempty';
import isEqual from 'lodash.isequal';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { connect, history, Link } from 'umi';
import { formatter, formatterKM } from '../../utils/money';
import appCardContent from './appCardContent.json';
import { appCardSVGIcons } from './appCardSVGIcons';
import styles from './appShortcuts.css';

let appShortcutsModalTop = 0,
  appShortcutsModalLeft = 0;

const AppShortcuts = (props) => {
  // get app title for current userGroup
  const getAppTitle = (appKey) => {
    const app = appCardContent.find((item) => item.key === appKey);
    if (app) {
      return app.title;
    } else {
      return '';
    }
  };

  // get abbreviations of current userGroup to use in URLs
  const getAbbrev = (selectedUserGroup) => {
    for (const userGroup in userGroupAccess) {
      if (userGroup === selectedUserGroup) {
        return userGroupAccess[userGroup].abbrev;
      }
    }
    // if no match, return empty string
    return '';
  };

  // generate userGroup specific app URLs
  const generateAppURL = (selectedUserGroup, appKey) => {
    const abbrev = getAbbrev(selectedUserGroup);
    // console.log('abbrev', abbrev);
    let appURL = '';
    if (!isInactive(selectedUserGroup, appKey)) {
      if (abbrev !== '') {
        if (['REMAXFine'].includes(selectedUserGroup)) {
          appURL = `https://aveone-${appKey}.locatealpha.com`;
        } else if (appKey === 'acqPortal') {
          if (['demo-users'].includes(selectedUserGroup)) {
            appURL = `https://${abbrev}-portfolio-${appKey}.locatealpha.com`;
          } else {
            appURL = `https://${abbrev}-${appKey}.locatealpha.com`;
          }
        } else if (appKey === 'Partner') {
          appURL = `https://${abbrev}-${appKey}.locatealpha.com/partner`;
        } else {
          appURL = `https://${abbrev}-${appKey}.locatealpha.com`;
        }
      } else {
        if (appKey === 'Partner') {
          appURL = `https://${appKey}.locatealpha.com/partner`;
        } else {
          appURL = `https://${appKey}.locatealpha.com`;
        }
      }
    }
    return appURL;
  };

  // generate home page URL
  const generateHomeURL = (selectedUserGroup) => {
    const abbrev = getAbbrev(selectedUserGroup);
    let homeURL = '';
    if (abbrev !== '') {
      homeURL = `https://${abbrev}.locatealpha.com`;
    } else {
      homeURL = `https://home.locatealpha.com`;
    }
    return homeURL;
  };

  // check if the app shortcut should be disabled
  const isInactive = (selectedUserGroup, appKey) => {
    if (
      userGroupAccess[selectedUserGroup] &&
      userGroupAccess[selectedUserGroup].app
    ) {
      return !userGroupAccess[selectedUserGroup].app.includes(appKey);
    } else {
      return true;
    }
  };

  const onCloseModal = () => {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        showAppShortcuts: false,
      },
    });
  };

  // get button position to position modal
  useEffect(() => {
    if (document.getElementById('appShortcutsButton')) {
      const appShortcutsButtonDOMRect = document
        .getElementById('appShortcutsButton')
        .getBoundingClientRect();
      appShortcutsModalTop =
        appShortcutsButtonDOMRect.top + appShortcutsButtonDOMRect.height + 8;
      appShortcutsModalLeft = appShortcutsButtonDOMRect.left;
      // console.log('appShortcutsButtonDOMRect', appShortcutsButtonDOMRect);
    }
  }, [props.selectedUserGroup]);

  return (
    <>
      <Modal
        key="app shortcuts modal"
        // getContainer={() => document.getElementById('appShortcutsButton')}
        open={props.showAppShortcuts}
        title={
          <span
            key="app shortcuts modal title"
            style={{ display: 'block', textAlign: 'center' }}
          >
            LocateAlpha Apps
          </span>
        }
        footer={null}
        mask={false}
        maskClosable={true}
        closable={false}
        onCancel={onCloseModal}
        width={320}
        style={{
          position: 'absolute',
          top: appShortcutsModalTop,
          left: appShortcutsModalLeft,
          borderRadius: 4,
        }}
        bodyStyle={{
          padding: 0,
        }}
      >
        <Row className={styles.appShortcuts} gutter={[0, 0]}>
          {appCardContent.map((item, index) => {
            return (
              <Col key={item.key} span={12}>
                <a
                  key="app shortcut anchor wrapper"
                  href={generateAppURL(props.selectedUserGroup, item.key)}
                  target={
                    ['ILE'].includes(props.selectedUserGroup)
                      ? '_self'
                      : '_blank'
                  }
                  rel="noopener noreferrer"
                  className={styles.appShortcutsAnchorWrapper}
                  style={{
                    cursor: isInactive(props.selectedUserGroup, item.key)
                      ? 'not-allowed'
                      : 'pointer',
                  }}
                >
                  <div
                    key="app shortcut icon wrapper"
                    className={styles.appShortcutsSVG}
                    style={{
                      fill: isInactive(props.selectedUserGroup, item.key)
                        ? 'rgba(0, 0, 0, 0.2)'
                        : '#34C759',
                    }}
                  >
                    {appCardSVGIcons[item.key]}
                  </div>
                  <div
                    key="app shortcut app name"
                    className={styles.appShortcutsText}
                  >
                    {getAppTitle(item.key)}
                  </div>
                </a>
              </Col>
            );
          })}
          <Divider style={{ margin: 0, marginBottom: 16 }} />
          <Col span={24}>
            <a
              key="app shortcuts home anchor"
              href={generateHomeURL(props.selectedUserGroup)}
              target={
                ['ILE'].includes(props.selectedUserGroup) ? '_self' : '_blank'
              }
              rel="noopener noreferrer"
            >
              <span
                key="app shortcuts home"
                className={styles.appShortcutsHomeLinkText}
                style={{ display: 'block', textAlign: 'center' }}
              >
                See all apps
              </span>
            </a>
          </Col>
        </Row>
      </Modal>
      <Button
        key="app shortcuts button"
        id="appShortcutsButton"
        type="text"
        onClick={() => {
          props.dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              showAppShortcuts: !props.showAppShortcuts,
            },
          });
        }}
        // className={styles.appShortcutsButton}
        style={{
          position: 'relative',
          width: 40,
          height: 40,
          padding: 0,
        }}
      >
        <div key="svg wrapper" className={styles.appShortcutsButtonSVG}>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48">
            <path d="M11.3 40q-1.4 0-2.35-.95Q8 38.1 8 36.7q0-1.4.95-2.35.95-.95 2.35-.95 1.4 0 2.35.95.95.95.95 2.35 0 1.4-.95 2.35-.95.95-2.35.95ZM24 40q-1.4 0-2.35-.95-.95-.95-.95-2.35 0-1.4.95-2.35.95-.95 2.35-.95 1.4 0 2.35.95.95.95.95 2.35 0 1.4-.95 2.35Q25.4 40 24 40Zm12.7 0q-1.4 0-2.35-.95-.95-.95-.95-2.35 0-1.4.95-2.35.95-.95 2.35-.95 1.4 0 2.35.95.95.95.95 2.35 0 1.4-.95 2.35-.95.95-2.35.95ZM11.3 27.3q-1.4 0-2.35-.95Q8 25.4 8 24q0-1.4.95-2.35.95-.95 2.35-.95 1.4 0 2.35.95.95.95.95 2.35 0 1.4-.95 2.35-.95.95-2.35.95Zm12.7 0q-1.4 0-2.35-.95-.95-.95-.95-2.35 0-1.4.95-2.35.95-.95 2.35-.95 1.4 0 2.35.95.95.95.95 2.35 0 1.4-.95 2.35-.95.95-2.35.95Zm12.7 0q-1.4 0-2.35-.95-.95-.95-.95-2.35 0-1.4.95-2.35.95-.95 2.35-.95 1.4 0 2.35.95.95.95.95 2.35 0 1.4-.95 2.35-.95.95-2.35.95ZM11.3 14.6q-1.4 0-2.35-.95Q8 12.7 8 11.3q0-1.4.95-2.35Q9.9 8 11.3 8q1.4 0 2.35.95.95.95.95 2.35 0 1.4-.95 2.35-.95.95-2.35.95Zm12.7 0q-1.4 0-2.35-.95-.95-.95-.95-2.35 0-1.4.95-2.35Q22.6 8 24 8q1.4 0 2.35.95.95.95.95 2.35 0 1.4-.95 2.35-.95.95-2.35.95Zm12.7 0q-1.4 0-2.35-.95-.95-.95-.95-2.35 0-1.4.95-2.35Q35.3 8 36.7 8q1.4 0 2.35.95.95.95.95 2.35 0 1.4-.95 2.35-.95.95-2.35.95Z" />
          </svg>
        </div>
      </Button>
    </>
  );
};

export default connect(({ CMA }) => ({
  // cbsaId: acqPortal.cbsaId,
  showAppShortcuts: CMA.showAppShortcuts,
  selectedUserGroup: CMA.selectedUserGroup,
}))(AppShortcuts);
