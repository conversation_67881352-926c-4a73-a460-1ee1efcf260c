import moment from 'moment';
import styles from './propertyDetailPopup.css';
import { showSFROperatorsFullName } from './SFRBrandConvertFunction';
import { formatter } from '../utils/money';
import { MAP_LAYER_NAME_BASE } from '../constants';
import { FaSearch, FaMinus } from 'react-icons/fa';

const dateFormat = 'YYYY-MM-DD';
let propertyType;
let MLSPopupImageSrc;
let isLeaseMode;

function PropertyDetailPopup({ hoverPropertyDetails }) {
  const propertyDetails = convertProperties(hoverPropertyDetails);

  let popupContent;
  propertyType = hoverPropertyDetails.type;
  MLSPopupImageSrc = hoverPropertyDetails.MLSPopupImageSrc;
  isLeaseMode = hoverPropertyDetails.isLeaseMode;

  switch (propertyType) {
    case MAP_LAYER_NAME_BASE.BTOwned:
      popupContent = createBTOwnedPopup(propertyDetails);
      break;
    case MAP_LAYER_NAME_BASE.hotPads:
    case MAP_LAYER_NAME_BASE.nationalOperator:
      popupContent = createNSFRPopup(propertyDetails);
      break;
    case MAP_LAYER_NAME_BASE.mls:
      popupContent = createMLSPopup(propertyDetails);
      break;
    case MAP_LAYER_NAME_BASE.padSplit:
      popupContent = createPadSplitPopup(propertyDetails);
      break;
    case MAP_LAYER_NAME_BASE.multiFamily:
      popupContent = createMultiFamilyPopup(propertyDetails);
      break;
    case MAP_LAYER_NAME_BASE.parcel:
      popupContent = createParcelPopup(propertyDetails);
      break;
    case MAP_LAYER_NAME_BASE.newbuilds:
      popupContent = createNewBuildsPopup(propertyDetails);
      break;
  }

  return <div id="property-detail-popup">{popupContent}</div>;
}

export default PropertyDetailPopup;

const createBasePropertyContent = (propertyDetails) => {
  return (
    <>
      <div className={styles.popupStat}>
        <div>
          <p>
            <span className={styles.popupValue}>{propertyDetails.Rent}</span>{' '}
            {isLeaseMode && propertyType != 'parcel'
              ? 'rent'
              : propertyDetails.Status === 'Closed' && propertyType != 'parcel'
              ? 'sold'
              : propertyType != 'parcel'
              ? 'sales'
              : 'rent'}{' '}
            -{' '}
            {propertyType != 'parcel' ? (
              propertyDetails.Status
            ) : (
              <>
                <span className={styles.popupValue}>
                  {propertyDetails.Sales}
                </span>{' '}
                sales
              </>
            )}
          </p>
        </div>
        <div>
          <p>
            <span className={styles.popupValue}>
              {propertyDetails.Beds || '-'}
            </span>{' '}
            Bds ·{' '}
            <span className={styles.popupValue}>
              {propertyDetails.Baths || '-'}
            </span>{' '}
            Ba ·{' '}
            <span className={styles.popupValue}>
              {propertyDetails.Sqft || '-'}
            </span>{' '}
            Sqft
          </p>
        </div>
      </div>
      <div className={styles.popupAddress}>
        <div>
          <p className={styles.popupValue}>{propertyDetails.Address[0]}</p>
        </div>
        <div>
          <p>{propertyDetails.Address[1]}</p>
        </div>
      </div>
    </>
  );
};

const createBTOwnedPopup = (propertyDetails) => {
  return (
    <div className={styles.popupContent}>
      {createBasePropertyContent(propertyDetails)}
      <div className={styles.popupStat} style={{ lineHeight: '18px' }}>
        <span className={styles.popupValue}>{propertyDetails.Owner}</span>
        <span>Type: {propertyDetails.Type}</span>
        <span>Yr. Built: {propertyDetails['Yr. Built']}</span>
        <span>Expiration: {propertyDetails['Exp.']}</span>
      </div>
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'center',
          gap: '10px',
        }}
      >
        <button
          id="locatePropertyButton"
          data-property-id={propertyDetails.unitid}
          data-property-type={propertyDetails.type}
          className={styles.locatePropertyButton}
        >
          <FaSearch />
        </button>
        {/* <button
          id="removePropertyButton"
          data-property-id={propertyDetails.unitid}
          data-property-type={propertyDetails.type}
          className={styles.removePropertyButton}
        >
          <FaMinus />
        </button> */}
      </div>
    </div>
  );
};

const createNSFRPopup = (propertyDetails) => {
  return (
    <div className={styles.popupContent}>
      {createBasePropertyContent(propertyDetails)}
      <div className={styles.popupStat} style={{ lineHeight: '18px' }}>
        <span className={styles.popupValue}>{propertyDetails.Owner}</span>
        <span>PSF: {propertyDetails.PSF}</span>
        <span>Available: {propertyDetails['Avail.']}</span>
        <span>Closed: {propertyDetails.Closed}</span>
      </div>
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'center',
          gap: '10px',
        }}
      >
        <button
          id="locatePropertyButton"
          data-property-id={propertyDetails.base_id}
          data-property-type={propertyDetails.type}
          className={styles.locatePropertyButton}
        >
          <FaSearch />
        </button>
        <button
          id="removePropertyButton"
          data-property-id={propertyDetails.base_id}
          data-property-type={propertyDetails.type}
          className={styles.removePropertyButton}
        >
          <FaMinus />
        </button>
      </div>
    </div>
  );
};

const createMLSPopup = (propertyDetails) => {
  console.log('propertyDetails', propertyDetails);
  return (
    <>
      <div className={styles.popupContainer}>
        {MLSPopupImageSrc && MLSPopupImageSrc != '' && (
          <>
            <div style={{ maxHeight: '150px', overflow: 'hidden' }}>
              <img
                className={styles.MLS_popupImage}
                src={MLSPopupImageSrc}
                alt=""
              />
            </div>
            <p
              style={{
                textAlign: 'center',
                margin: 0,
                padding: 0,
                lineHeight: '12px',
                marginTop: '5px',
              }}
            >
              Click to see images
            </p>
          </>
        )}
        <div className={styles.popupContent}>
          {createBasePropertyContent(propertyDetails)}
          <div className={styles.popupStatOther}>
            <div className={styles.popupStat} style={{ alignItems: 'center' }}>
              <span className={styles.popupValue}>
                {propertyDetails['Yr. Built']}
              </span>
              <span>Yr. Built</span>
            </div>
            <div className={styles.popupStat} style={{ alignItems: 'center' }}>
              <span className={styles.popupValue}>{propertyDetails.CDOM}</span>
              <span>CDOM</span>
            </div>
            <div className={styles.popupStat} style={{ alignItems: 'center' }}>
              <span className={styles.popupValue}>{propertyDetails.Type}</span>
              <span>Type</span>
            </div>
          </div>
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'center',
              gap: '10px',
            }}
          >
            <button
              id="locatePropertyButton"
              data-property-id={propertyDetails.mlsid}
              data-property-type={propertyDetails.type}
              className={styles.locatePropertyButton}
            >
              <FaSearch />
            </button>
            <button
              id="removePropertyButton"
              data-property-id={propertyDetails.mlsid}
              data-property-type={propertyDetails.type}
              className={styles.removePropertyButton}
            >
              <FaMinus />
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

const createPadSplitPopup = (propertyDetails) => {
  return (
    <div className={styles.popupContent}>
      <div className={styles.popupStat}>
        <div>
          <p
            style={{
              padding: '2px 0',
            }}
          >
            <span className={styles.popupValue}>
              {propertyDetails.currentActiveRoomsPrivateBath}bd / $
              {formatter(propertyDetails.currentAvgRoomsPrivateBathPrice)}
            </span>
            &nbsp;- Private Bath Avg. Rent
          </p>
          <p
            style={{
              padding: '2px 0',
            }}
          >
            <span className={styles.popupValue}>
              {propertyDetails.currentActiveRoomsSharedBath}bd / $
              {formatter(propertyDetails.currentAvgRoomsSharedBathPrice)}
            </span>
            &nbsp;- Shared Bath Avg. Rent
          </p>
        </div>
        <div>
          <p
            style={{
              padding: '2px 0',
            }}
          >
            <span className={styles.popupValue}>
              {propertyDetails.propertyBeds || '-'}
            </span>{' '}
            Bds ·{' '}
            <span className={styles.popupValue}>
              {propertyDetails.propertyBaths || '-'}
            </span>{' '}
            Ba ·{' '}
            <span className={styles.popupValue}>
              {propertyDetails.propertySqft || '-'}
            </span>{' '}
            Sqft
          </p>
        </div>
      </div>
      <div className={styles.popupAddress}>
        <div>
          <p
            style={{
              padding: '2px 0',
            }}
          >
            {propertyDetails.city}, {propertyDetails.state}{' '}
            {propertyDetails.zipCode}
          </p>
        </div>
      </div>
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'center',
          gap: '10px',
        }}
      >
        <button
          id="locatePropertyButton"
          data-property-id={propertyDetails.property_id}
          data-property-type={propertyDetails.property_type}
          className={styles.locatePropertyButton}
        >
          <FaSearch />
        </button>
        <button
          id="removePropertyButton"
          data-property-id={propertyDetails.property_id}
          data-property-type={propertyDetails.property_type}
          className={styles.removePropertyButton}
        >
          <FaMinus />
        </button>
      </div>
    </div>
  );
};

const createMultiFamilyPopup = (propertyDetails) => {
  return (
    <div className={styles.popupContent}>
      <div className={styles.popupStat}>
        <p>
          <span className={styles.popupValue}>{propertyDetails.Property}</span>
        </p>
        <p>
          Total{' '}
          <span className={styles.popupValue}>{propertyDetails.Total}</span>
        </p>
      </div>
      <div
        className={styles.popupStatOther}
        style={{ gap: '5px', justifyContent: 'center' }}
      >
        <div className={styles.popupStat} style={{ alignItems: 'center' }}>
          <span className={styles.popupValue}>
            {propertyDetails['2BR'] || '-'}
          </span>
          <span style={{ whiteSpace: 'nowrap' }}>2BR</span>
        </div>
        <div className={styles.popupDotContainer}>
          <span className={styles.popupValue}>·</span>
        </div>
        <div className={styles.popupStat} style={{ alignItems: 'center' }}>
          <span className={styles.popupValue}>
            {propertyDetails['2BR Avail.'] || '-'}
          </span>
          <span style={{ whiteSpace: 'nowrap' }}>2BR Avail.</span>
        </div>
        <div className={styles.popupDotContainer}>
          <span className={styles.popupValue}>·</span>
        </div>
        <div className={styles.popupStat} style={{ alignItems: 'center' }}>
          <span className={styles.popupValue}>
            {propertyDetails['2BR Rent'] || '-'}
          </span>
          <span style={{ whiteSpace: 'nowrap' }}>2BR Rent</span>
        </div>
        <div className={styles.popupDotContainer}>
          <span className={styles.popupValue}>·</span>
        </div>
        <div className={styles.popupStat} style={{ alignItems: 'center' }}>
          <span className={styles.popupValue}>
            {propertyDetails['2BR PSF'] || '-'}
          </span>
          <span style={{ whiteSpace: 'nowrap' }}>2BR PSF</span>
        </div>
      </div>
      <div
        className={styles.popupStatOther}
        style={{ gap: '5px', justifyContent: 'center' }}
      >
        <div className={styles.popupStat} style={{ alignItems: 'center' }}>
          <span className={styles.popupValue}>
            {propertyDetails['3BR'] || '-'}
          </span>
          <span style={{ whiteSpace: 'nowrap' }}>3BR</span>
        </div>
        <div className={styles.popupDotContainer}>
          <span className={styles.popupValue}>·</span>
        </div>
        <div className={styles.popupStat} style={{ alignItems: 'center' }}>
          <span className={styles.popupValue}>
            {propertyDetails['3BR Avail.'] || '-'}
          </span>
          <span style={{ whiteSpace: 'nowrap' }}>3BR Avail.</span>
        </div>
        <div className={styles.popupDotContainer}>
          <span className={styles.popupValue}>·</span>
        </div>
        <div className={styles.popupStat} style={{ alignItems: 'center' }}>
          <span className={styles.popupValue}>
            {propertyDetails['3BR Rent'] || '-'}
          </span>
          <span style={{ whiteSpace: 'nowrap' }}>3BR Rent</span>
        </div>
        <div className={styles.popupDotContainer}>
          <span className={styles.popupValue}>·</span>
        </div>
        <div className={styles.popupStat} style={{ alignItems: 'center' }}>
          <span className={styles.popupValue}>
            {propertyDetails['3BR PSF'] || '-'}
          </span>
          <span style={{ whiteSpace: 'nowrap' }}>3BR PSF</span>
        </div>
      </div>
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'center',
          gap: '10px',
        }}
      >
        <button
          id="locatePropertyButton"
          data-property-id={propertyDetails.uid}
          data-property-type={propertyDetails.type}
          className={styles.locatePropertyButton}
        >
          <FaSearch />
        </button>
      </div>
    </div>
  );
};

const createParcelPopup = (propertyDetails) => {
  return (
    <div className={styles.popupContent}>
      {createBasePropertyContent(propertyDetails)}

      <div className={styles.popupStat}>
        <span className={styles.popupValue}>
          {propertyDetails['Yr. Built']}
        </span>
        <span>Yr. Built</span>
      </div>
      <div className={styles.popupStat}>
        <span className={styles.popupValue}>{propertyDetails.Subdivision}</span>
        <span>Subdivision</span>
      </div>
    </div>
  );
};

const createNewBuildsPopup = (propertyDetails) => {
  return (
    <div style={{ padding: '10px', fontWeight: '500' }}>
      {propertyDetails.address && (
        <h4 style={{ fontSize: '14px', fontWeight: '600', margin: 0 }}>
          {propertyDetails.address}
        </h4>
      )}
      {propertyDetails.builder && propertyDetails.builder.length > 0 && (
        <p style={{ margin: 0 }}>Builder: {propertyDetails.builder}</p>
      )}
      {propertyDetails.first_seen && (
        <p style={{ margin: 0 }}>First Seen: {propertyDetails.first_seen}</p>
      )}
      <p style={{ margin: 0 }}>
        Status:{' '}
        {propertyDetails.status ? propertyDetails.status : 'Not Specified'}
      </p>
      {propertyDetails.price && propertyDetails.price > 0 && (
        <p style={{ margin: 0 }}>Price: ${formatter(propertyDetails.price)}</p>
      )}
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'center',
          gap: '10px',
        }}
      >
        <button
          id="locatePropertyButton"
          data-property-id={propertyDetails.base_id}
          data-property-type={propertyDetails.type}
          className={styles.locatePropertyButton}
        >
          <FaSearch />
        </button>
        <button
          id="removePropertyButton"
          data-property-id={propertyDetails.base_id}
          data-property-type={propertyDetails.type}
          className={styles.removePropertyButton}
        >
          <FaMinus />
        </button>
      </div>
    </div>
  );
};

const convertProperties = (record) => {
  let propertyDetails = {};
  switch (record.type) {
    case MAP_LAYER_NAME_BASE.BTOwned:
      propertyDetails.unitid = record.unitid;
      propertyDetails.type = record.type;
      propertyDetails.Address = [
        record.propertystreet1,
        record.propertycity +
          ', ' +
          record.propertystate +
          ' ' +
          record.propertyzip,
      ];
      // propertyDetails.Address2 = record.standard_city + ', ' + record.standard_state;
      propertyDetails.Status = record.status;
      propertyDetails.Owner = record.owners;
      propertyDetails.Rent =
        '$' + formatter(parseFloat(record.totalrent).toFixed());
      propertyDetails['Type'] = record.propertytype;
      propertyDetails['Yr. Built'] = record.yearbuilt;
      propertyDetails.Beds =
        record.bdba.split('/')[0] === '--' ? '-' : record.bdba.split('/')[0];
      propertyDetails.Baths =
        record.bdba.split('/')[1] === '--'
          ? '-'
          : (+record.bdba.split('/')[1]).toFixed(1);
      propertyDetails.Sqft = record.sqft;
      propertyDetails['Exp.'] = record.leaseexpirationdate
        ? moment(record.leaseexpirationdate).format(dateFormat)
        : '-';
      break;
    case MAP_LAYER_NAME_BASE.hotPads:
    case MAP_LAYER_NAME_BASE.nationalOperator:
      propertyDetails.base_id = record.base_id;
      propertyDetails.type = record.type;
      propertyDetails.Address = [
        record.address.includes(',')
          ? record.address.split(',')[0]
          : record.address,
        record.standard_city + ', ' + record.standard_state,
      ];
      // propertyDetails.Address2 = record.standard_city + ', ' + record.standard_state;
      propertyDetails.Status = record.exists ? 'Avail.' : 'Closed';
      // propertyDetails.Owner = showSFROperatorsFullNameOnly(record.brand);
      propertyDetails.Owner = showSFROperatorsFullName(record.brand);
      propertyDetails.Rent = '$' + formatter(parseFloat(record.rent).toFixed());
      propertyDetails.Beds = record.bed_rooms;
      propertyDetails.Baths = record.bath_rooms;
      propertyDetails.Sqft = record.square_feet;
      propertyDetails['PSF'] =
        record.rent && record.square_feet
          ? '$' + (record.rent / record.square_feet).toFixed(2)
          : '-';
      propertyDetails['Avail.'] = record.available_date
        ? moment(record.available_date).format(dateFormat)
        : '-';
      propertyDetails['Closed'] = record.close_date
        ? moment(record.close_date).format(dateFormat)
        : '-';
      break;
    case MAP_LAYER_NAME_BASE.mls:
      propertyDetails.mlsid = record.mlsid;
      propertyDetails.type = record.type;
      propertyDetails.Address = [
        record.fulladdress.replace(/  /g, ' '),
        record.city + ', ' + record.stateorprovince + ' ' + record.zipcode,
      ];
      propertyDetails.Status = record.status;
      propertyDetails.Rent =
        '$' + formatter(parseFloat(record.latestPrice).toFixed());
      propertyDetails['Type'] = [
        'Single Family Residence',
        'Single Family Detached',
      ].includes(record.propertysubtype)
        ? 'Single Family'
        : record.propertysubtype;
      propertyDetails['Yr. Built'] = record.yearbuilt;
      propertyDetails.Beds = record.bed;
      propertyDetails.Baths = record.bath;
      propertyDetails.Sqft = record.size ? formatter(record.size) : '-';
      propertyDetails['CDOM'] = record.cdom;
      break;
    case MAP_LAYER_NAME_BASE.multiFamily:
      propertyDetails.uid = record.uid;
      propertyDetails.type = record.type;
      // propertyDetails['Property'] = [record.property_name, <br />];
      propertyDetails['Property'] = record.property_name;
      propertyDetails['Total'] = record.total_relevant_units;
      propertyDetails['2BR'] = record.two_br_units;
      propertyDetails['2BR Avail.'] = record.two_br_avail;
      propertyDetails['2BR Rent'] = record.two_br_rent
        ? '$' + formatter(record.two_br_rent)
        : '-';
      propertyDetails['2BR PSF'] = record.two_br_rent_sf
        ? '$' + formatter(record.two_br_rent_sf)
        : '-';
      propertyDetails['3BR'] = record.three_br_units;
      propertyDetails['3BR Avail.'] = record.three_br_avail;
      propertyDetails['3BR Rent'] = record.three_br_rent
        ? '$' + formatter(record.three_br_rent)
        : '-';
      propertyDetails['3BR PSF'] = record.three_br_rent_sf
        ? '$' + formatter(record.three_br_rent_sf)
        : '-';
      break;
    case MAP_LAYER_NAME_BASE.parcel:
      propertyDetails.Address = [
        record.street_number +
          (record.street_prefix ? ' ' + record.street_prefix : '') +
          ' ' +
          record.street_name +
          (record.street_suffix ? ' ' + record.street_suffix : ''),
        record.city + ', ' + record.state + ' ' + record.zip_code,
      ];
      propertyDetails.Rent = '$' + formatter(parseFloat(record.rent).toFixed());
      propertyDetails.Sales =
        '$' + formatter(parseFloat(record.sales).toFixed());
      propertyDetails['Yr. Built'] = record.year_built;
      propertyDetails.Beds = record.beds_count;
      propertyDetails.Baths = record.baths;
      propertyDetails.Sqft = record.total_area_sq_ft
        ? formatter(+record.total_area_sq_ft)
        : '-'; // ensure size is of type number
      propertyDetails.Subdivision = record.subdivision;
      break;
    case MAP_LAYER_NAME_BASE.newbuilds:
      propertyDetails.base_id = record.base_id;
      propertyDetails.type = record.type;
      propertyDetails.address = record.address;
      propertyDetails.builder = record.builder;
      propertyDetails.price = record.price;
      propertyDetails.first_seen = record.first_seen;
      propertyDetails.status = record.status;
      break;
    case MAP_LAYER_NAME_BASE.padSplit:
      propertyDetails = record;
      break;
  }
  return propertyDetails;
};
