import { Row, Col } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import styles from './ResultTable/resultTable.css';

const EmptyState = (props) => {
  return (
    <Row
      key="empty state"
      justify="center"
      align="middle"
      className={styles.emptyTable}
      gutter={8}
    >
      <Col>
        <ExclamationCircleOutlined />
      </Col>
      <Col>
        <div>
          {props.description && props.description.length > 0
            ? props.description
            : 'No match properties within radius'}
        </div>
      </Col>
    </Row>
  );
};

export default EmptyState;
