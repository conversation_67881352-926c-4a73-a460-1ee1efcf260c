.div_search {
  width: 414px;
  /* height:299px; */
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.2);
}
.input {
  width: 300px;
  height: 44px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}
.text1 {
  height: 20px;
  font-size: 16px;
  /* font-family: 'IBM Plex Sans'; */
  font-style: normal;
  font-weight: 450;
  font-weight: normal;
  color: rgba(0, 122, 255, 1);
  line-height: 20px;
  cursor: pointer;
}
.text2 {
  /* width:114px; */
  height: 19px;
  font-size: 15px;
  /* font-family: 'IBM Plex Sans'; */
  font-style: normal;
  font-weight: 450;
  font-weight: normal;
  color: rgba(77, 77, 77, 1);
  line-height: 19px;
}
.text3 {
  /* width:36px; */
  height: 19px;
  font-size: 15px;
  /* font-family: 'IBM Plex Sans'; */
  font-style: normal;
  font-weight: 450;
  font-weight: normal;
  color: rgba(0, 122, 255, 1);
  line-height: 19px;
  text-align: center;
  cursor: pointer;
}
.text4 {
  width: 330px;
  height: 20px;
  font-size: 16px;
  /* font-family: 'IBM Plex Sans'; */
  font-style: normal;
  font-weight: 450;
  font-weight: normal;
  color: rgba(0, 0, 0, 1);
  line-height: 20px;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.text5 {
  width: 330px;
  height: 18px;
  font-size: 14px;
  /* font-family: 'IBM Plex Sans'; */
  font-style: normal;
  font-weight: 400;
  color: rgba(77, 77, 77, 1);
  line-height: 18px;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* .fullScreenMobile {
  position: absolute;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  padding: 16px;
  background: #fff;
} */

.autoCompleteInput {
  width: 250px;
  max-width: 40vw;
}
.autoCompleteInputFocus {
  width: 500px;
  max-width: 80vw;
}
.autoCompleteInputFullLengthMobile {
  /* width: 500px;
  max-width: 40vw; */
  width: 100%;
  font-size: 16px;
}
.autoCompleteInputFullLength {
  width: 500px;
  max-width: 40vw;
  /* width: 100%; */
  font-size: 16px;
}
.addressContentLine1 {
  /* width:calc(100% - 80px); */
  /* width: 420px; */
  /* height:20px; */
  font-size: 15px;
  /* font-family: 'IBM Plex Sans'; */
  font-style: normal;
  font-weight: 450;
  /* font-weight:normal; */
  color: rgba(0, 0, 0, 1);
  line-height: 20px;
  text-align: left;
  /* overflow: hidden; */
  /* text-overflow:ellipsis; */
  /* white-space: nowrap; */
  white-space: normal;
}
.addressContentLine2 {
  /* width:calc(100% - 28px); */
  /* width: 420px; */
  /* height:18px; */
  font-size: 14px;
  /* font-family: 'IBM Plex Sans'; */
  font-style: normal;
  font-weight: 400;
  color: rgba(77, 77, 77, 1);
  line-height: 18px;
  text-align: left;
  margin-top: 4px;
  /* overflow: hidden; */
  /* text-overflow:ellipsis; */
  /* white-space: nowrap; */
}

.addressContentLine1Mobile {
  composes: addressContentLine1;
  width: auto;
}

.addressContentLine2Mobile {
  composes: addressContentLine2;
  width: auto;
}

.buttonRow {
  padding: 24px 32px;
}

.tableDropdownBase {
  /* font-family: 'IBM Plex Sans'; */
  font-size: 14px;
  font-weight: 400;
}

.tableDropdownWide {
  composes: tableDropdownBase;
  width: 200px;
}

.tableDropdownMid {
  composes: tableDropdownBase;
  width: 150px;
}

.tableDropdownNarrow {
  composes: tableDropdownBase;
  width: 105px;
}

.clickToCopyString {
  color: #007aff;
  cursor: pointer;
}