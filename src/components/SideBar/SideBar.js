import React, { useState, useEffect } from 'react';
import { connect } from 'umi';
import { Row, Col, Button, Divider } from 'antd';
import isEqual from 'lodash.isequal';
import isEmpty from 'lodash.isempty';
import styles from './sideBar.css';
import sideBarButtons from './sideBar.json';
import { capitalize } from '../../utils/strings';
import MapLayerMenu from './MapLayerMenu';

const sideBarButtonGroups = [
  'School',
  'Admin',
  'Zone',
  'Flood',
  'POI',
  'Housing & Urban Development',
];

export const buttonIconSize = 24;

const SideBar = (props) => {
  const [shouldSideBarExpand, setShouldSideBarExpand] = useState(false);
  return (
    <div
      key="sidebar container"
      style={{
        position: 'absolute',
        width: shouldSideBarExpand ? 200 : buttonIconSize,
        // transition: 'width 0.3s ease-in-out',
        paddingLeft: '45%',
        zIndex: 1000,
      }}
    >
      <div
        key="sidebar button wrapper"
        style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'flex-start',
          alignItems: 'center',
          gap: 8,
          paddingTop: '8px',
          height: 'calc(100vh - 102px)',
          backgroundColor: shouldSideBarExpand
            ? // ? 'rgb(255 255 255 / 0.9)'
              '#f0f8ff'
            : 'rgb(0 0 0 / 0)',
          // boxShadow: shouldSideBarExpand
          //   ? '0px 0px 10px 0px rgb(0 0 0 / 50%)'
          //   : 'none',
        }}
        onMouseEnter={() => setShouldSideBarExpand(true)}
        onMouseLeave={() => setShouldSideBarExpand(false)}
      >
        <MapLayerMenu shouldSideBarExpand={shouldSideBarExpand} />
      </div>
    </div>
  );
};

export default connect(({ CMA }) => ({
  // showSentinelLayer: CMA.showSentinelLayer,
}))(SideBar);
