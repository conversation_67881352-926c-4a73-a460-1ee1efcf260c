import React, { useState, useEffect } from 'react';
import { connect } from 'umi';
import { Row, Col, Button, Divider } from 'antd';
import isEqual from 'lodash.isequal';
import isEmpty from 'lodash.isempty';
import styles from './sideBar.css';
import { capitalize } from '../../utils/strings';
import { buttonIconSize } from './SideBar';

import schoolDistrictsImg from '../../assets/images/mapbox/controls/schooldistricts.png';
import schoolAttendanceImg from '../../assets/images/mapbox/controls/schoolattendance.png';
import cbsaImg from '../../assets/images/mapbox/controls/cbsa.png';
import countyImg from '../../assets/images/mapbox/controls/county.png';
import zipcodeImg from '../../assets/images/mapbox/controls/ZIPcode.png';
import activityCentersImg from '../../assets/images/mapbox/controls/activitycenters.png';
import opportunityZonesImg from '../../assets/images/mapbox/controls/opportunityzones.png';
import floodZoneImg from '../../assets/images/mapbox/controls/floodzone.png';
import chainLocationsImg from '../../assets/images/mapbox/controls/chainlocations.png';
import hudPublicHousingImg from '../../assets/images/mapbox/controls/hudpublichousing.png';
import newHomesImg from '../../assets/images/mapbox/controls/newhomes.png';
import subdivisionImg from '../../assets/images/mapbox/controls/subdivision.png';
import neighborhoodImg from '../../assets/images/mapbox/controls/neighborhood.png';
import wetlandsImg from '../../assets/images/mapbox/controls/wetlands.png';
import powerlinesImg from '../../assets/images/mapbox/controls/powerlines.png';
import honlayerImg from '../../assets/images/mapbox/controls/honlayer.png';
import gorlickImg from '../../assets/images/mapbox/controls/gorlicklayer.png';
import trueholdImg from '../../assets/images/mapbox/controls/truehold-icon.jpg';
import trueholdGreyImg from '../../assets/images/mapbox/controls/truehold-grey-icon.jpg';
import drivetimeImg from '../../assets/images/mapbox/controls/drivetime.png';
import airbnbImg from '../../assets/images/mapbox/controls/airbnb.png';
import institutionalOwnerImg from '../../assets/images/mapbox/controls/institutionalowners.png';
import multiFamilyImg from '../../assets/images/mapbox/controls/multi_family_icon.png';
import fundriseIcon from '../../assets/images/mapbox/controls/fundrise-icon.png';
import waterDistrictIcon from '../../assets/images/mapbox/controls/waterdistrict.png';
import railNetworkIcon from '../../assets/images/mapbox/controls/railnetwork.png';
import BridgeTowerIcon from '../../assets/images/mapbox/controls/BridgeTower-icon.png';
import CityIcon from '../../assets/images/mapbox/controls/city_icon.png';
import prpIcon from '../../assets/images/mapbox/controls/prp.png';

const SCHOOL_DISTRICTS = 'school districts';
const SCHOOL_ATTENDANTS = 'school zones';
const ACTIVITY_CENTERS = 'activity centers';
const COUNTY = 'county';
const ZIPCODE = 'ZIP code';
const OPPORTUNITY_ZONES = 'opportunity zones';
const CBSA = 'cbsa';
const CITY = 'city';
const FLOOD_ZONES = 'flood zone';
const WETLANDS = 'wetlands';
const CHAIN_LOCATIONS = 'chain stores';
const NEW_HOMES = 'new construction';
const HUD_PUBLIC_HOUSING = 'public housing';
const SUBDIVISION = 'subdivision';
const NEIGHBORHOOD = 'neighborhood';
const POWER_LINES = 'power lines';
const HON_OWNED = 'hon owned';
const GORLICK_OWNED = 'gorlick owned';
const TRUEHOLD_OWNED = 'truehold owned';
const TRUEHOLD_UNDERWRITTEN = 'truehold underwritten';
const TRUEHOLD_TARGET = 'truehold target';
const DRIVE_TIME = 'drive time';
const AIRBNB = 'airbnb';
const INSTITUTIONAL_OWNERS = 'institutional owners';
const MULTI_FAMILY = 'multi family';
const FUNDRISE_PROPERTIES = 'fundrise properties';
const WATER_DISTRICT = 'municipal water district';
const RAIL_NETWORK = 'rail network';
const BRIDGE_TOWER_ICON = 'Bridge Tower BTR';
const BLUERIVER_PRP = 'public record prospect';

const imageSrc = {};
imageSrc[SCHOOL_DISTRICTS] = schoolDistrictsImg;
imageSrc[SCHOOL_ATTENDANTS] = schoolAttendanceImg;
imageSrc[ACTIVITY_CENTERS] = activityCentersImg;
imageSrc[COUNTY] = countyImg;
imageSrc[ZIPCODE] = zipcodeImg;
imageSrc[OPPORTUNITY_ZONES] = opportunityZonesImg;
imageSrc[CBSA] = cbsaImg;
imageSrc[CITY] = CityIcon;
imageSrc[FLOOD_ZONES] = floodZoneImg;
imageSrc[WETLANDS] = wetlandsImg;
imageSrc[CHAIN_LOCATIONS] = chainLocationsImg;
imageSrc[HUD_PUBLIC_HOUSING] = hudPublicHousingImg;
imageSrc[NEW_HOMES] = newHomesImg;
imageSrc[SUBDIVISION] = subdivisionImg;
imageSrc[NEIGHBORHOOD] = neighborhoodImg;
imageSrc[POWER_LINES] = powerlinesImg;
imageSrc[HON_OWNED] = honlayerImg;
imageSrc[TRUEHOLD_OWNED] = trueholdImg;
imageSrc[TRUEHOLD_UNDERWRITTEN] = trueholdGreyImg;
imageSrc[TRUEHOLD_TARGET] = trueholdImg;
imageSrc[GORLICK_OWNED] = gorlickImg;
imageSrc[DRIVE_TIME] = drivetimeImg;
imageSrc[AIRBNB] = airbnbImg;
imageSrc[INSTITUTIONAL_OWNERS] = institutionalOwnerImg;
imageSrc[MULTI_FAMILY] = multiFamilyImg;
imageSrc[FUNDRISE_PROPERTIES] = fundriseIcon;
imageSrc[WATER_DISTRICT] = waterDistrictIcon;
imageSrc[RAIL_NETWORK] = railNetworkIcon;
imageSrc[BRIDGE_TOWER_ICON] = BridgeTowerIcon;
imageSrc[BLUERIVER_PRP] = prpIcon;

const MapLayerMenu = connect(({ CMA }) => ({
  currentMapLayerOptions: CMA.currentMapLayerOptions,
  map: CMA.map,
  userGroup: CMA.userGroup,
}))(function (props) {
  const schoolType = [SCHOOL_DISTRICTS, SCHOOL_ATTENDANTS];
  const adminType = [CBSA, COUNTY, CITY, ZIPCODE, SUBDIVISION, NEIGHBORHOOD];
  const zoneType = [ACTIVITY_CENTERS, OPPORTUNITY_ZONES, DRIVE_TIME];
  const floodType = [FLOOD_ZONES, WETLANDS, WATER_DISTRICT];
  const poiType = [
    // CHAIN_LOCATIONS,
    // NEW_HOMES,
    AIRBNB,
    INSTITUTIONAL_OWNERS,
    MULTI_FAMILY,
  ];
  const hudType = [HUD_PUBLIC_HOUSING, POWER_LINES, RAIL_NETWORK];
  const ownedType = [];

  if (props.userGroup.includes('BlueRiver')) {
    poiType.push(BLUERIVER_PRP);
  }

  if (props.userGroup.includes('HON')) {
    ownedType.push(HON_OWNED);
  } else if (
    props.userGroup.includes('BridgeAdmin') ||
    props.userGroup.includes('BridgeFull')
  ) {
    ownedType.push(GORLICK_OWNED);
  } else if (props.userGroup.includes('Truehold')) {
    ownedType.push(TRUEHOLD_OWNED);
    ownedType.push(TRUEHOLD_UNDERWRITTEN);
    ownedType.push(TRUEHOLD_TARGET);
  } else if (props.userGroup.includes('Fundrise')) {
    ownedType.push(FUNDRISE_PROPERTIES);
  } else if (props.userGroup.includes('BridgeTower')) {
    ownedType.push(BRIDGE_TOWER_ICON);
  }

  const mapLayerOptions = [...props.currentMapLayerOptions];

  if (!props.userGroup.includes('BridgeTower')) {
    mapLayerOptions.push(ownedType[0]);
  }

  useEffect(() => {
    if (!props.map) return;
    // on mount, the map sets owned layer on by default
    if (!props.userGroup.includes('Fundrise') && ownedType.length > 0) {
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          currentMapLayerOptions: mapLayerOptions,
        },
      });
    }
  }, [props.map]);

  const handleSelectLayerImg = (e) => {
    e.stopPropagation();
    handleLayerOptions(e.currentTarget.value);
  };

  const handleLayerOptions = (clickedLayer) => {
    if (
      props.currentMapLayerOptions instanceof Array &&
      !props.currentMapLayerOptions.includes(clickedLayer)
    ) {
      // Determine which layer type and get related layers
      // const relatedLayers = findWhichLayerType(clickedLayer);

      // Remove any previous layer of same type
      // const selectedLayer = [
      //   ...props.currentMapLayerOptions.filter(
      //     (layer) => !relatedLayers.includes(layer),
      //   ),
      // ];
      const selectedLayer = [...props.currentMapLayerOptions];
      selectedLayer.push(clickedLayer);

      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          currentMapLayerOptions: selectedLayer,
        },
      });

      props.map.fire('mapLayers.currentMapLayerOptions', {
        payload: {
          currentMapLayerOptions: selectedLayer,
        },
      });

      // fetchLayerTypesData(props, clickedLayer);
    } else {
      // if icon layer clicked itself
      const currentMapLayerOptions = [
        ...props.currentMapLayerOptions.filter(
          (layer) => layer !== clickedLayer,
        ),
      ];
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          currentMapLayerOptions: currentMapLayerOptions,
        },
      });
      props.map.fire('mapLayers.currentMapLayerOptions', {
        payload: {
          currentMapLayerOptions: currentMapLayerOptions,
        },
      });
    }
  };

  const findWhichLayerType = (clickedLayer) => {
    if (schoolType.includes(clickedLayer)) return schoolType;
    if (adminType.includes(clickedLayer)) return adminType;
    if (zoneType.includes(clickedLayer)) return zoneType;
    if (floodType.includes(clickedLayer)) return floodType;
    if (poiType.includes(clickedLayer)) return poiType;
    if (hudType.includes(clickedLayer)) return hudType;
    if (ownedType.includes(clickedLayer)) return ownedType;
  };

  const isLayerActive = (layer) => {
    return (
      Array.isArray(props.currentMapLayerOptions) &&
      props.currentMapLayerOptions.includes(layer)
    );
  };

  const getTypeIcons = (typesData) => {
    return typesData.map((layer) => {
      let layerName = capitalize(layer);
      if (layer === 'cbsa') layerName = layer.toUpperCase();
      if (layer === HON_OWNED) layerName = 'HON Owned';
      if (layer === GORLICK_OWNED) layerName = 'Bridge Owned';
      if (layer === TRUEHOLD_OWNED) layerName = 'Truehold Owned';
      if (layer.includes('osm')) layerName = layerName.replace('Osm', 'OSM');
      if (layer.includes('osm residential polygons'))
        layerName = layerName.replace('Polygons', 'Land');
      if (layer === 'Bridge Tower BTR') layerName = 'BTR';

      return (
        <button
          key={layer}
          className={`${styles.typeImgBtn} ${
            props.currentMapLayerOptions instanceof Array &&
            props.currentMapLayerOptions.includes(layer)
              ? styles.layerMenuImgItemActive
              : ''
          }`}
          value={layer}
          onClick={handleSelectLayerImg}
          style={{
            width: props.shouldSideBarExpand ? 200 : buttonIconSize,
            height: buttonIconSize,
            background: 'rgba(255, 255, 255, 0)',
            padding: 0,
            border: 'none',
            display: 'flex',
            justifyContent: 'flex-start',
            alignItems: 'center',
            gap: 8,
            cursor: 'pointer',
          }}
        >
          <div
            key={layer + 'button icon wrapper'}
            style={{
              width: buttonIconSize,
              height: buttonIconSize,
              border: isLayerActive(layer)
                ? '2px solid rgb(22, 70, 133)'
                : 'none',
              borderRadius: 8,
              overflow: 'hidden',
            }}
          >
            <img
              src={imageSrc[layer]}
              width={isLayerActive(layer) ? buttonIconSize - 4 : buttonIconSize}
              height={
                isLayerActive(layer) ? buttonIconSize - 4 : buttonIconSize
              }
            />
          </div>
          {props.shouldSideBarExpand && (
            <span
              key={layer + 'button text'}
              className={styles.sideBarButtonText}
            >
              {layerName}
            </span>
          )}
        </button>
      );
    });
  };

  return (
    <>
      {ownedType.length > 0 && (
        <>
          {getTypeIcons(ownedType)}
          <Divider key="ownedType divider" style={{ margin: '4px 0' }} />
        </>
      )}
      {getTypeIcons(schoolType)}
      <Divider key="schoolType divider" style={{ margin: '4px 0' }} />
      {getTypeIcons(adminType)}
      <Divider key="adminType divider" style={{ margin: '4px 0' }} />
      {getTypeIcons(zoneType)}
      <Divider key="zoneType divider" style={{ margin: '4px 0' }} />
      {getTypeIcons(floodType)}
      <Divider key="floodType divider" style={{ margin: '4px 0' }} />
      {getTypeIcons(poiType)}
      <Divider key="poiType divider" style={{ margin: '4px 0' }} />
      {getTypeIcons(hudType)}
    </>
  );
});

export default MapLayerMenu;
