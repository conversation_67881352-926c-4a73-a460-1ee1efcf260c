import { useEffect, useState } from 'react';
import { useSelector } from 'umi';
import { FaExclamationTriangle } from 'react-icons/fa';
import { Month } from './LandMonthlyImages';
import {
  MapSourceDataEvent,
  Map,
  VectorSource,
  RasterSource,
  AnyLayer,
} from 'mapbox-gl';
import { getMonthlyImagesURL } from '../../services/data';
import { getUserToken } from '@/utils/auth';

const sourceId = 'landMonthlyImages';

export const minZoom = 14;
const getLayerStyle = (sourceId: string): AnyLayer => {
  return {
    id: `${sourceId}LayerStyle`,
    type: 'raster',
    source: sourceId,
    minzoom: minZoom,
    maxzoom: minZoom + 1,
    paint: {
      'raster-opacity': 1,
    },
  };
};

export const getURL = (year: string, month: string, token: string) => {
  const mosaic_name = `global_monthly_${year}_${month}_mosaic`;
  return [
    getMonthlyImagesURL({ tile: 'tiles0', mosaic_name: mosaic_name, token }),
    getMonthlyImagesURL({ tile: 'tiles1', mosaic_name: mosaic_name, token }),
    getMonthlyImagesURL({ tile: 'tiles2', mosaic_name: mosaic_name, token }),
    getMonthlyImagesURL({ tile: 'tiles3', mosaic_name: mosaic_name, token }),
  ];
};

export const MapLayer = ({
  months,
  currentMonth,
}: {
  months: Month[];
  currentMonth: null | Month;
}) => {
  const map = useSelector((state: any) => state.CMA.map) as Map;
  const appViewMode = useSelector((state: any) => state.CMA.appViewMode);
  const currentMapThemeOption = useSelector(
    (state: any) => state.CMA.currentMapThemeOption,
  );

  const [currentSourceId, setCurrentSourceId] = useState<string | null>(null);
  const [token, setToken] = useState<string | null>(null);

  useEffect(() => {
    if (!map) return;

    if (
      appViewMode.mode === 'LandDevelopment' &&
      !appViewMode.subMode.some((mode: string) => mode == 'LandMonthlyImages')
    ) {
      const maxZoom = map.getMaxZoom();
      if (maxZoom != 22) map.setMaxZoom(22);
      return;
    }

    const maxZoom = map.getMaxZoom();
    if (maxZoom >= minZoom + 1) map.setMaxZoom(minZoom + 1 - 0.01);

    const styleLoad = () => {
      const planetSatelliteLayer = map.getLayer(getLayerStyle(sourceId).id);
      if (planetSatelliteLayer) {
        const layers = map.getStyle().layers;
        const roadPrimary = layers.find((l: any) => l.id === 'road-primary');

        map.moveLayer(
          getLayerStyle(sourceId).id,
          roadPrimary ? roadPrimary.id : 'road-simple',
        );
      }
    };

    const moveEnd = async () => {
      const newToken = await getUserToken('id');
      if (token !== newToken) {
        setToken(newToken);
      }
    };

    styleLoad();
    moveEnd();
    map.on('moveend', moveEnd);
    map.on('zoomend', () => console.log('zoom', map.getZoom()));
    map.on('style.load', styleLoad);
    return () => {
      // if (token) setToken(null);
      map.off('style.load', styleLoad);
      map.off('moveend', moveEnd);
      const maxZoom = map.getMaxZoom();
      if (maxZoom != 22) map.setMaxZoom(22);
    };
  }, [map, currentMapThemeOption, appViewMode, token]);

  useEffect(() => {
    if (!map || !currentMonth || !token) return;

    // if (currentSourceId) {
    //   const source = map.getSource(currentSourceId) as RasterSource;
    //   source.
    // }
    const newSourceId = `${sourceId}-${crypto.randomUUID()}`;
    map.addSource(newSourceId, {
      type: 'raster',
      tiles: getURL(currentMonth.year, currentMonth.month, token),
      // tileSize: 256,
    });
    const layers = map.getStyle().layers;
    const roadPrimary = layers.find((l: any) => l.id === 'road-primary');
    map.addLayer(
      getLayerStyle(newSourceId),
      roadPrimary ? roadPrimary.id : 'road-simple',
    );

    // idle is slower but works more reliable than sourcedata event
    const idle = () => {
      if (!currentSourceId) {
        setCurrentSourceId(newSourceId);
      } else if (currentSourceId && currentSourceId != newSourceId) {
        if (map.getSource(currentSourceId)) {
          map.removeLayer(getLayerStyle(currentSourceId).id);
          map.removeSource(currentSourceId);
        }
        setCurrentSourceId(newSourceId);
      }
      map.off('idle', idle);
    };
    map.once('idle', idle);
  }, [map, currentMonth, token]);

  useEffect(() => {
    return () => {
      if (currentSourceId && map.getSource(currentSourceId)) {
        map.removeLayer(getLayerStyle(currentSourceId).id);
        map.removeSource(currentSourceId);
      }
    };
  }, [map, currentSourceId]);

  // if (!map || appViewMode != 'LandMonthlyImages' || !currentMonth) return null;
  return null;
};

export const ZoomTooltip = () => {
  const map = useSelector((state: any) => state.CMA.map);

  const [zoom, setZoom] = useState(map ? map.getZoom() : 0);

  useEffect(() => {
    if (!map) return;

    const zoomEnd = () => {
      setZoom(map.getZoom());
    };

    zoomEnd();
    map.on('zoomend', zoomEnd);
    return () => {
      map.off('zoomend', zoomEnd);
    };
  }, [map]);

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'center',
      }}
    >
      <div
        style={{
          background: 'white',
          padding: '4px 8px',
          borderRadius: '8px',
          display: 'flex',
          flexDirection: 'row',

          alignItems: 'center',
          gap: '4px',
          boxShadow:
            '0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)',
        }}
      >
        <FaExclamationTriangle color="#fdb100f5" size={18} />
        <span>
          Monthly images are visible at zoom {minZoom}. Current zoom is{' '}
          {Math.floor(zoom)}
        </span>
      </div>
    </div>
  );
};
