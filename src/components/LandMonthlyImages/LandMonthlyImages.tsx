import { Fragment, useCallback, useEffect, useState } from 'react';
import { MapLayer, ZoomTooltip, getURL } from './Map';
import { useDispatch, useSelector } from 'umi';
import {
  HorizontalTimeline,
  TimelineButton,
  TimelineItem,
  TimelinePadding,
  TimeLineSeparator,
} from './HorizontalTimeline';
import moment from 'moment';
import { usePrevious } from '@/hooks';
import { Button } from 'antd';
import { motion } from 'framer-motion';
import { FaChevronUp, FaChevronDown } from 'react-icons/fa';
import { getUserToken } from '@/utils/auth';

import { minZoom } from './Map';

export type Month = {
  label: string;
  month: string;
  year: string;
};

const findTheLastAvailableMonth = async () => {
  let monthMinus = 1;
  while (monthMinus <= 5) {
    const m = moment().subtract(monthMinus, 'months');

    const month = (m.get('month') + 1).toString().padStart(2, '0');
    const year = m.get('year').toString();

    const url = getURL(year, month, (await getUserToken('id')) as string)[0]
      .replace('{z}', '16')
      .replace('{x}', '15168')
      .replace('{y}', '26368');
    // const response = await fetch(url, {
    //   headers: {
    //     Authorization: `Bearer ${await getUserToken('id')}`,
    //   },
    // });
    const response = await fetch(url);
    if (response.status === 200) {
      return monthMinus - 1;
    } else {
      monthMinus++;
    }
  }
};

const getMonths = (minus?: number | undefined): Month[] => {
  const months = [];
  for (let i = 0; i <= 24; i++) {
    const m = moment().subtract(minus != undefined ? i + minus : i, 'months');
    months.push({
      label: m.format('MMM'),
      month: (m.get('month') + 1).toString().padStart(2, '0'),
      year: m.get('year').toString(),
    });
  }

  return months.reverse();
};

const LandMonthlyImages = () => {
  const map = useSelector((state: any) => state.CMA.map);
  const appViewMode = useSelector((state: any) => state.CMA.appViewMode);
  const currentMapThemeOption = useSelector(
    (state: any) => state.CMA.currentMapThemeOption,
  );
  const dispatch = useDispatch();

  const [months, setMonths] = useState(getMonths());
  const [currentMonth, setCurrentMonth] = useState<null | Month>(null);
  const [showZoomToolip, setShowZoomTooltip] = useState(false);

  const appViewModePrev = usePrevious(appViewMode);
  const [exitMapThemeOption, setExitMapThemeOption] = useState<string>(
    currentMapThemeOption,
  );

  const [showParcelLayer, setShowParcelLayer] = useState(true);
  const [showParcelBoundaryLayer, setShowParcelBoundaryLayer] = useState(true);

  const [showToolbar, setShowToolbar] = useState(true);

  const [foundLastAvailableMonth, setFoundLastAvailableMonth] = useState(false);

  useEffect(() => {
    if (foundLastAvailableMonth) return;
    if (appViewMode.mode != 'LandDevelopment') return;
    if (
      appViewMode.mode == 'LandDevelopment' &&
      !appViewMode.subMode.includes('LandMonthlyImages')
    )
      return;

    const fetchMonths = async () => {
      const monthMinus = await findTheLastAvailableMonth();
      const newMonths = getMonths(monthMinus);
      setMonths(newMonths);
      setFoundLastAvailableMonth(true);
    };
    fetchMonths();
  }, [appViewMode]);

  useEffect(() => {
    setCurrentMonth(months[months.length - 2]);
  }, [months]);

  useEffect(() => {
    // ideally want to do this in Map.tsx, but this parent component returns null if appViewMode is not 'LandMonthlyImages'
    if (!map) return;

    // console.log(map._requestManager._transformRequestFn.toString());

    const sentinel = document.querySelector(
      '#MapSentinelLayerControl',
    ) as HTMLElement;

    if (
      appViewMode.mode === 'LandDevelopment' &&
      appViewMode.subMode[appViewMode.subMode.length - 1] ===
        'LandMonthlyImages'
    ) {
      if (
        appViewMode.subMode[appViewMode.subMode.length - 1] !==
        appViewModePrev.subMode[appViewModePrev.subMode.length - 1]
      ) {
        setExitMapThemeOption(currentMapThemeOption);
      }

      if (currentMapThemeOption != 'Satellite') {
        map.fire('map.setThemeOption', {
          payload: {
            currentMapThemeOption: 'Satellite',
          },
        });
      }

      if (sentinel) {
        sentinel.style.display = 'none';
      }
      if (showParcelLayer) toggleParcelLayer();
      if (showParcelBoundaryLayer) toggleParcelBoundaryLayer();
    } else {
      if (exitMapThemeOption != 'Satellite') {
        map.fire('map.setThemeOption', {
          payload: {
            currentMapThemeOption: exitMapThemeOption,
          },
        });
      }

      if (sentinel) {
        sentinel.style.display = 'block';
      }

      if (!showParcelLayer) toggleParcelLayer();
      if (!showParcelBoundaryLayer) toggleParcelBoundaryLayer();
    }
  }, [map, appViewMode, currentMapThemeOption]);

  useEffect(() => {
    if (!map) return;

    if (!appViewMode.subMode.includes('LandMonthlyImages')) {
      if (showZoomToolip) setShowZoomTooltip(false);
      return;
    }

    const zoomEnd = () => {
      const zoom = map.getZoom();
      if (zoom < minZoom && !showZoomToolip) {
        setShowZoomTooltip(true);
      } else if (zoom >= minZoom && showZoomToolip) {
        setShowZoomTooltip(false);
      }
    };

    zoomEnd();
    map.on('zoomend', zoomEnd);
    return () => {
      map.off('zoomend', zoomEnd);
    };
  }, [map, showZoomToolip, appViewMode]);

  const toggleParcelLayer = useCallback(() => {
    if (!map) return;
    map.fire('layers.updateShowParcelLayer', {
      payload: { showParcelLayer: !showParcelLayer },
    });
    setShowParcelLayer(!showParcelLayer);
  }, [map, showParcelLayer]);

  const toggleParcelBoundaryLayer = useCallback(() => {
    if (!map) return;
    map.fire('layers.updateShowParcelBoundaryLayer', {
      payload: { showParcelBoundaryLayer: !showParcelBoundaryLayer },
    });
    setShowParcelBoundaryLayer(!showParcelBoundaryLayer);
  }, [map, showParcelBoundaryLayer]);

  if (
    appViewMode.mode != 'LandDevelopment' ||
    (appViewMode.mode == 'LandDevelopment' &&
      appViewMode.subMode[appViewMode.subMode.length - 1] !=
        'LandMonthlyImages')
  ) {
    return null;
  }
  return (
    <div>
      <MapLayer months={months} currentMonth={currentMonth} />

      <div
        style={{
          position: 'absolute',
          top: '8px',
          left: '50%',
          transform: 'translateX(-50%)',
        }}
      >
        <motion.div
          initial={false} // Disable initial animation
          animate={{
            opacity: showToolbar ? 1 : 0, // Fade in when true, out when false
            y: showToolbar ? 0 : -50, // Move down when true, up when false
          }}
          transition={{ duration: 0.5 }} // Duration of the animation
        >
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              gap: '4px',
              backgroundColor: 'white',
              boxShadow:
                'rgba(0, 0, 0, 0.2) 0px 4px 8px 0px, rgba(0, 0, 0, 0.19) 0px 6px 20px 0px)',
              borderRadius: '8px',
              padding: '4px 8px',
              border: '1px solid #d9d9d9',
            }}
          >
            <div>
              <Button
                onClick={() => toggleParcelLayer()}
                style={{ borderRadius: '8px' }}
              >
                {showParcelLayer ? 'Hide Parcels' : 'Show Parcels'}
              </Button>
            </div>
            <div>
              <Button
                onClick={() => toggleParcelBoundaryLayer()}
                style={{ borderRadius: '8px' }}
              >
                {showParcelBoundaryLayer
                  ? 'Hide Parcel Boundaries'
                  : 'Show Parcel Boundaries'}
              </Button>
            </div>
          </div>
        </motion.div>
        <motion.div
          animate={{
            // opacity: showToolbar ? 1 : 0, // Fade in when true, out when false
            x: showToolbar ? 0 : '-175px', // Move down when true, up when false
          }}
          transition={{ duration: 0.5 }} // Duration of the animation
          style={{
            position: 'absolute',
            right: '-40px',
            top: '5px',
            // transform: 'translateY(-50%)',
          }}
        >
          <Button
            shape="circle"
            size="small"
            onClick={() => setShowToolbar(!showToolbar)}
            style={{ position: 'relative' }}
          >
            {!showToolbar ? (
              <FaChevronUp
                size={18}
                style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%,-50%)',
                }}
              />
            ) : (
              <FaChevronDown
                size={18}
                style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%,-50%)',
                }}
              />
            )}
          </Button>
        </motion.div>
      </div>
      <div
        style={{
          position: 'absolute',
          bottom: '24px',
          left: '50%',
          transform: 'translateX(-50%)',
          width: '60%',
          maxWidth: 'fit-content',
          display: 'flex',
          flexDirection: 'column',
          gap: '16px',
          justifyContent: 'center',
        }}
      >
        {showZoomToolip && <ZoomTooltip />}
        <HorizontalTimeline
          onClose={() =>
            dispatch({
              type: 'CMA/saveCMAStates',
              payload: {
                appViewMode:
                  appViewMode.subMode.length === 1
                    ? {
                        mode: 'CMA',
                        subMode: [],
                      }
                    : {
                        mode: appViewMode.mode,
                        subMode: appViewMode.subMode.filter(
                          (mode: string) => mode !== 'LandMonthlyImages',
                        ),
                      },
              },
            })
          }
        >
          {months.map((m, index) => (
            <Fragment key={index}>
              {index === 0 && <TimelinePadding />}
              {(index === 0 || m.year != months[index - 1].year) && (
                <TimeLineSeparator label={m.year} />
              )}
              <TimelineItem label={`${m.label}`}>
                {index != months.length - 1 && (
                  <TimelineButton
                    active={
                      currentMonth &&
                      currentMonth.month === m.month &&
                      currentMonth.year === m.year
                        ? true
                        : false
                    }
                    onClick={() => setCurrentMonth(m)}
                  />
                )}
              </TimelineItem>
            </Fragment>
          ))}
        </HorizontalTimeline>
      </div>
    </div>
  );
};

export default LandMonthlyImages;
