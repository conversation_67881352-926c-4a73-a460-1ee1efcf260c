import { Button } from 'antd';
import {
  ElementRef,
  ReactChildren,
  ReactNode,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import { FaChevronLeft, FaChevronRight, FaTimes } from 'react-icons/fa';

export const HorizontalTimeline = ({
  onClose,
  children,
}: {
  onClose: () => void;
  children: ReactNode;
}) => {
  const [mouseDown, setMouseDown] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);
  const [isLeftEnd, setIsLeftEnd] = useState(false);
  const [isRightEnd, setIsRightEnd] = useState(false);

  const sliderRef = useRef<ElementRef<'div'>>(null);

  useEffect(() => {
    if (!sliderRef.current) return;

    const maxScroll =
      sliderRef.current.scrollWidth - sliderRef.current.offsetWidth;
    sliderRef.current.scrollLeft = maxScroll;
    setScrollLeft(maxScroll);

    const updateEnds = () => {
      if (!sliderRef.current) return;
      const maxScroll =
        sliderRef.current.scrollWidth - sliderRef.current.clientWidth;
      setIsLeftEnd(sliderRef.current.scrollLeft === 0);
      setIsRightEnd(sliderRef.current.scrollLeft === maxScroll);
    };

    updateEnds();

    const handleScroll = () => {
      updateEnds();
    };

    sliderRef.current.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', handleScroll);

    return () => {
      if (sliderRef.current) {
        sliderRef.current.removeEventListener('scroll', handleScroll);
      }
      window.removeEventListener('resize', handleScroll);
    };
  }, [sliderRef.current]);

  const startDragging = useCallback((e) => {
    if (!sliderRef.current) return;
    setMouseDown(true);
    setStartX(e.pageX - sliderRef.current.offsetLeft);
    setScrollLeft(sliderRef.current.scrollLeft);
  }, []);

  const stopDragging = useCallback(() => {
    setMouseDown(false);
  }, []);

  const move = useCallback(
    (e) => {
      e.preventDefault();
      if (!sliderRef.current) return;
      if (!mouseDown) return;

      const x = e.pageX - sliderRef.current.offsetLeft;
      const scroll = x - startX;
      sliderRef.current.scrollLeft = scrollLeft - scroll;
    },
    [mouseDown, startX, scrollLeft],
  );

  const panButtonHandler = useCallback(
    (direction) => {
      if (!sliderRef.current) return;
      const distance = sliderRef.current.offsetWidth;
      const newScrollLeft = (sliderRef.current.scrollLeft +=
        direction === 'left' ? -distance : distance);

      if (
        (newScrollLeft < 0 && sliderRef.current.scrollLeft > 0) ||
        newScrollLeft - distance < sliderRef.current.scrollLeft
      ) {
        smoothScroll(newScrollLeft);
      }
    },
    [sliderRef.current, scrollLeft],
  );

  const smoothScroll = useCallback(
    (targetScrollLeft) => {
      if (!sliderRef.current) return;
      const startTime = performance.now();
      const duration = 300; // Adjust the duration as needed

      const animateScroll = (currentTime: number) => {
        if (!sliderRef.current) return;
        const elapsedTime = currentTime - startTime;
        const progress = Math.min(elapsedTime / duration, 1);
        sliderRef.current.scrollLeft =
          scrollLeft + (targetScrollLeft - scrollLeft) * progress;

        if (progress < 1) {
          requestAnimationFrame(animateScroll);
        } else {
          setScrollLeft(sliderRef.current.scrollLeft);
        }
      };

      requestAnimationFrame(animateScroll);
    },
    [sliderRef.current, scrollLeft],
  );

  return (
    <div
      style={{
        background: 'rgba(255,255,255,1)',
        display: 'flex',
        flexDirection: 'column',
        boxShadow:
          '0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)',
        borderRadius: '16px',
        padding: '8px 16px',
      }}
    >
      <Button
        type="text"
        shape="circle"
        size="small"
        style={{ position: 'absolute', right: '32px' }}
        onClick={() => onClose()}
      >
        <FaTimes
          size={18}
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%,-50%)',
          }}
        />
      </Button>
      <div>
        <h3 style={{ margin: 0, textAlign: 'center' }}>Monthly Images</h3>
      </div>
      <div style={{ display: 'flex', flexDirection: 'row' }}>
        <div
          style={{
            padding: '0 6px',
            visibility:
              sliderRef.current &&
              sliderRef.current.scrollWidth > sliderRef.current.offsetWidth
                ? 'visible'
                : 'hidden',
          }}
        >
          <Button
            disabled={isLeftEnd}
            shape="circle"
            size="small"
            onClick={() => panButtonHandler('left')}
            style={{ position: 'relative' }}
          >
            <FaChevronLeft
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%,-50%)',
              }}
            />
          </Button>
        </div>

        <div
          ref={sliderRef}
          onMouseMove={move}
          onMouseDown={startDragging}
          onMouseUp={stopDragging}
          onMouseLeave={stopDragging}
          style={{
            overflowX: 'hidden',
            float: 'left',
            width: '100%',
            cursor: 'grab',
          }}
        >
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              float: 'left',
              width: '100%',
            }}
          >
            {children}
          </div>
        </div>

        <div
          style={{
            padding: '0 6px',
            visibility:
              sliderRef.current &&
              sliderRef.current.scrollWidth > sliderRef.current.offsetWidth
                ? 'visible'
                : 'hidden',
          }}
        >
          <Button
            disabled={isRightEnd}
            shape="circle"
            size="small"
            onClick={() => panButtonHandler('right')}
            style={{ position: 'relative' }}
          >
            <FaChevronRight
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%,-50%)',
              }}
            />
          </Button>
        </div>
      </div>
    </div>
  );
};

export const TimelineButton = ({
  active = false,
  onClick,
}: {
  active: boolean;
  onClick: (e: any) => void;
}) => {
  const [hover, setHover] = useState(false);

  return (
    <button
      onMouseEnter={() => setHover(true)}
      onMouseLeave={() => setHover(false)}
      onClick={(e) => onClick(e)}
      style={{
        position: 'absolute',
        top: '17px',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        borderRadius: '50%',
        width: '15px',
        height: '15px',
        background: active ? '#72DEFF' : 'white',
        border: '2px solid #333',
        cursor: 'pointer',
        outline: hover ? '5px solid lightblue' : 'none',
      }}
    ></button>
  );
};

export const TimeLineSeparator = ({ label }: { label: string }) => {
  return (
    <div>
      <div style={{ transform: 'translateY(15px)', textAlign: 'center' }}>
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            position: 'relative',
          }}
        >
          <div
            style={{
              width: '3px',
              height: '24px',
              background: 'lightgray',
            }}
          />
        </div>
        <span
          style={{
            position: 'absolute',
            left: '50%',
            transform: 'translateX(-50%)',
            userSelect: 'none',
          }}
        >
          {label}
        </span>
      </div>
    </div>
  );
};

export const TimelinePadding = () => {
  return (
    <div
      style={{
        minWidth: '25px',
        height: '64px',
        position: 'relative',
      }}
    >
      <div style={{ transform: 'translateY(15px)', textAlign: 'center' }}>
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <div
            style={{ width: '100%', height: '3px', background: 'lightgray' }}
          />
        </div>
      </div>
    </div>
  );
};

export const TimelineItem = ({
  label,
  extended = false,
  children,
}: {
  label: string;
  extended?: boolean;
  children: ReactNode;
}) => {
  return (
    <div
      style={{
        minWidth: '50px',
        height: '64px',
        position: 'relative',
      }}
    >
      <div style={{ transform: 'translateY(15px)', textAlign: 'center' }}>
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <div
            style={{ width: '100%', height: '3px', background: 'lightgray' }}
          />
          <div
            style={{
              width: '3px',
              height: extended ? '14px' : '7px',
              background: 'lightgray',
            }}
          />
        </div>
        <span style={{ userSelect: 'none' }}>{label}</span>
      </div>
      {children}
    </div>
  );
};
