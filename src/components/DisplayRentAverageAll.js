import React, { PureComponent } from 'react';
import { connect } from 'umi';
// import { Row } from 'antd';

class DisplayRentAverageAll extends PureComponent {
  render() {
    return (
      <div
        style={{
          width: 200,
          height: 70,
          position: 'absolute',
          top: 32,
          left: 'calc(25vw - 100px)',
          padding: '12px',
          borderRadius: 16,
          zIndex: 100,
          background: 'rgba(255,255,255,0.9',
          textAlign: 'center',
        }}
      >
        <div>Average Rent</div>
        <div>${this.props.rentAverageAll}</div>
      </div>
    );
  }
}
export default connect(({ CMA }) => ({
  rentAverageAll: CMA.rentAverageAll,
}))(DisplayRentAverageAll);
