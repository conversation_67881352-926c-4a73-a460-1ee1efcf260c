@import './globalVars.css';

.headerWrapper {
  padding: var(--map-padding) 20px;
  /* margin-bottom: 8px; */
  background-color: #fff;
}

.logoWrapper {
  /* padding: var(--map-padding); */
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.logo_AlliedDev {
  width: calc(225px * var(--logo-height) / 55);
  height: var(--logo-height);
  display: block;
}

.logo_AMH svg {
  width: calc(1010px * var(--logo-height) / 310);
  height: var(--logo-height);
}

.logo_Arabella {
  width: calc(226px * var(--logo-height) / 160);
  height: var(--logo-height);
  display: block;
}

.logo_ArkHomesForRent {
  width: calc(443px * var(--logo-height) / 151);
  height: var(--logo-height);
  display: block;
}

.logo_BridgeTower {
  width: calc(79px * var(--logo-height) / 32);
  /* width: var(--logo-height); */
  height: var(--logo-height);
  /* padding: var(--map-padding); */
  display: block;
}

.logo_Camillo {
  width: calc(1351px * var(--logo-height) / 450);
  height: var(--logo-height);
  display: block;
}

.logo_Castle svg {
  width: calc(623px * var(--logo-height) / 427);
  height: var(--logo-height);
  display: block;
}

.logo_Pathway svg {
  width: calc(286px * var(--logo-height) / 47);
  height: var(--logo-height);
  display: block;
}


.logo_USLegacy svg {
  width: calc(212px * var(--logo-height) / 33);
  height: var(--logo-height);
  display: block;
}

.logo_CommonGroundCapital svg {
  height: var(--logo-height);
  display: block;
}

.logo_MarketplaceHomes svg {
  width: calc(156px * var(--logo-height) / 30);
  height: var(--logo-height);
  display: block;
}

.logo_DivvyHomes svg {
  width: calc(124px * var(--logo-height) / 44);
  height: var(--logo-height);
  display: block;
}

.logo_Evergreen {
  width: calc(1801 * var(--logo-height) / 501);
  height: var(--logo-height);
  display: block;
}

.logo_SecondAvenue svg {
  width: calc(1570px * var(--logo-height) / 292);
  height: var(--logo-height);
  display: block;
}

.logo_SmithDouglas {
  width: calc(187px * var(--logo-height) / 74);
  height: var(--logo-height);
  display: block;
}

.logo_TrellyGroup {
  width: calc(162px * var(--logo-height) / 59);
  height: var(--logo-height);
}

.logo_Avanta {
  width: calc(2346px * var(--logo-height) / 550);
  height: var(--logo-height);
  display: block;
}

.logo_Bridge {
  width: calc(279px * var(--logo-height) / 168);
  height: var(--logo-height);
  display: block;
}

.logo_Nhimble {
  width: calc(722px * var(--logo-height) / 154);
  height: var(--logo-height);
  display: block;
}

.logo_HON {
  width: calc(708px * var(--logo-height) / 138);
  height: var(--logo-height);
  display: block;
}

.logo_ILE svg {
  width: calc(152px * var(--logo-height) / 57);
  height: var(--logo-height);
  display: block;
}

.logo_VentureREI {
  width: calc(1000px * var(--logo-height) / 261);
  height: var(--logo-height);
  display: block;
}

.logo_ILE_placeholder {
  width: 60px;
  height: var(--logo-height);
  display: block;
}

.logo_SVN svg {
  width: calc(122px * var(--logo-height) / 46);
  height: var(--logo-height);
  display: block;
}

.logo_Darwin svg {
  width: calc(110px * var(--logo-height) / 28);
  height: var(--logo-height);
  display: block;
}

.logo_GACapital {
  width: calc(942px * var(--logo-height) / 126);
  height: var(--logo-height);
  display: block;
}

.logo_GEMRC {
  width: calc(227.056px * var(--logo-height) / 36.066);
  height: var(--logo-height);
  display: block;
}

.logo_BlueRiver {
  width: calc(673px * var(--logo-height) / 260);
  height: var(--logo-height);
  display: block;
}

.logo_Truehold svg {
  width: calc(150px * var(--logo-height) / 28);
  height: var(--logo-height);
  display: block;
}

.logo_Fundrise svg {
  width: calc(137px * var(--logo-height) / 27);
  height: var(--logo-height);
  display: block;
}

.logo_JLL svg {
  width: calc(100px * var(--logo-height) / 43);
  height: var(--logo-height);
  display: block;
}

.logo_Sunroom svg {
  width: calc(281px * var(--logo-height) / 70);
  height: var(--logo-height);
  display: block;
}

.logo_DRHorton svg {
  width: calc(355px * var(--logo-height) / 207);
  height: var(--logo-height);
  display: block;
}

.logo_Homebound svg {
  width: calc(600px * var(--logo-height) / 100);
  height: var(--logo-height);
  display: block;
}

.logo_Lennar svg {
  width: calc(102px * var(--logo-height) / 10 / 2);
  height: calc(var(--logo-height) / 2);
  display: block;
}
.logo_Hawkhill svg {
  width: calc(1144px * var(--logo-height)/488);
  height: var(--logo-height);
  display: block;
}

.logo_UrbanRowGroup {
  width: calc(1019px * var(--logo-height) / 335);
  height: var(--logo-height);
  display: block;
}

.logo_Embry {
  width: calc(350px * var(--logo-height) / 115);
  height: var(--logo-height);
  display: block;
}

.logo_GreatGulf svg {
  width: calc(749px * var(--logo-height) / 83 / 2);
  height: calc(var(--logo-height) / 2);
  display: block;
}

.logo_Vertica {
  width: calc(216px * var(--logo-height) / 100);
  height: var(--logo-height);
  display: block;
}

.logo_JCGLand {
  width: calc(124px * var(--logo-height) / 95);
  height: var(--logo-height);
  display: block;
}

.logo_LedgerTC svg {
  width: calc(800px * var(--logo-height) / 650);
  height: var(--logo-height);
  display: block;
}

.logo_InvitationHomes svg {
  width: calc(517px * var(--logo-height) / 101);
  height: var(--logo-height);
  display: block;
}

.logo_Kairos {
  width: calc(355px * var(--logo-height) / 146);
  height: var(--logo-height);
  display: block;
}

.logo_UpAndUp {
  width: calc(113px * var(--logo-height) / 37);
  height: var(--logo-height);
  display: block;
}

.logo_AveOne {
  width: calc(132px * var(--logo-height) / 46);
  height: var(--logo-height);
  display: block;
}

.logo_Greystar {
  width: calc(169px * var(--logo-height) / 37);
  height: var(--logo-height);
  display: block;
}

.logo_Rithm svg {
  width: calc(85px * var(--logo-height) / 29);
  height: var(--logo-height);
  display: block;
}

.logo_Tricon svg {
  width: calc(136px * var(--logo-height) / 48);
  height: var(--logo-height);
  display: block;
}

.logo_MMG svg {
  width: calc(144px * var(--logo-height) / 69);
  height: var(--logo-height);
  display: block;
}

.logo_HunterQuinn {
  width: calc(1080px * var(--logo-height) / 283);
  height: var(--logo-height);
  display: block;
}

.logo_BeaconRidge {
  width: calc(250px * var(--logo-height) / 55);
  height: var(--logo-height);
  display: block;
}

.logo_WebCity {
  width: calc(1000px * var(--logo-height) / 281);
  height: var(--logo-height);
  display: block;
}

.logo_Heyday {
  width: calc(1200px * var(--logo-height) / 302);
  height: var(--logo-height);
  display: block;
}

.logo_LocateAlpha {
  width: calc(188px * var(--logo-height) / 72);
  height: var(--logo-height);
  display: block;
}

.logo_RealCo svg {
  width: var(--logo-height);
  height: var(--logo-height);
  display: block;
}

.wordmark {
  font-size: 40px;
  font-weight: 900;
  color: var(--color-BT-blue);
  line-height: 1;
  margin-left: 2px;
}

.logoDivider {
  width: 1px;
  height: calc(var(--logo-height) - 12px);
  background-color: rgba(0, 0, 0, 0.1);
  margin: 0 16px;
}

.logoDividerInvisible {
  composes: logoDivider;
  background-color: rgba(0, 0, 0, 0);
  margin: 0 8px;
}

.logoTitle {
  font-size: 13px;
  font-weight: 300;
  line-height: 16px;
  color: #164685;
}

.searchWrapper {
  composes: flexCenter from global;
  min-width: 0px;
}

.batchProcessWrapper {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 16px;
  padding-left: 20px;
}

.batchProcessButton {
  font-weight: 500;
  color: var(--color-BT-blue);
  background-color: rgba(0, 0, 0, 0);
  border: 2px solid var(--color-BT-blue);
  padding: 6px 12px;
  cursor: pointer;
  white-space: nowrap;
}

.batchProcessButton:hover {
  color: #fff;
  background-color: var(--color-BT-blue);
}

.KMLButton {
  composes: batchProcessButton;
  border: none;
  white-space: nowrap;
  padding: 6px 0px;
}

.KMLButton:hover {
  color: var(--color-BT-blue);
  background-color: rgba(0, 0, 0, 0);
}

.signOutButton {
  composes: batchProcessButton;
  border: none;
  white-space: nowrap;
}

/* .signOutButton:hover {
	color: #fff;
	background-color: var(--color-BT-blue);
} */

.header_right_button_user {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 999px;
  background-color: #34c759;
  font-family: 'IBM Plex Sans';
  font-style: normal;
  font-weight: 700;
  font-size: 16px;
  color: #ffffff;
  text-align: center;
  /* margin: 0 8px; */
  box-shadow: 0 0 0 8px rgba(52, 199, 89, 0.1);
  cursor: pointer;
}

.header_right_button_user:hover {
  box-shadow: 0 0 0 8px rgba(52, 199, 89, 0.2);
}

.headerBarWrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
  width: 100vw;
  padding: 4px 0 4px 8px;
  /* margin-bottom: 8px; */
  border-top: 1px solid rgb(0 0 0 / 0.08);
  border-bottom: 1px solid rgb(0 0 0 / 0.15);
  background-color: #fff;
}

.headerBarButton {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.5;
  color: var(--color-BT-blue);
}
