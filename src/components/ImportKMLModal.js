import {
  Button,
  Checkbox,
  Col,
  Modal,
  Progress,
  Radio,
  Row,
  Spin,
  Table,
  Tooltip,
  Tree,
} from 'antd';
import moment from 'moment';
import React, { useEffect, useRef, useState } from 'react';
import { connect } from 'umi';
import styles from './batchProcessing.css';
// import difference from 'lodash.difference';
import toGeoJSON from '@mapbox/togeojson';
import { default as turf_centerOfMass } from '@turf/center-of-mass';
import isEqual from 'lodash.isequal';
// import meta from 'turf-meta';
import JSZip from 'jszip';

// get file extension
const getExtension = (filename) => {
  return filename.split('.').pop();
};

const extractFirstKMLFromKMZ = async (kmz) => {
  const zip = await JSZip.loadAsync(kmz);
  const files = await zip.file(/\.kml$/i);
  if (files.length > 0) {
    const kml = await files[0].async('blob');
    return kml;
  } else {
    alert('No KML file found in the KMZ file');
    return null;
  }
};

const ImportKMLModal = (props) => {
  // coordinates of centre of current KML file
  const [KMLCenterCoordinates, setKMLCenterCoordinates] = useState([]);

  const onChangeKMLInput = async (e) => {
    if (e.target.files.length > 0) {
      let KMLFile;
      if (getExtension(e.target.files[0].name).toLowerCase() === 'kmz') {
        KMLFile = await extractFirstKMLFromKMZ(e.target.files[0]);
        // console.log('KMLFile', KMLFile);
      } else if (getExtension(e.target.files[0].name).toLowerCase() === 'kml') {
        KMLFile = e.target.files[0];
      } else {
        alert('Please upload a KML or KMZ file');
        removeFileInput();
      }
      const KMLReader = new FileReader();
      // load content of the KML file
      // async
      if (KMLFile) {
        KMLReader.readAsText(KMLFile);
        // listener for the event that the loading is done successfully
        KMLReader.onload = (event) => {
          const KMLFileContent = event.target.result;
          // console.log('KMLFileContent', KMLFileContent);
          // toGeoJSON's input must be a KML document as an XML DOM - not as a string
          const parser = new DOMParser();
          const KMLXMLDOM = parser.parseFromString(KMLFileContent, 'text/xml');
          const convertedJSON = toGeoJSON.kml(KMLXMLDOM);
          console.log('convertedJSON', convertedJSON);
          if (convertedJSON.features.length > 0) {
            props.dispatch({
              type: 'CMA/saveCMAStates',
              payload: {
                GeoJSONFromKML: convertedJSON,
              },
            });
            const KMLCenter = turf_centerOfMass(
              convertedJSON.features[0].geometry,
            );
            setKMLCenterCoordinates([
              KMLCenter.geometry.coordinates[0],
              KMLCenter.geometry.coordinates[1],
            ]);
            // close modal when loading KML is done
            closeModal();
          } else {
            alert('No valid KML features found');
            removeFileInput();
          }
        };
      } else {
        alert('Please upload a valid KML or KMZ file');
        removeFileInput();
      }
    }
  };

  const closeModal = () => {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        showImportKMLModal: false,
      },
    });
  };

  // pan to KML after user moved away on map
  const panToKML = () => {
    // kml is still the subject property
    // pan to eventCoordinates
    if (isEqual(props.eventCoordinates, KMLCenterCoordinates)) {
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          shouldPanToKML: true,
        },
      });
    }
  };

  const removeFileInput = () => {
    const kmlInput = document.getElementById('kml input');
    if (kmlInput) {
      kmlInput.value = '';
    }
  };

  // clear out file input
  // when props.eventCoordinates changes
  // which means subject property has changed
  useEffect(() => {
    if (!isEqual(props.eventCoordinates, KMLCenterCoordinates)) {
      removeFileInput();
    }
  }, [props.eventCoordinates]);

  return (
    <Modal
      open={props.showImportKMLModal}
      // onCancel={closeModal}
      // onOk={closeModal}
      closable={true}
      maskClosable={true}
      onCancel={closeModal}
      centered={true}
      // footer={<Button onClick={closeModal}>Close</Button>}
      footer={null}
      // width={'calc(100% - 64px)'}
      style={{
        backgroundColor: 'rgba(253,253,253,0.92)',
        // height: 'calc(100vh - 64px)',
        zIndex: 999,
      }}
      // bodyStyle={{ height: 'calc(100vh - 64px)' }}
    >
      <input type="file" id="kml input" onChange={onChangeKMLInput} />
      {KMLCenterCoordinates.length > 0 &&
        isEqual(props.eventCoordinates, KMLCenterCoordinates) && (
          <Button key="pan to kml" type="default" onClick={panToKML}>
            Pan to KML
          </Button>
        )}
    </Modal>
  );
};

export default connect(({ CMA }) => ({
  showImportKMLModal: CMA.showImportKMLModal,
  eventCoordinates: CMA.eventCoordinates,
  GeoJSONFromKML: CMA.GeoJSONFromKML,
}))(ImportKMLModal);
