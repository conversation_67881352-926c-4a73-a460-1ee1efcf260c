import { Button } from 'antd';
// @ts-ignore
import { LoadingOutlined } from '@ant-design/icons';
import { Spin } from 'antd';
import json2csv from 'csvjson-json2csv/json2csv';
import moment from 'moment';
import React, { useState } from 'react';

// Define types based on the MultiFamilyApartments component
type MultiFamilyUnitType = {
  base_id: string;
  first_seen: string | null;
  last_seen: string | null;
  unit_name: string | null;
  unit_number: string | null;
  unit_count: number | null;
  rental_key: string | null;
  rental_type: string | null;
  model_key: string | null;
  model_id: string | null;
  beds: number | null;
  baths: number | null;
  unit_rent: number | null;
  unit_max_rent: number | null;
  unit_deposit: number | null;
  sqft: number | null;
  max_sqft: number | null;
  available_date: string | null;
  apartment_id: string;
  apartmentUnitForRent?: Record<string, any>;
};

type SelectedPropertyType = {
  distance_mi: number;
  base_id: string;
  first_seen: string | null;
  last_seen: string | null;
  listing_id: string | null;
  listing_type_id: string | null;
  name: string | null;
  phone: string | null;
  url: string | null;
  city: string | null;
  county: string | null;
  state: string | null;
  country: string | null;
  zipcode: string | null;
  address: string | null;
  listing_dma: string | null;
  min_rent: number | null;
  max_rent: number | null;
  min_bedrooms: number | null;
  property_type: string;
  latitude: number | null;
  longitude: number | null;
  company_id: string | null;
  unit_availabilities: number | null;
  url_external: string | null;
  rent_info: Record<string, any> | null;
  description: string | null;
  year_built: number | null;
  total_property_units: number | null;
  property_unit_type: string | null;
  amenities: string[] | null;
  features: string[] | null;
  fees_policies: Record<string, any> | null;
  important_details: Record<string, any> | null;
  computed?: {
    vacant_pct?: number;
    absorption_pct?: number;
    [key: string]: any;
  };
  units: MultiFamilyUnitType[];
};

type UnitMixData = {
  base_id: string;
  name: string | null;
  unit_type: string;
  avg_rent: number | null;
  avg_psf: number | null;
  unit_count: number | null;
  unit_pct: number | null;
};

interface ExportPropertyDetailsCSVProps {
  selectedProperty: SelectedPropertyType;
  selectedPropertyUnitMix?: UnitMixData[];
  currentPropertyAddress?: {
    streetAddress: string;
  };
  currentRadiusMile?: number;
  unitsTableFilters?: Record<string, any>;
}

const antIcon = <LoadingOutlined style={{ fontSize: 16 }} spin />;

export const ExportPropertyDetailsCSV: React.FC<
  ExportPropertyDetailsCSVProps
> = ({
  selectedProperty,
  selectedPropertyUnitMix,
  currentPropertyAddress,
  currentRadiusMile,
  unitsTableFilters,
}) => {
  const [loading, setLoading] = useState(false);

  // Calculate unit ranges
  const unitRanges = React.useMemo(() => {
    const count = selectedProperty?.units?.length || 0;
    if (count === 0) return null;

    const rentData: number[] = [];
    const bedsData: number[] = [];
    const bathsData: number[] = [];
    const sqftData: number[] = [];

    for (let i = 0; i < count; i++) {
      const unit = selectedProperty.units[i];
      typeof unit?.unit_rent === 'number' && rentData.push(unit.unit_rent);
      typeof unit?.beds === 'number' && bedsData.push(unit.beds);
      typeof unit?.baths === 'number' && bathsData.push(unit.baths);
      typeof unit?.sqft === 'number' && sqftData.push(unit.sqft);
    }

    const rent = [...new Set(rentData)].sort((a, b) => a - b);
    const beds = [...new Set(bedsData)].sort((a, b) => a - b);
    const baths = [...new Set(bathsData)].sort((a, b) => a - b);
    const sqft = [...new Set(sqftData)].sort((a, b) => a - b);

    return { rent, beds, baths, sqft };
  }, [selectedProperty]);

  const handleExport = () => {
    if (!selectedProperty) {
      return;
    }

    setLoading(true);

    try {
      // Prepare CSV data arrays
      let csvData: any[] = [];

      // 1. Property Information Section
      csvData.push({
        Section: 'PROPERTY INFORMATION',
        Field: '',
        Value: '',
        'Additional Info': '',
        Status: '',
        'Unit Name': '',
        'Unit #': '',
        '# of Units': '',
        Beds: '',
        Baths: '',
        Sqft: '',
        Rent: '',
      });

      csvData.push({
        Section: '',
        Field: 'Property Name',
        Value: selectedProperty.name || 'N/A',
        'Additional Info': '',
        Status: '',
        'Unit Name': '',
        'Unit #': '',
        '# of Units': '',
        Beds: '',
        Baths: '',
        Sqft: '',
        Rent: '',
      });

      csvData.push({
        Section: '',
        Field: 'Address',
        Value: `${selectedProperty.address}, ${selectedProperty.city}, ${selectedProperty.state}, ${selectedProperty.zipcode}`,
        'Additional Info': '',
        Status: '',
        'Unit Name': '',
        'Unit #': '',
        '# of Units': '',
        Beds: '',
        Baths: '',
        Sqft: '',
        Rent: '',
      });

      if (selectedProperty.phone) {
        csvData.push({
          Section: '',
          Field: 'Phone',
          Value: selectedProperty.phone,
          'Additional Info': '',
          Status: '',
          'Unit Name': '',
          'Unit #': '',
          '# of Units': '',
          Beds: '',
          Baths: '',
          Sqft: '',
          Rent: '',
        });
      }

      // Add unit ranges
      if (unitRanges) {
        if (unitRanges.rent.length > 0) {
          const rentRange =
            unitRanges.rent.length > 1
              ? `$${unitRanges.rent[0]?.toLocaleString()} - $${unitRanges.rent[
                  unitRanges.rent.length - 1
                ]?.toLocaleString()}`
              : `$${unitRanges.rent[0]?.toLocaleString()}`;
          csvData.push({
            Section: '',
            Field: 'Monthly Rent',
            Value: rentRange,
            'Additional Info': '',
            Status: '',
            'Unit Name': '',
            'Unit #': '',
            '# of Units': '',
            Beds: '',
            Baths: '',
            Sqft: '',
            Rent: '',
          });
        }

        if (unitRanges.beds.length > 0) {
          const bedsRange =
            unitRanges.beds.length > 1
              ? `${
                  unitRanges.beds[0] === 0 ? 'Studio' : unitRanges.beds[0]
                } - ${unitRanges.beds[unitRanges.beds.length - 1]} bd`
              : `${
                  unitRanges.beds[0] === 0 ? 'Studio' : unitRanges.beds[0]
                } bd`;
          csvData.push({
            Section: '',
            Field: 'Bedrooms',
            Value: bedsRange,
            'Additional Info': '',
            Status: '',
            'Unit Name': '',
            'Unit #': '',
            '# of Units': '',
            Beds: '',
            Baths: '',
            Sqft: '',
            Rent: '',
          });
        }

        if (unitRanges.baths.length > 0) {
          const bathsRange =
            unitRanges.baths.length > 1
              ? `${unitRanges.baths[0]} - ${
                  unitRanges.baths[unitRanges.baths.length - 1]
                } ba`
              : `${unitRanges.baths[0]} ba`;
          csvData.push({
            Section: '',
            Field: 'Bathrooms',
            Value: bathsRange,
            'Additional Info': '',
            Status: '',
            'Unit Name': '',
            'Unit #': '',
            '# of Units': '',
            Beds: '',
            Baths: '',
            Sqft: '',
            Rent: '',
          });
        }

        if (unitRanges.sqft.length > 0) {
          const sqftRange =
            unitRanges.sqft.length > 1
              ? `${unitRanges.sqft[0]?.toLocaleString()} - ${unitRanges.sqft[
                  unitRanges.sqft.length - 1
                ]?.toLocaleString()}`
              : `${unitRanges.sqft[0]?.toLocaleString()}`;
          csvData.push({
            Section: '',
            Field: 'Square Feet',
            Value: sqftRange,
            'Additional Info': '',
            Status: '',
            'Unit Name': '',
            'Unit #': '',
            '# of Units': '',
            Beds: '',
            Baths: '',
            Sqft: '',
            Rent: '',
          });
        }
      }

      // Empty row
      csvData.push({
        Section: '',
        Field: '',
        Value: '',
        'Additional Info': '',
        Status: '',
        'Unit Name': '',
        'Unit #': '',
        '# of Units': '',
        Beds: '',
        Baths: '',
        Sqft: '',
        Rent: '',
      });

      // 2. Unit Mix Summary Section
      if (selectedPropertyUnitMix && selectedPropertyUnitMix.length > 0) {
        csvData.push({
          Section: 'UNIT MIX SUMMARY',
          Field: '',
          Value: '',
          'Additional Info': '',
          Status: '',
          'Unit Name': '',
          'Unit #': '',
          '# of Units': '',
          Beds: '',
          Baths: '',
          Sqft: '',
          Rent: '',
        });

        selectedPropertyUnitMix.forEach((unitMix) => {
          csvData.push({
            Section: '',
            Field: 'Unit Type',
            Value: unitMix.unit_type,
            'Additional Info': `Count: ${
              unitMix.unit_count?.toLocaleString() || 'N/A'
            } (${unitMix.unit_pct || 0}%) | Avg Rent: ${
              unitMix.avg_rent ? `$${unitMix.avg_rent.toLocaleString()}` : 'N/A'
            } | Avg PSF: ${
              unitMix.avg_psf ? `$${unitMix.avg_psf.toLocaleString()}` : 'N/A'
            }`,
            Status: '',
            'Unit Name': '',
            'Unit #': '',
            '# of Units': '',
            Beds: '',
            Baths: '',
            Sqft: '',
            Rent: '',
          });
        });

        // Empty row
        csvData.push({
          Section: '',
          Field: '',
          Value: '',
          'Additional Info': '',
          Status: '',
          'Unit Name': '',
          'Unit #': '',
          '# of Units': '',
          Beds: '',
          Baths: '',
          Sqft: '',
          Rent: '',
        });
      }

      // 3. Individual Units Section
      if (selectedProperty.units && selectedProperty.units.length > 0) {
        csvData.push({
          Section: 'INDIVIDUAL UNITS',
          Field: '',
          Value: '',
          'Additional Info': '',
          Status: '',
          'Unit Name': '',
          'Unit #': '',
          '# of Units': '',
          Beds: '',
          Baths: '',
          Sqft: '',
          Rent: '',
        });

        // Determine latest last seen date
        const sortedUnits = [...selectedProperty.units].sort((a, b) =>
          a.last_seen && b.last_seen
            ? new Date(a.last_seen).getTime() - new Date(b.last_seen).getTime()
            : 0,
        );
        const latestLastSeenDate =
          sortedUnits[sortedUnits.length - 1]?.last_seen;

        const filteredUnits = selectedProperty.units.filter((unit) => {
          if (unitsTableFilters?.beds && unitsTableFilters.beds.length > 0) {
            if (!unitsTableFilters.beds.includes(unit.beds)) {
              return false;
            }
          }

          if (unitsTableFilters?.baths && unitsTableFilters.baths.length > 0) {
            if (!unitsTableFilters.baths.includes(unit.baths)) {
              return false;
            }
          }

          if (
            unitsTableFilters?.last_seen &&
            unitsTableFilters.last_seen.length > 0
          ) {
            const status =
              unit.last_seen === latestLastSeenDate ? 'Active' : 'Closed';
            if (!unitsTableFilters.last_seen.includes(status)) {
              return false;
            }
          }

          if (
            unitsTableFilters?.unit_name &&
            unitsTableFilters.unit_name.length > 0
          ) {
            if (
              !unit.unit_name ||
              !unitsTableFilters.unit_name.some((name: string) =>
                unit.unit_name?.startsWith(name),
              )
            ) {
              return false;
            }
          }

          return true;
        });

        filteredUnits.forEach((unit) => {
          const status =
            unit.last_seen === latestLastSeenDate ? 'Active' : 'Closed';
          const beds =
            unit.beds === 0 ? 'Studio' : unit.beds?.toString() || 'N/A';

          csvData.push({
            Section: '',
            Field: '',
            Value: '',
            'Additional Info': '',
            Status: status,
            'Unit Name': unit.unit_name || 'N/A',
            'Unit #': unit.unit_number || 'N/A',
            '# of Units': unit.unit_count?.toString() || 'N/A',
            Beds: beds,
            Baths: unit.baths?.toString() || 'N/A',
            Sqft: unit.sqft?.toString() || 'N/A',
            Rent: unit.unit_rent
              ? `$${unit.unit_rent.toLocaleString()}`
              : 'N/A',
          });
        });
      }

      // Create CSV content
      let csv = json2csv(csvData);

      // Add header information
      const headerInfo = [
        [`"Export Date: "`, `${moment().format('MM/DD/YYYY')}\n`],
        [
          `"Property: "`,
          `"${selectedProperty.name || selectedProperty.address}"\n`,
        ],
        [
          `"Search Center: "`,
          `"${currentPropertyAddress?.streetAddress || 'N/A'}"\n`,
        ],
        [
          `"Distance: "`,
          `"${selectedProperty.distance_mi?.toFixed(2) || 'N/A'} miles"\n`,
        ],
        [
          `"Total Units in Property: "`,
          `"${selectedProperty.total_property_units || 'N/A'}"\n`,
        ],
        [`"Year Built: "`, `"${selectedProperty.year_built || 'N/A'}"\n`],
        ['\n'],
      ];

      // Prepend header info to CSV
      for (let i = headerInfo.length - 1; i >= 0; i--) {
        csv = headerInfo[i].toString() + csv;
      }

      // Create and download file
      const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
      const urlObject = window.URL || window.webkitURL || window;
      const saveLink = document.createElementNS(
        'http://www.w3.org/1999/xhtml',
        'a',
      ) as HTMLAnchorElement;
      saveLink.href = urlObject.createObjectURL(blob);

      const fileName = `Property Details - ${
        selectedProperty.name || selectedProperty.address || 'Property'
      } - ${moment().format('YYYY-MM-DD')}.csv`;
      saveLink.download = fileName;
      saveLink.click();

      // Cleanup
      urlObject.revokeObjectURL(saveLink.href);
    } catch (error) {
      console.error('Error exporting property details CSV:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
      <Button
        type="default"
        onClick={handleExport}
        disabled={!selectedProperty || loading}
        size="small"
      >
        Export Property Details to CSV
      </Button>
      {loading && <Spin indicator={antIcon} />}
    </div>
  );
};

export default ExportPropertyDetailsCSV;
