import { dateFormat } from '@/components/ResultTable/ResultTable';
import { showSFROperatorsFullNameOnly } from '@/components/SFRBrandConvertFunction';
import { formatter } from '@/utils/money';
import { LoadingOutlined } from '@ant-design/icons';
import { Spin } from 'antd';
import json2csv from 'csvjson-json2csv/json2csv';
import moment from 'moment';
import { useEffect, useState } from 'react';
import { connect } from 'umi';
import styles from '../ResultTable/resultTable.css';

const antIcon = <LoadingOutlined style={{ fontSize: 16 }} spin />;

const ExportToCSV = connect(({ CMA }) => ({
  currentPropertyAddress: CMA.currentPropertyAddress,
  currentRadiusMile: CMA.currentRadiusMile,
}))(function ({
  currentPropertyAddress,
  currentRadiusMile,
  data,
  columnDataIndex,
  tableName,
}) {
  const [loading, setLoading] = useState(false);
  const [generateCSV, setGenerateCSV] = useState(false);

  useEffect(() => {
    if (!loading && generateCSV) {
      setLoading(true);
    }
    if (
      loading &&
      generateCSV &&
      columnDataIndex &&
      columnDataIndex.length > 0 &&
      data &&
      data.length > 0
    ) {
      let csvData = [];

      // sort data
      if (
        tableName === 'National SFR Operators Listings' ||
        tableName === 'Portal Listings' ||
        tableName === 'MLS'
      ) {
        // default: distance nearest to furthest
        data = data.sort((a, b) => +a.distance - +b.distance);
        console.log('csvv data', data);
      }

      // create csv content
      for (let i = 0; i < data.length; i++) {
        const rowData = data[i];
        const csvRowData = {};
        for (let j = 0; j < columnDataIndex.length; j++) {
          if (tableName === 'Properties Owned by Bridge Tower') {
            if (columnDataIndex[j].columnName === 'Ba') {
              let baData = rowData[columnDataIndex[j].dataIndex];
              baData = baData.split('/')[0];
              csvRowData[columnDataIndex[j].columnName] = baData;
            } else if (columnDataIndex[j].columnName === 'Bd') {
              let bdData = rowData[columnDataIndex[j].dataIndex];
              bdData = bdData.split('/')[1];
              csvRowData[columnDataIndex[j].columnName] = bdData;
            } else {
              csvRowData[columnDataIndex[j].columnName] =
                rowData[columnDataIndex[j].dataIndex];
            }
          } else if (
            tableName === 'National SFR Operators Listings' ||
            tableName === 'Portal Listings' ||
            tableName === 'MLS'
          ) {
            if (columnDataIndex[j].columnName === 'Address') {
              if (
                ['National SFR Operators Listings', 'Portal Listings'].includes(
                  tableName,
                )
              ) {
                let addressData;
                if (rowData[columnDataIndex[j].dataIndex[0]].includes(',')) {
                  addressData = rowData[
                    columnDataIndex[j].dataIndex[0]
                  ].replace(/,(?! )/g, ', ');
                } else {
                  addressData = `${rowData[columnDataIndex[j].dataIndex[0]]}, ${
                    rowData[columnDataIndex[j].dataIndex[1]]
                  }, ${rowData[columnDataIndex[j].dataIndex[2]]}`;
                }
                csvRowData[columnDataIndex[j].columnName] = addressData;
              } else if (tableName === 'MLS') {
                let addressData = rowData[
                  columnDataIndex[j].dataIndex[0]
                ].replace(/ /g, ' ');
                addressData =
                  addressData +
                  ', ' +
                  rowData[columnDataIndex[j].dataIndex[1]] +
                  ', ' +
                  rowData[columnDataIndex[j].dataIndex[2]] +
                  ' ' +
                  rowData[columnDataIndex[j].dataIndex[3]];
                csvRowData[columnDataIndex[j].columnName] = addressData;
              }
            } else if (columnDataIndex[j].columnName === 'Dist.') {
              let distData = rowData[columnDataIndex[j].dataIndex];
              distData = (parseFloat(distData) / 1609.34).toFixed(1) + ' mi';
              csvRowData[columnDataIndex[j].columnName] = distData;
            } else if (columnDataIndex[j].columnName === 'Status') {
              if (
                ['National SFR Operators Listings', 'Portal Listings'].includes(
                  tableName,
                )
              ) {
                let statusData = rowData[columnDataIndex[j].dataIndex];
                statusData = statusData ? 'Available' : 'Closed';
                csvRowData[columnDataIndex[j].columnName] = statusData;
              } else if (tableName === 'MLS') {
                let statusData = rowData[columnDataIndex[j].dataIndex];
                csvRowData[columnDataIndex[j].columnName] = statusData;
              }
            } else if (columnDataIndex[j].columnName === 'Owner') {
              if (['Portal Listings'].includes(tableName)) {
                csvRowData[columnDataIndex[j].columnName] = 'HotPads';
              } else if (tableName === 'MLS') {
                csvRowData[columnDataIndex[j].columnName] = 'MLS';
              } else {
                let ownerData = rowData[columnDataIndex[j].dataIndex];
                ownerData = showSFROperatorsFullNameOnly(ownerData);
                csvRowData[columnDataIndex[j].columnName] = ownerData;
              }
            } else if (
              columnDataIndex[j].columnName === 'Rent' ||
              columnDataIndex[j].columnName === 'Sold'
            ) {
              let rentData = rowData[columnDataIndex[j].dataIndex];
              rentData = '$' + formatter(parseFloat(rentData).toFixed());
              csvRowData[columnDataIndex[j].columnName] = rentData;
            } else if (columnDataIndex[j].columnName === 'Type') {
              if (
                ['National SFR Operators Listings', 'Portal Listings'].includes(
                  tableName,
                )
              ) {
                csvRowData[columnDataIndex[j].columnName] = 'N/A';
              } else {
                let typeData = rowData[columnDataIndex[j].dataIndex];
                if (typeData === 'Single Family Residence') {
                  typeData = 'SFR';
                } else if (typeData === 'Townhouse') {
                  typeData = 'TH';
                }
                csvRowData[columnDataIndex[j].columnName] = typeData;
              }
            } else if (columnDataIndex[j].columnName === 'Sqft') {
              let sqftData = rowData[columnDataIndex[j].dataIndex];
              sqftData = formatter(sqftData);
              csvRowData[columnDataIndex[j].columnName] = sqftData;
            } else if (columnDataIndex[j].columnName === 'Avail.') {
              let availData = rowData[columnDataIndex[j].dataIndex];
              if (availData) {
                availData = availData && moment(availData).format(dateFormat);
              } else {
                availData = '';
              }
              csvRowData[columnDataIndex[j].columnName] = availData;
            } else if (columnDataIndex[j].columnName === 'Cls.') {
              let clsData = rowData[columnDataIndex[j].dataIndex];
              if (clsData) {
                clsData = moment(clsData).format(dateFormat);
              } else {
                clsData = '';
              }
              csvRowData[columnDataIndex[j].columnName] = clsData;
            } else if (columnDataIndex[j].columnName === 'PSF') {
              let psfData =
                rowData[columnDataIndex[j].dataIndex[0]] /
                rowData[columnDataIndex[j].dataIndex[1]];
              psfData = '$' + psfData.toFixed(2);
              csvRowData[columnDataIndex[j].columnName] = psfData;
            } else {
              csvRowData[columnDataIndex[j].columnName] =
                rowData[columnDataIndex[j].dataIndex];
            }
          } else if (tableName === 'Multi-Family') {
            const data =
              rowData[columnDataIndex[j].dataIndex] === null
                ? ''
                : rowData[columnDataIndex[j].dataIndex];

            csvRowData[columnDataIndex[j].columnName] = data;
          } else {
            csvRowData[columnDataIndex[j].columnName] =
              rowData[columnDataIndex[j].dataIndex];
          }
        }
        csvData.push(csvRowData);
      }
      console.log(csvData);
      let csv = json2csv(csvData);

      const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
      var urlObject = window.URL || window.webkitURL || window;
      var save_link = document.createElementNS(
        'http://www.w3.org/1999/xhtml',
        'a',
      );
      save_link.href = urlObject.createObjectURL(blob);
      save_link.download = `${tableName} - ${currentPropertyAddress.streetAddress} - ${currentRadiusMile} miles.csv`;
      save_link.click();

      setGenerateCSV(false);
      setLoading(false);
    }
  });

  const exportClickHandler = () => {
    setGenerateCSV(true);
  };

  return (
    <>
      <button onClick={exportClickHandler} className={styles.csvButton}>
        Export to CSV
      </button>
      {loading && <Spin indicator={antIcon} />}
    </>
  );
});

export default ExportToCSV;
