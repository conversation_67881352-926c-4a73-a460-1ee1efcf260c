import { default as turf_distance } from '@turf/distance';
import { point } from '@turf/helpers';
import json2csv from 'csvjson-json2csv/json2csv';
import React from 'react';
import { useSelector } from 'umi';
import { calculateMedian } from '../../utils/calculations';
import styles from '../ResultTable/resultTable.css';

const ExportNewBuildsCSV = () => {
  const currentNewBuildsProperties = useSelector(
    (state) => state.CMA.currentNewBuildsProperties,
  );
  const currentPropertyAddress = useSelector(
    (state) => state.CMA.currentPropertyAddress,
  );
  const currentRadiusMile = useSelector((state) => state.CMA.currentRadiusMile);
  const eventCoordinates = useSelector((state) => state.CMA.eventCoordinates);
  const newConstructionFilters = useSelector(
    (state) => state.CMA.newConstructionFilters,
  );

  const getMedianPrice = () => {
    const medianPrice = calculateMedian(
      currentNewBuildsProperties.map((build) => build.price),
    );
    if (medianPrice) {
      return medianPrice;
    } else {
      return '-';
    }
  };

  const getMedianPSF = () => {
    const medianPSF = calculateMedian(
      currentNewBuildsProperties.map((build) => build.psf),
    );
    if (medianPSF) {
      return medianPSF;
    } else {
      return '-';
    }
  };

  const btnClickHandler = () => {
    if (currentNewBuildsProperties.length > 0) {
      const content = [];
      currentNewBuildsProperties.forEach((property) => {
        let dist = 'N/A';
        if (eventCoordinates.length > 0) {
          const propertyPoint = point(property.geom.coordinates);
          const eventPoint = point(eventCoordinates);
          const distance = turf_distance(propertyPoint, eventPoint, {
            units: 'miles',
          });

          dist = Math.floor(distance * 10) / 10 + ' mi';
        }
        console.log('property 123', property);

        content.push({
          Status: property.status,
          Builder: property.builder,
          Community: property.community,
          Address: property.address,
          Distance: dist,
          Price: `$${property.price
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`,
          Beds: property.bed_rooms,
          Baths: property.bath_rooms,
          Sqft: property.square_feet
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ','),
          Garage: property.garage,
          'First Seen': property.first_seen.split('T')[0],
          PSF: `$${
            !property.psf
              ? 0
              : property.psf.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
          }`,
          lng: property.geom.coordinates[0],
          lat: property.geom.coordinates[1],
        });
      });

      const csv = json2csv(content);

      const filters =
        Object.keys(newConstructionFilters)
          .map((key) => {
            return `${JSON.stringify(key)}, ${JSON.stringify(
              newConstructionFilters[key],
            )}`;
          })
          .join('\n') +
        '\nMedian Price, ' +
        getMedianPrice().toFixed(2) +
        '\nMedian PSF, ' +
        getMedianPSF().toFixed(2) +
        '\n\n';

      const blob = new Blob([filters + csv], {
        type: 'text/csv;charset=utf-8;',
      });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute(
        'download',
        `New Constructions - ${currentPropertyAddress.streetAddress} - ${currentRadiusMile} miles.csv`,
      );
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
      alert('No new builds data to export!');
    }
  };

  return (
    <button
      style={{
        display:
          currentNewBuildsProperties.length > 0 ? 'inline-block' : 'none',
      }}
      onClick={btnClickHandler}
      className={styles.csvButtonSmall}
    >
      Export
      <br />
      to CSV
    </button>
  );
};

export default ExportNewBuildsCSV;
