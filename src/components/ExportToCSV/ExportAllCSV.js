import { dateFormat } from '@/components/ResultTable/ResultTable';
import { showSFROperatorsFullNameOnly } from '@/components/SFRBrandConvertFunction';
import { formatter } from '@/utils/money';
import { default as turf_distance } from '@turf/distance';
import { point } from '@turf/helpers';
import { Checkbox, Modal, Radio, Space } from 'antd';
import json2csv from 'csvjson-json2csv/json2csv';
import moment from 'moment';
import { useEffect, useState } from 'react';
import { connect } from 'umi';
import styles from '../ResultTable/resultTable.css';

const ExportAllCSV = connect(({ CMA }) => ({
  builtForRentData: CMA.builtForRentData,
  currentNationalOperatorsPropertiesFiltered:
    CMA.currentNationalOperatorsPropertiesFiltered,
  currentHotPadsPropertiesFiltered: CMA.currentHotPadsPropertiesFiltered,
  currentMLSPropertiesFiltered: CMA.currentMLSPropertiesFiltered,
  lastSalePublicRecordDataForRender: CMA.lastSalePublicRecordDataForRender,
  selectedRowKeysMLSLease: CMA.selectedRowKeysMLSLease,
  selectedRowKeysMLSSale: CMA.selectedRowKeysMLSSale,
  selectedRowKeysNationalOperators: CMA.selectedRowKeysNationalOperators,
  selectedRowKeysHotPads: CMA.selectedRowKeysHotPads,
  builtForRentSelectedRowKeys: CMA.builtForRentSelectedRowKeys,
  lastSalePublicRecordSelectedRowKey: CMA.lastSalePublicRecordSelectedRowKey,
  currentPropertyAddress: CMA.currentPropertyAddress,
  currentRadiusMile: CMA.currentRadiusMile,
  drawnCustomPolygons: CMA.drawnCustomPolygons,
  eventCoordinates: CMA.eventCoordinates,
  // from filter:
  // eventCoordinates: CMA.eventCoordinates,
  searchingMode: CMA.searchingMode,
  compingMode: CMA.compingMode,
  // currentPropertyAddress: CMA.currentPropertyAddress,
  // filter values
  minBeds: CMA.minBeds,
  maxBeds: CMA.maxBeds,
  relationBeds: CMA.relationBeds,
  minBaths: CMA.minBaths,
  maxBaths: CMA.maxBaths,
  relationBaths: CMA.relationBaths,
  minSqft: CMA.minSqft,
  maxSqft: CMA.maxSqft,
  relationSqft: CMA.relationSqft,
  minYearBuilt: CMA.minYearBuilt,
  maxYearBuilt: CMA.maxYearBuilt,
  relationYearBuilt: CMA.relationYearBuilt,
  minCumulativeDaysOnMarket: CMA.minCumulativeDaysOnMarket,
  maxCumulativeDaysOnMarket: CMA.maxCumulativeDaysOnMarket,
  relationCumulativeDaysOnMarket: CMA.relationCumulativeDaysOnMarket,
  minCoveredParking: CMA.minCoveredParking,
  maxCoveredParking: CMA.maxCoveredParking,
  relationCoveredParking: CMA.relationCoveredParking,
  // checkedPropertySubTypes: CMA.checkedPropertySubTypes,
  selectedPoolAllowed: CMA.selectedPoolAllowed,
  selectedRowKeysRealtorDotCom: CMA.selectedRowKeysRealtorDotCom,
  realtorSingleFamilyDataForRender: CMA.realtorSingleFamilyDataForRender,
}))(function (props) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [checkedItems, setCheckedItems] = useState([]);
  const displayButton = () => {
    if (
      checkDataExist('National SFR Operators Listings') ||
      checkDataExist('Portal Listings') ||
      checkDataExist('Secondary Portal Listings') ||
      checkDataExist('MLS') ||
      checkDataExist('Built For Rent') ||
      checkDataExist('Last Sale Public Record')
    ) {
      return true;
    }
    return false;
  };

  const btnClickHandler = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    generateCSV(checkedItems);
    setIsModalOpen(false);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const checkDataExist = (type) => {
    if (
      props.realtorSingleFamilyDataForRender &&
      type === 'Secondary Portal Listings'
    ) {
      const selectedRealtorRental =
        props.realtorSingleFamilyDataForRender.filter((realtorSF) =>
          props.selectedRowKeysRealtorDotCom.includes(`${realtorSF.id}`),
        );
      return selectedRealtorRental.length > 0 ? true : false;
    }

    if (type === 'Last Sale Public Record') {
      const selectedLastSalePublicRecord =
        props.lastSalePublicRecordDataForRender.filter((lastSalePublicRecord) =>
          props.lastSalePublicRecordSelectedRowKey.includes(
            `${lastSalePublicRecord.key}`,
          ),
        );
      return selectedLastSalePublicRecord.length > 0 ? true : false;
    }
    if (type === 'Built For Rent') {
      console.log('selectedBuiltForRent', props.builtForRentSelectedRowKeys);

      const selectedBuiltForRent = props.builtForRentData.filter(
        (builtForRent) =>
          props.builtForRentSelectedRowKeys.includes(`${builtForRent.apn}`),
      );
      return selectedBuiltForRent.length > 0 ? true : false;
    }
    if (type === 'National SFR Operators Listings') {
      const selectedNSFR =
        props.currentNationalOperatorsPropertiesFiltered.filter((nsfr) =>
          props.selectedRowKeysNationalOperators.includes(nsfr.base_id),
        );
      return selectedNSFR.length > 0 ? true : false;
    } else if (type === 'Portal Listings') {
      const selectedHotPads = props.currentHotPadsPropertiesFiltered.filter(
        (hotPads) => props.selectedRowKeysHotPads.includes(hotPads.base_id),
      );
      return selectedHotPads.length > 0 ? true : false;
    } else if (type === 'MLS') {
      let selectedMLS;
      if (props.searchingMode === 'Lease') {
        selectedMLS = props.currentMLSPropertiesFiltered.filter((mls) =>
          props.selectedRowKeysMLSLease.includes(mls.mlsid),
        );
      } else {
        selectedMLS = props.currentMLSPropertiesFiltered.filter((mls) =>
          props.selectedRowKeysMLSSale.includes(mls.mlsid),
        );
      }
      return selectedMLS.length > 0 ? true : false;
    }
  };

  const generateCSV = () => {
    console.log('checkedItems', checkedItems);
    const csvData = [];

    let selectedNSFR = props.currentNationalOperatorsPropertiesFiltered.filter(
      (nsfr) => props.selectedRowKeysNationalOperators.includes(nsfr.base_id),
    );
    let selectedHotPads = props.currentHotPadsPropertiesFiltered.filter(
      (hotPads) => props.selectedRowKeysHotPads.includes(hotPads.base_id),
    );
    // let selectedBuiltForRent = props.builtForRentData.filter((builtForRent) =>
    //   props.builtForRentSelectedRowKeys.includes(`${builtForRent.apn}`),
    // );
    let selectedLastSalePublicRecord =
      props.lastSalePublicRecordDataForRender.filter((lastSalePublicRecord) =>
        props.lastSalePublicRecordSelectedRowKey.includes(
          `${lastSalePublicRecord.key}`,
        ),
      );
    let selectedRealtorSF = props.realtorSingleFamilyDataForRender.filter(
      (realtorSF) =>
        props.selectedRowKeysRealtorDotCom.includes(`${realtorSF.id}`),
    );

    if (checkedItems.includes('National SFR Operators Listings')) {
      // console.log('NSFR');

      let hideDistance = false;
      if (
        props.drawnCustomPolygons.length > 0 &&
        props.eventCoordinates.length > 0
      ) {
        for (let i = 0; i < selectedNSFR.length; i++) {
          const propertyPoint = point(selectedNSFR[i].geom.coordinates);
          const eventPoint = point(props.eventCoordinates);
          const distance = turf_distance(propertyPoint, eventPoint, 'miles');

          selectedNSFR[i].distance = distance * 1609.34; // convert to meters
        }
      } else if (
        props.drawnCustomPolygons.length > 0 &&
        props.eventCoordinates.length == 0
      ) {
        hideDistance = true;
      }

      // default: distance nearest to furthest
      const sortedNSFR = selectedNSFR.sort((a, b) => +a.distance - +b.distance);
      for (let i = 0; i < sortedNSFR.length; i++) {
        const rowData = sortedNSFR[i];
        csvData.push(
          getFormattedCSVRow({
            rowData: rowData,
            type: 'National SFR Operators Listings',
            hideDistance: hideDistance,
          }),
        );
      }
    }

    if (checkedItems.includes('Portal Listings')) {
      // console.log('HotPads');

      let hideDistance = false;
      if (
        props.drawnCustomPolygons.length > 0 &&
        props.eventCoordinates.length > 0
      ) {
        for (let i = 0; i < selectedHotPads.length; i++) {
          const propertyPoint = point(selectedHotPads[i].geom.coordinates);
          const eventPoint = point(props.eventCoordinates);
          const distance = turf_distance(propertyPoint, eventPoint, 'miles');

          selectedHotPads[i].distance = distance * 1609.34; // convert to meters
        }
      } else if (
        props.drawnCustomPolygons.length > 0 &&
        props.eventCoordinates.length == 0
      ) {
        hideDistance = true;
      }

      // default: distance nearest to furthest
      const sortedHotPads = selectedHotPads.sort(
        (a, b) => +a.distance - +b.distance,
      );
      for (let i = 0; i < sortedHotPads.length; i++) {
        const rowData = sortedHotPads[i];
        csvData.push(
          getFormattedCSVRow({
            rowData: rowData,
            type: 'Portal Listings',
            hideDistance: hideDistance,
          }),
        );
      }
    }

    if (checkedItems.includes('MLS')) {
      // console.log('MLS');
      let selectedMLS;
      if (props.searchingMode === 'Lease') {
        selectedMLS = props.currentMLSPropertiesFiltered.filter((mls) =>
          props.selectedRowKeysMLSLease.includes(mls.mlsid),
        );
      } else {
        selectedMLS = props.currentMLSPropertiesFiltered.filter((mls) =>
          props.selectedRowKeysMLSSale.includes(mls.mlsid),
        );
      }

      let hideDistance = false;
      if (
        props.drawnCustomPolygons.length > 0 &&
        props.eventCoordinates.length > 0
      ) {
        for (let i = 0; i < selectedMLS.length; i++) {
          const propertyPoint = point(selectedMLS[i].geography.coordinates);
          const eventPoint = point(props.eventCoordinates);
          const distance = turf_distance(propertyPoint, eventPoint, 'miles');

          selectedMLS[i].distance = distance * 1609.34; // convert to meters
        }
      } else if (
        props.drawnCustomPolygons.length > 0 &&
        props.eventCoordinates.length == 0
      ) {
        hideDistance = true;
      }

      // default: distance nearest to furthest
      // NOTE: to remove duplicated SFR as MLS, dont push if matching placekey found
      const sortedMLS = selectedMLS.sort((a, b) => +a.distance - +b.distance);
      for (let i = 0; i < sortedMLS.length; i++) {
        const rowData = sortedMLS[i];
        // console.log('rowData: ', rowData);
        let duplicateSFR, duplicateHotpads;
        if (checkedItems.length > 1) {
          // only remove duplicates if more than 1 source are exported
          duplicateSFR = selectedNSFR.find(
            (item) => item.placekey === rowData.placekey,
          );
          duplicateHotpads = selectedHotPads.find(
            (item) => item.placekey === rowData.placekey,
          );
        }

        if (!duplicateSFR && !duplicateHotpads) {
          csvData.push(
            getFormattedCSVRow({
              rowData: rowData,
              type: 'MLS',
              searchingMode: props.searchingMode,
              hideDistance: hideDistance,
            }),
          );
        }
      }
    }

    // if (checkedItems.includes('Built For Rent')) {
    //   let hideDistance = false;
    //   if (
    //     props.drawnCustomPolygons.length > 0 &&
    //     props.eventCoordinates.length > 0
    //   ) {
    //     for (let i = 0; i < selectedBuiltForRent.length; i++) {
    //       const propertyPoint = point([
    //         +selectedBuiltForRent[i].latitude,
    //         +selectedBuiltForRent[i].longitude,
    //       ]);
    //       const eventPoint = point(props.eventCoordinates);
    //       const distance = turf_distance(propertyPoint, eventPoint, 'miles');
    //       console.log('CSV Distance', distance);
    //       // selectedBuiltForRent[i].distance = distance * 1609.34; // convert to meters
    //     }
    //   } else if (
    //     props.drawnCustomPolygons.length > 0 &&
    //     props.eventCoordinates.length == 0
    //   ) {
    //     hideDistance = true;
    //   }

    //   // default: distance nearest to furthest
    //   const sortedBuiltForRent = selectedBuiltForRent.sort(
    //     (a, b) => +a.distance - +b.distance,
    //   );
    //   for (let i = 0; i < sortedBuiltForRent.length; i++) {
    //     const rowData = sortedBuiltForRent[i];
    //     console.log('rowData', rowData);
    //     csvData.push(
    //       getFormattedCSVRow({
    //         rowData: rowData,
    //         type: 'Built For Rent',
    //         hideDistance: hideDistance,
    //       }),
    //     );
    //   }
    // }

    if (checkedItems.includes('Secondary Portal Listings')) {
      let hideDistance = false;
      if (
        props.drawnCustomPolygons.length > 0 &&
        props.eventCoordinates.length > 0
      ) {
        for (let i = 0; i < selectedRealtorSF.length; i++) {
          const propertyPoint = point([
            +selectedRealtorSF[i].longitude,
            +selectedRealtorSF[i].latitude,
          ]);
          const eventPoint = point(props.eventCoordinates);
          const distance = turf_distance(propertyPoint, eventPoint, 'miles');
          // console.log('Secondary Portal Listings - distance', distance);
          selectedRealtorSF[i].distance = distance * 1609.34; // convert to meters
        }
      } else if (
        props.drawnCustomPolygons.length > 0 &&
        props.eventCoordinates.length === 0
      ) {
        hideDistance = true;
      }

      const sortedRealtorSF = selectedRealtorSF.sort(
        (a, b) => +a.distance - +b.distance,
      );
      for (let i = 0; i < sortedRealtorSF.length; i++) {
        const rowData = sortedRealtorSF[i];
        csvData.push(
          getFormattedCSVRow({
            rowData: rowData,
            type: 'Secondary Portal Listings',
            hideDistance: hideDistance,
          }),
        );
      }
    }

    if (checkedItems.includes('Last Sale Public Record')) {
      let hideDistance = false;
      if (
        props.drawnCustomPolygons.length > 0 &&
        props.eventCoordinates.length > 0
      ) {
        for (let i = 0; i < selectedLastSalePublicRecord.length; i++) {
          const propertyPoint = point(
            selectedLastSalePublicRecord[i].geom.coordinates,
          );
          const eventPoint = point(props.eventCoordinates);
          const distance = turf_distance(propertyPoint, eventPoint, 'miles');
          selectedLastSalePublicRecord[i].distance = distance; // convert to meters
        }
      } else if (
        props.drawnCustomPolygons.length > 0 &&
        props.eventCoordinates.length == 0
      ) {
        hideDistance = true;
      }

      // default: distance nearest to furthest
      const sortedLastSalePublicRecord = selectedLastSalePublicRecord.sort(
        (a, b) => +a.distance - +b.distance,
      );
      for (let i = 0; i < sortedLastSalePublicRecord.length; i++) {
        const rowData = sortedLastSalePublicRecord[i];
        csvData.push(
          getFormattedCSVRow({
            rowData: rowData,
            type: 'Last Sale Public Record',
            hideDistance: hideDistance,
          }),
        );
      }
    }

    console.log('CSVDATA: ', csvData);

    let csv = json2csv(csvData);

    let compingSelection = '';
    if (props.compingMode === 'noFilter') {
      compingSelection = 'All';
    } else if (props.compingMode === 'smartFilter') {
      compingSelection = 'Match';
    } else if (props.compingMode === 'intelligentComping') {
      compingSelection = 'Intelligent';
    }

    // prettier-ignore
    const headerInfo = [
      [`"Date: "`, `${moment().format('MM/DD/YYYY')}\n`],
      [`"Address :"`, `${props.currentPropertyAddress.streetAddress}\n`],
      [`"Coordinates: "`, `"${props.eventCoordinates[1]},${props.eventCoordinates[0]}"\n`],
      [`"Radius: "`, `"${props.currentRadiusMile} miles"\n`],
      [`"Lease/Sale Mode: "`, `"${props.searchingMode === 'Lease' ? 'Lease' : 'Sale'}"\n`],
      [`"Comping Mode: "`, `"${compingSelection}"\n`],
      [`"Beds: "`, `"'${props.minBeds}-${props.maxBeds}"\n`],
      [`"Baths: "`, `"'${props.minBaths}-${props.maxBaths}"\n`],
      [`"Sqft: "`, `"'${props.minSqft} - ${props.maxSqft}"\n`],
      [`"Lot Size: "`, `"'${props.minLotSize} - ${props.maxLotSize}"\n`],
      [`"Year Built: "`, `"'${props.minYearBuilt} - ${props.maxYearBuilt}"\n`],
      [`"Cumulative Days on Market: "`, `"'${props.minCumulativeDaysOnMarket} - ${props.maxCumulativeDaysOnMarket}"\n`],
      [`"Covered Parking: "`, `"'${props.minCoveredParking} - ${props.maxCoveredParking}"\n`],
      // [`"Property Subtypes: "`, `"${props.checkedPropertySubTypes.join(' ')}"\n`],
      [`"Pool Allowed: "`, `"${props.selectedPoolAllowed ? 'Yes' : 'No'}"\n`],
      ['\n'],
    ];

    for (let i = headerInfo.length - 1; i >= 0; i--) {
      csv = headerInfo[i].toString() + csv;
    }

    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    var urlObject = window.URL || window.webkitURL || window;
    var save_link = document.createElementNS(
      'http://www.w3.org/1999/xhtml',
      'a',
    );
    save_link.href = urlObject.createObjectURL(blob);
    // save_link.download = `${tableName} - ${currentPropertyAddress.streetAddress} - ${currentRadiusMile} miles.csv`;
    save_link.download = getCSVFileName();
    save_link.click();
  };

  const getCSVFileName = () => {
    const compsSourceString = checkedItems.join(' - ');
    return `${compsSourceString} - ${
      props.currentPropertyAddress.streetAddress
    } - ${props.currentRadiusMile} miles - ${
      props.searchingMode === 'Lease' ? 'Lease' : 'Sale'
    }.csv`;
  };

  useEffect(() => {
    let tempCheckedItems = [];
    if (props.searchingMode === 'Lease') {
      if (checkDataExist('MLS')) {
        tempCheckedItems.push('MLS');
      }
      if (checkDataExist('National SFR Operators Listings')) {
        tempCheckedItems.push('National SFR Operators Listings');
      }
      if (checkDataExist('Portal Listings')) {
        tempCheckedItems.push('Portal Listings');
      }
      if (checkDataExist('Secondary Portal Listings')) {
        tempCheckedItems.push('Secondary Portal Listings');
      }
      if (checkDataExist('Built For Rent')) {
        tempCheckedItems.push('Built For Rent');
      }
    } else if (props.searchingMode === 'Sale') {
      if (checkDataExist('MLS')) {
        tempCheckedItems.push('MLS');
      }
      if (checkDataExist('Last Sale Public Record')) {
        tempCheckedItems.push('Last Sale Public Record');
      }
    }
    setCheckedItems(tempCheckedItems);
  }, [props.searchingMode, props.currentMLSPropertiesFiltered, props.currentHotPadsPropertiesFiltered, props.currentNationalOperatorsPropertiesFiltered, props.builtForRentData, props.lastSalePublicRecordDataForRender]);

  return (
    <>
      <button
        style={{ display: displayButton() ? 'inline-block' : 'none' }}
        onClick={btnClickHandler}
        className={styles.csvButtonSmall}
      >
        Export
        <br />
        to CSV
      </button>
      <Modal
        title="Export to CSV"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        centered
        width={300}
      >
        <Checkbox.Group onChange={setCheckedItems} value={checkedItems}>
          <Space direction="vertical">
            {props.searchingMode === 'Lease' && (
              <>
                {/* {displayAllRadio() && <Radio value={0}>All</Radio>}
                {checkDataExist('National SFR Operators Listings') && (
                  <Radio value={1}>National SFR Operators Listings</Radio>
                )}
                {checkDataExist('Portal Listings') && (
                  <Radio value={2}>Portal Listings</Radio>
                )}
                {checkDataExist('Built For Rent') && (
                  <Radio value={4}>Built For Rent</Radio>
                )} */}
                <Checkbox value="MLS" disabled={!checkDataExist('MLS')}>
                  MLS
                </Checkbox>
                <Checkbox
                  value="National SFR Operators Listings"
                  disabled={!checkDataExist('National SFR Operators Listings')}
                >
                  National SFR Operators Listings
                </Checkbox>
                <Checkbox
                  value="Portal Listings"
                  disabled={!checkDataExist('Portal Listings')}
                >
                  Portal Listings
                </Checkbox>
                <Checkbox
                  value="Secondary Portal Listings"
                  disabled={!checkDataExist('Secondary Portal Listings')}
                >
                  Secondary Portal Listings
                </Checkbox>
              </>
            )}
            {props.searchingMode === 'Sale' && (
              <>
                {/* {checkDataExist('MLS') && <Radio value={3}>MLS</Radio>}
                {checkDataExist('Last Sale Public Record') && (
                  <Radio value={5}>Last Sale Public Record</Radio>
                )} */}
                <Checkbox value="MLS" disabled={!checkDataExist('MLS')}>
                  MLS
                </Checkbox>
                <Checkbox
                  value="Last Sale Public Record"
                  disabled={!checkDataExist('Last Sale Public Record')}
                >
                  Last Sale Public Record
                </Checkbox>
              </>
            )}
          </Space>
        </Checkbox.Group>
      </Modal>
    </>
  );
});

export default ExportAllCSV;

// format
// {
//   Address: '',
//   'Dist.': '',
//   Status: '',
//   Owner: '',
//   Rent: '',
//   Type: '',
//   YrB: '',
//   Bd: '',
//   Ba: '',
//   Sqft: '',
//   CDOM: '',
//   'Avail.': '',
//   'Cls.': '',
//   PSF: '',
// };

const getFormattedCSVRow = ({ rowData, type, searchingMode, hideDistance }) => {
  console.log('csvv getFormattedCSVRow', {
    rowData,
    type,
    searchingMode,
    hideDistance,
  });
  let columnDataIndex;
  if (type === 'MLS') {
    columnDataIndex = getCsvColumnDataIndexMLS(searchingMode);
  } else if (type === 'Built For Rent') {
    columnDataIndex = getCsvColumnDataIndexBuiltForRent();
  } else if (type === 'Last Sale Public Record') {
    columnDataIndex = getCsvColumnDataIndexLastSalePublicRecord();
  } else if (type === 'Secondary Portal Listings') {
    columnDataIndex = getCsvColumnDataIndexRealtorSF();
  } else {
    columnDataIndex = getCsvColumnDataIndex(type);
  }

  const csvRowData = {};

  for (let i = 0; i < columnDataIndex.length; i++) {
    const columnName = columnDataIndex[i].columnName;

    if (columnName === 'Address') {
      if (
        [
          'National SFR Operators Listings',
          'Portal Listings',
          'Secondary Portal Listings',
        ].includes(type)
      ) {
        let addressData;
        console.log('rsf', rowData[columnDataIndex[i].dataIndex[0]]);
        if (rowData[columnDataIndex[i].dataIndex[0]].includes(',')) {
          addressData = rowData[columnDataIndex[i].dataIndex[0]].replace(
            /,(?! )/g,
            ', ',
          );
        } else {
          addressData = `${rowData[columnDataIndex[i].dataIndex[0]]}, ${
            rowData[columnDataIndex[i].dataIndex[1]]
          }, ${rowData[columnDataIndex[i].dataIndex[2]]}`;
        }
        csvRowData[columnName] = addressData;
      } else if (['MLS', 'Built For Rent'].includes(type)) {
        let addressData = rowData[columnDataIndex[i].dataIndex[0]].replace(
          / /g,
          ' ',
        );
        addressData =
          addressData +
          ', ' +
          rowData[columnDataIndex[i].dataIndex[1]] +
          ', ' +
          rowData[columnDataIndex[i].dataIndex[2]] +
          ' ' +
          rowData[columnDataIndex[i].dataIndex[3]];
        csvRowData[columnName] = addressData;
      } else if (type === 'Last Sale Public Record') {
        let addressData = rowData[columnDataIndex[i].dataIndex];
        csvRowData[columnName] = addressData;
      }
    } else if (columnName === 'Dist.') {
      if (hideDistance) continue;
      let distData = rowData[columnDataIndex[i].dataIndex];
      console.log('distData', distData);
      if (type !== 'Built For Rent') {
        distData = (parseFloat(distData) / 1609.34).toFixed(1) + ' mi';
      } else {
        distData = ((distData * 100) / 1609).toFixed(1) + ' mi';
      }
      csvRowData[columnName] = distData;
    } else if (columnName === 'Status') {
      if (
        [
          // 'National SFR Operators Listings',
          'Portal Listings',
        ].includes(type)
      ) {
        let statusData = rowData[columnDataIndex[i].dataIndex];
        // statusData = statusData ? 'Available' : 'Closed';
        csvRowData[columnName] = statusData;
      } else if (
        ['MLS', 'National SFR Operators Listings', 'Built For Rent'].includes(
          type,
        )
      ) {
        let statusData = rowData[columnDataIndex[i].dataIndex];
        console.log('csvv statusData', statusData);
        if (statusData) {
          statusData = rowData[columnDataIndex[i].dataIndex];
        } else {
          statusData = 'N/A';
        }
        csvRowData[columnName] = statusData;
      } else if (type === 'Last Sale Public Record') {
        let statusData = 'Closed';
        csvRowData[columnName] = statusData;
      }
    } else if (columnName === 'Owner') {
      if (['Portal Listings'].includes(type)) {
        csvRowData[columnName] = 'HotPads';
      } else if (type === 'MLS') {
        if (rowData.sfr_owner) {
          csvRowData[columnName] = showSFROperatorsFullNameOnly(
            rowData.sfr_owner,
          );
        } else {
          csvRowData[columnName] = 'MLS';
        }
      } else if (['Built For Rent'].includes(type)) {
        let ownerData = rowData[columnDataIndex[i].dataIndex];
        csvRowData[columnName] = ownerData;
      } else if (type === 'Secondary Portal Listings') {
        let ownerData = 'Realtor.com';
        csvRowData[columnName] = ownerData;
      } else if (type === 'Last Sale Public Record') {
        let ownerData = 'Public Record';
        csvRowData[columnName] = ownerData;
      } else {
        let ownerData = rowData[columnDataIndex[i].dataIndex];
        ownerData = showSFROperatorsFullNameOnly(ownerData);
        csvRowData[columnName] = ownerData;
      }
    } else if (columnName === 'Rent' || columnName === 'Sold') {
      console.log(rowData);
      let rentData = rowData[columnDataIndex[i].dataIndex];
      if (isNaN(rentData) || rentData === null) {
        rentData = 'N/A';
      } else {
        rentData = '$' + formatter(parseFloat(rentData).toFixed());
      }
      csvRowData[columnName] = rentData;
    } else if (columnName === 'Type') {
      let typeData = rowData[columnDataIndex[i].dataIndex];
      if (
        typeData &&
        typeData.length > 0 &&
        (typeData.toLowerCase() === 'single family residence' ||
          typeData.toLowerCase() === 'single family residential')
      ) {
        typeData = 'SFR';
      } else if (
        typeData &&
        typeData.length > 0 &&
        typeData.toLowerCase() === 'townhouse'
      ) {
        typeData = 'TH';
      } else if (typeData === null) {
        typeData = 'N/A';
      }
      csvRowData[columnName] = typeData;
      // }
    } else if (columnName === 'YrB') {
      let year = rowData[columnDataIndex[i].dataIndex];
      if (year === null) {
        year = 'N/A';
      }
      csvRowData[columnName] = year;
    } else if (columnName === 'Sqft') {
      let sqftData = rowData[columnDataIndex[i].dataIndex];
      sqftData = formatter(sqftData);
      if (sqftData && sqftData !== '0') {
        csvRowData[columnName] = sqftData;
      } else {
        csvRowData[columnName] = 'N/A';
      }
    }
    // else if (columnName === 'Avail.') {
    //   let availData = rowData[columnDataIndex[i].dataIndex];
    //   if (availData) {
    //     availData = availData && moment(availData).format(dateFormat);
    //   } else {
    //     availData = '';
    //   }
    //   csvRowData[columnName] = availData;
    // }
    else if (columnName === 'Cls.') {
      let clsData = rowData[columnDataIndex[i].dataIndex];
      if (clsData) {
        clsData = moment(clsData).format(dateFormat);
      } else {
        clsData = '';
      }
      csvRowData[columnName] = clsData;
    } else if (columnName === 'PSF') {
      let psfData =
        rowData[columnDataIndex[i].dataIndex[0]] /
        rowData[columnDataIndex[i].dataIndex[1]];
      psfData = '$' + psfData.toFixed(2);
      if (psfData && psfData !== '$Infinity' && psfData !== '$0.00') {
        csvRowData[columnName] = psfData;
      } else {
        csvRowData[columnName] = 'N/A';
      }
    } else if (columnName === 'Chg. $(%)') {
      const originalPrice = rowData[columnDataIndex[i].dataIndex[1]];
      const latestPrice = rowData[columnDataIndex[i].dataIndex[0]];
      const change = latestPrice - originalPrice;
      const changePercent = Math.round((change / originalPrice) * 100);
      csvRowData[columnName] = `${formatter(change)} (${changePercent}%)`;
    } else {
      if (rowData[columnDataIndex[i].dataIndex]) {
        csvRowData[columnName] = rowData[columnDataIndex[i].dataIndex];
      } else {
        csvRowData[columnName] = 'N/A';
      }
    }
  }
  return csvRowData;
};

const getCsvColumnDataIndex = (type) => {
  const csvColumnDataIndex = [
    {
      columnName: 'Address',
      dataIndex: ['address', 'standard_city', 'standard_state'],
    },
    { columnName: 'Dist.', dataIndex: 'distance' },
    {
      columnName: 'Status',
      dataIndex:
        type === 'National SFR Operators Listings' ? 'status' : 'status',
    },
    // {
    //   columnName: 'Owner',
    //   dataIndex: 'brand',
    // },
    {
      columnName: 'Rent',
      dataIndex: 'rent',
    },
    {
      columnName: 'Type',
      dataIndex: 'propertysubtype',
    },
    {
      columnName: 'YrB',
      dataIndex: 'yearbuilt',
    },
    {
      columnName: 'Bd',
      dataIndex: 'bed_rooms',
    },
    {
      columnName: 'Ba',
      dataIndex: 'bath_rooms',
    },
    {
      columnName: 'Sqft',
      dataIndex: 'square_feet',
    },
    {
      columnName: 'CDOM',
      dataIndex: 'cdom',
    },
    {
      columnName: 'Avail.',
      dataIndex: 'available_date',
    },
    {
      columnName: 'Cls.',
      dataIndex: 'close_date',
    },
    {
      columnName: 'PSF',
      dataIndex: ['rent', 'square_feet'],
    },
  ];
  // if (type === 'SFR') {
  const ownerColumnIndex = {
    columnName: 'Owner',
    dataIndex: 'brand',
  };
  csvColumnDataIndex.splice(3, 0, ownerColumnIndex);
  // }
  return csvColumnDataIndex;
};

const getCsvColumnDataIndexMLS = (searchingMode) => {
  return [
    {
      columnName: 'Address',
      dataIndex: ['fulladdress', 'city', 'stateorprovince', 'zipcode'],
    },
    { columnName: 'Dist.', dataIndex: 'distance' },
    {
      columnName: 'Status',
      dataIndex: 'status',
    },
    {
      columnName: 'Owner',
      dataIndex: '',
    },
    {
      columnName: searchingMode === 'Lease' ? 'Rent' : 'Sold',
      dataIndex: 'latestPrice',
    },
    {
      columnName: 'Type',
      dataIndex: 'propertysubtype',
    },
    {
      columnName: 'YrB',
      dataIndex: 'yearbuilt',
    },
    {
      columnName: 'Bd',
      dataIndex: 'bed',
    },
    {
      columnName: 'Ba',
      dataIndex: 'bath',
    },
    {
      columnName: 'Sqft',
      dataIndex: 'size',
    },
    {
      columnName: 'CDOM',
      dataIndex: 'cdom',
    },
    {
      columnName: 'Cls.',
      dataIndex: 'closedate',
    },
    {
      columnName: 'PSF',
      dataIndex: ['latestPrice', 'size'],
    },
  ];
};

const getCsvColumnDataIndexBuiltForRent = () => {
  return [
    {
      columnName: 'Address',
      dataIndex: ['address', 'city', 'state', 'zip_code'],
    },
    {
      columnName: 'Dist.',
      dataIndex: 'distance',
    },
    {
      columnName: 'Status',
      dataIndex: 'status',
    },
    {
      columnName: 'Owner',
      dataIndex: 'owner_name',
    },
    // {
    //   columnName: 'SFR Owner',
    //   dataIndex: 'sfr_owner'
    // },
    {
      columnName: 'Rent',
      dataIndex: 'closeprice',
    },
    // {
    //   columnName: 'Sold Price',
    //   dataIndex: 'deed_last_sale_price',
    // },
    // {
    //   columnName: 'Sold Date',
    //   dataIndex: 'deed_last_sale_date',
    // },
    // {
    //   columnName: 'Chg. $(%)',
    //   dataIndex: ['latestPrice', 'originalPrice'],
    // },
    {
      columnName: 'Type',
      dataIndex: 'propertysubtype',
    },
    {
      columnName: 'YrB',
      dataIndex: 'yearbuilt',
    },
    {
      columnName: 'Bd',
      dataIndex: 'bed',
    },
    {
      columnName: 'Ba',
      dataIndex: 'bath',
    },
    {
      columnName: 'Sqft',
      dataIndex: 'size',
    },
    {
      columnName: 'CDOM',
      dataIndex: 'cdom',
    },
    {
      columnName: 'Cls.',
      dataIndex: 'closedate',
    },
    {
      columnName: 'PSF',
      dataIndex: ['closeprice', 'size'],
    },
  ];
};

const getCsvColumnDataIndexLastSalePublicRecord = () => {
  return [
    {
      columnName: 'Address',
      dataIndex: 'address',
    },
    { columnName: 'Dist.', dataIndex: 'distance' },
    {
      columnName: 'Status',
      dataIndex: '',
    },
    {
      columnName: 'Owner',
      dataIndex: '',
    },
    {
      columnName: 'Sold',
      dataIndex: 'last_sale_price',
    },
    {
      columnName: 'Type',
      dataIndex: 'propertysubtype',
    },
    {
      columnName: 'YrB',
      dataIndex: 'year_built',
    },
    {
      columnName: 'Bd',
      dataIndex: 'bedroom',
    },
    {
      columnName: 'Ba',
      dataIndex: 'bathroom',
    },
    {
      columnName: 'Sqft',
      dataIndex: 'sqft',
    },
    {
      columnName: 'CDOM',
      dataIndex: '',
    },
    {
      columnName: 'Cls.',
      dataIndex: 'last_sale_date',
    },
    {
      columnName: 'PSF',
      dataIndex: ['last_sale_price', 'sqft'],
    },
  ];
};
const getCsvColumnDataIndexRealtorSF = () => {
  return [
    {
      columnName: 'Address',
      dataIndex: ['address', 'city', 'state', 'postal_code'],
    },
    { columnName: 'Dist.', dataIndex: 'distance' },
    {
      columnName: 'Status',
      dataIndex: 'status',
    },
    {
      columnName: 'Owner',
      dataIndex: '',
    },
    {
      columnName: 'Rent',
      dataIndex: 'currentprice',
    },
    {
      columnName: 'Type',
      dataIndex: 'propertytype',
    },
    {
      columnName: 'YrB',
      dataIndex: 'year_built',
    },
    {
      columnName: 'Bd',
      dataIndex: 'beds',
    },
    {
      columnName: 'Ba',
      dataIndex: 'baths',
    },
    {
      columnName: 'Sqft',
      dataIndex: 'square_feet',
    },
    {
      columnName: 'CDOM',
      dataIndex: 'listeddate',
    },
    {
      columnName: 'Cls.',
      dataIndex: 'removeddate',
    },
    {
      columnName: 'PSF',
      dataIndex: ['currentprice', 'square_feet'],
    },
  ];
};
