import { Checkbox, Modal } from 'antd';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'umi';
import { postLandCompsCSV } from '../../services/data';
import styles from '../ResultTable/resultTable.css';

const ExportLandCSV = () => {
  const {
    currentPropertyAddress,
    currentStatusMLS,
    minBeds,
    maxBeds,
    minBaths,
    maxBaths,
    minSqft,
    maxSqft,
    currentStartMLS,
    currentEndMLS,
    landCompsData,
    landCompsSelectedRowKey,
    landShowcaseData,
    landShowcaseSelectedRowKey,
    currentRadiusMile,
    landCrexiData,
    landCrexiSelectedRowKey,
  } = useSelector((state) => state.CMA);

  const [buttonOff, setButtonOff] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [CSVButtonLoading, setCSVButtonLoading] = useState(false);

  const [exportOptions, setExportOptions] = useState({
    landComps: false,
    landShowcase: false,
    landCrexi: false,
    currentPropertyAddress,
    currentStatusMLS,
    startDate: currentStartMLS,
    endDate: currentEndMLS,
    minBeds,
    maxBeds,
    minBaths,
    maxBaths,
    minSqft,
    maxSqft,
    landCompsSelectedRowKey,
    landShowcaseSelectedRowKey,
    landCrexiSelectedRowKey,
    currentRadiusMile,
  });

  const landClickHandler = () => {
    setIsModalOpen(true);
  };

  const isDataExist = (dataList, key, keyName) => {
    try {
      if (dataList && dataList.length > 0) {
        const filteredData = dataList.filter((data) => {
          return key.includes(data[keyName]);
        });
        console.log('LAND CSV is Data Exist', filteredData);
        return filteredData.length > 0;
      }
      return false;
    } catch (error) {
      console.log(error);
      return false;
    }
  };

  const handleModalOk = () => {
    setCSVButtonLoading(true);
    console.log('exportOptions', exportOptions);
    const fetchData = async () => {
      try {
        const response = await postLandCompsCSV({ body: exportOptions });
        if (response) {
          const blob = new Blob([response], {
            type: 'text/csv;charset=utf-8;',
          });

          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;

          const date = new Date().toISOString();
          const filename = `land_comps_${date}.csv`;
          a.download = filename;

          document.body.appendChild(a);
          a.click();

          window.URL.revokeObjectURL(url);
          a.remove();
        }
        setCSVButtonLoading(false);
      } catch (error) {
        console.error('Error downloading CSV:', error);
        setCSVButtonLoading(false);
      }
    };
    fetchData();
    setIsModalOpen(false);
  };

  const handleModalCancel = () => {
    setIsModalOpen(false);
  };

  const handleCheckboxChange = (option) => {
    setExportOptions((prev) => ({
      ...prev,
      [option]: !prev[option],
    }));
  };

  useEffect(() => {
    const hasLandCompsData = landCompsData && landCompsData.length > 0;
    const hasLandShowcaseData = landShowcaseData && landShowcaseData.length > 0;
    const hasLandCrexiData = landCrexiData && landCrexiData.length > 0;

    setButtonOff(
      !(hasLandCompsData || hasLandShowcaseData || hasLandCrexiData),
    );
  }, [landCompsData, landShowcaseData, landCrexiData]);

  useEffect(() => {
    const hasLandCompsData = isDataExist(
      landCompsData,
      landCompsSelectedRowKey,
      'mlsid',
    );
    const hasLandShowcaseData = isDataExist(
      landShowcaseData,
      landShowcaseSelectedRowKey,
      'base_id',
    );
    const hasLandCrexiData = isDataExist(
      landCrexiData,
      landCrexiSelectedRowKey,
      'id',
    );

    setExportOptions((prev) => ({
      ...prev,
      landComps: hasLandCompsData ? prev.landComps : false,
      landShowcase: hasLandShowcaseData ? prev.landShowcase : false,
      landCrexi: hasLandCrexiData ? prev.landCrexi : false,
      landCompsSelectedRowKey,
      landShowcaseSelectedRowKey,
      landCrexiSelectedRowKey,
      currentPropertyAddress,
      currentStatusMLS,
      currentRadiusMile,
      minBeds,
      maxBeds,
      minBaths,
      startDate: currentStartMLS,
      endDate: currentEndMLS,
      maxBaths,
      minSqft,
      maxSqft,
    }));
  }, [
    landCompsData,
    currentStartMLS,
    currentEndMLS,
    landCompsSelectedRowKey,
    landShowcaseData,
    landShowcaseSelectedRowKey,
    landCrexiData,
    landCrexiSelectedRowKey,
    currentPropertyAddress,
    currentStatusMLS,
    minBeds,
    maxBeds,
    minBaths,
    maxBaths,
    minSqft,
    maxSqft,
    currentRadiusMile,
  ]);

  const isLandCompsDataAvailable = () => {
    return isDataExist(landCompsData, landCompsSelectedRowKey, 'mlsid');
  };

  const isLandShowcaseDataAvailable = () => {
    return isDataExist(landShowcaseData, landShowcaseSelectedRowKey, 'base_id');
  };

  const isLandCrexiDataAvailable = () => {
    return isDataExist(landCrexiData, landCrexiSelectedRowKey, 'id');
  };

  return (
    <>
      <button
        style={{ display: !buttonOff ? 'inline-block' : 'none' }}
        onClick={landClickHandler}
        className={styles.csvButtonSmall}
      >
        Export
        <br />
        to CSV
      </button>

      <Modal
        title="Export to CSV"
        open={isModalOpen}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={300}
        centered
        okButtonProps={{
          disabled:
            !exportOptions.landComps &&
            !exportOptions.landShowcase &&
            !exportOptions.landCrexi,
          loading: CSVButtonLoading,
        }}
      >
        <div className="checkbox-group">
          <div>
            <Checkbox
              checked={exportOptions.landComps}
              onChange={() => handleCheckboxChange('landComps')}
              disabled={!isLandCompsDataAvailable()}
            >
              Land Comps
            </Checkbox>
          </div>
          <div>
            <Checkbox
              checked={exportOptions.landShowcase}
              onChange={() => handleCheckboxChange('landShowcase')}
              disabled={!isLandShowcaseDataAvailable()}
            >
              Land Showcase
            </Checkbox>
          </div>
          <div>
            <Checkbox
              checked={exportOptions.landCrexi}
              onChange={() => handleCheckboxChange('landCrexi')}
              disabled={!isLandCrexiDataAvailable()}
            >
              Land Crexi
            </Checkbox>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default ExportLandCSV;
