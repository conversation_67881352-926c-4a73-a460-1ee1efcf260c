import { Checkbox, Modal } from 'antd';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'umi';
import { postBFRCompsCSV } from '../../services/data';
import styles from '../ResultTable/resultTable.css';

const ExportBFRCSV = () => {
  const {
    currentPropertyAddress,
    currentStatusMLS,
    minBeds,
    maxBeds,
    minBaths,
    maxBaths,
    minSqft,
    maxSqft,
    builtForRentData,
    builtForRentSelectedRowKeys,
    currentRadiusMile,
    btrCommunityInfoSelectedRowKey,
  } = useSelector((state) => state.CMA);
  const btnClickHandler = () => {
    setIsModalOpen(true);
  };
  const [buttonOff, setButtonOff] = useState(true);
  const [exportOptions, setExportOptions] = useState({
    buildForRent: false,
    bfrCommunityInfo: false,
    currentPropertyAddress,
    currentStatusMLS,
    minBeds,
    maxBeds,
    minBaths,
    maxBaths,
    minSqft,
    maxSqft,
    builtForRentSelectedRowKeys,
    btrCommunityInfoSelectedRowKey,
    currentRadiusMile,
  });
  const [isModalOpen, setIsModalOpen] = useState(false);
  const isDataExist = (dataList, key) => {
    try {
      if (dataList && dataList.length > 0) {
        const filteredData = dataList.filter((data) => {
          return key.includes(data.apn);
        });
        console.log('BFR CSV is Data Exist', filteredData);
        return filteredData.length > 0;
      }
      return false;
    } catch (error) {
      console.log(error);
      return false;
    }
  };
  const [CSVButtonLoading, setCSVButtonLoading] = useState(false);

  const handleModalOk = () => {
    setCSVButtonLoading(true);
    console.log('exportOptions', exportOptions);
    const fetchData = async () => {
      try {
        const response = await postBFRCompsCSV({ body: exportOptions });
        if (response) {
          // Create blob directly from the response string
          const blob = new Blob([response], {
            type: 'text/csv;charset=utf-8;',
          });

          // Create download URL
          const url = window.URL.createObjectURL(blob);

          // Create and trigger download
          const a = document.createElement('a');
          a.href = url;

          // Create filename with current date
          const date = new Date().toISOString();
          const filename = `bfr_comps_${date}.csv`;
          a.download = filename;

          // Trigger download
          document.body.appendChild(a);
          a.click();

          // Cleanup
          window.URL.revokeObjectURL(url);
          a.remove();
        }
        setCSVButtonLoading(false);
      } catch (error) {
        console.error('Error downloading CSV:', error);
        // Handle error appropriately
      }
    };
    fetchData();
    setIsModalOpen(false);
  };

  const handleModalCancel = () => {
    setIsModalOpen(false);
  };

  const handleCheckboxChange = (option) => {
    setExportOptions((prev) => ({
      ...prev,
      [option]: !prev[option],
    }));
  };
  useEffect(() => {
    const hasBFRData = isDataExist(
      builtForRentData,
      builtForRentSelectedRowKeys,
    );
    const hasCommunityData =
      btrCommunityInfoSelectedRowKey &&
      btrCommunityInfoSelectedRowKey.length > 0;

    setButtonOff(!(hasBFRData || hasCommunityData));
  }, [
    builtForRentData,
    builtForRentSelectedRowKeys,
    btrCommunityInfoSelectedRowKey,
  ]);
  useEffect(() => {
    const hasBFRData = isDataExist(
      builtForRentData,
      builtForRentSelectedRowKeys,
    );
    const hasCommunityData =
      btrCommunityInfoSelectedRowKey &&
      btrCommunityInfoSelectedRowKey.length > 0;

    setExportOptions((prev) => ({
      ...prev,
      buildForRent: hasBFRData ? prev.buildForRent : false,
      bfrCommunityInfo: hasCommunityData ? prev.bfrCommunityInfo : false,
      builtForRentSelectedRowKeys,
      btrCommunityInfoSelectedRowKey,
      currentPropertyAddress,
      currentStatusMLS,
      currentRadiusMile,
      minBeds,
      maxBeds,
      minBaths,
    }));
  }, [
    builtForRentData,
    builtForRentSelectedRowKeys,
    btrCommunityInfoSelectedRowKey,
    currentPropertyAddress,
    currentStatusMLS,
    minBeds,
    maxBeds,
    minBaths,
    currentRadiusMile,
  ]);
  const isBFRDataAvailable = () => {
    return isDataExist(builtForRentData, builtForRentSelectedRowKeys);
  };

  const isBFRCommunityDataAvailable = () => {
    return (
      btrCommunityInfoSelectedRowKey &&
      btrCommunityInfoSelectedRowKey.length > 0
    );
  };
  return (
    <>
      <button
        style={{ display: !buttonOff ? 'inline-block' : 'none' }}
        onClick={btnClickHandler}
        className={styles.csvButtonSmall}
      >
        Export
        <br />
        to CSV
      </button>

      <Modal
        title="Export to CSV"
        open={isModalOpen}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={300}
        centered
        okButtonProps={{
          disabled:
            !exportOptions.buildForRent && !exportOptions.bfrCommunityInfo,
          loading: CSVButtonLoading,
        }}
      >
        <div className="checkbox-group">
          <div>
            <Checkbox
              checked={exportOptions.buildForRent}
              onChange={() => handleCheckboxChange('buildForRent')}
              disabled={!isBFRDataAvailable()}
            >
              Build For Rent
            </Checkbox>
          </div>
          <div>
            <Checkbox
              checked={exportOptions.bfrCommunityInfo}
              onChange={() => handleCheckboxChange('bfrCommunityInfo')}
              disabled={!isBFRCommunityDataAvailable()}
            >
              BFR Community Information
            </Checkbox>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default ExportBFRCSV;
