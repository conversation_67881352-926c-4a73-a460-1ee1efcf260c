import { Button } from 'antd';
// @ts-ignore
import { LoadingOutlined } from '@ant-design/icons';
import { Spin } from 'antd';
import json2csv from 'csvjson-json2csv/json2csv';
import moment from 'moment';
import React, { useState } from 'react';

// Define types based on the MultiFamilyApartments component
type MultiFamilyFeatureType = {
  type: 'Feature';
  geometry: {
    type: 'Point';
    coordinates: number[];
  };
  properties: {
    distance_mi: number;
    base_id: string;
    first_seen: string | null;
    last_seen: string | null;
    listing_id: string | null;
    listing_type_id: string | null;
    name: string | null;
    phone: string | null;
    url: string | null;
    city: string | null;
    county: string | null;
    state: string | null;
    country: string | null;
    zipcode: string | null;
    address: string | null;
    listing_dma: string | null;
    min_rent: number | null;
    max_rent: number | null;
    min_bedrooms: number | null;
    property_type: string;
    latitude: number | null;
    longitude: number | null;
    company_id: string | null;
    unit_availabilities: number | null;
    url_external: string | null;
    rent_info: Record<string, any> | null;
    description: string | null;
    year_built: number | null;
    total_property_units: number | null;
    property_unit_type: string | null;
    amenities: string[] | null;
    features: string[] | null;
    fees_policies: Record<string, any> | null;
    important_details: Record<string, any> | null;
    computed?: {
      vacant_pct?: number;
      absorption_pct?: number;
      [key: string]: any;
    };
    units?: Array<{
      beds?: number | null;
      [key: string]: any;
    }>;
  };
};

interface ExportMultiFamilyCSVProps {
  selectedRowKeys: React.Key[];
  data: MultiFamilyFeatureType[];
  currentPropertyAddress?: {
    streetAddress: string;
  };
  currentRadiusMile?: number;
}

const antIcon = <LoadingOutlined style={{ fontSize: 16 }} spin />;

export const ExportMultiFamilyCSV: React.FC<ExportMultiFamilyCSVProps> = ({
  selectedRowKeys,
  data,
  currentPropertyAddress,
  currentRadiusMile,
}) => {
  const [loading, setLoading] = useState(false);

  const handleExport = () => {
    if (!selectedRowKeys.length || !data.length) {
      return;
    }

    setLoading(true);

    try {
      // Filter data to only selected rows
      const selectedData = data.filter((item) =>
        selectedRowKeys.includes(item.properties.base_id),
      );

      // Sort by distance (nearest to furthest)
      const sortedData = selectedData.sort(
        (a, b) => a.properties.distance_mi - b.properties.distance_mi,
      );

      // Calculate room keys from all data to ensure consistent columns
      let rooms = [] as number[];
      for (const f of data) {
        for (const unit of f.properties.units || []) {
          if (typeof unit.beds === 'number') {
            rooms.push(unit.beds);
          }
        }
      }
      const roomKeys = [...new Set(rooms)].sort((a, b) => a - b);

      // Transform data for CSV
      const csvData = sortedData.map((item) => {
        const props = item.properties;

        // Base properties
        const baseData: Record<string, any> = {
          Distance: props.distance_mi
            ? `${props.distance_mi.toFixed(2)} mi`
            : 'N/A',
          Name: props.name || 'N/A',
          Address: props.address || 'N/A',
          City: props.city || 'N/A',
          State: props.state || 'N/A',
          Zipcode: props.zipcode || 'N/A',
          'Year Built': props.year_built || 'N/A',
          '# of Units': props.total_property_units || 'N/A',
          'Unit Type': props.property_unit_type || 'N/A',
          'Vacancy %': props.computed?.vacant_pct
            ? `${props.computed.vacant_pct}%`
            : 'N/A',
          'Absorption %': props.computed?.absorption_pct
            ? `${props.computed.absorption_pct}%`
            : 'N/A',
        };

        // Add dynamic bedroom columns (Count and Rent for each bedroom type)
        roomKeys.forEach((key) => {
          const bedroomType = key === 0 ? 'Studio' : `${key}BD`;
          const countKey = `unit_count_${key}bd`;
          const rentKey = `avg_rent_${key}bd`;

          baseData[`${bedroomType} Count`] = props.computed?.[countKey]
            ? props.computed[countKey].toLocaleString()
            : 'N/A';
          baseData[`${bedroomType} Rent`] = props.computed?.[rentKey]
            ? `$${props.computed[rentKey].toLocaleString()}`
            : 'N/A';
        });

        // Add remaining properties
        const additionalData = {
          'Min Rent': props.min_rent
            ? `$${props.min_rent.toLocaleString()}`
            : 'N/A',
          'Max Rent': props.max_rent
            ? `$${props.max_rent.toLocaleString()}`
            : 'N/A',
          Phone: props.phone || 'N/A',
          'External URL': props.url_external || 'N/A',
          'Property Type': props.property_type || 'N/A',
          'Unit Availabilities': props.unit_availabilities || 'N/A',
          County: props.county || 'N/A',
          'First Seen': props.first_seen
            ? moment(props.first_seen).format('MM/DD/YYYY')
            : 'N/A',
          'Last Seen': props.last_seen
            ? moment(props.last_seen).format('MM/DD/YYYY')
            : 'N/A',
          Latitude: props.latitude || 'N/A',
          Longitude: props.longitude || 'N/A',
        };

        return { ...baseData, ...additionalData };
      });

      let csv = json2csv(csvData);

      const headerInfo = [
        [`"Export Date: "`, `${moment().format('MM/DD/YYYY')}\n`],
        [
          `"Property Address: "`,
          `"${currentPropertyAddress?.streetAddress || 'N/A'}"\n`,
        ],
        [`"Search Radius: "`, `"${currentRadiusMile || 'N/A'} miles"\n`],
        [`"Total Records: "`, `"${csvData.length}"\n`],
        ['\n'],
      ];

      for (let i = headerInfo.length - 1; i >= 0; i--) {
        csv = headerInfo[i].toString() + csv;
      }

      const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
      const urlObject = window.URL || window.webkitURL || window;
      const saveLink = document.createElementNS(
        'http://www.w3.org/1999/xhtml',
        'a',
      ) as HTMLAnchorElement;
      saveLink.href = urlObject.createObjectURL(blob);

      const fileName = `Multi-Family Apartments - ${
        currentPropertyAddress?.streetAddress || 'Export'
      } - ${currentRadiusMile || 'N/A'} miles - ${moment().format(
        'YYYY-MM-DD',
      )}.csv`;
      saveLink.download = fileName;
      saveLink.click();

      urlObject.revokeObjectURL(saveLink.href);
    } catch (error) {
      console.error('Error exporting CSV:', error);
    } finally {
      setLoading(false);
    }
  };

  const isDisabled = !selectedRowKeys.length || !data.length || loading;

  return (
    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
      <Button
        type="default"
        onClick={handleExport}
        disabled={isDisabled}
        size="small"
      >
        Export Selected to CSV ({selectedRowKeys.length})
      </Button>
      {loading && <Spin indicator={antIcon} />}
    </div>
  );
};

export default ExportMultiFamilyCSV;
