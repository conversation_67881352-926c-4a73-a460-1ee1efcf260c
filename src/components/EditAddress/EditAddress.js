import userGroupAccess from '@/userGroupAccess.json';
import {
  Button,
  Checkbox,
  Input,
  InputNumber,
  message,
  Modal,
  Row,
  Table,
  Tooltip,
} from 'antd';
import isEmpty from 'lodash.isempty';
import { useEffect, useState } from 'react';
import { FaPencilAlt } from 'react-icons/fa';
import { MdAutorenew } from 'react-icons/md';
import { connect } from 'umi';
import {
  getNewHouseAVMRentData,
  getNewHouseAVMRentMLData,
  getNewHouseAVMSalesMLData,
} from '../../services/data';
import { getMetroNameForParamViaZIPCode } from '../../utils/geography';
import { userGroupHasAccess } from '../../utils/userGroup';
import styles from './editAddress.css';

const DEFAULT_FORM_DATA = {
  streetAddress: '',
  beds_count: '',
  baths: '',
  total_area_sq_ft: '',
  year_built: '',
};

// machine learning metros available
const ML_METROS = [
  'stellar',
  'nashville',
  'atlanta',
  'phoenix',
  // 'jacksonville',
];

const getEditIconColor = (subjectPropertyParcelData) => {
  if (
    !subjectPropertyParcelData.beds_count ||
    !subjectPropertyParcelData.baths ||
    !subjectPropertyParcelData.total_area_sq_ft
  ) {
    return '#e200ff';
  }
  return '#1890ff';
};

const EditAddress = connect(({ CMA }) => ({
  geocodingData: CMA.geocodingData,
  compingMode: CMA.compingMode,
  eventCoordinates: CMA.eventCoordinates,
  currentRadiusMile: CMA.currentRadiusMile,
  currentPropertyAddress: CMA.currentPropertyAddress,
  subjectPropertyParcelData: CMA.subjectPropertyParcelData,
  currentStatusMLS: CMA.currentStatusMLS,
  expDateFilterOn: CMA.expDateFilterOn,
  userGroup: CMA.userGroup,
  selectedUserGroup: CMA.selectedUserGroup,

  searchingMode: CMA.searchingMode,
}))(function (props) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [formData, setFormData] = useState(DEFAULT_FORM_DATA);
  const [messageApi, contextHolder] = message.useMessage();
  const [avmEnabled, setAvmEnabled] = useState(
    // props.type === 'premium' ? true : false,
    userGroupAccess[props.selectedUserGroup]?.premium.includes('New House AVM')
      ? true
      : false,
  );

  const [bedInputError, setBedInputError] = useState(false);
  const [bathInputError, setBathInputError] = useState(false);
  const [sqftInputError, setSqftInputError] = useState(false);
  const [yearBuiltInputError, setYearBuiltInputError] = useState(false);

  useEffect(() => {
    setFormData(DEFAULT_FORM_DATA);
  }, [props.eventCoordinates]);

  console.log('HERE: ', props.geocodingData);
  console.log('HERE: ', props.subjectPropertyParcelData);
  const showModal = () => {
    // if (
    //   props.geocodingData &&
    //   props.geocodingData.features &&
    //   props.geocodingData.features.length > 0
    // ) {
    // const preFormData = structuredClone(DEFAULT_FORM_DATA);
    const preFormData = structuredClone(formData);
    // const features = props.geocodingData.features;
    // for (let i = 0; i < features.length; i++) {
    if (
      props.currentPropertyAddress.streetAddress &&
      props.currentPropertyAddress.streetAddress.length > 0
    ) {
      preFormData.streetAddress = props.currentPropertyAddress.streetAddress;
    }
    // }
    if (!isEmpty(props.subjectPropertyParcelData)) {
      for (let key in props.subjectPropertyParcelData) {
        if (!preFormData.hasOwnProperty(key)) continue;
        preFormData[key] = props.subjectPropertyParcelData[key];
      }
    }

    console.log('HERE: ', preFormData);
    setFormData(preFormData);
    // }
    setIsModalOpen(true);
  };

  const handleOk = async () => {
    let formError = false;

    const metro = getMetroNameForParamViaZIPCode(
      props.currentPropertyAddress.postalCode,
      false,
    );

    if (formData.beds_count === null || formData.beds_count === '') {
      setBedInputError(true);
      formError = true;
    }
    if (formData.baths === null || formData.baths === '') {
      setBathInputError(true);
      formError = true;
    }
    if (
      formData.total_area_sq_ft === null ||
      formData.total_area_sq_ft === ''
    ) {
      setSqftInputError(true);
      formError = true;
    }
    if (
      ML_METROS.includes(metro) &&
      (formData.year_built === null || formData.year_built === '')
    ) {
      console.log('metro: ', metro);
      setYearBuiltInputError(true);
      formError = true;
    }

    if (formError && avmEnabled) {
      messageApi.error(
        'Please fill out all required fields if New House AVM is checked.',
      );
    } else {
      let parcelData = props.subjectPropertyParcelData;
      if (avmEnabled && ML_METROS.includes(metro)) {
        let newHouseRentAVM;
        let newHouseSalesAVM;

        if (ML_METROS.includes(metro)) {
          // lat/long is reversed for this api
          newHouseRentAVM = await getNewHouseAVMRentMLData({
            metro: metro,
            lng: props.eventCoordinates[0],
            lat: props.eventCoordinates[1],
            beds: formData.beds_count,
            baths: formData.baths,
            sqft: formData.total_area_sq_ft,
            yearbuilt: formData.year_built,
          });
          newHouseSalesAVM = await getNewHouseAVMSalesMLData({
            metro: metro,
            lng: props.eventCoordinates[0],
            lat: props.eventCoordinates[1],
            beds: formData.beds_count,
            baths: formData.baths,
            sqft: formData.total_area_sq_ft,
            yearbuilt: formData.year_built,
          });
          console.log(`newHouseRentAVM ${metro}:`, newHouseRentAVM);
          console.log(`newHouseSalesAVM ${metro}:`, newHouseSalesAVM);
        } else {
          // newHouseRentAVM = await getNewHouseAVMRentData({
          //   lng: props.eventCoordinates[0],
          //   lat: props.eventCoordinates[1],
          //   radius: props.currentRadiusMile * 1609.34,
          //   beds: formData.beds_count,
          //   baths: formData.baths,
          //   sqft: formData.total_area_sq_ft,
          // });
          // console.log('newHouseRentAVM: ', newHouseRentAVM);
        }

        parcelData.rent =
          newHouseRentAVM && newHouseRentAVM > 0
            ? newHouseRentAVM
            : props.subjectPropertyParcelData.rent;

        parcelData.sales =
          newHouseSalesAVM && newHouseSalesAVM > 0
            ? newHouseSalesAVM
            : props.subjectPropertyParcelData.sales;
      }

      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          currentPropertyAddress: {
            ...props.currentPropertyAddress,
            streetAddress: formData.streetAddress,
          },
          subjectPropertyParcelData: {
            ...parcelData,
            beds_count: formData.beds_count,
            baths: formData.baths,
            total_area_sq_ft: formData.total_area_sq_ft,
            year_built: formData.year_built,
          },
        },
      });

      if (props.compingMode === 'intelligentComping') {
        props.dispatch({
          type: 'CMA/getAllPropertyData',
          payload: {
            mode: 'edit property details',
            lng: props.eventCoordinates[0],
            lat: props.eventCoordinates[1],
            status: props.currentStatusMLS,
            propertyType:
              props.searchingMode === 'Lease'
                ? 'Residential Lease'
                : 'Residential',
            exists: props.currentStatusMLS,
            expDateFilterOn: props.expDateFilterOn ? 'yes' : 'no',
          },
        });
      } else if (props.compingMode === 'smartFilter') {
        props.dispatch({
          type: 'CMA/filterMLSAndSFRData',
          payload: {
            dataSourceType: 'MLS',
          },
        });
        props.dispatch({
          type: 'CMA/filterMLSAndSFRData',
          payload: {
            dataSourceType: 'SFR',
          },
        });
        props.dispatch({
          type: 'CMA/filterMLSAndSFRData',
          payload: {
            dataSourceType: 'HotPads',
          },
        });
      } else {
        props.dispatch({
          type: 'CMA/filterMLSAndSFRDataManual',
          payload: {
            dataSourceType: 'MLS',
          },
        });
        props.dispatch({
          type: 'CMA/filterMLSAndSFRDataManual',
          payload: {
            dataSourceType: 'SFR',
          },
        });
        props.dispatch({
          type: 'CMA/filterMLSAndSFRDataManual',
          payload: {
            dataSourceType: 'HotPads',
          },
        });
      }

      setIsModalOpen(false);
    }
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const onChangeInput = (e) => {
    setFormData((prevState) => ({
      ...prevState,
      [e.target.name]: e.target.value,
    }));
  };

  const onChangeBeds = (bedCount) => {
    setFormData((prevState) => ({
      ...prevState,
      beds_count: bedCount,
    }));
    if (bedCount != null || bedCount != '') {
      if (bedInputError) setBedInputError(false);
    }
  };

  const onChangeBaths = (bathCount) => {
    setFormData((prevState) => ({
      ...prevState,
      baths: bathCount,
    }));
    if (bathCount != null || bathCount != '') {
      if (bathInputError) setBathInputError(false);
    }
  };
  const onChangeSqft = (newSqft) => {
    setFormData((prevState) => ({
      ...prevState,
      total_area_sq_ft: newSqft,
    }));
    if (newSqft != null || newSqft != '') {
      if (sqftInputError) setSqftInputError(false);
    }
  };
  const onChangeYearBuilt = (yearBuilt) => {
    setFormData((prevState) => ({
      ...prevState,
      year_built: yearBuilt,
    }));

    const metro = getMetroNameForParamViaZIPCode(
      props.currentPropertyAddress.postalCode,
      false,
    );

    if (metro === 'atlanta' && (yearBuilt != null || yearBuilt != '')) {
      if (yearBuiltInputError) setYearBuiltInputError(false);
    }
  };

  const getAutoAddress = () => {
    let autoAddress = '';
    if (
      props.geocodingData &&
      props.geocodingData.features &&
      props.geocodingData.features.length > 0
    ) {
      const features = props.geocodingData.features;

      for (let i = 0; i < features.length; i++) {
        if (
          features[i].id.indexOf('poi') != -1 ||
          features[i].id.indexOf('address') != -1
        ) {
          if (
            features[i].address &&
            features[i].address.length > 0 &&
            features[i].text &&
            features[i].text.length > 0
          ) {
            autoAddress = features[i].address + ' ' + features[i].text;
            break;
          } else {
            autoAddress = features[i].text;
            break;
          }
        }
      }
    }

    if (autoAddress.length === 0) {
      messageApi.info('Unable to generate address for this location');
    }

    setFormData((prevState) => ({
      ...prevState,
      streetAddress: autoAddress,
    }));
  };

  const newHouseAVMBtnHandler = () => {
    const preFormData = structuredClone(formData);
    if (!isEmpty(props.currentPropertyAddress)) {
      for (let key in props.currentPropertyAddress) {
        if (!preFormData.hasOwnProperty(key)) continue;
        preFormData[key] = props.currentPropertyAddress[key];
      }
    }

    if (!isEmpty(props.subjectPropertyParcelData)) {
      for (let key in props.subjectPropertyParcelData) {
        if (!preFormData.hasOwnProperty(key)) continue;
        preFormData[key] = props.subjectPropertyParcelData[key];
      }
    }

    setFormData(preFormData);
    setIsModalOpen(true);
  };

  return (
    <>
      {contextHolder}
      {props.type === 'default' && (
        <Tooltip title="Edit address">
          {' '}
          <button className={styles.button} onClick={showModal}>
            <FaPencilAlt
              size={16}
              color={getEditIconColor(props.subjectPropertyParcelData)}
            />
          </button>
        </Tooltip>
      )}
      {props.type === 'premium' && (
        <Tooltip
          title={
            !userGroupHasAccess(props.selectedUserGroup, 'New House AVM')
              ? 'Please contact us for a demo'
              : props.eventCoordinates.length === 0
              ? 'Please select a location'
              : 'Apply New House AVM'
          }
        >
          <button
            id="newHouseAVMButton" // style for disable button is in global.css
            // style={{
            //   background: 'transparent',
            //   border: 'none',
            //   cursor: 'pointer',
            //   padding: 0,
            // }}
            className={styles.buttonWrapperSummaryRowText}
            disabled={
              props.eventCoordinates.length === 0 ||
              !userGroupHasAccess(props.selectedUserGroup, 'New House AVM')
            }
            onClick={newHouseAVMBtnHandler}
          >
            <span className={styles.headerBarButton}>New House AVM</span>
          </button>
        </Tooltip>
      )}

      <Modal
        title="Edit Address"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        centered
      >
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '10px',
          }}
        >
          {
            // props.type === 'premium' &&
            !isEmpty(props.currentPropertyAddress) &&
              ML_METROS.includes(
                getMetroNameForParamViaZIPCode(
                  props.currentPropertyAddress.postalCode,
                  false,
                ),
              ) &&
              userGroupAccess[props.selectedUserGroup].premium.includes(
                'New House AVM',
              ) && (
                <div>
                  <Checkbox
                    checked={avmEnabled}
                    onChange={(e) => {
                      setAvmEnabled(e.target.checked);
                    }}
                  >
                    <b>Apply New House AVM</b>
                  </Checkbox>
                </div>
              )
          }
          <Input.Group compact>
            <label>Street address:</label>
            <Input
              value={formData.streetAddress}
              name="streetAddress"
              onChange={onChangeInput}
              placeholder="Street Address or Location Name"
              style={{ width: 'calc(100% - 46px)' }}
            />
            <Tooltip title="Auto-Generate Address">
              <Button onClick={getAutoAddress}>
                <MdAutorenew />
              </Button>
            </Tooltip>
          </Input.Group>
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              gap: '10px',
            }}
          >
            <div>
              <span>Beds:</span>
              <InputNumber
                value={formData.beds_count}
                onChange={onChangeBeds}
                placeholder="Beds"
                min={0}
                style={{ width: '100%' }}
                status={bedInputError ? 'error' : ''}
              />
            </div>
            <div>
              <span>Baths:</span>
              <InputNumber
                value={formData.baths}
                onChange={onChangeBaths}
                placeholder="Baths"
                min={0}
                style={{ width: '100%' }}
                status={bathInputError ? 'error' : ''}
              />
            </div>
            <div>
              <span>Sqft:</span>
              <InputNumber
                value={formData.total_area_sq_ft}
                formatter={(value) =>
                  `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                }
                onChange={onChangeSqft}
                placeholder="Sqft"
                min={0}
                step={100}
                style={{ width: '100%' }}
                status={sqftInputError ? 'error' : ''}
              />
            </div>
            <div>
              <span>Year Built:</span>
              <InputNumber
                value={formData.year_built}
                onChange={onChangeYearBuilt}
                placeholder="Year Built"
                min={0}
                max={new Date().getFullYear()}
                style={{ width: '100%' }}
                status={yearBuiltInputError ? 'error' : ''}
              />
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
});

export default EditAddress;
