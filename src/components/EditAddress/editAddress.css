.button {
  background: transparent;
  border: none;
  cursor: pointer;
}
/* .button svg { */
/* fill: var(--antd-active-blue); */
/* } */

.headerBarButton {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.5;
  color: var(--color-BT-blue);
}

.buttonWrapperSummaryRow {
  background-color: rgba(0, 0, 0, 0);
  border: none;
  cursor: pointer;
}

.buttonWrapperSummaryRowText {
  composes: buttonWrapperSummaryRow;
  padding: 0;
  white-space: nowrap;
}

.buttonWrapperSummaryRowText > button:disabled {
  color: rgba(0, 0, 0, 0.25);
  background-color: rgba(0, 0, 0, 0);
  border: none;
  padding: 0;
  white-space: nowrap;
}
