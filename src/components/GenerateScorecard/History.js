import { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'umi';
import { Button, Table, Radio, Tooltip } from 'antd';
import styles from './generateScorecard.css';
import moment from 'moment';
import json2csv from 'csvjson-json2csv/json2csv';
import { sortString } from '../../utils/strings';

function History(props) {
  const scorecardRecordList = useSelector(
    (state) => state.CMA.scorecardRecordList,
  );
  const dispatch = useDispatch();

  const [loading, setLoading] = useState(false);
  const [devManagerFilter, setDevManagerFilter] = useState([]);
  const [historyType, setHistoryType] = useState('user');

  useEffect(() => {
    setLoading(true);
    dispatch({
      type: 'CMA/getUserScorecardRecord',
      payload: { find: historyType },
    }).then(() => {
      setLoading(false);
    });

    return () => {
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          scorecardRecordList: [],
        },
      });
      setLoading(false);
    };
  }, [historyType]);

  const exportHistoryToCSV = () => {
    if (scorecardRecordList.length > 0) {
      let csvData = [];
      const format = 'YYYY-MM-DD HH:mm:ss';

      for (let i = 0; i < scorecardRecordList.length; i++) {
        const row = scorecardRecordList[i];

        if (
          devManagerFilter.length > 0 &&
          !devManagerFilter.includes(row.development_manager_name)
        ) {
          continue;
        }

        let projectName = '';
        if (row.csv_url) {
          const split = row.csv_url.split('/');
          if (split.length > 0) {
            projectName = split[split.length - 1];
          }
        }

        const creationTime = moment(row.creation_time).format(format);

        csvData.push({
          creation_time: creationTime,
          project_name: projectName,
          development_manager_name: row.development_manager_name,
          download_link: row.csv_url,
        });
      }

      var currentdate = new Date();
      const dateFormat = 'YYYY-MM-DD--HH-mm-ss';
      const csvDate = moment(currentdate).format(dateFormat);

      const csv = json2csv(csvData);
      const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
      var urlObject = window.URL || window.webkitURL || window;
      var save_link = document.createElementNS(
        'http://www.w3.org/1999/xhtml',
        'a',
      );
      save_link.href = urlObject.createObjectURL(blob);
      save_link.download = `Scorecard-History-${csvDate}.csv`;
      save_link.click();
    }
  };

  const historyTableOnChange = (pagination, filters, sorter, extra) => {
    if (filters.devManager && filters.devManager.length > 0) {
      setDevManagerFilter(filters.devManager);
    } else {
      setDevManagerFilter([]);
    }
  };

  const columns = [
    {
      title: 'Project Name',
      dataIndex: 'csv_url',
      key: 'name',
      width: 215,
      render: (text) => {
        if (!text) return;

        const split = text.split('/');
        if (split.length > 0) {
          return (
            <span style={{ wordWrap: 'break-word', wordBreak: 'break-word' }}>
              {split[split.length - 1]}
            </span>
          );
        } else {
          return text;
        }
      },
    },
    {
      title: 'Development Manager',
      dataIndex: 'development_manager_name',
      key: 'devManager',
      sorter: (a, b) => {
        if (a.development_manager_name && b.development_manager_name) {
          return sortString(
            a.development_manager_name,
            b.development_manager_name,
          );
        }
        return a.development_manager_name ? false : true;
      },
      filters: props.developmentManagerList.map((name) => {
        return { text: name, value: name };
      }),
      filterMode: 'menu',
      onFilter: (value, record) => {
        if (record.development_manager_name) {
          return record.development_manager_name.includes(value);
        }
        return false;
      },
    },
    {
      title: 'Creation Time',
      dataIndex: 'creation_time',
      key: 'creationTime',
      render: (text) => {
        const format = 'YYYY-MM-DD HH:mm:ss';
        return moment(text).format(format);
      },
      sorter: (a, b) => {
        return +moment(a.creation_time) - +moment(b.creation_time);
      },
    },
    {
      title: 'Download Link',
      dataIndex: 'csv_url',
      key: 'csvUrl',
      render: (text) => {
        if (!text) return;
        return <a href={text}>Link</a>;
      },
      align: 'center',
    },
  ];

  return (
    <div className={`${styles.history}`}>
      <div className={styles.historyHeader}>
        <Radio.Group
          className={styles.historyTypeButton}
          value={historyType}
          size="small"
          onChange={(e) => {
            setHistoryType(e.target.value);
          }}
        >
          <Tooltip title="Created by you">
            <Radio.Button value="user">User</Radio.Button>
          </Tooltip>
          <Tooltip title="Created by all users">
            <Radio.Button value="all">All</Radio.Button>
          </Tooltip>
        </Radio.Group>
        <h2>History</h2>
        {scorecardRecordList.length > 0 && (
          <div className={styles.csvBtnContainer}>
            <Button onClick={exportHistoryToCSV}>Export to CSV</Button>
          </div>
        )}
      </div>
      <div className={styles.historyBody}>
        <Table
          rowKey={(record) => record.id}
          columns={columns}
          dataSource={scorecardRecordList}
          pagination={false}
          onChange={historyTableOnChange}
          loading={loading}
        />
      </div>
    </div>
  );
}

export default History;
