import { UploadOutlined } from '@ant-design/icons';
import polyline from '@mapbox/polyline';
import * as Sentry from '@sentry/react';
import { default as turf_bbox } from '@turf/bbox';
import {
  Button,
  DatePicker,
  Input,
  InputNumber,
  Segmented,
  Select,
  Upload,
  message,
} from 'antd';
import { toPng } from 'html-to-image';
import moment from 'moment';
import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import { MAPBOX_TOKEN } from '../../constants';
import {
  getScorecardData,
  getScorecardDataFetch,
  getScorecardProgressData,
  submitBatchScorecardData,
} from '../../services/data';
import { legalDescriptionTrimmer } from '../ResultTable/Summary/Summary';
import styles from './generateScorecard.css';

let interval;

function Scorecard(props) {
  const map = useSelector((state) => state.CMA.map);
  const eventCoordinates = useSelector((state) => state.CMA.eventCoordinates);
  const subjectPropertyParcelData = useSelector(
    (state) => state.CMA.subjectPropertyParcelData,
  );
  const GeoJSONFromKML = useSelector((state) => state.CMA.GeoJSONFromKML);
  const getScorecardLoading = useSelector(
    (state) => state.CMA.getScorecardLoading,
  );
  const scorecardDownloadLink = useSelector(
    (state) => state.CMA.scorecardDownloadLink,
  );

  const userGroup = useSelector((state) => state.CMA.userGroup);
  const selectedUserGroup = useSelector((state) => state.CMA.selectedUserGroup);
  const currentUserEmail = useSelector((state) => state.CMA.currentUserEmail);

  const [scorecardType, setScorecardType] = useState(
    // ['demo-users'].includes(selectedUserGroup) ? 'For Sale' : 'Full Scorecard',
    'Full Scorecard',
  );

  const scorecardAreaType = useSelector((state) => state.CMA.scorecardAreaType);
  const scorecardImageData = useSelector(
    (state) => state.CMA.scorecardImageData,
  );

  const [targetRent, setTargetRent] = useState(null);

  const [scorecardGenerationType, setScorecardGenerationType] =
    useState('single_scorecard');
  const [scorecardBatchLocationInput, setScorecardBatchLocationInput] =
    useState('batch_input_address');

  const [batchInputFile, setBatchInputFile] = useState(null);
  const [batchOutputEmail, setBatchOutputEmail] = useState([currentUserEmail]);
  const [batchIsSubmitting, setBatchIsSubmitting] = useState(false);

  const dispatch = useDispatch();

  const {
    projectName,
    setProjectName,
    dateIdentified,
    setDateIdentified,
    devManager,
    setDevManager,
    consultant,
    setConsultant,
    projectStatus,
    setProjectStatus,
    latitude,
    setLatitude,
    longitude,
    setLongitude,
    legalDescription,
    setLegalDescription,
    projectNameError,
    setProjectNameError,
    dateIdentifiedError,
    setDateIdentifiedError,
    devManagerError,
    setDevManagerError,
    consultantError,
    setConsultantError,
    projectStatusError,
    setProjectStatusError,
    coordinatesError,
    setCoordinatesError,
  } = props;

  useEffect(() => {
    return () => {
      clearInterval(interval);
    };
  }, []);

  const onChangeProjectName = (e) => {
    if (e.target.value.length != 0 && projectNameError) {
      setProjectNameError(false);
    }
    setProjectName(e.target.value);
  };
  const onChangeDateIdentified = (date, dateString) => {
    if (dateIdentifiedError) {
      setDateIdentifiedError(false);
    }
    // setDateIdentified(dateString);
    setDateIdentified(date);
  };
  const onChangeDevManager = (managerName) => {
    if (devManagerError) {
      setDevManagerError(false);
    }
    setDevManager(managerName);
  };
  const onChangeConsultant = (consultant) => {
    if (consultantError) {
      setConsultantError(false);
    }
    setConsultant(consultant);
  };
  const onChangeProjectStatus = (status) => {
    if (projectStatusError) {
      setProjectStatusError(false);
    }
    setProjectStatus(status);
  };
  const onChangeDescription = (e) => {
    // if (legalDescriptionError) {
    //   setLegalDescriptionError(false);
    // }
    setLegalDescription(e.target.value);
  };

  useEffect(() => {
    if (eventCoordinates.length === 2) {
      if (coordinatesError) {
        setCoordinatesError(false);
      }
      setLongitude(eventCoordinates[0]);
      setLatitude(eventCoordinates[1]);

      // Reset scorecard download link and get isochrone
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: { scorecardDownloadLink: null },
      });
    }
  }, [eventCoordinates]);

  useEffect(() => {
    if (
      subjectPropertyParcelData.legal_description &&
      subjectPropertyParcelData.legal_description.length > 0
    ) {
      setLegalDescription(
        legalDescriptionTrimmer(subjectPropertyParcelData.legal_description),
      );
    } else {
      setLegalDescription('');
    }
  }, [subjectPropertyParcelData.legal_description]);

  const onGenerateClick = async () => {
    let formValidated = true;
    if (projectName === '' && projectName.length === 0) {
      setProjectNameError(true);
      formValidated = false;
    }
    if (dateIdentified === '' && dateIdentified.length === 0) {
      setDateIdentifiedError(true);
      formValidated = false;
    }
    if (
      ['Avanta'].includes(selectedUserGroup) &&
      (devManager === null || (devManager === '' && devManager.length === 0))
    ) {
      setDevManagerError(true);
      formValidated = false;
    }
    if (
      ['Avanta'].includes(selectedUserGroup) &&
      (consultant === null || (consultant === '' && consultant.length === 0))
    ) {
      setConsultantError(true);
      formValidated = false;
    }
    if (
      projectStatus === null ||
      (projectStatus === '' && projectStatus.length === 0)
    ) {
      setProjectStatusError(true);
      formValidated = false;
    }
    if (latitude === null || longitude === null) {
      setCoordinatesError(true);
      formValidated = false;
    }

    if (formValidated) {
      const location = await getCityAndState(longitude, latitude);

      let kmlPolygon = '';
      let locationImgDataURL;
      if (
        GeoJSONFromKML &&
        GeoJSONFromKML.features &&
        GeoJSONFromKML.features.length > 0
      ) {
        try {
          locationImgDataURL = await getLocationImageWithKML(
            structuredClone(GeoJSONFromKML),
          );
        } catch (err) {
          console.log('Failed getting image with kml');
          locationImgDataURL = await getLocationImage(longitude, latitude);
        }

        const coordinates = GeoJSONFromKML.features[0].geometry.coordinates[0];
        for (let i = 0; i < coordinates.length; i++) {
          // remove elevation parameter
          if (coordinates[i].length != 2) {
            coordinates[i] = coordinates[i].slice(0, 2);
          }
        }
        kmlPolygon = JSON.stringify([coordinates]);
      } else {
        locationImgDataURL = await getLocationImage(longitude, latitude);
      }
      // if (locationImgDataURL) {
      //   const img = new Image();
      //   img.src = locationImgDataURL;

      //   const w = window.open('');
      //   w.document.write(img.outerHTML);
      // }

      // const areaImage = await takeMapBoxScreenshot(map);
      // console.log(driveImage);
      // if (driveImage) {
      //   const img = new Image();
      //   img.src = driveImage;

      //   const w = window.open('');
      //   w.document.write(img.outerHTML);
      // }

      const formData = new FormData();
      formData.append('area_type', scorecardAreaType);
      formData.append('project_name', projectName);
      formData.append('location', `${location.city}, ${location.state}`);
      formData.append('report_date', getTodaysDate());
      formData.append(
        'date_identified',
        moment(dateIdentified).format('YYYY-MM-DD'),
      );
      formData.append(
        'development_manager_name',
        devManager || currentUserEmail,
      );
      formData.append('consultant_name', consultant || '');
      formData.append('target_rent', targetRent || 0);
      formData.append('project_status', projectStatus);
      formData.append('lat', latitude);
      formData.append('lng', longitude);
      formData.append('county', location.county);
      formData.append('legal_description', legalDescription || '');
      formData.append('kml_polygon', kmlPolygon);
      formData.append(
        'scorecard_type',
        scorecardType === 'For Sale' ? scorecardType : '',
      );
      formData.append(
        'image',
        DataURIToBlob(locationImgDataURL),
        'locationImage.jpg',
      );
      formData.append(
        'area_image',
        DataURIToBlob(scorecardImageData),
        'area_image.png',
      );

      // props.dispatch({
      //   type: 'CMA/getScorecard',
      //   payload: { body: formData },
      // });

      dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          getScorecardLoading: true,
        },
      });

      // let response = await fetch(getScorecardData, {
      //   body: formData,
      //   method: 'POST',
      // });

      let response = await getScorecardDataFetch({ body: formData });
      console.log('response: ', response);

      const status = response.status;
      if (status === 200) {
        response = await response.json();
      }

      console.log('response: ', response);
      if (response && status === 200) {
        interval = setInterval(async () => {
          const fileRequest = await getScorecardProgressData({
            id: response.id,
          });

          if (fileRequest) {
            if (fileRequest.status === 'Done') {
              dispatch({
                type: 'CMA/saveCMAStates',
                payload: {
                  scorecardDownloadLink: fileRequest.csv_url,
                  getScorecardLoading: false,
                },
              });

              clearInterval(interval);
            } else {
              // console.log('Waiting for next request');
              // setCurrentProgress([+fileRequest.current_row, +fileRequest.total_rows]);
            }
          } else {
            clearInterval(interval);
            dispatch({
              type: 'CMA/saveCMAStates',
              payload: {
                scorecardDownloadLink: '',
                getScorecardLoading: false,
              },
            });
          }
        }, 5000);
      } else if (status === 204) {
        console.log('No content');
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            scorecardDownloadLink: '204 No Content',
            getScorecardLoading: false,
          },
        });
      } else {
        dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            scorecardDownloadLink: '',
            getScorecardLoading: false,
          },
        });
        Sentry.captureException(new Error(JSON.stringify(response)));
      }
    }
  };

  const getScorecardTypeOptions = useCallback(() => {
    const options = [];

    if (['demo-users'].includes(selectedUserGroup)) {
      options.push({
        value: 'For Sale',
        label: 'For Sale',
      });
    }

    options.push({
      value: 'Full Scorecard',
      label: 'For Rent',
    });

    return options;
  }, [selectedUserGroup]);

  const onGenerateBatchClick = async () => {
    if (
      !batchInputFile ||
      projectName.length === 0 ||
      batchOutputEmail.length === 0
    )
      return;
    const formData = new FormData();
    formData.append('area_type', scorecardAreaType);
    formData.append('portfolio_name', projectName);
    formData.append('input_source_type', scorecardBatchLocationInput);
    formData.append('input_file', batchInputFile);
    formData.append('output_email', batchOutputEmail[0]);
    formData.append('target_rent', targetRent || 0);

    let response = await submitBatchScorecardData({ body: formData });

    setBatchInputFile(null);
    setProjectName('');
    setBatchIsSubmitting(false);
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        scorecardModalOpen: false,
      },
    });

    message.config({
      getContainer: () => document.getElementById('root'),
    });
    message.success(
      "We're processing your request. You will receive an email once the batch scorecard is ready.",
    );
  };

  return (
    <>
      <div className={styles.scorecardModalHeader}>
        <div>
          <h2>Scorecard</h2>
        </div>
      </div>
      <div className={styles.scorecardModalBody}>
        <div className={styles.scorecardForm}>
          <p>
            Complete the following details and select a location on the map to
            generate the scorecard
          </p>
          <div>
            <label htmlFor="scorecard_type">Scorecard Type</label>
            <Segmented
              id="scorecard_type"
              options={[
                { label: 'Single', value: 'single_scorecard' },
                { label: 'Batch', value: 'batch_scorecard' },
              ]}
              value={scorecardGenerationType}
              onChange={(value) =>
                setScorecardGenerationType(value || 'single_scorecard')
              }
              block
            />
          </div>
          <div>
            <label htmlFor="area_type">Area Type</label>
            <Segmented
              id="area_type"
              options={[
                { label: 'Drive Time', value: 'drive_time' },
                { label: 'Radius', value: 'buffer' },
              ]}
              value={scorecardAreaType}
              onChange={(value) =>
                dispatch({
                  type: 'CMA/saveCMAStates',
                  payload: { scorecardAreaType: value },
                })
              }
              block
            />
          </div>
          <div className={styles.scorecardInputGroup}>
            <label htmlFor="project_name">Project Name</label>
            <Input
              id="project_name"
              placeholder="Name of project"
              value={projectName}
              onChange={onChangeProjectName}
              status={projectNameError ? 'error' : ''}
            />
          </div>
          {/*  {['BlueRiver', 'demo-users'].includes(selectedUserGroup) ? ( */}
          {scorecardGenerationType === 'single_scorecard' && (
            <React.Fragment>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  gap: '12px',
                  width: '100%',
                }}
              >
                <div
                  style={{
                    width: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '5px',
                  }}
                >
                  <label htmlFor="date_identified">Date Identified</label>
                  <DatePicker
                    id="date_identified"
                    value={dateIdentified}
                    onChange={onChangeDateIdentified}
                    status={dateIdentifiedError ? 'error' : ''}
                  />
                </div>
                <div
                  style={{
                    width: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '5px',
                  }}
                >
                  <label htmlFor="scorecard_type">Type</label>
                  <Select
                    id="scorecard_type"
                    style={{ width: '100%' }}
                    placeholder="Select a type"
                    value={scorecardType}
                    onChange={(value) => setScorecardType(value)}
                    options={getScorecardTypeOptions()}
                  />
                </div>
              </div>
              {!['GreatGulf'].includes(selectedUserGroup) && (
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    gap: '10px',
                    width: '100%',
                  }}
                >
                  <div
                    className={styles.scorecardInputGroup}
                    style={{ width: '100%' }}
                  >
                    <label htmlFor="dev_managers">
                      {props.developmentManagerTitle}
                    </label>
                    <Select
                      id="dev_managers"
                      placeholder="Select a manager"
                      optionFilterProp="children"
                      value={devManager}
                      onChange={onChangeDevManager}
                      options={props.developmentManagerList.map((name) => ({
                        value: name,
                        label: name,
                      }))}
                      status={devManagerError ? 'error' : ''}
                    />
                  </div>
                  <div
                    className={styles.scorecardInputGroup}
                    style={{ width: '100%' }}
                  >
                    <label htmlFor="consultant">{props.consultantTitle}</label>
                    <Select
                      id="consultant"
                      placeholder={`Select ${
                        props.consultantTitle === 'Consultant' ? 'a' : 'an'
                      } ${props.consultantTitle.toLowerCase()}`}
                      optionFilterProp="children"
                      value={consultant}
                      onChange={onChangeConsultant}
                      options={props.consultantList.map((name) => ({
                        value: name,
                        label: name,
                      }))}
                      status={consultantError ? 'error' : ''}
                    />
                  </div>
                </div>
              )}
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  gap: '10px',
                  width: '100%',
                }}
              >
                <div
                  className={styles.scorecardInputGroup}
                  style={{ width: '100%' }}
                >
                  <label htmlFor="project_status">Project Status</label>
                  <Select
                    id="project_status"
                    placeholder="Select a status"
                    optionFilterProp="children"
                    value={projectStatus}
                    onChange={onChangeProjectStatus}
                    options={props.projectStatusList.map((status) => ({
                      value: status,
                      label: status,
                    }))}
                    status={projectStatusError ? 'error' : ''}
                  />
                </div>
                <div
                  className={styles.scorecardInputGroup}
                  style={{ width: '100%' }}
                >
                  <label htmlFor="target_rent">Target Rent</label>
                  <InputNumber
                    id="target_rent"
                    value={targetRent}
                    placeholder="Enter target rent"
                    onChange={(value) => setTargetRent(Number(value))}
                    formatter={(value) =>
                      `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                    }
                    parser={(value) => `${value}`.replace(/\$\s?|(,*)/g, '')}
                    onPressEnter={() => setTargetRent(Math.round(targetRent))}
                    onBlur={() => setTargetRent(Math.round(targetRent))}
                    step={100}
                    min={0}
                    style={{ width: '100%' }}
                  />
                </div>
              </div>

              <div className={styles.scorecardInputGroup}>
                <label htmlFor="coordinates">Coordinates (map location)</label>
                <Input
                  id="coordinates"
                  placeholder="Select a location on the map"
                  onMouseDown={(e) => e.stopPropagation()}
                  value={
                    latitude != null && longitude != null
                      ? `${Number(latitude).toFixed(5)}, ${Number(
                          longitude,
                        ).toFixed(5)}`
                      : ''
                  }
                  status={coordinatesError ? 'error' : ''}
                />
              </div>

              <div className={styles.scorecardInputGroup}>
                <label htmlFor="legal_description">Parcel Number</label>
                <Input
                  id="legal_description"
                  value={legalDescription}
                  onChange={onChangeDescription}
                  // status={legalDescriptionError ? 'error' : ''}
                />
                {latitude && longitude && (
                  <div style={{ textAlign: 'center' }}>
                    <a
                      href={`https://www.google.com/maps/@${latitude},${longitude},860m/data=!3m1!1e3`}
                      target="_blank"
                    >
                      Google Maps
                    </a>
                  </div>
                )}
              </div>
              <div
                className={styles.scorecardGenerateBtnContainer}
                style={{ marginTop: '25px' }}
              >
                <Button
                  type="primary"
                  onClick={onGenerateClick}
                  loading={getScorecardLoading || !scorecardImageData}
                >
                  Generate
                </Button>
              </div>
              <p style={{ textAlign: 'center' }}>
                {!getScorecardLoading &&
                  scorecardDownloadLink &&
                  scorecardDownloadLink.length != 0 &&
                  scorecardDownloadLink != '204 No Content' && (
                    <a href={`${scorecardDownloadLink}`} target="_blank">
                      Download Scorecard
                    </a>
                  )}
                {!getScorecardLoading &&
                  scorecardDownloadLink != null &&
                  scorecardDownloadLink.length === 0 &&
                  scorecardDownloadLink != '204 No Content' &&
                  'Something went wrong. Please try again.'}
                {!getScorecardLoading &&
                  scorecardDownloadLink != null &&
                  scorecardDownloadLink.length != 0 &&
                  scorecardDownloadLink === '204 No Content' &&
                  'Unable to generate scorecard for selected location.'}
              </p>
            </React.Fragment>
          )}

          {scorecardGenerationType === 'batch_scorecard' && (
            <React.Fragment>
              <div
                className={styles.scorecardInputGroup}
                style={{ width: '100%' }}
              >
                <label htmlFor="target_rent">Target Rent</label>
                <InputNumber
                  id="target_rent"
                  value={targetRent}
                  placeholder="Enter target rent"
                  onChange={(value) => setTargetRent(Number(value))}
                  formatter={(value) =>
                    `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  }
                  parser={(value) => `${value}`.replace(/\$\s?|(,*)/g, '')}
                  onPressEnter={() => setTargetRent(Math.round(targetRent))}
                  onBlur={() => setTargetRent(Math.round(targetRent))}
                  step={100}
                  min={0}
                  style={{ width: '100%' }}
                />
              </div>
              <div>
                <label htmlFor="area_type">Location Input</label>
                <Segmented
                  id="batch_location_input"
                  options={[
                    { label: 'Address', value: 'batch_input_address' },
                    { label: 'Coordinates', value: 'batch_input_coordinates' },
                  ]}
                  value={scorecardBatchLocationInput}
                  onChange={(value) =>
                    setScorecardBatchLocationInput(
                      value || 'batch_input_address',
                    )
                  }
                  block
                />
              </div>
              {scorecardBatchLocationInput === 'batch_input_address' && (
                <div>
                  <span>Download and fill in the following excel sheet.</span>
                  <Button
                    type="link"
                    href="https://spat-scorecard.s3.us-east-1.amazonaws.com/scorecard_templates/sbti_address.xlsx"
                    target="_blank"
                  >
                    sbti_address.xlsx
                  </Button>
                </div>
              )}
              {scorecardBatchLocationInput === 'batch_input_coordinates' && (
                <div className="flex flex-col gap-1">
                  <p>Download and fill in the following excel sheet.</p>
                  <Button
                    type="link"
                    href="https://spat-scorecard.s3.us-east-1.amazonaws.com/scorecard_templates/sbti_coordinates.xlsx"
                    target="_blank"
                  >
                    sbti_coordinates.xlsx
                  </Button>
                </div>
              )}
              <div className="flex flex-col gap-1">
                <p>Upload when done.</p>
                <Upload
                  name="input_file"
                  onChange={(info) => {
                    if (info.file.status === 'done') {
                      setBatchInputFile(info.file.originFileObj);
                    }
                  }}
                  maxCount={1}
                >
                  <Button icon={<UploadOutlined />}>Click to Upload</Button>
                </Upload>
              </div>
              <div
                className={styles.scorecardInputGroup}
                style={{ width: '100%' }}
              >
                <label htmlFor="output_email">
                  Send result to the following email:
                </label>
                <Select
                  mode="tags"
                  id="output_email"
                  optionFilterProp="children"
                  value={batchOutputEmail}
                  maxCount={1}
                  onChange={setBatchOutputEmail}
                  options={[
                    { value: currentUserEmail, label: currentUserEmail },
                  ]}
                />
              </div>
              <div
                className={styles.scorecardGenerateBtnContainer}
                style={{ marginTop: '25px', marginBottom: '25px' }}
              >
                <Button
                  type="primary"
                  onClick={() => {
                    setBatchIsSubmitting(true);
                    onGenerateBatchClick();
                  }}
                  disabled={batchInputFile === null || projectName.length === 0}
                  loading={batchIsSubmitting}
                >
                  Submit
                </Button>
              </div>
            </React.Fragment>
          )}
        </div>
      </div>
    </>
  );
}

export default Scorecard;

const getScreenshotDimensions = (size, imgWidth, imgHeight) => {
  let newHeight, newWidth, xStart, yStart;
  if (imgHeight > imgWidth) {
    newHeight = imgHeight * (size / imgWidth);
    newWidth = size;
    xStart = 0;
    yStart = -(newHeight / 2 - size / 2);
  } else if (imgWidth > imgHeight) {
    newWidth = imgWidth * (size / imgHeight);
    newHeight = size;
    yStart = 0;
    xStart = -(newWidth / 2 - size / 2);
  } else {
    newWidth = 1000;
    newHeight = 1000;
    xStart = 0;
    yStart = 0;
  }
  return { xStart, yStart, width: newWidth, height: newHeight };
};

const takeMapBoxScreenshot = (map) => {
  // https://github.com/mapbox/mapbox-gl-js/issues/2766
  return new Promise(function (resolve, reject) {
    map.once('render', async function () {
      // const imgSrc = map.getCanvas().toDataURL();

      const zoomBtns = document.querySelector(`.mapboxgl-ctrl-top-right`);
      zoomBtns.style.visibility = 'hidden';

      const imgSrc = await toPng(document.querySelector(`.mapboxgl-map`));

      zoomBtns.style.visibility = 'visible';

      // crop image
      const img = new Image();
      img.src = imgSrc;
      img.onload = () => {
        // const size = img.width; // height and width of crop image
        const size = 1000; // height and width of crop image

        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = size;
        canvas.height = size;
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
        // ctx.globalCompositeOperation = 'darken';
        ctx.globalCompositeOperation = 'multiply';

        const dim = getScreenshotDimensions(size, img.width, img.height);
        ctx.drawImage(img, dim.xStart, dim.yStart, dim.width, dim.height);

        resolve(canvas.toDataURL());
      };

      // resolve(map.getCanvas().toDataURL());
    });
    /* trigger render */
    map.setBearing(map.getBearing());
  });
};

const setMapBBox = (map, geojson) => {
  return new Promise(function (resolve, reject) {
    setTimeout(() => {
      map.fitBounds(turf_bbox(geojson), {
        padding: 32,
      });
    }, 0);
    map.on('moveend', () => {
      resolve();
    });
  });
};

function getLocationImage(longitude, latitude) {
  // const SCREENSHOT_ZOOM = 18.5;
  const SCREENSHOT_ZOOM = 15;
  const imgWidth = 400;
  const imgHeight = 375;
  const topImgURL = `https://api.mapbox.com/styles/v1/mapbox/satellite-v9/static/${longitude},${latitude},${SCREENSHOT_ZOOM},0,0/${imgWidth}x${imgHeight}?access_token=${MAPBOX_TOKEN}`;

  return new Promise(function (resolve, reject) {
    fetch(topImgURL)
      .then((response) => response.blob())
      .then((imageBlob) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result);
        reader.readAsDataURL(imageBlob);
      });
  });
}

function getLocationImageWithKML(GeoJSONFromKML) {
  const coordinates = GeoJSONFromKML.features[0].geometry.coordinates[0];

  for (let i = 0; i < coordinates.length; i++) {
    // remove elevation parameter
    if (coordinates[i].length != 2) {
      coordinates[i] = coordinates[i].slice(0, 2);
    }
    // polyline.encode uses lat, lng
    const longitude = coordinates[i][0];
    const latitude = coordinates[i][1];
    coordinates[i][0] = latitude;
    coordinates[i][1] = longitude;
  }

  const imgWidth = 400;
  const imgHeight = 375;
  const pathPolyline = polyline.encode(coordinates);
  const strokeWidth = 5;
  const strokeColor = 'fd0927';
  const url = `https://api.mapbox.com/styles/v1/mapbox/satellite-streets-v11/static/path-${strokeWidth}+${strokeColor}(${pathPolyline})/auto/${imgWidth}x${imgHeight}?before_layer=waterway-label&access_token=${MAPBOX_TOKEN}`;

  return new Promise(function (resolve, reject) {
    fetch(url)
      .then((response) => {
        if (response.ok) {
          return response.blob();
        }
        reject(response);
      })
      .then((imageBlob) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result);
        reader.readAsDataURL(imageBlob);
      })
      .catch((err) => {
        console.log('ERR: ', err);
      });
  });
}

function getCityAndState(longitude, latitude) {
  return new Promise(function (resolve, reject) {
    fetch(
      `https://api.mapbox.com/geocoding/v5/mapbox.places/${longitude},${latitude}.json?access_token=${MAPBOX_TOKEN}`,
    )
      .then((response) => response.json())
      .then((location) => {
        if (location && location.features && location.features.length > 0) {
          let city = '';
          let state = '';
          let county = '';
          for (let i = 0; i < location.features.length; i++) {
            const feature = location.features[i];
            if (feature.id.includes('place')) {
              city = feature.text;
            } else if (feature.id.includes('region')) {
              state = feature.properties.short_code.split('-')[1];
            } else if (feature.id.includes('district')) {
              if (feature.text.toLowerCase().includes('county')) {
                const endIndex = feature.text.toLowerCase().indexOf('county');
                county = feature.text.substring(0, endIndex).trim();
              } else {
                county = feature.text;
              }
            }
          }
          resolve({ city, state, county });
        }
      });
  });
}

const getTodaysDate = () => {
  var today = new Date();
  var dd = String(today.getDate()).padStart(2, '0');
  var mm = String(today.getMonth() + 1).padStart(2, '0'); //January is 0!
  var yyyy = today.getFullYear();

  return `${yyyy}-${mm}-${dd}`;
};

function DataURIToBlob(dataURI) {
  // https://stackoverflow.com/questions/26667820/upload-a-base64-encoded-image-using-formdata
  const splitDataURI = dataURI.split(',');
  const byteString =
    splitDataURI[0].indexOf('base64') >= 0
      ? atob(splitDataURI[1])
      : decodeURI(splitDataURI[1]);
  const mimeString = splitDataURI[0].split(':')[1].split(';')[0];

  const ia = new Uint8Array(byteString.length);
  for (let i = 0; i < byteString.length; i++) ia[i] = byteString.charCodeAt(i);

  return new Blob([ia], { type: mimeString });
}
