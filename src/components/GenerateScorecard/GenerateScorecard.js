import { Dropdown } from 'antd';
import { useEffect, useRef, useState } from 'react';
import Draggable from 'react-draggable';
import { IoClose } from 'react-icons/io5';
import { SlOptionsVertical } from 'react-icons/sl';
import { connect, useSelector } from 'umi';
import Analytics from './Analytics';
import styles from './generateScorecard.css';
import History from './History';
import Scorecard from './Scorecard';
import ScorecardBatchResult from './ScorecardBatchResult';

const data = {
  Avanta: {
    developmentManagerList: {
      title: 'Development Manager',
      list: [
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        'None',
      ],
    },
    consultantList: {
      title: 'Consultant',
      list: [
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON><PERSON>',
        'None',
        'Other',
      ],
    },
    projectStatusList: {
      list: [
        'Active Deal',
        'Active Prospect',
        'Comp',
        'Dead Deal',
        'Dead Prospect',
        'Hold Deal',
        'Hold Prospect',
        'Lease-Up',
        'None',
        'Stabilized',
        'Test',
        'Under Construction',
      ],
    },
  },
  BridgeTower: {
    developmentManagerList: {
      title: 'Development Manager',
      list: [
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        'Kody <PERSON>',
        '<PERSON> Priddy',
      ],
    },
    consultant<PERSON>ist: {
      title: 'Underwriter',
      list: [
        '<PERSON> <PERSON>',
        '<PERSON> <PERSON>',
        'Kody <PERSON>',
        '<PERSON> P<PERSON>dy',
        '<PERSON> <PERSON>',
      ],
    },
    projectStatusList: {
      list: [
        'Active <PERSON>',
        'Active Prospect',
        'Dead Prospect',
        'Dead Deal',
        'Hold Deal',
        'Hold Prospect',
        'Lease-Up',
        'Stabilized',
        'Under Construction',
      ],
    },
  },
  Nhimble: {
    developmentManagerList: {
      title: 'Development Manager',
      list: ['Sanket Kumar'],
    },
    consultantList: {
      title: 'Underwriter',
      list: ['Sanket Kumar'],
    },
    projectStatusList: {
      list: [
        'Active Deal',
        'Active Prospect',
        'Dead Prospect',
        'Dead Deal',
        'Hold Deal',
        'Hold Prospect',
        'Lease-Up',
        'Stabilized',
        'Under Construction',
      ],
    },
  },
  test: {
    developmentManagerList: {
      title: 'Development Manager',
      list: ['Test Manager 1', 'Test Manager 2', 'Test Manager 3'],
    },
    consultantList: {
      title: 'Consultant',
      list: ['Test Consultant 1', 'Test Consultant 2', 'Test Consultant 3'],
    },
    projectStatusList: {
      list: [
        'Active Deal',
        'Active Prospect',
        'Comp',
        'Dead Deal',
        'Dead Prospect',
        'Hold Deal',
        'Hold Prospect',
        'Lease-Up',
        'None',
        'Stabilized',
        'Test',
        'Under Construction',
      ],
    },
  },
};

const GenerateScorecard = connect(({ CMA }) => ({
  scorecardModalOpen: CMA.scorecardModalOpen,
  eventCoordinates: CMA.eventCoordinates,
  subjectPropertyParcelData: CMA.subjectPropertyParcelData,
  scorecardDownloadLink: CMA.scorecardDownloadLink,
  getScorecardLoading: CMA.getScorecardLoading,
  scorecardRecordList: CMA.scorecardRecordList,
  GeoJSONFromKML: CMA.GeoJSONFromKML,
  userGroup: CMA.userGroup,
  selectedUserGroup: CMA.selectedUserGroup,
}))(function (props) {
  const map = useSelector((state) => state.CMA.map);

  const scorecardContainer = useRef(null);
  const [draggablePosition, setDraggablePosition] = useState({
    x: -311,
    y: -380,
  });
  const [projectName, setProjectName] = useState('');
  const [dateIdentified, setDateIdentified] = useState('');
  const [devManager, setDevManager] = useState(null);
  const [consultant, setConsultant] = useState(null);
  const [projectStatus, setProjectStatus] = useState('Active Prospect');
  const [latitude, setLatitude] = useState(null);
  const [longitude, setLongitude] = useState(null);
  const [legalDescription, setLegalDescription] = useState('');

  const [projectNameError, setProjectNameError] = useState(false);
  const [dateIdentifiedError, setDateIdentifiedError] = useState(false);
  const [devManagerError, setDevManagerError] = useState(false);
  const [consultantError, setConsultantError] = useState(false);
  const [projectStatusError, setProjectStatusError] = useState(false);
  const [coordinatesError, setCoordinatesError] = useState(false);

  const [viewedPage, setViewedPage] = useState('scorecard');

  const geojsonTemplate = {
    type: 'FeatureCollection',
    features: [],
  };

  const closeScorecardModal = () => {
    props.dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        scorecardModalOpen: false,
        scorecardDownloadLink: null,
        getScorecardLoading: false,
        scorecardAreaType: 'drive_time',
      },
    });
  };

  const [draggableBounds, setDraggableBounds] = useState({});

  useEffect(() => {
    if (!scorecardContainer.current) return;

    const setBounds = () => {
      const viewportOffset = scorecardContainer.current.getBoundingClientRect();
      const windowWidth = window.innerWidth;
      const windowHeight = window.innerHeight;

      const scorecardWidth = scorecardContainer.current.clientWidth;
      const scorecardHeight = scorecardContainer.current.clientHeight;
      const startX = draggablePosition.x;
      const startY = draggablePosition.y;

      const left = -viewportOffset.x + startX;
      const right = startX + (windowWidth - scorecardWidth) - viewportOffset.x;
      const top = -viewportOffset.y + startY;
      const bottom =
        startY + (windowHeight - scorecardHeight) - viewportOffset.y;

      setDraggableBounds({
        left: left,
        right: right,
        top: top,
        bottom: bottom,
      });
    };

    setBounds();

    window.addEventListener('resize', setBounds);
    return () => {
      window.removeEventListener('resize', setBounds);
    };
  }, [scorecardContainer, draggablePosition]);

  const getScorecardOptionsMenu = () => {
    const items = [
      {
        label: 'Scorecard',
        key: 'scorecard',
        disabled: viewedPage === 'scorecard' ? true : false,
      },
      {
        label: 'Analytics',
        key: 'analytics',
        disabled: viewedPage === 'analytics' ? true : false,
      },
      {
        label: 'History',
        key: 'history',
        disabled: viewedPage === 'history' ? true : false,
      },
      {
        label: 'Scorecard Batch Result',
        key: 'scorecard-batch-result',
        disabled: viewedPage === 'scorecard-batch-result' ? true : false,
      },
    ];

    return items;
  };

  const scorecardOptionsHandler = (e) => {
    setViewedPage(e.key);
  };

  const client = props.selectedUserGroup;
  let developmentManagerTitle;
  let developmentManagerList;
  let consultantTitle;
  let consultantList;
  let projectStatusList;
  if (client && client.length > 0) {
    if (data[client]) {
      developmentManagerTitle = data[client].developmentManagerList.title;
      developmentManagerList = data[client].developmentManagerList.list;
      consultantTitle = data[client].consultantList.title;
      consultantList = data[client].consultantList.list;
      projectStatusList = data[client].projectStatusList.list;
    }
  }

  if (
    client === 'dev' ||
    client === 'demo-users' ||
    client === 'demo-CMA-DFW-only' ||
    !developmentManagerTitle ||
    !developmentManagerList ||
    !consultantTitle ||
    !consultantList ||
    !projectStatusList
  ) {
    developmentManagerTitle = data['test'].developmentManagerList.title;
    developmentManagerList = data['test'].developmentManagerList.list;
    consultantTitle = data['test'].consultantList.title;
    consultantList = data['test'].consultantList.list;
    projectStatusList = data['test'].projectStatusList.list;
  }

  return (
    <div className={styles.scorecardModalWrap}>
      <Draggable
        position={draggablePosition}
        onDrag={(e, data) => {
          setDraggablePosition({ x: data.x, y: data.y });
        }}
        bounds={draggableBounds}
      >
        <div
          ref={scorecardContainer}
          className={styles.scorecardModalContainer}
        >
          <div className={styles.scorecardModalHeaderBtnContainer}>
            <Dropdown
              menu={{
                items: getScorecardOptionsMenu(),
                onClick: scorecardOptionsHandler,
              }}
              trigger={['click']}
            >
              <button className={styles.viewOptionsBtn}>
                <SlOptionsVertical />
              </button>
            </Dropdown>
            <button className={styles.scorecardModalCloseBtn}>
              <IoClose onClick={closeScorecardModal} />
            </button>
          </div>

          <div className={styles.scorecardContainer}>
            <div>
              {viewedPage === 'history' && (
                <History developmentManagerList={developmentManagerList} />
              )}
              {viewedPage === 'analytics' && (
                <Analytics
                  developmentManagerList={['All', ...developmentManagerList]}
                  consultantList={['All', ...consultantList]}
                />
              )}
              {viewedPage === 'scorecard-batch-result' && (
                <ScorecardBatchResult />
              )}

              {viewedPage === 'scorecard' && (
                <Scorecard
                  developmentManagerTitle={developmentManagerTitle}
                  developmentManagerList={developmentManagerList}
                  consultantTitle={consultantTitle}
                  consultantList={consultantList}
                  projectStatusList={projectStatusList}
                  {...{
                    projectName,
                    setProjectName,
                    dateIdentified,
                    setDateIdentified,
                    devManager,
                    setDevManager,
                    consultant,
                    setConsultant,
                    projectStatus,
                    setProjectStatus,
                    latitude,
                    setLatitude,
                    longitude,
                    setLongitude,
                    legalDescription,
                    setLegalDescription,
                    projectNameError,
                    setProjectNameError,
                    dateIdentifiedError,
                    setDateIdentifiedError,
                    devManagerError,
                    setDevManagerError,
                    consultantError,
                    setConsultantError,
                    projectStatusError,
                    setProjectStatusError,
                    coordinatesError,
                    setCoordinatesError,
                  }}
                />
              )}
            </div>
          </div>
        </div>
      </Draggable>
    </div>
  );
});

export default GenerateScorecard;
