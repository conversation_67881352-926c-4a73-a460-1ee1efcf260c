.scorecardModalWrap {
  position: absolute !important;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  /* background-color: rgba(0, 0, 0, 0.25); */
}

.scorecardModalContainer {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-52.5%, -50%);
  background-color: white;
  /* height: 95%;
  width: 90%; */
  /* height: 65%; */
  width: 90%;
  max-width: 600px;
  /* height: 663px; */
  overflow: hidden;
  box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  display: flex;
  flex-direction: column;
  border-radius: 10px;
}
.scorecardModalHeaderBtnContainer {
  padding: 20px 20px 0px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
}
.scorecardModalHeader {
  padding: 0px 20px 10px;
  display: flex;
  /* flex-direction: row; */
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  position: relative;
}
.scorecardModalHeader h2 {
  margin: 0;
  font-weight: 600;
}

.scorecardModalBody {
  padding: 10px 20px;
  display: flex;
  flex-direction: row;
  justify-content: center;
}

.scorecardModalCloseBtn {
  /* position: absolute;
  right: 20px;
  top: 10px; */
  outline: none;
  padding: 0;
  margin: 0;
  background-color: transparent;
  border: transparent;
  cursor: pointer;
  font-size: 24px;
}

.scorecardForm {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 400px;
}
.scorecardInputGroup {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.scorecardGenerateBtnContainer {
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin-top: 15px;
}

.viewHistoryBtn {
  outline: 0;
  padding: 0;
  margin: 0;
  background-color: transparent;
  border: transparent;
  cursor: pointer;
}

.viewHistoryBtn:hover {
  color: var(--antd-active-blue);
}

.viewOptionsBtn {
  outline: 0;
  padding: 0;
  margin: 0;
  background-color: transparent;
  border: transparent;
  cursor: pointer;
}
.viewOptionsBtn:hover {
  color: var(--antd-active-blue);
}

.scorecardContainer {
  display: flex;
  flex-direction: row;
  /* width: 200%; */
  justify-content: center;
}

.scorecard {
  /* height: 674.5px; */
  max-height: 721.5px;
  width: 100%;
}

.analytics {
  height: 674.5px;
  max-height: 721.5px;
  width: 440px;
}
.analyticsHeader {
  text-align: center;
}
.analyticsBody {
  padding: 10px 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: 500px;
}

.history {
  height: 674.5px;
  max-height: 721.5px;
  width: 100%;
  /* transform: translate(-100%);
  opacity: 0;
  transition: all 500ms ease-in-out; */
}

.historyIn {
  transform: translate(0);
  opacity: 1;
}

.historyHeader {
  position: relative;
  text-align: center;
}

.historyBody {
  height: calc(100% - 75px);
  overflow-y: auto;
  padding: 10px;
}

.historyTypeButton {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 20px;
}
.csvBtnContainer {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 20px;
}
