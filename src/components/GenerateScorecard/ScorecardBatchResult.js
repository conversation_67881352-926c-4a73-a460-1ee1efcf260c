import { DownloadOutlined } from '@ant-design/icons';
import { Button, Table } from 'antd';
import moment from 'moment';
import React from 'react';
import { render } from 'react-dom';
import { useQuery } from 'react-query';
import { useSelector } from 'umi';
import { getUserScorecardBatchResult } from '../../services/data';
import styles from './generateScorecard.css';

const ScorecardBatchResult = () => {
  const { userEmail } = useSelector((state) => state.CMA);
  const { data, isError, isLoading, refetch } = useQuery(
    ['user-scorecard-batch-result', userEmail],
    async () =>
      await getUserScorecardBatchResult({
        email: userEmail,
      }),
    {
      enabled: userEmail != null,
    },
  );
  console.log('ScorecardBatchResult data', data);

  const columns = [
    {
      title: 'Portfolio Name',
      dataIndex: 'portfolio_name',
      key: 'portfolio_name',
      width: 200,
      sorter: (a, b) => a.portfolio_name.localeCompare(b.portfolio_name),
    },
    {
      title: 'Batch Size',
      dataIndex: 'batch_size',
      key: 'batch_size',
      sorter: (a, b) => a.batch_size - b.batch_size,
    },
    {
      title: 'Output Timestamp (UTC)',
      defaultSortOrder: 'descend',
      dataIndex: 'output_timestamp',
      key: 'output_timestamp',
      sorter: (a, b) => {
        const dateA = new Date(a.output_timestamp);
        const dateB = new Date(b.output_timestamp);
        return dateA - dateB;
      },
      render: (text) => {
        // Parse the timestamp (assuming UTC input)
        const date = moment.utc(text);
        return date.format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: 'Download',
      dataIndex: 'output_file_url',
      key: 'output_file_url',
      render: (url) => (
        <a href={url} download>
          <DownloadOutlined />
        </a> // <Button
      ),
    },
  ];

  return (
    <div className={`${styles.history}`}>
      <div className={styles.analyticsHeader}>
        <h2>Scorecard Batch Result</h2>
      </div>

      {isLoading ? (
        <div>Loading...</div>
      ) : isError ? (
        <div>Error</div>
      ) : (
        <div className={styles.historyBody}>
          <Table
            columns={columns}
            dataSource={data}
            rowKey="id"
            pagination={false}
            className="shadow-md rounded"
          />
        </div>
      )}
    </div>
  );
};

export default ScorecardBatchResult;
