import { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'umi';
import styles from './generateScorecard.css';
import { Select, DatePicker, Input, AutoComplete, Progress, Spin } from 'antd';
import county from './county';
import moment from 'moment';
import { dateFormat } from '../../constants';
import isEmpty from 'lodash.isempty';

const { RangePicker } = DatePicker;

const categories = [
  { value: 'date', label: 'Date' },
  { value: 'devManager', label: 'Development Manager' },
  { value: 'consultant', label: 'Consultant' },
  { value: 'submarket', label: 'Submarket' },
  { value: 'county', label: 'County' },
];

function Analytics(props) {
  const scorecardAnalytics = useSelector(
    (state) => state.CMA.scorecardAnalytics,
  );
  const dispatch = useDispatch();

  const [loading, setLoading] = useState(false);

  const [options, setOptions] = useState([]);
  const [analyticCategory, setAnalyticCategory] = useState(null);
  const [analyticDateRange, setAnalyticDateRange] = useState([]);
  const [devManagerAnalytic, setDevManagerAnalytic] = useState(null);
  const [consultantAnalytic, setConsultantAnalytic] = useState(null);
  const [submarketAnalytic, setSubmarketAnalytic] = useState('Johnson County');
  const [countyAnalytic, setCountyAnalytic] = useState(null);

  useEffect(() => {
    return () => {
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: { scorecardAnalytics: {} },
      });
    };
  }, []);

  const onChangeCategory = (category) => {
    setAnalyticCategory(category);
    // clear inputs
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: { scorecardAnalytics: {} },
    });
    setAnalyticDateRange([]);
    setDevManagerAnalytic(null);
    setConsultantAnalytic(null);
    setCountyAnalytic(null);

    if (category === 'submarket') {
      setLoading(true);
      dispatch({
        type: 'CMA/getScorecardAnalyticsBySubmarket',
        payload: { submarket: submarketAnalytic },
      }).then(() => {
        setLoading(false);
      });
    }
  };

  const onChangeDevManager = (managerName) => {
    setDevManagerAnalytic(managerName);
    setLoading(true);
    dispatch({
      type: 'CMA/getScorecardAnalyticsByDevManager',
      payload: { devManager: managerName },
    }).then(() => {
      setLoading(false);
    });
  };
  const onChangeConsultant = (consultant) => {
    setConsultantAnalytic(consultant);
    setLoading(true);
    dispatch({
      type: 'CMA/getScorecardAnalyticsByConsultant',
      payload: { consultant: consultant },
    }).then(() => {
      setLoading(false);
    });
  };

  const onSearch = (text) => {
    const searchText = text.trim();
    if (searchText.length > 0 && searchText.replace(/\s/g, '').length > 0) {
      const newOptions = county
        .filter((county) =>
          county.toLowerCase().includes(searchText.toLowerCase()),
        )
        .map((county) => ({ value: county }));
      setOptions(newOptions);
    } else {
      setOptions([]);
    }
  };

  const onSelectCounty = (county) => {
    setCountyAnalytic(county);
    setLoading(true);
    dispatch({
      type: 'CMA/getScorecardAnalyticsByCounty',
      payload: { county: county },
    }).then(() => {
      setLoading(false);
    });
  };

  const onChangeDateRange = (dateRange) => {
    if (dateRange.length === 2) {
      setAnalyticDateRange(dateRange);

      setLoading(true);
      const startDate = moment(dateRange[0]).format(dateFormat);
      const endDate = moment(dateRange[1]).format(dateFormat);
      dispatch({
        type: 'CMA/getScorecardAnalyticsByDate',
        payload: { startDate: startDate, endDate: endDate },
      }).then(() => {
        setLoading(false);
      });
    }
  };

  return (
    <div className={styles.analytics}>
      <div className={styles.analyticsHeader}>
        <h2>Analytics</h2>
      </div>
      <div className={styles.analyticsBody}>
        <div>
          <p>Get the overall score of scorecards</p>
        </div>
        <div>
          <Select
            id="category"
            placeholder="Select analysis category"
            value={analyticCategory}
            onChange={onChangeCategory}
            options={categories}
            style={{ width: '100%' }}
          />
        </div>
        {analyticCategory === 'date' && (
          <div>
            <RangePicker
              value={analyticDateRange}
              format={dateFormat}
              allowClear={false}
              allowEmpty={[false, false]}
              onChange={onChangeDateRange}
              style={{ width: '100%' }}
            />
          </div>
        )}
        {analyticCategory === 'devManager' && (
          <div>
            <Select
              id="dev_managers"
              placeholder="Select a manager"
              optionFilterProp="children"
              value={devManagerAnalytic}
              onChange={onChangeDevManager}
              options={props.developmentManagerList.map((name) => ({
                value: name,
                label: name,
              }))}
              style={{ width: '100%' }}
            />
          </div>
        )}
        {analyticCategory === 'consultant' && (
          <div>
            <Select
              id="consultant"
              placeholder="Select a consultant"
              optionFilterProp="children"
              value={consultantAnalytic}
              onChange={onChangeConsultant}
              options={props.consultantList.map((name) => ({
                value: name,
                label: name,
              }))}
              style={{ width: '100%' }}
            />
          </div>
        )}
        {analyticCategory === 'submarket' && (
          <div>
            <Input value={submarketAnalytic} disabled={true} />
          </div>
        )}
        {analyticCategory === 'county' && (
          <div>
            <AutoComplete
              defaultValue={countyAnalytic}
              onSearch={onSearch}
              onSelect={onSelectCounty}
              options={options}
              style={{ width: '100%' }}
              placeholder="Enter a county"
            />
          </div>
        )}
        <div style={{ height: '100%', padding: '50px', textAlign: 'center' }}>
          {!loading && !isEmpty(scorecardAnalytics) && (
            <>
              {scorecardAnalytics.overall_score ? (
                <Progress
                  id="scorecard_progress"
                  type="circle"
                  percent={(scorecardAnalytics.overall_score / 10) * 100}
                  format={() =>
                    `${scorecardAnalytics.overall_score.toFixed(1)}/10`
                  }
                  strokeColor={{ '0%': '#108ee9', '100%': '#87d068' }}
                  style={{ width: '100%' }}
                />
              ) : (
                'No overall score found'
              )}
            </>
          )}
          {loading && <Spin size="large" />}
        </div>
      </div>
    </div>
  );
}

export default Analytics;
