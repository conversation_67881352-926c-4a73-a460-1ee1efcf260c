.modalRowWrapper {
  /* padding: 24px 36px; */
  /* padding-left: 24px;
  padding-right: 24px; */
}

.modalColWrapper {
  padding: 0 36px;
}

.modalTitle {
  font-size: 20px;
  display: flex;
  justify-content: flex-start;
  padding: 0px 12px 8px;
}

.divider {
  width: calc(100% + 48px);
  height: 1px;
  background-color: rgba(0,0,0,.1);
  margin: 8px -24px;
}

.instruction {
  /* font-size: 13px; */
  /* color: #444; */
  justify-content: flex-start;
  padding: 16px 0;
}

.csvUploadWrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  /* gap: 8px; */
  /* padding: 16px 0; */
  margin-bottom: 36px;
}

.csvUploadInput {
  /* background-color: azure; */
}

.recordTable {
  padding-top: 16px;
  margin-bottom: -16px;
  /* max-height: 60vh; */
  overflow: auto;
}

/* .modalFooter {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
  height: 32px;

} */

.fieldsetWrapper {
  margin-bottom: 36px;
}

.filterTitle {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 16px;
}

.filterLabel {
  display: block;
  margin-bottom: 36px;
}

.filterLabelText {
  font-size: 16px;
  font-weight: 500;
  margin-right: 16px;
}

.filterDivider {
  width: 100%;
  height: 1px;
  background-color: rgba(0,0,0,.1);
  margin: 8px 0;
}

.uploadCSVButton {
  width: 200px;
  padding: 0;
}

/* progress bar */
/* ref: https://css-tricks.com/css3-progress-bars/ */
.progressMeter {
  width: 100%;
  height: 100%;
  position: relative;
  background: #e2e2e2;
  /* padding: 4px; */
}

.progressFill {
  display: block;
  position: relative;
  overflow: hidden;
  height: 100%;
  background-color: rgb(43,194,83);
}

.progressText {
  display: block;
  position: absolute;
  width: 100%;
  top: 0px;
  left: 0px;
  text-align: center;
  line-height: 30px;
  color: #333;
  font-weight: 500;
}

.progressAnimate {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-image: linear-gradient(
    -45deg,
    rgba(255, 255, 255, 0.2) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 75%,
    transparent
  );
  z-index: 1;
  background-size: 50px 50px;
  animation: move 2s linear infinite;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
  overflow: hidden;
}

@keyframes move {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 50px 50px;
  }
}