import styles from '@/components/ResultTable/resultTable.css';

export const showSFROperatorsFullName = (brand) => {
  brand = brand.replace(/ /g, ''); // remove whitespace

  switch (brand) {
    case 'AH4R':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>
          American Homes 4 Rent
        </span>
      );
    case 'Invitatio':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>
          Invitation Homes
        </span>
      );
    case 'HRG':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>
          HomeRiver Group
        </span>
      );
    case 'PR':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>
          Progress Residential
        </span>
      );
    case 'CPM':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>
          Conrex Property Management
        </span>
      );
    case 'TR':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>
          Tricon Residential
        </span>
      );
    case 'MYND':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>MYND</span>
      );
    case 'KP':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>
          Kinloch Partners
        </span>
      );
    case 'RW':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>
          Renters Warehouse
        </span>
      );
    case 'VH':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>
          Vinebrook Homes
        </span>
      );
    case 'Amhrest':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>
          Amherst
        </span>
      );
    case 'ARG':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>ARG</span>
      );
    case 'Brandywine':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>
          Brandywine
        </span>
      );
    case 'BridgeHome':
    case 'BridgeHomes':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>
          Bridge Home
        </span>
      );
    case 'Camillo':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>
          Camillo
        </span>
      );
    case 'Copperbay':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>
          Copperbay
        </span>
      );
    case 'Divvy':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>Divvy</span>
      );
    case 'FirstKey':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>
          FirstKey
        </span>
      );
    case 'Hudson':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>
          Hudson
        </span>
      );
    case 'Imagine':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>
          Imagine
        </span>
      );
    case 'KairosLiving':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>
          Kairos Living
        </span>
      );
    case 'KrchRealty':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>
          Krch Realty
        </span>
      );
    case 'LiveReszi':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>
          Live Reszi
        </span>
      );
    case 'OpenHouse':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>
          Open House
        </span>
      );
    case 'Pathway':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>
          Pathway Homes
        </span>
      );
    case 'Peak':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>Peak</span>
      );
    case 'PPMG':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>PPMG</span>
      );
    case 'Propify':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>
          National Home Rental
        </span>
      );
    case 'RENU':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>RENU</span>
      );
    case 'ResiHome':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>
          ResiHome
        </span>
      );
    case 'SPA':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>
          Sparrow
        </span>
      );
    case 'Streetlane':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>
          Streetlane
        </span>
      );
    case 'SYLV':
      return (
        <span className={styles['text_nationalOperators_' + brand]}>
          Sylvan
        </span>
      );
  }
};

export const showSFROperatorsFullNameOnly = (brand) => {
  brand = brand.replace(/ /g, ''); // remove whitespace

  switch (brand) {
    case 'AH4R':
      return 'American Homes 4 Rent';
    case 'Invitatio':
      return 'Invitation Homes';
    case 'HRG':
      return 'HomeRiver Group';
    case 'PR':
      return 'Progress Residential';
    case 'CPM':
      return 'Conrex Property Management';
    case 'TR':
      return 'Tricon Residential';
    case 'MYND':
      return 'MYND';
    case 'KP':
      return 'Kinloch Partners';
    case 'RW':
      return 'Renters Warehouse';
    case 'VH':
      return 'Vinebrook Homes';
    case 'Amhrest':
      return 'Amherst';
    case 'ARG':
      return 'ARG';
    case 'Brandywine':
      return 'Brandywine';
    case 'BridgeHome':
    case 'BridgeHomes':
      return 'Bridge Home';
    case 'Camillo':
      return 'Camillo';
    case 'Copperbay':
      return 'Copperbay';
    case 'Divvy':
      return 'Divvy';
    case 'FirstKey':
      return 'FirstKey';
    case 'Hudson':
      return 'Hudson';
    case 'Imagine':
      return 'Imagine';
    case 'KairosLiving':
      return 'Kairos';
    case 'KrchRealty':
      return 'Krch Realty';
    case 'LiveReszi':
      return 'Live Reszi';
    case 'OpenHouse':
      return 'Open House';
    case 'Pathway':
      return 'Pathway Homes';
    case 'Peak':
      return 'Peak';
    case 'PPMG':
      return 'PPMG';
    case 'Propify':
      return 'National Home Rental';
    case 'RENU':
      return 'RENU';
    case 'ResiHome':
      return 'ResiHome';
    case 'SPA':
      return 'Sparrow';
    case 'Streetlane':
      return 'Streetlane';
    case 'SYLV':
      return 'Sylvan';
    case 'Hotpads':
      return '3rd Party';
  }
};
