import GeneratePDF from '@/components/GeneratePDF/GeneratePDF';
import { getOtherOwnersDetails } from '@/services/data';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { useMap } from '@spatiallaser/map';
import { Badge, Button, Checkbox, Dropdown, Modal, Row, Tooltip } from 'antd';
import React, { useCallback, useEffect } from 'react';
import { Dispatch, connect } from 'umi';
import { geojsonTemplate } from '../../constants';
import { userGroupHasAccess } from '../../utils/userGroup';
import CluesoLink from '../CluesoLink';
import EditAddress from '../EditAddress/EditAddress';
import GenerateCombinePDF from '../GeneratePDF/GenerateCombinePDF';
import BadgeWrapper from './BadgeWrapper';

interface IHeaderBarProps {
  map: any; // Define a more specific type if possible
  currentMapLayerOptions: string[];
  scorecardModalOpen: boolean;
  displayScorecardHistory: boolean;
  userEmail: string;
  userGroup: string[];
  selectedUserGroup: string;
  trialAccountHasLandParcelSearch: boolean;
  trialAccountHasLandDevelopmentTools: boolean;
  appViewMode: {
    mode: string;
    subMode: string[];
  };
  dispatch: Dispatch;
}

const HeaderBar: React.FC<IHeaderBarProps> = (props) => {
  const { activeLayers, setActiveLayers, setOpenSitePlanModal } = useMap();

  console.log('activeLayers', activeLayers);

  const onOk = useCallback(
    (viewMode: any) => {
      props.map.fire('mapDraw.clear');
      props.map.fire('selectRadius.parcelMode', {
        payload: { parcelMode: false },
      });
      props.map.fire('selectRadius.setEventCoordinates', {
        payload: {
          eventCoordinates: [],
        },
      });
      props.map.fire('mapLayers.currentMapLayerOptions', {
        payload: {
          currentMapLayerOptions: [],
        },
      });
      props.map.fire('heatmap.updateType', {
        payload: { heatmapType: null },
      });
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          cmaTabKey: null,
          appViewMode: {
            mode: 'LandDevelopment',
            subMode: [viewMode],
          },
          currentMapLayerOptions: [],
          ownersOtherParcels: true,
        },
      });
    },
    [props.map],
  );
  useEffect(() => {
    if (!props.map) return;

    props.map.on('open-monthly-images', () => {
      onOk('LandMonthlyImages');
    });
    props.map.on('owner-other-parcel-land-search', (e: any) => {
      const parcel = e.parcel; // This contains the data you passed
      console.log('Received data land-search:', parcel);
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          ownersOtherParcelsTrigger: true,
          currentParcelDetails: parcel,
        },
      });

      onOk('LandParcelSearch');
    });
  }, [props.map]);

  const menuItemsWorkforce = [
    {
      key: 'Toyota',
      // label: 'Toyota',
      label: (
        <Tooltip
          key="toyota tooltip"
          title={
            !userGroupHasAccess(props.selectedUserGroup, 'workforcemobile')
              ? 'Please contact us for a demo'
              : ''
          }
        >
          <span>Toyota</span>
        </Tooltip>
      ),
      disabled: !userGroupHasAccess(props.selectedUserGroup, 'workforcemobile'),
    },
    {
      key: 'TrinityFalls',
      // label: 'Trinity Falls',
      label: (
        <Tooltip
          key="toyota tooltip"
          title={
            !userGroupHasAccess(props.selectedUserGroup, 'workforcemobile')
              ? 'Please contact us for a demo'
              : ''
          }
        >
          <span>Trinity Falls</span>
        </Tooltip>
      ),
      disabled: !userGroupHasAccess(props.selectedUserGroup, 'workforcemobile'),
    },
  ];
  const menuItemsToolBox = [
    {
      key: 'parcel-output',
      // label: 'Toyota',
      label: (
        <Tooltip
          key="parcel output"
          title={'Draw a boundary to export parcels'}
        >
          <span>Parcel Output</span>
        </Tooltip>
      ),
      disabled: !userGroupHasAccess(props.selectedUserGroup, 'workforcemobile'),
    },
  ];
  const menuItemsHeatmap = [
    {
      key: 'heatmap-demographics',
      label: 'Demographics',
    },
    {
      key: 'heatmap-submarket',
      label: 'MF Submarket',
    },
  ];

  const menuItemsScorecard = [
    {
      key: 'generate-scorecard',
      label: 'Generate Scorecard',
    },
    {
      key: 'scorecard-history',
      label: (
        <div className="flex items-center justify-between">
          <span>Scorecard History</span>
          {props.displayScorecardHistory && (
            <div className="w-2 h-2 bg-green-500 rounded-full ml-2"></div>
          )}
        </div>
      ),
    },
  ];

  const menuItemsChainStores = [
    {
      key: 'chain-stores',
      // label: 'Chain Stores',
      label: (
        <Tooltip
          title={
            !userGroupHasAccess(props.selectedUserGroup, 'Chain Stores')
              ? 'Please contact us for a demo'
              : ''
          }
        >
          <Button
            type="text"
            onClick={() => {
              if (props.appViewMode.mode === 'CMA') {
                const payload = {
                  currentMapLayerOptions: [
                    ...props.currentMapLayerOptions,
                    'chain stores',
                  ],
                };
                props.map.fire('mapLayers.currentMapLayerOptions', {
                  payload: payload,
                });
                props.dispatch({
                  type: 'CMA/saveCMAStates',
                  payload: payload,
                });
              }
            }}
            disabled={
              !userGroupHasAccess(props.selectedUserGroup, 'Chain Stores')
            }
          >
            <span>Chain Stores</span>
          </Button>
        </Tooltip>
      ),
    },
    {
      key: 'gentrifying-neighbourhoods',
      label: (
        <Tooltip
          title={
            !userGroupHasAccess(
              props.selectedUserGroup,
              'Gentrifying Neighbourhoods',
            )
              ? 'Please contact us for a demo'
              : ''
          }
        >
          <Button
            type="text"
            onClick={() => {
              if (props.appViewMode.mode === 'CMA') {
                const payload = {
                  currentMapLayerOptions: [
                    ...props.currentMapLayerOptions,
                    'gn',
                  ],
                };
                props.map.fire('mapLayers.currentMapLayerOptions', {
                  payload: payload,
                });
                props.dispatch({
                  type: 'CMA/saveCMAStates',
                  payload: payload,
                });
              }
            }}
            disabled={
              !userGroupHasAccess(
                props.selectedUserGroup,
                'Gentrifying Neighbourhoods',
              )
            }
          >
            <span>Gentrifying Neighborhoods</span>
          </Button>
        </Tooltip>
      ),
    },
  ];

  const onClickMenuWorkforce = ({ key }: { key: string }) => {
    switch (key) {
      case 'Toyota':
      case 'TrinityFalls':
        props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            mobileDataType: key,
          },
        });
        break;
      default:
        break;
    }
  };

  const onClickMenuToolbox = ({ key }: { key: string }) => {
    switch (key) {
      case 'parcel-output':
        if (props.map) {
          // Check current zoom level
          const currentZoom = props.map.getZoom();

          // If current zoom is less than 14, zoom the map to level 14
          if (currentZoom < 14) {
            props.map.zoomTo(14, {
              duration: 1000, // 1 second animation
            });
          }

          // Activate the polygon export feature
          props.map.fire('polygonExport.activate');

          // Update menu state
          const newOptions = [
            ...props.currentMapLayerOptions,
            'export parcels',
          ];
          props.dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              currentMapLayerOptions: newOptions,
            },
          });
        }
        break;
      default:
        break;
    }
  };
  const onClickMenuHeatmap = ({ key }: { key: string }) => {
    switch (key) {
      case 'heatmap-demographics':
        props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            openDemographicsHeatmap: true,
          },
        });
        setActiveLayers((prevState: Array<string>) => [
          ...prevState,
          'heatmap-demographics',
        ]);
        break;
      case 'heatmap-submarket':
        if (props.map) {
          const heatmapType = key.split('-')[1];
          console.log('heatmapType', heatmapType);
          props.map.fire('heatmap.updateType', {
            payload: { heatmapType: heatmapType },
          });
        }
        break;
      default:
        break;
    }
  };

  const onClickMenuScorecard = ({ key }: { key: string }) => {
    switch (key) {
      case 'generate-scorecard':
        if (userGroupHasAccess(props.selectedUserGroup, 'Scorecard')) {
          props.dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              scorecardModalOpen: !props.scorecardModalOpen,
            },
          });
        }
        break;
      case 'scorecard-history':
        props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            displayScorecardHistory: !props.displayScorecardHistory,
          },
        });
        break;
      default:
        break;
    }
  };

  const openConfirmation = (title: string, viewMode: any) => {
    const confirm = () => {
      Modal.confirm({
        title: title,
        icon: <ExclamationCircleOutlined />,
        content:
          'Opening this feature will cause you to enter into land development mode which will turn off comps and other active features. Do you want to continue?',
        okText: 'Yes',
        cancelText: 'No',
        centered: true,
        onOk: () => onOk(viewMode),
      });
    };

    if (props.appViewMode.mode != 'LandDevelopment') {
      confirm();
    } else {
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          appViewMode: {
            mode: 'LandDevelopment',
            subMode: [
              ...props.appViewMode.subMode.filter((mode) => mode != viewMode),
              viewMode,
            ],
          },
        },
      });
    }
  };

  const handleMapLayerOptions = (clickedLayer: any) => {
    if (!props.map) return;
    if (!props.currentMapLayerOptions.includes(clickedLayer)) {
      const payload = {
        currentMapLayerOptions: [...props.currentMapLayerOptions, clickedLayer],
      };
      props.map.fire('mapLayers.currentMapLayerOptions', {
        payload: payload,
      });
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: payload,
      });
    } else {
      const currentMapLayerOptions = [
        ...props.currentMapLayerOptions.filter(
          (layer) => layer !== clickedLayer,
        ),
      ];
      props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          currentMapLayerOptions: currentMapLayerOptions,
        },
      });
      props.map.fire('mapLayers.currentMapLayerOptions', {
        payload: {
          currentMapLayerOptions: currentMapLayerOptions,
        },
      });
    }
  };

  const onClickMenuLandDevelopment = ({ key }: { key: string }) => {
    switch (key) {
      case 'land-residential-development':
        handleMapLayerOptions('residential development');
        break;
      case 'land-new-construction':
        handleMapLayerOptions('new construction');
        break;
      case 'land-bank':
        handleMapLayerOptions('land bank');
        break;
      case 'industry-news':
        handleMapLayerOptions('industry news');
        break;
      case 'btr-pipeline-ld':
        handleMapLayerOptions('btr-pipeline-ld');
        break;
      case 'land-parcel-search':
        openConfirmation('Land Parcel Search', 'LandParcelSearch');
        break;
      case 'land-monthly-images':
        openConfirmation('Monthly Images', 'LandMonthlyImages');
        break;
      case 'site-plan-generator':
        setOpenSitePlanModal(true);
        break;
      case 'newly-subdivided-parcels':
        handleMapLayerOptions('split parcels');
      default:
        break;
    }
  };

  let landDevelopmentCount = 0;
  if (props.currentMapLayerOptions) {
    landDevelopmentCount = props.currentMapLayerOptions.filter((layer) =>
      ['residential development', 'new construction'].includes(layer),
    ).length;
  }

  return (
    <Row
      key="headerBar row"
      // gutter={16}
      className="flex flex-row items-center justify-start gap-2 w-screen py-1 pl-2 pr-4 border-t border-b border-gray-200 border-opacity-5 bg-white"
    >
      {/* TrueHold Target Upload */}
      {props.userGroup[0] === 'Truehold' && (
        <Tooltip key="truehold tooltip" title="">
          <Button
            key="targets upload button"
            type="text"
            onClick={() => {
              props.dispatch({
                type: 'CMA/saveCMAStates',
                payload: {
                  showTargetsUploadModal: true,
                },
              });
            }}
          >
            <span className="text-sm font-medium leading-normal text-BT-blue">
              Targets Upload
            </span>
          </Button>
        </Tooltip>
      )}

      {/* Batch Comp */}

      <Tooltip
        key="batch comp tooltip"
        title={
          !userGroupHasAccess(props.selectedUserGroup, 'Batch Process')
            ? 'Please contact us for a demo'
            : ''
        }
      >
        <Button
          key="batch comp button"
          type="text"
          onClick={() => {
            props.dispatch({
              type: 'CMA/saveCMAStates',
              payload: {
                showBatchProcessor: true,
              },
            });
          }}
          disabled={
            !userGroupHasAccess(props.selectedUserGroup, 'Batch Process')
          }
        >
          <BadgeWrapper currentMapLayerOptions={[]}>
            <span className="text-sm font-medium leading-normal text-BT-blue">
              Batch Comp
            </span>
          </BadgeWrapper>

          {/* <span className="text-sm font-medium leading-normal text-BT-blue">
            Batch Comp
          </span> */}
        </Button>
      </Tooltip>

      {/* BTR */}
      <Dropdown
        key="btr dropdown"
        menu={{
          items: [
            // BFR Communities (Divided Lots)
            {
              key: 'bfr cluster',
              label: (
                <Checkbox
                  onChange={(e) => {
                    const layerKey = 'bfr cluster';
                    const updatedOptions = e.target.checked
                      ? [...props.currentMapLayerOptions, layerKey]
                      : props.currentMapLayerOptions.filter(
                          (option) => option !== layerKey,
                        );

                    props.dispatch({
                      type: 'CMA/saveCMAStates',
                      payload: {
                        currentMapLayerOptions: updatedOptions,
                      },
                    });
                  }}
                  checked={props.currentMapLayerOptions.includes('bfr cluster')}
                >
                  Communities
                </Checkbox>
              ),
            },
            // BTR Pipeline
            {
              key: 'btr-pipeline-ld',
              label: (
                <Checkbox
                  onChange={(e) => {
                    const layerKey = 'btr-pipeline-ld';
                    const updatedOptions = e.target.checked
                      ? [...props.currentMapLayerOptions, layerKey]
                      : props.currentMapLayerOptions.filter(
                          (option) => option !== layerKey,
                        );

                    props.dispatch({
                      type: 'CMA/saveCMAStates',
                      payload: {
                        currentMapLayerOptions: updatedOptions,
                      },
                    });
                  }}
                  checked={props.currentMapLayerOptions.includes(
                    'btr-pipeline-ld',
                  )}
                >
                  Pipelines
                </Checkbox>
              ),
            },
            // BTR Pipeline Submission
            {
              key: 'btr pipeline submission',
              label: (
                <Tooltip key="btr submission tooltip" title="">
                  <Button
                    key="btr submission button"
                    type="text"
                    onClick={() => {
                      props.dispatch({
                        type: 'CMA/saveCMAStates',
                        payload: {
                          showBTRSubmissionPanel: true,
                        },
                      });
                    }}
                  >
                    New Submissions
                  </Button>
                </Tooltip>
              ),
            },
          ],
          // onClick: onClickMenuLandDevelopment,
        }}
      >
        <Button
          type="text"
          // disabled={!userGroupHasAccess(props.selectedUserGroup, 'Land Development')}
        >
          <BadgeWrapper currentMapLayerOptions={props.currentMapLayerOptions}>
            <span className="text-sm font-medium leading-normal text-BT-blue">
              BTR
            </span>
          </BadgeWrapper>
        </Button>
      </Dropdown>
      {/* Scorecard */}
      <Dropdown
        key="scorecard dropdown"
        menu={{
          items: menuItemsScorecard,
          onClick: onClickMenuScorecard,
        }}
      >
        <Button
          key="scorecard button"
          type="text"
          disabled={!userGroupHasAccess(props.selectedUserGroup, 'Scorecard')}
        >
          <span
            className={`text-sm font-medium leading-normal ${
              !userGroupHasAccess(props.selectedUserGroup, 'Scorecard')
                ? 'text-gray-500'
                : 'text-BT-blue'
            }`}
          >
            Scorecard
          </span>
        </Button>
      </Dropdown>
      {/* <GeneratePDF isIcon={false} source="header" /> */}
      {/* <GenerateCombinePDF /> */}
      {/* <EditAddress type="premium" /> */}

      {/* Heatmap */}
      <Dropdown
        key="heatmap dropdown"
        menu={{
          items: menuItemsHeatmap,
          onClick: onClickMenuHeatmap,
        }}
      >
        <Button
          key="heatmap button"
          type="text"
          // disabled={!userGroupHasAccess(props.selectedUserGroup, 'Scorecard')}
        >
          <span className="text-sm font-medium leading-normal text-BT-blue">
            Heatmap
          </span>
        </Button>
      </Dropdown>
      {/* LD */}
      <Dropdown
        key="land development dropdown"
        menu={{
          items: [
            {
              key: 'newly-subdivided-parcels',
              label: (
                <Checkbox
                  onChange={(e) => {
                    onClickMenuLandDevelopment({
                      key: 'newly-subdivided-parcels',
                    });
                  }}
                  checked={props.currentMapLayerOptions.includes(
                    'split parcels',
                  )}
                >
                  Newly Subdivided Parcels
                </Checkbox>
              ),
            },
            {
              key: 'land-residential-development',
              label: (
                <Checkbox
                  onChange={(e) => {
                    onClickMenuLandDevelopment({
                      key: 'land-residential-development',
                    });
                  }}
                  checked={props.currentMapLayerOptions.includes(
                    'residential development',
                  )}
                  disabled={!props.trialAccountHasLandDevelopmentTools}
                >
                  Residential Development
                </Checkbox>
              ),
            },
            {
              key: 'land-new-construction',
              label: (
                <Checkbox
                  onChange={(e) => {
                    onClickMenuLandDevelopment({
                      key: 'land-new-construction',
                    });
                  }}
                  checked={props.currentMapLayerOptions.includes(
                    'new construction',
                  )}
                  disabled={!props.trialAccountHasLandDevelopmentTools}
                >
                  New Construction
                </Checkbox>
              ),
            },
            {
              key: 'land-bank',
              label: (
                <Checkbox
                  onChange={(e) => {
                    onClickMenuLandDevelopment({
                      key: 'land-bank',
                    });
                  }}
                  checked={props.currentMapLayerOptions.includes('land bank')}
                  // disabled={!props.trialAccountHasLandDevelopmentTools}
                >
                  Land Bank
                </Checkbox>
              ),
            },
            {
              key: 'industry-news',
              label: (
                <Checkbox
                  onChange={(e) => {
                    onClickMenuLandDevelopment({
                      key: 'industry-news',
                    });
                  }}
                  checked={props.currentMapLayerOptions.includes(
                    'industry news',
                  )}
                >
                  Industry News
                </Checkbox>
              ),
            },
            {
              key: 'land-parcel-search',
              label: (
                <Button
                  type="text"
                  onClick={() => {
                    onClickMenuLandDevelopment({
                      key: 'land-parcel-search',
                    });
                  }}
                  disabled={
                    (props.selectedUserGroup === 'DRHorton' &&
                      ![
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                      ].includes(props.userEmail.toLowerCase())) ||
                    !props.trialAccountHasLandParcelSearch ||
                    (props.selectedUserGroup != 'DRHorton' &&
                      !userGroupHasAccess(
                        props.selectedUserGroup,
                        'Land Parcel Search',
                      ))
                  }
                >
                  Land Search
                </Button>
              ),
            },
            {
              key: 'land-monthly-images',
              label: (
                <Button
                  type="text"
                  onClick={() => {
                    onClickMenuLandDevelopment({
                      key: 'land-monthly-images',
                    });
                  }}
                  disabled={
                    // !['demo-users', 'BlueRiver'].includes(props.userGroup[0])
                    true
                  }
                >
                  Monthly Images
                </Button>
              ),
            },
            {
              key: 'site-plan-generator',
              label: (
                <Button
                  type="text"
                  onClick={() => {
                    onClickMenuLandDevelopment({
                      key: 'site-plan-generator',
                    });
                  }}
                  disabled={
                    // !userGroupHasAccess(
                    //   props.selectedUserGroup,
                    //   'Site Plan Generator',
                    // )
                    true
                  }
                >
                  Site Plan Generator
                </Button>
              ),
            },
          ],
          // onClick: onClickMenuLandDevelopment,
        }}
      >
        <Button
          type="text"
          // disabled={!userGroupHasAccess(props.selectedUserGroup, 'Land Development')}
        >
          <Badge
            count={landDevelopmentCount}
            showZero={false}
            size="small"
            offset={[5, 0]}
          >
            <span className="text-sm font-medium leading-normal text-BT-blue">
              Land Development
            </span>
          </Badge>
        </Button>
      </Dropdown>

      {/* Chainstore */}
      <Dropdown
        key="chain stores dropdown"
        menu={{
          items: menuItemsChainStores,
          onClick: onClickMenuHeatmap,
        }}
      >
        <Button
          key="heatmap button"
          type="text"
          // disabled={!userGroupHasAccess(props.selectedUserGroup, 'Scorecard')}
        >
          <span className="text-sm font-medium leading-normal text-BT-blue">
            Chain Stores
          </span>
        </Button>
      </Dropdown>
      {/* Mobile */}
      <Dropdown
        key="workforce dropdown"
        menu={{
          items: menuItemsWorkforce,
          onClick: onClickMenuWorkforce,
        }}
        // disabled={!userGroupHasAccess(props.selectedUserGroup, 'workforcemobile')}
      >
        <Button key="workforce mobile analytics button" type="text">
          <span className="text-sm font-medium leading-normal text-BT-blue">
            Mobile Analytics
          </span>
        </Button>
      </Dropdown>

      {/* PH */}
      <Tooltip
        key="affordable housing tooltip"
        title={
          !userGroupHasAccess(props.selectedUserGroup, 'Affordable Housing')
            ? 'Contact us for demo...'
            : ''
        }
      >
        <Button
          type="text"
          disabled={
            !userGroupHasAccess(props.selectedUserGroup, 'Affordable Housing')
          }
          onClick={() => {
            if (props.appViewMode.mode === 'CMA') {
              const payload = {
                currentMapLayerOptions: [
                  ...props.currentMapLayerOptions,
                  'public housing menu',
                ],
              };
              props.map.fire('mapLayers.currentMapLayerOptions', {
                payload: payload,
              });
              props.dispatch({
                type: 'CMA/saveCMAStates',
                payload: payload,
              });
            }
          }}
        >
          <span
            className={`text-sm font-medium leading-normal ${
              !userGroupHasAccess(props.selectedUserGroup, 'Affordable Housing')
                ? 'text-gray-500'
                : 'text-BT-blue'
            }`}
          >
            Affordable Housing
          </span>
        </Button>
      </Tooltip>

      {/* Toolbox */}
      <Dropdown
        key="toolbox dropdown"
        menu={{
          items: menuItemsToolBox,
          onClick: onClickMenuToolbox,
        }}
        // disabled={!userGroupHasAccess(props.selectedUserGroup, 'workforcemobile')}
      >
        <Button key="toolbox button" type="text">
          <span className="text-sm font-medium leading-normal text-BT-blue">
            Toolbox
          </span>
        </Button>
      </Dropdown>
      <div key="divider" className="h-3.5 w-0.5 bg-BT-blue opacity-70" />
      <div className="px-4 hover:cursor-pointer hover:bg-slate-50">
        <CluesoLink />
      </div>
    </Row>
  );
};

export default connect(({ CMA }: { CMA: any }) => ({
  map: CMA.map,
  currentMapLayerOptions: CMA.currentMapLayerOptions,
  scorecardModalOpen: CMA.scorecardModalOpen,
  displayScorecardHistory: CMA.displayScorecardHistory,
  userEmail: CMA.userEmail,
  userGroup: CMA.userGroup,
  selectedUserGroup: CMA.selectedUserGroup,
  trialAccountHasLandParcelSearch: CMA.trialAccountHasLandParcelSearch,
  trialAccountHasLandDevelopmentTools: CMA.trialAccountHasLandDevelopmentTools,
  appViewMode: CMA.appViewMode,
}))(HeaderBar);
