import { Badge } from 'antd';

const BadgeWrapper = ({
  currentMapLayerOptions = [],
  children,
}: {
  currentMapLayerOptions: string[];
  children: React.ReactNode;
}) => {
  // Determine badge text and color based on layer options
  let badgeText;
  let color;

  const bfrClusterSelected = currentMapLayerOptions.includes('bfr cluster');
  const btrPipelineSelected =
    currentMapLayerOptions.includes('btr-pipeline-ld');

  // Count how many of these specific layers are selected
  const selectedLayerCount = [bfrClusterSelected, btrPipelineSelected].filter(
    Boolean,
  ).length;

  if (selectedLayerCount > 0) {
    // If any layers are selected, show the count with green color
    badgeText = selectedLayerCount.toString();
    color = 'green';
  } else {
    // If no layers are selected, show 'HOT' with red color
    badgeText = 'HOT';
    color = 'red';
  }

  // Determine colors based on the selected color
  let bgColorCode;
  let textColorCode;

  switch (color) {
    case 'red':
      bgColorCode = '#D30000';
      textColorCode = '#fff';
      break;
    case 'green':
      bgColorCode = '#00D300';
      textColorCode = '#fff';
      break;
    default:
      bgColorCode = '#D30000';
      textColorCode = '#fff';
      break;
  }

  return (
    <Badge
      count={
        <span
          style={{
            backgroundColor: bgColorCode,
            color: textColorCode,
            padding: '2px 6px',
            borderRadius: '8px',
            fontSize: '10px',
            fontWeight: 'bold',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
          }}
        >
          {badgeText}
        </span>
      }
      offset={[15, 0]}
    >
      {children}
    </Badge>
  );
};

export default BadgeWrapper;
