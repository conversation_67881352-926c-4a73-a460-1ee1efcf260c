import { getClientInformation } from '@/utils/userGroup';
import { Col, Dropdown, Row } from 'antd';
import { MenuProps } from 'antd/lib/menu';
import moment from 'moment';
import React, { PureComponent } from 'react';
import { connect } from 'umi';
import AddressSearch from './AddressSearch';
import AppShortcuts from './AppShortcuts/AppShortcuts';
import BookmarksDropdown from './Bookmarks/BookmarksDropDown';
import CluesoLink from './CluesoLink';
import styles from './header.css';
const dateFormat = 'YYYY-MM-DD';

const getGeofenceParamForGeocodingAPI = (userGroup: any) => {
  switch (true) {
    // case userGroup.includes('demo-CMA-DFW-only'):
    //   return 'DFW';
    case userGroup.includes('demo-CMA-Charlotte-only'):
      return 'Charlotte';
    default:
      return '';
  }
};

interface CMAState {
  eventCoordinates: any;
  searchingMode: any;
  subjectPropertyParcelData: any;
  currentRadiusMile: number;
  currentStatusMLS: string;
  currentStartMLS: string;
  currentEndMLS: string;
  expDateFilterOn: boolean;
  userGroup: string;
  userEmail: string;
  scorecardModalOpen: boolean;
  drawnCustomPolygons: any[];
  currentPropertyAddress: string;
  map: any;
  showAVM: boolean;
  selectedFundriseCommunity?: any; // Add this new state property
}

interface HeaderProps {
  eventCoordinates: CMAState['eventCoordinates'];
  subjectPropertyParcelData: CMAState['subjectPropertyParcelData'];
  searchingMode: CMAState['searchingMode'];
  currentRadiusMile: CMAState['currentRadiusMile'];
  currentStatusMLS: CMAState['currentStatusMLS'];
  currentStartMLS: CMAState['currentStartMLS'];
  currentEndMLS: CMAState['currentEndMLS'];
  expDateFilterOn: CMAState['expDateFilterOn'];
  userGroup: CMAState['userGroup'];
  userEmail: CMAState['userEmail'];
  scorecardModalOpen: CMAState['scorecardModalOpen'];
  drawnCustomPolygons: CMAState['drawnCustomPolygons'];
  currentPropertyAddress: CMAState['currentPropertyAddress'];
  map: CMAState['map'];
  showAVM: CMAState['showAVM'];
  selectedFundriseCommunity?: CMAState['selectedFundriseCommunity']; // Add this new prop
  dispatch: (action: any) => void;
  signOut: () => void;
}

class Header extends PureComponent<HeaderProps> {
  // Add event listener when component mounts
  componentDidMount() {
    window.addEventListener('message', this.handleFundriseCommunitySelection);
    document.addEventListener(
      'FUNDRISE_COMMUNITY_SELECTED',
      this.handleFundriseCommunityEvent,
    );
  }

  // Remove event listener when component unmounts
  componentWillUnmount() {
    window.removeEventListener(
      'message',
      this.handleFundriseCommunitySelection,
    );
    document.removeEventListener(
      'FUNDRISE_COMMUNITY_SELECTED',
      this.handleFundriseCommunityEvent,
    );
  }

  // Handle postMessage events
  handleFundriseCommunitySelection = (event: MessageEvent) => {
    if (event.data && event.data.type === 'FUNDRISE_COMMUNITY_SELECTED') {
      console.log('Selected community from postMessage:', event.data.data);

      // Save the selected community to state
      this.props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          selectedFundriseCommunity: event.data.data,
          compingMode: 'smartFilter',
        },
      });

      // If coordinates are available, center the map to the community
      if (event.data.data.coordinates && this.props.map) {
        this.props.map.flyTo({
          center: event.data.data.coordinates,
          zoom: 14,
        });
      }
    }
  };

  // Handle custom DOM events
  handleFundriseCommunityEvent = (event: Event) => {
    const customEvent = event as CustomEvent;
    if (customEvent.detail) {
      console.log('Selected community from custom event:', customEvent.detail);

      // First dispatch the search coordinates immediately
      this.props.dispatch({
        type: 'CMA/getAllPropertyData',
        payload: {
          mode: 'search coordinates',
          lng: customEvent.detail.coordinates[0],
          lat: customEvent.detail.coordinates[1],
          geofence: getGeofenceParamForGeocodingAPI(this.props.userGroup),
          status: this.props.currentStatusMLS,
          propertyType:
            this.props.searchingMode === 'Lease'
              ? 'Residential Lease'
              : 'Residential',
          startDate: moment(this.props.currentStartMLS).format(dateFormat),
          endDate: moment(this.props.currentEndMLS).format(dateFormat),
          distance: this.props.currentRadiusMile * 1609.34,
          exists: this.props.currentStatusMLS,
          expDateFilterOn: this.props.expDateFilterOn ? 'yes' : 'no',
        },
      });

      // Update coordinates immediately
      this.props.dispatch({
        type: 'CMA/saveCMAStates',
        payload: {
          eventCoordinates: [
            customEvent.detail.coordinates[0],
            customEvent.detail.coordinates[1],
          ],
          compingMode: 'smartFilter',
        },
      });

      // Wait 5 seconds before updating beds, baths, and square footage
      setTimeout(() => {
        let parcelData = this.props.subjectPropertyParcelData;
        const payloadMode = 'switch to smart filter';
        const payloadStatusMLS = 'Closed';
        this.props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            subjectPropertyParcelData: {
              ...parcelData,
              beds_count: Number(customEvent.detail.bedrooms),
              baths: Number(customEvent.detail.bathrooms),
              total_area_sq_ft: Number(customEvent.detail.livingAreaSqft),
            },
          },
        });

        this.props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            compingMode: 'smartFilter',
            currentStatusMLS: payloadStatusMLS,
            currentStartMLS: moment().subtract(90, 'days').format(dateFormat),
            currentEndMLS: moment().format(dateFormat),
          },
        });
        this.props.dispatch({
          type: 'CMA/getAllPropertyData',
          payload: {
            mode: payloadMode,
            lng: this.props.eventCoordinates[0],
            lat: this.props.eventCoordinates[1],
            status: payloadStatusMLS || this.props.currentStatusMLS,
            propertyType:
              this.props.searchingMode === 'Lease'
                ? 'Residential Lease'
                : 'Residential',
            exists: payloadStatusMLS || this.props.currentStatusMLS,
            expDateFilterOn: this.props.expDateFilterOn ? 'yes' : 'no',
          },
        });
        console.log('Updated property details after 5 seconds:', {
          beds: Number(customEvent.detail.bedrooms),
          baths: Number(customEvent.detail.bathrooms),
          sqft: Number(customEvent.detail.livingAreaSqft),
        });
      }, 1500); // 5000 milliseconds = 5 seconds
    }
  };
  generateUserMenuItems = () => {
    let menuItems: MenuProps['items'] = [
      {
        label: this.props.userEmail,
        key: 'userEmail',
      },
      {
        type: 'divider',
      },
      {
        label: <CluesoLink />,
        key: 'Help',
      },
      {
        type: 'divider',
      },
      {
        label: 'Sign Out',
        key: 'Sign Out',
      },
    ];

    // add a show/hide AVM button for test.locatealpha.com and localhost
    if (
      ['test.locatealpha.com', 'localhost'].includes(window.location.hostname)
    ) {
      // insert a divider and show/hide AVM button
      menuItems.splice(
        1,
        0,
        { type: 'divider' },
        {
          label: this.props.showAVM ? 'Hide AVM' : 'Show AVM',
          key: 'Show/Hide AVM',
        },
      );
    }

    return menuItems;
  };

  onClickUserMenuItems = ({ key }: { key: string }) => {
    switch (key) {
      case 'Sign Out':
        this.props.signOut();
        break;
      case 'Show/Hide AVM':
        this.props.dispatch({
          type: 'CMA/saveCMAStates',
          payload: {
            showAVM: !this.props.showAVM,
          },
        });
        break;
      default:
        break;
    }
  };

  render() {
    const clientInfo = getClientInformation(this.props.userGroup);

    return (
      <Row
        key="header"
        justify="start"
        className={`${styles.headerWrapper} border-b border-[#ddd]`}
      >
        {clientInfo.clientName === 'ILE' && (
          <Col
            key="logo wrapper"
            flex={clientInfo.flex}
            className={styles.logoWrapper}
          >
            <div style={{ height: 36 }} />
          </Col>
        )}
        {clientInfo.clientName !== 'ILE' && (
          <Col
            key="logo wrapper"
            flex={clientInfo.flex}
            className={styles.logoWrapper}
          >
            {clientInfo.clientName === 'AlliedDev' && (
              <img src={clientInfo.png} className={styles.logo_AlliedDev} />
            )}
            {clientInfo.clientName === 'AMH' && (
              <div className={styles.logo_AMH}>{clientInfo.svg}</div>
            )}
            {clientInfo.clientName === 'Arabella' && (
              <img src={clientInfo.png} className={styles.logo_Arabella} />
            )}
            {clientInfo.clientName === 'ArkHomesForRent' && (
              <img
                src={clientInfo.png}
                className={styles.logo_ArkHomesForRent}
              />
            )}
            {clientInfo.clientName === 'BridgeTower' && (
              <img src={clientInfo.png} className={styles.logo_BridgeTower} />
            )}
            {clientInfo.clientName === 'Camillo' && (
              <img src={clientInfo.png} className={styles.logo_Camillo} />
            )}
            {clientInfo.clientName === 'CommonGroundCapital' && (
              <div className={styles.logo_CommonGroundCapital}>
                {clientInfo.svg}
              </div>
            )}
            {clientInfo.clientName === 'MarketplaceHomes' && (
              <div className={styles.logo_MarketplaceHomes}>
                {clientInfo.svg}
              </div>
            )}
            {clientInfo.clientName === 'MMG' && (
              <div className={styles.logo_MMG}>{clientInfo.svg}</div>
            )}
            {clientInfo.clientName === 'DivvyHomes' && (
              <div className={styles.logo_DivvyHomes}>{clientInfo.svg}</div>
            )}
            {clientInfo.clientName === 'Greystar' && (
              <img src={clientInfo.png} className={styles.logo_Greystar} />
            )}
            {clientInfo.clientName === 'Pathway' && (
              <div className={styles.logo_Pathway}>{clientInfo.svg}</div>
            )}
            {clientInfo.clientName === 'SecondAvenue' && (
              <div className={styles.logo_SecondAvenue}>{clientInfo.svg}</div>
            )}
            {clientInfo.clientName === 'SmithDouglas' && (
              <img src={clientInfo.png} className={styles.logo_SmithDouglas} />
            )}
            {clientInfo.clientName === 'UpAndUp' && (
              <img src={clientInfo.png} className={styles.logo_UpAndUp} />
            )}
            {clientInfo.clientName === 'USLegacy' && (
              <div className={styles.logo_USLegacy}>{clientInfo.svg}</div>
            )}
            {clientInfo.clientName === 'Avanta' && (
              <img src={clientInfo.png} className={styles.logo_Avanta} />
            )}
            {clientInfo.clientName === 'Bridge' && (
              <img src={clientInfo.png} className={styles.logo_Bridge} />
            )}
            {clientInfo.clientName === 'Nhimble' && (
              <img src={clientInfo.png} className={styles.logo_Nhimble} />
            )}
            {clientInfo.clientName === 'TrellyGroup' && (
              <img src={clientInfo.png} className={styles.logo_TrellyGroup} />
            )}
            {clientInfo.clientName === 'HON' && (
              <img src={clientInfo.png} className={styles.logo_HON} />
            )}
            {clientInfo.clientName === 'ILE' && (
              <div className={styles.logo_ILE_placeholder} />
            )}
            {clientInfo.clientName === 'InvitationHomes' && (
              <div className={styles.logo_InvitationHomes}>
                {clientInfo.svg}
              </div>
            )}
            {clientInfo.clientName === 'Evergreen' && (
              <img src={clientInfo.png} className={styles.logo_Evergreen} />
            )}
            {clientInfo.clientName === 'Kairos' && (
              <img src={clientInfo.png} className={styles.logo_Kairos} />
            )}
            {clientInfo.clientName === 'VentureREI' && (
              <img src={clientInfo.png} className={styles.logo_VentureREI} />
            )}
            {clientInfo.clientName === 'SVN' && (
              <div className={styles.logo_SVN}>{clientInfo.svg}</div>
            )}
            {clientInfo.clientName === 'Darwin' && (
              <div className={styles.logo_Darwin}>{clientInfo.svg}</div>
            )}
            {clientInfo.clientName === 'GEMRC' && (
              <img src={clientInfo.png} className={styles.logo_GEMRC} />
            )}
            {clientInfo.clientName === 'BlueRiver' && (
              <img src={clientInfo.png} className={styles.logo_BlueRiver} />
            )}
            {clientInfo.clientName === 'Truehold' && (
              <div className={styles.logo_Truehold}>{clientInfo.svg}</div>
            )}
            {clientInfo.clientName === 'Fundrise' && (
              <div className={styles.logo_Fundrise}>{clientInfo.svg}</div>
            )}
            {clientInfo.clientName === 'JLL' && (
              <div className={styles.logo_JLL}>{clientInfo.svg}</div>
            )}
            {clientInfo.clientName === 'Sunroom' && (
              <div className={styles.logo_Sunroom}>{clientInfo.svg}</div>
            )}
            {clientInfo.clientName === 'DRHorton' && (
              <div className={styles.logo_DRHorton}>{clientInfo.svg}</div>
            )}
            {clientInfo.clientName === 'Homebound' && (
              <div className={styles.logo_Homebound}>{clientInfo.svg}</div>
            )}
            {clientInfo.clientName === 'Lennar' && (
              <div className={styles.logo_Lennar}>{clientInfo.svg}</div>
            )}
            {clientInfo.clientName === 'LedgerTC' && (
              <div className={styles.logo_LedgerTC}>{clientInfo.svg}</div>
            )}
            {clientInfo.clientName === 'Rithm' && (
              <div className={styles.logo_Rithm}>{clientInfo.svg}</div>
            )}
            {clientInfo.clientName === 'Tricon' && (
              <div className={styles.logo_Tricon}>{clientInfo.svg}</div>
            )}
            {clientInfo.clientName === 'UrbanRowGroup' && (
              <img src={clientInfo.png} className={styles.logo_UrbanRowGroup} />
            )}
            {clientInfo.clientName === 'Embry' && (
              <img src={clientInfo.png} className={styles.logo_Embry} />
            )}
            {clientInfo.clientName === 'Hawkhill' && (
              <div className={styles.logo_Hawkhill}>{clientInfo.svg}</div>
            )}
            {clientInfo.clientName === 'GreatGulf' && (
              <div className={styles.logo_GreatGulf}>{clientInfo.svg}</div>
            )}
            {clientInfo.clientName === 'Vertica' && (
              <img src={clientInfo.png} className={styles.logo_Vertica} />
            )}
            {clientInfo.clientName === 'JCGLand' && (
              <img src={clientInfo.png} className={styles.logo_JCGLand} />
            )}
            {clientInfo.clientName === 'GACapital' && (
              <img src={clientInfo.png} className={styles.logo_GACapital} />
            )}
            {clientInfo.clientName === 'BeaconRidge' && (
              <img src={clientInfo.png} className={styles.logo_BeaconRidge} />
            )}
            {clientInfo.clientName === 'HunterQuinn' && (
              <img src={clientInfo.png} className={styles.logo_HunterQuinn} />
            )}
            {clientInfo.clientName === 'AveOne' && (
              <img src={clientInfo.png} className={styles.logo_AveOne} />
            )}
            {clientInfo.clientName === 'WebCity' && (
              <img src={clientInfo.png} className={styles.logo_WebCity} />
            )}
            {clientInfo.clientName === 'Heyday' && (
              <img src={clientInfo.png} className={styles.logo_Heyday} />
            )}

            {clientInfo.clientName === 'RealCo' && (
              <img src={clientInfo.png} className={styles.logo_LocateAlpha} />
            )}
            <div key="divider" className={styles.logoDivider} />
            <div key="CMA name wrapper">
              <div key="CMA name line 1" className={styles.logoTitle}>
                Comparative
              </div>
              <div key="CMA name line 2" className={styles.logoTitle}>
                Market Analysis
              </div>
            </div>
            <div key="divider 2" className={styles.logoDividerInvisible} />
            <AppShortcuts />
          </Col>
        )}
        <Col
          key="search wrapper"
          flex="auto"
          className={styles.searchWrapper}
          id="headerSearchWrapper"
        >
          <AddressSearch key="address search" />
          <div key="divider" className={styles.logoDivider} />
          <button
            key="import kml button"
            className={styles.KMLButton}
            onClick={() => {
              this.props.dispatch({
                type: 'CMA/saveCMAStates',
                payload: {
                  showImportKMLModal: true,
                },
              });
            }}
          >
            Import KML
          </button>
        </Col>

        <Col
          key="bookmarks button wrapper"
          flex={'160px'}
          className={styles.batchProcessWrapper}
        >
          <BookmarksDropdown />
          <Dropdown
            menu={{
              items: this.generateUserMenuItems(),
              onClick: this.onClickUserMenuItems,
            }}
          >
            <button className={styles.header_right_button_user}>
              {this.props.userEmail.charAt(0).toUpperCase()}
            </button>
          </Dropdown>
        </Col>
      </Row>
    );
  }
}

export default connect(({ CMA }: { CMA: CMAState }) => ({
  eventCoordinates: CMA.eventCoordinates,
  subjectPropertyParcelData: CMA.subjectPropertyParcelData,
  searchingMode: CMA.searchingMode,
  currentRadiusMile: CMA.currentRadiusMile,
  currentStatusMLS: CMA.currentStatusMLS,
  currentStartMLS: CMA.currentStartMLS,
  currentEndMLS: CMA.currentEndMLS,
  expDateFilterOn: CMA.expDateFilterOn,
  userGroup: CMA.userGroup,
  userEmail: CMA.userEmail,
  scorecardModalOpen: CMA.scorecardModalOpen,
  drawnCustomPolygons: CMA.drawnCustomPolygons,
  currentPropertyAddress: CMA.currentPropertyAddress,
  map: CMA.map,
  showAVM: CMA.showAVM,
  selectedFundriseCommunity: CMA.selectedFundriseCommunity, // Add this to the connected props
}))(Header);
