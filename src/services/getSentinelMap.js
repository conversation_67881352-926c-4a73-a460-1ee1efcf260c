import axios from 'axios';
import moment from 'moment';

// get access token every time the site loads
// because token expires in 60 minutes
const client_id = '876805b7-7f17-403f-9bed-9ab592f5a3ed';
const client_secret = encodeURIComponent(
  `:B-pUj1uL5*ZqyDA^bvi6S9UdjmYLHd&t3Rhk9NU`,
); // need encodeURIComponent because it's sent as a query in url and it needs to escape characters

// body must match content-type set in header
// x-www-form-urlencoded is the format used in url qurey
const requestBodyNewToken = `client_id=${client_id}&client_secret=${client_secret}&grant_type=client_credentials`;

const instance = axios.create({
  baseURL: 'https://services.sentinel-hub.com',
});

const config = {
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8',
  },
};

// All requests using this instance will have an access token automatically added
instance.post('/oauth/token', requestBodyNewToken, config).then((resp) => {
  console.log('new token requested');
  Object.assign(instance.defaults, {
    headers: { authorization: `Bearer ${resp.data.access_token}` },
  });
});

// fetch satellite image
export const getSentinelLayer = (bboxLngLat, width, height) => {
  const dateFormat = 'YYYY-MM-DDThh:mm:ssZ';
  const requestBodyProcessingAPI = {
    input: {
      bounds: {
        bbox: bboxLngLat,
      },
      data: [
        {
          dataFilter: {
            timeRange: {
              // from: '2022-09-14T00:00:00Z', // change to moment
              // to: '2022-10-14T23:59:59Z',
              from: moment()
                .subtract(30, 'days')
                .startOf('day')
                .format(dateFormat),
              to: moment().endOf('day').format(dateFormat),
            },
            maxCloudCoverage: 10,
          },
          type: 'sentinel-2-l2a',
        },
      ],
    },
    output: {
      width: width,
      height: height,
      responses: [
        {
          identifier: 'default',
          format: {
            type: 'image/jpeg',
          },
        },
      ],
    },
    evalscript:
      '//VERSION=3\n\nfunction setup() {\n  return {\n    input: ["B02", "B03", "B04"],\n    output: { bands: 3 }\n  };\n}\n\nfunction evaluatePixel(sample) {\n  return [2.5 * sample.B04, 2.5 * sample.B03, 2.5 * sample.B02];\n}',
  };

  return instance.post('/api/v1/process', requestBodyProcessingAPI, {
    responseType: 'blob',
  });
};
