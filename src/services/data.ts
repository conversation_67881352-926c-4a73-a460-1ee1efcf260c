import { getUserToken } from '@/utils/auth';
import { MAPBOX_TOKEN } from '../constants';
import {
  getStatusHotPadsFromStatusMLS,
  getStatusSFRFromStatusMLS,
} from '../utils/getStatusSFRFromStatusMLS';
import { customRequest, request, requestPlacekey } from '../utils/request';

type ServerType = 'exp' | 'prod';

// DO NOT MODIFY serverType and MLSServerType HERE and push to remote branches.
// See .env file for development and live production, see amplify.yml
export const serverType: ServerType = process.env
  .UMI_APP_SERVER_TYPE as ServerType;
const MLSServerType: 'mlsTest' | 'mls/prod' =
  process.env.UMI_APP_SERVER_TYPE === 'prod' ? 'mls/prod' : 'mlsTest';

export const tileURLRoot = (serverId: string, serverType: string) => {
  if (serverId === 'cma') {
    return serverType === 'exp'
      ? 'https://v72b1ngqw2.execute-api.us-east-1.amazonaws.com/expriment'
      : 'https://wkgcer251f.execute-api.us-east-1.amazonaws.com/Prod';
  } else if (serverId === 'sbs') {
    return serverType === 'exp'
      ? 'https://edo14g2g8a.execute-api.us-east-1.amazonaws.com/test'
      : 'https://745tabhi4e.execute-api.us-east-1.amazonaws.com/prod';
  }
};

const generateParamsForGeofence = (geofence: any) => {
  switch (geofence) {
    case 'DFW':
      return `&bbox=-98.36250660282381%2C32.17494189615582%2C-95.87561207161805%2C33.838764143245825`;
    case 'Charlotte':
      return `&bbox=-81.430664%2C34.888184%2C-80.123978%2C35.414236`;
    default:
      return ``;
  }
};

const generateParamsForGetNearestSubdivisions = (params: any) => {
  let paramsString = '';
  for (const property in params) {
    if (params[property]) {
      paramsString += `&${property}=${params[property]}`;
    }
  }
  return paramsString;
};

export const getMLSPropertiesWithinRadiusData = async ({
  constructionType = 'ALL',
  ...params
}: {
  status: string;
  propertyType: string;
  startDate: string;
  endDate: string;
  lng: number;
  lat: number;
  distance: number;
  constructionType?: 'ALL' | 'NEW' | 'EXISTING';
}) => {
  return await customRequest(
    `/api/cma/${serverType}/mls?status=${params.status}&propertyType=${params.propertyType}&startDate=${params.startDate}&endDate=${params.endDate}&lng=${params.lng}&lat=${params.lat}&distance=${params.distance}&constructionType=${constructionType}`,
    {
      method: 'GET',
    },
  );
};

export const getNationalOperatorsPropertiesWithinRadiusData = async (
  params: any,
) => {
  return await customRequest(
    `/api/cma/${serverType}/realestate?exists=${getStatusSFRFromStatusMLS(
      params.exists,
    )}&startDate=${params.startDate}&endDate=${params.endDate}&lng=${
      params.lng
    }&lat=${params.lat}&distance=${params.distance}`,
    {
      method: 'GET',
    },
  );
};

export const getHotPadsData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/realestate/hotpads?exists=${getStatusHotPadsFromStatusMLS(
      params.exists,
    )}&startDate=${params.startDate}&endDate=${params.endDate}&lng=${
      params.lng
    }&lat=${params.lat}&distance=${params.distance}`,
    {
      method: 'GET',
    },
  );
};

export const getMultiFamilyPropertiesWithinRadiusData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/multifamily?lng=${params.lng}&lat=${params.lat}&distance=${params.distance}`,
    {
      method: 'GET',
    },
  );
};

export const getPadSplitData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/realestate/padsplit-current?lng=${params.lng}&lat=${params.lat}&distance=${params.distance}`,
    {
      method: 'GET',
    },
  );
};

// always use /prod for /btfolio
export const getBTOwnedPropertiesWithinRadiusData = async (params: any) => {
  return await customRequest(
    `/api/acq/prod/btfolio?lng=${params.lng}&lat=${params.lat}&distance=${params.distance}&days=${params.expDateFilterOn}`,
    {
      method: 'GET',
    },
  );
};

export const getParcelMapScopeData = async (params: any) => {
  // ATTN: lng/lat switched
  return await customRequest(
    `/api/cma/${serverType}/parcel/square?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    {
      method: 'GET',
    },
  );
};

export const getParcelBoundaryMapScopeData = async (params: any) => {
  // ATTN: lng/lat switched
  return await customRequest(
    `/api/cma/${serverType}/boundary/square?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    {
      method: 'GET',
    },
  );
};

export const getDistrictMapScopeData = async (params: any) => {
  // ATTN: lng/lat switched
  return await customRequest(
    `/api/cma/${serverType}/district/square?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    {
      method: 'GET',
    },
  );
};

export const getActivityCenterMapScopeData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/brookings?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    {
      method: 'GET',
    },
  );
};

export const getMapboxReverseGeocodingData = async (params: any) => {
  return await request(
    `https://api.mapbox.com/geocoding/v5/mapbox.places/${params.lng},${
      params.lat
    }.json?access_token=pk.eyJ1Ijoic3hieGNoZW4iLCJhIjoiYjRhNWMyMmI0NzVjZjEzZjYyZGUzZDM0NmFhZTcyNjEifQ.-T2S1ZeAEBGxjC4rC0CZzA${generateParamsForGeofence(
      params.geofence,
    )}`,
    {
      method: 'GET',
    },
  );
};

export const getMapboxForwardGeocodingData = async (params: any) => {
  // https://docs.mapbox.com/playground/geocoding/
  return await request(
    `https://api.mapbox.com/geocoding/v5/mapbox.places/${
      params.searchText
    }.json?country=us%2Cpr&types=address%2Cpostcode%2Cplace&autocomplete=true&fuzzyMatch=true&access_token=pk.eyJ1Ijoic3hieGNoZW4iLCJhIjoiYjRhNWMyMmI0NzVjZjEzZjYyZGUzZDM0NmFhZTcyNjEifQ.-T2S1ZeAEBGxjC4rC0CZzA${generateParamsForGeofence(
      params.geofence,
    )}`,
    {
      method: 'GET',
    },
  );
};

export const getGoogleForwardGeocodingData = async (params: any) => {
  const address = params.searchText.replace(/ /g, '+');
  return await request(
    `https://maps.googleapis.com/maps/api/geocode/json?address=${address}&key=AIzaSyCjNoMR5ZTHEyXyqOdMrFWKWOCbE_JfynQ`,
    { method: 'GET' },
  );
};

export const getMapboxIsochroneData = async (params: any) => {
  return await request(
    // `https://api.mapbox.com/isochrone/v1/mapbox/${params.profile}/${params.lng},${params.lat}?contours_minutes=${params.minutes}&access_token=${MAPBOX_TOKEN}`,
    `https://api.mapbox.com/isochrone/v1/mapbox/${params.profile}/${params.lng},${params.lat}?contours_minutes=${params.minutes}&polygons=true&access_token=${MAPBOX_TOKEN}`,
    { method: 'GET' },
  );
};

export const getSingleParcelWithPlacekeyData = async (params: any) => {
  let url = `/api/cma/${serverType}/parcel?placekey=${params.placekey}&lat=${params.lat}&lng=${params.lng}`;

  if (params.streetNum) {
    url = `${url}&streetnum=${params.streetNum}`;
  }

  return await customRequest(url, {
    method: 'GET',
  });
};

export const getPlacekeyData = async (params: any) => {
  return await requestPlacekey.post('', {
    data: {
      query: {
        street_address: params.streetAddress,
        city: params.city,
        postal_code: params.postalCode,
        latitude: params.latitude,
        longitude: params.longitude,
        iso_country_code: 'US',
        region: params.region,
      },
    },
  });
};

export const getUserBatchProcessingRecordData = async (params: any) => {
  return await customRequest(`/api/cma/${serverType}/batch/files`, {
    method: 'GET',
  });
};

export const postBatchProcessingFileData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/batch/bulk?filename=${params.filename}&status=${params.status}`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};

export const getBatchProcessingProgressData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/batch/file?id=${params.id}`,
    {
      method: 'GET',
    },
  );
};

export const getBatchMLSPropertyImagesData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/batch/img?key=${params.key}&city=${params.city}`,
    {
      method: 'GET',
    },
  );
};

export const getNearestSubdivisionsData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/subdivision/search?${generateParamsForGetNearestSubdivisions(
      params,
    )}`,
    {
      method: 'GET',
    },
  );
};

export const getParcelOwnerSummaryData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/mls/summary?lng=${params.lng}&lat=${params.lat}&distance=${params.distance}`,
    {
      method: 'GET',
    },
  );
};

export const getScorecardData = async (params: any) => {
  return await customRequest(`/api/cma/${serverType}/scorecard`, {
    method: 'POST',
    data: params.body,
  });
};

export const getScorecardDataFetch = async (params: any) => {
  // let url = 'http://ec2-3-235-170-15.compute-1.amazonaws.com:8080';
  // let url = 'https://v72b1ngqw2.execute-api.us-east-1.amazonaws.com/expriment';
  // let url = 'https://api.locatealpha.com/cma/exp';
  // if (serverType === 'prod') {
  // url = 'http://ec2-44-222-3-252.compute-1.amazonaws.com:8080';
  const url =
    serverType == 'prod'
      ? 'https://wkgcer251f.execute-api.us-east-1.amazonaws.com/Prod'
      : 'https://v72b1ngqw2.execute-api.us-east-1.amazonaws.com/expriment';
  // }
  return await fetch(`${url}/scorecard`, {
    headers: { Authorization: `Bearer ${await getUserToken('access')}` },
    method: 'POST',
    body: params.body,
  });
};

export const submitBatchScorecardData = async (params: any) => {
  return await customRequest(`/api/cma/${serverType}/scorecard/batch`, {
    method: 'POST',
    data: params.body,
  });
};

export const getScorecardProgressData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/scorecard/file?id=${params.id}`,
    {
      method: 'GET',
    },
  );
};

export const getUserScorecardRecordData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/scorecard/files?find=${params.find}`,
    {
      method: 'GET',
    },
  );
};

export const getCountyData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/county?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    {
      method: 'GET',
    },
  );
};

export const getZipCodeData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/zipcode?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    {
      method: 'GET',
    },
  );
};

export const getStrategyOpportunityZoneData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/strategy-opportunity?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    {
      method: 'GET',
    },
  );
};

export const getAttendanceZoneData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/attendance/square?category=${params.category}&lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    { method: 'GET' },
  );
};

export const getCBSAData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/cbsa?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    { method: 'GET' },
  );
};

export const getFloodZoneData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/flood-zone?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    { method: 'GET' },
  );
};

export const getParcelOwnerSummaryWithinPolygonsData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/mls/summary/multipolygon`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};

export const getBTOwnedPropertiesWithinPolygonsData = async (params: any) => {
  return await customRequest(
    `/api/acq/${serverType}/btfolio/multipolygon?days=${params.expDateFilterOn}`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};

export const getNationalOperatorsPropertiesWithinPolygonsData = async (
  params: any,
) => {
  return await customRequest(
    `/api/cma/${serverType}/realestate/multipolygon?exists=${getStatusSFRFromStatusMLS(
      params.exists,
    )}&startDate=${params.startDate}&endDate=${params.endDate}`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};

export const getHotPadsDataWithinPolygonsData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/realestate/hotpads/multipolygon?exists=${getStatusHotPadsFromStatusMLS(
      params.exists,
    )}&startDate=${params.startDate}&endDate=${params.endDate}`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};

export const getMLSPropertiesWithinPolygonsData = async ({
  constructionType = 'ALL',
  ...params
}: {
  status: string;
  propertyType: string;
  startDate: string;
  endDate: string;
  body: Array<any>;
  distance: number;
  constructionType?: 'ALL' | 'NEW' | 'EXISTING';
}) => {
  return await customRequest(
    `/api/cma/${serverType}/mls/multipolygon?status=${params.status}&propertyType=${params.propertyType}&startDate=${params.startDate}&endDate=${params.endDate}&constructionType=${constructionType}`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};

export const getMultiFamilyPropertiesWithinPolygonsData = async (
  params: any,
) => {
  return await customRequest(
    `/api/cma/${serverType}/multifamily/multipolygon`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};

// New House AVM only has PROD - NOT USED
export const getNewHouseAVMRentData = async (params: any) => {
  return await request(
    `/api/newhouseavm/prod/parcel/rent?lat=${params.lat}&lng=${params.lng}&radius=${params.radius}&baths=${params.baths}&beds=${params.beds}&sqft=${params.sqft}`,
    // `http://ec2-35-171-3-127.compute-1.amazonaws.com:8060/parcel/rent?lat=${params.lat}&lng=${params.lng}&radius=${params.radius}&baths=${params.baths}&beds=${params.beds}&sqft=${params.sqft}`,
    { method: 'GET' },
  );
};

// New House AVM only has PROD
export const getNewHouseAVMRentMLData = async (params: any) => {
  return await request(
    // `/api/newhouseavm/ml/prod/parcel/atlanta/rent?lat=${params.lat}&lng=${params.lng}&beds=${params.beds}&baths=${params.baths}&yearbuilt=${params.yearbuilt}&sqft=${params.sqft}`,
    `/api/newhouseavm/ml/prod/parcel/rent?metro=${params.metro}&beds=${params.beds}&yearbuilt=${params.yearbuilt}&lat=${params.lat}&lng=${params.lng}&baths=${params.baths}&sqft=${params.sqft}`,
    { method: 'GET' },
  );
};

// New House AVM only has PROD
export const getNewHouseAVMSalesMLData = async (params: any) => {
  return await request(
    // `/api/newhouseavm/ml/prod/parcel/atlanta/sales?lat=${params.lat}&lng=${params.lng}&beds=${params.beds}&baths=${params.baths}&yearbuilt=${params.yearbuilt}&sqft=${params.sqft}`,
    `/api/newhouseavm/ml/prod/parcel/sales?metro=${params.metro}&beds=${params.beds}&yearbuilt=${params.yearbuilt}&lat=${params.lat}&lng=${params.lng}&baths=${params.baths}&sqft=${params.sqft}`,
    { method: 'GET' },
  );
};

export const getMobileData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/mobiledata?polygon_id=${params.polygon_id}&lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    { method: 'GET' },
  );
};

export const getMLSListingSummaryWithinSphereData = async (params: {
  propertyType: string;
  year: number;
  month: number;
  numberOfMonths: number;
  lng: number;
  lat: number;
  distance: number;
  // signal: AbortSignal;
}) => {
  return await customRequest(
    // `/api/cma/${serverType}/mls-listing-summary?propertyType=${params.propertyType}&startDate=${params.startDate}&endDate=${params.endDate}&lng=${params.lng}&lat=${params.lat}&distance=${params.distance}`,
    `/api/cma/${serverType}/mls-listing-summary?propertyType=${params.propertyType}&year=${params.year}&month=${params.month}&numberOfMonths=${params.numberOfMonths}&lng=${params.lng}&lat=${params.lat}&distance=${params.distance}`,
    {
      method: 'GET',
      // signal: params.signal
    },
  );
};

export const getMLSListingSummaryWithinPolygonData = async (params: {
  propertyType: string;
  year: number;
  month: number;
  numberOfMonths: number;
  body: any;
}) => {
  return await customRequest(
    // `/api/cma/${serverType}/mls-listing-summary/multipolygon?propertyType=${params.propertyType}&startDate=${params.startDate}&endDate=${params.endDate}`,
    `/api/cma/${serverType}/mls-listing-summary/multipolygon?propertyType=${params.propertyType}&year=${params.year}&month=${params.month}&numberOfMonths=${params.numberOfMonths}`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};

export const getMLSListingSummaryWithinZIPCodeData = async (params: any) => {
  return await customRequest(
    // `/api/cma/${serverType}/mls-listing-summary/zipcode?propertyType=${params.propertyType}&startDate=${params.startDate}&endDate=${params.endDate}&lng=${params.lng}&lat=${params.lat}`,
    `/api/cma/${serverType}/mls-listing-summary/zipcode?propertyType=${params.propertyType}&year=${params.year}&month=${params.month}&numberOfMonths=${params.numberOfMonths}&lng=${params.lng}&lat=${params.lat}`,
    {
      method: 'GET', //signal: params.signal
    },
  );
};

export const getMLSListingSummaryWithinMetroData = async (params: any) => {
  return await customRequest(
    // `/api/cma/${serverType}/mls-listing-summary/metro?propertyType=${params.propertyType}&startDate=${params.startDate}&endDate=${params.endDate}&lng=${params.lng}&lat=${params.lat}`,
    `/api/cma/${serverType}/mls-listing-summary/metro?propertyType=${params.propertyType}&year=${params.year}&month=${params.month}&numberOfMonths=${params.numberOfMonths}&lng=${params.lng}&lat=${params.lat}`,
    {
      method: 'GET', //signal: params.signal
    },
  );
};

export const getMLSListingSummaryWithinDistrictData = async (params: any) => {
  return await customRequest(
    // `/api/cma/${serverType}/mls-listing-summary/school-district?propertyType=${params.propertyType}&startDate=${params.startDate}&endDate=${params.endDate}&lng=${params.lng}&lat=${params.lat}`,
    `/api/cma/${serverType}/mls-listing-summary/school-district?propertyType=${params.propertyType}&year=${params.year}&month=${params.month}&numberOfMonths=${params.numberOfMonths}&lng=${params.lng}&lat=${params.lat}`,
    {
      method: 'GET', //signal: params.signal
    },
  );
};

export const getMLSListingSummaryWithinCountyData = async (params: any) => {
  return await customRequest(
    // `/api/cma/${serverType}/mls-listing-summary/county?propertyType=${params.propertyType}&startDate=${params.startDate}&endDate=${params.endDate}&lng=${params.lng}&lat=${params.lat}`,
    `/api/cma/${serverType}/mls-listing-summary/county?propertyType=${params.propertyType}&year=${params.year}&month=${params.month}&numberOfMonths=${params.numberOfMonths}&lng=${params.lng}&lat=${params.lat}`,
    { method: 'GET' },
  );
};

export const getMLSListingSummaryPointWithinLayerData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/mls-listing-summary/point-within-data-layer?lng=${params.lng}&lat=${params.lat}`,
    { method: 'GET' },
  );
};

export const getScorecardAnalyticsByDateData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/scorecards-analytics/date?start_date=${params.startDate}&end_date=${params.endDate}`,
    { method: 'GET' },
  );
};

export const getScorecardAnalyticsByDevManagerData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/scorecards-analytics/development-manager?development_manager=${params.devManager}`,
    { method: 'GET' },
  );
};

export const getScorecardAnalyticsByConsultantData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/scorecards-analytics/consultant?consultant=${params.consultant}`,
    { method: 'GET' },
  );
};

export const getScorecardAnalyticsBySubmarketData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/scorecards-analytics/submarket?submarket=${params.submarket}`,
    { method: 'GET' },
  );
};

export const getScorecardAnalyticsByCountyData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/scorecards-analytics/county?county=${params.county}`,
    { method: 'GET' },
  );
};

export const getZIPCodeBySearchData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/zipcode/search?zipcode=${params.zipcode}`,
    { method: 'GET' },
  );
};

export const getMarketRentCompareData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/market-rent-compare?zipcode=${params.zipcode}`,
    // `http://localhost:8080/market-rent-compare?zipcode=${params.zipcode}`,
    // { method: 'GET' },
    { method: 'GET', signal: params.signal },
  );
};

export const getAdjustedRentAndSaleData = async (params: any) => {
  return await customRequest(
    `/api/acq/${serverType}/adjusted?preference=${params.preference}`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};

export const getNewBuildsWithinRadiusData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/real-estate-house-new-home?status=${params.status}&builder=${params.builder}&min_price=${params.minPrice}&max_price=${params.maxPrice}&min_bed_rooms=${params.minBed}&max_bed_rooms=${params.maxBed}&min_bath_rooms=${params.minBath}&max_bath_rooms=${params.maxBath}&min_square_feet=${params.minSqft}&max_square_feet=${params.maxSqft}&min_first_seen_date=${params.minFirstSeenDate}&max_first_seen_date=${params.maxFirstSeenDate}&lng=${params.lng}&lat=${params.lat}&distance=${params.distance}&source=${params.source}`,
    { method: 'GET' },
  );
};

export const getNewBuildsWithinPolygonData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/real-estate-house-new-home/multipolygon?status=${params.status}&builder=${params.builder}&min_price=${params.minPrice}&max_price=${params.maxPrice}&min_bed_rooms=${params.minBed}&max_bed_rooms=${params.maxBed}&min_bath_rooms=${params.minBath}&max_bath_rooms=${params.maxBath}&min_square_feet=${params.minSqft}&max_square_feet=${params.maxSqft}&min_first_seen_date=${params.minFirstSeenDate}&max_first_seen_date=${params.maxFirstSeenDate}&source=${params.source}`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};

export const getHouseDetailsData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/unified-details?listing_id=${params.listingID}&city_code=${params.cityCode}`,
    { method: 'GET' },
  );
};

export const getAPNOwnerNameData = async (params: any) => {
  return await customRequest(
    `/api/${MLSServerType}/apn-owner-name?placekey=${params.placekey}`,
    { method: 'GET' },
  );
};

export const getBrokerOfficeInfoData = async (params: any) => {
  return await customRequest(
    `/api/${MLSServerType}/office-member-details?city_code=${params.cityCode}&mls_id=${params.mlsNumber}`,
    {
      method: 'GET',
    },
  );
};

export const MLSListingSummaryChartSphereData = async (params: any) => {
  console.log('HERE 0');
  return await customRequest(
    `/api/cma/${serverType}/mls-listing-summary/chart?propertyType=${params.propertyType}&lng=${params.lng}&lat=${params.lat}&distance=${params.distance}`,
    { method: 'GET' },
  );
};

export const MLSListingSummaryChartMultipolygonData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/mls-listing-summary/chart/multipolygon?propertyType=${params.propertyType}`,
    { method: 'POST', data: params.body },
  );
};

export const MLSListingSummaryChartZIPCodeData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/mls-listing-summary/chart/zipcode?propertyType=${params.propertyType}&lng=${params.lng}&lat=${params.lat}`,
    { method: 'GET' },
  );
};

export const MLSListingSummaryChartCBSAData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/mls-listing-summary/chart/metro?propertyType=${params.propertyType}&lng=${params.lng}&lat=${params.lat}`,
    { method: 'GET' },
  );
};

export const MLSListingSummaryChartSchoolDistrictData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/mls-listing-summary/chart/school-district?propertyType=${params.propertyType}&lng=${params.lng}&lat=${params.lat}`,
    { method: 'GET' },
  );
};

export const MLSListingSummaryChartCountyData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/mls-listing-summary/chart/county?propertyType=${params.propertyType}&lng=${params.lng}&lat=${params.lat}`,
    { method: 'GET' },
  );
};

export const getDemographicData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/demographic?lng=${params.lng}&lat=${params.lat}`,
    { method: 'GET' },
  );
};

// reworked off-market
export const sendPropertyToOffMarketData = async (params: any) => {
  return await customRequest(`/api/offMarket/exp/portfolios/custom`, {
    method: 'POST',
    data: params.body,
  });
};

export const getParcelRadiusData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/parcel/radius?lat=${params.lat}&lng=${params.lng}&radius=${params.radius}`,
    { method: 'GET' },
  );
};

export const getParcelUnitMixData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/parcel/mix?lat=${params.lat}&lng=${params.lng}&radius=${params.radius}`,
    { method: 'GET' },
  );
};

export const getParcelPolygonData = async (params: any) => {
  return await customRequest(`/api/cma/${serverType}/parcel/multipolygon`, {
    method: 'POST',
    data: params.body,
  });
};

export const getSingleMLSRecordData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/mls/single?placekey=${params.placekey}&lat=${params.lat}&lng=${params.lng}`,
    {
      method: 'GET',
    },
  );
};

export const saveCompsData = async (params: any) => {
  return await customRequest(`/api/cma/${serverType}/comps/v2`, {
    method: 'POST',
    data: params.body,
  });
};

export const getSavedMLSCompsData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/comps/v2/mls?placekey=${params.placekey}&lat=${params.lat}&lng=${params.lng}&stage=${params.stage}`,
    {
      method: 'GET',
    },
  );
};

export const getSavedSFRCompsData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/comps/v2/sfr?placekey=${params.placekey}&lat=${params.lat}&lng=${params.lng}&stage=${params.stage}`,
    {
      method: 'GET',
    },
  );
};

export const getLeaseExpiryAOIData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/insights-lease-expire?lat=${params.lat}&lng=${params.lng}&distance=${params.distance}`,
    { method: 'GET' },
  );
};

export const getLeaseExpiryAOIDataWithinPolygon = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/insights-lease-expire/multipolygon`,
    { method: 'POST', data: params.body },
  );
};

export const getLeaseExpiryAOIDataWithinZIPCode = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/insights-lease-expire/zipcode?lat=${params.lat}&lng=${params.lng}`,
    { method: 'GET' },
  );
};

export const getFilteredParcelWithinMetroData = async (params: any) => {
  let url = `/api/cma/${serverType}/parcel/cbsa?`;

  const paramKeys = Object.keys(params);

  for (let i = 0; i < paramKeys.length; i++) {
    const name = paramKeys[i];
    const value = params[name];
    url += `${name}=${value}`;

    if (i !== paramKeys.length - 1) {
      url += '&';
    }
  }

  return await customRequest(url, { method: 'GET' });
};

export const getFilteredParcelForCustomPOIData = async (params: any) => {
  let url = `/api/cma/${serverType}/parcel/cbsa-poi?`;

  const paramKeys = Object.keys(params);

  for (let i = 0; i < paramKeys.length; i++) {
    if (paramKeys[i] === 'body') continue;
    const name = paramKeys[i];
    const value = params[name];
    url += `${name}=${value}`;

    if (i !== paramKeys.length - 1) {
      url += '&';
    }
  }

  return await customRequest(url, { method: 'POST', data: params.body });
};

export const getFilteredParcelAdditionalData = async (params: any) => {
  return await customRequest(`/api/cma/${serverType}/parcel/cbsa-additional`, {
    method: 'POST',
    data: params.body,
  });
};

export const getAddressAutoCompleteData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/geocoding/autocomplete?address=${params.address}`,
    { method: 'GET' },
  );
};

export const getPOIChainSearchData = async (params: any) => {
  let url = `/api/cma/${serverType}/poi-chain-location/search?name=${encodeURIComponent(
    params.search,
  )}`;
  if (params.limit) {
    url += `&limit=${params.limit}`;
  }
  return await customRequest(url, { method: 'GET', signal: params.signal });
};

export const getCBSAOfPointData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/cbsa/point?lat=${params.lat}&lng=${params.lng}`,
    { method: 'GET' },
  );
};

export const getStateOfPointData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/state/point?lat=${params.lat}&lng=${params.lng}`,
    { method: 'GET' },
  );
};

export const getCBSASearchData = async (params: any) => {
  let url = `/api/cma/${serverType}/cbsa/search?name=${encodeURIComponent(
    params.search,
  )}`;
  if (params.limit) {
    url += `&limit=${params.limit}`;
  }
  if (params.sorted) {
    url += `&sorted=${params.sorted}`;
  }
  return await customRequest(url, { method: 'GET' });
};

export const getStateSearchData = async (params: any) => {
  let url = `/api/cma/${serverType}/state/search?name=${encodeURIComponent(
    params.search,
  )}`;
  if (params.limit) {
    url += `&limit=${params.limit}`;
  }
  if (params.sorted) {
    url += `&sorted=${params.sorted}`;
  }
  return await customRequest(url, { method: 'GET' });
};

export const getRelevantPOIChainsOfCBSAData = async (params: any) => {
  let url = `/api/cma/${serverType}/poi-chain-location/relevant-cbsa-chains`;
  if (params.cbsaKey) {
    url += `?cbsaKey=${params.cbsaKey}`;
  } else if (params.lat && params.lng) {
    url += `?lat=${params.lat}&lng=${params.lng}`;
  }

  return await customRequest(url, { method: 'POST', data: params.body });
};

export const getRelevantDemographicsOfCBSAData = async (params: any) => {
  let url = `/api/cma/${serverType}/heatmap-demographics/relevant-cbsa`;
  if (params.cbsaKey) {
    url += `?cbsaKey=${params.cbsaKey}`;
  } else if (params.lat && params.lng) {
    url += `?lat=${params.lat}&lng=${params.lng}`;
  }

  return await customRequest(url, { method: 'GET' });
};

export const getBuiltForRentData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/bfr?lng=${params.lng}&lat=${params.lat}&distance=${params.distance}`,
    { method: 'GET' },
  );
};

export const getSchoolDistrictOfPointData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/district/point?lat=${params.lat}&lng=${params.lng}`,
    { method: 'GET' },
  );
};

export const getDemographicRangeData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/demographic/min-max?demographicType=${params.type}`,
    { method: 'POST', data: params.body },
  );
};

export const getLandParcelSearchData = async (params: any) => {
  return await customRequest(`/api/cma/${serverType}/parcel/land-development`, {
    method: 'POST',
    data: params.body,
  });
};

export const getAllDemographicsInSquareData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/heatmap-demographics/square?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    { method: 'GET' },
  );
};

export const getZipCodeOfPointData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/zipcode/point?lat=${params.lat}&lng=${params.lng}`,
    { method: 'GET' },
  );
};

export const getCountyOfPointData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/county/point?lat=${params.lat}&lng=${params.lng}`,
    { method: 'GET' },
  );
};

export const getCountySearchData = async (params: any) => {
  let url = `/api/cma/${serverType}/county/search?name=${encodeURIComponent(
    params.search,
  )}`;
  if (params.limit) {
    url += `&limit=${params.limit}`;
  }
  if (params.sorted) {
    url += `&sorted=${params.sorted}`;
  }
  return await customRequest(url, { method: 'GET' });
};

export const getPOIChainLocationData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/poi-chain-location?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}&first_appeared=${params.firstappeared}`,
    {
      method: 'POST',
      body: JSON.stringify(params.body),
      headers: {
        'Content-Type': 'application/json',
      },
    },
  );
};

export const getNewHomeBuildersData = async () =>
  await customRequest(
    `/api/cma/${serverType}/real-estate-house-new-home/builders`,
    { method: 'GET' },
  );

export const getMonthlyImagesURL = ({
  tile,
  mosaic_name,
  token,
}: {
  tile: string;
  mosaic_name: string;
  token: string;
}) =>
  `/api/sbs/${serverType}/api/v1/monthly-images?tile=${tile}&mosaic_name=${mosaic_name}&coordinate={z}/{x}/{y}&access_token=${token}`;

interface LandBreakdownResponse {
  sl_uuid: string;
  source: string;
  regrid: Record<string, any>;
  parcel: Record<string, any>;
  mls: Record<string, any>;
  showcase: Record<string, any>;
}
export const getLandBreakDownBySpatialLaserId = async (params: {
  sl_uuid: string;
}): Promise<LandBreakdownResponse> => {
  const data = await customRequest(
    `/api/sbs/${serverType}/api/v1/land/breakdown/sl_id/${params.sl_uuid}`,
    { method: 'GET' },
  );
  return data as unknown as LandBreakdownResponse;
};
export const getLandBreakDownByBoundaryId = async (params: {
  boundary_id: string;
}) => {
  const data = await customRequest(
    `/api/sbs/${serverType}/api/v1/land/breakdown/boundary_id/${params.boundary_id}`,
    { method: 'GET' },
  );
  return data as unknown as LandBreakdownResponse;
};
export const getSpatialLaserIdWithParcelFipsApn = async (params: {
  body: Array<Record<'fips' | 'apn', string>>;
}) => {
  const data = await customRequest(
    `/api/sbs/${serverType}/api/v1/land/fips_apn_to_sl_uuid`,
    { method: 'PUT', data: params.body },
  );
  return data;
};
export const getLandFeaturesBySpatialLaserIds = async (params: {
  body: Array<Record<'sl_uuid', string>>;
}) => {
  const data = await customRequest(
    `/api/sbs/${serverType}/api/v1/land/sl_uuid`,
    { method: 'PUT', data: params.body },
  );
  return data;
};
//api/cma/prod/real-estate-house-new-home/builders

export const postLandSearchCSVExportTracking = async (params: any) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/land/log-csv-export`,
    { method: 'POST', data: params.body },
  );
};

export const postLandSearchSavedFilters = async (params: any) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/land/saved-filters`,
    { method: 'POST', data: params.body },
  );
};

export const getLandSearchSavedFilters = async () => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/land/saved-filters`,
    { method: 'GET' },
  );
};

export const deleteLandSearchSavedFilters = async (params: any) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/land/saved-filters/${params.filter_id}`,
    { method: 'DELETE' },
  );
};

export const getNewHomeBuilders = async () => {
  return await customRequest(
    `api/cma/${serverType}/real-estate-house-new-home/builders`,
    { method: 'GET' },
  );
};

export const getSchoolScoreDemographic = async (params: any) => {
  return await customRequest(
    `api/cma/${serverType}/demographic?lng=${params.lng}&lat=${params.lat}`,
    { method: 'GET' },
  );
};

export const postTargetsUploadCSV = async (params: any) => {
  return await customRequest(
    `/api/offMarket/${serverType}/truehold/targets/upload`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};

export const getPsychographicData = async (params: any) => {
  const url = `https://api.persona.spatial.ai/get_segment?lat=${params.lat}&lng=${params.lng}&zip=${params.zip}`;
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': 'b29a2058-1449-49b6-b343-b4d24889ee3d',
      },
    });
    if (!response.ok) {
      throw new Error(`API call failed: ${response.status}`);
    }
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching psychographic data:', error);
    throw error;
  }
};
export const getLastSoldPublicRecord = async (params: any) => {
  return await customRequest(
    `api/cma/${serverType}/parcel/last-sold?lng=${params.lng}&lat=${params.lat}&radius=${params.radius}`,
    { method: 'GET' },
  );
};

export const getPopulationDensityClass = async (params: any) => {
  return await customRequest(
    `api/cma/${serverType}/density/class?lng=${params.lng}&lat=${params.lat}`,
    { method: 'GET' },
  );
};

export const getPopulationDensity = async (params: any, distance: number) => {
  return await customRequest(
    `api/cma/${serverType}/density/population-density?lng=${params.lng}&lat=${params.lat}&area_type=buffer&distance=${distance}`,
    { method: 'GET' },
  );
};

// Client Allowed Access
export const getClientAllowedAccessCoordinatesData = async (params: {
  coordinates: `${number},${number}`;
}) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/metadata/allowed/coordinates/${params.coordinates}`,
    { method: 'GET' },
  );
};
export const getClientAllowedAccessCountyData = async (params: {
  countyKey: number | string;
}) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/metadata/allowed/county/${params.countyKey}`,
    { method: 'GET' },
  );
};
export const getClientAllowedAccessCBSAData = async (params: {
  cbsaKey: number | string;
}) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/metadata/allowed/cbsa/${params.cbsaKey}`,
    { method: 'GET' },
  );
};
export const getClientAllowedAccessMetroData = async (params: {
  metroKey: number | string;
}) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/metadata/allowed/metro/${params.metroKey}`,
    { method: 'GET' },
  );
};
export const getClientAllowedAccessStateData = async (params: {
  stateAbbrev: string;
}) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/metadata/allowed/state/${params.stateAbbrev}`,
    { method: 'GET' },
  );
};

export const postMapTrackingAPI = async (body: any) => {
  return await customRequest(`/api/management/${serverType}/map/store`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json', // Set the Content-Type header
    },
    body: body,
  });
};

export const getZoningDetailsData = async (params: {
  lat: number;
  lng: number;
}) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/zoneDetails?lat=${params.lat}&lng=${params.lng}`,
    {
      method: 'GET',
    },
  );
};

// Parcel Owner Summary Other Details
export const getOtherOwnersDetails = async (params: any) => {
  return await customRequest(
    `api/cma/${serverType}/mls/other/detail?lng=${params.lng}&lat=${params.lat}&distance=${params.distance}`,
    { method: 'GET' },
  );
};

export interface createNewFilterDataParams {
  name: string;
  body: any;
}

export const createNewFilterData = async (
  params: createNewFilterDataParams,
) => {
  return await customRequest(
    `/api/cma/${serverType}/filters?name=${params.name}`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};

export const getSavedFiltersData = async () => {
  return await customRequest(`/api/cma/${serverType}/filters`, {
    method: 'GET',
  });
};

export interface updateSavedFilterDataParams {
  id: number;
  name: string;
  body: any;
}

export const updateSavedFilterData = async (
  params: updateSavedFilterDataParams,
) => {
  return await customRequest(
    `/api/cma/${serverType}/filters?id=${params.id}&name=${params.name}`,
    {
      method: 'PUT',
      data: params.body,
    },
  );
};

export interface deleteSavedFilterDataParams {
  id: number;
}

export const deleteSavedFilterData = async (
  params: deleteSavedFilterDataParams,
) => {
  return await customRequest(`/api/cma/${serverType}/filters?id=${params.id}`, {
    method: 'DELETE',
  });
};

export const getMajorEmployers = async (params: any) => {
  return await customRequest(
    `api/cma/${serverType}/major-employers/distance?lng=${params.lat}&lat=${params.lng}&distance=${params.distance}`,
    { method: 'GET' },
  );
};

export const getAffordableHousingBuffer = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/affordable-housing/buffer?lng=${params.lng}&lat=${params.lat}&distance=${params.distance}`,
    {
      method: 'GET',
    },
  );
};

export const getBuildingPermit = async (params: any) => {
  return await customRequest(
    `api/cma/${serverType}/building_permit?lng=${params.lng}&lat=${params.lat}&distance=${params.distance}&days=${params.days}`,
    { method: 'GET' },
  );
};

export const getNearestRoad = async (params: any) => {
  return await customRequest(
    `api/sbs/${serverType}/api/v1/road/nearest/${params.lat},${params.lng}`,
    { method: 'GET' },
  );
};

export const getLandComps = async (params: any) => {
  return await customRequest(
    `api/cma/${serverType}/mls/land?lng=${params.lng}&lat=${params.lat}&distance=${params.distance}&status=${params.status}&startDate=${params.startDate}&endDate=${params.endDate}`,
    { method: 'GET' },
  );
};
export const getRealtorMultifamily = async (params: any) => {
  return await customRequest(
    `api/cma/${serverType}/realestate/realtor/multifamily?lng=${params.lng}&lat=${params.lat}&distance=${params.distance}&startDate=${params.startDate}`,
    { method: 'GET' },
  );
};

export const getMultiFamilyPropertiesInBuffer = async (opts: {
  distance: number;
  latitude: number;
  longitude: number;
}) => {
  return await customRequest(
    `api/sbs/${serverType}/api/v1/multifamily/buffer?latitude=${encodeURIComponent(
      opts.latitude,
    )}&longitude=${encodeURIComponent(
      opts.longitude,
    )}&distance=${encodeURIComponent(opts.distance)}`,
    { method: 'GET' },
  );
};

export const getRealtorSingleFamily = async (params: any) => {
  return await customRequest(
    `api/cma/${serverType}/realestate/realtor/singlefamily?lng=${params.lng}&lat=${params.lat}&distance=${params.distance}&startDate=${params.startDate}`,
    { method: 'GET' },
  );
};

export const getRealtorSingleFamilyWithinPolygon = async (params: any) => {
  // const serverType = 'exp'; // TEMP
  return await customRequest(
    `api/cma/${serverType}/realestate/realtor/singlefamily/multipolygon?startDate=${params.startDate}`,
    { method: 'POST', data: params.body },
  );
};

export const getLandShowcase = async (params: any) => {
  return await customRequest(
    `api/cma/${serverType}/realestate/showcase?lng=${params.lng}&lat=${params.lat}&distance=${params.distance}`,
    { method: 'GET' },
  );
};

export const getPsychographicDataDetails = async (params: any) => {
  return await customRequest(
    `api/cma/${serverType}/psychographic/distance?lng=${params.lng}&lat=${params.lat}&distance=${params.distance}`,
    { method: 'GET' },
  );
};

export const getPsychographicDataDetailsDriveTime = async (params: any) => {
  return await customRequest(`api/cma/${serverType}/psychographic/drivetime`, {
    method: 'POST',
    data: params.body,
  });
};

export const getAreaDemographicInDistance = async (params: any) => {
  return await customRequest(
    `api/cma/${serverType}/area-demographic/distance?lng=${params.lng}&lat=${params.lat}&distance=${params.distance}`,
    { method: 'GET' },
  );
};

export const getAreaDemographicInDriveTime = async (params: any) => {
  return await customRequest(
    `api/cma/${serverType}/area-demographic/drivetime`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};
export const getMajorEmployersnDriveTime = async (params: any) => {
  return await customRequest(
    `api/cma/${serverType}/major-employers/drivetime`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};

//Bookmarks
export const getUserBookmarks = async () => {
  return await customRequest(
    `api/cma/${serverType}/bookmark/user`,
    // `http://localhost:8080/bookmark/user`,
    {
      method: 'GET',
    },
  );
};
export const getBookmarkProperty = async (props: any) => {
  return await customRequest(`api/cma/${serverType}/bookmark/${props.id}`, {
    method: 'GET',
  });
};
export const postSaveBookmark = async (params: any) => {
  return await customRequest(
    `api/cma/${serverType}/bookmark`,
    // `http://localhost:8080/bookmark`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};
export const patchUpdateBookmarkName = async (params: any) => {
  return await customRequest(
    `api/cma/${serverType}/bookmark/${params.id}/name?name=${params.name}`,
    // `http://localhost:8080/bookmark`,
    {
      method: 'PATCH',
    },
  );
};

export const deleteBookmark = async (params: any) => {
  return await customRequest(`api/cma/${serverType}/bookmark/${params.id}`, {
    method: 'DELETE',
  });
};

export const getParcelDetailData = async (params: any) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/parcel/details/${params.ll_uuid}`,
    {
      method: 'GET',
    },
  );
};

export const getPropertyTaxData = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/parcel?placekey=${params.placekey}&lat=${params.lat}&lng=${params.lng}&streetnum=${params.streetnum}`,
    { method: 'GET' },
  );
};

export const getOwnersOtherParcels = async (params: any) => {
  const { lat, lng } = params;
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/parcel/owner_parcels/${lat},${lng}`,
    {
      method: 'GET',
    },
  );
};

export const postSkipTrace = async (params: any) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/parcel/skip-trace`,
    // `http://localhost:9003/api/v1/parcel/skip-trace`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};

export const getParcelAVMData = async (params) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/parcel/avm/${params.lat},${params.lng}`,
    {
      method: 'GET',
    },
  );
};

export const getTaxProperDetailsData = async (params) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/taxproper/tax-estimates`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};

export const getBTRPipeLine = async (params: any) => {
  const { lat, lng, distance } = params;
  return await customRequest(
    `/api/cma/${serverType}/btr-pipeline/point?lng=${lng}&lat=${lat}&distance=${distance}`,
    // `http://localhost:8080/btr-pipeline/point?lng=${lng}&lat=${lat}&distance=${distance}`,
    {
      method: 'GET',
    },
  );
};

export const postBuildingPermitCSV = async (params: any) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/csv/building-permit`,
    // `http://localhost:9003/api/v1/csv/building-permit`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};

export const postBTRPipelineCSV = async (params: any) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/csv/btr-pipeline`,
    // `http://localhost:9003/api/v1/csv/building-permit`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};

export const getAllBTRPipeline = async (params: any) => {
  return await customRequest(`/api/cma/${serverType}/btr-pipeline/all`, {
    method: 'GET',
  });
};

export const postAreaDemographicCSV = async (params: any) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/csv/area-demographic`,
    // `http://localhost:9003/api/v1/csv/building-permit`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};

export const postBTRSubmission = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/btr-submission`,
    // `http://localhost:8080/btr-submission`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};

export const getNewlySubdividedPipeLine = async (params: any) => {
  const { lat, lng, distance } = params;
  return await customRequest(
    `/api/cma/${serverType}/btr-pipeline/newly-subdivided-pipeline?lng=${lng}&lat=${lat}&distance=${distance}`,
    // `http://localhost:8080/btr-pipeline/point?lng=${lng}&lat=${lat}&distance=${distance}`,
    {
      method: 'GET',
    },
  );
};

export const postAffordableHousingCSV = async (params: any) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/csv/affordable-housing`,
    // `http://localhost:9003/api/v1/csv/building-permit`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};

export const getCrexi = async (params: any) => {
  const { lat, lng, distance } = params;
  return await customRequest(
    `/api/cma/${serverType}/realestate/crexi?lng=${lng}&lat=${lat}&distance=${distance}`,
    // `http://localhost:8080/btr-pipeline/point?lng=${lng}&lat=${lat}&distance=${distance}`,
    {
      method: 'GET',
    },
  );
};

export const postNewlySubdivdedLotsCSV = async (params: any) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/csv/newly-subdivided-lot`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};

export const getBTRCommunityInfo = async (params: any) => {
  const { lat, lng, distance } = params;
  return await customRequest(
    `/api/cma/${serverType}/btr-community-info?lng=${lng}&lat=${lat}&distance=${distance}`,
    // `http://localhost:8080/btr-pipeline/point?lng=${lng}&lat=${lat}&distance=${distance}`,
    {
      method: 'GET',
    },
  );
};

export const postBFRCompsCSV = async (params: any) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/csv/bfr-comps-combined`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};

export const postLandCompsCSV = async (params: any) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/csv/land-comps-combined`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};

export const postBatchProcessingFileDataV2 = async (params: any) => {
  if (params.date) {
    return await customRequest(
      `/api/elixir/${serverType}/upload?date=${params.date}`,
      {
        method: 'POST',
        data: params.body,
        requestType: 'form',
        headers: {
          // 'Content-Type': 'multipart/form-data',
          // 'Content-Type': 'application/octet-stream',
          // 'Content-Type': 'text/csv',
          Accept: '*/*',
        },
      },
    );
  }

  return await customRequest(`/api/elixir/${serverType}/upload`, {
    // return await customRequest(`/api/elixir/prod/upload`, {
    method: 'POST',
    data: params.body,
    requestType: 'form',
    headers: {
      // 'Content-Type': 'multipart/form-data',
      // 'Content-Type': 'application/octet-stream',
      // 'Content-Type': 'text/csv',
      Accept: '*/*',
    },
  });
};

export const getBatchProcessingProgressDataV2 = async (params: any) => {
  return await customRequest(
    `/api/elixir/${serverType}/job_status/${params.job_id}`,
    // `/api/elixir/prod/job_status/${params.job_id}`,
    {
      method: 'GET',
    },
  );
};

export const getBFRClusterData = async (params: any) => {
  const url = `/api/cma/${serverType}/bfr-cluster/all`;
  return await customRequest(url, { method: 'GET' });
};

// User Table Column Settings
export const getUserTableColumnSettings = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/user-table-column-settings/${params.username}`,
    // `http://localhost:8080/user-table-column-settings/${params.username}`,
    { method: 'GET' },
  );
};

export const postSaveUserTableColumnSettings = async (params: any) => {
  return await customRequest(
    `/api/cma/${serverType}/user-table-column-settings/save`,
    // `http://localhost:8080/user-table-column-settings/save`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};

export const getUserScorecardBatchResult = async (params: {
  email: string;
}) => {
  return await customRequest(
    `/api/cma/${serverType}/scorecard/batch-result-files-list?output_email=${params.email}`,
    // `http://localhost:8080/user-table-column-settings/${params.username}`,
    { method: 'GET' },
  );
};

export const postMajorEmployersCSV = async (params: any) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/csv/major-employers`,
    // `http://localhost:9003/api/v1/csv/building-permit`,
    {
      method: 'POST',
      data: params.body,
    },
  );
};

export const getSegmentOverview = async (params: { segmnetKey: string }) => {
  return await customRequest(
    `/api/cma/${serverType}/segments/${params.segmnetKey}/overview`,
    { method: 'GET' },
  );
};

export const getSegmentOverviewDma = async (params: { segmnetKey: string }) => {
  return await customRequest(
    `/api/cma/${serverType}/segments/${params.segmnetKey}/overview-dma`,
    { method: 'GET' },
  );
};

export const getSegmentRange = async (params: { segmnetKey: string }) => {
  return await customRequest(
    `/api/cma/${serverType}/segments/${params.segmnetKey}/range`,
    { method: 'GET' },
  );
};

export interface getJacksonVilleBuildersParams {
  lat1: number;
  lng1: number;
  lat2: number;
  lng2: number;
  t4yCount?: number;
  medianJobValue?: number;
  latestLicenseDescription?: string[];
  contractorType?: string[];
}

export const getJacksonVilleBuilders = async (
  params: getJacksonVilleBuildersParams,
) => {
  // Build query parameters, filtering out undefined values
  const queryParams = new URLSearchParams();

  // Required parameters
  queryParams.append('lat1', params.lat1.toString());
  queryParams.append('lng1', params.lng1.toString());
  queryParams.append('lat2', params.lat2.toString());
  queryParams.append('lng2', params.lng2.toString());

  // Optional parameters - only add if they have values
  if (params.t4yCount !== undefined && params.t4yCount !== null) {
    queryParams.append('t4yCount', params.t4yCount.toString());
  }

  if (params.medianJobValue !== undefined && params.medianJobValue !== null) {
    queryParams.append('medianJobValue', params.medianJobValue.toString());
  }

  if (
    params.latestLicenseDescription &&
    params.latestLicenseDescription.length > 0
  ) {
    params.latestLicenseDescription.forEach((desc) => {
      queryParams.append('latestLicenseDescription', desc);
    });
  }

  if (params.contractorType && params.contractorType.length > 0) {
    params.contractorType.forEach((type) => {
      queryParams.append('contractorType', type);
    });
  }

  return await customRequest(
    `/api/cma/${serverType}/building_permit/jacksonville_builders?${queryParams.toString()}`,
    {
      method: 'GET',
    },
  );
};

export const getJacksonVilleBuilderById = async (params: {
  contractorId: string;
}) => {
  return await customRequest(
    `/api/cma/${serverType}/building_permit/jacksonville_builders/${params.contractorId}`,
    { method: 'GET' },
  );
};
export const getJacksonVilleBuilderChartData = async () => {
  return await customRequest(
    `/api/cma/${serverType}/building_permit/jacksonville_builders_chart`,
    { method: 'GET' },
  );
};
export const postCreateShareCMA = async (params: any) => {
  return await customRequest(
    `/api/admin-server/${serverType}/api/cma-share/create?address=${params.address}&mode=${params.mode}`,
    {
      method: 'POST',
      data: params.body,
      requestType: 'form',
      headers: {
        // 'Content-Type': 'multipart/form-data',
        // 'Content-Type': 'application/octet-stream',
        // 'Content-Type': 'text/csv',
        Accept: '*/*',
      },
    },
  );
};

export const getScorecardHistory = async (params: { find: string }) => {
  return await customRequest(
    `/api/cma/${serverType}/scorecard/history?find=${params.find}`,
    {
      method: 'GET',
    },
  );
};
