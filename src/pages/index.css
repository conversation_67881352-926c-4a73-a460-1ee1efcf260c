@import '../components/globalVars.css';

.overallWrapper {
  width: 100%;
  height: 100vh;
  background-color: #f0f8ff; /* rgba(240 248 255 / 1) */
  /* min-height: 928px; */
}

.title {
  background: rgb(121, 242, 157);
}

.mapCMAContainer {
  position: relative;
  padding: var(--map-padding);
  padding-top: 0;
}

.mapBorderRadius {
  border-radius: 8px;
}

/* 136px is height of navbar - TODO: refactor to use variable */
.contentContainer {
  position: relative;
  left: 44px;
  width: calc(100vw - 56px);
  height: calc(100vh - 102px);
  display: flex;
  flex-direction: row;
  padding-left: 20px;
  /* padding-right: 20px; */
  padding-bottom: 20px;
  /* gap: 20px; */
  min-height: 792px;
  /* overflow: hidden; */
}

.mapContainer {
  position: relative;
  height: 100%;
  width: calc((9 / 24) * 100%);
  transition: width 250ms ease-in-out;
  border-radius: 8px;
  overflow: hidden;
}

.mapContainerExpanded {
  width: calc((15 / 24) * 100%);
}

.resultContainer {
  position: relative;
  /* left: 56px; */
  height: 100%;
  padding-top: 8px;
  padding-left: 20px;
  /* transition: width 250ms ease-in-out; */
  /* overflow: hidden; */
}

.resultContainerDefault {
  min-width: calc((15 / 24) * 100%);
  max-width: calc((15 / 24) * 100%);
}

.resultContainerExpanded {
  min-width: calc((9 / 24) * 100%);
  max-width: calc((9 / 24) * 100%);
}
