import '@aws-amplify/ui-react/styles.css';
import { Col, Row } from 'antd';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import Header from '../components/Header';
import HeaderBar from '../components/HeaderBar/HeaderBar';
import MapCMA from '../components/MapCMA/MapCMA';
import ResultTable from '../components/ResultTable/ResultTable';
import styles from './index.css';
// import '@ant-design/flowchart/dist/index.css';
import { getMenuSchema } from '@spatiallaser/map';
import { SideBar } from '@spatiallaser/menu-bar';
import 'antd/dist/reset.css';
import { motion } from 'framer-motion';
import { LandParcelSearchProvider } from '../components/LandParcelSearch';
import LandParcelSearch from '../components/LandParcelSearch/LandParcelSearch';
import LandParcelSearchCMAWrapper from '../components/LandParcelSearch/LandParcelSearchCMAWrapper';
import SiteSelection from '../components/LandParcelSearch/SiteSelection';
import OldSideBar from '../components/SideBar/SideBar';

import TaxDetailsModal from '../components/ResultTable/TaxDetails';
import { usePrevious } from '../hooks';
const defaultSignOut = () => {};
const sideBarWidth = 65;

const Layout = ({ signOut = defaultSignOut }) => {
  const map = useSelector(({ CMA }) => CMA.map);
  const appViewMode = useSelector(({ CMA }) => CMA.appViewMode);
  const mapExpandedView = useSelector(({ CMA }) => CMA.mapExpandedView);
  const selectedUserGroup = useSelector(({ CMA }) => CMA.selectedUserGroup);
  const chatOpened = useSelector(({ CMA }) => CMA.chatOpened);
  const showTaxDetailsModal = useSelector(({ CMA }) => CMA.showTaxDetailsModal);
  const userEmail = useSelector(({ CMA }) => CMA.userEmail);
  const currentMapLayerOptions = useSelector(
    ({ CMA }) => CMA.currentMapLayerOptions,
  );

  const dispatch = useDispatch();

  const closeTaxDetailsModal = () => {
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: {
        taxDetails: null,
        showTaxDetailsModal: false,
      },
    });
  };
  const toggleChat = () => {
    dispatch({
      type: 'CMA/saveCMAStates',
      payload: { chatOpened: !chatOpened },
    });
  };
  const appViewModePrev = usePrevious(appViewMode);

  useEffect(() => {
    if (!map) return;
    const mapContainer = document.querySelector('#mapContainer');
    const mapRadiusSelector = document.querySelector('#radiusSelectWrapper');

    if (!mapContainer || !mapRadiusSelector) return;
    if (!appViewMode || appViewMode.mode === 'CMA') {
      mapContainer.classList.add(styles.mapBorderRadius);
      mapRadiusSelector.style.display = 'flex';
    } else if (
      appViewMode.mode === 'LandDevelopment' &&
      appViewMode.subMode.some((subMode) =>
        ['LandParcelSearch', 'LandMonthlyImages'].some(
          (mode) => subMode == mode,
        ),
      )
    ) {
      mapContainer.classList.remove(styles.mapBorderRadius);
      mapRadiusSelector.style.display = 'none';
    }

    if (
      appViewMode.mode === 'LandDevelopment' &&
      appViewMode.subMode.includes('LandMonthlyImages') &&
      appViewModePrev.mode != 'CMA'
    ) {
      map.resize();
    }
  }, [map, appViewMode]);

  return (
    // <div className={styles.overallWrapper}>
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        width: '100%',
        height: '100vh',
        backgroundColor: '#f0f8ff',
      }}
    >
      <Row className="border-b border-[#ccc]" style={{ zIndex: 100 }}>
        <Col span={24}>
          <Header key="header" signOut={signOut} />
        </Col>
        <Col>
          <HeaderBar />
          <TaxDetailsModal
            visible={showTaxDetailsModal}
            onClose={closeTaxDetailsModal}
          />
        </Col>
      </Row>
      {/* <div style={{ display: 'flex', flexDirection: 'row' }}>
        <div key="sidebar" className={styles.sidebarContainer}>
          <SideBar />
        </div>
        <div className={styles.contentContainer}>
          <MapCMA />
          <div id="result-container" className={styles.resultContainer}>
            <ResultTable />
          </div>
        </div>
      </div> */}
      <div
        style={{
          display: 'flex',
          flexDirection: 'row',
          width: '100%',
          height: '100%',
          minHeight: 0,
          // height: 'calc(100vh - 94px)',
        }}
      >
        <SideBar
          options={{
            mode: 'vertical',
            barWidth: sideBarWidth,
            expanderWidth: 250,
          }}
          values={currentMapLayerOptions}
          onClick={(id) => {
            console.log('test1 id', id);
            console.log('test1 currentMapLayerOptions', currentMapLayerOptions);
            const selectedLayer = !currentMapLayerOptions.includes(id)
              ? [...currentMapLayerOptions, id]
              : currentMapLayerOptions.filter((layer) => layer !== id);
            console.log('test1 selectedLayer', selectedLayer);
            dispatch({
              type: 'CMA/saveCMAStates',
              payload: {
                currentMapLayerOptions: selectedLayer,
              },
            });
            map.fire('mapLayers.currentMapLayerOptions', {
              payload: {
                currentMapLayerOptions: selectedLayer,
              },
            });
          }}
          onRemove={(id) => {
            const selectedLayer = currentMapLayerOptions.filter(
              (layer) => layer !== id,
            );
            dispatch({
              type: 'CMA/saveCMAStates',
              payload: {
                currentMapLayerOptions: selectedLayer,
              },
            });
            map.fire('mapLayers.currentMapLayerOptions', {
              payload: {
                currentMapLayerOptions: selectedLayer,
              },
            });
          }}
          schema={getMenuSchema(selectedUserGroup, userEmail)}
        />

        <div
          style={{
            // width: '100%',
            width: `calc(100% - ${sideBarWidth}px)`,
            display: 'flex',
            flexDirection: 'row',
          }}
        >
          <motion.div
            initial={false}
            animate={{
              width:
                (!appViewMode || appViewMode.mode === 'CMA') && mapExpandedView
                  ? '60%'
                  : (!appViewMode || appViewMode.mode === 'CMA') &&
                    !mapExpandedView
                  ? '40%'
                  : '100%',
            }}
            style={{
              padding: !appViewMode || appViewMode.mode === 'CMA' ? '8px 0' : 0,
            }}
            onAnimationComplete={() => {
              if (!map) return;
              setTimeout(() => {
                map.resize();
              }, 200);
            }}
          >
            <MapCMA style={{ borderRadius: 0 }} />
          </motion.div>
          {(!appViewMode || appViewMode.mode === 'CMA') && (
            <motion.div
              id="result-container"
              initial={false}
              className={`${styles.resultContainer}`}
              animate={{ width: !mapExpandedView ? '60%' : '40%' }}
              style={{
                padding:
                  !appViewMode || appViewMode.mode === 'CMA' ? '8px 0' : 0,
              }}
            >
              <ResultTable />
            </motion.div>
          )}

          {appViewMode.mode === 'LandDevelopment' &&
            appViewMode.subMode.includes('LandParcelSearch') && (
              <LandParcelSearchProvider selectedUserGroup={selectedUserGroup}>
                <LandParcelSearchCMAWrapper />
              </LandParcelSearchProvider>
            )}
        </div>
      </div>
    </div>
  );
};

export default Layout;
