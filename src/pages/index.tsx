import useUserAuthentication from '@/hooks/useUserAuthentication';
import { DEFAULT_STATE } from '@/models/cma';
import { getUserToken } from '@/utils/auth';
import {
  WithAuthenticatorProps,
  withAuthenticator,
} from '@aws-amplify/ui-react';
import * as Sentry from '@sentry/react';
import { Button, ConfigProvider, Spin } from 'antd';
import { Amplify } from 'aws-amplify';
import { useCallback, useEffect } from 'react';
import { QueryClient, QueryClientProvider } from 'react-query';
import { history, useDispatch, useSelector } from 'umi';
import Layout from './Layout';

const queryClient = new QueryClient();

import { MapProvider } from '@spatiallaser/map';

import Hotjar from '@hotjar/browser';

const siteId = 6492526;
const hotjarVersion = 6;
Hotjar.init(siteId, hotjarVersion, {
  debug: true,
});

if (!(window as any).spatiallaser) {
  (window as any).spatiallaser = {
    getUserToken,
  };
}

if (process.env.NODE_ENV != 'development') {
  console.log = () => {};
}

if (process.env.NODE_ENV === 'production') {
  Sentry.init({
    dsn: 'http://e2dcc3925e0145c69855b36ac80d8887@3.235.170.15:9041/3',
    environment: 'production',
  });
}
const Index = ({ signOut, user }: WithAuthenticatorProps) => {
  const { initLoading, accessAllowed, authSession } = useUserAuthentication({
    user,
  });

  const dispatch = useDispatch();

  useEffect(() => {
    return () => {
      dispatch({
        type: 'CMA/saveCMAStates',
        payload: DEFAULT_STATE,
      });
    };
  }, []);

  const signUserOut = useCallback(async () => {
    signOut && signOut();
  }, [signOut]);

  if (!user) {
    signUserOut();
  } else if (initLoading) {
    return (
      <div
        key="full screen"
        style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <Spin />
      </div>
    );
  } else if (!accessAllowed) {
    return (
      <div
        key="no access to CMA portal"
        style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          gap: 16,
          textAlign: 'center',
          fontSize: 16,
          lineHeight: 2,
          color: '#333',
        }}
      >
        {user && user.signInDetails && user.signInDetails.loginId && (
          <div key="account email">
            Signed in as
            <br />
            <span style={{ fontWeight: 'bold' }}>
              {user.signInDetails.loginId}
            </span>
            {authSession &&
              authSession.userGroup &&
              authSession.userGroup.length > 0 && (
                <>
                  <br />
                  as a member of
                  <br />
                  <span style={{ fontWeight: 'bold' }}>
                    {/* {authSession?.userGroup[0]} */}
                    {authSession?.userGroup?.find(
                      (group) => !group.toLowerCase().includes('localinvestor'),
                    )}
                  </span>
                </>
              )}
          </div>
        )}
        <div key="warning">
          CMA portal is currently not in your subscription. Please use a
          different account or contact us to request a demo.
        </div>
        <Button key="sign out" type="default" onClick={signUserOut}>
          Sign out
        </Button>
      </div>
    );
  }

  return (
    <ConfigProvider
      theme={{
        token: {
          fontFamily: 'IBM Plex Sans',
        },
      }}
    >
      <QueryClientProvider client={queryClient}>
        <MapProvider>
          <Layout signOut={signUserOut} />
        </MapProvider>
      </QueryClientProvider>
    </ConfigProvider>
  );
};

// re-use existing authentication resource
// ref: https://docs.amplify.aws/lib/auth/start/q/platform/js/#re-use-existing-authentication-resource
// parameters ref: https://stackoverflow.com/a/********/********
// ATTN: new parameters for new version of aws-amplify
Amplify.configure({
  Auth: {
    Cognito: {
      userPoolId: 'us-east-1_sxzcOZ6F2',
      userPoolClientId: '2e304o8db57tg73pr4nbreiq75',
      loginWith: {
        username: false,
        email: true,
      },
    },
  },
});

const formFields = {
  forceNewPassword: {
    password: {
      descriptiveText: (
        <div style={{ fontSize: 12 }}>
          <p key="line1" style={{ marginBottom: 0 }}>
            Password minimum length: 8 character(s)
          </p>
          <p key="line2" style={{ marginBottom: 0 }}>
            Password requirements:
          </p>
          <ul
            style={{
              listStyleType: 'none',
              // textIndent: '-3em',
              paddingInlineStart: '0px',
            }}
          >
            <li key="itme1">- Contains at least 1 number</li>
            <li key="item2">
              - Contains at least 1 special character (^ $ * . [ ] {} ( ) ? - "
              ! @ # % & / \ , &gt; &lt; ' : ; | _ ~ ` + =)
            </li>
            <li key="item3">- Contains at least 1 uppercase letter</li>
            <li key="item4">- Contains at least 1 lowercase letter</li>
          </ul>
        </div>
      ),
    },
  },
};

export default withAuthenticator(Index, {
  initialState: 'signIn',
  hideSignUp: true,
  variation: 'modal',
  formFields: formFields as any,
});
