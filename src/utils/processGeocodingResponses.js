export const processReverseGeocodingResponse = (response) => {
  if (response && response.features.length > 0) {
    const item = response.features[0];
    // get ZIP Code from geocoding response
    const postalCodeContext = item.context.find(
      (subItem) => subItem.id.indexOf('postcode') > -1,
    );
    let postalCode;
    if (postalCodeContext) {
      postalCode = postalCodeContext.text;
    } else if (item.id.includes('postcode')) {
      postalCode = item.text;
    }
    // get city from geocoding response
    const cityContext = item.context.find(
      (subItem) => subItem.id.indexOf('place') > -1,
    );
    let city;
    if (cityContext) {
      city = cityContext.text;
    }
    // get state from geocoding response
    const regionContext = item.context.find(
      (subItem) => subItem.id.indexOf('region') > -1,
    );
    let region;
    if (regionContext) {
      region = regionContext.text;
    }
    const subjectPropertyPlacekeyAPIParams = {
      fullAddress: item.place_name,
      streetAddress: item.address ? item.address + ' ' + item.text : item.text, // street number + street name; if no street number, use street name only
      postalCode,
      city,
      region,
      latitude: item.center[1],
      longitude: item.center[0],
    };
    return subjectPropertyPlacekeyAPIParams;
    // get placekey
    // yield put({
    //   type: 'getPlacekey',
    //   payload: subjectPropertyPlacekeyAPIParams,
    // });
    // yield put({
    //   type: 'saveCMAStates',
    //   payload: {
    //     currentPropertyAddress: subjectPropertyPlacekeyAPIParams,
    //   },
    // });
    // so that when sharing this subject property, we'll use lngLat as params
    // yield put({
    //   type: 'saveCMAStates',
    //   payload: {
    //     modeToFindSubjectProperty: 'Click on Map',
    //   },
    // });
  } else {
    return {};
  }
};

export const processForwardGeocodingResponse = (response) => {
  if (response && response.features.length > 0) {
    // yield put({
    //   type: 'saveCMAStates',
    //   payload: {
    //     geocodingData: response,
    //   },
    // });
    const item = response.features[0];
    const postalCodeContext = item.context.find(
      (subItem) => subItem.id.indexOf('postcode') > -1,
    );
    let postalCode;
    if (postalCodeContext) {
      postalCode = postalCodeContext.text;
    }
    // get city from geocoding response
    const cityContext = item.context.find(
      (subItem) => subItem.id.indexOf('place') > -1,
    );
    let city;
    if (cityContext) {
      city = cityContext.text;
    }
    // get state from geocoding response
    const regionContext = item.context.find(
      (subItem) => subItem.id.indexOf('region') > -1,
    );
    let region;
    if (regionContext) {
      region = regionContext.text;
    }
    const currentPropertyAddress = {
      fullAddress: item.place_name,
      streetAddress: item.address + ' ' + item.text, // street number + street name
      postalCode,
      city,
      region,
      latitude: item.center[1],
      longitude: item.center[0],
    };

    return {
      geocodingData: response,
      eventCoordinates: item.center,
      currentPropertyAddress: currentPropertyAddress,
    };

    // yield put({
    //   type: 'saveCMAStates',
    //   payload: {
    //     eventCoordinates: item.center,
    //     currentPropertyAddress: currentPropertyAddress,
    //   },
    // });
    // // so that when sharing this subject property, we'll use address as params
    // yield put({
    //   type: 'saveCMAStates',
    //   payload: {
    //     modeToFindSubjectProperty: 'Search an Address',
    //   },
    // });
  } else {
    return {
      geocodingData: {},
      eventCoordinates: [],
      currentPropertyAddress: {},
    };
  }
};
