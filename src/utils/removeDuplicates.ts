import moment from 'moment';

const dateFormat = 'YYYY-MM-DD';

interface TProperty {
  [key: string]: any;
}

// remove objects in an array that have the same value for one key, only keep one of them matching conditions
// i.e. remove properties with the same addresses from an API response, only keep one property with the latest closing date
// @param {Array} dataSource - array of objects, i.e. response from API
// @param {string} keyWithDuplicates - object key that has duplicated value, i.e. property address
// @param {string} keyToCompare - object key that we use its value to decide which object to keep, i.e. only keep the object with the latest closing date
// @param {function} compareFunction - function to compare values, return boolean
export const removeDuplicatedEarlierProperties = (
  dataSource: TProperty[],
  keyWithDuplicates: string,
  keyToCompare: string,
  compareFunction: (newValue: any, existingValue: any) => boolean,
): TProperty[] => {
  let resultArray: TProperty[] = [];
  let tempSet = new Set();
  for (const property of dataSource) {
    const prevTempSetSize = tempSet.size;
    tempSet.add(property[keyWithDuplicates]);

    // some properties have addresses with only a space in it
    // keep all these properties
    if (keyWithDuplicates === 'fulladdress' && property[keyWithDuplicates].trim() === '') {
      resultArray.push(property);
    } else if (tempSet.size === prevTempSetSize + 1) {
      resultArray.push(property);
    } else {
      const existingPropertyWithSameValue = resultArray.find(
        (item) => item[keyWithDuplicates] === property[keyWithDuplicates],
      );
      if (
        existingPropertyWithSameValue &&
        compareFunction(
          property[keyToCompare],
          existingPropertyWithSameValue[keyToCompare],
        )
      ) {
        resultArray = resultArray.filter(
          (item) => item[keyWithDuplicates] !== property[keyWithDuplicates],
        );
        resultArray.push(property);
      }
    }
  }
  return resultArray;
};

// function to compare date
// for using as a param in removeDuplicatedEarlierProperties
export const compareDate = (newDate: string, existingDate: string): boolean => {
  return moment(newDate).isAfter(moment(existingDate));
};
// function to process any properties with sqft < 10 and/or rent > 50k
// remove abnormal sqft and rent values
export const removeAbnormalSqftAndRentValues = (
  dataSource: TProperty[],
  keySqft: string,
  keyRent: string,
): TProperty[] => {
  return dataSource.map((item) => {
    if (item[keySqft] <= 10) {
      console.log(`${item.address}-sqft: ${item[keySqft]}`, 'removed value:');
    }
    if (item[keyRent] >= 50000) {
      console.log(`${item.address}-rent: ${item[keyRent]}`);
    }
    return {
      ...item,
      [keySqft]: item[keySqft] > 10 ? item[keySqft] : null,
      [keyRent]: item[keyRent] < 50000 ? item[keyRent] : null,
    };
  });
};
