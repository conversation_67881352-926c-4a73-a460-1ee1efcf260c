import { geojsonTemplate } from '../constants';

export const generateGeoJSON = (filteredDataSource, dataSourceType) => {
  // generate geojson
  let geojsonFeatures = [];
  if (filteredDataSource && filteredDataSource.length > 0) {
    for (const property of filteredDataSource) {
      if (dataSourceType === 'MLS') {
        let { geography, ...geojsonProperties } = property;
        geojsonFeatures.push({
          type: 'Feature',
          geometry: property.geography,
          properties: geojsonProperties,
        });
      } else if (['SFR', 'HotPads'].includes(dataSourceType)) {
        let { geom, ...geojsonProperties } = property;
        geojsonFeatures.push({
          type: 'Feature',
          geometry: property.geom,
          properties: geojsonProperties,
        });
      } else if (['PadSplit'].includes(dataSourceType)) {
        let { geog, ...geojsonProperties } = property;
        geojsonFeatures.push({
          type: 'Feature',
          geometry: property.geog,
          properties: geojsonProperties,
        });
      }
    }
  }
  const currentGeoJSON = {
    type: 'FeatureCollection',
    features: geojsonFeatures,
  };

  return currentGeoJSON;
};

export const convertToGeoJSON = (data, geomAccessor, propertiesAccessor) => {
  const geojson = structuredClone(geojsonTemplate);

  geojson.features = data.map((item) => {
    return {
      type: 'Feature',
      geometry: geomAccessor(item),
      properties: propertiesAccessor ? propertiesAccessor(item) : item,
    };
  });

  return geojson;
};
