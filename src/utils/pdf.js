import { legalDescriptionTrimmer } from '@/components/ResultTable/Summary/Summary';
import { calculateAverage, calculateMedian } from '@/utils/calculations';
import { formatter } from '@/utils/money';
import { toPng } from 'html-to-image';
import isEmpty from 'lodash.isempty';
import moment from 'moment';
import { MAPBOX_TOKEN } from '../constants';
import { getClientInformation } from './userGroup';

export const createHeaderContent = (userGroup) => {
  const today = new Date();
  const dd = String(today.getDate()).padStart(2, '0');
  const mm = String(today.getMonth() + 1).padStart(2, '0'); //January is 0!
  const yyyy = today.getFullYear();
  const dateFormatted = mm + '/' + dd + '/' + yyyy;

  const headerContent = {
    rowName: 'Header',
    clientInfo: getClientInformation(userGroup),
    title: 'CMA Report',
    generatedText: userGroup.includes('Sunroom')
      ? `Generated on\n${dateFormatted}`
      : `Generated on\n${dateFormatted}\nby Spatial Laser`,
  };

  return headerContent;
};

export const createSubjectPropertyContent = (
  currentPropertyAddress,
  subjectPropertyParcelData,
  eventCoordinates,
  medianDOM,
) => {
  const SCREENSHOT_ZOOM = 19;
  const imgWidth = 350;
  const imgHeight = 300;
  // const { longitude, latitude } = currentPropertyAddress;
  const longitude = eventCoordinates[0];
  const latitude = eventCoordinates[1];

  const subjectPropertyContent = {
    rowName: 'Subject Property',
    propertyImage: {
      src: `https://api.mapbox.com/styles/v1/mapbox/satellite-v9/static/${longitude},${latitude},${SCREENSHOT_ZOOM},0,0/${imgWidth}x${imgHeight}?access_token=${MAPBOX_TOKEN}`,
      width: imgWidth,
      height: imgHeight,
    },
    propertyAddress: {
      streetAddress:
        currentPropertyAddress.streetAddress || `${longitude}, ${latitude}`,
      city: currentPropertyAddress.city || '-',
      region: currentPropertyAddress.region || '-',
      postalCode: currentPropertyAddress.postalCode || '-',
    },
    propertyDetail: {
      beds: subjectPropertyParcelData.beds_count || '-',
      baths: subjectPropertyParcelData.baths || '-',
      sqft: subjectPropertyParcelData.total_area_sq_ft || '-',
      builtYear: subjectPropertyParcelData.year_built || '-',
      hoa:
        subjectPropertyParcelData.hoa_fees >= 0
          ? subjectPropertyParcelData.hoa_fees === 0 ||
            subjectPropertyParcelData.hoa_fees === null
            ? '$0'
            : '$' + subjectPropertyParcelData.hoa_fees
          : '-',
      subdivision: subjectPropertyParcelData.subdivision || '',
      legalDescription:
        legalDescriptionTrimmer(subjectPropertyParcelData.legal_description) ||
        '',
    },
    avm: {
      rent: {
        value: subjectPropertyParcelData.rent
          ? `$${formatter(subjectPropertyParcelData.rent)}`
          : '-',
        min:
          subjectPropertyParcelData.rent && subjectPropertyParcelData.low_ratio
            ? `$${formatter(
                subjectPropertyParcelData.rent *
                  (1 + subjectPropertyParcelData.low_ratio),
              )}`
            : '',
        max:
          subjectPropertyParcelData.rent && subjectPropertyParcelData.high_ratio
            ? `$${formatter(
                subjectPropertyParcelData.rent *
                  (1 + subjectPropertyParcelData.high_ratio),
              )}`
            : '',
      },
      sales: {
        value: subjectPropertyParcelData.sales
          ? `$${formatter(subjectPropertyParcelData.sales)}`
          : '-',
        min:
          subjectPropertyParcelData.sales && subjectPropertyParcelData.low_ratio
            ? `$${formatter(
                subjectPropertyParcelData.sales *
                  (1 + subjectPropertyParcelData.low_ratio),
              )}`
            : '',
        max:
          subjectPropertyParcelData.sales &&
          subjectPropertyParcelData.high_ratio
            ? `$${formatter(
                subjectPropertyParcelData.sales *
                  (1 + subjectPropertyParcelData.high_ratio),
              )}`
            : '',
      },
    },
    medianDOM: {
      lease: medianDOM.leaseMedianCDOM,
      sale: medianDOM.saleMedianCDOM,
    },
  };

  return subjectPropertyContent;
};

export const createNearbyParcelOwnerContent = (currentParcelOwnerSummary) => {
  const { total_parcels, owner_count, institution_count } =
    currentParcelOwnerSummary;

  const notOccupied = owner_count.filter(
    (owner) => owner.owner_occupied_sl === 'No',
  );
  const renterCount = notOccupied.length > 0 ? notOccupied[0].count : 0;

  const isOccupied = owner_count.filter(
    (owner) => owner.owner_occupied_sl === 'Yes',
  );
  const ownerCount = isOccupied.length > 0 ? isOccupied[0].count : 0;

  const institution_order = {
    AH4R: 1,
    Cerberus: 2,
    'Invitation Homes': 3,
    'Progress Residential': 4,
    Other: 5,
    'Mom & Pop': 6,
    default: 100,
  };

  institution_count.sort((a, b) => {
    return (
      (institution_order[a.institution] || institution_order.default) -
      (institution_order[b.institution] || institution_order.default)
    );
  });

  // add percentage and modify 'Other' to 'Other Funds'
  const institutions = institution_count
    .map((institution) => {
      if (institution.institution != 'Other') {
        return {
          ...institution,
          percent: (institution.count / renterCount) * 100,
        };
      } else {
        return {
          ...institution,
          institution: 'Other Funds',
          percent: (institution.count / renterCount) * 100,
        };
      }
    })
    .filter((institution) => institution.institution != 'None');

  const nearbyParcelOwnerContent = {
    rowName: 'Nearby Parcel Owner Summary',
    totalParcels: total_parcels,
    renter: {
      count: renterCount,
      percent: (renterCount / total_parcels) * 100,
    },
    owner: {
      count: ownerCount,
      percent: (ownerCount / total_parcels) * 100,
    },
    institutions: institutions,
  };

  return nearbyParcelOwnerContent;
};

export const createNearbyLeaseSummaryContent = (
  userGroup,
  subjectPropertyParcelData,
  NSFRInformation,
  hotPadInformation,
  mlsLeaseInformation,
  marketRentPreference,
  adjustedParams,
) => {
  const isBridgeTower = userGroup && userGroup.includes('BridgeTower');
  const isCGC = userGroup && userGroup.includes('CommonGroundCapital');

  const crimeScore = isCGC && {
    title: 'Crime\nScore',
    text: `${subjectPropertyParcelData.score_crime}`,
    subText: '',
  };

  const subjectPropertyRentAVM = {
    title: 'Subject Property\nRent AVM',
    text: subjectPropertyParcelData.rent
      ? `${'$' + formatter(subjectPropertyParcelData.rent)}`
      : '-',
    subText:
      subjectPropertyParcelData.rent &&
      subjectPropertyParcelData.total_area_sq_ft
        ? `$${(
            subjectPropertyParcelData.rent /
            subjectPropertyParcelData.total_area_sq_ft
          ).toFixed(2)} per Sqft`
        : '-',
  };

  const nsfrRentValues = NSFRInformation
    ? NSFRInformation.map((nsfr) => nsfr.rent)
    : NSFRInformation;
  const nsfrRentMedianSFR = NSFRInformation
    ? calculateMedian(nsfrRentValues)
    : 0;
  const nsfrRentAverageNationalOperators = NSFRInformation
    ? calculateAverage(nsfrRentValues)
    : 0;
  const nsfrRentAveragePerSqftNationalOperators = NSFRInformation
    ? calculateAverage(
        NSFRInformation.filter(
          (nsfr) => nsfr.square_feet && nsfr.square_feet > 0 && nsfr.rent,
        ).map((nsfr) => nsfr.rent / nsfr.square_feet),
      )
    : 0;

  const nationalSFR = {
    title: `Median National SFR\nOperators Listings`,
    text: nsfrRentMedianSFR ? `$${formatter(nsfrRentMedianSFR)}` : '-',
    subText: nsfrRentAveragePerSqftNationalOperators
      ? `$${nsfrRentAveragePerSqftNationalOperators.toFixed(2)} per Sqft`
      : '-',
    // subText: !isBridgeTower
    //   ? nsfrRentAveragePerSqftNationalOperators
    //     ? `$${nsfrRentAveragePerSqftNationalOperators.toFixed(2)} per Sqft`
    //     : '-'
    //   : '',
  };

  const hotPadRentValues = hotPadInformation
    ? hotPadInformation.map((hotPad) => hotPad.rent)
    : hotPadInformation;
  const hotPadRentMedian = hotPadInformation
    ? calculateMedian(hotPadRentValues)
    : hotPadInformation;
  const hotPadRentAverage = hotPadInformation
    ? calculateAverage(hotPadRentValues)
    : hotPadInformation;
  const hotPadAvgRentSqft = hotPadInformation
    ? calculateAverage(
        hotPadInformation
          .filter(
            (hotPad) =>
              hotPad.square_feet && hotPad.square_feet > 0 && hotPad.rent,
          )
          .map((hotPad) => hotPad.rent / hotPad.square_feet),
      )
    : hotPadInformation;

  const hotPadContent = {
    title: `Median 3rd Party\nListings`,
    text: hotPadRentMedian ? `$${formatter(hotPadRentMedian)}` : '-',
    subText: hotPadAvgRentSqft
      ? `$${hotPadAvgRentSqft.toFixed(2)} per Sqft`
      : '-',
    // subText: !isBridgeTower
    //   ? hotPadAvgRentSqft
    //     ? `$${hotPadAvgRentSqft.toFixed(2)} per Sqft`
    //     : '-'
    //   : '',
  };

  const mlsRentValues = mlsLeaseInformation
    ? mlsLeaseInformation.map((mls) => mls.latestPrice)
    : mlsLeaseInformation;
  const mlsRentMedianMLS = mlsLeaseInformation
    ? calculateMedian(mlsRentValues)
    : mlsLeaseInformation;
  const mlsRentAverageMLS = mlsLeaseInformation
    ? calculateAverage(mlsRentValues)
    : mlsLeaseInformation;
  const mlsRentAveragePerSqftMLS = mlsLeaseInformation
    ? calculateAverage(
        mlsLeaseInformation.map((mls) => mls.latestPrice / mls.size),
      )
    : mlsLeaseInformation;

  const mlsListingLease = {
    title: `Median MLS\nListings`,
    text: mlsRentMedianMLS ? `$${formatter(mlsRentMedianMLS)}` : '-',
    subText: mlsRentAveragePerSqftMLS
      ? `$${mlsRentAveragePerSqftMLS.toFixed(2)} per Sqft`
      : '-',
    // subText: !isBridgeTower
    //   ? mlsRentAveragePerSqftMLS
    //     ? `$${mlsRentAveragePerSqftMLS.toFixed(2)} per Sqft`
    //     : '-'
    //   : '',
  };

  // const MLSAverageToAVMRatio =
  //   (subjectPropertyParcelData.rent - mlsRentAverageMLS) /
  //   subjectPropertyParcelData.rent;
  //   if (MLSAverageToAVMRatio > -0.05 && MLSAverageToAVMRatio < 0.05) {
  //     rentAdjustedBT = (subjectPropertyParcelData.rent + mlsRentAverageMLS) / 2;
  //   } else if (MLSAverageToAVMRatio > -0.1 && MLSAverageToAVMRatio < -0.05) {
  //     rentAdjustedBT = mlsRentAverageMLS;
  //   } else if (MLSAverageToAVMRatio > 0.05 && MLSAverageToAVMRatio < 0.1) {
  //     rentAdjustedBT = mlsRentAverageMLS * 1.02;
  //   }

  let rentAdjustedBT;
  let rentAdjustedFormula = '';
  let MLSMedianToAVMRatio = null;
  const rentAVM =
    subjectPropertyParcelData && subjectPropertyParcelData.rent
      ? subjectPropertyParcelData.rent
      : 'Missing Rent AVM';
  let preferredInputName = '';
  let diffText = '';

  if (
    !isEmpty(adjustedParams) &&
    adjustedParams.compingMode === 'intelligentComping'
  ) {
    rentAdjustedFormula = adjustedParams.adjustedRentFormula;
    if (rentAdjustedFormula) {
      const subjectPropertyAVM = subjectPropertyParcelData.rent || 0;
      switch (rentAdjustedFormula) {
        case '((AVM Rent + Median Rent) / 2)':
          rentAdjustedBT = (subjectPropertyAVM + mlsRentMedianMLS) / 2;
          break;
        case 'Median Rent':
          rentAdjustedBT = mlsRentMedianMLS;
          break;
        case 'Median Rent * 1.02':
          rentAdjustedBT = mlsRentMedianMLS * 1.02;
          break;
        case '((AVM Rent + Median SFR) / 2)':
          rentAdjustedBT = (subjectPropertyAVM + nsfrRentMedianSFR) / 2;
          break;
        case 'Median SFR':
          rentAdjustedBT = nsfrRentMedianSFR;
          break;
        case 'Median SFR * 1.02':
          rentAdjustedBT = nsfrRentMedianSFR * 1.02;
          break;
        case '((AVM Rent + Median 3rd Party) / 2)':
          rentAdjustedBT = (subjectPropertyAVM + hotPadRentMedian) / 2;
          break;
        case 'Median 3rd Party':
          rentAdjustedBT = hotPadRentMedian;
          break;
        case 'Median 3rd Party * 1.02':
          rentAdjustedBT = hotPadRentMedian * 1.02;
          break;
        case 'AVM Rent':
          rentAdjustedBT = subjectPropertyAVM;
          break;
        default:
          rentAdjustedBT = subjectPropertyAVM;
          break;
      }
    }
  } else {
    const getMarketRentValue = (preference) => {
      switch (preference) {
        case 'mls':
          if (
            mlsLeaseInformation.length >= 3 ||
            (mlsLeaseInformation.length < 3 &&
              mlsLeaseInformation.length > 0 &&
              NSFRInformation.length < 3 &&
              hotPadInformation.length < 3 &&
              rentAVM === 'Missing Rent AVM')
          ) {
            preferredInputName = 'MLS Median';
            return mlsRentMedianMLS;
          } else if (
            NSFRInformation.length >= 3 ||
            (mlsLeaseInformation.length === 0 &&
              NSFRInformation.length < 3 &&
              NSFRInformation.length > 0 &&
              hotPadInformation.length < 3 &&
              rentAVM === 'Missing Rent AVM')
          ) {
            preferredInputName = 'SFR Operator Median';
            return nsfrRentMedianSFR;
          } else if (
            hotPadInformation.length >= 3 ||
            (mlsLeaseInformation.length === 0 &&
              NSFRInformation.length === 0 &&
              hotPadInformation.length < 3 &&
              hotPadInformation.length > 0 &&
              rentAVM === 'Missing Rent AVM')
          ) {
            preferredInputName = '3rd Party Median';
            return hotPadRentMedian;
          } else if (rentAVM !== 'Missing Rent AVM') {
            preferredInputName = 'AVM';
            rentAdjustedFormula = 'AVM';
            return rentAVM;
          }
        case 'sfr':
          if (
            NSFRInformation.length >= 3 ||
            (NSFRInformation.length < 3 &&
              NSFRInformation.length > 0 &&
              mlsLeaseInformation.length < 3 &&
              hotPadInformation.length < 3 &&
              rentAVM === 'Missing Rent AVM')
          ) {
            preferredInputName = 'SFR Operator Median';
            return nsfrRentMedianSFR;
          } else if (
            mlsLeaseInformation.length >= 3 ||
            (NSFRInformation.length === 0 &&
              mlsLeaseInformation.length < 3 &&
              mlsLeaseInformation.length > 0 &&
              hotPadInformation.length < 3 &&
              rentAVM === 'Missing Rent AVM')
          ) {
            preferredInputName = 'MLS Median';
            return mlsRentMedianMLS;
          } else if (
            hotPadInformation.length >= 3 ||
            (NSFRInformation.length === 0 &&
              mlsLeaseInformation.length === 0 &&
              hotPadInformation.length < 3 &&
              hotPadInformation.length > 0 &&
              rentAVM === 'Missing Rent AVM')
          ) {
            preferredInputName = '3rd Party Median';
            return hotPadRentMedian;
          } else if (rentAVM !== 'Missing Rent AVM') {
            preferredInputName = 'AVM';
            rentAdjustedFormula = 'AVM';
            return rentAVM;
          }
        case 'hotpads':
          if (
            hotPadInformation.length >= 3 ||
            (hotPadInformation.length < 3 &&
              hotPadInformation.length > 0 &&
              mlsLeaseInformation.length < 3 &&
              NSFRInformation.length < 3 &&
              rentAVM === 'Missing Rent AVM')
          ) {
            preferredInputName = '3rd Party Median';
            return hotPadRentMedian;
          } else if (
            mlsLeaseInformation.length > 3 ||
            (hotPadInformation.length === 0 &&
              mlsLeaseInformation.length < 3 &&
              mlsLeaseInformation.length > 0 &&
              NSFRInformation.length < 3 &&
              rentAVM === 'Missing Rent AVM')
          ) {
            preferredInputName = 'MLS Median';
            return mlsRentMedianMLS;
          } else if (
            NSFRInformation.length >= 3 ||
            (hotPadInformation.length === 0 &&
              mlsLeaseInformation.length === 0 &&
              NSFRInformation.length < 3 &&
              NSFRInformation.length > 0 &&
              rentAVM === 'Missing Rent AVM')
          ) {
            preferredInputName = 'SFR Operator Median';
            return nsfrRentMedianSFR;
          } else if (rentAVM !== 'Missing Rent AVM') {
            preferredInputName = 'AVM';
            rentAdjustedFormula = 'AVM';
            return rentAVM;
          }
        default:
          return 'Invalid preference';
      }
    };

    const getDifferenceRatio = (input) => {
      if (
        preferredInputName !== 'AVM' &&
        input &&
        !['Not enough comps', 'Invalid preference'].includes(input)
      ) {
        if (rentAVM !== 'Missing Rent AVM') {
          const differentRatio = (rentAVM - input) / rentAVM;
          return differentRatio;
        }
      }
      return null;
    };

    const preferredInput = getMarketRentValue(marketRentPreference);
    const differentRatio = getDifferenceRatio(
      preferredInput,
      marketRentPreference,
    );

    if (differentRatio != null) {
      switch (true) {
        case differentRatio && differentRatio >= 10 / 100:
        case differentRatio && differentRatio < -10 / 100:
        case differentRatio &&
          differentRatio >= -5 / 100 &&
          differentRatio < 5 / 100:
          rentAdjustedBT = (rentAVM + preferredInput) / 2;
          rentAdjustedFormula = `(AVM + ${preferredInputName}) / 2`;
          MLSMedianToAVMRatio = differentRatio;
          break;
        case differentRatio &&
          differentRatio >= -10 / 100 &&
          differentRatio < -5 / 100:
          rentAdjustedBT = preferredInput;
          rentAdjustedFormula = `${preferredInputName}`;
          MLSMedianToAVMRatio = differentRatio;
          break;
        case differentRatio &&
          differentRatio >= 5 / 100 &&
          differentRatio < 10 / 100:
          rentAdjustedBT = preferredInput * (1 + 2 / 100);
          rentAdjustedFormula = `${preferredInputName} * 1.02`;
          MLSMedianToAVMRatio = differentRatio;
          break;
        default:
          break;
      }
    } else {
      if (
        preferredInputName !== 'AVM' &&
        preferredInput &&
        !['Not enough comps', 'Invalid preference'].includes(preferredInput)
      ) {
        if (rentAVM === 'Missing Rent AVM' && preferredInput) {
          rentAdjustedBT = preferredInput;
          rentAdjustedFormula = preferredInputName;
          MLSMedianToAVMRatio = null;
        } else {
          rentAdjustedFormula = 'Cannot be calculated';
        }
      } else if (preferredInputName === 'AVM') {
        rentAdjustedBT = rentAVM;
        rentAdjustedFormula = preferredInputName;
        MLSMedianToAVMRatio = null;
      }
    }

    // const rentAdjusted = isBridgeTower && {

    diffText = 'Diff between rent AVM and ';
    if (marketRentPreference === 'mls') {
      diffText += 'MLS';
    } else if (marketRentPreference === 'hotpads') {
      diffText += '3rd Party';
    } else if (marketRentPreference === 'nsfr') {
      diffText += 'SFR Operator';
    }
  }

  const rentAdjusted = {
    title: 'Market\nRent',
    text: rentAdjustedBT
      ? `$${formatter(rentAdjustedBT)}`
      : mlsRentAverageMLS && subjectPropertyParcelData.rent
      ? 'Manual Review'
      : '-',
    subText:
      MLSMedianToAVMRatio != null
        ? `Diff: ${Math.abs(MLSMedianToAVMRatio * 100).toFixed(1)}%`
        : '-',
    formula: rentAdjustedFormula,
    diffText: diffText,
  };

  // const allAvgValues = [];
  // const allSqftValues = [];

  // subjectPropertyParcelData.rent &&
  //   allAvgValues.push(subjectPropertyParcelData.rent);
  // subjectPropertyParcelData.total_area_sq_ft &&
  //   allSqftValues.push(
  //     subjectPropertyParcelData.rent /
  //       subjectPropertyParcelData.total_area_sq_ft,
  //   );
  // nsfrRentAverageNationalOperators &&
  //   allAvgValues.push(nsfrRentAverageNationalOperators);
  // nsfrRentAveragePerSqftNationalOperators &&
  //   allSqftValues.push(nsfrRentAveragePerSqftNationalOperators);
  // hotPadRentAverage && allAvgValues.push(hotPadRentAverage);
  // hotPadAvgRentSqft && allSqftValues.push(hotPadAvgRentSqft);
  // mlsRentAverageMLS && allAvgValues.push(mlsRentAverageMLS);
  // mlsRentAveragePerSqftMLS && allSqftValues.push(mlsRentAveragePerSqftMLS);

  // const avgAll = calculateAverage(allAvgValues);
  // const sqftAll = calculateAverage(allSqftValues);

  // const avgAllMethods = isCGC && {
  //   title: 'Avg.\n All Methods',
  //   text: avgAll ? `$${formatter(avgAll)}` : '-',
  //   subText: sqftAll ? `$${sqftAll.toFixed(2)} per Sqft` : '-',
  // };

  const leaseContent = [];
  crimeScore && leaseContent.push(crimeScore);
  if (!userGroup.includes('Sunroom')) {
    leaseContent.push(subjectPropertyRentAVM);
  }
  leaseContent.push(mlsListingLease);
  leaseContent.push(nationalSFR);
  leaseContent.push(hotPadContent);
  rentAdjusted && leaseContent.push(rentAdjusted);
  // avgAllMethods && leaseContent.push(avgAllMethods);

  const nearbyLeaseSummaryContent = {
    rowName: 'Nearby Lease Summary',
    content: leaseContent,
  };

  return nearbyLeaseSummaryContent;
};

export const createNearbySaleSummaryContent = (
  userGroup,
  subjectPropertyParcelData,
  mlsSaleInformation,
  adjustedParams,
) => {
  const isBridgeTower = userGroup && userGroup.includes('BridgeTower');
  const isCGC = userGroup && userGroup.includes('CommonGroundCapital');
  const salesContent = [];

  const crimeScoreSales = isCGC && {
    title: 'Crime\nScore',
    text: `${subjectPropertyParcelData.score_crime}`,
    subText: '',
  };

  const subjectPropertySalesAVM = {
    title: 'Subject Property\nSales AVM',
    text: subjectPropertyParcelData.sales
      ? `${'$' + formatter(subjectPropertyParcelData.sales)}`
      : '-',
    subText:
      subjectPropertyParcelData.sales &&
      subjectPropertyParcelData.total_area_sq_ft
        ? `$${(
            subjectPropertyParcelData.sales /
            subjectPropertyParcelData.total_area_sq_ft
          ).toFixed(2)} per Sqft`
        : '-',
  };

  console.log('mlsSaleInformation: ', mlsSaleInformation);

  console.log('INNER 1');

  const mlsSalesValues = mlsSaleInformation
    ? mlsSaleInformation.map((mls) => mls.latestPrice)
    : mlsSaleInformation;
  const mlsSalesMedianMLS = mlsSaleInformation
    ? calculateMedian(mlsSalesValues)
    : mlsSaleInformation;
  const mlsSalesAverageMLS = mlsSaleInformation
    ? calculateAverage(mlsSalesValues)
    : mlsSaleInformation;
  const mlsSalesAveragePerSqftMLS = mlsSaleInformation
    ? calculateAverage(
        mlsSaleInformation.map((mls) => mls.latestPrice / mls.size),
      )
    : mlsSaleInformation;

  console.log('INNER 2');

  const mlsListingSale = {
    title: `Median MLS\nListings`,
    text: mlsSalesMedianMLS ? `$${formatter(mlsSalesMedianMLS)}` : '-',
    subText: mlsSalesAveragePerSqftMLS
      ? mlsSalesAveragePerSqftMLS
        ? `$${mlsSalesAveragePerSqftMLS.toFixed(2)} per Sqft`
        : '-'
      : '-',
    // subText: !isBridgeTower
    //   ? mlsSalesAveragePerSqftMLS
    //     ? `$${mlsSalesAveragePerSqftMLS.toFixed(2)} per Sqft`
    //     : '-'
    //   : '',
  };

  console.log('INNER 3');

  // const MLSAverageToAVMRatioSales =
  //   (subjectPropertyParcelData.sales - mlsSalesAverageMLS) /
  //   subjectPropertyParcelData.sales;
  // let salesAdjustedBT;
  // if (MLSAverageToAVMRatioSales > -0.05 && MLSAverageToAVMRatioSales < 0.05) {
  //   salesAdjustedBT =
  //     (subjectPropertyParcelData.sales + mlsSalesAverageMLS) / 2;
  // } else if (
  //   MLSAverageToAVMRatioSales > -0.1 &&
  //   MLSAverageToAVMRatioSales < -0.05
  // ) {
  //   salesAdjustedBT = mlsSalesAverageMLS;
  // } else if (
  //   MLSAverageToAVMRatioSales > 0.05 &&
  //   MLSAverageToAVMRatioSales < 0.1
  // ) {
  //   salesAdjustedBT = mlsSalesAverageMLS * 1.02;
  // }

  let salesAdjustedBT;
  let salesAdjustedFormula = '';
  let MLSMedianToAVMRatio = null;
  const salesAVM =
    subjectPropertyParcelData && subjectPropertyParcelData.sales
      ? subjectPropertyParcelData.sales
      : 'Missing Sales AVM';
  let preferredInputName = '';

  console.log('INNER 4 ', salesAVM);
  if (
    !isEmpty(adjustedParams) &&
    adjustedParams.compingMode === 'intelligentComping'
  ) {
    console.log('INNER 5');

    salesAdjustedFormula = adjustedParams.adjustedSalesFormula;

    console.log('salesAdjustedFormula: ', salesAdjustedFormula);
    if (salesAdjustedFormula) {
      const subjectPropertyAVM = subjectPropertyParcelData.sales || 0;
      switch (salesAdjustedFormula) {
        case '((AVM Sales + Median Sales) / 2)':
          salesAdjustedBT = (subjectPropertyAVM + mlsSalesMedianMLS) / 2;
          break;
        case 'Median Sales':
          salesAdjustedBT = mlsSalesMedianMLS;
          break;
        case 'Median Sales * 1.02':
          salesAdjustedBT = mlsSalesMedianMLS * 1.02;
          break;
        default:
          salesAdjustedBT = subjectPropertyAVM;
          break;
      }
    }

    console.log('INNER 6');
  } else {
    const getDifferentRatio = () => {
      if (salesAVM != 'Missing Sales AVM') {
        if (mlsSaleInformation.length >= 3) {
          const differentRatio = (salesAVM - mlsSalesMedianMLS) / salesAVM;
          return differentRatio;
        } else {
          return 'Not enough comps';
        }
      } else if (salesAVM == 'Missing Sales AVM' && mlsSalesMedianMLS) {
        return 'Use MLS median when AVM is missing';
      } else {
        salesAdjustedFormula = 'Cannot be calculated';
        return;
      }
    };

    const differentRatio = getDifferentRatio();

    switch (true) {
      case differentRatio >= 5 / 100:
      case differentRatio < -5 / 100:
      case differentRatio >= -2.5 / 100 && differentRatio < 2.5 / 100:
        salesAdjustedBT = (salesAVM + mlsSalesMedianMLS) / 2;
        salesAdjustedFormula = `(AVM + MLS Median) / 2`;
        MLSMedianToAVMRatio = differentRatio;
        break;
      case differentRatio >= -10 / 100 && differentRatio < -5 / 100:
        salesAdjustedBT = mlsSalesMedianMLS;
        salesAdjustedFormula = `MLS Median`;
        MLSMedianToAVMRatio = differentRatio;
        break;
      case differentRatio >= 5 / 100 && differentRatio < 10 / 100:
        salesAdjustedBT = mlsSalesMedianMLS * (1 + 2 / 100);
        salesAdjustedFormula = `MLS Median * 1.02`;
        MLSMedianToAVMRatio = differentRatio;
        break;
      case differentRatio === 'Not enough comps':
        salesAdjustedBT = salesAVM;
        salesAdjustedFormula = 'AVM';
      case differentRatio === 'Use MLS median when AVM is missing':
        salesAdjustedBT = mlsSalesMedianMLS;
        salesAdjustedFormula = 'MLS Median';
      default:
        break;
    }
    console.log('differentRatio: ', differentRatio);
    console.log('mlsSalesMedianMLS: ', mlsSalesMedianMLS);
    console.log('salesAdjustedBT: ', salesAdjustedBT);
  }

  console.log('INNER 7');

  const salesAdjusted = {
    title: 'Market\nValue',
    text: salesAdjustedBT
      ? `$${formatter(salesAdjustedBT)}`
      : mlsSalesAverageMLS && subjectPropertyParcelData.sales
      ? 'Manual Review'
      : '-',
    subText:
      subjectPropertyParcelData.sales &&
      mlsSalesAverageMLS &&
      MLSMedianToAVMRatio
        ? `Diff: ${Math.abs(MLSMedianToAVMRatio * 100).toFixed(1)}%`
        : '-',
    formula: salesAdjustedFormula,
  };

  // const allAvgValues = [];
  // const allSqftValues = [];

  // subjectPropertyParcelData.sales &&
  //   allAvgValues.push(subjectPropertyParcelData.sales);
  // subjectPropertyParcelData.total_area_sq_ft &&
  //   allSqftValues.push(
  //     subjectPropertyParcelData.sales /
  //       subjectPropertyParcelData.total_area_sq_ft,
  //   );

  // mlsSalesAverageMLS && allAvgValues.push(mlsSalesAverageMLS);
  // mlsSalesAveragePerSqftMLS && allSqftValues.push(mlsSalesAveragePerSqftMLS);

  // const avgAllSales = calculateAverage(allAvgValues);
  // const sqftAllSales = calculateAverage(allSqftValues);

  // const avgAllMethodSales = isCGC && {
  //   title: 'Avg.\n All Methods',
  //   text: avgAllSales ? `$${formatter(avgAllSales)}` : '-',
  //   subText: sqftAllSales ? `$${sqftAllSales.toFixed(2)} per Sqft` : '-',
  // };

  crimeScoreSales && salesContent.push(crimeScoreSales);
  if (!userGroup.includes('Sunroom')) {
    salesContent.push(subjectPropertySalesAVM);
  }
  salesContent.push(mlsListingSale);
  salesAdjusted && salesContent.push(salesAdjusted);
  // avgAllMethodSales && salesContent.push(avgAllMethodSales);

  const nearbySaleSummaryContent = {
    rowName: 'Nearby Sale Summary',
    content: salesContent,
  };

  return nearbySaleSummaryContent;
};

export const createBTOwnedContent = (userGroup, btOwnedPropertyInformation) => {
  const isBridgeTower = userGroup && userGroup.includes('BridgeTower');

  const btOwnedContent = {
    rowName: 'Properties Owned by Bridge Tower',
    rowStat: [
      { label: 'Total', content: `${btOwnedPropertyInformation.length}` },
    ],
    tableData: isBridgeTower ? btOwnedPropertyInformation : [],
  };

  return btOwnedContent;
};

export const createNSFRContent = (userGroup, NSFRInformation) => {
  const isBridgeTower = userGroup && userGroup.includes('BridgeTower');

  const nsfrRentValues = NSFRInformation.map((nsfr) => nsfr.rent);
  const nsfrRentMedianSFR = calculateMedian(nsfrRentValues);
  const nsfrRentAverageNationalOperators = calculateAverage(nsfrRentValues);

  const nsfrContent = {
    rowName: 'National SFR Operators Listings',
    rowStat: [
      { label: 'Total', content: `${NSFRInformation.length}` },
      {
        label: 'Median Rent',
        content: `$${formatter(nsfrRentMedianSFR)}`,
      },
    ],
    tableData: NSFRInformation,
  };

  return nsfrContent;
};

export const createHotPadContent = (userGroup, hotPadInformation) => {
  const isBridgeTower = userGroup && userGroup.includes('BridgeTower');

  const hotPadRentValues = hotPadInformation.map((hotPod) => hotPod.rent);
  const hotPadRentMedian = calculateMedian(hotPadRentValues);
  const hotPadRentAverage = calculateAverage(hotPadRentValues);

  const hotPadContent = {
    rowName: 'Rental Listings from 3rd Party',
    rowStat: [
      { label: 'Total', content: `${hotPadInformation.length}` },
      {
        label: 'Median Rent',
        content: `$${formatter(hotPadRentMedian)}`,
      },
    ],
    tableData: hotPadInformation,
  };

  return hotPadContent;
};

export const createMLSLeaseContent = (userGroup, mlsLeaseInformation) => {
  const isBridgeTower = userGroup && userGroup.includes('BridgeTower');

  const mlsRentValues = mlsLeaseInformation.map((mls) => mls.latestPrice);
  const mlsRentMedianMLS = calculateMedian(mlsRentValues);
  const mlsRentAverageMLS = calculateAverage(mlsRentValues);

  const mlsLeaseContent = {
    rowName: 'MLS (Lease)',
    rowStat: [
      { label: 'Total', content: `${mlsLeaseInformation.length}` },
      {
        label: 'Median Rent',
        content: `$${formatter(mlsRentMedianMLS)}`,
      },
    ],
    tableData: mlsLeaseInformation,
  };

  return mlsLeaseContent;
};

export const createMLSSaleContent = (userGroup, mlsSaleInformation) => {
  const isBridgeTower = userGroup && userGroup.includes('BridgeTower');

  const mlsSalesValues = mlsSaleInformation.map((mls) => mls.latestPrice);
  const mlsSalesMedianMLS = calculateMedian(mlsSalesValues);
  const mlsSalesAverageMLS = calculateAverage(mlsSalesValues);

  const mlsSaleContent = {
    rowName: 'MLS (Sales)',
    rowStat: [
      { label: 'Total', content: `${mlsSaleInformation.length}` },
      {
        label: 'Median Sale',
        content: `$${formatter(mlsSalesMedianMLS)}`,
      },
    ],
    tableData: mlsSaleInformation,
  };

  return mlsSaleContent;
};

export const createMultiFamilyContent = (userGroup, multiFamilyInformation) => {
  const isBridgeTower = userGroup && userGroup.includes('BridgeTower');
  const isDemoUser = userGroup && userGroup.includes('demo-users');
  const isCGC = userGroup && userGroup.includes('CommonGroundCapital');

  let multiFamilyContent = {};

  // if ((isBridgeTower || isDemoUser) && multiFamilyInformation.length > 0) {
  if (!isCGC && multiFamilyInformation.length > 0) {
    const totalRelevantUnitValues = multiFamilyInformation.map(
      (mf) => mf.total_relevant_units - mf.four_br_units,
    );
    const avail2BRValues = multiFamilyInformation.map((mf) => mf.two_br_avail);
    const rent2BRValues = multiFamilyInformation.map(
      (mf) => mf.two_br_rent * mf.two_br_avail,
    );
    const avail3BRValues = multiFamilyInformation.map(
      (mf) => mf.three_br_avail,
    );
    const rent3BRValues = multiFamilyInformation.map(
      (mf) => mf.three_br_rent * mf.three_br_avail,
    );

    const totalRelevantUnitSum = totalRelevantUnitValues.reduce(
      (acc, curr) => acc + curr,
    );
    const avail2BRSum = avail2BRValues.reduce((acc, curr) => acc + curr);
    const rent2BRAvg =
      rent2BRValues.reduce((acc, curr) => acc + curr) / avail2BRSum;
    const avail3BRSum = avail3BRValues.reduce((acc, curr) => acc + curr);
    const rent3BRAvg =
      rent3BRValues.reduce((acc, curr) => acc + curr) / avail3BRSum;

    multiFamilyContent = {
      rowName: 'Multi-Family',
      rowStat: [
        {
          label: 'Total Relevant Units',
          content: `${totalRelevantUnitSum ? totalRelevantUnitSum : '-'}`,
        },
        {
          label: '2BR Avail.',
          content: `${avail2BRSum ? avail2BRSum : '-'}`,
        },
        {
          label: '2BR Avg.',
          content: `$${rent2BRAvg ? formatter(rent2BRAvg) : '-'}`,
        },
        {
          label: '3BR Avail.',
          content: `${avail3BRSum ? avail3BRSum : '-'}`,
        },
        {
          label: '3BR Avg.',
          content: `$${rent3BRAvg ? formatter(rent3BRAvg) : '-'}`,
        },
      ],
      // tableData: isBridgeTower || isDemoUser ? multiFamilyInformation : [],
      tableData: multiFamilyInformation,
    };
  }
  return multiFamilyContent;
};

export const createNewConstructionsContent = (newConstructionsData) => {
  const homePriceValues = newConstructionsData.map((home) => home.price);
  const medianHomePriceValues = calculateMedian(homePriceValues);

  const newConstructionsContent = {
    rowName: 'New Construction Homes',
    rowStat: [
      { label: 'Total', content: `${newConstructionsData.length}` },
      {
        label: 'Median Rent',
        content: `$${formatter(medianHomePriceValues)}`,
      },
    ],
    tableData: newConstructionsData,
  };

  return newConstructionsContent;
};

export const createLocationScoreContent = (
  subjectPropertyParcelData,
  demographics,
) => {
  if (!subjectPropertyParcelData || !demographics) return;

  return {
    school: [
      ['Elementary', subjectPropertyParcelData.elementary || 'N/A'],
      ['Middle', subjectPropertyParcelData.middle || 'N/A'],
      ['High', subjectPropertyParcelData.highschool || 'N/A'],
    ],
    risk: [
      [
        'Flood Zone',
        demographics.fld_zone
          ? demographics.fld_zone === 'N'
            ? 'No'
            : `Yes - ${demographics.fld_zone}`
          : 'N/A',
      ],
      ['Crime Score', demographics.crime_score || 'N/A'],
    ],
    demographics: [
      [
        'Median HH Income',
        `${
          demographics.medianhhincome
            ? `$${formatter(demographics.medianhhincome)}`
            : 'N/A'
        }`,
      ],
      [
        '5-Year Population Growth',
        `${
          demographics.fiveyearpopgrowth
            ? `${Math.round(demographics.fiveyearpopgrowth)}%`
            : 'N/A'
        }`,
      ],
      [
        '5-Year Income Growth',
        `${
          demographics.fiveyearincomegrowth
            ? `${Math.round(demographics.fiveyearincomegrowth)}%`
            : 'N/A'
        }`,
      ],
      [
        "Bachelor's and Above",
        `${
          demographics.bachelorsandabove
            ? `${Math.round(demographics.bachelorsandabove)}%`
            : 'N/A'
        }`,
      ],
    ],
  };
};

const getScreenshotDimensions = (size, imgWidth, imgHeight) => {
  let newHeight, newWidth, xStart, yStart;
  if (imgHeight > imgWidth) {
    newHeight = imgHeight * (size / imgWidth);
    newWidth = size;
    xStart = 0;
    yStart = -(newHeight / 2 - size / 2);
  } else if (imgWidth > imgHeight) {
    newWidth = imgWidth * (size / imgHeight);
    newHeight = size;
    yStart = 0;
    xStart = -(newWidth / 2 - size / 2);
  } else {
    newWidth = size;
    newHeight = size;
    xStart = 0;
    yStart = 0;
  }
  return { xStart, yStart, width: newWidth, height: newHeight };
};

export const takeMapBoxScreenshot = (map, size) => {
  // https://github.com/mapbox/mapbox-gl-js/issues/2766
  return new Promise(function (resolve, reject) {
    map.once('render', async function () {
      // const imgSrc = map.getCanvas().toDataURL();

      const zoomBtns = document.querySelector(`.mapboxgl-ctrl-top-right`);
      zoomBtns.style.visibility = 'hidden';

      const imgSrc = await toPng(document.querySelector(`.mapboxgl-map`));
      // const imgSrc = map.getCanvas().toDataURL();

      zoomBtns.style.visibility = 'visible';

      // crop image
      const img = new Image();
      img.src = imgSrc;
      img.onload = () => {
        // const size = img.width; // height and width of crop image
        // const size = 500; // height and width of crop image

        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = size;
        canvas.height = size;
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
        // ctx.globalCompositeOperation = 'darken';
        ctx.globalCompositeOperation = 'multiply';

        const dim = getScreenshotDimensions(size, img.width, img.height);
        ctx.drawImage(img, dim.xStart, dim.yStart, dim.width, dim.height);

        resolve(canvas.toDataURL());
      };

      // resolve(map.getCanvas().toDataURL());
    });
    /* trigger render */
    map.setBearing(map.getBearing());
  });
};
