export const calculateMedian = (values: number[]): number => {
  if (values.length === 0) {
    return 0;
  } else {
    values.sort((a, b) => a - b);

    const half = Math.floor(values.length / 2);

    if (values.length % 2) {
      return values[half];
    }

    return (values[half - 1] + values[half]) / 2.0;
  }
};

export const calculateAverage = (
  numbers: Array<number | null | undefined>,
): number => {
  // remove nulls/undefined and non-finite values (NaN/Infinity)
  const filteredNumbers = numbers.filter(
    (n): n is number => typeof n === 'number' && Number.isFinite(n),
  );
  if (filteredNumbers.length === 0) return 0;
  return filteredNumbers.reduce((a, b) => a + b, 0) / filteredNumbers.length;
};
