import userGroupAccess from '@/userGroupAccess.json';
import { default as turf_centerOfMass } from '@turf/center-of-mass';
import { default as turf_distance } from '@turf/distance';
import { point, polygon } from '@turf/helpers';
import allMetros from '../cityCodes.json';
import allMetrosWithMLSData from '../components/Filters/proFormaPropertySubTypesByMetro.json';
import { HOA_FEE_COLOR, OWNER_COLOR } from '../constants';

const getAllMetrosZIPCodes = () => {
  const allMetrosZIPCodesRaw = [];
  allMetros.forEach((metro) => {
    if (metro?.ZIPCode) {
      allMetrosZIPCodesRaw.push(...metro.ZIPCode);
    }
  });
  const uniqueValues = [...new Set(allMetrosZIPCodesRaw)];
  return uniqueValues;
};

// use 'realtrac' instead of 'nashville':
// getMLSPopupImages
// getBatchMLSPropertyImages

// use metro names instead of/in addition to city codes:
// 2 MLS image APIs
// getNewHouseAVMRentMLData, getNewHouseAVMSalesMLData
// saveSelectedComps

export const getMetroNameForParam = (MLSData, isRealtrac) => {
  if (MLSData && Object.hasOwn(MLSData, 'metro') && MLSData.metro) {
    // if metro field is available in MLS data, use it
    const metroName = convertMetroNameForParam(MLSData.metro, isRealtrac);
    return metroName;
  } else if (MLSData && MLSData?.cityCode) {
    // if metro field is not available in MLS data, use city code to find metro
    const cityCode = MLSData.cityCode || MLSData.city_code || MLSData.citycode;
    const metroObject = allMetrosWithMLSData.find(
      (item) => item.cityCode === cityCode,
    );
    if (metroObject) {
      const metroName = convertMetroNameForParam(metroObject.metro, isRealtrac);
      return metroName;
    } else {
      console.log('metro not found in getMetroName()', MLSData);
      return '';
    }
  } else if (
    MLSData &&
    (MLSData?.zipCode ||
      MLSData?.zipcode ||
      MLSData?.postalCode ||
      MLSData?.postal_code)
  ) {
    const ZIPCode =
      MLSData.zipCode ||
      MLSData.zipcode ||
      MLSData.postalCode ||
      MLSData.postal_code;
    // if metro field is not available in MLS data, use zipcode to find metro
    const metroObject = getCurrentMetroObjectViaZIPCode(ZIPCode);
    if (metroObject) {
      const metroName = convertMetroNameForParam(metroObject.metro, isRealtrac);
      return metroName;
    } else {
      console.log('metro not found in getMetroName()', MLSData);
      return '';
    }
  } else {
    console.log('metro not found in getMetroName()', MLSData);
    return '';
  }
};

export const getCurrentMetroObjectViaZIPCode = (ZIPCode) => {
  if (ZIPCode) {
    const zipCodeFirst3Digits = Number(ZIPCode.substring(0, 3));
    const metroObject = allMetrosWithMLSData.find((item) =>
      item.zipcode.includes(zipCodeFirst3Digits),
    );
    console.log('metroObject', metroObject);
    if (metroObject) {
      return metroObject;
    } else {
      console.log(
        'metro not found in getCurrentMetroObjectViaZIPCode()',
        ZIPCode,
      );
      return '';
    }
  } else {
    console.log(
      'metro not found in getCurrentMetroObjectViaZIPCode()',
      ZIPCode,
    );
    return '';
  }
};

export const convertMetroNameForParam = (metro, isRealtrac) => {
  switch (metro) {
    case 'North Carolina':
      return 'carolina';
    case 'San Antonio':
      return 'sanantonio';
    case 'Nashville':
      return isRealtrac ? 'realtrac' : 'nashville';
    case 'Saint Louis':
      return 'saint_louis';
    case 'Charleston':
      return 'сharleston'; // TEMP: use cyrillic 'с' instead of latin 'c'
    default:
      return metro.toLowerCase();
  }
};

export const getMetroNameForParamViaZIPCode = (ZIPCode, isRealtrac) => {
  const metroObject = getCurrentMetroObjectViaZIPCode(ZIPCode);
  if (metroObject && metroObject.metro) {
    const metroName = convertMetroNameForParam(metroObject.metro, isRealtrac);
    return metroName;
  } else {
    console.log('metro not found in getMetroNameForParamViaZIPCode()', ZIPCode);
    return '';
  }
};

export const getCityCodeViaMetroName = (metro) => {
  if (metro) {
    const metroObject = allMetrosWithMLSData.find(
      (item) => item.metro === metro,
    );
    if (metroObject) {
      return metroObject.cityCode;
    } else {
      console.log('metro ' + value + ' not found in getCityCodeViaMetroName()');
      return '';
    }
  } else {
    console.log('metro ' + value + ' not found in getCityCodeViaMetroName()');
    return '';
  }
};

export const getCityCodeViaZIPCode = (ZIPCode) => {
  const metroObject = getCurrentMetroObjectViaZIPCode(ZIPCode);
  if (metroObject) {
    return metroObject.cityCode;
  } else {
    console.log('metro not found in getCityCodeViaZIPCode()', ZIPCode);
    return '';
  }
};

export const setPropertyOwnerColor = (property) => {
  if (property['owner_occupied_sl'] === 'Yes') {
    property.ownerColor = OWNER_COLOR.ownerOccupiedColor;
  } else {
    if (property.institution === 'AH4R') {
      property.ownerColor = OWNER_COLOR.AH4RColor;
    } else if (property.institution === 'Cerberus') {
      property.ownerColor = OWNER_COLOR.cerberusColor;
    } else if (property.institution === 'Invitation Homes') {
      property.ownerColor = OWNER_COLOR.invitationHomes;
    } else if (property.institution === 'Progress Residential') {
      property.ownerColor = OWNER_COLOR.progressResColor;
    } else if (property.institution === 'Other') {
      property.ownerColor = OWNER_COLOR.othersColor;
    } else {
      property.ownerColor = OWNER_COLOR.momAndPopColor;
    }
  }
  return property;
};

export const setHOAFeeColor = (property) => {
  switch (true) {
    case property.hoa_fees >= 1001:
      property.hoaColor = HOA_FEE_COLOR.haoFeeRange5Color;
      break;
    case property.hoa_fees >= 601:
      property.hoaColor = HOA_FEE_COLOR.haoFeeRange4Color;
      break;
    case property.hoa_fees >= 201:
      property.hoaColor = HOA_FEE_COLOR.haoFeeRange3Color;
      break;
    case property.hoa_fees > 0:
      property.hoaColor = HOA_FEE_COLOR.haoFeeRange2Color;
      break;
    default:
      property.hoaColor = HOA_FEE_COLOR.haoFeeRange1Color;
  }
  return property;
};

/**
 * Checks if a coordinate is within a specified distance
 * @param {Array}  coordinateA  Coordinates of a point
 * @param {Array}  coordinateB  Coordinates of a point
 * @param {number} distance     The distance to check for in meters
 * @return {boolean}  True if distance between two points are within distance
 */
export const isWithinDistance = ({ coordinateA, coordinateB, distance }) => {
  const pointA = point(coordinateA);
  const pointB = point(coordinateB);
  const options = { units: 'kilometers' };

  const measurement = turf_distance(pointA, pointB, options) * 1000;

  return measurement < distance;
};

export const withinSubscribedMetro = (
  zipcode,
  selectedUserGroup,
  metrosAllowedOnIndividualAccountLevel,
) => {
  if (!zipcode)
    return {
      isWithinSubscribedMetro: false,
      isWithinAnyMetro: false,
    }; // for when the user clicks outside the US

  // const code = Number(zipcode.substring(0, 3));

  // if (userGroup.includes('Nhimble')) {
  //   return atlantaZipCodes.includes(code) ? true : false;
  // }

  const resultCityCode = getCityCodeViaZIPCode(zipcode);
  let isWithinSubscribedMetro;
  if (['demo-CMA-DFW-only'].includes(selectedUserGroup)) {
    isWithinSubscribedMetro =
      metrosAllowedOnIndividualAccountLevel.includes(resultCityCode);
  } else {
    isWithinSubscribedMetro =
      userGroupAccess[selectedUserGroup] &&
      userGroupAccess[selectedUserGroup].metro.includes(resultCityCode);
  }

  // check whether the zipcode is within any metro with MLS data
  const isWithinAnyMetro = getAllMetrosZIPCodes().includes(
    Number(zipcode.substring(0, 3)),
  );

  return {
    isWithinSubscribedMetro,
    isWithinAnyMetro,
  };
};

export const polygonWithinMetro = async (
  polygonData,
  selectedUserGroup,
  metrosAllowedOnIndividualAccountLevel,
) => {
  if (polygonData && polygonData.length > 0) {
    const polyGeoJSON = polygon([polygonData]);
    const center = turf_centerOfMass(polyGeoJSON);

    const lng = center.geometry.coordinates[0];
    const lat = center.geometry.coordinates[1];
    const response = await (
      await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${lng},${lat}.json?access_token=pk.eyJ1Ijoic3hieGNoZW4iLCJhIjoiYjRhNWMyMmI0NzVjZjEzZjYyZGUzZDM0NmFhZTcyNjEifQ.-T2S1ZeAEBGxjC4rC0CZzA`,
      )
    ).json();

    let zipcode;

    for (let i = 0; i < response.features.length; i++) {
      const feature = response.features[i];
      if (feature.id.includes('postcode')) {
        zipcode = feature.text;
        break;
      }
    }

    return withinSubscribedMetro(
      zipcode,
      selectedUserGroup,
      metrosAllowedOnIndividualAccountLevel,
    );
  }
  return {
    isWithinSubscribedMetro: false,
    isWithinAnyMetro: false,
  };
};
