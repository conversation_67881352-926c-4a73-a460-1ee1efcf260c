import { notification } from 'antd';
import { extend } from 'umi-request';
// import router from 'umi/router';
// import * as Sentry from "@sentry/react";

const shouldShowErrorMessage =
  window.location.href.includes('localhost') ||
  window.location.href.includes('dev') ||
  window.location.href.includes('test');

// 异常处理程序
// error handling
const errorHandler = (error: any) => {
  const { response = {} } = error;
  const errortext = (response && response.statusText) || 'Network Exception';
  const { status, url } = response || {};

  // for /parcel 500 error when clicking on a place with no parcel
  // error response is not api response
  // console.log('error response', response);
  if (url && url.includes('/parcel?placekey') && status === 500) {
    return;
  }

  if (status && status === 401) {
    // const urlArr = url.split('/');
    // const lastContext = urlArr[urlArr.length - 1];
    // if (lastContext !== 'logout') {
    //   window.g_app._store.dispatch({
    //     type: 'login/logout',
    //   });
    // }
    return;
  }

  if (response && Object.keys(response).length > 0) {
    return response
      .json()
      .then((data: any) => {
        if (
          data &&
          data.message &&
          data.message.includes(
            'Too many parcels found. Please narrow your search.',
          )
        ) {
          return data;
        }

        if (
          shouldShowErrorMessage &&
          process.env.UMI_APP_SERVER_TYPE == 'prod'
        ) {
          if (data && data.message) {
            notification.error({
              message: `request error ${status}: ${url}`,
              description: data.message,
            });
          } else {
            notification.error({
              message: `request error ${status}: ${url}`,
              description: errortext,
            });
          }
        }
      })
      .catch((error: any) => {
        if (
          shouldShowErrorMessage &&
          process.env.UMI_APP_SERVER_TYPE == 'prod'
        ) {
          if (error.code) {
            notification.error({
              message: error.name,
              description: error.message,
            });
          }
          if ('stack' in error && 'message' in error) {
            if (
              !error.message.includes("Failed to execute 'json'") &&
              !error.message.includes('body stream already read')
            ) {
              notification.error({
                message: `request error: ${url}`,
                description: error.message,
              });
            }
          }
        }
      });
  } else {
    return null;
  }
  // environment should not be used
  if (
    status === 403 ||
    (status <= 504 && status >= 500) ||
    (status >= 404 && status < 422)
  ) {
    return;
  }
};

// 配置request请求时的默认参数
// configure default params for request
// AWS Cognito's response header doesn't has set-cookie
// instead it stores security tokens in localStorage
export const request = extend({
  errorHandler, // 默认错误处理 // default error handling
  // credentials: 'same-origin', // 默认请求是否带上cookie // whether to include cookie
});

// for Placekey API
export const requestPlacekey = extend({
  errorHandler,
  prefix: 'https://api.placekey.io/v1/placekey',
  method: 'post',
  timeout: 15000,
  headers: {
    Accept: 'application/json',
    apikey: 'lH3qT8LeZjhQiW16KaHvTXRDG6fMdVhP',
  },
});

// 若url中包含protected,则此接口可选择是否带token
// if url contains 'protected'
// you can choose whether to include token here
// export const customRequest = (url, parameter) => {
//   if (url.indexOf('private') > -1) {
//     const userInfoString = localStorage.getItem('signInUserSession');
//     let userInfo = null;
//     try {
//       userInfo = JSON.parse(userInfoString);
//       let Authorization = `Bearer ${userInfo.accessToken.jwtToken}`;
//       // if (parameter && parameter.accessToken.jwtToken) {
//       //   Authorization = `Bearer ${parameter.accessToken.jwtToken}`;
//       // }
//       return request(url, { headers: { Authorization }, ...parameter });
//     } catch (e) {
//       let Authorization = `Bearer`;
//       // if (parameter && parameter.accessToken.jwtToken) {
//       //   Authorization = `Bearer ${parameter.accessToken.jwtToken}`;
//       // }
//       return request(url, { headers: { Authorization }, ...parameter });
//     }
//   } else if (url.indexOf('protected') > -1) {
//     const userInfoString = localStorage.getItem('signInUserSession');
//     let userInfo = null;
//     userInfo = JSON.parse(userInfoString);
//     if (userInfo) {
//       const Authorization = `Bearer ${userInfo.accessToken.jwtToken}`;
//       return request(url, { headers: { Authorization }, ...parameter });
//     } else {
//       return request(url, parameter);
//     }
//   } else {
//     return request(url, parameter);
//   }
// };

// AWS Cognito stores tokens in localStorage by default
// above is obsolete

import { getUserToken } from '@/utils/auth';

export const customRequest = async (url: any, parameter: any) => {
  const accessToken = await getUserToken('access');

  const Authorization = `Bearer ${accessToken}`;
  return request(url, {
    ...parameter,
    headers: { Authorization, ...parameter.headers },
  });
};

// export default customRequest;
