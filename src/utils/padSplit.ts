export function calculateAvgRent(record: any) {
  return (
    (record.rooms_with_private_bath_price *
      record.active_rooms_with_private_bath +
      record.rooms_with_shared_bath_price *
        record.active_rooms_with_shared_bath) /
    (record.active_rooms_with_private_bath +
      record.active_rooms_with_shared_bath)
  );
}

export function transformNumber(input: number): number {
  // If the number is less than 0.05, return 0
  if (input < 0.09) {
    return 0;
  } else return Math.round(input * 10) / 10;
}
