export const capitalizeFirstLetter = (text: string): string => {
  if (text.length > 0) {
    return text.charAt(0).toUpperCase() + text.slice(1);
  }
  return text;
};

// re: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort
export const sortString = (a: string, b: string): number => {
  const nameA = a.toUpperCase(); // ignore upper and lowercase
  const nameB = b.toUpperCase(); // ignore upper and lowercase
  if (nameA < nameB) {
    return -1;
  }
  if (nameA > nameB) {
    return 1;
  }
  // names must be equal
  return 0;
};

export const parserNumberInput = (valueString: string): number => {
  if (valueString.slice(-1) === '.') {
    return +valueString.slice(0, -1) / 100;
  } else if (valueString.slice(0, 1) === '.') {
    return +('0' + valueString) / 100;
  } else {
    return +valueString / 100;
  }
};

export const formatterNumberInput = (value: number): string => {
  if (Number.isInteger(value * 100)) {
    return (value * 100).toFixed(0);
  } else if (Number.isInteger(value * 1000)) {
    return (value * 100).toFixed(1);
  } else {
    return (value * 100).toFixed(2);
  }
};

export const parseStringWithCommas = (value: number | string): string => {
  const valueString = value.toString();
  return valueString.replace(/,/g, '');
};

export const replaceSpecialCharactersInFileName = (
  fileName: string,
): string => {
  return fileName.replace(/[^a-zA-Z0-9()_-\s]/g, '_');
};
