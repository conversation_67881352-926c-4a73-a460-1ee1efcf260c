export const getStatusSFRFromStatusMLS = (statusMLS: string) => {
  switch (statusMLS) {
    case 'Closed':
      return 'Closed';
    case 'Active':
      return 'Active';
    case 'Pending':
      return 'Pending';
    case 'status':
      return 'exists';
    default:
      return 'Closed';
  }
};

// for prod; temp
// export const getStatusSFRFromStatusMLSProd = (statusMLS) => {
//   switch (statusMLS) {
//     case 'Closed':
//       return false;
//     case 'Active':
//       return true;
//     case 'status':
//       return 'exists';
//     default:
//       return null;
//   }
// };

// because the values of 'exists' are different between APIs for fetching SFR and HotPads
// this function is for converting 'exists' values for SFR to values for HotPads
// the input is the output of getStatusSFRFromStatusMLS()
// export const getStatusHotPadsFromStatusSFR = (statusSFR) => {
//   switch (statusSFR) {
//     case 'Closed':
//       return false;
//     case 'Active':
//       return true;
//     // case 'Pending':
//     //   return 'Pending';
//     case 'exists':
//       return 'exists';
//     default:
//       return null;
//   }
// };

export const getStatusHotPadsFromStatusMLS = (statusMLS: string) => {
  switch (statusMLS) {
    case 'Closed':
      return false;
    case 'Active':
      return true;
    case 'status':
      return 'exists';
    default:
      return null;
  }
};
