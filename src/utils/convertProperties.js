import { camelCase } from 'lodash';
import moment from 'moment';
import filterValuesDefault from '../components/Filters/filterValuesDefault.json';
import fieldNameConversions from './fieldNameConversions.json';

const dateFormat = 'YYYY-MM-DD';

export const convertFilterFromAPIToUI = (APIData) => {
  return {
    // ...APIData,
    priority: APIData.priority,
    buyBoxName: APIData.buybox_name,
    buyBoxId: APIData.id,
    cityCode: APIData.citycode,
    fundId: APIData.fund.id,
    floodZone: APIData.floodzone,
    lastStatus: APIData.last_status,
    // parking: APIData.parking,
    poolYN: APIData.poolyn,
    propertySubType: APIData.property_sub_type,
    status: APIData.status,
    minPrice: APIData.min_list_price,
    maxPrice: APIData.max_list_price,
    minBed: APIData.min_beds_total,
    maxBed: APIData.max_beds_total,
    minBath: APIData.min_baths_full,
    maxBath: APIData.max_baths_full,
    minSqft: APIData.min_sqft,
    maxSqft: APIData.max_sqft,
    minLotSize: APIData.min_lot_size,
    maxLotSize: APIData.max_lot_size,
    maxParking: APIData.max_parking,
    minYearBuilt: APIData.min_year_built,
    maxYearBuilt: APIData.max_year_built,
    elementary: APIData.min_elemen,
    middle: APIData.min_middle,
    high: APIData.min_high,
    maxCDOM: APIData.max_cdom,
    minCDOM: APIData.min_cdom,
    maxCrime: APIData.max_crime,
    minCrime: APIData.min_crime,
    minParking: APIData.min_parking,
  };
};

export const convertFilterFromUIToAPI = (UIData) => {
  return {
    // ...UIData,
    client_id: UIData.clientId,
    fund_id: UIData.fundId,
    priority: UIData.priority,
    buybox_name: UIData.buyBoxName,
    buybox_id: UIData.buyBoxId,
    citycode: UIData.cityCode,
    floodzone: UIData.floodZone,
    last_status: UIData.lastStatus,
    // parking: UIData.parking,
    poolyn: UIData.poolYN,
    property_sub_type: UIData.propertySubType,
    status: UIData.status,
    max_baths_full: UIData.maxBath,
    max_beds_total: UIData.maxBed,
    max_elemen: 10,
    max_high: 10,
    max_list_price: UIData.maxPrice,
    max_middle: 10,
    max_sqft: UIData.maxSqft,
    max_year_built: UIData.maxYearBuilt,
    max_cdom: UIData.maxCDOM,
    max_crime: UIData.maxCrime,
    max_parking: UIData.maxParking,
    min_baths_full: UIData.minBath,
    min_beds_total: UIData.minBed,
    min_elemen: UIData.elementary,
    min_high: UIData.high,
    min_list_price: UIData.minPrice,
    min_middle: UIData.middle,
    min_sqft: UIData.minSqft,
    min_year_built: UIData.minYearBuilt,
    min_cdom: UIData.minCDOM,
    min_crime: 1,
    min_parking: UIData.minParking,
  };
};

export const camelCaseData = (APIData) => {
  let dataCamelCased = {};
  for (const property in APIData) {
    dataCamelCased[camelCase(property)] = APIData[property];
  }
  // console.log('dataCamelCased', dataCamelCased);
  return dataCamelCased;
};

// convert non-snake-cased properties
// use after using camelCaseData
export const camelCaseNonSnakeCaseProperties = (data) => {
  let dataCamelCased = {};
  for (const property in data) {
    switch (property) {
      case 'citycode':
        dataCamelCased.cityCode = data.citycode;
        break;
      case 'buyboxId':
        dataCamelCased.buyBoxId = data.buyboxId;
        break;
      case 'elemen':
        dataCamelCased.elementary = data.elemen;
        break;
      case 'floodzone':
        dataCamelCased.floodZone = data.floodzone;
        break;
      case 'fullAddress':
        dataCamelCased.fullStreetAddress = data.fullAddress;
        break;
      case 'listingkey':
        dataCamelCased.listingKey = data.listingkey;
        break;
      case 'mlsnumber':
        dataCamelCased.mlsNumber = data.mlsnumber;
        break;
      case 'poolyn':
        dataCamelCased.poolYN = data.poolyn;
        break;
      default:
        dataCamelCased[property] = data[property];
        break;
    }
  }
  // console.log('dataCamelCased', dataCamelCased);
  return dataCamelCased;
};

export const convertBuyBoxDetailsFromAPIToUI = (APIData) => {
  const dataCamelCasedStep1 = camelCaseData(APIData);
  const dataCamelCasedStep2 =
    camelCaseNonSnakeCaseProperties(dataCamelCasedStep1);
  return dataCamelCasedStep2;
};

export const getSortByColumnNameForAPI = (columnKey) => {
  console.log('columnKey', columnKey);
  const targetItem = fieldNameConversions.find((item) => item.UI === columnKey);
  if (targetItem) {
    return targetItem.API;
  } else {
    return '';
  }
};

export const convertAdjustedParamsFromAPIToUI = (APIData, userGroup) => {
  if (APIData) {
    let convertedFromAPIData = {
      currentRadiusMile: APIData.distance,
      isDistrictFilterOn: APIData.isd && APIData.isd !== 'Any' ? true : false,
      currentStartMLS: APIData.startDate,
      currentEndMLS: APIData.endDate,
      minBeds: APIData.minbeds,
      maxBeds: APIData.maxbeds,
      relationBeds: 'between',
      minBaths: APIData.bath,
      maxBaths: 99,
      relationBaths: 'between',
      minSqft: APIData.minSqft,
      maxSqft: APIData.maxSqft,
      relationSqft: 'between',
      minLotSize: APIData.minLotSize,
      maxLotSize: APIData.maxLotSize,
      relationLotSize: 'between',
      minYearBuilt: APIData.minYearbuilt,
      maxYearBuilt: APIData.maxYearbuilt,
      relationYearBuilt: 'between',
      minCumulativeDaysOnMarket: 0,
      maxCumulativeDaysOnMarket: APIData.dom,
      relationCumulativeDaysOnMarket: 'between',
    };
    // if a value is null in API data
    // set it to default filter value
    for (const property in filterValuesDefault) {
      if (convertedFromAPIData[property] === null) {
        convertedFromAPIData[property] = filterValuesDefault[property];
      }
    }
    return convertedFromAPIData;
  } else {
    const currentStartMLSWithinDays = ['ILE'].includes(userGroup) ? 180 : 90;
    return {
      ...filterValuesDefault,
      selectedPoolAllowed: userGroup.includes('VentureREI') ? true : false,
      // currentRadiusMile: 0.5,
      isDistrictFilterOn: false,
      currentStartMLS: moment()
        .subtract(currentStartMLSWithinDays, 'days')
        .format(dateFormat),
      currentEndMLS: moment().format(dateFormat),
      relationBeds: 'between',
      relationBaths: 'between',
      relationSqft: 'between',
      relationYearBuilt: 'between',
      relationCumulativeDaysOnMarket: 'between',
    };
  }
};
