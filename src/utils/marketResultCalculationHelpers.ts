/**
 * Calculates the average of two values. If one of the values is zero, returns the other value.
 * If neither value is zero, returns their average.
 *
 * @param value1 - The first number to average.
 * @param value2 - The second number to average.
 * @returns The average of value1 and value2 or the non-zero value if one of them is zero.
 */
export const getAverageWithZeroValueCheck = (
  value1: number,
  value2: number,
): number => {
  // If the first value is zero, return the second value.
  if (value1 === 0) {
    return value2;
  }
  // If the second value is zero, return the first value.
  else if (value2 === 0) {
    return value1;
  }
  // If neither value is zero, calculate and return the average.
  else {
    return (value1 + value2) / 2;
  }
};
