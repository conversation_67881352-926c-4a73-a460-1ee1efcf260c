import { fetchAuthSession } from 'aws-amplify/auth';
import userGroupAccess from '../userGroupAccess.json';

// https://docs.amplify.aws/react/build-a-backend/auth/manage-user-session/#refreshing-sessions

export const getUserToken = async (type: 'id' | 'access') => {
  if (type !== 'id' && type !== 'access') throw new Error('Invalid token type');

  const { idToken, accessToken } = (await fetchAuthSession()).tokens || {};
  if (idToken && type === 'id') {
    return idToken.toString() || '';
  } else if (accessToken && type === 'access') {
    return accessToken.toString() || '';
  }
  return null;
};

export const getUsername = async () => {
  const { idToken } = (await fetchAuthSession()).tokens || {};
  return idToken!.payload['cognito:username'];
};

export const getUserEmail = async () => {
  const { idToken } = (await fetchAuthSession()).tokens || {};
  return idToken!.payload.email;
};

export const getUserGroup = async () => {
  const { idToken } = (await fetchAuthSession()).tokens || {};
  return idToken!.payload['cognito:groups'];
};

export const getSelectedUserGroup = (userGroup: Array<string>): string => {
  const selectedUserGroup: string | undefined = userGroup.find((group) =>
    Object.hasOwn(userGroupAccess, group),
  );
  console.log('getSelectedUserGroup - selectedUserGroup', selectedUserGroup);
  if (selectedUserGroup) {
    return selectedUserGroup;
  } else {
    return '';
  }
};

type TokenType = 'id' | 'access' | 'refresh';

/**
 * Returns the JWT token from local storage
 *
 * Disclaimer: This is to be used after the user has been authenticated
 * Amplify stores user credentials in local storage
 *
 * @param type {@link TokenType} - The type of token to get
 * @returns {string | null} - The JWT token or null if not found
 */
export const getTokenFromLocalStorage = (type: TokenType): string | null => {
  for (const key in localStorage) {
    // This will return the first instance, if multiple pools used, also check for user pool id in key
    if (
      key.startsWith('CognitoIdentityServiceProvider') &&
      key.endsWith(`${type}Token`)
    ) {
      const token = localStorage.getItem(key);
      return token;
    }
  }
  return null;
};
