import isEmpty from 'lodash.isempty';
import moment from 'moment';
import { formatter } from './money';
import {
  compareDate,
  removeAbnormalSqftAndRentValues,
  removeDuplicatedEarlierProperties,
} from './removeDuplicates';

const hasFilteredOutKeywords = ({ property, selectedUserGroup }) => {
  const getFilteredOutKeywords = () => {
    if (['ILE'].includes(selectedUserGroup)) {
      return [
        'condo',
        'apartment',
        'multi',
        'farm',
        'acreage',
        'remark',
        'mobile',
        'high rise',
        'hi-rise',
        'manufactured',
        'land',
        'stock',
        'modular',
        'loft',
        'garden',
        'studio',
        'dock',
        'mixed',
        'ranch',
        'hotel',
        'business',
        'timeshare',
        'retail',
        'boat',
        'commercial',
        'industrial',
        'rv',
        'church',
        'office',
      ];
    } else {
      return [];
    }
  };
  const filteredOutKeywords = getFilteredOutKeywords();
  let hasKeywords = filteredOutKeywords.some((keyword) => {
    if (property.propertysubtype) {
      return property.propertysubtype.toLowerCase().indexOf(keyword) !== -1;
    }
  });
  return hasKeywords;
};

export const processMLSProperties = ({
  response,
  // subjectPropertyAVM,
  // compingMode,
  // isLeaseMode,
  selectedUserGroup,
}) => {
  let currentMLSProperties = [];
  if (response && Array.isArray(response) && response.length > 0) {
    const responseWithDuplicatesRemoved = removeDuplicatedEarlierProperties(
      response,
      'fulladdress',
      'modificationtimestamp',
      compareDate,
    );
    // console.log('responseWithDuplicatesRemoved', responseWithDuplicatesRemoved);
    if (
      responseWithDuplicatesRemoved &&
      responseWithDuplicatesRemoved.length > 0
    ) {
      for (const property of responseWithDuplicatesRemoved) {
        // for ILE ONLY
        // remove properties whose subtype is condo, apartment, multi-family, farm, etc
        const hasKeywords = hasFilteredOutKeywords({
          property,
          selectedUserGroup,
        });
        if (!hasKeywords) {
          const propertyClone = { ...property };
          // add a latest price key for the price we'll be using
          // for closed listing, use 'closeprice'
          // for active listing, use 'currentprice'
          propertyClone.latestPrice =
            property?.status === 'Closed'
              ? property?.closeprice ?? property?.currentprice
              : property?.currentprice;
          // add avm to rent/sold price ratio
          // if (compingMode !== 'intelligentComping' && !isNaN(subjectPropertyAVM)) {
          //   if (isLeaseMode) {
          //     property.avmToRentPrice = property.latestPrice
          //       ? +subjectPropertyAVM / property.latestPrice
          //       : null;
          //   } else {
          //     property.avmToSoldPrice = property.latestPrice
          //       ? +subjectPropertyAVM / property.latestPrice
          //       : null;
          //   }
          // }
          // convert pool value to boolean
          // raw values for pool include 'false', 'true', and others
          switch (property.pool) {
            case 'true':
              propertyClone.pool = true;
              break;
            case 'false':
              propertyClone.pool = false;
              break;
            default:
              // pass the pool value along
              // could be 'null'
              propertyClone.pool = property.pool;
              break;
          }
          // convert lot size to acres
          propertyClone.area_acres = property.lot_size / 43560;
          currentMLSProperties.push(propertyClone);
        }
      }
      // console.log('process mls currentMLSProperties', currentMLSProperties);
    }
  }
  return currentMLSProperties;
};

export const processSFRProperties = ({ response, selectedUserGroup }) => {
  if (response && Array.isArray(response) && response.length > 0) {
    const responseWithDuplicatesRemoved = removeDuplicatedEarlierProperties(
      response,
      'address',
      'close_date',
      compareDate,
    );
    const responseWithFilteredOutKeywordsRemoved =
      responseWithDuplicatesRemoved.filter((property) => {
        return !hasFilteredOutKeywords({
          property,
          selectedUserGroup,
        });
      });
    return responseWithFilteredOutKeywordsRemoved;
  } else {
    return [];
  }
};

export const processMarketConditionResponse = (response) => {
  if (!response || isEmpty(response)) return {};
  return {
    active: response.active,
    activeMedianPSF: response.active_median_current_price_and_size_ratio
      ? (
          Math.round(
            response.active_median_current_price_and_size_ratio * 100,
          ) / 100
        ).toFixed(2)
      : 0,
    closed: response.closed,
    closedMedianPSF: response.closed_median_close_price_and_size_ratio
      ? (
          Math.round(response.closed_median_close_price_and_size_ratio * 100) /
          100
        ).toFixed(2)
      : 0,
    medianDOM: response.closed_dom_median
      ? +response.closed_dom_median.toFixed(0)
      : 0,
    monthsOfInventory: response.ratio
      ? Math.round(response.ratio * 100) / 100
      : 0,
  };
};

export const processMarketRentCompareResponse = (response) => {
  if (!response || response.length === 0) return [];

  if (response.length === 2) {
    const processedResponse = [];

    for (let i = 0; i < 3; i++) {
      const fairMarketRent =
        response?.[0]?.[`fair_market_rent_${i + 2}_bedrooms`];
      const avgClosePrice =
        response?.[1]?.[`avg_close_price_${i + 2}_bedrooms`];
      const closeCount = response?.[1]?.[`count_${i + 2}_bedrooms`];
      processedResponse.push({
        key: `bedroom_${i + 2}`,
        bedrooms: i + 2,
        fairMarketRent:
          fairMarketRent > 0 ? `$${formatter(fairMarketRent)}` : `$0`,
        avgClosePrice:
          avgClosePrice > 0 ? `$${formatter(avgClosePrice)}` : `$0`,
        closeCount: closeCount && closeCount > 0 ? closeCount : 0,
        ratio:
          fairMarketRent &&
          fairMarketRent > 0 &&
          avgClosePrice &&
          avgClosePrice > 0
            ? `${((avgClosePrice / fairMarketRent) * 100).toFixed(0)}%`
            : `0%`,
      });
    }

    return processedResponse;
  }

  return response;
};

export const processMLSListingSummaryChartResponse = (
  response,
  propertyType,
) => {
  if (!response || response.length === 0) return [];

  let priceDataPSF = [];
  let priceDataFull = [];
  let listClosedData = [];
  let ratioData = [];
  let domData = [];

  for (let i = response.length - 1; i >= 0; i--) {
    // if (!response[i].mlsListingSummary) continue;

    const { year, month, mlsListingSummary } = response[i];
    const date = moment(`${year}-${month}-15`).format('MMM YYYY');
    if (mlsListingSummary) {
      for (const key in mlsListingSummary) {
        const item = {
          date: date,
          value: mlsListingSummary[key],
          type: processMLSListingKey(key, propertyType),
        };
        if (
          key === 'active_median_current_price_and_size_ratio' ||
          key === 'closed_median_close_price_and_size_ratio'
        ) {
          priceDataPSF.push(item);
        } else if (
          key === 'closed_median_close_price' ||
          key === 'active_median_current_price'
        ) {
          priceDataFull.push(item);
        } else if (key === 'active' || key === 'closed') {
          listClosedData.push(item);
        } else if (key === 'ratio') {
          ratioData.push(item);
        } else if (key === 'closed_dom_median') {
          domData.push(item);
        }
      }
    } else {
      const getItem = (key) => ({
        date: date,
        value: null,
        type: processMLSListingKey(key, propertyType),
      });
      priceDataPSF.push(getItem('active_median_current_price_and_size_ratio'));
      priceDataPSF.push(getItem('closed_median_close_price_and_size_ratio'));
      priceDataFull.push(getItem('active_median_current_price'));
      priceDataFull.push(getItem('closed_median_close_price'));
      listClosedData.push(getItem('active'));
      listClosedData.push(getItem('closed'));
      ratioData.push(getItem('ratio'));
      domData.push(getItem('closed_dom_median'));
    }
  }

  // list prices should come before closed prices
  priceDataPSF = swapDataOrder(priceDataPSF);
  priceDataFull = swapDataOrder(priceDataFull);

  const priceDataPSFYoY = generateYearOnYearData(priceDataPSF);
  const priceDataFullYoY = generateYearOnYearData(priceDataFull);
  const listClosedDataYoY = generateYearOnYearData(
    [...listClosedData, ...ratioData].sort(
      (a, b) => moment(a.date, 'MMM YYYY') - moment(b.date, 'MMM YYYY'),
    ),
  );
  const domDataYoY = generateYearOnYearData(domData);

  return {
    priceChartData: {
      data: priceDataFull,
      psfData: priceDataPSF,
      yoy: priceDataFullYoY,
      psfYoY: priceDataPSFYoY,
    },
    listClosedChartData: {
      data: [listClosedData, ratioData],
      yoy: listClosedDataYoY,
    },
    domChartData: {
      data: domData,
      yoy: domDataYoY,
    },
  };

  // return { priceDataPSF, priceDataFull, listClosedData, domData };
};

const swapDataOrder = (data) => {
  const result = [];
  for (let i = 0; i < data.length; i += 2) {
    result.push(data[i + 1]);
    result.push(data[i]);
  }
  return result;
};

const generateYearOnYearData = (data) => {
  const result = [];

  for (let i = 0; i < data.length; i++) {
    const firstType = data[i].type;
    const firstDate = moment(data[i].date, 'MMM YYYY');
    firstDate.add(1, 'y');
    for (let j = i; j < data.length; j++) {
      const secondType = data[j].type;
      const secondDate = moment(data[j].date, 'MMM YYYY');

      if (firstDate.isSame(secondDate) && firstType === secondType) {
        const yearOnYearValue =
          data[i].value > 0 ? (data[j].value / data[i].value - 1) * 100 : 0;
        data[i].yearOnYearValue = yearOnYearValue;
        result.push({
          date: secondDate.format('MMM YYYY'),
          value: yearOnYearValue,
          type: `${firstType} YoY`,
        });
      }
    }
  }
  return result;
};

const processMLSListingKey = (key, propertyType) => {
  switch (key) {
    case 'active':
      return 'Active';
    case 'active_median_current_price':
      if (propertyType === 'Residential Lease') {
        return 'Active Median Rent';
      }
      return 'Active Median Price';
    case 'active_median_current_price_and_size_ratio':
      if (propertyType === 'Residential Lease') {
        return 'Active Median RSF';
      }
      return 'Active Median PSF';
    case 'closed':
      return 'Closed';
    case 'closed_median_close_price':
      if (propertyType === 'Residential Lease') {
        return 'Closed Median Rent';
      }
      return 'Closed Median Price';
    case 'closed_median_close_price_and_size_ratio':
      if (propertyType === 'Residential Lease') {
        return 'Closed Median RSF';
      }
      return 'Closed Median PSF';
    case 'closed_dom_median':
      return 'Median DOM';
    case 'ratio':
      return 'Months of Inventory';
    default:
      // console.log('key not found in processMLSListingKey');
      return '';
  }
};
