[{"API": "priority", "UI": "priority"}, {"API": "buybox_name", "UI": "buyBoxName"}, {"API": "citycode", "UI": "cityCode"}, {"API": "floodzone", "UI": "floodZone"}, {"API": "last_status", "UI": "lastStatus"}, {"API": "parking", "UI": "parking"}, {"API": "<PERSON><PERSON>", "UI": "poolYN"}, {"API": "property_sub_type", "UI": "propertySubType"}, {"API": "status", "UI": "status"}, {"API": "min_list_price", "UI": "minPrice"}, {"API": "max_list_price", "UI": "maxPrice"}, {"API": "min_beds_total", "UI": "minBed"}, {"API": "max_beds_total", "UI": "maxBed"}, {"API": "min_baths_full", "UI": "minBath"}, {"API": "max_baths_full", "UI": "maxBath"}, {"API": "min_sqft", "UI": "minSqft"}, {"API": "max_sqft", "UI": "maxSqft"}, {"API": "min_lot_size", "UI": "minLotSize"}, {"API": "max_lot_size", "UI": "maxLotSize"}, {"API": "min_year_built", "UI": "minYearBuilt"}, {"API": "max_year_built", "UI": "maxYearBuilt"}, {"API": "min_elemen", "UI": "elementary"}, {"API": "min_middle", "UI": "middle"}, {"API": "min_high", "UI": "high"}, {"API": "max_cdom", "UI": "maxCDOM"}, {"API": "min_cdom", "UI": "minCDOM"}, {"API": "max_crime", "UI": "maxCrime"}, {"API": "min_crime", "UI": "minCrime"}, {"API": "max_parking", "UI": "maxParking"}, {"API": "min_parking", "UI": "minParking"}, {"API": "offer_status", "UI": "offerStatus"}, {"API": "rets_id", "UI": "retsId"}, {"API": "buybox_id", "UI": "buyBoxId"}, {"API": "status_change_timestamp", "UI": "statusChangeTimestamp"}, {"API": "association_type", "UI": "associationType"}, {"API": "back_on_market_date", "UI": "backOnMarketDate"}, {"API": "baths_full", "UI": "bathsFull"}, {"API": "baths_half", "UI": "bathsHalf"}, {"API": "beds_total", "UI": "bedsTotal"}, {"API": "block", "UI": "block"}, {"API": "county_or_parish", "UI": "countyOrParish"}, {"API": "last_change_timestamp", "UI": "lastChangeTimestamp"}, {"API": "last_change_type", "UI": "lastChangeType"}, {"API": "last_list_price", "UI": "lastListPrice"}, {"API": "last_status", "UI": "lastStatus"}, {"API": "list_price", "UI": "listPrice"}, {"API": "listing_contract_date", "UI": "listingContractDate"}, {"API": "mlsnumber", "UI": "mlsNumber"}, {"API": "matrix_modifieddt", "UI": "matrixModifieddt"}, {"API": "number_of_stories", "UI": "numberOfStories"}, {"API": "off_market_date", "UI": "offMarketDate"}, {"API": "original_list_price", "UI": "originalListPrice"}, {"API": "parking_spaces_garage", "UI": "parkingSpacesGarage"}, {"API": "<PERSON><PERSON>", "UI": "poolYN"}, {"API": "property_sub_type", "UI": "propertySubType"}, {"API": "property_type", "UI": "propertyType"}, {"API": "sqft", "UI": "sqft"}, {"API": "status", "UI": "status"}, {"API": "year_built", "UI": "yearBuilt"}, {"API": "elemen", "UI": "elementary"}, {"API": "floodzone", "UI": "floodZone"}, {"API": "high", "UI": "high"}, {"API": "improvement_ratio", "UI": "improvementRatio"}, {"API": "middle", "UI": "middle"}, {"API": "crime_score", "UI": "crimeScore"}, {"API": "city", "UI": "city"}, {"API": "state", "UI": "state"}, {"API": "postal_code", "UI": "postalCode"}, {"API": "full_address", "UI": "fullStreetAddress"}, {"API": "latitude", "UI": "latitude"}, {"API": "longitude", "UI": "longitude"}, {"API": "placekey", "UI": "placekey"}, {"API": "listingkey", "UI": "listingKey"}, {"API": "list_agent_mui", "UI": "listAgentMui"}, {"API": "list_office_mui", "UI": "listOfficeMui"}, {"API": "citycode", "UI": "cityCode"}, {"API": "dispositions", "UI": "dispositions"}, {"API": "offer_sent", "UI": "offerSent"}, {"API": "avm_ratio", "UI": "avmRatio"}, {"API": "asking_ratio", "UI": "askingRatio"}, {"API": "imp_ratio", "UI": "impRatio"}, {"API": "ASC", "UI": "ascend"}, {"API": "DESC", "UI": "descend"}]