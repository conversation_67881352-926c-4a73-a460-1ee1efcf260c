// generate geofence param used in geocoding API calls
export const getGeofenceParamForGeocodingAPI = (userGroup) => {
  switch (true) {
    // case userGroup.includes('demo-CMA-DFW-only'):
    //   return 'DFW';
    case userGroup.includes('demo-CMA-Charlotte-only'):
      return 'Charlotte';
    default:
      return '';
  }
};

export const getShareableURLSubdomain = (userGroup) => {
  switch (true) {
    case userGroup.includes('BridgeTower'):
      return 'bt';
    case userGroup.includes('CommonGroundCapital'):
      return 'cgc';
    case userGroup.includes('Avanta'):
      return 'avanta';
    case userGroup.includes('demo-CMA-DFW-only'):
      return 'cma-dfw-trial';
    case userGroup.includes('demo-CMA-Charlotte-only'):
      return 'cma-charlotte-trial';
    default:
      return 'cma';
  }
};

export const getOwnPropertyTitleText = (userGroup) => {
  switch (true) {
    case userGroup.includes('BridgeTower'):
      return 'Properties Owned by Bridge Tower';
    default:
      return 'Own Properties';
  }
};
