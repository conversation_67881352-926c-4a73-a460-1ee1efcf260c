export interface parcelData {
  area_acres: number;
  baths: number;
  beds_count: number;
  rent: number;
  sales: number;
  total_area_sq_ft: number;
  year_built: number;
}

export interface mlsData {
  baths_full: number;
  beds_total: number;
  // latitude: string;
  // list_price: number;
  // longitude: string;
  // market_rent: string;
  // market_sales: string;
  // property_sub_type: string;
  sqft: number;
  year_built: number;
}

export interface portfolioPropertyData {
  baths: number;
  beds: number;
  // lat: number;
  // lng: number;
  // meta: {
  //   rent: number;
  // }
  // rent: number;
  // sales: number;
  sqft: number;
  yearbuilt: number;
}

export interface mlsCompData {
  bath: number;
  bed: number;
  cdom: number;
  closeprice: number;
  currentprice: number;
  // distance: number;
  // dom: number;
  garage: number;
  // latitude: number;
  // longitude: number;
  lot_size: number;
  // originalprice: number;
  pool: string;
  propertysubtype: string;
  size: number;
  yearbuilt: number;
  mlsid: string;
  status: 'Closed' | 'Active' | 'Pending' | 'Hold';
  geography: {
    type: 'Point';
    coordinates: number[];
  };
}

export interface sfrAndHotPadsCompData {
  bath_rooms: number;
  bed_rooms: number;
  cdom: number;
  // distance: number;
  geom: {
    type: 'Point';
    coordinates: number[];
  };
  propertysubtype: string | null;
  rent: number;
  square_feet: number;
  yearbuilt: number | null;
  base_id: string;
}

export interface realtorDotComCompData {
  baths: string;
  beds: string;
  currentprice: string;
  // distance: number;
  latitude: string;
  longitude: string;
  propertytype: string;
  square_feet: string;
  year_built: number | string;
  id: string;
}

export interface filters {
  // geo filters: need point-in-poly
  // default values are false under match mode
  isSameSchoolDistrict: boolean;
  isSameCounty: boolean;
  isSameZIPCode: boolean;
  // regular filters
  minRent: number; // for lease comps
  maxRent: number;
  minSales: number; // for sale comps
  maxSales: number;
  minBeds: number;
  maxBeds: number;
  minBaths: number;
  maxBaths: number;
  minSqft: number;
  maxSqft: number;
  minYearBuilt: number;
  maxYearBuilt: number;
  minLotSize: number;
  maxLotSize: number;
  minCumulativeDaysOnMarket: number; // cululative days on market
  maxCumulativeDaysOnMarket: number;
  minCoveredParking: number;
  maxCoveredParking: number;
  isPoolAllowed: boolean; // default value is true
}

// for lease, filter MLS, SFR, HotPads, and realtor.com comps
export interface filterCompDataParamsForLease {
  subjectPropertyData: parcelData | mlsData | portfolioPropertyData;
  subjectPropertyType: 'parcel' | 'mls' | 'portfolio';
  subjectPropertyParcelData?: parcelData;
  leaseOrSale: 'lease';
  mlsCompData: mlsCompData[];
  sfrCompData: sfrAndHotPadsCompData[];
  hotPadsCompData: sfrAndHotPadsCompData[];
  realtorDotComCompData: realtorDotComCompData[];
}

// for sale, filter MLS comps only
export interface filterCompDataParamsForSale {
  subjectPropertyData: parcelData | mlsData | portfolioPropertyData;
  subjectPropertyType: 'parcel' | 'mls' | 'portfolio';
  subjectPropertyParcelData?: parcelData;
  leaseOrSale: 'sale';
  mlsCompData: mlsCompData[];
}

export type filterCompDataParams =
  | filterCompDataParamsForLease
  | filterCompDataParamsForSale;

// school district polygon
export interface schoolDistrictPolygon {
  country: 'USA';
  geom: {
    type: 'MultiPolygon';
    coordinates: number[][][][];
  };
  lat: number;
  lng: number;
  metro: string;
  obj_area: string;
  obj_id: string;
  obj_name: string;
  obj_subtcd: string;
  obj_subtyp: string;
  obj_typ: string;
  reldate: string;
}

// county polygon
export interface countyPolygon {
  geom: {
    type: 'MultiPolygon';
    coordinates: number[][][][];
  };
  id: number;
  key: string;
  name: string;
}

// ZIP Code polygon
export interface ZIPCodePolygon {
  geom: {
    type: 'MultiPolygon';
    coordinates: number[][][][];
  };
  id: number;
  key: string;
}
