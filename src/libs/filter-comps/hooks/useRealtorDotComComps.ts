import { testLog } from '@/components/ResultTable/LandComp/utils/functions';
import {
  getRealtorSingleFamily,
  getRealtorSingleFamilyWithinPolygon,
} from '@/services/data';
import { FeatureCollection } from '@turf/helpers';
import { isEmpty, isEqual } from 'lodash';
import moment from 'moment';
import { useEffect, useState } from 'react';
import { useSelector } from 'umi';
import { calculateMarketRentAndValue } from '../calculate-market-rent-value';
import { filterDataBySubjectPolygon } from '../filter-geo';
import { filterCompData } from '../filter-process';
import {
  countyPolygon,
  filters,
  realtorDotComCompData,
  schoolDistrictPolygon,
  ZIPCodePolygon,
} from '../types';

export const useRealtorDotComComps = () => {
  // State
  const [loading, setLoading] = useState(false);
  const [filteredComps, setFilteredComps] = useState<realtorDotComCompData[]>(
    [],
  );
  const [calculationResults, setCalculationResults] = useState<any>(null);
  const [prevPropertyAddress, setPrevPropertyAddress] = useState<any>(null);
  const [prevRadiusMile, setPrevRadiusMile] = useState<number | null>(null);
  const [prevStartMLS, setPrevStartMLS] = useState<moment.Moment | null>(null);
  const [prevEndMLS, setPrevEndMLS] = useState<moment.Moment | null>(null);
  const [allCompsDeduped, setAllCompsDeduped] = useState<
    realtorDotComCompData[]
  >([]);
  const [prevAllCompsDeduped, setPrevAllCompsDeduped] = useState<
    realtorDotComCompData[]
  >([]);
  const [prevFilters, setPrevFilters] = useState<filters | null>(null);
  const [prevDrawnCustomPolygons, setPrevDrawnCustomPolygons] = useState<any[]>(
    [],
  );
  const [prevSchoolDistrictProperties, setPrevSchoolDistrictProperties] =
    useState<schoolDistrictPolygon[]>([]);
  const [prevCountyData, setPrevCountyData] = useState<countyPolygon[]>([]);
  const [prevZipCodeData, setPrevZipCodeData] = useState<ZIPCodePolygon[]>([]);

  // Selectors
  const drawnCustomPolygons = useSelector(
    (state: any) => state.CMA.drawnCustomPolygons,
  );
  const selectedRowKeysRealtorDotCom = useSelector(
    (state: any) => state.CMA.selectedRowKeysRealtorDotCom,
  );
  const currentPropertyAddress = useSelector(
    (state: any) => state.CMA.currentPropertyAddress,
  );
  const currentRadiusMile = useSelector(
    (state: any) => state.CMA.currentRadiusMile,
  );
  const currentStartMLS = useSelector(
    (state: any) => state.CMA.currentStartMLS,
  );
  const currentEndMLS = useSelector((state: any) => state.CMA.currentEndMLS);
  const searchingMode = useSelector((state: any) => state.CMA.searchingMode);
  const marketRentPreference = useSelector(
    (state: any) => state.CMA.marketRentPreference,
  );
  const eventCoordinates = useSelector(
    (state: any) => state.CMA.eventCoordinates,
  );
  const currentSchoolDistrictProperties = useSelector(
    (state: any) => state.CMA.currentSchoolDistrictProperties,
  );
  const currentCountyData = useSelector(
    (state: any) => state.CMA.currentCountyData,
  );
  const currentZipCodeData = useSelector(
    (state: any) => state.CMA.currentZipCodeData,
  );
  const isDistrictFilterOn = useSelector(
    (state: any) => state.CMA.isDistrictFilterOn,
  );
  const isCountyFilterOn = useSelector(
    (state: any) => state.CMA.isCountyFilterOn,
  );
  const isZipCodeFilterOn = useSelector(
    (state: any) => state.CMA.isZipCodeFilterOn,
  );

  const filters: filters = {
    isSameSchoolDistrict: isDistrictFilterOn,
    isSameCounty: isCountyFilterOn,
    isSameZIPCode: isZipCodeFilterOn,
    minBeds: useSelector((state: any) => state.CMA.minBeds),
    maxBeds: useSelector((state: any) => state.CMA.maxBeds),
    minBaths: useSelector((state: any) => state.CMA.minBaths),
    maxBaths: useSelector((state: any) => state.CMA.maxBaths),
    minSqft: useSelector((state: any) => state.CMA.minSqft),
    maxSqft: useSelector((state: any) => state.CMA.maxSqft),
    minLotSize: useSelector((state: any) => state.CMA.minLotSize),
    maxLotSize: useSelector((state: any) => state.CMA.maxLotSize),
    minYearBuilt: useSelector((state: any) => state.CMA.minYearBuilt),
    maxYearBuilt: useSelector((state: any) => state.CMA.maxYearBuilt),
    minCumulativeDaysOnMarket: useSelector(
      (state: any) => state.CMA.minCumulativeDaysOnMarket,
    ),
    maxCumulativeDaysOnMarket: useSelector(
      (state: any) => state.CMA.maxCumulativeDaysOnMarket,
    ),
    minCoveredParking: useSelector((state: any) => state.CMA.minCoveredParking),
    maxCoveredParking: useSelector((state: any) => state.CMA.maxCoveredParking),
    minRent: useSelector((state: any) => state.CMA.minRentPrice),
    maxRent: useSelector((state: any) => state.CMA.maxRentPrice),
    minSales: useSelector((state: any) => state.CMA.minSoldPrice),
    maxSales: useSelector((state: any) => state.CMA.maxSoldPrice),
    isPoolAllowed: useSelector((state: any) => state.CMA.selectedPoolAllowed),
  };
  const subjectPropertyParcelData = useSelector(
    (state: any) => state.CMA.subjectPropertyParcelData,
  );

  useEffect(() => {
    // only fetch comps if the property address, radius mile, start date or end date has changed
    const fetchComps = async () => {
      if (
        (!isEmpty(currentPropertyAddress) &&
          currentRadiusMile &&
          currentStartMLS &&
          currentEndMLS &&
          (!isEqual(currentPropertyAddress, prevPropertyAddress) ||
            currentRadiusMile !== prevRadiusMile ||
            moment(currentStartMLS).isSame(moment(prevStartMLS)) ||
            moment(currentEndMLS).isSame(moment(prevEndMLS)))) ||
        (drawnCustomPolygons &&
          Array.isArray(drawnCustomPolygons) &&
          drawnCustomPolygons.length > 0 &&
          !isEqual(drawnCustomPolygons, prevDrawnCustomPolygons))
      ) {
        setLoading(true);
        try {
          let result;
          if (drawnCustomPolygons.length === 0) {
            result = await getRealtorSingleFamily({
              lat: currentPropertyAddress.latitude,
              lng: currentPropertyAddress.longitude,
              distance: currentRadiusMile * 1609.344,
              startDate: currentStartMLS,
            });
          } else {
            result = await getRealtorSingleFamilyWithinPolygon({
              body: drawnCustomPolygons,
              startDate: currentStartMLS,
            });
          }

          // Dedupe results by id
          const dedupedResult = (
            result as unknown as realtorDotComCompData[]
          ).filter(
            (item, index, self) =>
              index === self.findIndex((t) => t.id === item.id),
          );
          // console.log('dedupedResult', result, dedupedResult);
          setAllCompsDeduped(dedupedResult);
          setPrevPropertyAddress(currentPropertyAddress);
          setPrevRadiusMile(currentRadiusMile);
          setPrevStartMLS(currentStartMLS);
          setPrevEndMLS(currentEndMLS);
        } catch (error) {
          console.error('Error fetching realtor.com comps:', error);
          setFilteredComps([]);
        }
        setLoading(false);
      }
    };

    fetchComps();
  }, [
    currentPropertyAddress,
    currentRadiusMile,
    currentStartMLS,
    currentEndMLS,
    drawnCustomPolygons,
  ]);

  useEffect(() => {
    if (
      !isEqual(filters, prevFilters) ||
      !isEqual(allCompsDeduped, prevAllCompsDeduped) ||
      !isEqual(currentSchoolDistrictProperties, prevSchoolDistrictProperties) ||
      !isEqual(currentCountyData, prevCountyData) ||
      !isEqual(currentZipCodeData, prevZipCodeData)
    ) {
      // Apply filters
      // console.log('allCompsDeduped', prevAllCompsDeduped);
      const filteredData = filterCompData({
        compData: allCompsDeduped,
        compType: 'realtorDotCom',
        filters,
        leaseOrSale: searchingMode === 'Lease' ? 'lease' : 'sale',
      }) as realtorDotComCompData[];

      // Apply geo filters if needed
      let geoFilteredData = filteredData;
      if (isDistrictFilterOn && currentSchoolDistrictProperties) {
        geoFilteredData = filterDataBySubjectPolygon({
          subjectCoordinates: eventCoordinates,
          data: filteredData,
          polygons: currentSchoolDistrictProperties,
        }) as realtorDotComCompData[];
      }
      if (isCountyFilterOn && currentCountyData && geoFilteredData) {
        geoFilteredData = filterDataBySubjectPolygon({
          subjectCoordinates: eventCoordinates,
          data: geoFilteredData,
          polygons: currentCountyData,
        }) as realtorDotComCompData[];
      }
      if (isZipCodeFilterOn && currentZipCodeData && geoFilteredData) {
        geoFilteredData = filterDataBySubjectPolygon({
          subjectCoordinates: eventCoordinates,
          data: geoFilteredData,
          polygons: currentZipCodeData,
        }) as realtorDotComCompData[];
      }

      setFilteredComps(geoFilteredData);
      if (!isEqual(filters, prevFilters)) {
        setPrevFilters(filters);
      }
      if (!isEqual(allCompsDeduped, prevAllCompsDeduped)) {
        setPrevAllCompsDeduped(allCompsDeduped);
      }
      if (
        !isEqual(currentSchoolDistrictProperties, prevSchoolDistrictProperties)
      ) {
        setPrevSchoolDistrictProperties(currentSchoolDistrictProperties);
      }
      if (!isEqual(currentCountyData, prevCountyData)) {
        setPrevCountyData(currentCountyData);
      }
      if (!isEqual(currentZipCodeData, prevZipCodeData)) {
        setPrevZipCodeData(currentZipCodeData);
      }
    }
  }, [
    allCompsDeduped,
    filters,
    currentSchoolDistrictProperties,
    currentCountyData,
    currentZipCodeData,
  ]);

  // Calculate market rent/value when filtered comps or selected rows change
  useEffect(() => {
    if (
      filteredComps.length > 0 &&
      selectedRowKeysRealtorDotCom.length > 0 &&
      subjectPropertyParcelData
    ) {
      const results = calculateMarketRentAndValue({
        mlsCompDataFiltered: [],
        sfrCompDataFiltered: [],
        hotPadsCompDataFiltered: [],
        realtorDotComCompDataFiltered: filteredComps,
        subjectPropertyParcelData,
        leaseOrSale: searchingMode === 'Lease' ? 'lease' : 'sale',
        compingMode: 'smartFilter',
        preference: marketRentPreference,
        selectedRowKeysMLSParam: [],
        selectedRowKeysNationalOperatorsParam: [],
        selectedRowKeysHotPadsParam: [],
        selectedRowKeysRealtorDotComParam: selectedRowKeysRealtorDotCom,
      });

      setCalculationResults(results);
    }
  }, [
    filteredComps,
    selectedRowKeysRealtorDotCom,
    subjectPropertyParcelData,
    searchingMode,
  ]);

  // Create GeoJSON from filtered comps
  const geoJSON: FeatureCollection = {
    type: 'FeatureCollection',
    features: filteredComps
      .filter((comp) => selectedRowKeysRealtorDotCom.includes(comp.id))
      .map((comp) => ({
        type: 'Feature',
        geometry: {
          type: 'Point',
          coordinates: [Number(comp.longitude), Number(comp.latitude)],
        },
        properties: {
          ...comp,
        },
      })),
  };

  return {
    loading,
    filteredComps,
    calculationResults,
    geoJSON,
  };
};
