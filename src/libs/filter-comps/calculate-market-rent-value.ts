import {
  mlsCompData,
  parcelData,
  realtorDotComCompData,
  sfrAndHotPadsCompData,
} from './types';
import {
  calculateMedian,
  getAverageWithZeroValueCheck,
} from './utils/calculation';

export const calculateMarketRentAndValue = ({
  mlsCompDataFiltered,
  sfrCompDataFiltered,
  hotPadsCompDataFiltered,
  realtorDotComCompDataFiltered,
  subjectPropertyParcelData,
  leaseOrSale,
  compingMode = 'smartFilter', // match mode
  preference = 'mls', // default to MLS
  adjustedRentFormula,
  adjustedSalesFormula,
  selectedRowKeysMLSParam,
  selectedRowKeysNationalOperatorsParam,
  selectedRowKeysHotPadsParam,
  selectedRowKeysRealtorDotComParam,
}: {
  mlsCompDataFiltered: mlsCompData[];
  sfrCompDataFiltered: sfrAndHotPadsCompData[];
  hotPadsCompDataFiltered: sfrAndHotPadsCompData[];
  realtorDotComCompDataFiltered: realtorDotComCompData[];
  subjectPropertyParcelData: parcelData;
  leaseOrSale: 'lease' | 'sale';
  compingMode: 'smartFilter' | 'noFilter' | 'intelligentComping';
  preference: 'mls' | 'sfr' | 'hotpads';
  adjustedRentFormula?: string;
  adjustedSalesFormula?: string;
  selectedRowKeysMLSParam: string[];
  selectedRowKeysNationalOperatorsParam: string[];
  selectedRowKeysHotPadsParam: string[];
  selectedRowKeysRealtorDotComParam: string[];
}) => {
  let rentAverageMLS = 0,
    rentAverageNationalOperators = 0,
    rentAverageHotPads = 0,
    rentAverageRealtorDotCom = 0,
    rentAverageAll = 0,
    MLSAverageToAVMRatio = 0,
    rentSumMLS = 0,
    rentSumNationalOperators = 0,
    rentSumHotPads = 0,
    rentSumRealtorDotCom = 0,
    hasAverageMLS = 0, // for deciding total sum for average all is divided by how many
    hasAverageNationalOperators = 0,
    hasAverageHotPads = 0,
    hasAverageRealtorDotCom = 0,
    hasAverageSubjectProperty = 0,
    // per Sqft
    rentAveragePerSqftMLS = 0,
    rentAveragePerSqftNationalOperators = 0,
    rentAveragePerSqftHotPads = 0,
    rentAveragePerSqftRealtorDotCom = 0,
    rentAveragePerSqftAll = 0,
    rentPerSqftSumMLS = 0,
    rentPerSqftSumNationalOperators = 0,
    rentPerSqftSumHotPads = 0,
    rentPerSqftSumRealtorDotCom = 0,
    hasAveragePerSqftMLS = 0,
    hasAveragePerSqftNationalOperators = 0,
    hasAveragePerSqftHotPads = 0,
    hasAveragePerSqftRealtorDotCom = 0,
    hasAveragePerSqftSubjectProperty = 0,
    propertyWithPerSqftMLS = 0, // number of MLS properties that has per Sqft
    propertyWithPerSqftNationalOperators = 0,
    propertyWithPerSqftHotPads = 0,
    propertyWithPerSqftRealtorDotCom = 0,
    // median
    rentMedianMLS = 0,
    rentMedianSFR = 0,
    rentMedianHotPads = 0,
    rentMedianRealtorDotCom = 0,
    rentArrayMLS: number[] = [],
    rentArraySFR: number[] = [],
    rentArrayHotPads: number[] = [],
    rentArrayRealtorDotCom: number[] = [],
    rentPerSqftMedianMLS = 0,
    rentPerSqftMedianSFR = 0,
    rentPerSqftMedianHotPads = 0,
    rentPerSqftMedianRealtorDotCom = 0,
    rentPerSqftArrayMLS: number[] = [],
    rentPerSqftArraySFR: number[] = [],
    rentPerSqftArrayHotPads: number[] = [],
    rentPerSqftArrayRealtorDotCom: number[] = [],
    marketRentOrValue: number | string = 0,
    rentAdjustedFormula = '',
    MLSMedianToAVMRatio = null;

  // calculate MLS average for selected rows
  if (selectedRowKeysMLSParam && selectedRowKeysMLSParam.length > 0) {
    mlsCompDataFiltered.forEach((property) => {
      if (selectedRowKeysMLSParam.includes(property.mlsid)) {
        const latestPrice =
          property.status === 'Closed'
            ? property.closeprice
            : property.currentprice;
        rentSumMLS += latestPrice;
        if (latestPrice && property.size) {
          rentPerSqftSumMLS += latestPrice / property.size;
          propertyWithPerSqftMLS++;
        }
        // for MLS median
        rentArrayMLS.push(latestPrice);
        // for MLS per Sqft median
        if (latestPrice && property.size) {
          rentPerSqftArrayMLS.push(latestPrice / property.size);
        }
      }
    });
    rentAverageMLS = rentSumMLS / selectedRowKeysMLSParam.length;
    // if selectedRowKeys.length > 0, it's in calculation, therefore hasAverage = 1
    hasAverageMLS = 1;
    // calculate median
    rentMedianMLS = calculateMedian(rentArrayMLS);
    // calculate per Sqft median
    rentPerSqftMedianMLS = calculateMedian(rentPerSqftArrayMLS);

    if (propertyWithPerSqftMLS) {
      rentAveragePerSqftMLS = rentPerSqftSumMLS / propertyWithPerSqftMLS;
      hasAveragePerSqftMLS = 1;
    }
  }
  // calculate national operators average for selected rows
  if (
    selectedRowKeysNationalOperatorsParam &&
    selectedRowKeysNationalOperatorsParam.length > 0
  ) {
    sfrCompDataFiltered.forEach((property) => {
      if (selectedRowKeysNationalOperatorsParam.includes(property.base_id)) {
        rentSumNationalOperators += property.rent;
        if (property.rent && property.square_feet) {
          rentPerSqftSumNationalOperators +=
            property.rent / property.square_feet;
          propertyWithPerSqftNationalOperators++;
        }
        // for median SFR
        rentArraySFR.push(property.rent);
        // for per Sqft median SFR
        if (property.rent && property.square_feet) {
          rentPerSqftArraySFR.push(property.rent / property.square_feet);
        }
      }
    });
    rentAverageNationalOperators =
      rentSumNationalOperators / selectedRowKeysNationalOperatorsParam.length;
    // if selectedRowKeys.length > 0, it's in calculation, therefore hasAverage = 1
    hasAverageNationalOperators = 1;
    // calculate median
    rentMedianSFR = calculateMedian(rentArraySFR);
    // calculate per Sqft median
    rentPerSqftMedianSFR = calculateMedian(rentPerSqftArraySFR);

    if (propertyWithPerSqftNationalOperators) {
      rentAveragePerSqftNationalOperators =
        rentPerSqftSumNationalOperators / propertyWithPerSqftNationalOperators;
      hasAveragePerSqftNationalOperators = 1;
    }
  }
  // calculate Hot Pads average for selected rows
  if (selectedRowKeysHotPadsParam && selectedRowKeysHotPadsParam.length > 0) {
    hotPadsCompDataFiltered.forEach((property) => {
      if (selectedRowKeysHotPadsParam.includes(property.base_id)) {
        rentSumHotPads += property.rent;
        if (property.rent && property.square_feet) {
          rentPerSqftSumHotPads += property.rent / property.square_feet;
          propertyWithPerSqftHotPads++;
        }
        // for median SFR
        rentArrayHotPads.push(property.rent);
        // for per Sqft median SFR
        if (property.rent && property.square_feet) {
          rentPerSqftArrayHotPads.push(property.rent / property.square_feet);
        }
      }
    });
    rentAverageHotPads = rentSumHotPads / selectedRowKeysHotPadsParam.length;
    // if selectedRowKeys.length > 0, it's in calculation, therefore hasAverage = 1
    hasAverageHotPads = 1;
    // calculate median
    rentMedianHotPads = calculateMedian(rentArrayHotPads);
    // calculate per Sqft median
    rentPerSqftMedianHotPads = calculateMedian(rentPerSqftArrayHotPads);

    if (propertyWithPerSqftHotPads) {
      rentAveragePerSqftHotPads =
        rentPerSqftSumHotPads / propertyWithPerSqftHotPads;
      hasAveragePerSqftHotPads = 1;
    }
  }
  // calculate realtor.com average for selected rows
  if (
    selectedRowKeysRealtorDotComParam &&
    selectedRowKeysRealtorDotComParam.length > 0
  ) {
    realtorDotComCompDataFiltered.forEach((property) => {
      if (selectedRowKeysRealtorDotComParam.includes(property.id)) {
        rentSumRealtorDotCom += Number(property.currentprice);
        if (Number(property.currentprice) && Number(property.square_feet)) {
          rentPerSqftSumRealtorDotCom +=
            Number(property.currentprice) / Number(property.square_feet);
          propertyWithPerSqftRealtorDotCom++;
        }
        // for median realtor.com
        rentArrayRealtorDotCom.push(Number(property.currentprice));
        // for per Sqft median realtor.com
        if (Number(property.currentprice) && Number(property.square_feet)) {
          rentPerSqftArrayRealtorDotCom.push(
            Number(property.currentprice) / Number(property.square_feet),
          );
        }
      }
    });
    rentAverageRealtorDotCom =
      rentSumRealtorDotCom / selectedRowKeysRealtorDotComParam.length;
    // if selectedRowKeys.length > 0, it's in calculation, therefore hasAverage = 1
    hasAverageRealtorDotCom = 1;
    // calculate median
    rentMedianRealtorDotCom = calculateMedian(rentArrayRealtorDotCom);
    // calculate per Sqft median
    rentPerSqftMedianRealtorDotCom = calculateMedian(
      rentPerSqftArrayRealtorDotCom,
    );

    if (propertyWithPerSqftRealtorDotCom) {
      rentAveragePerSqftRealtorDotCom =
        rentPerSqftSumRealtorDotCom / propertyWithPerSqftRealtorDotCom;
      hasAveragePerSqftRealtorDotCom = 1;
    }
  }
  // if selectedRowKeys.length > 0, it's in calculation, therefore hasAverage = 1
  if (
    (leaseOrSale === 'lease' && subjectPropertyParcelData.rent > 0) ||
    (leaseOrSale === 'sale' && subjectPropertyParcelData.sales > 0)
  ) {
    hasAverageSubjectProperty = 1;
    if (subjectPropertyParcelData.total_area_sq_ft > 0) {
      hasAveragePerSqftSubjectProperty = 1;
    }
  }
  // for cases when rent is missing in parcel data
  const subjectPropertyAVM =
    (leaseOrSale === 'lease'
      ? subjectPropertyParcelData.rent
      : subjectPropertyParcelData.sales) || 0;
  const subjectPropertyRentPerSqft =
    (leaseOrSale === 'lease'
      ? subjectPropertyParcelData.rent
      : subjectPropertyParcelData.sales) /
      subjectPropertyParcelData.total_area_sq_ft || 0;
  // calculate average of all methods
  // for cases when all 3 sources are empty
  if (
    hasAverageMLS +
    hasAverageNationalOperators +
    hasAverageHotPads +
    hasAverageSubjectProperty
  ) {
    rentAverageAll =
      (rentAverageMLS +
        rentAverageNationalOperators +
        rentAverageHotPads +
        subjectPropertyAVM) /
      (hasAverageMLS +
        hasAverageNationalOperators +
        hasAverageHotPads +
        hasAverageSubjectProperty);
  }
  if (
    hasAveragePerSqftMLS +
    hasAveragePerSqftNationalOperators +
    hasAveragePerSqftHotPads +
    hasAveragePerSqftSubjectProperty
  ) {
    rentAveragePerSqftAll =
      (rentAveragePerSqftMLS +
        rentAveragePerSqftNationalOperators +
        rentAveragePerSqftHotPads +
        subjectPropertyRentPerSqft) /
      (hasAveragePerSqftMLS +
        hasAveragePerSqftNationalOperators +
        hasAveragePerSqftHotPads +
        hasAveragePerSqftSubjectProperty);
  }

  // under intelligent comping mode
  // calculate adjusted rent using formula in adjusted API return
  if (compingMode === 'intelligentComping') {
    if (leaseOrSale === 'lease') {
      if (adjustedRentFormula) {
        // if user deselects all comps, return AVM
        switch (adjustedRentFormula) {
          case '((AVM Rent + Median Rent) / 2)':
            // marketRentOrValue = (subjectPropertyAVM + rentMedianMLS) / 2;
            marketRentOrValue = getAverageWithZeroValueCheck(
              subjectPropertyAVM,
              rentMedianMLS,
            );
            break;
          case 'Median Rent':
            marketRentOrValue = rentMedianMLS || subjectPropertyAVM;
            break;
          case 'Median Rent * 1.02':
            marketRentOrValue = rentMedianMLS * 1.02 || subjectPropertyAVM;
            break;
          case '((AVM Rent + Median SFR) / 2)':
            // marketRentOrValue = (subjectPropertyAVM + rentMedianSFR) / 2;
            marketRentOrValue = getAverageWithZeroValueCheck(
              subjectPropertyAVM,
              rentMedianSFR,
            );
            break;
          case 'Median SFR':
            marketRentOrValue = rentMedianSFR || subjectPropertyAVM;
            break;
          case 'Median SFR * 1.02':
            marketRentOrValue = rentMedianSFR * 1.02 || subjectPropertyAVM;
            break;
          case '((AVM Rent + Median 3rd Party) / 2)':
            // marketRentOrValue = (subjectPropertyAVM + rentMedianHotPads) / 2;
            marketRentOrValue = getAverageWithZeroValueCheck(
              subjectPropertyAVM,
              rentMedianHotPads,
            );
            break;
          case 'Median 3rd Party':
            marketRentOrValue = rentMedianHotPads || subjectPropertyAVM;
            break;
          case 'Median 3rd Party * 1.02':
            marketRentOrValue = rentMedianHotPads * 1.02 || subjectPropertyAVM;
            break;
          case 'AVM Rent':
            marketRentOrValue = subjectPropertyAVM;
            break;
          default:
            marketRentOrValue = subjectPropertyAVM;
            break;
        }
      }
    } else {
      switch (adjustedSalesFormula) {
        case '((AVM Sales + Median Sales) / 2)':
          // marketRentOrValue = (subjectPropertyAVM + rentMedianMLS) / 2;
          marketRentOrValue = getAverageWithZeroValueCheck(
            subjectPropertyAVM,
            rentMedianMLS,
          );
          break;
        case 'Median Sales':
          marketRentOrValue = rentMedianMLS || subjectPropertyAVM;
          break;
        case 'Median Sales * 1.02':
          marketRentOrValue = rentMedianMLS * 1.02 || subjectPropertyAVM;
          break;
        default:
          marketRentOrValue = subjectPropertyAVM;
          break;
      }
    }
  } else {
    // the mode is show all or smart filter
    // we need to choose the market rent formula based on preference, comp availability, and difference between AVM and the preferred input
    if (leaseOrSale === 'lease') {
      const getRentAVM = () => {
        if (subjectPropertyParcelData?.rent) {
          return subjectPropertyParcelData.rent;
        } else return 'Missing Rent AVM';
      };
      const rentAVM = getRentAVM();
      let preferredInputName = '';
      const getCompBasedOnPreference = (
        preference: 'mls' | 'sfr' | 'hotpads',
      ) => {
        switch (preference) {
          case 'mls':
            if (
              selectedRowKeysMLSParam.length >= 3 ||
              // all comps are not enough and AVM is missing
              (selectedRowKeysMLSParam.length < 3 &&
                selectedRowKeysMLSParam.length > 0 &&
                selectedRowKeysNationalOperatorsParam.length < 3 &&
                selectedRowKeysHotPadsParam.length < 3 &&
                rentAVM === 'Missing Rent AVM') ||
              // MLS comps are fewer than 3 but more than 0, and the difference with AVM is greater than 20%
              (selectedRowKeysMLSParam.length > 0 &&
                selectedRowKeysMLSParam.length < 3 &&
                selectedRowKeysNationalOperatorsParam.length < 3 &&
                selectedRowKeysHotPadsParam.length < 3 &&
                rentAVM !== 'Missing Rent AVM' &&
                Math.abs((rentAVM - rentMedianMLS) / rentAVM) > 0.2)
            ) {
              preferredInputName = 'MLS Median';
              return rentMedianMLS;
            } else if (
              selectedRowKeysNationalOperatorsParam.length >= 3 ||
              // all comps are not enough, MLS has no comps, and AVM is missing
              (selectedRowKeysMLSParam.length === 0 &&
                selectedRowKeysNationalOperatorsParam.length < 3 &&
                selectedRowKeysNationalOperatorsParam.length > 0 &&
                selectedRowKeysHotPadsParam.length < 3 &&
                rentAVM === 'Missing Rent AVM') ||
              // MLS has no comps, and the difference with AVM is greater than 20%
              (selectedRowKeysMLSParam.length === 0 &&
                selectedRowKeysNationalOperatorsParam.length < 3 &&
                selectedRowKeysNationalOperatorsParam.length > 0 &&
                selectedRowKeysHotPadsParam.length < 3 &&
                rentAVM !== 'Missing Rent AVM' &&
                Math.abs((rentAVM - rentMedianSFR) / rentAVM) > 0.2)
            ) {
              preferredInputName = 'SFR Operator Median';
              return rentMedianSFR;
            } else if (
              selectedRowKeysHotPadsParam.length >= 3 ||
              // all comps are not enough, MLS and SFR have no comps, and AVM is missing
              (selectedRowKeysMLSParam.length === 0 &&
                selectedRowKeysNationalOperatorsParam.length === 0 &&
                selectedRowKeysHotPadsParam.length < 3 &&
                selectedRowKeysHotPadsParam.length > 0 &&
                rentAVM === 'Missing Rent AVM') ||
              // MLS and SFR have no comps, and the difference with AVM is greater than 20%
              (selectedRowKeysMLSParam.length === 0 &&
                selectedRowKeysNationalOperatorsParam.length === 0 &&
                selectedRowKeysHotPadsParam.length < 3 &&
                selectedRowKeysHotPadsParam.length > 0 &&
                rentAVM !== 'Missing Rent AVM' &&
                Math.abs((rentAVM - rentMedianHotPads) / rentAVM) > 0.2)
            ) {
              preferredInputName = '3rd Party Median';
              return rentMedianHotPads;
            } else if (rentAVM !== 'Missing Rent AVM') {
              preferredInputName = 'AVM';
              rentAdjustedFormula = 'AVM';
              return getRentAVM();
            }
          case 'sfr':
            if (
              selectedRowKeysNationalOperatorsParam.length >= 3 ||
              // all comps are not enough and AVM is missing
              (selectedRowKeysNationalOperatorsParam.length < 3 &&
                selectedRowKeysNationalOperatorsParam.length > 0 &&
                selectedRowKeysMLSParam.length < 3 &&
                selectedRowKeysHotPadsParam.length < 3 &&
                rentAVM === 'Missing Rent AVM') ||
              // SFR comps are fewer than 3 but more than 0, and the difference with AVM is greater than 20%
              (selectedRowKeysNationalOperatorsParam.length > 0 &&
                selectedRowKeysNationalOperatorsParam.length < 3 &&
                selectedRowKeysMLSParam.length < 3 &&
                selectedRowKeysHotPadsParam.length < 3 &&
                rentAVM !== 'Missing Rent AVM' &&
                Math.abs((rentAVM - rentMedianSFR) / rentAVM) > 0.2)
            ) {
              preferredInputName = 'SFR Operator Median';
              return rentMedianSFR;
            } else if (
              selectedRowKeysMLSParam.length >= 3 ||
              // all comps are not enough, SFR has no comps, and AVM is missing
              (selectedRowKeysNationalOperatorsParam.length === 0 &&
                selectedRowKeysMLSParam.length < 3 &&
                selectedRowKeysMLSParam.length > 0 &&
                selectedRowKeysHotPadsParam.length < 3 &&
                rentAVM === 'Missing Rent AVM') ||
              // SFR has no comps, and the difference with AVM is greater than 20%
              (selectedRowKeysNationalOperatorsParam.length === 0 &&
                selectedRowKeysMLSParam.length < 3 &&
                selectedRowKeysMLSParam.length > 0 &&
                selectedRowKeysHotPadsParam.length < 3 &&
                rentAVM !== 'Missing Rent AVM' &&
                Math.abs((rentAVM - rentMedianMLS) / rentAVM) > 0.2)
            ) {
              preferredInputName = 'MLS Median';
              return rentMedianMLS;
            } else if (
              selectedRowKeysHotPadsParam.length >= 3 ||
              // all comps are not enough, SFR and MLS have no comps, and AVM is missing
              (selectedRowKeysNationalOperatorsParam.length === 0 &&
                selectedRowKeysMLSParam.length === 0 &&
                selectedRowKeysHotPadsParam.length < 3 &&
                selectedRowKeysHotPadsParam.length > 0 &&
                rentAVM === 'Missing Rent AVM') ||
              // SFR and MLS have no comps, and the difference with AVM is greater than 20%
              (selectedRowKeysNationalOperatorsParam.length === 0 &&
                selectedRowKeysMLSParam.length === 0 &&
                selectedRowKeysHotPadsParam.length < 3 &&
                selectedRowKeysHotPadsParam.length > 0 &&
                rentAVM !== 'Missing Rent AVM' &&
                Math.abs((rentAVM - rentMedianHotPads) / rentAVM) > 0.2)
            ) {
              preferredInputName = '3rd Party Median';
              return rentMedianHotPads;
            } else if (rentAVM !== 'Missing Rent AVM') {
              preferredInputName = 'AVM';
              rentAdjustedFormula = 'AVM';
              return getRentAVM();
            }
          case 'hotpads':
            if (
              selectedRowKeysHotPadsParam.length >= 3 ||
              // all comps are not enough and AVM is missing
              (selectedRowKeysHotPadsParam.length < 3 &&
                selectedRowKeysHotPadsParam.length > 0 &&
                selectedRowKeysMLSParam.length < 3 &&
                selectedRowKeysNationalOperatorsParam.length < 3 &&
                rentAVM === 'Missing Rent AVM') ||
              // 3rd party comps are fewer than 3 but more than 0, and the difference with AVM is greater than 20%
              (selectedRowKeysHotPadsParam.length > 0 &&
                selectedRowKeysHotPadsParam.length < 3 &&
                selectedRowKeysMLSParam.length < 3 &&
                selectedRowKeysNationalOperatorsParam.length < 3 &&
                rentAVM !== 'Missing Rent AVM' &&
                Math.abs((rentAVM - rentMedianHotPads) / rentAVM) > 0.2)
            ) {
              preferredInputName = '3rd Party Median';
              return rentMedianHotPads;
            } else if (
              selectedRowKeysMLSParam.length >= 3 ||
              // all comps are not enough, 3rd party has no comps, and AVM is missing
              (selectedRowKeysHotPadsParam.length === 0 &&
                selectedRowKeysMLSParam.length < 3 &&
                selectedRowKeysMLSParam.length > 0 &&
                selectedRowKeysNationalOperatorsParam.length < 3 &&
                rentAVM === 'Missing Rent AVM') ||
              // 3rd party has no comps, and the difference with AVM is greater than 20%
              (selectedRowKeysHotPadsParam.length === 0 &&
                selectedRowKeysMLSParam.length < 3 &&
                selectedRowKeysMLSParam.length > 0 &&
                selectedRowKeysNationalOperatorsParam.length < 3 &&
                rentAVM !== 'Missing Rent AVM' &&
                Math.abs((rentAVM - rentMedianMLS) / rentAVM) > 0.2)
            ) {
              preferredInputName = 'MLS Median';
              return rentMedianMLS;
            } else if (
              selectedRowKeysNationalOperatorsParam.length >= 3 ||
              // all comps are not enough, 3rd party and MLS have no comps, and AVM is missing
              (selectedRowKeysHotPadsParam.length === 0 &&
                selectedRowKeysMLSParam.length === 0 &&
                selectedRowKeysNationalOperatorsParam.length < 3 &&
                selectedRowKeysNationalOperatorsParam.length > 0 &&
                rentAVM === 'Missing Rent AVM') ||
              // 3rd party and MLS have no comps, and the difference with AVM is greater than 20%
              (selectedRowKeysHotPadsParam.length === 0 &&
                selectedRowKeysMLSParam.length === 0 &&
                selectedRowKeysNationalOperatorsParam.length < 3 &&
                selectedRowKeysNationalOperatorsParam.length > 0 &&
                rentAVM !== 'Missing Rent AVM' &&
                Math.abs((rentAVM - rentMedianSFR) / rentAVM) > 0.2)
            ) {
              preferredInputName = 'SFR Operator Median';
              return rentMedianSFR;
            } else if (rentAVM !== 'Missing Rent AVM') {
              preferredInputName = 'AVM';
              rentAdjustedFormula = 'AVM';
              return getRentAVM();
            }
          default:
            return 'Invalid preference';
        }
      };
      const getDifferenceBetweenAVMAndPreferredInput = (
        preference: 'mls' | 'sfr' | 'hotpads',
      ) => {
        const preferredInput = getCompBasedOnPreference(preference);
        if (
          preferredInputName !== 'AVM' &&
          preferredInput &&
          typeof preferredInput === 'number' &&
          !(
            typeof preferredInput === 'string' &&
            ['Not enough comps', 'Invalid preference'].includes(preferredInput)
          )
        ) {
          if (rentAVM !== 'Missing Rent AVM') {
            const differentRatio = (rentAVM - preferredInput) / rentAVM;
            return differentRatio;
          } else if (rentAVM === 'Missing Rent AVM' && preferredInput) {
            marketRentOrValue = preferredInput;
            rentAdjustedFormula = `${preferredInputName}`;
            MLSMedianToAVMRatio = null;
            return null;
          } else {
            rentAdjustedFormula = 'Cannot be calculated';
            return null;
          }
        } else if (preferredInputName === 'AVM') {
          marketRentOrValue = rentAVM;
          rentAdjustedFormula = 'AVM';
          MLSMedianToAVMRatio = null;
          return null;
        }
      };
      const preferredInput = getCompBasedOnPreference(preference);
      const differentRatio =
        getDifferenceBetweenAVMAndPreferredInput(preference);
      switch (true) {
        case typeof differentRatio === 'number' &&
          differentRatio >= 10 / 100 &&
          differentRatio < 20 / 100 &&
          typeof rentAVM === 'number' &&
          typeof preferredInput === 'number':
        case typeof differentRatio === 'number' &&
          differentRatio < -10 / 100 &&
          differentRatio >= -20 / 100 &&
          typeof rentAVM === 'number' &&
          typeof preferredInput === 'number':
        case typeof differentRatio === 'number' &&
          differentRatio >= -5 / 100 &&
          differentRatio < 5 / 100 &&
          typeof rentAVM === 'number' &&
          typeof preferredInput === 'number':
          marketRentOrValue = (rentAVM + preferredInput) / 2;
          rentAdjustedFormula = `(AVM + ${preferredInputName}) / 2`;
          MLSMedianToAVMRatio = differentRatio;
          break;
        case typeof differentRatio === 'number' &&
          differentRatio >= -10 / 100 &&
          differentRatio < -5 / 100:
        case typeof differentRatio === 'number' && differentRatio >= 20 / 100:
        case typeof differentRatio === 'number' && differentRatio < -20 / 100:
          marketRentOrValue = preferredInput;
          rentAdjustedFormula = `${preferredInputName}`;
          MLSMedianToAVMRatio = differentRatio;
          break;
        case typeof differentRatio === 'number' &&
          differentRatio >= 5 / 100 &&
          differentRatio < 10 / 100 &&
          typeof rentAVM === 'number' &&
          typeof preferredInput === 'number':
          marketRentOrValue = preferredInput * (1 + 2 / 100);
          rentAdjustedFormula = `${preferredInputName} * 1.02`;
          MLSMedianToAVMRatio = differentRatio;
          break;
        default:
          break;
      }
    } else {
      // sale mode for show all and match modes
      const getSalesAVM = () => {
        if (subjectPropertyParcelData?.sales) {
          return subjectPropertyParcelData.sales;
        } else return 'Missing Sales AVM';
      };
      const salesAVM = getSalesAVM();
      const getDifferenceBetweenAVMAndMedianMLS = () => {
        if (salesAVM !== 'Missing Sales AVM') {
          if (selectedRowKeysMLSParam.length >= 3) {
            const differentRatio = (salesAVM - rentMedianMLS) / salesAVM;
            return differentRatio;
          } else {
            return 'Not enough comps';
          }
        } else if (salesAVM === 'Missing Sales AVM' && rentMedianMLS) {
          // AVM is missing but MLS median is available
          return 'Use MLS median when AVM is missing';
        } else {
          // AVM is missing and not enough comps
          rentAdjustedFormula = 'Cannot be calculated';
          return;
        }
      };
      const differentRatio = getDifferenceBetweenAVMAndMedianMLS();
      switch (true) {
        case typeof differentRatio === 'number' &&
          differentRatio >= 5 / 100 &&
          differentRatio < 20 / 100 &&
          typeof salesAVM === 'number' &&
          typeof rentMedianMLS === 'number':
        case typeof differentRatio === 'number' &&
          differentRatio < -5 / 100 &&
          differentRatio >= -20 / 100 &&
          typeof salesAVM === 'number' &&
          typeof rentMedianMLS === 'number':
        case typeof differentRatio === 'number' &&
          differentRatio >= -2.5 / 100 &&
          differentRatio < 2.5 / 100 &&
          typeof salesAVM === 'number' &&
          typeof rentMedianMLS === 'number':
          marketRentOrValue = (salesAVM + rentMedianMLS) / 2;
          rentAdjustedFormula = `(AVM + MLS Median) / 2`;
          MLSMedianToAVMRatio = differentRatio;
          break;
        case typeof differentRatio === 'number' &&
          differentRatio >= -5 / 100 &&
          differentRatio < -2.5 / 100:
        case typeof differentRatio === 'number' && differentRatio >= 20 / 100:
        case typeof differentRatio === 'number' && differentRatio < -20 / 100:
          marketRentOrValue = rentMedianMLS;
          rentAdjustedFormula = `MLS Median`;
          MLSMedianToAVMRatio = differentRatio;
          break;
        case typeof differentRatio === 'number' &&
          differentRatio >= 2.5 / 100 &&
          differentRatio < 5 / 100:
          marketRentOrValue = rentMedianMLS * (1 + 2 / 100);
          rentAdjustedFormula = `MLS Median * 1.02`;
          MLSMedianToAVMRatio = differentRatio;
          break;
        case differentRatio === 'Not enough comps':
          marketRentOrValue = salesAVM;
          rentAdjustedFormula = 'AVM';
          break;
        case differentRatio === 'Use MLS median when AVM is missing':
          marketRentOrValue = rentMedianMLS;
          rentAdjustedFormula = 'MLS Median';
          break;
        default:
          break;
      }
    }
  }

  return {
    rentAverageMLS: rentAverageMLS,
    rentAverageNationalOperators: rentAverageNationalOperators,
    rentAverageHotPads: rentAverageHotPads,
    rentAverageAll: rentAverageAll,
    MLSAverageToAVMRatio: MLSAverageToAVMRatio,
    MLSMedianToAVMRatio: MLSMedianToAVMRatio,
    marketRentOrValue: marketRentOrValue,
    rentAdjustedFormula: rentAdjustedFormula,
    rentAveragePerSqftMLS: rentAveragePerSqftMLS,
    rentAveragePerSqftNationalOperators: rentAveragePerSqftNationalOperators,
    rentAveragePerSqftHotPads: rentAveragePerSqftHotPads,
    rentAveragePerSqftRealtorDotCom: rentAveragePerSqftRealtorDotCom,
    rentAveragePerSqftAll: rentAveragePerSqftAll,
    rentMedianMLS: rentMedianMLS,
    rentMedianSFR: rentMedianSFR,
    rentMedianHotPads: rentMedianHotPads,
    rentMedianRealtorDotCom: rentMedianRealtorDotCom,
    rentPerSqftMedianMLS: rentPerSqftMedianMLS,
    rentPerSqftMedianSFR: rentPerSqftMedianSFR,
    rentPerSqftMedianHotPads: rentPerSqftMedianHotPads,
    rentPerSqftMedianRealtorDotCom: rentPerSqftMedianRealtorDotCom,
  };
};
