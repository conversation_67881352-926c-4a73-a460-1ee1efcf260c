import { useQuery } from 'react-query';
import { getClientAllowedAccessCoordinatesData } from '../services/data';

interface useClientAllowedCoordinatesProps {
  latitude: number;
  longitude: number;
  selectedUserGroup?: string;
}
export const useClientAllowedCoordinates = (
  props: useClientAllowedCoordinatesProps,
) => {
  const { data, isLoading } = useQuery(
    `getAllowedCoordinates-${props.latitude},${props.longitude}`,
    () =>
      getClientAllowedAccessCoordinatesData({
        coordinates: `${props.latitude},${props.longitude}`,
      }),
    // {
    //   enabled:
    //     props.selectedUserGroup &&
    //     ['Embry', 'Vertica'].includes(props.selectedUserGroup) &&
    //     process.env.UMI_APP_SERVER_TYPE === 'exp'
    //       ? true
    //       : false,
    // },
  );

  // if (
  //   props.selectedUserGroup &&
  //   !['Embry', 'Vertica'].includes(props.selectedUserGroup) &&
  //   process.env.UMI_APP_SERVER_TYPE === 'exp'
  // )
  //   return true;

  if (isLoading) return undefined;
  if (data) return true;
  return false;
};
