import { AuthSession } from '@aws-amplify/core/dist/esm/singleton/Auth/types';
import * as Sentry from '@sentry/react';
import {
  AuthUser,
  FetchUserAttributesOutput,
  fetchAuthSession,
  fetchUserAttributes,
} from 'aws-amplify/auth';
import { isEmpty } from 'lodash';
import moment from 'moment';
import { useEffect, useState } from 'react';
import { useDispatch } from 'umi';
import { dateFormat } from '../constants';
import userGroupAccess from '../userGroupAccess.json';
import { getSelectedUserGroup } from '../utils/auth';
import { getMetrosAllowedFromUserProfile } from '../utils/userGroup';

interface AuthSessionProperties {
  token?: string;
  userGroup: string[];
  profile?: string;
  email?: string;
}

interface useUserAuthenticationProps {
  user: AuthUser | undefined;
}

interface useUserAuthenticationReturn {
  initLoading: boolean;
  accessAllowed: boolean;
  authSession: AuthSessionProperties | null;
}
const useUserAuthentication = ({
  user,
}: useUserAuthenticationProps): useUserAuthenticationReturn => {
  const [initLoading, setInitLoading] = useState(true);
  const [accessAllowed, setAccessAllowed] = useState(false);
  const [authSession, setAuthSession] = useState<AuthSessionProperties | null>(
    null,
  );

  const dispatch = useDispatch();

  useEffect(() => {
    clearCognitoAmplifyCookies();
  }, []);

  useEffect(() => {
    if (!user) return;

    const getUserAuthentication = async () => {
      let session,
        attributes = null;

      try {
        session = await fetchAuthSession();
        attributes = await fetchUserAttributes();
        const auth = processAuthSessionData(session);

        if (userHasAccess(auth, attributes)) {
          const selectedUserGroup = getSelectedUserGroup(auth.userGroup);
          console.log('selectedUserGroup', selectedUserGroup);
          if (selectedUserGroup) {
            dispatch({
              type: 'CMA/saveCMAStates',
              payload: {
                selectedUserGroup: selectedUserGroup,
              },
            });
          }

          console.log('auth', auth);

          // prettier-ignore
          dispatch({
            type: 'CMA/saveCMAStates',
            payload: {
              accessToken: auth.token,
              currentUserEmail: auth.email,
              userEmail: auth.email,
              userGroup: auth.userGroup,
              isLeaseMode: auth.userGroup.includes('MarketplaceHomes') ? false : true,
              isDistrictFilterOn: auth.userGroup.includes('BridgeTower') ? true : false,
              metrosAllowedOnIndividualAccountLevel: auth.profile ? getMetrosAllowedFromUserProfile(auth.profile) : userGroupAccess['demo-CMA-DFW-only'].metro,
              trialAccountHasLandParcelSearch: !auth.profile?.includes('noLandParcelSearch'),
              trialAccountHasLandDevelopmentTools: !auth.profile?.includes('noLandDevelopmentTools'),
              compingMode: auth.userGroup.includes('demo-CMA-DFW-only') &&
                [
                  '<EMAIL>',
                  '<EMAIL>',
                  '<EMAIL>',
                ].includes(auth.email as string)
                  ? 'noFilter'
                  : 'noFilter',
              selectedPoolAllowed: auth.userGroup.includes('VentureREI') ? true : false,
            },
          });
          setAccessAllowed(true);

          if (auth.userGroup.includes('ILE')) {
            dispatch({
              type: 'CMA/saveCMAStates',
              payload: {
                compingMode: 'smartFilter',
                currentStartMLS: moment()
                  .subtract(180, 'days')
                  .format(dateFormat),
                rangeClosingDate: 6, // 6 months
                selectedPoolAllowed: true,
                currentStatusMLS: 'status',
              },
            });
          }

          if (auth.userGroup.includes('USLegacy')) {
            dispatch({
              type: 'CMA/saveCMAStates',
              payload: {
                searchingMode: 'Sale',
                compingMode: 'smartFilter',
              },
            });
          }

          Sentry.setUser({
            email: auth.email,
            userGroup: selectedUserGroup,
          });
        }

        setAuthSession(auth);
        setInitLoading(false);
      } catch (error) {
        console.error('error', error);
        console.log('getUserAuthentication session', session);
        console.log('getUserAuthentication attributes', attributes);
        setInitLoading(false);
      }
    };

    getUserAuthentication();
  }, [user]);

  return { initLoading, accessAllowed, authSession };
};

const processAuthSessionData = (
  authSession: AuthSession,
): AuthSessionProperties | never => {
  const result = {} as AuthSessionProperties;
  try {
    if (isEmpty(authSession) || !authSession?.tokens) return result;
    const { accessToken, idToken } = authSession.tokens;
    result.token = accessToken?.toString();
    result.userGroup = (idToken?.payload?.['cognito:groups'] as string[]) || [];
    result.profile = idToken?.payload?.profile as string;
    result.email = idToken?.payload?.email as string;

    return result;
  } catch (error) {
    console.log('processAuthSessionData authSession', authSession);
    throw error;
  }
};

const userHasAccess = (
  userSession: AuthSessionProperties,
  userAttributes: FetchUserAttributesOutput,
): boolean | never => {
  try {
    if (isEmpty(userSession)) return false;

    console.log('userSession', userSession);

    const { token, userGroup, email } = userSession;
    if (!token || userGroup.length === 0 || !email) return false;

    const selectedUserGroup = getSelectedUserGroup(userGroup);

    const portalAllowed = userAttributes['custom:portalAllowed'];
    if (
      !userGroupAccess[selectedUserGroup] ||
      (userGroupAccess[selectedUserGroup] &&
        !userGroupAccess[selectedUserGroup].app.includes('CMA')) ||
      (portalAllowed &&
        typeof portalAllowed === 'string' &&
        !portalAllowed.includes('CMA'))
    ) {
      return false;
    }
    return true;
  } catch (error) {
    console.log('userHasAccess userSession', userSession);
    console.log('userHasAccess userAttributes', userAttributes);
    throw error;
  }
};

const clearCognitoAmplifyCookies = () => {
  try {
    document.cookie.split('; ').forEach((cookie) => {
      const [name] = cookie.split('=');

      if (
        name.startsWith('amplify-signin-with-hostedUI') ||
        name.startsWith('CognitoIdentityServiceProvider')
      ) {
        // Delete the cookie by setting its expiration date in the past
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`;
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; Secure`;
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; domain=.locatealpha.com; SameSite=None`;
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; domain=.locatealpha.com; Secure; SameSite=None`;
      }
    });
  } catch (error) {
    console.error('clearCognitoAmplifyCookies error', error);
  }
};

export default useUserAuthentication;
