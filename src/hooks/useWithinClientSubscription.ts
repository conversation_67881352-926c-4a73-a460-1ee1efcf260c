import { set } from 'lodash';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import {
  getClientAllowedAccessCoordinatesData,
  getClientAllowedAccessCountyData,
  getClientAllowedAccessMetroData,
  getClientAllowedAccessStateData,
} from '../services/data';
import { withinSubscribedMetro } from '../utils/geography';

interface useWithinClientSubscriptionProps {
  lng?: number | undefined;
  lat?: number | undefined;
  cityCode?: string | undefined;
  state?: string | undefined;
}

interface useWithinClientSubscriptionReturn {
  withinClientSubscription: boolean;
  loading: boolean;
}

const useWithinClientSubscription = (
  props: useWithinClientSubscriptionProps,
): useWithinClientSubscriptionReturn => {
  const selectedUserGroup = useSelector(
    (state: any) => state.CMA.selectedUserGroup,
  );
  const metrosAllowedOnIndividualAccountLevel = useSelector(
    (state: any) => state.CMA.metrosAllowedOnIndividualAccountLevel,
  );
  const currentPropertyAddress = useSelector(
    (state: any) => state.CMA.currentPropertyAddress,
  );

  const [withinClientSubscription, setWithinClientSubscription] =
    useState(false);
  const [loading, setLoading] = useState(true); // indicate whether result is from API response, because if it's not, the return would be default value

  if (['demo-CMA-DFW-only'].includes(selectedUserGroup)) {
    const { isWithinSubscribedMetro, isWithinAnyMetro } = withinSubscribedMetro(
      currentPropertyAddress.postalCode,
      selectedUserGroup,
      metrosAllowedOnIndividualAccountLevel,
    );
    return {
      withinClientSubscription: isWithinSubscribedMetro,
      loading: false,
    };
  }

  useEffect(() => {
    const checkIfAllowed = async () => {
      switch (true) {
        case Object.hasOwn(props, 'lng') &&
          Object.hasOwn(props, 'lat') &&
          typeof props.lng === 'number' &&
          typeof props.lat === 'number' &&
          !Number.isNaN(props.lng) &&
          !Number.isNaN(props.lat):
          const allowedCoordinates =
            await getClientAllowedAccessCoordinatesData({
              coordinates: `${props.lat},${props.lng}`,
            });
          if (allowedCoordinates) {
            setWithinClientSubscription(true);
            setLoading(false);
          }
          break;
        case Object.hasOwn(props, 'cityCode') &&
          typeof props.cityCode === 'string':
          const allowedMetro = await getClientAllowedAccessMetroData({
            metroKey: props.cityCode,
          });
          if (allowedMetro) {
            setWithinClientSubscription(true);
            setLoading(false);
          }
          break;
        case Object.hasOwn(props, 'state') && typeof props.state === 'string':
          const allowedState = await getClientAllowedAccessStateData({
            stateAbbrev: props.state,
          });
          if (allowedState) {
            setWithinClientSubscription(true);
            setLoading(false);
          }
          break;
        default:
          break;
      }
    };
    checkIfAllowed();
  }, [props.lat, props.lng, props.cityCode, props.state]);

  return { withinClientSubscription, loading };
};

export default useWithinClientSubscription;
