{"AlliedDev": {"abbrev": "allieddev", "app": ["CMA"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Gentrifying Neighbourhoods"]}, "AMH": {"abbrev": "amh", "app": ["acqPortal", "CMA"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search"]}, "Arabella": {"abbrev": "arabella", "app": ["CMA"], "state": ["AL", "GA", "TN", "NC", "SC", "FL"], "metro": ["03", "05", "06", "08", "09", "14", "16", "17", "20", "22", "24", "28", "29", "30", "31", "32", "34", "40"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Gentrifying Neighbourhoods"]}, "ArkHomesForRent": {"abbrev": "arkhomes", "app": ["CMA"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Gentrifying Neighbourhoods"]}, "Avanta": {"abbrev": "avanta", "app": ["CMA"], "metro": ["01", "02", "03", "04", "05", "07", "08", "09", "14"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "Scorecard", "workforcemobile", "Land Parcel Search", "Muilty Family"]}, "AvenueOne": {"abbrev": "avenueone", "app": ["MLS", "acqPortal", "CMA"], "metro": ["01", "03", "06"], "premium": ["Muilty Family"]}, "BeaconRidge": {"abbrev": "<PERSON><PERSON>", "app": ["CMA"], "metro": [], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Gentrifying Neighbourhoods", "Affordable Housing"]}, "BlueRiver": {"abbrev": "blueriver", "app": ["CMA"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search"]}, "BridgeAdmin": {"abbrev": "bridge", "app": ["MLS", "acqPortal", "CMA", "newHomeLeads"], "metro": ["01", "02", "03", "05", "08", "09", "15", "40"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Land Parcel Search", "Gentrifying Neighbourhoods", "Muilty Family"]}, "BridgeFull": {"abbrev": "bridge", "app": ["MLS", "acqPortal", "CMA", "newHomeLeads"], "metro": ["01", "02", "03", "05", "08", "09", "15", "40"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Land Parcel Search", "Gentrifying Neighbourhoods", "Muilty Family"]}, "BridgeTower": {"abbrev": "bt", "app": ["acqPortal", "CMA", "PMPortal", "newHomeLeads", "PM"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "17"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "Land Parcel Search", "Muilty Family", "Land Parcel Search", "Gentrifying Neighbourhoods", "Affordable Housing"]}, "Camillo": {"abbrev": "camillo", "app": ["CMA"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Gentrifying Neighbourhoods"]}, "Castle": {"abbrev": "castle", "app": ["CMA"], "metro": ["08", "17", "22", "23", "28", "29"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Gentrifying Neighbourhoods"]}, "Darwin": {"abbrev": "darwin", "app": ["CMA"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "Scorecard", "workforcemobile"]}, "DCSDevelopment": {"abbrev": "dcs", "app": ["CMA"], "metro": ["01", "02", "04", "07"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Gentrifying Neighbourhoods", "Affordable Housing"]}, "DRHorton": {"abbrev": "dr<PERSON><PERSON>", "app": ["CMA"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search"]}, "Embry": {"abbrev": "embry", "app": ["CMA"], "metro": ["03"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Gentrifying Neighbourhoods"]}, "Evergreen": {"abbrev": "evergreen", "app": ["CMA"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Affordable Housing"]}, "Fundrise": {"abbrev": "fundrise", "app": ["CMA"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile"]}, "GACapital": {"abbrev": "gacapital", "app": ["CMA"], "metro": [], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Gentrifying Neighbourhoods", "Affordable Housing"]}, "GEMRC": {"abbrev": "gemrc", "app": ["CMA"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "workforcemobile"]}, "GreatGulf": {"abbrev": "greatgulf", "app": ["CMA"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search"]}, "Greystar": {"abbrev": "greystar", "app": ["CMA"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Gentrifying Neighbourhoods", "Affordable Housing", "Muilty Family"]}, "Hawkhill": {"abbrev": "hawkhill", "app": ["CMA", "acqPortal"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "workforcemobile"]}, "Heyday": {"abbrev": "heyday", "app": ["CMA"], "metro": [], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Gentrifying Neighbourhoods", "Affordable Housing"]}, "Homebound": {"abbrev": "homebound", "app": ["CMA"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Gentrifying Neighbourhoods"]}, "HON": {"abbrev": "hon", "app": ["acqPortal", "CMA", "newHomeLeads"], "metro": ["01", "02", "05", "09", "10"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "Affordable Housing"]}, "HunterQuinn": {"abbrev": "hunterquinn", "app": ["CMA"], "metro": [], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Gentrifying Neighbourhoods", "Affordable Housing"]}, "ILE": {"abbrev": "ile", "app": ["MLS", "acqPortal", "CMA"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Gentrifying Neighbourhoods", "Affordable Housing", "Muilty Family"]}, "InvitationHomes": {"abbrev": "invitationhomes", "app": ["CMA"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search"]}, "JCGLand": {"abbrev": "jcgland", "app": ["CMA"], "metro": ["03"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search"]}, "JLL": {"abbrev": "jll", "app": ["acqPortal", "CMA"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "Scorecard", "workforcemobile"]}, "Kairos": {"abbrev": "kairos", "app": ["CMA"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search"]}, "LedgerTC": {"abbrev": "ledgertc", "app": ["CMA", "acqPortal", "MLS", "newHomeLeads", "leads"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search"]}, "LendingOne": {"abbrev": "lendingone", "app": ["CMA", "acqPortal"], "metro": ["01"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search"]}, "Lennar": {"abbrev": "lennar", "app": ["CMA"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Gentrifying Neighbourhoods", "Muilty Family"]}, "MarketplaceHomes": {"abbrev": "mph", "app": ["MLS", "acqPortal", "CMA", "newHomeLeads"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "11"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "Scorecard", "Muilty Family"]}, "MMG": {"abbrev": "mmg", "app": ["CMA"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Gentrifying Neighbourhoods", "Affordable Housing"]}, "Nhimble": {"abbrev": "nhimble", "app": ["MLS", "CMA", "acqPortal", "leads"], "metro": ["03"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "Scorecard"]}, "P2Construction": {"abbrev": "p2construction", "app": ["CMA"], "metro": [], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Gentrifying Neighbourhoods", "Affordable Housing"]}, "Pathway": {"abbrev": "pathway", "app": ["CMA", "newHomeLeads"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search"]}, "REMAXFine": {"abbrev": "remaxfine", "app": ["CMA", "acqPortal", "MLS", "newHomeLeads"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Gentrifying Neighbourhoods", "Affordable Housing"]}, "Rithm": {"abbrev": "rithm", "app": ["CMA"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Gentrifying Neighbourhoods", "Affordable Housing"]}, "SecondAvenue": {"abbrev": "second<PERSON><PERSON>", "app": ["CMA"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Affordable Housing"]}, "SmithDouglas": {"abbrev": "s<PERSON><PERSON><PERSON><PERSON>", "app": ["CMA"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Affordable Housing"]}, "Sunroom": {"abbrev": "sunroom", "app": ["CMA"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF"]}, "SVN": {"abbrev": "svn", "app": ["CMA"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile"]}, "TrellyGroup": {"abbrev": "trelly", "app": ["MLS", "CMA"], "metro": ["01", "02"], "premium": []}, "Tricon": {"abbrev": "tricon", "app": ["CMA"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Gentrifying Neighbourhoods", "Affordable Housing", "Muilty Family"]}, "Truehold": {"abbrev": "truehold", "app": ["MLS", "acqPortal", "CMA", "newHomeLeads"], "metro": ["15", "18", "19", "25", "26", "33", "36", "37", "38", "41", "42", "44", "1002", "1003"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Muilty Family"]}, "UpAndUp": {"abbrev": "upandup", "app": ["CMA", "acqPortal"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Gentrifying Neighbourhoods", "Affordable Housing"]}, "UrbanRowGroup": {"abbrev": "urg", "app": ["CMA"], "metro": ["02"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search"]}, "USLegacy": {"abbrev": "uslegacy", "app": ["CMA", "acqPortal"], "metro": ["03"], "premium": ["Batch Process", "Chain Stores", "Comp PDF"]}, "VentureREI": {"abbrev": "eso-uw", "app": ["MLS", "acqPortal", "CMA", "newHomeLeads"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": []}, "VentureREIDemo": {"abbrev": "eso-uw", "app": ["MLS", "acqPortal", "CMA"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": []}, "Vertica": {"abbrev": "vertica", "app": ["CMA"], "metro": ["05", "09", "14", "30", "32"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search"]}, "WebCity": {"abbrev": "webcity", "app": ["CMA"], "metro": [], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Muilty Family"]}, "dev": {"abbrev": "dev", "app": ["MLS", "acqPortal", "CMA", "PMPortal", "PM", "leads", "newHomeLeads", "Partner"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Site Plan Generator"]}, "demo-users": {"abbrev": "demo", "app": ["MLS", "acqPortal", "CMA", "PMPortal", "PM", "leads", "newHomeLeads", "Partner"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Gentrifying Neighbourhoods", "Site Plan Generator", "Affordable Housing", "Muilty Family"]}, "demo-CMA-DFW-only": {"abbrev": "demo-unlimited", "app": ["CMA"], "metro": ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "44", "45", "55"], "premium": ["Batch Process", "Chain Stores", "Comp PDF", "New House AVM", "Scorecard", "workforcemobile", "Land Parcel Search", "Gentrifying Neighbourhoods", "Affordable Housing"]}}