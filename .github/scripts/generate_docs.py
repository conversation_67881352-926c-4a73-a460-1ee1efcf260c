import os
import json
from github import Github
import openai
import subprocess
from datetime import datetime

# GitHub and OpenAI credentials
GITHUB_TOKEN = os.getenv("GITHUB_TOKEN")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
REPO_NAME = os.getenv("GITHUB_REPOSITORY")
COMMIT_SHA = os.getenv("GITHUB_SHA")

# Initialize GitHub and OpenAI
g = Github(GITHUB_TOKEN)
client = openai.OpenAI(api_key=OPENAI_API_KEY)
repo = g.get_repo(REPO_NAME)
commit = repo.get_commit(COMMIT_SHA)

# Get commit details
commit_message = commit.commit.message
commit_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")

# Get the diff of the latest commit
diff = subprocess.check_output(
    ["git", "diff", f"{COMMIT_SHA}^", COMMIT_SHA],
    text=True
).strip()

if not diff:
    diff = "No changes detected in this commit."

# Generate documentation with OpenAI
prompt = f"""
Analyze the following changes from a commit and generate concise Markdown documentation:
{diff}

Provide a summary of what was changed and why it might have been changed. Output in Markdown format.
"""
try:
    response = client.chat.completions.create(
        model="gpt-3.5-turbo",
        messages=[{"role": "user", "content": prompt}],
        max_tokens=300
    )
    documentation = response.choices[0].message.content
except Exception as e:
    documentation = f"Failed to generate documentation: {str(e)}"

# Prepare new Markdown content
new_content = f"""
## Commit {COMMIT_SHA[:7]}
- **Date**: {commit_date}
- **Message**: {commit_message}
- **Explanation**:
{documentation}
"""

# File to update
docs_file = "commit_docs.md"

# Read existing content (if it exists)
existing_content = ""
if os.path.exists(docs_file):
    with open(docs_file, "r") as f:
        existing_content = f.read()
    # Remove the header from existing content if it exists, to avoid duplication
    if existing_content.startswith("# Commit Documentation\n\n"):
        existing_content = existing_content[len("# Commit Documentation\n\n"):]

# Write new content at the top
with open(docs_file, "w") as f:
    f.write("# Commit Documentation\n\n" + new_content)
    if existing_content.strip():  # Only add separator and existing content if there’s something to append
        f.write("\n---\n" + existing_content)