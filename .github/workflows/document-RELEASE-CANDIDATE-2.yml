name: AI Documentation Generator

on:
  push:
    branches: 
      - RELEASE-CANDIDATE-2  # Only trigger on main branch

jobs:
  generate-docs:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 2
          token: ${{ secrets.GITHUB_TOKEN }}  # Ensures push access

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install Dependencies
        run: pip install openai requests PyGithub

      - name: Generate Documentation
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        run: python .github/scripts/generate_docs.py

      - name: Commit and Push Documentation
        run: |
          git config user.name "GitHub Action"
          git config user.email "<EMAIL>"
          git add commit_docs.md
          git commit -m "Add AI-generated documentation for commit $GITHUB_SHA" || echo "No changes to commit"
          git push