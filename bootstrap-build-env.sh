#!/bin/bash
set -euo pipefail

echo "Setting up swap space..."
# Try to set up swap, but don't fail if it doesn't work
sudo fallocate -l 32G /swapfile || echo "fallocate failed, continuing..."
sudo chmod 600 /swapfile || echo "chmod failed, continuing..."
sudo mkswap /swapfile || echo "mkswap failed, continuing..."
sudo swapon /swapfile || echo "swapon failed, continuing..."
sudo swapon -s || echo "swapon -s failed, continuing..."
echo "Swap setup completed (with or without errors)"
echo ""